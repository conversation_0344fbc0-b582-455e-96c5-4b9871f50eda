import React, { type ReactNode } from "react";
import { BaseNavigation } from "@/components/navigation";
import { backendNavigationConfig } from "@/config/navigation";

interface BackendNavigationProps {
    title?: ReactNode;
}

/**
 * Backend Navigation Component
 * 
 * Spezifische Navigation für das Backend-Modul mit:
 * - Logo und Titel
 * - Backend-spezifische Navigation (System, Workflows)
 * - Health Indicator
 * - User Menu mit Settings-Link
 */
export default function BackendNavigation({ title }: BackendNavigationProps) {
    return (
        <BaseNavigation
            title={title}
            navigationConfig={backendNavigationConfig}
            showUserMenu={true}
        />
    );
}