import React, { useEffect, useState, memo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
} from "recharts";
import { DateRange } from "react-day-picker";
import { isWithinInterval } from "date-fns";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import apiService from "@/services/api.service";

// Definition des Datentyps für die WE-Daten aus der Datenbank
interface WEDataPoint {
  id?: number;
  datum: string;
  weAtrl: number | null;
  weManl: number | null;
}

// Definition des Datentyps für die Chart-Daten
interface IncomingDataPoint {
  name: string;
  date?: Date; // Hilfsdatum für die Sortierung
  weAtrl: number;
  weManl: number;
}

/**
 * IncomingPositionsChart Komponente
 * 
 * Zeigt die eingehenden Positionen (WE-Daten) als Balkendiagramm an.
 * Verwendet Daten aus der dispatch_data-Tabelle (atrl, aril)
 * 
 * @param dateRange Datumsbereich für die Filterung der Daten
 */
interface IncomingPositionsChartProps {
  // Datumsbereich für die Filterung der Daten
  dateRange?: DateRange;
}

export const IncomingPositionsChart = memo(function IncomingPositionsChart({ dateRange }: IncomingPositionsChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<IncomingDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Konfiguration für das Diagramm im Neobrutalism-Stil
  const chartConfig = {
    weAtrl: {
      label: t("WE Automatisch"),
      color: "var(--chart-1)", // Kräftige Farbe im Neobrutalism-Stil
    },
    weManl: {
      label: t("WE Manuell"),
      color: "var(--chart-2)",
    },
  };

  // Farben für die verschiedenen Balken im Neobrutalism-Stil
  const colors = ["var(--chart-1)", "var(--chart-2)"];

  // Lade Daten aus der Datenbank
  useEffect(() => {
    loadData();
  }, [dateRange]);

  /**
   * Lädt die WE-Daten aus der dispatch_data-Tabelle der Datenbank
   * und filtert sie nach dem angegebenen Datumsbereich
   */
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await apiService.getWEData();
      
      if (result && Array.isArray(result)) {
        if (result.length === 0) {
          setChartData([]);
          setError('Keine WE-Daten gefunden');
          return;
        }
        
        // Konvertiere WE-Daten zu Chart-Format
        let processedData: IncomingDataPoint[] = result.map((row: unknown) => {
          const dbRow = row as WEDataPoint;
          // Formatiere das Datum für die X-Achse
          let formattedDate = 'Unbekannt';
          let dateObj = new Date(0); // Standard-Datum, falls keins vorhanden ist
          
          if (dbRow.datum) {
            try {
              dateObj = new Date(dbRow.datum);
              formattedDate = dateObj.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
            } catch (e) {
              // Fehler beim Formatieren des Datums
            }
          }
          
          // Erstelle das Datenpunkt-Objekt mit Standardwerten für fehlende Felder
          const dataPoint: IncomingDataPoint = {
            name: formattedDate,
            date: dateObj, // Verwende das erstellte Date-Objekt
            weAtrl: dbRow.weAtrl ?? 0, // Nullish coalescing für null/undefined
            weManl: dbRow.weManl ?? 0,
          };
          
          return dataPoint;
        });
        
        // Sortiere die Daten nach Datum aufsteigend
        processedData.sort((a, b) => {
          const dateA = a.date ? a.date.getTime() : 0;
          const dateB = b.date ? b.date.getTime() : 0;
          return dateA - dateB;
        });
        
        // Filtere die Daten nach dem Datumsbereich, wenn vorhanden
        if (dateRange && dateRange.from && dateRange.to) {
          processedData = processedData.filter(item => {
            if (!item.date) return true;
            
            try {
              return isWithinInterval(item.date, {
                start: dateRange.from as Date,
                end: dateRange.to as Date
              });
            } catch (error) {
              return true;
            }
          });
        }
        
        // Entferne das Hilfsdatum aus den Daten für die finale Chart-Darstellung
        const finalData: IncomingDataPoint[] = processedData.map((item) => ({
          name: item.name,
          weAtrl: item.weAtrl,
          weManl: item.weManl,
        }));
        
        setChartData(finalData);
      } else {
        throw new Error('Keine gültigen WE-Daten erhalten');
      }
    } catch (err) {
      setChartData([]);
      setError('Fehler beim Laden der WE-Daten aus der Datenbank: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Berechne Gesamtwerte und Durchschnitte für den Footer
  const totalAtrl = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item.weAtrl || 0), 0) : 0;
  const totalManl = chartData.length > 0 ? chartData.reduce((sum, item) => sum + (item.weManl || 0), 0) : 0;
  const totalAll = totalAtrl + totalManl;
  const avgAtrl = chartData.length > 0 ? totalAtrl / chartData.length : 0;
  const avgManl = chartData.length > 0 ? totalManl / chartData.length : 0;

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">{t("loading")}...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="text-red-500 font-bold">{error}</p>
        <p className="mt-2">{t("check_database_connection")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <CardTitle>WARENEINGANG POSITIONEN</CardTitle>
        <CardDescription>
          Wareningänge ins ATrL und MAnL
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="h-60 w-full"
        >
          <BarChart data={chartData} margin={{ top: 5, right: 20, left: 20, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="name" 
              className="text-xs font-bold"
              tick={{ fill: "#000000" }}
              axisLine={{ stroke: "#000000", strokeWidth: 2 }}
            />
            <YAxis 
              className="text-xs font-bold"
              tick={{ fill: "#000000" }}
              axisLine={{ stroke: "#000000", strokeWidth: 2 }}
              // Beschriftung für die Y-Achse
              label={{ 
                value: "Anzahl Positionen", 
                angle: -90, 
                position: "insideLeft",
                style: { textAnchor: "middle", fontSize: 12, fill: "#000000" },
                offset: -5
              }}
            />
            <ChartTooltip
              content={
                <ChartTooltipContent
                  labelClassName="font-bold"
                  labelFormatter={(label) => `Datum: ${label}`}
                />
              }
            />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="weAtrl"
              name="WE Automatisch"
              fill={colors[0]}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
            <Bar
              dataKey="weManl"
              name="WE Manuell"
              fill={colors[1]}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex flex-col w-full text-sm">
          <div className="font-medium mb-1">Anzahl Wareneingangspositionen über die ausgewählte Zeitperiode:</div>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center">
              <span className="text-black">Durchschnitt: ATrL: {chartData.length > 0 ? avgAtrl.toFixed(1) : 'N/A'}  </span>
            </div>
            <div className="flex items-center">
              <span className="text-black">Manuell: {chartData.length > 0 ? avgManl.toFixed(1) : 'N/A'} | </span>
            </div>
            <div className="flex items-center">
             <span className="text-black">Gesamt: {chartData.length > 0 ? totalAll : 0} | </span>
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});
