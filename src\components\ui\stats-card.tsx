import React from "react";
import { Card, CardContent } from "./card";

interface StatItemProps {
  label: string;
  value: string | number;
  variant?: "default" | "success" | "warning" | "danger";
}

export function StatItem({ label, value, variant = "default" }: StatItemProps) {
  const variantStyles = {
    default: "bg-white text-black",
    success: "bg-neo-green text-white",
    warning: "bg-neo-yellow text-black", 
    danger: "bg-neo-red text-white"
  };

  return (
    <Card className={`text-center ${variantStyles[variant]}`}>
      <CardContent className="p-3">
        <div className="font-heading text-lg">{value}</div>
        <div className="font-base opacity-70 text-xs">{label}</div>
      </CardContent>
    </Card>
  );
}

interface StatsGridProps {
  stats: StatItemProps[];
}

export function StatsGrid({ stats }: StatsGridProps) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="grid grid-cols-3 gap-4">
          {stats.map((stat, index) => (
            <StatItem key={index} {...stat} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 