/**
 * Error Messages Utility Tests
 * Tests for German error message formatting
 */

import { describe, it, expect } from 'vitest';
import { 
  AI_ERROR_MESSAGES, 
  getErrorMessage, 
  formatErrorForUser, 
  formatErrorForTechnical 
} from '../errorMessages';
import { AIServiceErrorCode } from '../../types/errors';

describe('Error Messages Utility', () => {
  describe('AI_ERROR_MESSAGES', () => {
    it('should contain messages for all error codes', () => {
      const errorCodes = Object.values(AIServiceErrorCode);
      
      for (const code of errorCodes) {
        expect(AI_ERROR_MESSAGES[code]).toBeDefined();
        expect(AI_ERROR_MESSAGES[code].user).toBeDefined();
        expect(AI_ERROR_MESSAGES[code].technical).toBeDefined();
        expect(typeof AI_ERROR_MESSAGES[code].user).toBe('string');
        expect(typeof AI_ERROR_MESSAGES[code].technical).toBe('string');
      }
    });

    it('should have German user messages', () => {
      const germanIndicators = ['der', 'die', 'das', 'ist', 'sind', 'können', 'wurde', 'werden', 'Sie', 'Bitte'];
      
      Object.values(AI_ERROR_MESSAGES).forEach(messages => {
        const hasGermanWords = germanIndicators.some(indicator => 
          messages.user.toLowerCase().includes(indicator.toLowerCase())
        );
        expect(hasGermanWords).toBe(true);
      });
    });

    it('should have English technical messages', () => {
      Object.values(AI_ERROR_MESSAGES).forEach(messages => {
        // Technical messages should be in English (no German articles)
        const germanArticles = ['der', 'die', 'das'];
        const hasGermanArticles = germanArticles.some(article => 
          messages.technical.toLowerCase().includes(article.toLowerCase())
        );
        expect(hasGermanArticles).toBe(false);
      });
    });
  });

  describe('getErrorMessage', () => {
    it('should return user message by default', () => {
      const message = getErrorMessage(AIServiceErrorCode.API_REQUEST_FAILED);
      expect(message).toBe(AI_ERROR_MESSAGES[AIServiceErrorCode.API_REQUEST_FAILED].user);
    });

    it('should return technical message when requested', () => {
      const message = getErrorMessage(AIServiceErrorCode.API_REQUEST_FAILED, 'technical');
      expect(message).toBe(AI_ERROR_MESSAGES[AIServiceErrorCode.API_REQUEST_FAILED].technical);
    });

    it('should return user message when explicitly requested', () => {
      const message = getErrorMessage(AIServiceErrorCode.API_REQUEST_FAILED, 'user');
      expect(message).toBe(AI_ERROR_MESSAGES[AIServiceErrorCode.API_REQUEST_FAILED].user);
    });

    it('should return fallback message for unknown error code', () => {
      const unknownCode = 'UNKNOWN_ERROR_CODE' as AIServiceErrorCode;
      const userMessage = getErrorMessage(unknownCode, 'user');
      const technicalMessage = getErrorMessage(unknownCode, 'technical');
      
      expect(userMessage).toContain('unbekannter Fehler');
      expect(technicalMessage).toContain('Unknown error code');
    });
  });

  describe('formatErrorForUser', () => {
    it('should format AI service error with context', () => {
      const error = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        userMessage: 'Test user message'
      };
      
      const formatted = formatErrorForUser(error, 'Test Context');
      expect(formatted).toContain('Test Context');
      expect(formatted).toContain('Die KI-Anfrage konnte nicht verarbeitet werden');
    });

    it('should format AI service error without context', () => {
      const error = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        userMessage: 'Test user message'
      };
      
      const formatted = formatErrorForUser(error);
      expect(formatted).toBe('Die KI-Anfrage konnte nicht verarbeitet werden. Bitte versuchen Sie es erneut.');
    });

    it('should format regular error with context', () => {
      const error = new Error('Regular error');
      
      const formatted = formatErrorForUser(error, 'Test Context');
      expect(formatted).toContain('Test Context');
      expect(formatted).toContain('Ein unerwarteter Fehler ist aufgetreten');
    });

    it('should format regular error without context', () => {
      const error = new Error('Regular error');
      
      const formatted = formatErrorForUser(error);
      expect(formatted).toBe('Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.');
    });

    it('should format string error', () => {
      const error = 'String error message';
      
      const formatted = formatErrorForUser(error, 'String Context');
      expect(formatted).toContain('String Context');
      expect(formatted).toContain('Ein unerwarteter Fehler ist aufgetreten');
    });

    it('should handle null/undefined errors', () => {
      const formatted1 = formatErrorForUser(null);
      const formatted2 = formatErrorForUser(undefined);
      
      expect(formatted1).toContain('Ein unerwarteter Fehler ist aufgetreten');
      expect(formatted2).toContain('Ein unerwarteter Fehler ist aufgetreten');
    });
  });

  describe('formatErrorForTechnical', () => {
    it('should format AI service error', () => {
      const error = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        technicalMessage: 'Technical details'
      };
      
      const formatted = formatErrorForTechnical(error);
      expect(formatted).toBe('OpenRouter API request failed with HTTP error');
    });

    it('should format regular error', () => {
      const error = new Error('Regular error message');
      
      const formatted = formatErrorForTechnical(error);
      expect(formatted).toBe('Regular error message');
    });

    it('should format string error', () => {
      const error = 'String error';
      
      const formatted = formatErrorForTechnical(error);
      expect(formatted).toBe('String error');
    });

    it('should handle errors without message', () => {
      const error = {};
      
      const formatted = formatErrorForTechnical(error);
      expect(formatted).toBe('Unknown technical error occurred');
    });

    it('should handle null/undefined errors', () => {
      const formatted1 = formatErrorForTechnical(null);
      const formatted2 = formatErrorForTechnical(undefined);
      
      expect(formatted1).toBe('Unknown technical error occurred');
      expect(formatted2).toBe('Unknown technical error occurred');
    });
  });

  describe('Specific Error Code Messages', () => {
    it('should have appropriate messages for API errors', () => {
      const apiErrors = [
        AIServiceErrorCode.API_KEY_INVALID,
        AIServiceErrorCode.API_RATE_LIMIT_EXCEEDED,
        AIServiceErrorCode.API_REQUEST_FAILED,
        AIServiceErrorCode.API_TIMEOUT
      ];

      apiErrors.forEach(code => {
        const userMessage = getErrorMessage(code, 'user');
        const technicalMessage = getErrorMessage(code, 'technical');
        
        expect(userMessage).toContain('KI');
        expect(technicalMessage.toLowerCase()).toContain('api');
      });
    });

    it('should have appropriate messages for vector database errors', () => {
      const vectorErrors = [
        AIServiceErrorCode.VECTOR_STORAGE_FAILED,
        AIServiceErrorCode.VECTOR_SEARCH_FAILED,
        AIServiceErrorCode.EMBEDDING_GENERATION_FAILED
      ];

      vectorErrors.forEach(code => {
        const userMessage = getErrorMessage(code, 'user');
        const technicalMessage = getErrorMessage(code, 'technical');
        
        expect(userMessage.toLowerCase()).toMatch(/wissen|suche|text/);
        expect(technicalMessage.toLowerCase()).toMatch(/vector|embedding|search/);
      });
    });

    it('should have appropriate messages for optimization errors', () => {
      const optimizationErrors = [
        AIServiceErrorCode.OPTIMIZATION_FAILED,
        AIServiceErrorCode.INSUFFICIENT_DATA,
        AIServiceErrorCode.INVALID_PARAMETERS
      ];

      optimizationErrors.forEach(code => {
        const userMessage = getErrorMessage(code, 'user');
        const technicalMessage = getErrorMessage(code, 'technical');
        
        expect(userMessage.toLowerCase()).toMatch(/optimierung|daten|parameter|berechnung/);
        expect(technicalMessage.toLowerCase()).toMatch(/optimization|data|parameter|calculation/);
      });
    });
  });
});