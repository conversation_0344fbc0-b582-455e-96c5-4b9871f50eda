{"security": {"accessDenied": "<PERSON><PERSON><PERSON> verweigert", "authenticationRequired": "Authentifizierung erforderlich", "insufficientPermissions": "Unzureichende Berechtigungen", "loginRequired": "<PERSON><PERSON><PERSON><PERSON>", "securityCheck": "Sicherheitsprüfung läuft...", "inputValidation": {"title": "Eingabevalidierung", "invalid": "Eingabe ungültig", "valid": "Eingabe gültig", "sanitized": "Eingabe wurde bereinigt", "blocked": "Eingabe blockiert", "riskLevels": {"low": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "high": "Hoch"}, "violations": {"sqlInjection": "Potentielle SQL-Injection erkannt", "scriptInjection": "Script-Injection erkannt", "promptInjection": "Potentielle Prompt-Injection erkannt", "excessiveLength": "Eingabe zu lang (max. 10.000 Zeichen)", "suspiciousPatterns": "Verdächtige Muster in der Eingabe erkannt"}, "warnings": {"title": "Sicherheitswarnungen:", "highRiskBlocked": "Eingabe blockiert: Diese Eingabe enthält potentiell gefährliche Inhalte und kann nicht verarbeitet werden.", "sanitizationNotice": "Potentiell unsichere Zeichen wurden automatisch entfernt oder ersetzt.", "showSanitized": "Bereinigte Version anzeigen"}}, "permissions": {"title": "KI-Berechtigungen", "user": "<PERSON><PERSON><PERSON>", "roles": "<PERSON><PERSON>", "availableFeatures": "Verfügbare Funktionen", "features": {"ragQuery": "RAG Abfragen", "cuttingOptimization": "Schnittoptimierung", "inventoryIntelligence": "Lager-Intelligenz", "processOptimization": "Prozessoptimierung", "predictiveAnalytics": "Vorhersageanalyse", "warehouseOptimization": "Lageroptimierung", "supplyChainOptimization": "Lieferkettenoptimierung", "automatedReporting": "Automatisierte Berichte", "aiConfiguration": "KI-Konfiguration"}}, "dashboard": {"title": "KI-Sicherheitsdashboard", "subtitle": "Überwachung und Verwaltung der KI-Modulsicherheit", "refresh": "Aktualisieren", "loading": "Sicherheitsdaten werden geladen...", "alerts": {"missingApiKeys": "API-Schlüssel fehlen", "missingKeysMessage": "Folgende API-Schlüssel sind nicht konfiguriert: {keys}"}, "metrics": {"totalRequests": "Gesamtanfragen", "deniedRequests": "Verweigerte Anfragen", "highRiskAttempts": "Hochrisiko-Versuche", "apiStatus": "API-Status", "allRequests": "Alle KI-Anfragen", "percentageOfRequests": "{percentage}% der Anfragen", "noRequests": "<PERSON><PERSON>", "dangerousInputs": "Potentiell gefährliche Eingaben", "allKeysConfigured": "Alle Schlüssel konfiguriert", "keysMissing": "{count} fehlen", "statusOk": "OK", "statusError": "<PERSON><PERSON>"}, "tabs": {"auditLogs": "Audit-Protokoll", "violations": "Sicherheitsverletzungen", "apiKeys": "API-Schlüssel"}, "auditLogs": {"title": "Aktuelle Audit-Protokolle", "subtitle": "Die letzten 50 Sicherheitsereignisse im KI-Modul", "noLogs": "<PERSON><PERSON>-Protokolle vorhanden", "results": {"success": "Erfolg", "denied": "Verweigert", "error": "<PERSON><PERSON>"}}, "violations": {"title": "Häufigste Sicherheitsverletzungen", "subtitle": "Übersicht der am häufigsten auftretenden Sicherheitsprobleme", "noViolations": "<PERSON><PERSON>heitsverletzungen aufgezeichnet", "occurrences": "{count} Mal"}, "apiKeys": {"title": "API-Schlüssel Status", "subtitle": "Übersicht der konfigurierten API-Schlüssel für KI-Services", "noKeys": "Keine API-Schlüssel konfiguriert", "created": "Erstellt: {date}", "lastUsed": "Zuletzt verwendet: {date}", "status": {"active": "Aktiv", "inactive": "Inaktiv"}}}, "apiKeyManager": {"validation": {"unknownService": "Unbekannter Service: {service}", "emptyKey": "API-<PERSON><PERSON><PERSON><PERSON> darf nicht leer sein", "invalidFormat": "API-Schlüssel entspricht nicht dem erwarteten Format für {service}", "storageError": "Fehler beim Speichern des API-Schlüssels", "retrievalError": "Fehler beim Abrufen des API-Schlüssels", "removalError": "Fehler beim Entfernen des API-Schlüssels"}, "services": {"openrouter": "OpenRouter API"}}, "errors": {"networkError": "Netzwerkfehler", "unknownError": "Unbekannter Fehler", "securityValidationError": "Fehler bei der Sicherheitsvalidierung", "inputValidationError": "Fehler bei der Eingabevalidierung", "authenticationError": "Authentifizierungsfehler", "authorizationError": "Autorisierungsfehler"}}}