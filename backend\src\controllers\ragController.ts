/**
 * RAG Controller - Document Upload and Management
 * 
 * Handles document upload, processing, and RAG operations
 */

import { Request, Response } from 'express';
import multer from 'multer';
import * as path from 'path';
import * as fs from 'fs';
import SimpleRAGService from '../services/rag/SimpleRAGService';
import RAGSettingsService, { RAGModuleSettings } from '../services/rag/RAGSettingsService';

import { CreateDocumentRequest, CreateKnowledgeBaseRequest, SimilaritySearchRequest } from '../types/rag.types';

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'documents');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req: any, file: any, cb: any) => {
    // Allow text files, PDFs, and common document formats
    const allowedTypes = [
      'text/plain',
      'text/markdown',
      'text/x-markdown',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    // Also check file extension as fallback
    const allowedExtensions = ['.txt', '.md', '.pdf', '.doc', '.docx'];
    const fileExtension = path.extname(file.originalname).toLowerCase();
    
    console.log(`[RAG] File upload attempt: ${file.originalname}, MIME: ${file.mimetype}, Extension: ${fileExtension}`);
    
    // More permissive check - allow if either MIME type matches OR extension matches
    const mimeTypeAllowed = allowedTypes.includes(file.mimetype);
    const extensionAllowed = allowedExtensions.includes(fileExtension);
    
    console.log(`[RAG] MIME type allowed: ${mimeTypeAllowed}, Extension allowed: ${extensionAllowed}`);
    
    // TEMPORARY: Accept all files for debugging
    console.log(`[RAG] TEMPORARILY ACCEPTING ALL FILES FOR DEBUGGING`);
    cb(null, true);
    
    // Original logic (commented out for debugging)
    /*
    if (mimeTypeAllowed || extensionAllowed) {
      console.log(`[RAG] File accepted: ${file.originalname}`);
      cb(null, true);
    } else {
      console.error(`[RAG] Rejected file: ${file.originalname}, MIME: ${file.mimetype}, Extension: ${fileExtension}`);
      console.error(`[RAG] Allowed MIME types:`, allowedTypes);
      console.error(`[RAG] Allowed extensions:`, allowedExtensions);
      cb(new Error(`Unsupported file type. Please upload TXT, MD, PDF, DOC, or DOCX files. Received MIME: ${file.mimetype}, Extension: ${fileExtension}`));
    }
    */
  }
});

export class RAGController {
  private ragService: SimpleRAGService;
  private settingsService: RAGSettingsService;
  // private documentService: DocumentService;

  constructor() {
    this.ragService = new SimpleRAGService();
    this.settingsService = new RAGSettingsService();
    // this.documentService = new DocumentService();
  }

  /**
   * Upload and process document
   */
  uploadDocument = async (req: Request, res: Response): Promise<void> => {
    try {
      console.log('[RAG] Upload request received:', {
        file: req.file ? {
          originalname: req.file.originalname,
          mimetype: req.file.mimetype,
          size: req.file.size
        } : 'No file',
        body: req.body
      });

      if (!req.file) {
        res.status(400).json({ error: 'No file uploaded' });
        return;
      }

      const { title, category, language = 'de' } = req.body;
      
      if (!title || !category) {
        res.status(400).json({ error: 'Title and category are required' });
        return;
      }

      // Read file content
      const filePath = req.file.path;
      let content: string;

      try {
        if (req.file.mimetype === 'text/plain' || req.file.mimetype === 'text/markdown') {
          content = fs.readFileSync(filePath, 'utf-8');
        } else {
          // For other formats, we'd need additional parsing (PDF, DOC, etc.)
          // For now, treat as text
          content = fs.readFileSync(filePath, 'utf-8');
        }
      } catch (error) {
        res.status(400).json({ error: 'Failed to read file content' });
        return;
      }

      // Create document request
      const documentRequest: CreateDocumentRequest = {
        knowledgeBaseId: 1, // Default knowledge base for now
        title,
        content,
        contentType: req.file.mimetype,
        source: req.file.originalname,
        language,
        metadata: {
          originalFilename: req.file.originalname,
          fileSize: req.file.size,
          uploadedAt: new Date().toISOString(),
          category
        }
      };

      // Process document with RAG service
      const result = await this.ragService.addDocument(documentRequest, true);

      // Clean up uploaded file
      fs.unlinkSync(filePath);

      res.json({
        success: true,
        message: 'Document uploaded and processed successfully',
        data: {
          documentId: result.documentId,
          chunksCreated: result.chunksCreated,
          embeddingsGenerated: result.embeddingsGenerated,
          title,
          category
        }
      });

    } catch (error) {
      console.error('Error uploading document:', error);
      
      // Clean up file if it exists
      if (req.file?.path && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.status(500).json({
        error: 'Failed to upload and process document',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get all knowledge bases
   */
  getKnowledgeBases = async (req: Request, res: Response): Promise<void> => {
    try {
      const knowledgeBases = await this.ragService.getKnowledgeBases();
      res.json({
        success: true,
        data: knowledgeBases
      });
    } catch (error) {
      console.error('Error getting knowledge bases:', error);
      res.status(500).json({
        error: 'Failed to get knowledge bases',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Create new knowledge base
   */
  createKnowledgeBase = async (req: Request, res: Response): Promise<void> => {
    try {
      const { name, description, category } = req.body;

      if (!name || !category) {
        res.status(400).json({ error: 'Name and category are required' });
        return;
      }

      const request: CreateKnowledgeBaseRequest = {
        name,
        description,
        category
      };

      const knowledgeBase = await this.ragService.createKnowledgeBase(request);

      res.json({
        success: true,
        message: 'Knowledge base created successfully',
        data: knowledgeBase
      });

    } catch (error) {
      console.error('Error creating knowledge base:', error);
      res.status(500).json({
        error: 'Failed to create knowledge base',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Search documents
   */
  searchDocuments = async (req: Request, res: Response): Promise<void> => {
    try {
      const { query, limit = 10, threshold = 0.7, categories } = req.query;

      if (!query || typeof query !== 'string') {
        res.status(400).json({ error: 'Query parameter is required' });
        return;
      }

      const searchRequest = {
        query,
        limit: parseInt(limit as string),
        threshold: parseFloat(threshold as string),
        categories: categories ? (categories as string).split(',') : undefined
      };

      const results = await this.ragService.search(searchRequest);

      res.json({
        success: true,
        data: {
          query: results.query,
          results: results.results,
          totalResults: results.totalResults,
          executionTimeMs: results.executionTimeMs,
          context: results.context
        }
      });

    } catch (error) {
      console.error('Error searching documents:', error);
      res.status(500).json({
        error: 'Failed to search documents',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get RAG statistics
   */
  getStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const stats = await this.ragService.getPerformanceMetrics();
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Error getting RAG statistics:', error);
      res.status(500).json({
        error: 'Failed to get statistics',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get recent queries
   */
  getRecentQueries = async (req: Request, res: Response): Promise<void> => {
    try {
      const { limit = 20 } = req.query;
      const queries = await this.ragService.getRecentQueries(parseInt(limit as string));
      
      res.json({
        success: true,
        data: queries
      });
    } catch (error) {
      console.error('Error getting recent queries:', error);
      res.status(500).json({
        error: 'Failed to get recent queries',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get all documents
   */
  getDocuments = async (req: Request, res: Response): Promise<void> => {
    try {
      const documents = await this.ragService.getDocuments();
      
      res.json({
        success: true,
        data: documents
      });
    } catch (error) {
      console.error('Error getting documents:', error);
      res.status(500).json({
        error: 'Failed to get documents',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Delete document
   */
  deleteDocument = async (req: Request, res: Response): Promise<void> => {
    try {
      const { documentId } = req.params;

      if (!documentId) {
        res.status(400).json({ error: 'Document ID is required' });
        return;
      }

      const result = await this.ragService.deleteDocument(documentId);

      if (!result.success) {
        res.status(404).json({ 
          error: 'Document not found or could not be deleted',
          details: result.error 
        });
        return;
      }

      res.json({
        success: true,
        message: 'Document deleted successfully',
        data: {
          documentId,
          chunksDeleted: result.chunksDeleted,
          embeddingsDeleted: result.embeddingsDeleted
        }
      });

    } catch (error) {
      console.error('Error deleting document:', error);
      res.status(500).json({
        error: 'Failed to delete document',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get active RAG settings
   */
  getSettings = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.query.userId as string;
      const settings = await this.settingsService.getActiveSettings(userId);
      
      res.json({
        success: true,
        data: settings
      });
    } catch (error) {
      console.error('[RAGController] Error getting settings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get RAG settings'
      });
    }
  };

  /**
   * Save RAG settings
   */
  saveSettings = async (req: Request, res: Response): Promise<void> => {
    try {
      // Accept settings directly from body or from nested settings property
      const settings: RAGModuleSettings = req.body.settings || req.body;
      const userId = req.body.userId as string;
      const name = req.body.name || 'default';

      if (!settings) {
        res.status(400).json({
          success: false,
          error: 'Settings data is required'
        });
        return;
      }

      const result = await this.settingsService.saveSettings(settings, userId, name);
      
      if (result.success) {
        res.json({
          success: true,
          message: 'Settings saved successfully'
        });
      } else {
        res.status(500).json({
          success: false,
          error: result.error || 'Failed to save settings'
        });
      }
    } catch (error) {
      console.error('[RAGController] Error saving settings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to save RAG settings'
      });
    }
  };

  /**
   * Get all settings configurations
   */
  getAllSettings = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.query.userId as string;
      const settings = await this.settingsService.getAllSettings(userId);
      
      res.json({
        success: true,
        data: settings
      });
    } catch (error) {
      console.error('[RAGController] Error getting all settings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get settings list'
      });
    }
  };

  /**
   * Delete settings configuration
   */
  deleteSettings = async (req: Request, res: Response): Promise<void> => {
    try {
      const settingsId = req.params.settingsId;
      
      if (!settingsId) {
        res.status(400).json({
          success: false,
          error: 'Settings ID is required'
        });
        return;
      }

      const result = await this.settingsService.deleteSettings(settingsId);
      
      if (result.success) {
        res.json({
          success: true,
          message: 'Settings deleted successfully'
        });
      } else {
        res.status(500).json({
          success: false,
          error: result.error || 'Failed to delete settings'
        });
      }
    } catch (error) {
      console.error('[RAGController] Error deleting settings:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete settings'
      });
    }
  };

  /**
   * Test endpoint
   */
  test = async (req: Request, res: Response): Promise<void> => {
    try {
      res.json({
        success: true,
        message: 'RAG Controller is working',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Test endpoint error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  };

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    await this.ragService.disconnect();
  }
}

// Export multer upload middleware
export const uploadMiddleware = upload.single('document');

export default RAGController;