import { relations } from "drizzle-orm/relations";
import { stoerungen, stoerungsComments, stoerungsAttachment } from "./schema";

// Relations für die SFM Dashboard-Tabellen
export const stoerungenRelations = relations(stoerungen, ({ many }) => ({
  comments: many(stoerungsComments),
  attachments: many(stoerungsAttachment),
}));

export const stoerungenCommentsRelations = relations(stoerungsComments, ({ one }) => ({
  stoerung: one(stoerungen, {
    fields: [stoerungsComments.stoerungId],
    references: [stoerungen.id]
  }),
}));

export const stoerungenAttachmentRelations = relations(stoerungsAttachment, ({ one }) => ({
  stoerung: one(stoerungen, {
    fields: [stoerungsAttachment.stoerungId],
    references: [stoerungen.id]
  }),
}));

