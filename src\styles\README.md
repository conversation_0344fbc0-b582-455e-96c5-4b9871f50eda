# Label Authentication Styling System

This directory contains the complete styling system for the label authentication feature. The system provides a cohesive label-print aesthetic.

## Files Overview

- **`label-auth.css`** - Main stylesheet with all label styling rules
- **`labelStyles.ts`** - TypeScript utilities for programmatic style application
- **`LabelStyleDemo.tsx`** - React component demonstrating all styling features
- **`label-auth-test.html`** - Static HTML test file for browser testing
- **`responsive-test.html`** - Comprehensive responsive behavior test

## Quick Start

### 1. Import the CSS
```css
@import '@/styles/label-auth.css';
```

### 2. Use TypeScript Utilities
```typescript
import { 
  LABEL_CLASSES, 
  getLabelFormClasses, 
  getLabelButtonClasses 
} from '@/utils/labelStyles';
```

### 3. Apply Classes
```jsx
<div className={getLabelFormClasses()}>
  <input className={LABEL_CLASSES.input} />
  <button className={getLabelButtonClasses('primary')}>
    Submit
  </button>
</div>
```

## Core Features

### Typography
- **Monospace fonts** for label-print aesthetic
- **High-contrast text** with shadows for readability
- **Three text hierarchies**: primary, secondary, muted

### Form Elements
- **Semi-transparent backgrounds** for overlay effects
- **Label-style borders** with consistent styling
- **Interactive states** with visual feedback

### Responsive Design
- **Mobile-first approach** with breakpoints at 480px, 768px
- **Flexible layouts** that adapt to screen size
- **Touch-friendly sizing** on mobile devices

### Accessibility
- **High contrast mode** support
- **Reduced motion** preferences respected
- **Screen reader** compatibility
- **Keyboard navigation** support

## CSS Classes Reference

### Typography Classes
```css
.label-typography          /* Base monospace typography */
.label-text-primary        /* High-contrast primary text */
.label-text-secondary      /* Medium-contrast secondary text */
.label-text-muted          /* Low-contrast muted text */
```

### Form Container Classes
```css
.label-form-container      /* Main form container */
.label-form-background     /* Semi-transparent background */
```

### Form Element Classes
```css
.label-input              /* Text inputs and textareas */
.label-button             /* Base button styling */
.label-button-primary     /* Primary button variant */
.label-form-label         /* Form field labels */
```

### Message Classes
```css
.label-error              /* Error message styling */
.label-success            /* Success message styling */
```

### State Classes
```css
.label-loading            /* Loading state with animation */
.label-focus-visible      /* Enhanced focus indicators */
```

### Layout Utility Classes
```css
.label-stack              /* Vertical stack layout */
.label-row                /* Horizontal row layout */
.label-center             /* Center alignment */
.label-full-width         /* Full width elements */
.label-sr-only            /* Screen reader only content */
```

## TypeScript Utilities

### Class Name Constants
```typescript
import { LABEL_CLASSES } from '@/utils/labelStyles';

// Usage
<div className={LABEL_CLASSES.formContainer}>
  <input className={LABEL_CLASSES.input} />
</div>
```

### Helper Functions
```typescript
import { 
  getLabelFormClasses,
  getLabelButtonClasses,
  getLabelTextClasses,
  combineClasses
} from '@/utils/labelStyles';

// Form container with additional classes
const formClass = getLabelFormClasses('my-custom-class');

// Button with variant
const buttonClass = getLabelButtonClasses('primary', 'w-full');

// Text with hierarchy
const textClass = getLabelTextClasses('secondary');

// Combine multiple classes
const combinedClass = combineClasses(
  LABEL_CLASSES.input,
  'border-red-500',
  error && 'shake-animation'
);
```

### Responsive Utilities
```typescript
import { 
  getCurrentBreakpoint,
  matchesBreakpoint,
  LABEL_BREAKPOINTS
} from '@/utils/labelStyles';

// Check current breakpoint
const breakpoint = getCurrentBreakpoint(); // 'mobile' | 'tablet' | 'desktop' | 'large'

// Check if matches specific breakpoint
const isMobile = matchesBreakpoint('mobile'); // boolean

// Access breakpoint values
const mobileWidth = LABEL_BREAKPOINTS.mobile; // 480
```

### Accessibility Helpers
```typescript
import { LABEL_ACCESSIBILITY } from '@/utils/labelStyles';

// Form ARIA attributes
const inputProps = LABEL_ACCESSIBILITY.getFormAriaAttributes(
  'username-input',
  hasError,
  'username-error'
);

// Button ARIA attributes
const buttonProps = LABEL_ACCESSIBILITY.getButtonAriaAttributes(
  isLoading,
  isDisabled
);

// Screen reader text
const loadingText = LABEL_ACCESSIBILITY.getLoadingText('Login');
const validationText = LABEL_ACCESSIBILITY.getValidationText('Username', error);
```

## Responsive Breakpoints

The system uses the following breakpoints:

- **Mobile**: ≤ 480px
- **Tablet**: ≤ 768px  
- **Desktop**: ≤ 1024px
- **Large**: > 1024px

### Mobile Adaptations
- Larger touch targets (44px minimum)
- Full-width buttons
- Increased padding and margins
- Simplified layouts

### Tablet Adaptations
- Medium-sized touch targets
- Flexible grid layouts
- Optimized for both portrait and landscape

## Accessibility Features

### Motion Preferences
```css
@media (prefers-reduced-motion: reduce) {
  /* Animations disabled */
  .label-button { transition: none; }
  .label-loading::after { animation: none; }
}
```

### High Contrast Mode
```css
@media (prefers-contrast: high) {
  /* Enhanced contrast */
  .label-form-container { 
    background: white;
    border-width: 3px;
  }
}
```

### Screen Reader Support
- Semantic HTML structure
- ARIA labels and descriptions
- Screen reader only content with `.label-sr-only`
- Live regions for dynamic content

### Keyboard Navigation
- Visible focus indicators
- Logical tab order
- Skip links where appropriate
- Keyboard shortcuts support

## Testing

### Browser Testing
Open `responsive-test.html` in different browsers to test:
- Visual appearance
- Responsive behavior
- Interactive elements
- Accessibility features

### Automated Testing
```bash
# TypeScript compilation
npx tsc --noEmit --skipLibCheck src/utils/labelStyles.ts

# CSS validation (if using stylelint)
npx stylelint src/styles/label-auth.css
```

### Manual Testing Checklist
- [ ] Typography renders correctly in monospace
- [ ] Forms are readable over backgrounds
- [ ] Buttons provide visual feedback on interaction
- [ ] Responsive breakpoints work as expected
- [ ] High contrast mode is supported
- [ ] Reduced motion preferences are respected
- [ ] Screen readers can navigate forms
- [ ] Keyboard navigation works properly

## Integration with Backgrounds

The styling system is designed to work seamlessly with various backgrounds:

1. **Semi-transparent backgrounds** allow background elements to show through
2. **High-contrast text** ensures readability
3. **Subtle shadows** provide depth
4. **Monospace typography** maintains the label aesthetic

### Background Overlay Best Practices
- Use `label-form-background` for main form containers
- Apply `label-text-primary` for important text
- Use `label-button-primary` for primary actions
- Test with actual background content for optimal contrast

## Customization

### CSS Custom Properties
The system uses CSS custom properties for easy theming:

```css
:root {
  --label-white: #ffffff;
  --label-black: #000000;
  --label-gray-light: rgba(255, 255, 255, 0.9);
  --label-font-primary: 'Courier New', monospace;
  /* ... more properties */
}
```

### Extending Styles
Add custom styles while maintaining the label aesthetic:

```css
.my-custom-form {
  @extend .label-form-container;
  /* Additional custom styles */
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}
```

## Performance Considerations

- **Minimal CSS footprint** (~8.7KB)
- **Efficient selectors** for fast rendering
- **Hardware acceleration** for animations
- **Optimized for mobile** performance

## Browser Support

- **Modern browsers**: Full support (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+)
- **Legacy browsers**: Graceful degradation
- **Mobile browsers**: Optimized experience
- **Accessibility tools**: Full compatibility

## Troubleshooting

### Common Issues

1. **Fonts not loading**: Ensure monospace fonts are available
2. **Transparency not working**: Check backdrop-filter support
3. **Animations not smooth**: Verify hardware acceleration
4. **Mobile layout issues**: Test actual device sizes

### Debug Utilities
```typescript
import { debugLabelStyles, validateLabelStyles } from '@/utils/labelStyles';

// Log current styling state
debugLabelStyles();

// Validate styles are loaded
const isValid = validateLabelStyles();
```

## Contributing

When adding new styles:
1. Follow the existing naming convention
2. Add responsive variants
3. Include accessibility considerations
4. Update TypeScript utilities
5. Test across browsers and devices
6. Document new features

## Related Files

- `src/hooks/useMotionPreference.ts` - Motion preference detection