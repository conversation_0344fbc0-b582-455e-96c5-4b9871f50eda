'use client';
import React, { useState, useMemo, useCallback, memo, PropsWithChildren } from 'react';

// Shadcn-kompatible API: Accordion, AccordionItem, AccordionTrigger, AccordionContent
// Hinweis: Dies ist eine minimale Implementierung, die visuell/semantisch kompatibel ist,
// damit bestehende Importe in der App (z. B. NavigationBar) funktionieren.

// Root
type AccordionType = 'single' | 'multiple';
interface AccordionRootProps extends React.HTMLAttributes<HTMLDivElement> {
  type?: AccordionType;
  collapsible?: boolean;
}
export function Accordion(props: PropsWithChildren<AccordionRootProps>) {
  const { className, children, ...rest } = props;
  return (
    <div className={className} {...rest}>
      {children}
    </div>
  );
}

// Item
interface AccordionItemProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string;
}
export function AccordionItem({ value, className, children, ...rest }: PropsWithChildren<AccordionItemProps>) {
  return (
    <div data-accordion-item={value} className={className} {...rest}>
      {children}
    </div>
  );
}

// Trigger
interface AccordionTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {}
export function AccordionTrigger({ className, children, ...rest }: PropsWithChildren<AccordionTriggerProps>) {
  return (
    <button
      type="button"
      className={className}
      {...rest}
    >
      {children}
    </button>
  );
}

// Content
interface AccordionContentProps extends React.HTMLAttributes<HTMLDivElement> {}
export function AccordionContent({ className, children, ...rest }: PropsWithChildren<AccordionContentProps>) {
  return (
    <div className={className} {...rest}>
      {children}
    </div>
  );
}

// Zusätzlich den bisherigen Default-Accordion (mit items[]) bereitstellen,
// falls andere Stellen diesen verwenden.
interface SimpleAccordionItemData {
  title: string;
  content: string;
}
interface SimpleAccordionProps {
  items: SimpleAccordionItemData[];
}
const SimpleAccordionItem = memo(function SimpleAccordionItem({
  title,
  content,
  isOpen,
  onClick,
  isLast,
}: {
  title: string;
  content: string;
  isOpen: boolean;
  onClick: () => void;
  isLast: boolean;
}) {
  const uniqueId = useMemo(() => title.replace(/\s+/g, '-'), [title]);
  const containerClasses = useMemo(
    () => (!isLast ? 'border-b border-gray-200 dark:border-slate-700' : ''),
    [isLast]
  );
  const buttonClasses = useMemo(
    () =>
      'w-full flex justify-between items-center p-5 text-left text-lg font-medium text-gray-800 dark:text-slate-200 hover:bg-gray-100 dark:hover:bg-slate-700/50 focus:outline-none transition-colors duration-300',
    []
  );
  const contentClasses = useMemo(
    () =>
      `grid overflow-hidden transition-all duration-500 ease-in-out ${
        isOpen ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0'
      }`,
    [isOpen]
  );

  return (
    <div className={containerClasses}>
      <button
        type="button"
        className={buttonClasses}
        onClick={onClick}
        aria-expanded={isOpen}
        aria-controls={`accordion-content-${uniqueId}`}
        id={`accordion-header-${uniqueId}`}
      >
        <span>{title}</span>
        <div className="w-6 h-6 flex-shrink-0 flex items-center justify-center">+</div>
      </button>

      <div
        id={`accordion-content-${uniqueId}`}
        role="region"
        aria-labelledby={`accordion-header-${uniqueId}`}
        className={contentClasses}
      >
        <div className="overflow-hidden">
          <div className="p-5 pt-2 text-gray-600 dark:text-slate-400">
            <p>{content}</p>
          </div>
        </div>
      </div>
    </div>
  );
});

export default function DefaultAccordion({ items }: SimpleAccordionProps) {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const handleClick = useCallback((index: number) => {
    setOpenIndex(prevIndex => (prevIndex === index ? null : index));
  }, []);

  const containerClasses = useMemo(
    () =>
      'rounded-xl shadow-lg bg-gray-50 dark:bg-slate-800/60 border border-gray-200 dark:border-slate-700 backdrop-blur-sm',
    []
  );

  return (
    <div className={containerClasses}>
      {items.map((item, index) => (
        <SimpleAccordionItem
          key={`${item.title}-${index}`}
          title={item.title}
          content={item.content}
          isOpen={openIndex === index}
          onClick={() => handleClick(index)}
          isLast={index === items.length - 1}
        />
      ))}
    </div>
  );
}