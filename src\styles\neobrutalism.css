/**
 * Neobrutalism-Stile für die Shopfloor-Management-App
 * 
 * Diese Datei enthält alle spezifischen Stile für den Neobrutalism-Look:
 * - Kräftige Farben
 * - Starke schwarze Umrandungen
 * - Versetzungen für 3D-Effekte
 * - G<PERSON>, direkte visuelle Elemente
 */

/* Grundlegende Neobrutalism-Karte */
.neo-brutalism-card {
  border: 3px solid #000;
  border-radius: 8px;
  box-shadow: 5px 5px 0 0 #000;
  transition: transform 0.2s, box-shadow 0.2s;
  background-color: #fff;
  overflow: hidden;
}

.neo-brutalism-card:hover {
  transform: translate(-2px, -2px);
  box-shadow: 7px 7px 0 0 #000;
}

/* Dark Mode für Karten */
.dark .neo-brutalism-card {
  background-color: #ffffff;
  border-color: #000000;
  box-shadow: 5px 5px 0 0 #000000;
}

.dark .neo-brutalism-card:hover {
  box-shadow: 7px 7px 0 0 #000000;
}

/* Neobrutalism-Diagramm */
.neo-brutalism-chart {
  border: 3px solid #000;
  border-radius: 8px;
  box-shadow: 5px 5px 0 0 #000;
  padding: 8px;
  background-color: #fff;
}

/* Neobrutalism-Balken in Diagrammen */
.neo-brutalism-bar {
  stroke: #000;
  stroke-width: 2px;
}

/* Neobrutalism-Kreisdiagramm */
.neo-brutalism-pie {
  stroke: #000;
  stroke-width: 2px;
}

/* Neobrutalism-Sidebar */
.neo-brutalism-sidebar {
  border-right: 4px solid #000;
  background-color: #fff;
}

.dark .neo-brutalism-sidebar {
  background-color: #272933;
  border-right-color: #ffffff;
}

/* Neobrutalism-Button */
.neo-brutalism-button {
  border: 3px solid #000;
  border-radius: 8px;
  box-shadow: 4px 4px 0 0 #000;
  transition: transform 0.2s, box-shadow 0.2s;
  background-color: #fff;
}

.neo-brutalism-button:hover {
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0 0 #000;
}

.neo-brutalism-button:active {
  transform: translate(2px, 2px);
  box-shadow: 1px 1px 0 0 #000;
}

/* Neobrutalism-Typografie */
.neo-brutalism-title {
  font-family: 'Inter', sans-serif;
  font-weight: 800;
  letter-spacing: -0.025em;
  color: var(--neo-text);
}

/* Neobrutalism-Abschnitt */
.neo-brutalism-section {
  border: 3px solid var(--neo-border);
  border-radius: 8px;
  box-shadow: 5px 5px 0 0 var(--neo-border);
  padding: 16px;
  background-color: #fff;
  margin-bottom: 24px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.neo-brutalism-section:hover {
  transform: translate(-2px, -2px);
  box-shadow: 7px 7px 0 0 var(--neo-border);
}

.dark .neo-brutalism-section {
  background-color: #ffffff;
}

/* Neobrutalism-Header */
.neo-brutalism-header {
  border-bottom: 3px solid var(--neo-border);
  padding-bottom: 8px;
  margin-bottom: 16px;
}

/* Neobrutalism-Footer */
.neo-brutalism-footer {
  border-top: 3px solid var(--neo-border);
  padding-top: 8px;
  margin-top: 16px;
}

/* Neobrutalism-Badge */
.neo-brutalism-badge {
  border: 2px solid var(--neo-border);
  border-radius: 4px;
  padding: 2px 8px;
  font-weight: bold;
  display: inline-block;
  background-color: #fff;
  color: var(--neo-text);
}

.dark .neo-brutalism-badge {
  background-color: #ffffff;
  color: #000;
}

/* Neobrutalism Input-Felder */
.neo-brutalism-input {
  border: 3px solid var(--neo-border);
  border-radius: 8px;
  padding: 12px 16px;
  background-color: #fff;
  color: var(--neo-text);
  font-weight: 500;
  transition: all 0.2s;
}

.neo-brutalism-input:focus {
  transform: translate(-2px, -2px);
  box-shadow: 4px 4px 0 0 var(--neo-border);
  outline: none;
}

.dark .neo-brutalism-input {
  background-color: #ffffff;
  color: #000;
}

/* Neobrutalism Utility-Klassen */
.neo-shadow {
  box-shadow: 4px 4px 0 0 var(--neo-border);
}

.neo-shadow-lg {
  box-shadow: 6px 6px 0 0 var(--neo-border);
}

.neo-shadow-xl {
  box-shadow: 8px 8px 0 0 var(--neo-border);
}

.neo-hover {
  transition: all 0.2s ease;
}

.neo-hover:hover {
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0 0 var(--neo-border);
}

/* Neobrutalism Animationen */
@keyframes neo-bounce {
  0%, 100% {
    transform: translate(0, 0);
    box-shadow: 4px 4px 0 0 var(--neo-border);
  }
  50% {
    transform: translate(-2px, -2px);
    box-shadow: 6px 6px 0 0 var(--neo-border);
  }
}

.neo-bounce {
  animation: neo-bounce 1s ease-in-out infinite;
}

/* Responsive Design für Neobrutalism-Elemente */
@media (max-width: 768px) {
  .neo-brutalism-card,
  .neo-brutalism-section {
    border-width: 2px;
    box-shadow: 3px 3px 0 0 var(--neo-border);
  }
  
  .neo-brutalism-card:hover,
  .neo-brutalism-section:hover {
    transform: translate(-1px, -1px);
    box-shadow: 4px 4px 0 0 var(--neo-border);
  }
  
  .neo-brutalism-button {
    border-width: 2px;
    box-shadow: 3px 3px 0 0 var(--neo-border);
  }
  
  .neo-brutalism-button:hover {
    box-shadow: 4px 4px 0 0 var(--neo-border);
  }
}