import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Play,
  Pause,
  RotateCcw,
  Clock,
  Database,
  FileText,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Timer
} from "lucide-react";
import {
  Workflow,
  WorkflowCardProps,
  WORKFLOW_STATUS_CONFIG,
  WORKFLOW_SOURCE_CONFIG,
  WORKFLOW_FREQUENCY_CONFIG
} from "@/types/workflow.types";

export function WorkflowCard({ workflow, onToggle, onRestart, onViewLogs }: WorkflowCardProps) {
  const statusConfig = WORKFLOW_STATUS_CONFIG[workflow.status];
  const sourceConfig = WORKFLOW_SOURCE_CONFIG[workflow.sourceType];
  const frequencyConfig = WORKFLOW_FREQUENCY_CONFIG[workflow.frequency];

  const formatDuration = (seconds: number | null): string => {
    if (!seconds) return 'N/A';
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
  };

  const formatLastExecution = (date: Date | null): string => {
    if (!date) return 'Nie ausgeführt';

    const now = new Date();
    const diffMs = now.getTime() - new Date(date).getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 1) return 'Gerade eben';
    if (diffMinutes < 60) return `Vor ${diffMinutes} Minuten`;
    if (diffMinutes < 1440) return `Vor ${Math.floor(diffMinutes / 60)} Stunden`;
    return `Vor ${Math.floor(diffMinutes / 1440)} Tagen`;
  };

  const formatNextExecution = (date: Date | null): string => {
    if (!date || !workflow.isActive) return 'Nicht geplant';

    const now = new Date();
    const diffMs = new Date(date).getTime() - now.getTime();

    if (diffMs <= 0) return 'Überfällig';

    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 60) return `In ${diffMinutes} Minuten`;
    if (diffMinutes < 1440) return `In ${Math.floor(diffMinutes / 60)} Stunden`;
    return `In ${Math.floor(diffMinutes / 1440)} Tagen`;
  };

  const getStatusIcon = () => {
    switch (workflow.status) {
      case 'running':
        return <div className="animate-pulse">🟢</div>;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'scheduled':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'disabled':
        return <Pause className="h-4 w-4 text-gray-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const canRestart = workflow.status === 'failed' || workflow.status === 'completed';
  const isRunning = workflow.status === 'running';

  return (
    <Card className="border-2 hover:shadow-lg transition-all duration-200 bg-white">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-lg font-bold text-gray-900 flex items-center gap-2">
              {getStatusIcon()}
              {workflow.name}
            </CardTitle>
            <div className="text-sm text-gray-600 mt-1">
              {workflow.description}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {/* Source Type Badge */}
            <Badge
              className={`${sourceConfig.bgColor} ${sourceConfig.textColor} border-0`}
            >
              {sourceConfig.icon} {sourceConfig.label}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-2">
        {/* Status und Frequenz */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-xs text-gray-500 uppercase tracking-wide">Status</div>
            <Badge
              className={cn(
                "text-xs font-medium shadow-sm border-0 mt-1",
                workflow.status === 'running' && "bg-blue-500 text-white",
                workflow.status === 'completed' && "bg-green-500 text-white", 
                workflow.status === 'failed' && "bg-red-500 text-white",
                workflow.status === 'scheduled' && "bg-yellow-500 text-white",
                workflow.status === 'disabled' && "bg-gray-500 text-white"
              )}
            >
              {statusConfig.label}
            </Badge>
          </div>
          <div>
            <div className="text-xs text-gray-500 uppercase tracking-wide">Frequenz</div>
            <div className="text-sm font-medium mt-1">
              {frequencyConfig.icon} {frequencyConfig.label}
            </div>
          </div>
        </div>

        {/* Ausführungsinfo */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-500">Letzte Ausführung:</span>
            <span className="text-sm font-medium">
              {formatLastExecution(workflow.lastExecution)}
            </span>
          </div>

          {workflow.isActive && (
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-500">Nächste Ausführung:</span>
              <span className="text-sm font-medium">
                {formatNextExecution(workflow.nextExecution)}
              </span>
            </div>
          )}

          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-500">Letzte Dauer:</span>
            <span className="text-sm font-medium flex items-center gap-1">
              <Timer className="h-3 w-3" />
              {formatDuration(workflow.duration)}
            </span>
          </div>
        </div>

        {/* Performance Metriken */}
        <div className="grid grid-cols-2 gap-4 p-3 bg-gray-50 rounded-lg border-2">
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">
              {workflow.successRate.toFixed(1)}%
            </div>
            <div className="text-xs text-gray-500">Erfolgsrate</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">
              {workflow.totalRuns}
            </div>
            <div className="text-xs text-gray-500">Ausführungen</div>
          </div>
        </div>

        {/* Ziel-Tabellen */}
        <div>
          <div className="text-xs text-gray-500 uppercase tracking-wide mb-2">
            Ziel-Tabellen ({workflow.targetTables.length})
          </div>
          <div className="flex flex-wrap gap-1">
            {workflow.targetTables.slice(0, 3).map((table) => (
              <Badge
                key={table}
                variant="outline"
                className="text-xs bg-blue-50 text-blue-700 border-blue-200"
              >
                <Database className="h-3 w-3 mr-1" />
                {table}
              </Badge>
            ))}
            {workflow.targetTables.length > 3 && (
              <Badge
                variant="outline"
                className="text-xs bg-gray-50 text-gray-600"
              >
                +{workflow.targetTables.length - 3} weitere
              </Badge>
            )}
          </div>
        </div>

        {/* Steuerungsbuttons */}
        <div className="space-y-3 pt-2 border-t">
          <div className="flex items-center gap-2">
            <Switch
              checked={workflow.isActive}
              onCheckedChange={() => onToggle(workflow.id)}
              disabled={isRunning}
            />
            <span className="text-sm text-gray-600">
              {workflow.isActive ? 'Aktiv' : 'Deaktiviert'}
            </span>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewLogs(workflow.id)}
              className="w-fit px-4"
            >
              <FileText className="h-3 w-3 mr-2" />
              Logs
            </Button>

            {canRestart && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onRestart(workflow.id)}
                className="w-fit px-4"
                disabled={!workflow.isActive}
              >
                <RotateCcw className="h-3 w-3 mr-2" />
                Restart
              </Button>
            )}
          </div>
        </div>

        {/* Fehler-Anzeige */}
        {workflow.status === 'failed' && workflow.errorCount > 0 && (
          <div className="p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
            <AlertTriangle className="h-3 w-3 inline mr-1" />
            {workflow.errorCount} Fehler in den letzten Ausführungen
          </div>
        )}
      </CardContent>
    </Card>
  );
}