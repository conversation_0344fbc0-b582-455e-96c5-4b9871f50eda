# Implementation Plan

- [x] 1. Erstelle modulare Verzeichnisstruktur und Basis-Konfiguration





  - Erstelle die vier Hauptmodulverzeichnisse mit pages/ und components/ Unterordnern
  - Implementiere ModuleConfig Interface und Basis-Konfigurationsdateien für jedes Modul
  - Erstelle index.ts Dateien für saubere Modul-Exports
  - _Requirements: 1.1, 1.2, 1.3_

- [x] 2. Implementiere Leitstand-Modul Migration





- [x] 2.1 Verschiebe Leitstand-Seiten in Modulstruktur


  - Verschiebe HomePage, DispatchPage, CuttingPage, IncomingGoodsPage, ArilPage, AtrlPage, MachinesPage nach `src/modules/leitstand/pages/`
  - Aktualisiere alle Import-Pfade in den verschobenen Dateien
  - Erstelle Leitstand-spezifische index.ts Dateien für Page-Exports
  - _Requirements: 2.1, 2.4_

- [x] 2.2 Verschiebe Leitstand-Komponenten in Modulstruktur


  - Verschiebe alle Chart-Komponenten aus `src/components/charts/` nach `src/modules/leitstand/components/charts/`
  - Verschiebe alle Stats-Komponenten aus `src/components/stats/` nach `src/modules/leitstand/components/stats/`
  - Aktualisiere Import-Pfade in allen Leitstand-Komponenten
  - _Requirements: 2.2, 2.3, 2.4_

- [x] 3. Implementiere Störungen-Modul Migration





- [x] 3.1 Verschiebe Störungen-Seiten und Komponenten



  - Verschiebe StoerungenPage nach `src/modules/stoerungen/pages/`
  - Verschiebe alle Komponenten aus `src/components/stoerungen/` nach `src/modules/stoerungen/components/stoerungen/`
  - Verschiebe alle Monitoring-Komponenten aus `src/components/monitoring/` nach `src/modules/stoerungen/components/monitoring/`
  - _Requirements: 3.1, 3.2, 3.3_



- [x] 3.2 Aktualisiere Störungen-Import-Pfade





  - Aktualisiere alle Import-Pfade in StoerungenPage und zugehörigen Komponenten
  - Erstelle Störungen-spezifische index.ts Dateien für saubere Exports
  - Teste Störungen-Modul Funktionalität nach der Migration
  - _Requirements: 3.1, 3.2, 3.3_

- [-] 4. Implementiere Backend & Automatisierung-Modul Migration


- [x] 4.1 Verschiebe Backend-Seiten und Komponenten



  - Verschiebe SystemPage und WorkflowPage nach `src/modules/backend/pages/`
  - Verschiebe alle Workflow-Komponenten aus `src/components/workflows/` nach `src/modules/backend/components/workflows/`
  - Aktualisiere Import-Pfade in Backend-Modul Dateien
  - _Requirements: 4.1, 4.2, 4.3_

- [x] 5. Implementiere AI-Modul Migration





- [x] 5.1 Verschiebe AI-Komponenten in Modulstruktur


  - Verschiebe alle AI-Komponenten aus `src/components/ai/` nach `src/modules/ai/components/ai/`
  - Verschiebe alle Chat-Komponenten aus `src/components/chat/` nach `src/modules/ai/components/chat/`
  - Erstelle AI-Modul pages/ Verzeichnis für zukünftige AI-Seiten
  - _Requirements: 5.1, 5.2, 5.3_

- [x] 5.2 Aktualisiere AI-Modul Import-Pfade


  - Aktualisiere alle Import-Pfade in AI- und Chat-Komponenten
  - Erstelle AI-spezifische index.ts Dateien für Komponenten-Exports
  - Teste AI-Komponenten Funktionalität nach der Migration
  - _Requirements: 5.1, 5.2, 5.4_

- [x] 6. Implementiere UserLandingPage Modulauswahl-Funktionalität





- [x] 6.1 Erweitere UserLandingPage für Modulnavigation


  - Implementiere ModuleCard Interface und Komponente für Modulauswahl
  - Erstelle Navigation-Handler für Modul-Routing
  - Integriere bestehende CardCarousel mit neuen Modul-Links
  - _Requirements: 6.1, 6.2, 6.3_

- [x] 6.2 Implementiere rollenbasierte Modulanzeige


  - Erstelle ModuleAccessService für Rollen-basierte Modulfilterung
  - Implementiere bedingte Anzeige von Modulen basierend auf Benutzerrollen
  - Erstelle Tests für verschiedene Rollen-Szenarien (Besucher, Benutzer, Administrator)
  - _Requirements: 8.1, 8.2, 8.3_

- [x] 7. Implementiere neue Routing-Struktur





- [x] 7.1 Erstelle modulare Route-Konfiguration


  - Implementiere TanStack Router Konfiguration für neue Modulpfade (/modules/*)
  - Erstelle Route Guards für Modul-spezifischen Zugriff
  - Implementiere ModuleGuard Komponente für Zugriffskontrolle
  - _Requirements: 7.3, 7.4, 8.4_

- [x] 7.2 Aktualisiere Navigation und Breadcrumbs






  - Implementiere NavigationState Management für modulare Navigation
  - Erstelle Breadcrumb-System für Modul-Navigation
  - Aktualisiere Sidebar-Navigation für neue Modulstruktur
  - _Requirements: 7.3, 7.4_

- [x] 8. Globale Import-Pfad Aktualisierung





- [x] 8.1 Systematische Import-Pfad Migration


  - Führe globale Suche und Ersetze für alle verschobenen Komponenten durch
  - Aktualisiere alle relativen Import-Pfade zu @/ Alias-Pfaden
  - Validiere und korrigiere alle Import-Pfade in der gesamten Codebasis
  - _Requirements: 7.1, 7.2_

- [x] 8.2 Teste und validiere Import-Pfade


  - Führe TypeScript-Kompilierung durch, um Import-Fehler zu identifizieren
  - Teste alle Seiten und Komponenten auf korrekte Import-Auflösung
  - Korrigiere verbleibende Import-Probleme
  - _Requirements: 7.1, 7.2_

- [x] 9. Implementiere umfassende Tests für modulare Struktur





- [x] 9.1 Erstelle Modul-spezifische Unit Tests


  - Schreibe Unit Tests für jedes Modul (Leitstand, Störungen, Backend, AI)
  - Teste Modul-spezifische Komponenten und deren Funktionalität
  - Implementiere Tests für ModuleGuard und Navigation-Komponenten
  - _Requirements: 9.1, 9.2_

- [x] 9.2 Implementiere Integration und E2E Tests


  - Erstelle E2E Tests für Modul-Navigation und Zugriffskontrolle
  - Teste rollenbasierte Modulzugriffe für alle Benutzerrollen
  - Implementiere Regression Tests für alle bestehenden Funktionalitäten
  - _Requirements: 9.2, 9.3, 9.4_

- [x] 10. Finale Validierung und Optimierung





- [x] 10.1 Führe umfassende Regressionstests durch


  - Teste alle Leitstand-Funktionen (Charts, KPIs, Navigation) manuell
  - Validiere Störungen- und Backend-Modul Funktionalitäten
  - Teste AI-Komponenten und Chat-Funktionalität
  - _Requirements: 9.3, 9.4_

- [x] 10.2 Performance-Optimierung und Code-Cleanup


  - Implementiere Lazy Loading für Module zur Performance-Verbesserung
  - Entferne nicht verwendete Import-Pfade und Code
  - Optimiere Bundle-Größe durch modulare Code-Aufteilung
  - _Requirements: 9.1, 9.2, 9.3, 9.4_