"use client";

import { cn } from "@/lib/utils";
import { ArrowRight, Repeat2 } from "lucide-react";
import { useState } from "react";
// WICHTIG: expliziter .tsx Import-Pfad für Vite/Electron HMR-Stabilität
import { ShineBorder } from "@/components/Animation/magicui/shine-border";

export interface CardFlipProps {
    title?: string;
    subtitle?: string;
    description?: string;
    features?: string[];
    color?: string;
    isAccessible?: boolean;
    inDevelopment?: boolean;
    onNavigate?: () => void;
    imageSrc?: string;
    imageAlt?: string;
}

export default function CardFlip({
    title = "Design Systems",
    subtitle = "Explore the fundamentals",
    description = "Dive deep into the world of modern UI/UX design.",
    features = ["UI/UX", "Modern Design", "Tailwind CSS", "Kokonut UI"],
    color = "#f67828",
    isAccessible = true,
    inDevelopment = false,
    onNavigate = () => {},
    imageSrc,
    imageAlt = "Module Image",
}: CardFlipProps) {
    const [isFlipped, setIsFlipped] = useState(false);

    const handleClick = () => {
        if (isAccessible && !inDevelopment && onNavigate) {
            onNavigate();
        }
    };

    return (
        <div
            className={cn(
                "relative w-full max-w-[350px] h-[400px] group [perspective:2000px]",
                isAccessible && !inDevelopment ? "cursor-pointer" : "cursor-not-allowed",
                inDevelopment ? "opacity-80" : !isAccessible ? "opacity-60" : ""
            )}
            onMouseEnter={() => setIsFlipped(true)}
            onMouseLeave={() => setIsFlipped(false)}
            onClick={handleClick}
        >
            {/* Variante A: ShineBorder im rotierenden 3D-Container (mitdrehend) */}
            <div className="relative w-full h-full">
                <div
                    className={cn(
                        "relative w-full h-full",
                        "[transform-style:preserve-3d]",
                        "[backface-visibility:visible]",
                        "transition-all duration-700",
                        isFlipped
                            ? "[transform:rotateY(180deg)]"
                            : "[transform:rotateY(0deg)]"
                    )}
                >
                    {/* ShineBorder als mitdrehende, deutlich sichtbare Border */}
                    <div className="pointer-events-none absolute -inset-[6px] rounded-[1.4rem] [transform:translateZ(1px)]">
                        <ShineBorder
                            borderWidth={4}
                            duration={8}
                            shineColor={[color, color.replace("#", "#55")]}
                            className="rounded-[1.4rem]"
                        />
                    </div>
                {/* Front of card */}
                <div
                    className={cn(
                        "absolute inset-0 w-full h-full",
                        "[backface-visibility:hidden] [transform:rotateY(0deg)]",
                        "overflow-hidden rounded-2xl",
                        "bg-zinc-50 dark:bg-zinc-900",
                        "border border-zinc-200 dark:border-zinc-800/50",
                        "shadow-xs dark:shadow-lg",
                        "transition-all duration-700",
                        "group-hover:shadow-lg dark:group-hover:shadow-xl",
                        isFlipped ? "opacity-0" : "opacity-100"
                    )}
                >
                    {imageSrc ? (
                        // Zeige Bild wenn vorhanden - füllt die ganze Karte aus
                        <div className="relative h-full overflow-hidden">
                            <img
                                src={imageSrc}
                                alt={imageAlt}
                                onLoad={() => {
                                    try { console.log('[CardFlip] Image loaded:', imageSrc); } catch {}
                                }}
                                onError={(e) => {
                                    try { console.error('[CardFlip] Image failed to load:', imageSrc, e); } catch {}
                                }}
                                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                            />
                            {/* Overlay für bessere Textlesbarkeit */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                        </div>
                    ) : (
                        // Fallback zu animierten Kreisen mit Hintergrund
                        <div className="relative h-full overflow-hidden bg-gradient-to-b from-zinc-100 to-white dark:from-zinc-900 dark:to-black">
                            <div className="absolute inset-0 flex items-start justify-center pt-24">
                                <div className="relative w-[200px] h-[100px] flex items-center justify-center">
                                    {[...Array(10)].map((_, i) => (
                                        <div
                                            key={i}
                                            className={cn(
                                                "absolute w-[50px] h-[50px]",
                                                "rounded-[140px]",
                                                "animate-[scale_3s_linear_infinite]",
                                                "opacity-0",
                                                "group-hover:animate-[scale_2s_linear_infinite]"
                                            )}
                                            style={{
                                                animationDelay: `${i * 0.3}s`,
                                                boxShadow: `0 0 50px ${color}50`,
                                            }}
                                        />
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}

                    <div className="absolute inset-0 z-10">
                        {/* Titel oben links, größer */}
                        <div className="absolute top-0 left-0 right-0 p-5">
                            <div className="flex items-start justify-between gap-2">
                                <h3 className={cn(
                                    "text-2xl md:text-3xl font-semibold leading-tight tracking-tighter underline underline-offset-4 decoration-2",
                                    "transition-all duration-500 ease-out-expo group-hover:translate-y-[-4px]",
                                    imageSrc ? "text-white drop-shadow-lg" : "text-zinc-900 dark:text-white"
                                )}>
                                    {title}
                                </h3>
                                {inDevelopment && (
                                    <div className={cn(
                                        "px-2 py-1 rounded-md text-xs font-medium",
                                        "bg-orange-500/90 text-white",
                                        "shadow-lg backdrop-blur-sm",
                                        "transition-all duration-500 ease-out-expo group-hover:translate-y-[-4px]",
                                        "animate-pulse"
                                    )}>
                                        In Bearbeitung
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Unterer Bereich: Untertitel unten links, Icon rechts */}
                        <div className="absolute bottom-0 left-0 right-0 p-5">
                            <div className="flex items-center justify-between gap-3">
                                <p className={cn(
                                    "text-base md:text-lg line-clamp-2 tracking-tight",
                                    "transition-all duration-500 ease-out-expo group-hover:translate-y-[-4px] delay-[50ms]",
                                    imageSrc ? "text-white/90 drop-shadow-md" : "text-zinc-600 dark:text-zinc-200"
                                )}>
                                    {subtitle}
                                </p>

                                <div className="relative group/icon shrink-0">
                                    <div
                                        className={cn(
                                            "absolute inset-[-8px] rounded-lg transition-opacity duration-300",
                                            imageSrc ? "bg-white/20" : "bg-gradient-to-br from-orange-500/20 via-orange-500/10 to-transparent"
                                        )}
                                    />
                                    <Repeat2
                                        className="relative z-10 w-4 h-4 transition-transform duration-300 group-hover/icon:scale-110 group-hover/icon:-rotate-12 drop-shadow-lg"
                                        style={{ color: imageSrc ? "white" : color }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Back of card */}
                <div
                    className={cn(
                        "absolute inset-0 w-full h-full",
                        "[backface-visibility:hidden] [transform:rotateY(180deg)]",
                        "p-6 rounded-2xl",
                        "bg-gradient-to-b from-zinc-100 to-white dark:from-zinc-900 dark:to-black",
                        "border border-zinc-200 dark:border-zinc-800",
                        "shadow-xs dark:shadow-lg",
                        "flex flex-col",
                        "transition-all duration-700",
                        "group-hover:shadow-lg dark:group-hover:shadow-xl",
                        !isFlipped ? "opacity-0" : "opacity-100"
                    )}
                >
                    <div className="flex-1 space-y-6">
                        <div className="space-y-2">
                            <div className="flex items-start justify-between gap-2">
                                <h3 className="text-2xl md:text-3xl font-semibold text-zinc-900 dark:text-white leading-snug tracking-tight underline underline-offset-4 decoration-2 transition-all duration-500 ease-out-expo group-hover:translate-y-[-2px]">
                                    {title}
                                </h3>
                                {inDevelopment && (
                                    <div className={cn(
                                        "px-2 py-1 rounded-md text-xs font-medium",
                                        "bg-orange-500/90 text-white",
                                        "shadow-lg",
                                        "transition-all duration-500 ease-out-expo group-hover:translate-y-[-2px]",
                                        "animate-pulse"
                                    )}>
                                        In Bearbeitung
                                    </div>
                                )}
                            </div>
                            <p className="text-base text-zinc-600 dark:text-zinc-400 tracking-tight transition-all duration-500 ease-out-expo group-hover:translate-y-[-2px] line-clamp-2">
                                {inDevelopment ? "Dieses Modul befindet sich derzeit in der Entwicklung und wird bald verfügbar sein." : description}
                            </p>
                        </div>

                        <div className="space-y-2">
                            {features.map((feature, index) => (
                                <div
                                    key={feature}
                                    className="flex items-center gap-2 text-base text-zinc-700 dark:text-zinc-300 transition-all duration-500"
                                    style={{
                                        transform: isFlipped
                                            ? "translateX(0)"
                                            : "translateX(-10px)",
                                        opacity: isFlipped ? 1 : 0,
                                        transitionDelay: `${
                                            index * 100 + 200
                                        }ms`,
                                    }}
                                >
                                    <ArrowRight className="w-3 h-3" style={{ color }} />
                                    <span>{feature}</span>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="pt-6 mt-6 border-t border-zinc-200 dark:border-zinc-800">
                        <div
                            className={cn(
                                "group/start relative",
                                "flex items-center justify-between",
                                "p-3 -m-3 rounded-xl",
                                "transition-all duration-300",
                                // Kräftigere Basisfarben
                                "bg-gradient-to-r from-white via-white to-white",
                                "dark:from-zinc-900 dark:via-zinc-900 dark:to-zinc-900",
                                "hover:scale-[1.02] hover:cursor-pointer",
                                "ring-1 ring-inset",
                            )}
                            style={{
                                // Deutlich kräftigerer Farbverlauf mit höherer Deckkraft
                                background: isAccessible
                                    ? `linear-gradient(to right, ${color}33, ${color}26, ${color}1A)`
                                    : undefined,
                                // leichte Kontur in Hauptfarbe
                                boxShadow: isAccessible
                                    ? `0 6px 20px ${color}33, inset 0 0 0 1px ${color}4D`
                                    : undefined
                            }}
                        >
                            <span
                                className="text-base font-semibold transition-colors duration-300"
                                style={{
                                    color: isAccessible && !inDevelopment ? "#0a0a0a" : undefined
                                }}
                            >
                                {inDevelopment ? "In Bearbeitung" : isAccessible ? "Jetzt starten" : "Nicht verfügbar"}
                            </span>
                            <div className="relative group/icon shrink-0">
                                <div
                                    className={cn(
                                        "absolute inset-[-6px] rounded-lg transition-all duration-300",
                                        "opacity-0 group-hover/start:opacity-100 scale-90 group-hover/start:scale-100"
                                    )}
                                    style={{
                                        background: `linear-gradient(to bottom right, ${color}40, ${color}33, transparent)`
                                    }}
                                />
                                <ArrowRight
                                    className="relative z-10 w-4 h-4 transition-all duration-300 group-hover/start:translate-x-0.5 group-hover/start:scale-110"
                                    style={{ color }}
                                />
                                </div> {/* end inner rotate container */}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <style>{`
                @keyframes scale {
                    0% {
                        transform: scale(2);
                        opacity: 0;
                        box-shadow: 0px 0px 50px ${color}50;
                    }
                    50% {
                        transform: translate(0px, -5px) scale(1);
                        opacity: 1;
                        box-shadow: 0px 8px 20px ${color}50;
                    }
                    100% {
                        transform: translate(0px, 5px) scale(0.1);
                        opacity: 0;
                        box-shadow: 0px 10px 20px ${color}00;
                    }
                }
            `}</style>
        </div>
    );
}