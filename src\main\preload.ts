import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import type { AppConfig } from '@/helpers/ipc/config/config-context';
import { CONFIG_CHANNELS } from '@/helpers/ipc/config/config-context';

type WorkflowId = 'bestand';

// Backend Base URL Ermittlung (für Renderer)
function resolveApiBaseUrl(): string {
  // Hole URL vom Main-Prozess, falls verfügbar
  // Optional: falls keine Antwort, auf Standard zurückfallen
  try {
    // Diese Invoke-Route kann später durch Main implementiert werden, hier zunächst defensiv
    // Wir verwenden localhost für Konsistenz mit CSP-Richtlinien
    // Hinweis: ipcRenderer.invoke ist asynchron; für sofortige Verfügbarkeit setzen wir unten einen Sync-Fallback
  } catch {
    // ignore
  }
  const fallback = 'http://localhost:3001';
  return fallback;
}

// Health-Check Helfer
async function checkBackendHealth(base: string): Promise<{ ok: boolean; url: string; error?: string }> {
  const url = `${base.replace(/\/+$/, '')}/api/health`;
  try {
    const res = await fetch(url, { method: 'GET' });
    if (!res.ok) {
      return { ok: false, url, error: `HTTP ${res.status}` };
    }
    return { ok: true, url };
  } catch (e: any) {
    return { ok: false, url, error: e?.message || String(e) };
  }
}

const apiBase = resolveApiBaseUrl();

// Exponiere Konfiguration und Health-Check in Renderer
const configAPI = {
  getApiBaseUrl: () => apiBase,
  // Setzt globale Variable für Services, die window.__API_BASE_URL__ lesen
  injectGlobal: () => {
    try {
      (window as any).__API_BASE_URL__ = `${apiBase.replace(/\/+$/, '')}/api/bereitschafts`;
    } catch {
      // ignore
    }
  },
  healthCheck: async () => {
    return await checkBackendHealth(apiBase);
  },
  // Neue API: vollständige Konfiguration vom Main-Prozess holen
  getConfig: async (): Promise<AppConfig> => {
    return await ipcRenderer.invoke(CONFIG_CHANNELS.GET_CONFIG);
  },
  // Listener für Konfigurationsänderungen
  onConfigUpdated: (callback: (config: AppConfig) => void) => {
    const listener = (_e: any, config: AppConfig) => callback(config);
    ipcRenderer.on(CONFIG_CHANNELS.CONFIG_UPDATED, listener);
    return () => ipcRenderer.removeListener(CONFIG_CHANNELS.CONFIG_UPDATED, listener);
  },
};

export const workflowsAPI = {
  list: async (): Promise<{ id: WorkflowId; name: string }[]> => {
    return await ipcRenderer.invoke('workflows:list');
  },
  start: async (id: WorkflowId, params?: Record<string, string>) => {
    return await ipcRenderer.invoke('workflows:start', { id, params });
  },
  status: async (id: WorkflowId) => {
    return await ipcRenderer.invoke('workflows:status', { id });
  },
  subscribeLogs: (id: WorkflowId, onLine: (line: string) => void) => {
    const logListener = (_e: any, payload: { id: WorkflowId; line: string }) => {
      if (payload.id === id) onLine(payload.line);
    };
    ipcRenderer.on('workflows:log', logListener);
    ipcRenderer.send('workflows:logs:start', { id });

    const finishedListener = (_e: any, payload: { id: WorkflowId; code: number; lastLog?: string }) => {
      if (payload.id === id) {
        // optional: handle finish event
      }
    };
    ipcRenderer.on('workflows:finished', finishedListener);

    return () => {
      ipcRenderer.send('workflows:logs:stop', { id });
      ipcRenderer.removeListener('workflows:log', logListener);
      ipcRenderer.removeListener('workflows:finished', finishedListener);
    };
  },
};

try {
  // Globale URL injizieren, bevor UI lädt
  (window as any).__API_BASE_URL__ = `${apiBase.replace(/\/+$/, '')}/api/bereitschafts`;
} catch {
  // ignore
}

contextBridge.exposeInMainWorld('api', {
  workflows: workflowsAPI,
  config: {
    getConfig: configAPI.getConfig,
    onConfigUpdated: configAPI.onConfigUpdated,
  },
});

// Expose configuration API under both names for backward compatibility
contextBridge.exposeInMainWorld('config', configAPI);
contextBridge.exposeInMainWorld('configAPI', configAPI);