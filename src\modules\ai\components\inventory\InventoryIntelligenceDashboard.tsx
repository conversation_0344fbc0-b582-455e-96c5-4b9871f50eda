import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChartErrorBoundary } from '@/components/ErrorBoundary';
import { ABCAnalysisChart } from './ABCAnalysisChart';
import { DemandForecastChart } from './DemandForecastChart';
import { InventoryRecommendationPanel } from './InventoryRecommendationPanel';
import { InventoryIntelligenceService } from '../../services/inventory/InventoryIntelligenceService';
import {
    ABCClassification,
    ABCAnalysisResult,
    DemandForecast,
    SeasonalityPattern,
    ReorderRecommendation,
    PurchaseRecommendation,
    StockAnomaly,
    InventoryItem
} from '../../services/inventory/types';
import {
    Brain,
    RefreshCw,
    Download,
    Settings,
    TrendingUp,
    Package,
    AlertTriangle,
    CheckCircle,
    BarChart3,
    Calendar
} from 'lucide-react';
import { toast } from 'sonner';

/**
 * Inventory Intelligence Dashboard
 * 
 * Main dashboard component for AI-powered inventory management.
 * Integrates ABC analysis, demand forecasting, and intelligent recommendations.
 */
export default function InventoryIntelligenceDashboard() {
    const { t } = useTranslation();

    // State management
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [selectedItem, setSelectedItem] = useState<string | null>(null);
    const [analysisTimeframe, setAnalysisTimeframe] = useState<number>(90); // Days
    const [forecastHorizon, setForecastHorizon] = useState<number>(30); // Days

    // Data state
    const [abcClassification, setAbcClassification] = useState<ABCClassification | null>(null);
    const [analysisResults, setAnalysisResults] = useState<ABCAnalysisResult[]>([]);
    const [demandForecast, setDemandForecast] = useState<DemandForecast | null>(null);
    const [seasonalityPattern, setSeasonalityPattern] = useState<SeasonalityPattern | null>(null);
    const [reorderRecommendations, setReorderRecommendations] = useState<ReorderRecommendation[]>([]);
    const [purchaseRecommendations, setPurchaseRecommendations] = useState<PurchaseRecommendation[]>([]);
    const [stockAnomalies, setStockAnomalies] = useState<StockAnomaly[]>([]);
    const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);

    // Initialize inventory intelligence service
    const [intelligenceService] = useState(() => new InventoryIntelligenceService({
        inventoryConfig: {
            abcAnalysis: {
                enabled: true,
                updateFrequency: 24,
                classAThreshold: 80,
                classBThreshold: 95,
                analysisWindow: analysisTimeframe
            },
            demandForecasting: {
                enabled: true,
                forecastHorizon: forecastHorizon,
                updateFrequency: 12,
                minHistoryDays: 30,
                models: ['exponential_smoothing', 'linear_regression']
            },
            anomalyDetection: {
                enabled: true,
                sensitivity: 0.8,
                checkFrequency: 6,
                alertThreshold: 0.7
            },
            reorderOptimization: {
                enabled: true,
                safetyStockDays: 7,
                leadTimeDays: 14,
                serviceLevel: 0.95
            }
        }
    }));

    // Initialize service on component mount
    useEffect(() => {
        const initService = async () => {
            try {
                await intelligenceService.initialize();
                await loadInitialData();
            } catch (error) {
                console.error('Failed to initialize inventory intelligence service:', error);
                toast.error('Fehler beim Initialisieren des Inventory Intelligence Service');
            }
        };

        initService();
    }, [intelligenceService]);

    /**
     * Load initial data
     */
    const loadInitialData = useCallback(async () => {
        setIsAnalyzing(true);

        try {
            // Load sample inventory items (in real app, this would come from repository)
            const sampleItems: InventoryItem[] = [
                {
                    id: 'NYY-J-3x1.5',
                    name: 'NYY-J 3x1,5 mm²',
                    category: 'Installationskabel',
                    currentStock: 2500,
                    unitPrice: 1.25,
                    supplier: 'Lapp Kabel',
                    location: 'Lager A',
                    lastUpdated: new Date()
                },
                {
                    id: 'NYM-J-5x2.5',
                    name: 'NYM-J 5x2,5 mm²',
                    category: 'Mantelleitung',
                    currentStock: 1800,
                    unitPrice: 3.45,
                    supplier: 'Lapp Kabel',
                    location: 'Lager B',
                    lastUpdated: new Date()
                },
                {
                    id: 'H07V-K-1x16',
                    name: 'H07V-K 1x16 mm²',
                    category: 'Einzelader',
                    currentStock: 850,
                    unitPrice: 2.15,
                    supplier: 'Lapp Kabel',
                    location: 'Lager A',
                    lastUpdated: new Date()
                }
            ];
            setInventoryItems(sampleItems);

            // Perform ABC analysis
            const classification = await intelligenceService.performABCAnalysis(sampleItems);
            setAbcClassification(classification);

            // Generate analysis results for visualization
            const results: ABCAnalysisResult[] = sampleItems.map((item, index) => ({
                itemId: item.id,
                classification: index === 0 ? 'A' : index === 1 ? 'B' : 'C',
                value: item.currentStock * item.unitPrice,
                percentage: index === 0 ? 45 : index === 1 ? 35 : 20,
                cumulativePercentage: index === 0 ? 45 : index === 1 ? 80 : 100,
                rank: index + 1,
                confidence: 0.85 + (Math.random() * 0.1)
            }));
            setAnalysisResults(results);

            // Generate sample recommendations
            await generateRecommendations(sampleItems);

            // Detect anomalies
            const anomalies = await intelligenceService.detectStockAnomalies();
            setStockAnomalies(anomalies);

            toast.success('Inventory Intelligence Analyse abgeschlossen');

        } catch (error) {
            console.error('Failed to load initial data:', error);
            toast.error('Fehler beim Laden der Daten: ' + (error instanceof Error ? error.message : 'Unbekannter Fehler'));
        } finally {
            setIsAnalyzing(false);
        }
    }, [intelligenceService]);

    /**
     * Generate recommendations for items
     */
    const generateRecommendations = useCallback(async (items: InventoryItem[]) => {
        try {
            const reorderRecs: ReorderRecommendation[] = [];
            const purchaseRecs: PurchaseRecommendation[] = [];

            for (const item of items) {
                // Generate reorder recommendation
                const reorderRec = await intelligenceService.calculateOptimalReorderPoint(item.id);
                reorderRecs.push(reorderRec);

                // Generate purchase recommendation if needed
                if (reorderRec.urgency === 'high' || reorderRec.urgency === 'critical') {
                    purchaseRecs.push({
                        itemId: item.id,
                        recommendedQuantity: reorderRec.recommendedOrderQuantity,
                        estimatedCost: reorderRec.recommendedOrderQuantity * item.unitPrice,
                        supplier: item.supplier,
                        priority: reorderRec.urgency === 'critical' ? 5 : 4,
                        reasoning: `Basierend auf Bedarfsprognose und aktuellem Bestand von ${reorderRec.currentStock} Stück`,
                        expectedDelivery: new Date(Date.now() + reorderRec.leadTime * 24 * 60 * 60 * 1000)
                    });
                }
            }

            setReorderRecommendations(reorderRecs);
            setPurchaseRecommendations(purchaseRecs);

        } catch (error) {
            console.error('Failed to generate recommendations:', error);
        }
    }, [intelligenceService]);

    /**
     * Handle item selection for detailed forecast
     */
    const handleItemSelect = useCallback(async (itemId: string) => {
        setSelectedItem(itemId);

        try {
            // Generate demand forecast for selected item
            const forecast = await intelligenceService.forecastDemand(itemId, forecastHorizon);
            setDemandForecast(forecast);

            // Detect seasonality
            const seasonality = await intelligenceService.detectSeasonality(itemId);
            setSeasonalityPattern(seasonality);

        } catch (error) {
            console.error('Failed to load item forecast:', error);
            toast.error('Fehler beim Laden der Bedarfsprognose');
        }
    }, [intelligenceService, forecastHorizon]);

    /**
     * Handle analysis refresh
     */
    const handleRefreshAnalysis = useCallback(async () => {
        await loadInitialData();
    }, [loadInitialData]);

    /**
     * Handle recommendation approval
     */
    const handleApproveRecommendation = useCallback(async (itemId: string, type: 'reorder' | 'purchase') => {
        try {
            // In real app, this would trigger actual ordering process
            toast.success(`${type === 'reorder' ? 'Nachbestellung' : 'Einkauf'} für ${itemId} genehmigt`);

            // Remove from recommendations
            if (type === 'reorder') {
                setReorderRecommendations(prev => prev.filter(r => r.itemId !== itemId));
            } else {
                setPurchaseRecommendations(prev => prev.filter(r => r.itemId !== itemId));
            }
        } catch (error) {
            console.error('Failed to approve recommendation:', error);
            toast.error('Fehler beim Genehmigen der Empfehlung');
        }
    }, []);

    /**
     * Handle anomaly dismissal
     */
    const handleDismissAnomaly = useCallback(async (anomalyId: string) => {
        try {
            setStockAnomalies(prev => prev.filter(a => a.id !== anomalyId));
            toast.success('Anomalie ignoriert');
        } catch (error) {
            console.error('Failed to dismiss anomaly:', error);
            toast.error('Fehler beim Ignorieren der Anomalie');
        }
    }, []);

    /**
     * Export analysis results
     */
    const exportAnalysis = useCallback(async (format: 'pdf' | 'excel') => {
        try {
            const exportData = {
                abcClassification,
                analysisResults,
                demandForecast,
                reorderRecommendations,
                purchaseRecommendations,
                stockAnomalies,
                timestamp: new Date().toISOString(),
                parameters: {
                    analysisTimeframe,
                    forecastHorizon
                }
            };

            // For now, download as JSON (would be replaced with actual PDF/Excel export)
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `inventory-intelligence-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            toast.success(`Analyse als ${format.toUpperCase()} exportiert`);
        } catch (error) {
            console.error('Export failed:', error);
            toast.error('Export fehlgeschlagen');
        }
    }, [abcClassification, analysisResults, demandForecast, reorderRecommendations, purchaseRecommendations, stockAnomalies, analysisTimeframe, forecastHorizon]);

    return (
        <div className="w-full bg-bg min-h-screen p-8">
            {/* Header */}
            <div className="mb-6 flex justify-between items-center">
                <div className="flex items-center">
                    <Brain className="h-8 w-8 mr-2 text-[#ff7a05]" />
                    <h1 className="text-3xl font-bold text-black">INVENTORY INTELLIGENCE</h1>
                </div>
                <div className="flex gap-2">
                    <Button
                        onClick={handleRefreshAnalysis}
                        disabled={isAnalyzing}
                        variant="outline"
                        className="flex items-center gap-2"
                    >
                        {isAnalyzing ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                            <RefreshCw className="h-4 w-4" />
                        )}
                        {isAnalyzing ? 'Analysiere...' : 'Aktualisieren'}
                    </Button>
                    <Button
                        onClick={() => exportAnalysis('pdf')}
                        variant="outline"
                        className="flex items-center gap-2"
                    >
                        <Download className="h-4 w-4" />
                        PDF Export
                    </Button>
                    <Button
                        onClick={() => exportAnalysis('excel')}
                        variant="outline"
                        className="flex items-center gap-2"
                    >
                        <Download className="h-4 w-4" />
                        Excel Export
                    </Button>
                </div>
            </div>

            {/* Configuration Panel */}
            <Card className="border-[#ff7a05] mb-6">
                <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                        <Settings className="h-5 w-5" />
                        <span>Analyse-Einstellungen</span>
                    </CardTitle>
                    <CardDescription>
                        Konfigurieren Sie die Parameter für die Inventory Intelligence Analyse
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <Label htmlFor="analysisTimeframe">Analysezeitraum (Tage)</Label>
                            <Select
                                value={analysisTimeframe.toString()}
                                onValueChange={(value) => setAnalysisTimeframe(parseInt(value))}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="30">30 Tage</SelectItem>
                                    <SelectItem value="60">60 Tage</SelectItem>
                                    <SelectItem value="90">90 Tage</SelectItem>
                                    <SelectItem value="180">180 Tage</SelectItem>
                                    <SelectItem value="365">365 Tage</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Label htmlFor="forecastHorizon">Prognosehorizont (Tage)</Label>
                            <Select
                                value={forecastHorizon.toString()}
                                onValueChange={(value) => setForecastHorizon(parseInt(value))}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="7">7 Tage</SelectItem>
                                    <SelectItem value="14">14 Tage</SelectItem>
                                    <SelectItem value="30">30 Tage</SelectItem>
                                    <SelectItem value="60">60 Tage</SelectItem>
                                    <SelectItem value="90">90 Tage</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Label htmlFor="selectedItem">Artikel für Detailanalyse</Label>
                            <Select
                                value={selectedItem || ''}
                                onValueChange={handleItemSelect}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Artikel auswählen..." />
                                </SelectTrigger>
                                <SelectContent>
                                    {inventoryItems.map(item => (
                                        <SelectItem key={item.id} value={item.id}>
                                            {item.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <Tabs defaultValue="abc-analysis" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="abc-analysis">ABC-Analyse</TabsTrigger>
                    <TabsTrigger value="demand-forecast">Bedarfsprognose</TabsTrigger>
                    <TabsTrigger value="recommendations">Empfehlungen</TabsTrigger>
                    <TabsTrigger value="overview">Übersicht</TabsTrigger>
                </TabsList>

                {/* ABC Analysis Tab */}
                <TabsContent value="abc-analysis" className="space-y-6">
                    {abcClassification ? (
                        <ChartErrorBoundary>
                            <ABCAnalysisChart
                                classification={abcClassification}
                                analysisResults={analysisResults}
                                onItemSelect={handleItemSelect}
                            />
                        </ChartErrorBoundary>
                    ) : (
                        <Card className="border-[#ff7a05]">
                            <CardContent className="flex flex-col items-center justify-center h-64">
                                <BarChart3 className="h-12 w-12 text-gray-400 mb-4" />
                                <p className="text-gray-600">
                                    {isAnalyzing ? 'ABC-Analyse wird durchgeführt...' : 'Führen Sie eine Analyse durch, um die ABC-Klassifizierung zu sehen'}
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </TabsContent>

                {/* Demand Forecast Tab */}
                <TabsContent value="demand-forecast" className="space-y-6">
                    {demandForecast && selectedItem ? (
                        <ChartErrorBoundary>
                            <DemandForecastChart
                                forecast={demandForecast}
                                seasonality={seasonalityPattern || undefined}
                                historicalData={[
                                    { date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), actualDemand: 45 },
                                    { date: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000), actualDemand: 52 },
                                    { date: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000), actualDemand: 38 },
                                    { date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), actualDemand: 61 },
                                    { date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), actualDemand: 47 },
                                    { date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), actualDemand: 55 }
                                ]}
                                onDateSelect={(date) => console.log('Selected date:', date)}
                            />
                        </ChartErrorBoundary>
                    ) : (
                        <Card className="border-[#ff7a05]">
                            <CardContent className="flex flex-col items-center justify-center h-64">
                                <Calendar className="h-12 w-12 text-gray-400 mb-4" />
                                <p className="text-gray-600">
                                    Wählen Sie einen Artikel aus, um die Bedarfsprognose zu sehen
                                </p>
                            </CardContent>
                        </Card>
                    )}
                </TabsContent>

                {/* Recommendations Tab */}
                <TabsContent value="recommendations" className="space-y-6">
                    <ChartErrorBoundary>
                        <InventoryRecommendationPanel
                            reorderRecommendations={reorderRecommendations}
                            purchaseRecommendations={purchaseRecommendations}
                            stockAnomalies={stockAnomalies}
                            onApproveRecommendation={handleApproveRecommendation}
                            onDismissAnomaly={handleDismissAnomaly}
                            onViewDetails={handleItemSelect}
                        />
                    </ChartErrorBoundary>
                </TabsContent>

                {/* Overview Tab */}
                <TabsContent value="overview" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Card className="border-[#ff7a05]">
                            <CardContent className="p-4">
                                <div className="flex items-center space-x-2">
                                    <Package className="h-5 w-5 text-blue-600" />
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Analysierte Artikel</p>
                                        <p className="text-2xl font-bold text-blue-600">
                                            {inventoryItems.length}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-[#ff7a05]">
                            <CardContent className="p-4">
                                <div className="flex items-center space-x-2">
                                    <TrendingUp className="h-5 w-5 text-green-600" />
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Klasse A Artikel</p>
                                        <p className="text-2xl font-bold text-green-600">
                                            {abcClassification?.classA.length || 0}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-[#ff7a05]">
                            <CardContent className="p-4">
                                <div className="flex items-center space-x-2">
                                    <AlertTriangle className="h-5 w-5 text-orange-600" />
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Aktive Anomalien</p>
                                        <p className="text-2xl font-bold text-orange-600">
                                            {stockAnomalies.length}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border-[#ff7a05]">
                            <CardContent className="p-4">
                                <div className="flex items-center space-x-2">
                                    <CheckCircle className="h-5 w-5 text-purple-600" />
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Empfehlungen</p>
                                        <p className="text-2xl font-bold text-purple-600">
                                            {reorderRecommendations.length + purchaseRecommendations.length}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Quick Actions */}
                    <Card className="border-[#ff7a05]">
                        <CardHeader>
                            <CardTitle>Schnellaktionen</CardTitle>
                            <CardDescription>
                                Häufig verwendete Funktionen für die Bestandsverwaltung
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Button
                                    variant="outline"
                                    className="h-20 flex flex-col items-center justify-center space-y-2"
                                    onClick={() => handleRefreshAnalysis()}
                                >
                                    <RefreshCw className="h-6 w-6" />
                                    <span>Analyse aktualisieren</span>
                                </Button>
                                <Button
                                    variant="outline"
                                    className="h-20 flex flex-col items-center justify-center space-y-2"
                                    onClick={() => exportAnalysis('excel')}
                                >
                                    <Download className="h-6 w-6" />
                                    <span>Bericht exportieren</span>
                                </Button>
                                <Button
                                    variant="outline"
                                    className="h-20 flex flex-col items-center justify-center space-y-2"
                                    onClick={() => toast.info('Konfiguration wird geöffnet...')}
                                >
                                    <Settings className="h-6 w-6" />
                                    <span>Einstellungen</span>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
}