import { Prisma } from '@prisma-sfm-dashboard/client';

/**
 * Typ für die Auslastungsdaten der Lager 200
 */
export type Auslastung200 = {
  id: number;
  aufnahmeDatum: string | null;
  auslastungA: string | null;
  auslastungB: string | null;
  auslastungC: string | null;
  createdAt: Date;
  updatedAt: Date;
};

// Manuell definierter Typ für die Auslastung200-Ergebnisse
export type Auslastung200WithRelations = {
  aufnahmeDatum: string | null;
  auslastungA: string | null;
  auslastungB: string | null;
  auslastungC: string | null;
};
