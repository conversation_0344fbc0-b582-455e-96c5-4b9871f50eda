/**
 * AI Error Display Component Tests
 * Tests for user-friendly error display functionality
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { AIErrorDisplay, AIErrorInline } from '../AIErrorDisplay';
import { AIServiceErrorCode, AIServiceErrorSeverity } from '../../../types/errors';
import * as errorMessages from '../../../utils/errorMessages';

// Mock the error messages utility
vi.mock('../../../utils/errorMessages');

describe('AIErrorDisplay', () => {
  beforeEach(() => {
    vi.mocked(errorMessages.formatErrorForUser).mockReturnValue('Formatted user error message');
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Error Parsing and Display', () => {
    it('should display AI service error correctly', () => {
      const aiError = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        severity: AIServiceErrorSeverity.HIGH,
        service: 'TestService',
        operation: 'testOperation',
        timestamp: new Date('2024-01-01T12:00:00Z'),
        recoverable: true,
        userMessage: 'User friendly message',
        technicalMessage: 'Technical error details'
      };

      render(<AIErrorDisplay error={aiError} context="Test Context" showDetails={true} />);

      expect(screen.getByText('Test Context - Fehler')).toBeInTheDocument();
      expect(screen.getByText('Hoch')).toBeInTheDocument(); // Severity badge
      expect(screen.getByText('Formatted user error message')).toBeInTheDocument();
      expect(screen.getByText('TestService')).toBeInTheDocument();
      expect(screen.getByText('testOperation')).toBeInTheDocument();
      expect(screen.getByText('API_REQUEST_FAILED')).toBeInTheDocument();
    });

    it('should display regular error correctly', () => {
      const regularError = new Error('Regular error message');

      render(<AIErrorDisplay error={regularError} showDetails={true} />);

      expect(screen.getByText('KI-Service Fehler')).toBeInTheDocument();
      expect(screen.getByText('Mittel')).toBeInTheDocument(); // Default severity
      expect(screen.getByText('Unknown')).toBeInTheDocument(); // Service
      expect(screen.getByText('Unknown')).toBeInTheDocument(); // Operation
    });

    it('should display string error correctly', () => {
      const stringError = 'Simple string error';

      render(<AIErrorDisplay error={stringError} />);

      expect(screen.getByText('KI-Service Fehler')).toBeInTheDocument();
      expect(screen.getByText('Formatted user error message')).toBeInTheDocument();
    });
  });

  describe('Severity Handling', () => {
    it('should display critical severity with correct styling', () => {
      const criticalError = {
        code: AIServiceErrorCode.SERVICE_UNAVAILABLE,
        severity: AIServiceErrorSeverity.CRITICAL,
        service: 'TestService',
        operation: 'test',
        timestamp: new Date(),
        recoverable: false,
        userMessage: 'Critical error',
        technicalMessage: 'Critical technical error'
      };

      render(<AIErrorDisplay error={criticalError} />);

      expect(screen.getByText('Kritisch')).toBeInTheDocument();
      // Should have red styling classes
      const card = screen.getByText('Kritisch').closest('.text-red-600');
      expect(card).toBeInTheDocument();
    });

    it('should display low severity with correct styling', () => {
      const lowError = {
        code: AIServiceErrorCode.CACHE_READ_FAILED,
        severity: AIServiceErrorSeverity.LOW,
        service: 'TestService',
        operation: 'test',
        timestamp: new Date(),
        recoverable: true,
        userMessage: 'Low severity error',
        technicalMessage: 'Low technical error'
      };

      render(<AIErrorDisplay error={lowError} />);

      expect(screen.getByText('Niedrig')).toBeInTheDocument();
      // Should have blue styling classes
      const card = screen.getByText('Niedrig').closest('.text-blue-600');
      expect(card).toBeInTheDocument();
    });
  });

  describe('Recoverable Error Indication', () => {
    it('should show recovery message for recoverable errors', () => {
      const recoverableError = {
        code: AIServiceErrorCode.API_TIMEOUT,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'TestService',
        operation: 'test',
        timestamp: new Date(),
        recoverable: true,
        userMessage: 'Recoverable error',
        technicalMessage: 'Technical error'
      };

      render(<AIErrorDisplay error={recoverableError} showDetails={true} />);

      expect(screen.getByText(/Dieser Fehler kann möglicherweise automatisch behoben werden/)).toBeInTheDocument();
    });

    it('should not show recovery message for non-recoverable errors', () => {
      const nonRecoverableError = {
        code: AIServiceErrorCode.API_KEY_INVALID,
        severity: AIServiceErrorSeverity.HIGH,
        service: 'TestService',
        operation: 'test',
        timestamp: new Date(),
        recoverable: false,
        userMessage: 'Non-recoverable error',
        technicalMessage: 'Technical error'
      };

      render(<AIErrorDisplay error={nonRecoverableError} showDetails={true} />);

      expect(screen.queryByText(/Dieser Fehler kann möglicherweise automatisch behoben werden/)).not.toBeInTheDocument();
    });
  });

  describe('Technical Details', () => {
    it('should show technical details when expanded', () => {
      const aiError = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'TestService',
        operation: 'testOperation',
        timestamp: new Date(),
        recoverable: true,
        userMessage: 'User message',
        technicalMessage: 'Technical error details',
        originalError: new Error('Original error message'),
        context: { additional: 'context data' }
      };

      render(<AIErrorDisplay error={aiError} showDetails={true} />);

      // Click to expand technical details
      const expandButton = screen.getByText('Technische Details anzeigen');
      fireEvent.click(expandButton);

      expect(screen.getByText('Technische Informationen:')).toBeInTheDocument();
      expect(screen.getByText(/Technical error details/)).toBeInTheDocument();
      expect(screen.getByText(/Original error message/)).toBeInTheDocument();
      expect(screen.getByText(/additional.*context data/)).toBeInTheDocument();
    });

    it('should hide technical details when collapsed', () => {
      const aiError = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'TestService',
        operation: 'testOperation',
        timestamp: new Date(),
        recoverable: true,
        userMessage: 'User message',
        technicalMessage: 'Technical error details'
      };

      render(<AIErrorDisplay error={aiError} showDetails={true} />);

      // Technical details should be hidden initially
      expect(screen.queryByText('Technische Informationen:')).not.toBeInTheDocument();

      // Expand and then collapse
      const expandButton = screen.getByText('Technische Details anzeigen');
      fireEvent.click(expandButton);

      const collapseButton = screen.getByText('Technische Details ausblenden');
      fireEvent.click(collapseButton);

      expect(screen.queryByText('Technische Informationen:')).not.toBeInTheDocument();
    });

    it('should show technical details in development mode', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const aiError = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'TestService',
        operation: 'testOperation',
        timestamp: new Date(),
        recoverable: true,
        userMessage: 'User message',
        technicalMessage: 'Technical error details'
      };

      render(<AIErrorDisplay error={aiError} showDetails={true} />);

      expect(screen.getByText('Technische Details anzeigen')).toBeInTheDocument();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Action Buttons', () => {
    it('should show retry button when enabled', () => {
      const mockOnRetry = vi.fn();
      const error = new Error('Test error');

      render(<AIErrorDisplay error={error} showRetry={true} onRetry={mockOnRetry} />);

      const retryButton = screen.getByText('Erneut versuchen');
      expect(retryButton).toBeInTheDocument();

      fireEvent.click(retryButton);
      expect(mockOnRetry).toHaveBeenCalled();
    });

    it('should show dismiss button when enabled', () => {
      const mockOnDismiss = vi.fn();
      const error = new Error('Test error');

      render(<AIErrorDisplay error={error} onDismiss={mockOnDismiss} />);

      const dismissButton = screen.getByRole('button');
      fireEvent.click(dismissButton);
      expect(mockOnDismiss).toHaveBeenCalled();
    });

    it('should not show retry button when disabled', () => {
      const error = new Error('Test error');

      render(<AIErrorDisplay error={error} showRetry={false} />);

      expect(screen.queryByText('Erneut versuchen')).not.toBeInTheDocument();
    });
  });

  describe('Context Display', () => {
    it('should show context in title when provided', () => {
      const error = new Error('Test error');

      render(<AIErrorDisplay error={error} context="Custom Context" />);

      expect(screen.getByText('Custom Context - Fehler')).toBeInTheDocument();
    });

    it('should show default title when no context provided', () => {
      const error = new Error('Test error');

      render(<AIErrorDisplay error={error} />);

      expect(screen.getByText('KI-Service Fehler')).toBeInTheDocument();
    });
  });

  describe('Custom Styling', () => {
    it('should apply custom className', () => {
      const error = new Error('Test error');

      render(<AIErrorDisplay error={error} className="custom-class" />);

      const card = screen.getByText('KI-Service Fehler').closest('.custom-class');
      expect(card).toBeInTheDocument();
    });
  });
});

describe('AIErrorInline', () => {
  beforeEach(() => {
    vi.mocked(errorMessages.formatErrorForUser).mockReturnValue('Inline error message');
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should display inline error correctly', () => {
    const error = new Error('Test error');

    render(<AIErrorInline error={error} context="Inline Context" />);

    expect(screen.getByText('Inline error message')).toBeInTheDocument();
    expect(errorMessages.formatErrorForUser).toHaveBeenCalledWith(error, 'Inline Context');
  });

  it('should apply custom className to inline error', () => {
    const error = new Error('Test error');

    render(<AIErrorInline error={error} className="inline-custom" />);

    const alert = screen.getByText('Inline error message').closest('.inline-custom');
    expect(alert).toBeInTheDocument();
  });

  it('should use destructive variant for inline errors', () => {
    const error = new Error('Test error');

    render(<AIErrorInline error={error} />);

    // Should have destructive alert styling
    const alert = screen.getByRole('alert');
    expect(alert).toHaveClass('border-red-200');
  });
});