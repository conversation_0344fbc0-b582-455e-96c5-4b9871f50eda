import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, AlertTriangle, TrendingUp, TrendingDown } from 'lucide-react';
import { DrumComparisonResult } from '../../types/cutting';

interface DrumComparisonDisplayProps {
  comparisonResult: DrumComparisonResult | null;
  className?: string;
}

/**
 * Komponente zur Anzeige der Vergleichsergebnisse zwischen KI- und manueller Trommelauswahl
 * Zeigt Qualitätsbewertung, Übereinstimmungen, Abweichungen und Empfehlungen an
 */
export const DrumComparisonDisplay: React.FC<DrumComparisonDisplayProps> = ({
  comparisonResult,
  className = ''
}) => {
  // Prüfe, ob comparisonResult vorhanden ist
  if (!comparisonResult) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Keine Vergleichsdaten verfügbar. Bitte führen Sie zuerst eine Analyse durch.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const {
    qualityScore,
    matches,
    differences,
    efficiency
  } = comparisonResult;

  // For now, recommendations can be derived from differences or passed separately
  const recommendations: string[] = [];

  // Bestimme die Farbe basierend auf dem Qualitätsscore
  const getQualityColor = (score: number): string => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Bestimme das Icon basierend auf dem Qualitätsscore
  const getQualityIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="h-5 w-5 text-green-600" />;
    if (score >= 60) return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    return <XCircle className="h-5 w-5 text-red-600" />;
  };

  // Bestimme Effizienz-Trend
  const getEfficiencyTrend = (difference: number) => {
    if (difference > 0) {
      return {
        icon: <TrendingUp className="h-4 w-4 text-green-600" />,
        text: `+${difference.toFixed(1)}% effizienter`,
        color: 'text-green-600'
      };
    } else if (difference < 0) {
      return {
        icon: <TrendingDown className="h-4 w-4 text-red-600" />,
        text: `${difference.toFixed(1)}% weniger effizient`,
        color: 'text-red-600'
      };
    } else {
      return {
        icon: null,
        text: 'Gleiche Effizienz',
        color: 'text-gray-600'
      };
    }
  };

  const efficiencyTrend = getEfficiencyTrend(efficiency?.difference || 0);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Qualitätsbewertung */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getQualityIcon(qualityScore)}
            Qualitätsbewertung
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Gesamtbewertung</span>
              <span className={`text-2xl font-bold ${getQualityColor(qualityScore)}`}>
                {qualityScore.toFixed(1)}%
              </span>
            </div>
            <Progress value={qualityScore} className="h-2" />

            {/* Effizienz-Vergleich */}
            <div className="flex items-center justify-between pt-2 border-t">
              <span className="text-sm font-medium">KI vs. Manuelle Auswahl</span>
              <div className={`flex items-center gap-1 ${efficiencyTrend.color}`}>
                {efficiencyTrend.icon}
                <span className="text-sm font-medium">{efficiencyTrend.text}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Übereinstimmungen */}
      {matches.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Übereinstimmungen ({matches.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {matches.map((match, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                  <div>
                    <span className="font-medium">{match.aiDrumId}</span>
                    <span className="text-sm text-gray-600 ml-2">↔ {match.manualChargeNumber}</span>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {match.matchType} ({Math.round(match.confidence * 100)}%)
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Abweichungen */}
      {differences.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              Abweichungen ({differences.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {differences.map((difference, index) => (
                <div key={index} className="p-3 bg-red-50 rounded-lg border-l-4 border-red-400">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-red-800">{difference.type}</span>
                        <Badge variant="destructive" className="text-xs">
                          {difference.impact}
                        </Badge>
                      </div>
                      <p className="text-sm text-red-700 mb-2">{difference.description}</p>

                      {/* KI vs. Manuelle Auswahl Details */}
                      {(difference.aiDrum || difference.manualDrum) && (
                        <div className="grid grid-cols-2 gap-4 text-xs">
                          <div>
                            <span className="font-medium text-gray-600">KI-Auswahl:</span>
                            <div className="text-gray-800">
                              {difference.aiDrum ? `${difference.aiDrum.id} - ${difference.aiDrum.material || 'N/A'}` : 'Keine'}
                            </div>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Manuelle Auswahl:</span>
                            <div className="text-gray-800">
                              {difference.manualDrum ? `${difference.manualDrum.chargeNumber} - ${difference.manualDrum.material}` : 'Keine'}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empfehlungen */}
      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-blue-600" />
              Empfehlungen
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {recommendations.map((recommendation, index) => (
                <Alert key={index} className="border-blue-200 bg-blue-50">
                  <AlertDescription className="text-blue-800">
                    {recommendation}
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Zusammenfassung */}
      <Card className="border-2 border-dashed border-gray-300">
        <CardHeader>
          <CardTitle className="text-lg">Zusammenfassung</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{matches.length}</div>
              <div className="text-sm text-gray-600">Übereinstimmungen</div>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{differences.length}</div>
              <div className="text-sm text-gray-600">Abweichungen</div>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className={`text-2xl font-bold ${getQualityColor(qualityScore)}`}>
                {qualityScore.toFixed(0)}%
              </div>
              <div className="text-sm text-gray-600">Qualität</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DrumComparisonDisplay;