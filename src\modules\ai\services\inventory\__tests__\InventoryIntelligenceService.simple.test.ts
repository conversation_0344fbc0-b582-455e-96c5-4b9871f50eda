/**
 * Inventory Intelligence Service Simple Tests
 * 
 * Basic unit tests for the main inventory intelligence service
 */

import { InventoryIntelligenceService } from '../InventoryIntelligenceService';

describe('InventoryIntelligenceService', () => {
  let service: InventoryIntelligenceService;

  beforeEach(() => {
    service = new InventoryIntelligenceService({
      inventoryConfig: {
        abcAnalysis: {
          enabled: true,
          updateFrequency: 24,
          classAThreshold: 80,
          classBThreshold: 95,
          analysisWindow: 90
        },
        demandForecasting: {
          enabled: true,
          forecastHorizon: 30,
          updateFrequency: 12,
          minHistoryDays: 14,
          models: ['simple_moving_average', 'exponential_smoothing']
        },
        anomalyDetection: {
          enabled: true,
          sensitivity: 0.7,
          checkFrequency: 6,
          alertThreshold: 0.8
        },
        reorderOptimization: {
          enabled: true,
          safetyStockDays: 7,
          leadTimeDays: 14,
          serviceLevel: 0.95
        }
      }
    });
  });

  describe('initialization', () => {
    it('should initialize successfully with default config', async () => {
      await service.initialize();
      
      const status = await service.healthCheck();
      expect(status.isHealthy).toBe(true);
    });

    it('should initialize with custom config', async () => {
      const customConfig = {
        inventoryConfig: {
          abcAnalysis: {
            enabled: false,
            updateFrequency: 48,
            classAThreshold: 70,
            classBThreshold: 90,
            analysisWindow: 60
          },
          demandForecasting: {
            enabled: true,
            forecastHorizon: 14,
            updateFrequency: 6,
            minHistoryDays: 7,
            models: ['simple_moving_average']
          },
          anomalyDetection: {
            enabled: true,
            sensitivity: 0.8,
            checkFrequency: 12,
            alertThreshold: 0.9
          },
          reorderOptimization: {
            enabled: false,
            safetyStockDays: 14,
            leadTimeDays: 21,
            serviceLevel: 0.98
          }
        }
      };

      await service.initialize(customConfig);
      
      const status = await service.healthCheck();
      expect(status.isHealthy).toBe(true);
    });
  });

  describe('performABCAnalysis', () => {
    it('should perform ABC analysis successfully', async () => {
      await service.initialize();
      
      const result = await service.performABCAnalysis();
      
      expect(result).toBeDefined();
      expect(result.classA).toBeDefined();
      expect(result.classB).toBeDefined();
      expect(result.classC).toBeDefined();
      expect(result.analysisDate).toBeInstanceOf(Date);
      expect(result.criteria).toBeDefined();
      
      // Total items should match input
      const totalClassified = result.classA.length + result.classB.length + result.classC.length;
      expect(totalClassified).toBeGreaterThanOrEqual(0);
    });
  });

  describe('forecastDemand', () => {
    it('should generate demand forecast for valid item', async () => {
      await service.initialize();
      
      const forecast = await service.forecastDemand('item-001', 14);
      
      expect(forecast).toBeDefined();
      expect(forecast.itemId).toBe('item-001');
      expect(forecast.predictions).toHaveLength(14);
      expect(forecast.confidence).toBeGreaterThanOrEqual(0);
      expect(forecast.confidence).toBeLessThanOrEqual(1);
      expect(forecast.trendDirection).toMatch(/increasing|decreasing|stable/);
      expect(forecast.forecastMethod).toBeTruthy();
      expect(forecast.generatedAt).toBeInstanceOf(Date);
      
      // Check prediction structure
      forecast.predictions.forEach(prediction => {
        expect(prediction.date).toBeInstanceOf(Date);
        expect(prediction.predictedDemand).toBeGreaterThanOrEqual(0);
        expect(prediction.confidenceInterval.lower).toBeGreaterThanOrEqual(0);
        expect(prediction.confidenceInterval.upper).toBeGreaterThan(prediction.confidenceInterval.lower);
      });
    });

    it('should use default horizon when not specified', async () => {
      await service.initialize();
      
      const forecast = await service.forecastDemand('item-001');
      
      expect(forecast.predictions).toHaveLength(30); // Default horizon
    });
  });

  describe('detectSeasonality', () => {
    it('should detect seasonality patterns', async () => {
      await service.initialize();
      
      const seasonality = await service.detectSeasonality('item-001');
      
      expect(seasonality).toBeDefined();
      expect(seasonality.itemId).toBe('item-001');
      expect(seasonality.hasSeasonality).toBeDefined();
      expect(seasonality.patterns).toBeDefined();
      expect(seasonality.strength).toBeGreaterThanOrEqual(0);
      expect(seasonality.strength).toBeLessThanOrEqual(1);
      expect(seasonality.detectedAt).toBeInstanceOf(Date);
    });
  });

  describe('calculateOptimalReorderPoint', () => {
    it('should calculate reorder recommendation', async () => {
      await service.initialize();
      
      const recommendation = await service.calculateOptimalReorderPoint('item-001');
      
      expect(recommendation).toBeDefined();
      expect(recommendation.itemId).toBe('item-001');
      expect(recommendation.currentStock).toBeGreaterThanOrEqual(0);
      expect(recommendation.reorderPoint).toBeGreaterThanOrEqual(0);
      expect(recommendation.recommendedOrderQuantity).toBeGreaterThan(0);
      expect(recommendation.urgency).toMatch(/low|medium|high|critical/);
      expect(recommendation.reasoning).toBeTruthy();
      expect(recommendation.confidence).toBeGreaterThanOrEqual(0);
      expect(recommendation.confidence).toBeLessThanOrEqual(1);
      expect(recommendation.leadTime).toBeGreaterThan(0);
      expect(recommendation.safetyStock).toBeGreaterThanOrEqual(0);
    });
  });

  describe('generatePurchaseRecommendations', () => {
    it('should generate purchase recommendations', async () => {
      await service.initialize();
      
      const recommendations = await service.generatePurchaseRecommendations();
      
      expect(Array.isArray(recommendations)).toBe(true);
      
      recommendations.forEach(rec => {
        expect(rec.itemId).toBeTruthy();
        expect(rec.recommendedQuantity).toBeGreaterThan(0);
        expect(rec.estimatedCost).toBeGreaterThan(0);
        expect(rec.priority).toBeGreaterThan(0);
        expect(rec.reasoning).toBeTruthy();
      });
    });

    it('should prioritize recommendations correctly', async () => {
      await service.initialize();
      
      const recommendations = await service.generatePurchaseRecommendations();
      
      // Should be sorted by priority
      for (let i = 1; i < recommendations.length; i++) {
        expect(recommendations[i].priority).toBeGreaterThanOrEqual(recommendations[i - 1].priority);
      }
    });
  });

  describe('detectStockAnomalies', () => {
    it('should detect stock anomalies', async () => {
      await service.initialize();
      
      const anomalies = await service.detectStockAnomalies();
      
      expect(Array.isArray(anomalies)).toBe(true);
      
      anomalies.forEach(anomaly => {
        expect(anomaly.id).toBeTruthy();
        expect(anomaly.itemId).toBeTruthy();
        expect(anomaly.anomalyType).toMatch(/sudden_spike|sudden_drop|unusual_pattern|stockout_risk|overstock/);
        expect(anomaly.severity).toMatch(/low|medium|high|critical/);
        expect(anomaly.detectedAt).toBeInstanceOf(Date);
        expect(anomaly.description).toBeTruthy();
        expect(anomaly.currentValue).toBeGreaterThanOrEqual(0);
        expect(anomaly.expectedValue).toBeGreaterThanOrEqual(0);
        expect(anomaly.deviation).toBeGreaterThanOrEqual(0);
        expect(Array.isArray(anomaly.recommendations)).toBe(true);
        expect(anomaly.confidence).toBeGreaterThanOrEqual(0);
        expect(anomaly.confidence).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('analyzeConsumptionPatterns', () => {
    it('should analyze consumption patterns', async () => {
      await service.initialize();
      
      const analysis = await service.analyzeConsumptionPatterns('item-001');
      
      expect(analysis).toBeDefined();
      expect(analysis.itemId).toBe('item-001');
      expect(analysis.averageDailyConsumption).toBeGreaterThanOrEqual(0);
      expect(analysis.consumptionTrend).toMatch(/increasing|decreasing|stable/);
      expect(analysis.volatility).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(analysis.patterns)).toBe(true);
      expect(Array.isArray(analysis.outliers)).toBe(true);
      expect(analysis.analysisDate).toBeInstanceOf(Date);
    });

    it('should identify consumption patterns correctly', async () => {
      await service.initialize();
      
      const analysis = await service.analyzeConsumptionPatterns('item-001');
      
      analysis.patterns.forEach(pattern => {
        expect(pattern.type).toMatch(/regular|irregular|seasonal|trending/);
        expect(pattern.description).toBeTruthy();
        expect(pattern.strength).toBeGreaterThanOrEqual(0);
        expect(pattern.strength).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('healthCheck', () => {
    it('should return comprehensive health status', async () => {
      await service.initialize();
      
      const status = await service.healthCheck();
      
      expect(status).toBeDefined();
      expect(status.isHealthy).toBeDefined();
      expect(status.lastAnalysisRun).toBeInstanceOf(Date);
      expect(status.totalItemsAnalyzed).toBeGreaterThanOrEqual(0);
      expect(status.classificationsUpToDate).toBeDefined();
      expect(status.forecastsUpToDate).toBeDefined();
      expect(status.anomaliesDetected).toBeGreaterThanOrEqual(0);
      expect(status.reorderRecommendations).toBeGreaterThanOrEqual(0);
      expect(status.performance).toBeDefined();
      expect(status.performance.analysisTime).toBeGreaterThanOrEqual(0);
      expect(status.performance.forecastAccuracy).toBeGreaterThanOrEqual(0);
      expect(status.performance.anomalyDetectionRate).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(status.errors)).toBe(true);
    });
  });

  describe('configuration management', () => {
    it('should respect configuration settings', async () => {
      const customService = new InventoryIntelligenceService({
        inventoryConfig: {
          abcAnalysis: {
            enabled: false,
            updateFrequency: 48,
            classAThreshold: 70,
            classBThreshold: 90,
            analysisWindow: 60
          },
          demandForecasting: {
            enabled: true,
            forecastHorizon: 14,
            updateFrequency: 6,
            minHistoryDays: 7,
            models: ['simple_moving_average']
          },
          anomalyDetection: {
            enabled: false,
            sensitivity: 0.8,
            checkFrequency: 12,
            alertThreshold: 0.9
          },
          reorderOptimization: {
            enabled: true,
            safetyStockDays: 14,
            leadTimeDays: 21,
            serviceLevel: 0.98
          }
        }
      });

      await customService.initialize();
      
      // Configuration should be applied
      const status = await customService.healthCheck();
      expect(status.isHealthy).toBe(true);
    });
  });
});