import React, { useEffect, useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Database, FileSpreadsheet, Play, Clock, CheckCircle, AlertTriangle, Settings, Calendar, Save } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { PowerSwitch } from '@/components/ui/power-switch';
import { workflowsClient } from '@/helpers/ipc/workflows.ipc';
import { workflowService } from '@/services/workflowService';
import { BestandConfigDialog } from './BestandConfigDialog';
import { SAPWorkflowProcess, WorkflowConfig } from '@/types/workflow';
import { BestandWorkflowCard } from './BestandWorkflowCard';
// Drawer-UI wie bei Servicegrad
import {
  Drawer,
  DrawerOverlay,
  DrawerContent,
  DrawerHeader,
  DrawerFooter,
  DrawerTitle,
  DrawerDescription,
} from '@/components/ui/drawer';

type WorkflowStatus = 'idle' | 'running' | 'success' | 'error';

interface BestandBentoCardProps {
  process: SAPWorkflowProcess;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  onExecute: (processId: string) => Promise<void>;
  isExecuting: boolean;
  initialTab?: 'overview' | 'settings';
}
type Schedule = {
  enabled: boolean;
  frequency: 'hourly' | 'daily' | 'weekly';
  time?: string;
  dayOfWeek?: number;
  interval?: number;
};
type BestandConfig = {
  id: 'bestand';
  name: string;
  description?: string;
  // generisch
  databasePath?: string;
  // Pfade für drei Teilprozesse (entspricht Python-ENV Variablen)
  exportDir240?: string;
  exportDir200?: string;
  exportDirRest?: string;
  // LGTYP Low/High Bereiche
  lgnum?: string;
  lgt240?: string;
  lgt200?: string;
  lgtRestLow?: string;
  lgtRestHigh?: string;
  // SAP Basis
  sapExecutablePath?: string;
  sapSystemId?: string;
  sapClient?: string;
  sapLanguage?: string;
  // E-Mail Empfänger etc.
  emailRecipients?: string[];
  schedule: Schedule;
  isActive?: boolean;
  lastModified?: Date;
};
const defaultBestandConfig: BestandConfig = {
  id: 'bestand',
  name: 'Bestand Workflow',
  description: 'Lagerspiegel Export aus verschiedenen Lägern mit Import in die Datenbank',
  databasePath: '',
  exportDir240: '',
  exportDir200: '',
  exportDirRest: '',
  lgnum: '512',
  lgt240: '240',
  lgt200: '200',
  lgtRestLow: '241',
  lgtRestHigh: '999',
  sapExecutablePath: 'C:\\\\Program Files (x86)\\\\SAP\\\\FrontEnd\\\\SapGui\\\\sapshcut.exe',
  sapSystemId: 'PS4',
  sapClient: '009',
  sapLanguage: 'DE',
  emailRecipients: [],
  schedule: {
    enabled: false,
    frequency: 'daily',
    time: '08:00',
    dayOfWeek: 1,
    interval: 1
  },
  isActive: true,
  lastModified: new Date()
};

export function BestandBentoCard({ className, size = 'large', initialTab = 'overview' }: BestandBentoCardProps) {
  const [status, setStatus] = useState<WorkflowStatus>('idle');
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionProgress, setExecutionProgress] = useState(0);
  const [showSettings, setShowSettings] = useState(initialTab === 'settings');

  // Settings UI State (analog Servicegrad)
  const [config, setConfig] = useState<BestandConfig>(defaultBestandConfig);
  const [showConfigDialog, setShowConfigDialog] = useState(false); // steuert Drawer open/close
  const [tempConfig, setTempConfig] = useState<BestandConfig>(defaultBestandConfig);

  // Status Poll (leichtgewichtig)
  useEffect(() => {
    loadConfig();
    let mounted = true;
    const poll = async () => {
      try {
        const s = await workflowsClient.status('bestand');
        if (!mounted) return;
        const mapped: WorkflowStatus =
          s.status === 'running' ? 'running' : s.status === 'success' ? 'success' : s.status === 'error' ? 'error' : 'idle';
        setStatus(mapped);
      } catch {
        // ignore
      }
    };
    poll();
    const i = setInterval(poll, 2000);
    return () => {
      mounted = false;
      clearInterval(i);
    };
  }, []);

  const loadConfig = async () => {
    try {
      const loaded = await workflowService.getWorkflowConfig('bestand');
      if (loaded) {
        // bestehende Keys mit Defaults mergen
        const merged = { ...defaultBestandConfig, ...loaded };
        setConfig(merged);
        setTempConfig(merged);
      }
    } catch (err) {
      console.error('Fehler beim Laden der Bestand-Config:', err);
    }
  };

  const saveConfig = async () => {
    try {
      // Bestand-Workflow hat die feste ID 'bestand'
      await workflowService.updateWorkflowConfig('bestand', tempConfig);
      setConfig(tempConfig);
      setShowSettings(false);
      console.log('Workflow-Konfiguration gespeichert:', tempConfig);
    } catch (error) {
      console.error('Fehler beim Speichern der Workflow-Konfiguration:', error);
    }
  };

  const handleConfigDialogClose = useCallback(() => {
    setShowConfigDialog(false);
    // Reload config after drawer closes to get updated values
    loadConfig();
  }, []);

  const handlePowerSwitchChange = useCallback((checked: boolean) => {
    const newConfig = { ...config, isActive: checked };
    setConfig(newConfig);
    // Asynchron speichern ohne await
    workflowService.updateWorkflowConfig('bestand', newConfig).catch(error => {
      console.error('Fehler beim Speichern der Workflow-Aktivierung:', error);
      // Bei Fehler zurücksetzen
      setConfig(prev => ({ ...prev, isActive: !checked }));
    });
  }, [config]);



  const handleStart = async () => {
    // Prüfen ob der Workflow aktiv ist
    if (!config.isActive) {
      console.warn('Workflow kann nicht gestartet werden - PowerSwitch ist ausgeschaltet');
      return;
    }

    try {
      setIsExecuting(true);
      setExecutionProgress(0);

      const res = await workflowsClient.start('bestand');
      if (!res.ok) {
        setStatus('error');
        throw new Error(res.message || 'Start fehlgeschlagen');
      } else {
        setStatus('running');
      }

      // Reset execution state after a short delay
      setTimeout(() => {
        setIsExecuting(false);
      }, 2000);
    } catch (e) {
      setIsExecuting(false);
      setStatus('error');
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'running':
        return <Clock className="h-5 w-5 text-purple-600 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Database className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'running':
        return 'Läuft';
      case 'success':
        return 'Abgeschlossen';
      case 'error':
        return 'Fehler';
      default:
        return 'Bereit';
    }
  };

  const variants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: 'spring' as const, damping: 25 },
    },
  };

  return (
    <motion.div
      variants={variants}
      className={cn(
        'group border-purple-300/60 bg-white hover:border-purple-400/80 relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border-2 px-6 pt-6 pb-6 shadow-lg transition-all duration-500',
        className,
      )}
    >
      {/* Background Pattern */}
      <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#a855f760_1px,transparent_1px),linear-gradient(to_bottom,#a855f760_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>

      {/* Large Background Icon */}
      <div className="text-purple-200/50 group-hover:text-purple-300/70 absolute right-1 bottom-3 scale-[4] transition-all duration-700 group-hover:scale-[4.2]">
        <Database className="h-8 w-8" />
      </div>

      <div className="relative z-10 flex h-full flex-col justify-between">
        {/* Header */}
        <div>
          <div className="flex items-start justify-between mb-2">
            <div className="flex flex-col items-center gap-1">
              <div className="bg-purple-100/70 text-purple-600 shadow-purple-200/50 group-hover:bg-purple-200/80 group-hover:shadow-purple-300/60 flex h-10 w-10 items-center justify-center rounded-full shadow-lg transition-all duration-500">
                {getStatusIcon()}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {config.schedule.enabled && (
                <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                  <Calendar className="h-3 w-3 mr-1" />
                  Geplant
                </Badge>
              )}
              <Badge
                className={cn(
                  'text-xs font-medium shadow-sm border-0',
                  status === 'running' && 'bg-purple-500 text-white',
                  status === 'success' && 'bg-green-500 text-white',
                  status === 'error' && 'bg-red-500 text-white',
                  status === 'idle' && 'bg-gray-500 text-white',
                )}
              >
                {getStatusText()}
              </Badge>
              {/* Settings öffnen als Popup */}
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 p-0 border-gray-300 text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 shadow-sm"
                title="Einstellungen"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowConfigDialog(true);
                }}
              >
                <Settings className="h-3 w-3" />
              </Button>
            </div>
          </div>

          <h3 className="mb-1 text-lg font-semibold tracking-tight">Bestand Workflow</h3>
          <p className="text-muted-foreground text-sm mb-2">Lagerspiegel Export aus verschiedenen Lägern mit Import in die Datenbank</p>

          {/* Hinweis: Kein Popup mehr – Einstellungen werden im Drawer angezeigt (analog Servicegrad) */}

          {/* Steps */}
          {size === 'large' && (
            <div className="space-y-1 mb-3 text-xs">
              <div className="flex items-center gap-2 text-gray-600">
                <Database className="h-3 w-3 text-purple-500" />
                <span>SAP LX03 - Lagertyp 240</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <Database className="h-3 w-3 text-purple-500" />
                <span>SAP LX03 - Lagertyp 200</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <Database className="h-3 w-3 text-purple-500" />
                <span>SAP LX03 - Lagertyp 241-999</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <FileSpreadsheet className="h-3 w-3 text-green-500" />
                <span>Datenbank-Speicherung</span>
              </div>
            </div>
          )}

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-3 mb-3 text-xs">
            <div>
              <div className="text-muted-foreground">Ziel-Tabelle</div>
              <div className="font-mono text-purple-600">Lagerspiegel</div>
            </div>
            {config.schedule.enabled && (
              <div className="col-span-2">
                <div className="text-muted-foreground">Zeitplan</div>
                <div className="text-green-600 text-xs">
                  {config.schedule.frequency === 'hourly' && `Alle ${config.schedule.interval}h`}
                  {config.schedule.frequency === 'daily' && `Täglich um ${config.schedule.time}`}
                  {config.schedule.frequency === 'weekly' && `Wöchentlich ${['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'][config.schedule.dayOfWeek || 1]} um ${config.schedule.time}`}
                </div>
              </div>
            )}
          </div>


        </div>

        {/* Action Button and Power Switch */}
        <div className="relative">
          <Button
            onClick={handleStart}
            disabled={status === 'running' || isExecuting || !config.isActive}
            className={cn(
              "w-fit px-4 text-white text-sm shadow-md",
              config.isActive
                ? "bg-purple-600 hover:bg-purple-700"
                : "bg-gray-400 cursor-not-allowed"
            )}
            size="sm"
          >
            {status === 'running' || isExecuting ? (
              <>
                <Clock className="h-3 w-3 mr-2 animate-spin" />
                Bestand-Workflow läuft...
              </>
            ) : !config.isActive ? (
              <>
                <Play className="h-3 w-3 mr-2" />
                Workflow deaktiviert
              </>
            ) : (
              <>
                <Play className="h-3 w-3 mr-2" />
                Prozess starten
              </>
            )}
          </Button>
          <div className="absolute -bottom-3 -right-3">
            <PowerSwitch
              id="workflow-active-footer2"
              checked={config.isActive}
              onChange={handlePowerSwitchChange}
              className="scale-75"
            />
          </div>

        </div>
      </div>

      {/* Bottom Gradient */}
      <div className="from-purple-400 to-purple-300 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
      
      {/* Slide-Out Drawer (ersetzt den bisherigen Dialog) */}
      <Drawer
        open={showConfigDialog}
        onOpenChange={(open) => {
          if (!open) {
            handleConfigDialogClose();
          } else {
            setShowConfigDialog(true);
          }
        }}
        side="left"
      >
        <DrawerOverlay />
        <DrawerContent className="w-[520px] max-w-[95vw]">
          <DrawerHeader>
            <DrawerTitle>Workflow-Einstellungen</DrawerTitle>
            <DrawerDescription>
              Konfiguration und Planung für Bestands Automatisierung
            </DrawerDescription>
          </DrawerHeader>

          <div className="p-4 overflow-auto">
            {/* Reiner Drawer-Content analog Servicegrad */}
            <BestandWorkflowCard
              onExecute={async () => {
                try {
                  const res = await workflowsClient.start('bestand');
                  if (!res?.ok) {
                    console.error('Bestand Start fehlgeschlagen:', res?.message);
                  }
                } catch (e) {
                  console.error('Fehler beim Starten des Bestand-Workflows:', e);
                }
              }}
              isExecuting={isExecuting}
              initialTab="settings"
            />
          </div>

          <DrawerFooter>
            <Button variant="accept" size="sm" onClick={saveConfig}>
              <Save className="h-4 w-4 mr-1" />
              Speichern
            </Button>
            <Button variant="outline" onClick={() => handleConfigDialogClose()}>
              Schließen
            </Button>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </motion.div>
  );
}