/**
 * AI Error Handler Tests
 * Comprehensive tests for error handling, recovery, and fallback mechanisms
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { AIErrorHandler } from '../AIErrorHandler';
import { AIOperationLogger } from '../AIOperationLogger';
import { AIServiceErrorCode, AIServiceErrorSeverity } from '../../types/errors';

// Mock the logger
vi.mock('../AIOperationLogger');

describe('AIErrorHandler', () => {
  let errorHandler: AIErrorHandler;
  let mockLogger: any;

  beforeEach(() => {
    // Reset singleton instance
    (AIErrorHandler as any).instance = undefined;
    errorHandler = AIErrorHandler.getInstance();
    
    // Mock logger
    mockLogger = {
      logError: vi.fn(),
      logSuccessfulRecovery: vi.fn(),
      logFailedRecovery: vi.fn(),
      logFailedRecoveryAttempt: vi.fn(),
      logSuccessfulFallback: vi.fn(),
      logFailedFallbackAttempt: vi.fn()
    };
    
    vi.mocked(AIOperationLogger.getInstance).mockReturnValue(mockLogger);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Error Creation', () => {
    it('should create structured AI error with correct properties', () => {
      const originalError = new Error('Test error');
      const context = { testData: 'value' };

      const aiError = errorHandler.createError(
        AIServiceErrorCode.API_REQUEST_FAILED,
        'TestService',
        'testOperation',
        originalError,
        context
      );

      expect(aiError.code).toBe(AIServiceErrorCode.API_REQUEST_FAILED);
      expect(aiError.service).toBe('TestService');
      expect(aiError.operation).toBe('testOperation');
      expect(aiError.originalError).toBe(originalError);
      expect(aiError.context).toBe(context);
      expect(aiError.severity).toBe(AIServiceErrorSeverity.MEDIUM);
      expect(aiError.recoverable).toBe(true);
      expect(aiError.userMessage).toBeDefined();
      expect(aiError.technicalMessage).toBeDefined();
      expect(aiError.timestamp).toBeInstanceOf(Date);
    });

    it('should determine correct severity levels', () => {
      const criticalError = errorHandler.createError(
        AIServiceErrorCode.SERVICE_UNAVAILABLE,
        'TestService',
        'test'
      );
      expect(criticalError.severity).toBe(AIServiceErrorSeverity.CRITICAL);

      const highError = errorHandler.createError(
        AIServiceErrorCode.API_KEY_INVALID,
        'TestService',
        'test'
      );
      expect(highError.severity).toBe(AIServiceErrorSeverity.HIGH);

      const mediumError = errorHandler.createError(
        AIServiceErrorCode.API_TIMEOUT,
        'TestService',
        'test'
      );
      expect(mediumError.severity).toBe(AIServiceErrorSeverity.MEDIUM);

      const lowError = errorHandler.createError(
        AIServiceErrorCode.CACHE_READ_FAILED,
        'TestService',
        'test'
      );
      expect(lowError.severity).toBe(AIServiceErrorSeverity.LOW);
    });

    it('should determine recoverability correctly', () => {
      const recoverableError = errorHandler.createError(
        AIServiceErrorCode.API_TIMEOUT,
        'TestService',
        'test'
      );
      expect(recoverableError.recoverable).toBe(true);

      const nonRecoverableError = errorHandler.createError(
        AIServiceErrorCode.API_KEY_INVALID,
        'TestService',
        'test'
      );
      expect(nonRecoverableError.recoverable).toBe(false);
    });

    it('should log error when logging is enabled', () => {
      errorHandler.configure({ enableLogging: true });
      
      const aiError = errorHandler.createError(
        AIServiceErrorCode.API_REQUEST_FAILED,
        'TestService',
        'test'
      );

      expect(mockLogger.logError).toHaveBeenCalledWith(aiError);
    });

    it('should not log error when logging is disabled', () => {
      errorHandler.configure({ enableLogging: false });
      
      errorHandler.createError(
        AIServiceErrorCode.API_REQUEST_FAILED,
        'TestService',
        'test'
      );

      expect(mockLogger.logError).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should return null when no recovery strategies available', async () => {
      const error = errorHandler.createError(
        AIServiceErrorCode.API_KEY_INVALID, // Non-recoverable
        'TestService',
        'test'
      );

      const result = await errorHandler.handleError(error);
      expect(result).toBeNull();
    });

    it('should attempt recovery for recoverable errors', async () => {
      const error = errorHandler.createError(
        AIServiceErrorCode.API_TIMEOUT,
        'TestService',
        'test'
      );

      // Mock recovery strategy
      const mockRecovery = vi.fn().mockResolvedValue('recovered result');
      errorHandler.registerRecoveryStrategy(AIServiceErrorCode.API_TIMEOUT, {
        canRecover: () => true,
        recover: mockRecovery,
        maxRetries: 2,
        retryDelay: 100
      });

      const result = await errorHandler.handleError(error, 'input data');
      
      expect(result).toBe('recovered result');
      expect(mockRecovery).toHaveBeenCalledWith(error, 'input data');
      expect(mockLogger.logSuccessfulRecovery).toHaveBeenCalled();
    });

    it('should retry recovery on failure up to max retries', async () => {
      const error = errorHandler.createError(
        AIServiceErrorCode.API_TIMEOUT,
        'TestService',
        'test'
      );

      let attemptCount = 0;
      const mockRecovery = vi.fn().mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error('Recovery failed');
        }
        return 'recovered result';
      });

      errorHandler.registerRecoveryStrategy(AIServiceErrorCode.API_TIMEOUT, {
        canRecover: () => true,
        recover: mockRecovery,
        maxRetries: 3,
        retryDelay: 10
      });

      const result = await errorHandler.handleError(error);
      
      expect(result).toBe('recovered result');
      expect(mockRecovery).toHaveBeenCalledTimes(3);
      expect(mockLogger.logSuccessfulRecovery).toHaveBeenCalledWith(error, 3);
    });

    it('should attempt fallback when recovery fails', async () => {
      const error = errorHandler.createError(
        AIServiceErrorCode.OPTIMIZATION_FAILED,
        'TestService',
        'test'
      );

      // Mock fallback strategy
      const mockFallback = vi.fn().mockResolvedValue('fallback result');
      errorHandler.registerFallbackStrategy('TestService', {
        name: 'TestFallback',
        canHandle: () => true,
        execute: mockFallback,
        priority: 1
      });

      const result = await errorHandler.handleError(error, 'input data', 'TestService');
      
      expect(result).toBe('fallback result');
      expect(mockFallback).toHaveBeenCalledWith('input data', error);
      expect(mockLogger.logSuccessfulFallback).toHaveBeenCalledWith(error, 'TestFallback');
    });

    it('should try multiple fallback strategies in priority order', async () => {
      const error = errorHandler.createError(
        AIServiceErrorCode.OPTIMIZATION_FAILED,
        'TestService',
        'test'
      );

      const lowPriorityFallback = vi.fn().mockRejectedValue(new Error('Low priority failed'));
      const highPriorityFallback = vi.fn().mockResolvedValue('high priority result');

      errorHandler.registerFallbackStrategy('TestService', {
        name: 'LowPriority',
        canHandle: () => true,
        execute: lowPriorityFallback,
        priority: 1
      });

      errorHandler.registerFallbackStrategy('TestService', {
        name: 'HighPriority',
        canHandle: () => true,
        execute: highPriorityFallback,
        priority: 2
      });

      const result = await errorHandler.handleError(error, 'input data', 'TestService');
      
      expect(result).toBe('high priority result');
      expect(highPriorityFallback).toHaveBeenCalled();
      expect(lowPriorityFallback).not.toHaveBeenCalled();
    });

    it('should handle errors during error handling gracefully', async () => {
      const error = errorHandler.createError(
        AIServiceErrorCode.API_TIMEOUT,
        'TestService',
        'test'
      );

      // Mock recovery that throws
      const mockRecovery = vi.fn().mockRejectedValue(new Error('Recovery error'));
      errorHandler.registerRecoveryStrategy(AIServiceErrorCode.API_TIMEOUT, {
        canRecover: () => true,
        recover: mockRecovery,
        maxRetries: 1,
        retryDelay: 10
      });

      const result = await errorHandler.handleError(error);
      
      expect(result).toBeNull();
      expect(mockLogger.logFailedRecovery).toHaveBeenCalled();
    });
  });

  describe('Configuration', () => {
    it('should update configuration correctly', () => {
      const newConfig = {
        enableLogging: false,
        enableRetries: false,
        maxRetries: 5,
        retryDelay: 2000
      };

      errorHandler.configure(newConfig);
      
      // Test that configuration is applied (indirectly through behavior)
      const error = errorHandler.createError(
        AIServiceErrorCode.API_REQUEST_FAILED,
        'TestService',
        'test'
      );

      expect(mockLogger.logError).not.toHaveBeenCalled();
    });
  });

  describe('Error Summary', () => {
    it('should return error summary from logger', () => {
      const mockSummary = {
        totalErrors: 5,
        errorsByService: { TestService: 3, OtherService: 2 },
        errorsByCode: { [AIServiceErrorCode.API_REQUEST_FAILED]: 5 },
        errorsBySeverity: { [AIServiceErrorSeverity.MEDIUM]: 5 }
      };

      mockLogger.getErrorSummary = vi.fn().mockReturnValue(mockSummary);

      const summary = errorHandler.getErrorSummary();
      expect(summary).toBe(mockSummary);
      expect(mockLogger.getErrorSummary).toHaveBeenCalled();
    });
  });

  describe('Utility Methods', () => {
    it('should format user errors correctly', () => {
      const error = errorHandler.createError(
        AIServiceErrorCode.API_REQUEST_FAILED,
        'TestService',
        'test'
      );

      const userError = errorHandler.formatUserError(error, 'Test Context');
      expect(userError).toContain('Test Context');
      expect(userError).toContain('Die KI-Anfrage konnte nicht verarbeitet werden');
    });

    it('should format technical errors correctly', () => {
      const error = errorHandler.createError(
        AIServiceErrorCode.API_REQUEST_FAILED,
        'TestService',
        'test'
      );

      const technicalError = errorHandler.formatTechnicalError(error);
      expect(technicalError).toContain('OpenRouter API request failed');
    });
  });

  describe('Singleton Pattern', () => {
    it('should return same instance', () => {
      const instance1 = AIErrorHandler.getInstance();
      const instance2 = AIErrorHandler.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });
});