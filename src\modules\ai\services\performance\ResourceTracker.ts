import { IService } from '@/services/base.service';

export interface ResourceMetrics {
  timestamp: number;
  memoryUsage: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  };
  cpuUsage: {
    user: number;
    system: number;
    percent: number;
  };
  networkUsage: {
    bytesReceived: number;
    bytesSent: number;
    requestsPerSecond: number;
  };
  diskUsage: {
    readBytes: number;
    writeBytes: number;
    readOperations: number;
    writeOperations: number;
  };
  aiSpecificMetrics: {
    activeEmbeddings: number;
    vectorSearches: number;
    cacheSize: number;
    queueLength: number;
  };
}

export interface ResourceThresholds {
  memoryWarning: number;
  memoryCritical: number;
  cpuWarning: number;
  cpuCritical: number;
  diskSpaceWarning: number;
  diskSpaceCritical: number;
  networkLatencyWarning: number;
  networkLatencyCritical: number;
}

export interface OptimizationRecommendation {
  id: string;
  type: 'memory' | 'cpu' | 'disk' | 'network' | 'cache' | 'algorithm';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  implementation: string;
  estimatedSavings: {
    memory?: number;
    cpu?: number;
    time?: number;
  };
  complexity: 'easy' | 'medium' | 'hard';
}

/**
 * Advanced resource tracking and optimization recommendations
 */
export class ResourceTracker implements IService {
  readonly serviceName = 'ResourceTracker';

  private metrics: ResourceMetrics[] = [];
  private maxHistorySize = 1000;
  private collectionInterval = 30000; // 30 seconds
  private intervalId?: NodeJS.Timeout;

  private thresholds: ResourceThresholds = {
    memoryWarning: 100 * 1024 * 1024, // 100MB
    memoryCritical: 500 * 1024 * 1024, // 500MB
    cpuWarning: 70, // 70%
    cpuCritical: 90, // 90%
    diskSpaceWarning: 1024 * 1024 * 1024, // 1GB
    diskSpaceCritical: 100 * 1024 * 1024, // 100MB
    networkLatencyWarning: 1000, // 1 second
    networkLatencyCritical: 5000 // 5 seconds
  };

  private lastCpuUsage = { user: 0, system: 0 };
  private lastNetworkUsage = { received: 0, sent: 0 };
  private lastDiskUsage = { read: 0, write: 0, readOps: 0, writeOps: 0 };

  constructor() {
    this.startCollection();
  }

  /**
   * Start resource collection
   */
  startCollection(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    this.intervalId = setInterval(() => {
      this.collectMetrics();
    }, this.collectionInterval);

    // Collect initial metrics
    this.collectMetrics();
  }

  /**
   * Stop resource collection
   */
  stopCollection(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }

  /**
   * Get current resource metrics
   */
  getCurrentMetrics(): ResourceMetrics {
    return this.collectMetrics();
  }

  /**
   * Get resource history
   */
  getHistory(timeWindow?: number): ResourceMetrics[] {
    const cutoff = timeWindow ? Date.now() - timeWindow : 0;
    return this.metrics.filter(m => m.timestamp > cutoff);
  }

  /**
   * Get resource usage trends
   */
  getTrends(timeWindow: number = 3600000): {
    memory: { trend: 'increasing' | 'decreasing' | 'stable'; rate: number };
    cpu: { trend: 'increasing' | 'decreasing' | 'stable'; rate: number };
    network: { trend: 'increasing' | 'decreasing' | 'stable'; rate: number };
  } {
    const history = this.getHistory(timeWindow);
    if (history.length < 2) {
      return {
        memory: { trend: 'stable', rate: 0 },
        cpu: { trend: 'stable', rate: 0 },
        network: { trend: 'stable', rate: 0 }
      };
    }

    const first = history[0];
    const last = history[history.length - 1];
    const timeDiff = last.timestamp - first.timestamp;

    const memoryRate = (last.memoryUsage.heapUsed - first.memoryUsage.heapUsed) / timeDiff;
    const cpuRate = (last.cpuUsage.percent - first.cpuUsage.percent) / timeDiff;
    const networkRate = (last.networkUsage.requestsPerSecond - first.networkUsage.requestsPerSecond) / timeDiff;

    return {
      memory: {
        trend: memoryRate > 0.1 ? 'increasing' : memoryRate < -0.1 ? 'decreasing' : 'stable',
        rate: memoryRate
      },
      cpu: {
        trend: cpuRate > 0.01 ? 'increasing' : cpuRate < -0.01 ? 'decreasing' : 'stable',
        rate: cpuRate
      },
      network: {
        trend: networkRate > 0.01 ? 'increasing' : networkRate < -0.01 ? 'decreasing' : 'stable',
        rate: networkRate
      }
    };
  }

  /**
   * Get optimization recommendations
   */
  getOptimizationRecommendations(): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = [];
    const current = this.getCurrentMetrics();
    const trends = this.getTrends();

    // Memory optimization recommendations
    if (current.memoryUsage.heapUsed > this.thresholds.memoryWarning) {
      recommendations.push({
        id: 'memory-optimization-1',
        type: 'memory',
        priority: current.memoryUsage.heapUsed > this.thresholds.memoryCritical ? 'critical' : 'high',
        title: 'Hoher Speicherverbrauch erkannt',
        description: `Aktueller Heap-Verbrauch: ${(current.memoryUsage.heapUsed / 1024 / 1024).toFixed(1)}MB`,
        impact: 'Reduziert Speicherverbrauch und verbessert Anwendungsleistung',
        implementation: 'Cache-Größe reduzieren, nicht verwendete Objekte freigeben, Garbage Collection optimieren',
        estimatedSavings: {
          memory: current.memoryUsage.heapUsed * 0.3 // 30% reduction
        },
        complexity: 'medium'
      });
    }

    if (trends.memory.trend === 'increasing' && trends.memory.rate > 1000) {
      recommendations.push({
        id: 'memory-leak-detection',
        type: 'memory',
        priority: 'high',
        title: 'Mögliches Memory Leak erkannt',
        description: `Speicherverbrauch steigt kontinuierlich um ${(trends.memory.rate * 1000 / 1024 / 1024).toFixed(2)}MB/s`,
        impact: 'Verhindert Anwendungsabstürze durch Speichermangel',
        implementation: 'Memory Profiling durchführen, Event Listener entfernen, Circular References auflösen',
        estimatedSavings: {
          memory: current.memoryUsage.heapUsed * 0.5
        },
        complexity: 'hard'
      });
    }

    // CPU optimization recommendations
    if (current.cpuUsage.percent > this.thresholds.cpuWarning) {
      recommendations.push({
        id: 'cpu-optimization-1',
        type: 'cpu',
        priority: current.cpuUsage.percent > this.thresholds.cpuCritical ? 'critical' : 'high',
        title: 'Hohe CPU-Auslastung',
        description: `Aktuelle CPU-Auslastung: ${current.cpuUsage.percent.toFixed(1)}%`,
        impact: 'Verbessert Anwendungsreaktivität und reduziert Energieverbrauch',
        implementation: 'Algorithmen optimieren, Worker Threads verwenden, Batch-Verarbeitung implementieren',
        estimatedSavings: {
          cpu: current.cpuUsage.percent * 0.4 // 40% reduction
        },
        complexity: 'medium'
      });
    }

    // Cache optimization recommendations
    if (current.aiSpecificMetrics.cacheSize > 50 * 1024 * 1024) { // 50MB
      recommendations.push({
        id: 'cache-optimization-1',
        type: 'cache',
        priority: 'medium',
        title: 'Cache-Größe optimieren',
        description: `Cache-Größe: ${(current.aiSpecificMetrics.cacheSize / 1024 / 1024).toFixed(1)}MB`,
        impact: 'Reduziert Speicherverbrauch bei gleichbleibender Performance',
        implementation: 'Cache-TTL anpassen, LRU-Eviction implementieren, Cache-Komprimierung aktivieren',
        estimatedSavings: {
          memory: current.aiSpecificMetrics.cacheSize * 0.3,
          time: -100 // Might slightly increase response time
        },
        complexity: 'easy'
      });
    }

    // Algorithm optimization recommendations
    if (current.aiSpecificMetrics.vectorSearches > 100) {
      recommendations.push({
        id: 'algorithm-optimization-1',
        type: 'algorithm',
        priority: 'medium',
        title: 'Vektor-Suche optimieren',
        description: `Hohe Anzahl von Vektor-Suchen: ${current.aiSpecificMetrics.vectorSearches}`,
        impact: 'Reduziert Latenz und CPU-Verbrauch bei Vektor-Operationen',
        implementation: 'Batch-Suchen implementieren, Index-Optimierung, Approximate Nearest Neighbor verwenden',
        estimatedSavings: {
          cpu: 20,
          time: 500 // 500ms average reduction
        },
        complexity: 'medium'
      });
    }

    // Network optimization recommendations
    if (current.networkUsage.requestsPerSecond > 50) {
      recommendations.push({
        id: 'network-optimization-1',
        type: 'network',
        priority: 'medium',
        title: 'Netzwerk-Requests optimieren',
        description: `Hohe Request-Rate: ${current.networkUsage.requestsPerSecond} req/s`,
        impact: 'Reduziert Netzwerk-Latenz und API-Kosten',
        implementation: 'Request-Batching, Connection Pooling, Response Caching implementieren',
        estimatedSavings: {
          time: 200 // 200ms average reduction
        },
        complexity: 'medium'
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Set resource thresholds
   */
  setThresholds(thresholds: Partial<ResourceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...thresholds };
  }

  /**
   * Get resource alerts
   */
  getResourceAlerts(): Array<{
    type: 'warning' | 'critical';
    resource: string;
    message: string;
    value: number;
    threshold: number;
  }> {
    const alerts = [];
    const current = this.getCurrentMetrics();

    // Memory alerts
    if (current.memoryUsage.heapUsed > this.thresholds.memoryCritical) {
      alerts.push({
        type: 'critical' as const,
        resource: 'memory',
        message: 'Kritischer Speicherverbrauch erreicht',
        value: current.memoryUsage.heapUsed,
        threshold: this.thresholds.memoryCritical
      });
    } else if (current.memoryUsage.heapUsed > this.thresholds.memoryWarning) {
      alerts.push({
        type: 'warning' as const,
        resource: 'memory',
        message: 'Hoher Speicherverbrauch',
        value: current.memoryUsage.heapUsed,
        threshold: this.thresholds.memoryWarning
      });
    }

    // CPU alerts
    if (current.cpuUsage.percent > this.thresholds.cpuCritical) {
      alerts.push({
        type: 'critical' as const,
        resource: 'cpu',
        message: 'Kritische CPU-Auslastung',
        value: current.cpuUsage.percent,
        threshold: this.thresholds.cpuCritical
      });
    } else if (current.cpuUsage.percent > this.thresholds.cpuWarning) {
      alerts.push({
        type: 'warning' as const,
        resource: 'cpu',
        message: 'Hohe CPU-Auslastung',
        value: current.cpuUsage.percent,
        threshold: this.thresholds.cpuWarning
      });
    }

    return alerts;
  }

  private collectMetrics(): ResourceMetrics {
    const now = Date.now();
    const memoryUsage = this.getMemoryUsage();
    const cpuUsage = this.getCpuUsage();
    const networkUsage = this.getNetworkUsage();
    const diskUsage = this.getDiskUsage();
    const aiMetrics = this.getAISpecificMetrics();

    const metrics: ResourceMetrics = {
      timestamp: now,
      memoryUsage,
      cpuUsage,
      networkUsage,
      diskUsage,
      aiSpecificMetrics: aiMetrics
    };

    this.metrics.push(metrics);

    // Keep only recent metrics
    if (this.metrics.length > this.maxHistorySize) {
      this.metrics = this.metrics.slice(-this.maxHistorySize);
    }

    return metrics;
  }

  private getMemoryUsage() {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage();
    }

    // Fallback for browser environment
    return {
      heapUsed: 0,
      heapTotal: 0,
      external: 0,
      rss: 0
    };
  }

  private getCpuUsage() {
    if (typeof process !== 'undefined' && process.cpuUsage) {
      const usage = process.cpuUsage(this.lastCpuUsage);
      this.lastCpuUsage = process.cpuUsage();

      const totalUsage = usage.user + usage.system;
      const percent = (totalUsage / 1000000) / (this.collectionInterval / 1000) * 100;

      return {
        user: usage.user,
        system: usage.system,
        percent: Math.min(percent, 100)
      };
    }

    // Fallback simulation
    return {
      user: Math.random() * 50000,
      system: Math.random() * 20000,
      percent: Math.random() * 30 + 10
    };
  }

  private getNetworkUsage() {
    // Simplified network usage - in real implementation, use proper network monitoring
    const received = this.lastNetworkUsage.received + Math.random() * 1000;
    const sent = this.lastNetworkUsage.sent + Math.random() * 500;

    const result = {
      bytesReceived: received,
      bytesSent: sent,
      requestsPerSecond: Math.random() * 20
    };

    this.lastNetworkUsage = { received, sent };
    return result;
  }

  private getDiskUsage() {
    // Simplified disk usage - in real implementation, use proper disk monitoring
    const read = this.lastDiskUsage.read + Math.random() * 1000;
    const write = this.lastDiskUsage.write + Math.random() * 500;
    const readOps = this.lastDiskUsage.readOps + Math.floor(Math.random() * 10);
    const writeOps = this.lastDiskUsage.writeOps + Math.floor(Math.random() * 5);

    const result = {
      readBytes: read,
      writeBytes: write,
      readOperations: readOps,
      writeOperations: writeOps
    };

    this.lastDiskUsage = { read, write, readOps, writeOps };
    return result;
  }

  private getAISpecificMetrics() {
    // These would be updated by the actual AI services
    return {
      activeEmbeddings: Math.floor(Math.random() * 100),
      vectorSearches: Math.floor(Math.random() * 50),
      cacheSize: Math.floor(Math.random() * 100 * 1024 * 1024), // Random cache size up to 100MB
      queueLength: Math.floor(Math.random() * 20)
    };
  }
}