import { createRoute, Navigate, createRootRoute } from "@tanstack/react-router";
import { lazy } from "react";
import { ProtectedRoute, AuthenticatedIndex } from '@/components/auth';
import { allModuleRoutes } from './modules';

// Lazy load pages for code splitting
const DashboardPage = lazy(() => import("@/modules/dashboard/pages/DashboardPage"));
const DispatchPage = lazy(() => import("@/modules/dashboard/pages/DispatchPage"));
const CuttingPage = lazy(() => import("@/modules/dashboard/pages/CuttingPage"));
const MachinesPage = lazy(() => import("@/modules/dashboard/pages/MachinesPage"));
const IncomingGoodsPage = lazy(() => import("@/modules/dashboard/pages/IncomingGoodsPage"));
const CSRPage = lazy(() => import("@/modules/dashboard/pages/CSRPage"));
const SettingsPage = lazy(() => import("@/pages/SettingsPage"));
const UserSettingsPage = lazy(() => import("@/pages/UserSettingsPage"));
const SystemPage = lazy(() => import("@/modules/backend/pages/SystemPage"));
const AtrlPage = lazy(() => import("@/modules/dashboard/pages/AtrlPage"));
const ArilPage = lazy(() => import("@/modules/dashboard/pages/ArilPage"));
const WorkflowPage = lazy(() => import("@/modules/backend/pages/WorkflowPage"));
const StoerungenPage = lazy(() => import("@/modules/stoerungen/pages/StoerungenPage"));
const RegistrationPage = lazy(() => import("@/pages/RegistrationPage"));
const LoginPage = lazy(() => import("@/pages/LoginPage"));
const UserLandingPage = lazy(() => import("@/pages/UserLandingPage"));
// AblaengereiTestPage wurde entfernt

/**
 * Routen-Konfiguration für die Shopfloor-Management-App
 * 
 * Definiert die verschiedenen Routen für:
 * - Home (Startseite/Dashboard)
 * - Dispatch (Versand-Bereich)
 * - Cutting (Schnitt-Bereich)
 * - Incoming Goods (Wareneingang-Bereich)
 * - Settings (Einstellungen)
 * - User Settings (Benutzereinstellungen)
 */

// Index-Route (Startseite/Dashboard) - zeigt direkt die HomePage


