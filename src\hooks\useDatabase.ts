import { useState, useCallback } from 'react';

interface DatabaseTable {
  name: string;
}

interface TableData {
  columns: string[];
  rows: any[];
}

/**
 * Hook für die Kommunikation mit der Datenbank über IPC
 */
export function useDatabase() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);

  /**
   * Stellt eine Verbindung zur Datenbank her
   */
  const connect = useCallback(async (customPath?: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // @ts-ignore - Die window.electronAPI wird vom Preload-Skript bereitgestellt
      const result = await window.electronAPI.connectToDatabase(customPath);
      
      if (result.success) {
        setIsConnected(true);
        return true;
      } else {
        setError(result.error || 'Unbekannter <PERSON>hler beim <PERSON> mit der Datenbank');
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unbekannter Fehler';
      setError(`Verbindungsfehler: ${errorMessage}`);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Ruft alle Tabellen der Datenbank ab
   */
  const getTables = useCallback(async (): Promise<DatabaseTable[]> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // @ts-ignore
      return await window.electronAPI.getDatabaseTables();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unbekannter Fehler';
      setError(`Fehler beim Abrufen der Tabellen: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Ruft die Daten einer bestimmten Tabelle ab
   */
  const getTableData = useCallback(async (tableName: string, limit: number = 100): Promise<TableData> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // @ts-ignore
      return await window.electronAPI.getTableData(tableName, limit);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unbekannter Fehler';
      setError(`Fehler beim Lesen der Tabelle ${tableName}: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    connect,
    getTables,
    getTableData,
    isLoading,
    error,
    isConnected,
  };
}

export default useDatabase;
