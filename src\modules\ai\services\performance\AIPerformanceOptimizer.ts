import { AIPerformanceMonitor, PerformanceStats } from './AIPerformanceMonitor';
import { ResourceTracker, OptimizationRecommendation } from './ResourceTracker';
import { AICacheService } from '../caching/AICacheService';

export interface OptimizationStrategy {
  id: string;
  name: string;
  description: string;
  category: 'caching' | 'algorithm' | 'resource' | 'network';
  priority: number;
  enabled: boolean;
  config: Record<string, any>;
}

export interface OptimizationResult {
  strategyId: string;
  applied: boolean;
  improvement: {
    latencyReduction?: number;
    memoryReduction?: number;
    cpuReduction?: number;
    throughputIncrease?: number;
  };
  metrics: {
    before: PerformanceStats;
    after: PerformanceStats;
  };
  timestamp: number;
}

export interface AutoOptimizationConfig {
  enabled: boolean;
  aggressiveness: 'conservative' | 'moderate' | 'aggressive';
  maxMemoryUsage: number;
  maxCpuUsage: number;
  minCacheHitRate: number;
  optimizationInterval: number;
}

/**
 * AI Performance Optimizer - Automatically optimizes AI service performance
 */
export class AIPerformanceOptimizer {
  private strategies: Map<string, OptimizationStrategy> = new Map();
  private optimizationHistory: OptimizationResult[] = [];
  private autoOptimizationConfig: AutoOptimizationConfig = {
    enabled: true,
    aggressiveness: 'moderate',
    maxMemoryUsage: 200 * 1024 * 1024, // 200MB
    maxCpuUsage: 80, // 80%
    minCacheHitRate: 0.7, // 70%
    optimizationInterval: 300000 // 5 minutes
  };

  private optimizationInterval?: NodeJS.Timeout;

  constructor(
    private performanceMonitor: AIPerformanceMonitor,
    private resourceTracker: ResourceTracker,
    private cacheService: AICacheService
  ) {
    this.initializeStrategies();
    this.startAutoOptimization();
  }

  /**
   * Get all available optimization strategies
   */
  getStrategies(): OptimizationStrategy[] {
    return Array.from(this.strategies.values());
  }

  /**
   * Enable or disable a strategy
   */
  setStrategyEnabled(strategyId: string, enabled: boolean): void {
    const strategy = this.strategies.get(strategyId);
    if (strategy) {
      strategy.enabled = enabled;
    }
  }

  /**
   * Update strategy configuration
   */
  updateStrategyConfig(strategyId: string, config: Record<string, any>): void {
    const strategy = this.strategies.get(strategyId);
    if (strategy) {
      strategy.config = { ...strategy.config, ...config };
    }
  }

  /**
   * Run optimization analysis and get recommendations
   */
  async analyzePerformance(): Promise<{
    recommendations: OptimizationRecommendation[];
    currentMetrics: any;
    trends: any;
  }> {
    const resourceRecommendations = this.resourceTracker.getOptimizationRecommendations();
    const performanceStats = this.performanceMonitor.getAllOperationStats(3600000); // Last hour
    const currentMetrics = this.resourceTracker.getCurrentMetrics();
    const trends = this.resourceTracker.getTrends();

    // Add performance-based recommendations
    const performanceRecommendations = this.generatePerformanceRecommendations(performanceStats);

    return {
      recommendations: [...resourceRecommendations, ...performanceRecommendations],
      currentMetrics,
      trends
    };
  }

  /**
   * Apply specific optimization strategy
   */
  async applyOptimization(strategyId: string): Promise<OptimizationResult> {
    const strategy = this.strategies.get(strategyId);
    if (!strategy || !strategy.enabled) {
      throw new Error(`Strategy ${strategyId} not found or disabled`);
    }

    // Get baseline metrics
    const beforeStats = this.performanceMonitor.getAllOperationStats(300000); // Last 5 minutes
    const beforeMetrics = this.resourceTracker.getCurrentMetrics();

    let applied = false;
    const improvement = {
      latencyReduction: 0,
      memoryReduction: 0,
      cpuReduction: 0,
      throughputIncrease: 0
    };

    try {
      switch (strategy.id) {
        case 'cache-optimization':
          applied = await this.applyCacheOptimization(strategy.config);
          break;
        case 'memory-cleanup':
          applied = await this.applyMemoryCleanup(strategy.config);
          break;
        case 'batch-processing':
          applied = await this.applyBatchProcessing(strategy.config);
          break;
        case 'connection-pooling':
          applied = await this.applyConnectionPooling(strategy.config);
          break;
        case 'algorithm-optimization':
          applied = await this.applyAlgorithmOptimization(strategy.config);
          break;
        default:
          throw new Error(`Unknown strategy: ${strategy.id}`);
      }

      // Wait a bit for metrics to stabilize
      await new Promise(resolve => setTimeout(resolve, 30000));

      // Get after metrics
      const afterStats = this.performanceMonitor.getAllOperationStats(300000);
      const afterMetrics = this.resourceTracker.getCurrentMetrics();

      // Calculate improvements
      if (beforeStats.length > 0 && afterStats.length > 0) {
        const beforeAvgLatency = beforeStats.reduce((sum, s) => sum + s.averageDuration, 0) / beforeStats.length;
        const afterAvgLatency = afterStats.reduce((sum, s) => sum + s.averageDuration, 0) / afterStats.length;
        improvement.latencyReduction = Math.max(0, beforeAvgLatency - afterAvgLatency);

        const beforeAvgThroughput = beforeStats.reduce((sum, s) => sum + s.throughput, 0) / beforeStats.length;
        const afterAvgThroughput = afterStats.reduce((sum, s) => sum + s.throughput, 0) / afterStats.length;
        improvement.throughputIncrease = Math.max(0, afterAvgThroughput - beforeAvgThroughput);
      }

      improvement.memoryReduction = Math.max(0, beforeMetrics.memoryUsage.heapUsed - afterMetrics.memoryUsage.heapUsed);
      improvement.cpuReduction = Math.max(0, beforeMetrics.cpuUsage.percent - afterMetrics.cpuUsage.percent);

      const result: OptimizationResult = {
        strategyId,
        applied,
        improvement,
        metrics: {
          before: beforeStats[0] || {} as PerformanceStats,
          after: afterStats[0] || {} as PerformanceStats
        },
        timestamp: Date.now()
      };

      this.optimizationHistory.push(result);
      return result;

    } catch (error) {
      const result: OptimizationResult = {
        strategyId,
        applied: false,
        improvement,
        metrics: {
          before: beforeStats[0] || {} as PerformanceStats,
          after: beforeStats[0] || {} as PerformanceStats
        },
        timestamp: Date.now()
      };

      this.optimizationHistory.push(result);
      throw error;
    }
  }

  /**
   * Run automatic optimization based on current performance
   */
  async runAutoOptimization(): Promise<OptimizationResult[]> {
    if (!this.autoOptimizationConfig.enabled) {
      return [];
    }

    const analysis = await this.analyzePerformance();
    const results: OptimizationResult[] = [];

    // Determine which optimizations to apply based on current state
    const currentMetrics = analysis.currentMetrics;
    const strategies = this.getApplicableStrategies(currentMetrics, analysis.trends);

    for (const strategy of strategies) {
      try {
        const result = await this.applyOptimization(strategy.id);
        results.push(result);

        // Wait between optimizations to avoid conflicts
        await new Promise(resolve => setTimeout(resolve, 10000));
      } catch (error) {
        console.error(`Failed to apply optimization ${strategy.id}:`, error);
      }
    }

    return results;
  }

  /**
   * Get optimization history
   */
  getOptimizationHistory(limit?: number): OptimizationResult[] {
    const history = this.optimizationHistory.sort((a, b) => b.timestamp - a.timestamp);
    return limit ? history.slice(0, limit) : history;
  }

  /**
   * Update auto-optimization configuration
   */
  updateAutoOptimizationConfig(config: Partial<AutoOptimizationConfig>): void {
    this.autoOptimizationConfig = { ...this.autoOptimizationConfig, ...config };
    
    if (config.enabled !== undefined) {
      if (config.enabled) {
        this.startAutoOptimization();
      } else {
        this.stopAutoOptimization();
      }
    }
  }

  /**
   * Get current auto-optimization configuration
   */
  getAutoOptimizationConfig(): AutoOptimizationConfig {
    return { ...this.autoOptimizationConfig };
  }

  private initializeStrategies(): void {
    const strategies: OptimizationStrategy[] = [
      {
        id: 'cache-optimization',
        name: 'Cache-Optimierung',
        description: 'Optimiert Cache-Größe und Eviction-Strategien',
        category: 'caching',
        priority: 1,
        enabled: true,
        config: {
          maxCacheSize: 100 * 1024 * 1024, // 100MB
          evictionPolicy: 'lru',
          compressionEnabled: true
        }
      },
      {
        id: 'memory-cleanup',
        name: 'Speicher-Bereinigung',
        description: 'Bereinigt nicht verwendete Objekte und forciert Garbage Collection',
        category: 'resource',
        priority: 2,
        enabled: true,
        config: {
          forceGC: true,
          clearUnusedCaches: true,
          compactMemory: true
        }
      },
      {
        id: 'batch-processing',
        name: 'Batch-Verarbeitung',
        description: 'Gruppiert ähnliche Operationen für effizientere Verarbeitung',
        category: 'algorithm',
        priority: 3,
        enabled: true,
        config: {
          batchSize: 10,
          maxWaitTime: 1000,
          enableBatching: true
        }
      },
      {
        id: 'connection-pooling',
        name: 'Verbindungs-Pooling',
        description: 'Optimiert Datenbankverbindungen und API-Calls',
        category: 'network',
        priority: 4,
        enabled: true,
        config: {
          maxConnections: 10,
          connectionTimeout: 5000,
          reuseConnections: true
        }
      },
      {
        id: 'algorithm-optimization',
        name: 'Algorithmus-Optimierung',
        description: 'Wählt effizientere Algorithmen basierend auf Datengrößen',
        category: 'algorithm',
        priority: 5,
        enabled: true,
        config: {
          adaptiveAlgorithms: true,
          parallelProcessing: true,
          approximateResults: false
        }
      }
    ];

    strategies.forEach(strategy => {
      this.strategies.set(strategy.id, strategy);
    });
  }

  private generatePerformanceRecommendations(stats: PerformanceStats[]): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = [];

    for (const stat of stats) {
      if (stat.errorRate > 0.05) {
        recommendations.push({
          id: `error-rate-${stat.operationName}`,
          type: 'algorithm',
          priority: 'high',
          title: `Hohe Fehlerrate bei ${stat.operationName}`,
          description: `Fehlerrate: ${(stat.errorRate * 100).toFixed(1)}%`,
          impact: 'Verbessert Zuverlässigkeit und Benutzererfahrung',
          implementation: 'Retry-Logik implementieren, Input-Validierung verbessern',
          estimatedSavings: {
            time: stat.averageDuration * 0.2
          },
          complexity: 'medium'
        });
      }

      if (stat.p95Duration > stat.averageDuration * 3) {
        recommendations.push({
          id: `latency-variance-${stat.operationName}`,
          type: 'algorithm',
          priority: 'medium',
          title: `Inkonsistente Performance bei ${stat.operationName}`,
          description: `P95 Latenz ist ${stat.p95Duration}ms vs Durchschnitt ${stat.averageDuration}ms`,
          impact: 'Verbessert vorhersagbare Performance',
          implementation: 'Caching implementieren, Algorithmus-Komplexität reduzieren',
          estimatedSavings: {
            time: stat.p95Duration - stat.averageDuration
          },
          complexity: 'medium'
        });
      }
    }

    return recommendations;
  }

  private getApplicableStrategies(metrics: any, trends: any): OptimizationStrategy[] {
    const applicable: OptimizationStrategy[] = [];

    // Memory-based strategies
    if (metrics.memoryUsage.heapUsed > this.autoOptimizationConfig.maxMemoryUsage) {
      const memoryStrategy = this.strategies.get('memory-cleanup');
      if (memoryStrategy?.enabled) {
        applicable.push(memoryStrategy);
      }

      const cacheStrategy = this.strategies.get('cache-optimization');
      if (cacheStrategy?.enabled) {
        applicable.push(cacheStrategy);
      }
    }

    // CPU-based strategies
    if (metrics.cpuUsage.percent > this.autoOptimizationConfig.maxCpuUsage) {
      const batchStrategy = this.strategies.get('batch-processing');
      if (batchStrategy?.enabled) {
        applicable.push(batchStrategy);
      }

      const algorithmStrategy = this.strategies.get('algorithm-optimization');
      if (algorithmStrategy?.enabled) {
        applicable.push(algorithmStrategy);
      }
    }

    // Network-based strategies
    if (metrics.networkUsage.requestsPerSecond > 20) {
      const connectionStrategy = this.strategies.get('connection-pooling');
      if (connectionStrategy?.enabled) {
        applicable.push(connectionStrategy);
      }
    }

    return applicable.sort((a, b) => a.priority - b.priority);
  }

  private async applyCacheOptimization(config: any): Promise<boolean> {
    try {
      // Clear old cache entries
      await this.cacheService.clear('old:*');
      
      // Update cache configuration
      // This would update the cache service configuration
      
      return true;
    } catch (error) {
      console.error('Cache optimization failed:', error);
      return false;
    }
  }

  private async applyMemoryCleanup(config: any): Promise<boolean> {
    try {
      if (config.forceGC && global.gc) {
        global.gc();
      }

      if (config.clearUnusedCaches) {
        await this.cacheService.clear('unused:*');
      }

      return true;
    } catch (error) {
      console.error('Memory cleanup failed:', error);
      return false;
    }
  }

  private async applyBatchProcessing(config: any): Promise<boolean> {
    try {
      // This would enable batching in relevant services
      // Implementation depends on specific service architecture
      return true;
    } catch (error) {
      console.error('Batch processing optimization failed:', error);
      return false;
    }
  }

  private async applyConnectionPooling(config: any): Promise<boolean> {
    try {
      // This would optimize database connection pooling
      // Implementation depends on database service
      return true;
    } catch (error) {
      console.error('Connection pooling optimization failed:', error);
      return false;
    }
  }

  private async applyAlgorithmOptimization(config: any): Promise<boolean> {
    try {
      // This would switch to more efficient algorithms
      // Implementation depends on specific algorithms used
      return true;
    } catch (error) {
      console.error('Algorithm optimization failed:', error);
      return false;
    }
  }

  private startAutoOptimization(): void {
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
    }

    this.optimizationInterval = setInterval(async () => {
      try {
        await this.runAutoOptimization();
      } catch (error) {
        console.error('Auto-optimization failed:', error);
      }
    }, this.autoOptimizationConfig.optimizationInterval);
  }

  private stopAutoOptimization(): void {
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
      this.optimizationInterval = undefined;
    }
  }
}