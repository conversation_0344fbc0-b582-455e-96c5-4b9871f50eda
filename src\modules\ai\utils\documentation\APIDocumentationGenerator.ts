import { DocumentationSection } from '../../components/documentation/AIDocumentationProvider';

export interface APIEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  description: string;
  parameters?: APIParameter[];
  requestBody?: APIRequestBody;
  responses: APIResponse[];
  examples?: APIExample[];
  tags: string[];
}

export interface APIParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description: string;
  example?: any;
  enum?: string[];
}

export interface APIRequestBody {
  contentType: string;
  schema: any;
  example?: any;
  description?: string;
}

export interface APIResponse {
  statusCode: number;
  description: string;
  schema?: any;
  example?: any;
}

export interface APIExample {
  title: string;
  description?: string;
  request?: any;
  response?: any;
}

export interface APIService {
  name: string;
  description: string;
  baseUrl: string;
  version: string;
  endpoints: APIEndpoint[];
  authentication?: {
    type: 'bearer' | 'apiKey' | 'basic';
    description: string;
  };
}

export class APIDocumentationGenerator {
  private services: APIService[] = [];

  constructor() {
    this.initializeServices();
  }

  // Generate documentation sections for all API services
  generateDocumentationSections(): DocumentationSection[] {
    const sections: DocumentationSection[] = [];

    // Overview section
    sections.push({
      id: 'api-overview',
      title: 'AI Services API Overview',
      content: this.generateOverviewContent(),
      category: 'api',
      tags: ['api', 'overview', 'services'],
      lastUpdated: new Date(),
      difficulty: 'intermediate'
    });

    // Authentication section
    sections.push({
      id: 'api-authentication',
      title: 'API Authentication',
      content: this.generateAuthenticationContent(),
      category: 'api',
      tags: ['api', 'authentication', 'security'],
      lastUpdated: new Date(),
      difficulty: 'intermediate'
    });

    // Service-specific sections
    this.services.forEach(service => {
      sections.push({
        id: `api-${service.name.toLowerCase().replace(/\s+/g, '-')}`,
        title: `${service.name} API`,
        content: this.generateServiceContent(service),
        category: 'api',
        tags: ['api', service.name.toLowerCase(), ...service.endpoints.flatMap(e => e.tags)],
        lastUpdated: new Date(),
        difficulty: 'advanced'
      });
    });

    // Error handling section
    sections.push({
      id: 'api-error-handling',
      title: 'API Error Handling',
      content: this.generateErrorHandlingContent(),
      category: 'api',
      tags: ['api', 'errors', 'troubleshooting'],
      lastUpdated: new Date(),
      difficulty: 'intermediate'
    });

    // Rate limiting section
    sections.push({
      id: 'api-rate-limiting',
      title: 'API Rate Limiting',
      content: this.generateRateLimitingContent(),
      category: 'api',
      tags: ['api', 'rate-limiting', 'performance'],
      lastUpdated: new Date(),
      difficulty: 'intermediate'
    });

    return sections;
  }

  // Generate OpenAPI specification
  generateOpenAPISpec(): any {
    const spec = {
      openapi: '3.0.3',
      info: {
        title: 'AI Module API',
        description: 'Comprehensive API for AI services including RAG, optimization, and analytics',
        version: '1.0.0',
        contact: {
          name: 'AI Module Support',
          email: '<EMAIL>'
        }
      },
      servers: [
        {
          url: '/api/ai',
          description: 'AI Module API Server'
        }
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          }
        },
        schemas: this.generateSchemas()
      },
      security: [
        {
          bearerAuth: []
        }
      ],
      paths: this.generatePaths()
    };

    return spec;
  }

  // Generate TypeScript interfaces for API types
  generateTypeScriptInterfaces(): string {
    let interfaces = '';

    // Generate interfaces for each service
    this.services.forEach(service => {
      interfaces += this.generateServiceInterfaces(service);
    });

    // Generate common interfaces
    interfaces += this.generateCommonInterfaces();

    return interfaces;
  }

  private initializeServices(): void {
    this.services = [
      {
        name: 'RAG Service',
        description: 'Retrieval-Augmented Generation service for enhanced AI responses',
        baseUrl: '/api/ai/rag',
        version: '1.0.0',
        authentication: {
          type: 'bearer',
          description: 'JWT token required for all endpoints'
        },
        endpoints: [
          {
            method: 'POST',
            path: '/query',
            description: 'Process a query with RAG enhancement',
            requestBody: {
              contentType: 'application/json',
              schema: {
                type: 'object',
                properties: {
                  query: { type: 'string', description: 'User query' },
                  context: { type: 'object', description: 'Additional context' },
                  includeRAG: { type: 'boolean', description: 'Enable RAG enhancement' }
                },
                required: ['query']
              },
              example: {
                query: 'Show me cutting efficiency trends',
                includeRAG: true
              }
            },
            responses: [
              {
                statusCode: 200,
                description: 'Successful response',
                schema: {
                  type: 'object',
                  properties: {
                    response: { type: 'string' },
                    sources: { type: 'array' },
                    confidence: { type: 'number' }
                  }
                }
              },
              {
                statusCode: 400,
                description: 'Invalid request'
              },
              {
                statusCode: 500,
                description: 'Internal server error'
              }
            ],
            tags: ['rag', 'query', 'ai']
          },
          {
            method: 'POST',
            path: '/index',
            description: 'Index documents for RAG search',
            requestBody: {
              contentType: 'application/json',
              schema: {
                type: 'object',
                properties: {
                  documents: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        content: { type: 'string' },
                        metadata: { type: 'object' }
                      }
                    }
                  }
                },
                required: ['documents']
              }
            },
            responses: [
              {
                statusCode: 200,
                description: 'Documents indexed successfully'
              }
            ],
            tags: ['rag', 'indexing']
          }
        ]
      },
      {
        name: 'Cutting Optimizer',
        description: 'Optimization service for cutting patterns and waste reduction',
        baseUrl: '/api/ai/cutting',
        version: '1.0.0',
        authentication: {
          type: 'bearer',
          description: 'JWT token required for all endpoints'
        },
        endpoints: [
          {
            method: 'POST',
            path: '/optimize',
            description: 'Optimize cutting patterns for minimal waste',
            requestBody: {
              contentType: 'application/json',
              schema: {
                type: 'object',
                properties: {
                  orders: { type: 'array', description: 'Cutting orders' },
                  availableDrums: { type: 'array', description: 'Available cable drums' },
                  constraints: { type: 'object', description: 'Optimization constraints' }
                },
                required: ['orders', 'availableDrums']
              }
            },
            responses: [
              {
                statusCode: 200,
                description: 'Optimization completed',
                schema: {
                  type: 'object',
                  properties: {
                    plan: { type: 'object' },
                    efficiency: { type: 'number' },
                    wastePercentage: { type: 'number' }
                  }
                }
              }
            ],
            tags: ['cutting', 'optimization']
          }
        ]
      },
      {
        name: 'Inventory Intelligence',
        description: 'AI-powered inventory management and analytics',
        baseUrl: '/api/ai/inventory',
        version: '1.0.0',
        authentication: {
          type: 'bearer',
          description: 'JWT token required for all endpoints'
        },
        endpoints: [
          {
            method: 'GET',
            path: '/abc-analysis',
            description: 'Perform ABC analysis on inventory items',
            parameters: [
              {
                name: 'department',
                type: 'string',
                required: false,
                description: 'Filter by department'
              }
            ],
            responses: [
              {
                statusCode: 200,
                description: 'ABC analysis results',
                schema: {
                  type: 'object',
                  properties: {
                    classA: { type: 'array' },
                    classB: { type: 'array' },
                    classC: { type: 'array' }
                  }
                }
              }
            ],
            tags: ['inventory', 'analysis']
          },
          {
            method: 'POST',
            path: '/forecast',
            description: 'Generate demand forecast for items',
            requestBody: {
              contentType: 'application/json',
              schema: {
                type: 'object',
                properties: {
                  itemIds: { type: 'array', items: { type: 'string' } },
                  horizon: { type: 'number', description: 'Forecast horizon in days' }
                },
                required: ['itemIds', 'horizon']
              }
            },
            responses: [
              {
                statusCode: 200,
                description: 'Forecast generated',
                schema: {
                  type: 'object',
                  properties: {
                    forecasts: { type: 'array' },
                    confidence: { type: 'number' }
                  }
                }
              }
            ],
            tags: ['inventory', 'forecasting']
          }
        ]
      }
    ];
  }

  private generateOverviewContent(): string {
    return `
# AI Services API Overview

The AI Module provides a comprehensive set of REST APIs for accessing intelligent automation and optimization capabilities. All APIs follow RESTful principles and return JSON responses.

## Base URL
\`\`\`
/api/ai
\`\`\`

## Available Services

${this.services.map(service => `
### ${service.name}
${service.description}
- **Base URL**: \`${service.baseUrl}\`
- **Version**: ${service.version}
- **Endpoints**: ${service.endpoints.length}
`).join('\n')}

## Common Response Format

All API responses follow a consistent format:

\`\`\`json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-01T00:00:00Z"
}
\`\`\`

## Error Response Format

Error responses include detailed information for debugging:

\`\`\`json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": { ... }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
\`\`\`
    `.trim();
  }

  private generateAuthenticationContent(): string {
    return `
# API Authentication

All AI service endpoints require authentication using JWT (JSON Web Tokens).

## Authentication Method

Include the JWT token in the Authorization header:

\`\`\`http
Authorization: Bearer <your-jwt-token>
\`\`\`

## Token Requirements

- **Format**: JWT (JSON Web Token)
- **Expiration**: 15 minutes
- **Refresh**: Automatic refresh available
- **Scope**: Role-based access control

## Example Request

\`\`\`bash
curl -X POST /api/ai/rag/query \\
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \\
  -H "Content-Type: application/json" \\
  -d '{"query": "Show me cutting efficiency trends"}'
\`\`\`

## Authentication Errors

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| 401 | UNAUTHORIZED | Missing or invalid token |
| 403 | FORBIDDEN | Insufficient permissions |
| 401 | TOKEN_EXPIRED | Token has expired |

## Role-Based Access

Different endpoints require different permission levels:

- **Besucher**: Read-only access to basic endpoints
- **Benutzer**: Access to analysis and optimization endpoints
- **Administrator**: Full access to all endpoints including management functions
    `.trim();
  }

  private generateServiceContent(service: APIService): string {
    let content = `
# ${service.name} API

${service.description}

**Base URL**: \`${service.baseUrl}\`
**Version**: ${service.version}

## Endpoints

${service.endpoints.map(endpoint => `
### ${endpoint.method} ${endpoint.path}

${endpoint.description}

${endpoint.parameters ? `
**Parameters:**
${endpoint.parameters.map(param => `
- \`${param.name}\` (${param.type}${param.required ? ', required' : ', optional'}): ${param.description}
  ${param.example ? `Example: \`${JSON.stringify(param.example)}\`` : ''}
  ${param.enum ? `Allowed values: ${param.enum.map(v => `\`${v}\``).join(', ')}` : ''}
`).join('')}
` : ''}

${endpoint.requestBody ? `
**Request Body:**
\`\`\`json
${JSON.stringify(endpoint.requestBody.example || endpoint.requestBody.schema, null, 2)}
\`\`\`
` : ''}

**Responses:**
${endpoint.responses.map(response => `
- **${response.statusCode}**: ${response.description}
  ${response.example ? `\`\`\`json\n${JSON.stringify(response.example, null, 2)}\n\`\`\`` : ''}
`).join('')}

${endpoint.examples ? `
**Examples:**
${endpoint.examples.map(example => `
#### ${example.title}
${example.description || ''}

${example.request ? `Request:\n\`\`\`json\n${JSON.stringify(example.request, null, 2)}\n\`\`\`` : ''}

${example.response ? `Response:\n\`\`\`json\n${JSON.stringify(example.response, null, 2)}\n\`\`\`` : ''}
`).join('')}
` : ''}
`).join('\n---\n')}
    `.trim();

    return content;
  }

  private generateErrorHandlingContent(): string {
    return `
# API Error Handling

The AI Module APIs use standard HTTP status codes and provide detailed error information to help with debugging and error recovery.

## Error Response Structure

All error responses follow this structure:

\`\`\`json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "field": "Additional error details",
      "validation": ["List of validation errors"]
    }
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "requestId": "unique-request-identifier"
}
\`\`\`

## Common Error Codes

| HTTP Status | Error Code | Description | Resolution |
|-------------|------------|-------------|------------|
| 400 | VALIDATION_ERROR | Request validation failed | Check request parameters and format |
| 401 | UNAUTHORIZED | Authentication required | Provide valid JWT token |
| 403 | FORBIDDEN | Insufficient permissions | Check user role and permissions |
| 404 | NOT_FOUND | Resource not found | Verify resource ID and availability |
| 409 | CONFLICT | Resource conflict | Check for duplicate resources |
| 429 | RATE_LIMITED | Too many requests | Implement request throttling |
| 500 | INTERNAL_ERROR | Server error | Contact support with requestId |
| 503 | SERVICE_UNAVAILABLE | Service temporarily unavailable | Retry with exponential backoff |

## Service-Specific Errors

### RAG Service Errors
- \`EMBEDDING_FAILED\`: Failed to generate embeddings
- \`VECTOR_SEARCH_FAILED\`: Vector search operation failed
- \`INSUFFICIENT_CONTEXT\`: Not enough context for RAG enhancement

### Cutting Optimizer Errors
- \`OPTIMIZATION_FAILED\`: Optimization algorithm failed
- \`INVALID_CONSTRAINTS\`: Invalid optimization constraints
- \`INSUFFICIENT_DRUMS\`: Not enough drums for optimization

### Inventory Intelligence Errors
- \`PREDICTION_FAILED\`: Demand prediction failed
- \`INSUFFICIENT_DATA\`: Not enough historical data
- \`CLASSIFICATION_ERROR\`: ABC classification failed

## Error Recovery Strategies

### Retry Logic
Implement exponential backoff for transient errors:

\`\`\`javascript
async function retryRequest(requestFn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      if (error.status >= 500 && i < maxRetries - 1) {
        await new Promise(resolve => 
          setTimeout(resolve, Math.pow(2, i) * 1000)
        );
        continue;
      }
      throw error;
    }
  }
}
\`\`\`

### Graceful Degradation
Handle service unavailability gracefully:

\`\`\`javascript
try {
  const result = await aiService.optimize(request);
  return result;
} catch (error) {
  if (error.code === 'SERVICE_UNAVAILABLE') {
    // Fall back to basic algorithm
    return basicOptimization(request);
  }
  throw error;
}
\`\`\`
    `.trim();
  }

  private generateRateLimitingContent(): string {
    return `
# API Rate Limiting

To ensure fair usage and system stability, all AI service endpoints implement rate limiting.

## Rate Limits

| Service | Endpoint | Limit | Window |
|---------|----------|-------|--------|
| RAG Service | /query | 100 requests | per minute |
| RAG Service | /index | 10 requests | per minute |
| Cutting Optimizer | /optimize | 20 requests | per minute |
| Inventory Intelligence | /forecast | 50 requests | per minute |
| Inventory Intelligence | /abc-analysis | 30 requests | per minute |

## Rate Limit Headers

All responses include rate limiting information:

\`\`\`http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
X-RateLimit-Window: 60
\`\`\`

## Rate Limit Exceeded Response

When rate limits are exceeded, the API returns:

\`\`\`json
{
  "success": false,
  "error": {
    "code": "RATE_LIMITED",
    "message": "Rate limit exceeded. Try again later.",
    "details": {
      "limit": 100,
      "window": 60,
      "resetTime": "2024-01-01T00:01:00Z"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
\`\`\`

## Best Practices

### Request Batching
Combine multiple operations into single requests when possible:

\`\`\`javascript
// Instead of multiple requests
const results = await Promise.all([
  aiService.forecast({ itemId: 'item1', horizon: 30 }),
  aiService.forecast({ itemId: 'item2', horizon: 30 }),
  aiService.forecast({ itemId: 'item3', horizon: 30 })
]);

// Use batch request
const results = await aiService.forecast({
  itemIds: ['item1', 'item2', 'item3'],
  horizon: 30
});
\`\`\`

### Caching
Implement client-side caching for frequently requested data:

\`\`\`javascript
const cache = new Map();

async function getCachedForecast(itemId, horizon) {
  const key = \`\${itemId}-\${horizon}\`;
  
  if (cache.has(key)) {
    const { data, timestamp } = cache.get(key);
    // Use cached data if less than 1 hour old
    if (Date.now() - timestamp < 3600000) {
      return data;
    }
  }
  
  const data = await aiService.forecast({ itemIds: [itemId], horizon });
  cache.set(key, { data, timestamp: Date.now() });
  return data;
}
\`\`\`

### Request Throttling
Implement client-side throttling to stay within limits:

\`\`\`javascript
class RateLimitedClient {
  constructor(requestsPerMinute = 50) {
    this.requests = [];
    this.limit = requestsPerMinute;
  }
  
  async request(requestFn) {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < 60000);
    
    if (this.requests.length >= this.limit) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = 60000 - (now - oldestRequest);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.requests.push(now);
    return await requestFn();
  }
}
\`\`\`
    `.trim();
  }

  private generateSchemas(): any {
    return {
      RAGQuery: {
        type: 'object',
        properties: {
          query: { type: 'string', description: 'User query' },
          context: { type: 'object', description: 'Additional context' },
          includeRAG: { type: 'boolean', description: 'Enable RAG enhancement' }
        },
        required: ['query']
      },
      RAGResponse: {
        type: 'object',
        properties: {
          response: { type: 'string', description: 'AI response' },
          sources: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                content: { type: 'string' },
                similarity: { type: 'number' }
              }
            }
          },
          confidence: { type: 'number', minimum: 0, maximum: 1 }
        }
      },
      CuttingOptimizationRequest: {
        type: 'object',
        properties: {
          orders: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                length: { type: 'number' },
                quantity: { type: 'number' },
                cableType: { type: 'string' }
              }
            }
          },
          availableDrums: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                length: { type: 'number' },
                cableType: { type: 'string' }
              }
            }
          }
        },
        required: ['orders', 'availableDrums']
      }
    };
  }

  private generatePaths(): any {
    const paths: any = {};

    this.services.forEach(service => {
      service.endpoints.forEach(endpoint => {
        const fullPath = `${service.baseUrl}${endpoint.path}`;
        if (!paths[fullPath]) {
          paths[fullPath] = {};
        }

        paths[fullPath][endpoint.method.toLowerCase()] = {
          summary: endpoint.description,
          tags: endpoint.tags,
          parameters: endpoint.parameters?.map(param => ({
            name: param.name,
            in: 'query',
            required: param.required,
            description: param.description,
            schema: { type: param.type }
          })),
          requestBody: endpoint.requestBody ? {
            required: true,
            content: {
              [endpoint.requestBody.contentType]: {
                schema: endpoint.requestBody.schema,
                example: endpoint.requestBody.example
              }
            }
          } : undefined,
          responses: endpoint.responses.reduce((acc, response) => {
            acc[response.statusCode] = {
              description: response.description,
              content: response.schema ? {
                'application/json': {
                  schema: response.schema,
                  example: response.example
                }
              } : undefined
            };
            return acc;
          }, {} as any)
        };
      });
    });

    return paths;
  }

  private generateServiceInterfaces(service: APIService): string {
    let interfaces = `
// ${service.name} Interfaces
export interface ${service.name.replace(/\s+/g, '')}Client {
${service.endpoints.map(endpoint => {
  const methodName = endpoint.path.replace(/^\//, '').replace(/\//g, '_');
  const paramType = endpoint.requestBody ? `${service.name.replace(/\s+/g, '')}${methodName}Request` : 'void';
  const returnType = `Promise<${service.name.replace(/\s+/g, '')}${methodName}Response>`;
  
  return `  ${methodName}(${paramType !== 'void' ? `request: ${paramType}` : ''}): ${returnType};`;
}).join('\n')}
}

${service.endpoints.map(endpoint => {
  const methodName = endpoint.path.replace(/^\//, '').replace(/\//g, '_');
  const requestInterface = endpoint.requestBody ? `
export interface ${service.name.replace(/\s+/g, '')}${methodName}Request {
${Object.entries(endpoint.requestBody.schema.properties || {}).map(([key, prop]: [string, any]) => 
  `  ${key}${endpoint.requestBody?.schema.required?.includes(key) ? '' : '?'}: ${this.mapSchemaTypeToTS(prop)};`
).join('\n')}
}` : '';

  const responseInterface = `
export interface ${service.name.replace(/\s+/g, '')}${methodName}Response {
  success: boolean;
  data: any;
  message?: string;
  timestamp: string;
}`;

  return requestInterface + responseInterface;
}).join('\n')}
`;

    return interfaces;
  }

  private generateCommonInterfaces(): string {
    return `
// Common API Interfaces
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: APIError;
  message?: string;
  timestamp: string;
  requestId?: string;
}

export interface APIError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export interface PaginatedResponse<T> extends APIResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  window: number;
}
`;
  }

  private mapSchemaTypeToTS(schema: any): string {
    switch (schema.type) {
      case 'string': return 'string';
      case 'number': return 'number';
      case 'boolean': return 'boolean';
      case 'array': return `${this.mapSchemaTypeToTS(schema.items)}[]`;
      case 'object': return 'Record<string, any>';
      default: return 'any';
    }
  }
}