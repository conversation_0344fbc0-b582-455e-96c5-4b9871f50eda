/**
 * Discrete Event Simulation Tests
 * Unit tests for the discrete event simulation framework
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DiscreteEventSimulation } from '../simulation/DiscreteEventSimulation';
import {
  SimulationConfig,
  ProcessData,
  ProcessStep,
  ProcessResource,
  SimulationStatistics
} from '../types';

describe('DiscreteEventSimulation', () => {
  let mockProcessData: ProcessData;
  let mockSimulationConfig: SimulationConfig;

  beforeEach(() => {
    // Mock process data for simulation
    mockProcessData = {
      processId: 'sim-test-001',
      name: 'Simulation Test Process',
      steps: [
        {
          stepId: 'step-001',
          name: 'Input',
          duration: 10, // minutes
          resourceRequirements: [
            { resourceId: 'worker-001', quantity: 1, duration: 10 }
          ],
          dependencies: [],
          capacity: 2,
          currentUtilization: 0.7
        },
        {
          stepId: 'step-002',
          name: 'Process',
          duration: 20,
          resourceRequirements: [
            { resourceId: 'machine-001', quantity: 1, duration: 20 }
          ],
          dependencies: ['step-001'],
          capacity: 1,
          currentUtilization: 0.8
        },
        {
          stepId: 'step-003',
          name: 'Output',
          duration: 5,
          resourceRequirements: [
            { resourceId: 'worker-002', quantity: 1, duration: 5 }
          ],
          dependencies: ['step-002'],
          capacity: 3,
          currentUtilization: 0.5
        }
      ],
      resources: [
        {
          resourceId: 'worker-001',
          name: 'Input Worker',
          type: 'human',
          capacity: 2,
          currentLoad: 0.7,
          availability: [],
          costPerHour: 25
        },
        {
          resourceId: 'machine-001',
          name: 'Processing Machine',
          type: 'machine',
          capacity: 1,
          currentLoad: 0.8,
          availability: [],
          costPerHour: 100
        },
        {
          resourceId: 'worker-002',
          name: 'Output Worker',
          type: 'human',
          capacity: 3,
          currentLoad: 0.5,
          availability: [],
          costPerHour: 30
        }
      ],
      metrics: {
        throughput: 2.5, // items per hour
        cycleTime: 35, // minutes
        leadTime: 45, // minutes
        efficiency: 0.75,
        utilization: 0.67,
        qualityRate: 0.95,
        cost: 120
      },
      constraints: [],
      historicalData: []
    };

    // Mock simulation configuration
    mockSimulationConfig = {
      processId: 'sim-test-001',
      duration: 2, // 2 hours
      iterations: 10,
      changes: [],
      randomSeed: 12345,
      warmupPeriod: 0.5, // 30 minutes
      confidenceLevel: 0.95
    };
  });

  describe('constructor', () => {
    it('should initialize simulation correctly', () => {
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      expect(simulation).toBeDefined();
    });

    it('should accept valid configuration', () => {
      expect(() => {
        new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      }).not.toThrow();
    });
  });

  describe('runSimulation', () => {
    it('should run simulation successfully', async () => {
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      expect(statistics).toBeDefined();
      expect(statistics.entitiesProcessed).toBeGreaterThanOrEqual(0);
      expect(statistics.totalWaitTime).toBeGreaterThanOrEqual(0);
      expect(statistics.totalProcessingTime).toBeGreaterThanOrEqual(0);
      expect(statistics.resourceUtilization).toBeDefined();
      expect(statistics.stepThroughput).toBeDefined();
      expect(statistics.qualityMetrics).toBeDefined();
    });

    it('should process entities through all steps', async () => {
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      expect(statistics.entitiesProcessed).toBeGreaterThan(0);
      expect(statistics.totalProcessingTime).toBeGreaterThan(0);
    });

    it('should track resource utilization', async () => {
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      expect(statistics.resourceUtilization).toBeInstanceOf(Map);
      
      // Should have utilization data for each resource
      mockProcessData.resources.forEach(resource => {
        const utilization = statistics.resourceUtilization.get(resource.resourceId);
        expect(utilization).toBeDefined();
        expect(utilization).toBeGreaterThanOrEqual(0);
        expect(utilization).toBeLessThanOrEqual(1);
      });
    });

    it('should track step throughput', async () => {
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      expect(statistics.stepThroughput).toBeInstanceOf(Map);
      
      // Should have throughput data for steps that processed entities
      if (statistics.entitiesProcessed > 0) {
        expect(statistics.stepThroughput.size).toBeGreaterThan(0);
      }
    });

    it('should handle warmup period', async () => {
      const configWithWarmup = {
        ...mockSimulationConfig,
        warmupPeriod: 1 // 1 hour warmup
      };

      const simulation = new DiscreteEventSimulation(configWithWarmup, mockProcessData);
      const statistics = await simulation.runSimulation();

      expect(statistics).toBeDefined();
      // Statistics should be reset after warmup
      expect(statistics.entitiesProcessed).toBeGreaterThanOrEqual(0);
    });

    it('should handle zero warmup period', async () => {
      const configNoWarmup = {
        ...mockSimulationConfig,
        warmupPeriod: 0
      };

      const simulation = new DiscreteEventSimulation(configNoWarmup, mockProcessData);
      const statistics = await simulation.runSimulation();

      expect(statistics).toBeDefined();
      expect(statistics.entitiesProcessed).toBeGreaterThanOrEqual(0);
    });
  });

  describe('entity processing', () => {
    it('should generate entities based on arrival rate', async () => {
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      // Should process some entities based on throughput
      const expectedEntities = mockProcessData.metrics.throughput * mockSimulationConfig.duration;
      expect(statistics.entitiesProcessed).toBeGreaterThan(0);
      expect(statistics.entitiesProcessed).toBeLessThan(expectedEntities * 2); // Allow for variability
    });

    it('should respect step dependencies', async () => {
      // This is tested indirectly through successful simulation completion
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      expect(statistics).toBeDefined();
      // If simulation completes without error, dependencies are respected
    });

    it('should handle resource constraints', async () => {
      // Create process with limited resources
      const constrainedProcess = {
        ...mockProcessData,
        resources: mockProcessData.resources.map(resource => ({
          ...resource,
          capacity: 1 // Very limited capacity
        }))
      };

      const simulation = new DiscreteEventSimulation(mockSimulationConfig, constrainedProcess);
      const statistics = await simulation.runSimulation();

      expect(statistics).toBeDefined();
      expect(statistics.totalWaitTime).toBeGreaterThan(0); // Should have wait times due to constraints
    });
  });

  describe('random number generation', () => {
    it('should produce consistent results with same seed', async () => {
      const config1 = { ...mockSimulationConfig, randomSeed: 12345 };
      const config2 = { ...mockSimulationConfig, randomSeed: 12345 };

      const simulation1 = new DiscreteEventSimulation(config1, mockProcessData);
      const simulation2 = new DiscreteEventSimulation(config2, mockProcessData);

      const stats1 = await simulation1.runSimulation();
      const stats2 = await simulation2.runSimulation();

      // Results should be similar (allowing for small floating-point differences)
      expect(Math.abs(stats1.entitiesProcessed - stats2.entitiesProcessed)).toBeLessThanOrEqual(1);
    });

    it('should produce different results with different seeds', async () => {
      const config1 = { ...mockSimulationConfig, randomSeed: 12345 };
      const config2 = { ...mockSimulationConfig, randomSeed: 54321 };

      const simulation1 = new DiscreteEventSimulation(config1, mockProcessData);
      const simulation2 = new DiscreteEventSimulation(config2, mockProcessData);

      const stats1 = await simulation1.runSimulation();
      const stats2 = await simulation2.runSimulation();

      // Results should be different (with high probability)
      const totalDifference = Math.abs(stats1.entitiesProcessed - stats2.entitiesProcessed) +
                             Math.abs(stats1.totalWaitTime - stats2.totalWaitTime);
      expect(totalDifference).toBeGreaterThan(0);
    });
  });

  describe('simulation duration', () => {
    it('should respect simulation duration', async () => {
      const shortConfig = {
        ...mockSimulationConfig,
        duration: 0.5 // 30 minutes
      };

      const longConfig = {
        ...mockSimulationConfig,
        duration: 4 // 4 hours
      };

      const shortSimulation = new DiscreteEventSimulation(shortConfig, mockProcessData);
      const longSimulation = new DiscreteEventSimulation(longConfig, mockProcessData);

      const shortStats = await shortSimulation.runSimulation();
      const longStats = await longSimulation.runSimulation();

      // Longer simulation should process more entities
      expect(longStats.entitiesProcessed).toBeGreaterThan(shortStats.entitiesProcessed);
    });

    it('should handle very short simulations', async () => {
      const veryShortConfig = {
        ...mockSimulationConfig,
        duration: 0.1, // 6 minutes
        warmupPeriod: 0
      };

      const simulation = new DiscreteEventSimulation(veryShortConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      expect(statistics).toBeDefined();
      expect(statistics.entitiesProcessed).toBeGreaterThanOrEqual(0);
    });
  });

  describe('processing time variability', () => {
    it('should add variability to processing times', async () => {
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      if (statistics.entitiesProcessed > 0) {
        const avgProcessingTime = statistics.totalProcessingTime / statistics.entitiesProcessed;
        const expectedTime = mockProcessData.steps.reduce((sum, step) => sum + step.duration, 0);
        
        // Processing time should be close to expected but with some variability
        expect(avgProcessingTime).toBeGreaterThan(expectedTime * 0.7);
        expect(avgProcessingTime).toBeLessThan(expectedTime * 1.3);
      }
    });
  });

  describe('error handling', () => {
    it('should handle invalid configuration gracefully', async () => {
      const invalidConfig = {
        ...mockSimulationConfig,
        duration: -1 // Invalid duration
      };

      const simulation = new DiscreteEventSimulation(invalidConfig, mockProcessData);
      
      await expect(simulation.runSimulation()).rejects.toThrow();
    });

    it('should handle empty process data', async () => {
      const emptyProcess = {
        ...mockProcessData,
        steps: [],
        resources: []
      };

      const simulation = new DiscreteEventSimulation(mockSimulationConfig, emptyProcess);
      
      // Should handle gracefully or throw appropriate error
      await expect(simulation.runSimulation()).rejects.toThrow();
    });

    it('should handle process with no dependencies', async () => {
      const noDepsProcess = {
        ...mockProcessData,
        steps: mockProcessData.steps.map(step => ({
          ...step,
          dependencies: []
        }))
      };

      const simulation = new DiscreteEventSimulation(mockSimulationConfig, noDepsProcess);
      const statistics = await simulation.runSimulation();

      expect(statistics).toBeDefined();
      expect(statistics.entitiesProcessed).toBeGreaterThanOrEqual(0);
    });
  });

  describe('performance', () => {
    it('should complete simulation within reasonable time', async () => {
      const startTime = Date.now();
      
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      await simulation.runSimulation();
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(5000); // 5 seconds
    });

    it('should handle longer simulations efficiently', async () => {
      const longConfig = {
        ...mockSimulationConfig,
        duration: 8, // 8 hours
        iterations: 1
      };

      const startTime = Date.now();
      
      const simulation = new DiscreteEventSimulation(longConfig, mockProcessData);
      await simulation.runSimulation();
      
      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(10000); // 10 seconds
    });
  });

  describe('statistical accuracy', () => {
    it('should produce reasonable throughput estimates', async () => {
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      if (statistics.entitiesProcessed > 0) {
        const simulatedThroughput = statistics.entitiesProcessed / mockSimulationConfig.duration;
        const expectedThroughput = mockProcessData.metrics.throughput;

        // Simulated throughput should be reasonably close to expected
        expect(simulatedThroughput).toBeGreaterThan(expectedThroughput * 0.3);
        expect(simulatedThroughput).toBeLessThan(expectedThroughput * 3);
      }
    });

    it('should track quality metrics', async () => {
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      expect(statistics.qualityMetrics).toBeInstanceOf(Map);
      // Quality metrics tracking is implemented in the simulation
    });
  });

  describe('event queue management', () => {
    it('should process events in chronological order', async () => {
      // This is tested indirectly through successful simulation completion
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      expect(statistics).toBeDefined();
      // If simulation completes successfully, events were processed in order
    });

    it('should handle concurrent events correctly', async () => {
      // Create process with multiple parallel steps
      const parallelProcess = {
        ...mockProcessData,
        steps: [
          {
            ...mockProcessData.steps[0],
            capacity: 5 // High capacity for parallel processing
          },
          ...mockProcessData.steps.slice(1)
        ]
      };

      const simulation = new DiscreteEventSimulation(mockSimulationConfig, parallelProcess);
      const statistics = await simulation.runSimulation();

      expect(statistics).toBeDefined();
      expect(statistics.entitiesProcessed).toBeGreaterThanOrEqual(0);
    });
  });

  describe('resource management', () => {
    it('should track resource availability correctly', async () => {
      const simulation = new DiscreteEventSimulation(mockSimulationConfig, mockProcessData);
      const statistics = await simulation.runSimulation();

      // Resource utilization should not exceed 100%
      statistics.resourceUtilization.forEach((utilization, resourceId) => {
        expect(utilization).toBeLessThanOrEqual(1.0);
        expect(utilization).toBeGreaterThanOrEqual(0);
      });
    });

    it('should handle resource conflicts appropriately', async () => {
      // Create process with resource conflicts
      const conflictProcess = {
        ...mockProcessData,
        steps: mockProcessData.steps.map(step => ({
          ...step,
          resourceRequirements: [
            { resourceId: 'shared-resource', quantity: 1, duration: step.duration }
          ]
        })),
        resources: [
          {
            resourceId: 'shared-resource',
            name: 'Shared Resource',
            type: 'machine',
            capacity: 1, // Only one unit available
            currentLoad: 0.8,
            availability: [],
            costPerHour: 50
          }
        ]
      };

      const simulation = new DiscreteEventSimulation(mockSimulationConfig, conflictProcess);
      const statistics = await simulation.runSimulation();

      expect(statistics).toBeDefined();
      expect(statistics.totalWaitTime).toBeGreaterThan(0); // Should have wait times due to conflicts
    });
  });
});