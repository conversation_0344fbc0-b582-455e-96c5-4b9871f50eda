# Test der Suchfunktion für MATNR und Materialkurztext

## Testbeschreibung
Dieser Test überprüft die Suchfunktion in der TrommelrechnungComponent, die in der RadioComp-Komponente implementiert ist.

## Zu testende Funktionen
1. Suche nach MATNR (Materialnummer)
2. Suche nach Materialkurztext
3. Case-insensitive Filterung
4. Korrekte Anzeige der Suchergebnisse

## Verfügbare Testdaten (Beispiele)
- MATNR: "0010000", Materialkurztext: "ÖLFLEX CLASSIC 100 2X0,5"
- MATNR: "00100004", Materialkurztext: "ÖLFLEX CLASSIC 100 300/500V 2X0,5"
- MATNR: "0010001", Materialkurztext: "ÖLFLEX CLASSIC 100 3G0,5"
- MATNR: "00100014", Materialkurztext: "ÖLFLEX CLASSIC 100 300/500V 3G0,5"
- MATNR: "00100023", Materialkurztext: "ÖLFLEX CLASSIC 100 4G0,5"

## Testfälle

### Test 1: Suche nach vollständiger MATNR
- **Eingabe:** "0010000"
- **Erwartetes Ergebnis:** Exakte Übereinstimmung "0010000 - ÖLFLEX CLASSIC 100 2X0,5" wird angezeigt
- **Status:** [ ] Bestanden [ ] Fehlgeschlagen

### Test 2: Suche nach Teilstring der MATNR
- **Eingabe:** "001000" (erste 6 Zeichen)
- **Erwartetes Ergebnis:** Alle Materialien mit MATNR beginnend mit "001000" werden angezeigt
- **Status:** [ ] Bestanden [ ] Fehlgeschlagen

### Test 3: Suche nach Materialkurztext
- **Eingabe:** "ÖLFLEX"
- **Erwartetes Ergebnis:** Alle Materialien mit "ÖLFLEX" im Kurztext werden angezeigt
- **Status:** [ ] Bestanden [ ] Fehlgeschlagen

### Test 4: Suche nach spezifischem Materialkurztext
- **Eingabe:** "CLASSIC 100 2X0,5"
- **Erwartetes Ergebnis:** Materialien mit diesem spezifischen Text werden angezeigt
- **Status:** [ ] Bestanden [ ] Fehlgeschlagen

### Test 5: Case-insensitive Suche
- **Eingabe:** "ölflex" (kleingeschrieben)
- **Erwartetes Ergebnis:** Ergebnisse unabhängig von der Schreibweise (gleich wie "ÖLFLEX")
- **Status:** [ ] Bestanden [ ] Fehlgeschlagen

### Test 6: Ungültiger Suchbegriff
- **Eingabe:** "XYZNICHTVORHANDEN"
- **Erwartetes Ergebnis:** "Kein Material gefunden" Meldung
- **Status:** [ ] Bestanden [ ] Fehlgeschlagen

### Test 7: Materialauswahl
- **Eingabe:** Auswahl von "0010000 - ÖLFLEX CLASSIC 100 2X0,5" aus den Suchergebnissen
- **Erwartetes Ergebnis:** Material wird ausgewählt, Popover schließt sich, Kabeldaten werden automatisch befüllt
- **Status:** [ ] Bestanden [ ] Fehlgeschlagen

## Durchführung
1. Navigiere zur Trommelrechnung-Seite
2. Wähle "Aus Stammdaten" Option
3. Klicke auf "Kabelmaterial auswählen..."
4. Führe die oben genannten Testfälle durch
5. Dokumentiere die Ergebnisse

## Ergebnisse
(Hier werden die Testergebnisse dokumentiert)

## Code-Referenz
Die Suchfunktion ist in `src/components/RadioComp.tsx` implementiert:
- Zeile 115-117: Filterlogik für MATNR und Materialkurztext
- Zeile 109: CommandInput für Sucheingabe
- Zeile 111: CommandEmpty für "Kein Material gefunden"