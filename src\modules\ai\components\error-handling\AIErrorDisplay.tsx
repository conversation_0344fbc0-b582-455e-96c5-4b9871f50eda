/**
 * AI Error Display Component
 * Displays user-friendly error messages for AI operations
 */

import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  XCircle, 
  RefreshCw, 
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { AIServiceError, AIServiceErrorSeverity } from '../../types/errors';
import { formatErrorForUser } from '../../utils/errorMessages';
import { useState } from 'react';

interface AIErrorDisplayProps {
  error: AIServiceError | Error | string;
  context?: string;
  showRetry?: boolean;
  showDetails?: boolean;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
}

export function AIErrorDisplay({
  error,
  context,
  showRetry = false,
  showDetails = false,
  onRetry,
  onDismiss,
  className = ''
}: AIErrorDisplayProps) {
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(false);

  // Parse error information
  const errorInfo = parseError(error);
  const userMessage = formatErrorForUser(error, context);

  const getSeverityIcon = (severity: AIServiceErrorSeverity) => {
    switch (severity) {
      case AIServiceErrorSeverity.CRITICAL:
        return <XCircle className="h-4 w-4" />;
      case AIServiceErrorSeverity.HIGH:
        return <AlertTriangle className="h-4 w-4" />;
      case AIServiceErrorSeverity.MEDIUM:
        return <AlertCircle className="h-4 w-4" />;
      case AIServiceErrorSeverity.LOW:
        return <Info className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getSeverityVariant = (severity: AIServiceErrorSeverity): "default" | "destructive" => {
    return severity === AIServiceErrorSeverity.CRITICAL || severity === AIServiceErrorSeverity.HIGH
      ? "destructive"
      : "default";
  };

  const getSeverityColor = (severity: AIServiceErrorSeverity) => {
    switch (severity) {
      case AIServiceErrorSeverity.CRITICAL:
        return 'text-red-600 bg-red-50 border-red-200';
      case AIServiceErrorSeverity.HIGH:
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case AIServiceErrorSeverity.MEDIUM:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case AIServiceErrorSeverity.LOW:
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <Card className={`${getSeverityColor(errorInfo.severity)} ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getSeverityIcon(errorInfo.severity)}
            <span className="text-sm font-medium">
              {context ? `${context} - Fehler` : 'KI-Service Fehler'}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={getSeverityVariant(errorInfo.severity)} className="text-xs">
              {getSeverityLabel(errorInfo.severity)}
            </Badge>
            {onDismiss && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDismiss}
                className="h-6 w-6 p-0"
              >
                <XCircle className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-3">
        <Alert variant={getSeverityVariant(errorInfo.severity)}>
          {getSeverityIcon(errorInfo.severity)}
          <AlertDescription className="text-sm">
            {userMessage}
          </AlertDescription>
        </Alert>

        {errorInfo.recoverable && (
          <div className="text-xs text-gray-600 bg-blue-50 p-2 rounded border border-blue-200">
            <Info className="h-3 w-3 inline mr-1" />
            Dieser Fehler kann möglicherweise automatisch behoben werden.
          </div>
        )}

        {showDetails && errorInfo.isAIError && (
          <div className="space-y-2">
            <div className="text-xs space-y-1">
              <div><strong>Service:</strong> {errorInfo.service}</div>
              <div><strong>Operation:</strong> {errorInfo.operation}</div>
              <div><strong>Zeit:</strong> {errorInfo.timestamp.toLocaleString('de-DE')}</div>
              {errorInfo.code && (
                <div><strong>Fehlercode:</strong> {errorInfo.code}</div>
              )}
            </div>

            {(showTechnicalDetails || process.env.NODE_ENV === 'development') && (
              <div className="space-y-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowTechnicalDetails(!showTechnicalDetails)}
                  className="h-6 text-xs p-1"
                >
                  {showTechnicalDetails ? (
                    <>
                      <ChevronUp className="h-3 w-3 mr-1" />
                      Technische Details ausblenden
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-3 w-3 mr-1" />
                      Technische Details anzeigen
                    </>
                  )}
                </Button>

                {showTechnicalDetails && (
                  <div className="text-xs bg-gray-100 p-2 rounded border">
                    <div className="font-medium mb-1">Technische Informationen:</div>
                    <pre className="whitespace-pre-wrap text-xs">
                      {errorInfo.technicalMessage}
                      {errorInfo.originalError && (
                        '\n\nOriginal Error:\n' + errorInfo.originalError.message
                      )}
                      {errorInfo.context && (
                        '\n\nContext:\n' + JSON.stringify(errorInfo.context, null, 2)
                      )}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {showRetry && onRetry && (
          <div className="flex justify-end">
            <Button
              onClick={onRetry}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Erneut versuchen
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function parseError(error: AIServiceError | Error | string) {
  if (typeof error === 'string') {
    return {
      isAIError: false,
      severity: AIServiceErrorSeverity.MEDIUM,
      service: 'Unknown',
      operation: 'Unknown',
      timestamp: new Date(),
      code: undefined,
      technicalMessage: error,
      originalError: undefined,
      context: undefined,
      recoverable: false
    };
  }

  if ('code' in error && 'severity' in error) {
    // It's an AIServiceError
    return {
      isAIError: true,
      severity: error.severity,
      service: error.service,
      operation: error.operation,
      timestamp: error.timestamp,
      code: error.code,
      technicalMessage: error.technicalMessage,
      originalError: error.originalError,
      context: error.context,
      recoverable: error.recoverable
    };
  }

  // It's a regular Error
  return {
    isAIError: false,
    severity: AIServiceErrorSeverity.MEDIUM,
    service: 'Unknown',
    operation: 'Unknown',
    timestamp: new Date(),
    code: undefined,
    technicalMessage: error.message,
    originalError: error,
    context: undefined,
    recoverable: true
  };
}

function getSeverityLabel(severity: AIServiceErrorSeverity): string {
  switch (severity) {
    case AIServiceErrorSeverity.CRITICAL:
      return 'Kritisch';
    case AIServiceErrorSeverity.HIGH:
      return 'Hoch';
    case AIServiceErrorSeverity.MEDIUM:
      return 'Mittel';
    case AIServiceErrorSeverity.LOW:
      return 'Niedrig';
    default:
      return 'Unbekannt';
  }
}

/**
 * Simplified error display for inline use
 */
export function AIErrorInline({ 
  error, 
  context, 
  className = '' 
}: { 
  error: AIServiceError | Error | string; 
  context?: string; 
  className?: string; 
}) {
  const userMessage = formatErrorForUser(error, context);
  
  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="text-sm">
        {userMessage}
      </AlertDescription>
    </Alert>
  );
}