import React, { useState } from "react";
import { DateRange } from "react-day-picker";
import { AtrlDataChart } from "@/modules/dashboard/components/charts/AtrlDataChart";
import { Lagerauslastung200Chart } from "@/modules/dashboard/components/charts/Lagerauslastung200Chart";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Drum } from "lucide-react";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import drumIcon from "@/assets/iconATRL.png";
import { AskJaszButton } from "@/modules/ai/components";
import { createPageContext } from "@/contexts/AskJaszContext";

/**
 * ATrL-Bereich Dashboard
 * 
 * Zeigt verschiedene Kennzahlen für den ATrL-Bereich an:
 * - ATrL-Daten als kombiniertes Diagramm mit Balken und Linien
 * - Umlagerungen, Ein- und Auslagerungen
 * - Lagerauslastung der AAA, BBB und CCC Artikel
 */
export default function AtrlPage() {
  // Zustand für den ausgewählten Datumsbereich - Standard: letzten 30 Tage (!NICHT ÄNDERN!)
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(2025, 5, 1), // 15. April 2025
    to: new Date(2025, 5, 30), // 7. Mai 2025 (alle verfügbaren Daten)
  });

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift und Date Range Picker */}
      <div className="mb-2 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <img
            src={drumIcon}
            alt="Automatisches Trommellager"
            className="h-10 w-10 mr-2 object-contain"
          />
          <h1 className="text-4xl font-bold text-black">AUTOMATISCHES TROMMELLAGER</h1>
          <div className="-mt-7">
            <AskJaszButton
              context={createPageContext(
                "ATrL Dashboard",
                [
                  "Automatisches Trommellager Übersicht",
                  "Umlagerungen, Ein- und Auslagerungen",
                  "Lagerauslastung AAA, BBB und CCC Artikel",
                  "Lagerbewegungen und Kapazitätsanalyse"
                ],
                "ATrL Bereich Performance Dashboard"
              )}
              position="inline"
              size="md"
              variant="default"
              tooltip="Hilfe zum ATrL Dashboard erhalten"
            />
          </div>
        </div>
        <DateRangePicker
          value={dateRange}
          onChange={setDateRange}
          label="Datumsauswahl"
        />
      </div>
      
      {/* Main Metrics */}
      <div className="mb-6">
        <div className="grid grid-cols-1 gap-2">
          {/* ATrL-Daten Chart */}
          <div className="xl:col-span-1">
            <ChartErrorBoundary>
              <AtrlDataChart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
          
          {/* Lagerauslastung Chart */}
          <div className="xl:col-span-1 mt-2">
            <ChartErrorBoundary>
              <Lagerauslastung200Chart dateRange={dateRange} />
            </ChartErrorBoundary>
          </div>
        </div>
      </div>
    </div>
  );
} 