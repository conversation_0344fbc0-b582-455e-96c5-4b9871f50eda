import React from 'react';
import styled from 'styled-components';

interface SwitchProps {
    checked?: boolean;
    onChange?: (checked: boolean) => void;
    disabled?: boolean;
    className?: string;
}

const Switch = ({ checked = false, onChange, disabled = false, className }: SwitchProps) => {
    const uniqueId = React.useMemo(() => `switch-${Math.random().toString(36).substr(2, 9)}`, []);

    const handleChange = () => {
        if (!disabled && onChange) {
            onChange(!checked);
        }
    };

    return (
        <StyledWrapper className={className}>
            <div className="container">
                <input
                    type="checkbox"
                    id={uniqueId}
                    checked={checked}
                    onChange={handleChange}
                    disabled={disabled}
                />
                <label htmlFor={uniqueId} className="button">
                    <span className="icon">
                        <svg xmlSpace="preserve" viewBox="0 0 30.143 30.143" xmlnsXlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" id="Capa_1" version="1.1" width="200px" height="200px">
                            <g strokeWidth={0} id="SVGRepo_bgCarrier" />
                            <g strokeLinejoin="round" strokeLinecap="round" id="SVGRepo_tracerCarrier" />
                            <g id="SVGRepo_iconCarrier">
                                <g>
                                    <path d="M20.034,2.357v3.824c3.482,1.798,5.869,5.427,5.869,9.619c0,5.98-4.848,10.83-10.828,10.83 c-5.982,0-10.832-4.85-10.832-10.83c0-3.844,2.012-7.215,5.029-9.136V2.689C4.245,4.918,0.731,9.945,0.731,15.801 c0,7.921,6.42,14.342,14.34,14.342c7.924,0,14.342-6.421,14.342-14.342C29.412,9.624,25.501,4.379,20.034,2.357z" />
                                    <path d="M14.795,17.652c1.576,0,1.736-0.931,1.736-2.076V2.08c0-1.148-0.16-2.08-1.736-2.08 c-1.57,0-1.732,0.932-1.732,2.08v13.496C13.062,16.722,13.225,17.652,14.795,17.652z" />
                                </g>
                            </g>
                        </svg>
                    </span>
                </label>
            </div>
        </StyledWrapper>
    );
}

const StyledWrapper = styled.div`
  .container {
    width: 7em;
    height: 7em;
    position: relative;
  }

  .button {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 4px solid #444245;
    background-color: transparent;
    background-image: linear-gradient(145deg, #444245, #FFFFFF);
    box-sizing: border-box;
    box-shadow: inset 2px 2px 0 #FFFFFF, inset -2px -2px 0px #444245;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .container input {
    display: none;
  }

  .button::before {
    position: absolute;
    content: "";
    width: 7.25em;
    height: 7.25em;
    border-radius: inherit;
    background-color: transparent;
    background-image: linear-gradient(145deg, #606060, #FFFFFF);
    z-index: -1;
    box-shadow: 11px 11px 22px #525252, -11px -11px 22px #FFFFFF;
  }

  .button .icon {
    width: 60px;
    height: 60px;
    display: inline-block;
  }

  .button .icon svg {
    height: 100%;
    width: 100%;
    fill: #444245;
    transition: fill 0.3s ease;
  }

  .container input:checked + .button {
    box-shadow: inset -2px -2px 0 #FFFFFF, inset 2px 2px 0 #5e5e5e;
    border: 4px solid #22c55e;
    box-shadow: 
      0px 0px 1px #22c55e inset,
      0px 0px 2px #22c55e inset, 
      0px 0px 10px #22c55e inset,
      0px 0px 40px #22c55e, 
      0px 0px 100px #22c55e,
      0px 0px 5px #22c55e;
  }

  .container input:checked + .button .icon svg {
    fill: #22c55e;
    filter: drop-shadow(0px 0px 10px #22c55e);
  }`;

export default Switch;
