"use client"

import { useEffect, useId, useRef, useState, useCallback } from "react"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CheckCircle, Search, Loader2 } from "lucide-react"
import TrommelrechnungService from "@/modules/ai/services/cutting/TrommelrechnungService"

// Props Interface für die RadioComp-Komponente
interface RadioCompProps {
  value?: string;
  onChange?: (value: string) => void;
  // Props für Materialauswahl
  verfügbareMaterialien?: any[];
  selectedMaterial?: string;
  onMaterialChange?: (value: string) => void;
  materialSuchOpen?: boolean;
  onMaterialSuchOpenChange?: (open: boolean) => void;
  materialSuchWert?: string;
  onMaterialSuchWertChange?: (value: string) => void;
  onMaterialSelect?: (material: any) => void;
}

export default function Component({ 
  value = "without-expansion", 
  onChange,
  verfügbareMaterialien = [],
  selectedMaterial = "",
  onMaterialChange,
  materialSuchOpen = false,
  onMaterialSuchOpenChange,
  materialSuchWert = "",
  onMaterialSuchWertChange,
  onMaterialSelect
}: RadioCompProps) {
  // State für dynamische Suche
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchTotal, setSearchTotal] = useState(0);
  
  // Debounced Search - Suche erst nach 300ms ohne weitere Eingabe
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  
  // Dynamische Suche implementieren
  const performSearch = useCallback(async (searchTerm: string) => {
    if (searchTerm.length < 2) {
      // Bei weniger als 2 Zeichen keine Suche durchführen
      setSearchResults([]);
      setSearchTotal(0);
      return;
    }
    
    setIsSearching(true);
    try {
      const { materials, total } = await TrommelrechnungService.searchKabelmaterialien(searchTerm, 100);
      setSearchResults(materials);
      setSearchTotal(total);
    } catch (error) {
      console.error('Fehler bei der Materialsuche:', error);
      setSearchResults([]);
      setSearchTotal(0);
    } finally {
      setIsSearching(false);
    }
  }, []);
  
  // Debounced Search Handler
  const handleSearchChange = useCallback((value: string) => {
    onMaterialSuchWertChange?.(value);
    
    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    // Set new timeout for search
    const newTimeout = setTimeout(() => {
      performSearch(value);
    }, 300); // 300ms Debounce
    
    setSearchTimeout(newTimeout);
  }, [searchTimeout, onMaterialSuchWertChange, performSearch]);
  
  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);
  
  // Bestimme welche Materialien angezeigt werden sollen
  const materialsToShow = materialSuchWert.length >= 2 ? searchResults : verfügbareMaterialien.slice(0, 100);
  
  // Verwende die dynamischen Suchergebnisse oder die ersten 100 Materialien
  const displayedMaterials = materialsToShow;
  const hasMoreResults = materialSuchWert.length >= 2 ? searchTotal > 100 : verfügbareMaterialien.length > 100;
  const totalResults = materialSuchWert.length >= 2 ? searchTotal : verfügbareMaterialien.length;
  const radioId = useId()
  const inputId = useId()
  const inputRef = useRef<HTMLInputElement>(null)

  // Verwende die übergebenen Props anstelle des lokalen State
  const selectedValue = value;
  const setSelectedValue = (newValue: string) => {
    onChange?.(newValue);
  };

  const handleValueChange = (value: string) => {
    setSelectedValue(value);
    onChange?.(value);
  };

  useEffect(() => {
    if (selectedValue === "with-expansion" && inputRef.current) {
      inputRef.current.focus()
    }
  }, [selectedValue])

  return (
    <div className="w-full">
      <RadioGroup value={selectedValue} onValueChange={handleValueChange} className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Linke Seite: Aus Stammdaten */}
        <div className="relative">
          <div 
            className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
              selectedValue === "with-expansion" 
                ? "border-primary bg-white shadow-sm" 
                : "border-border hover:border-primary/50"
            }`}
            onClick={() => handleValueChange("with-expansion")}
          >
            <div className="flex items-center space-x-2 mb-2">
              <RadioGroupItem value="with-expansion" id={`${radioId}-with-expansion`} />
              <Label htmlFor={`${radioId}-with-expansion`} className="text-sm font-medium cursor-pointer">
                Aus Stammdaten
              </Label>
            </div>
            <div className="text-xs text-muted-foreground mb-3">
              Kabeldaten aus den Stammdaten laden.
            </div>

            {/* Materialliste - immer sichtbar */}
            <div className="mt-3">
              <div>
                <div className="pointer-events-none -m-2 overflow-hidden p-2">
                   <div className="pointer-events-auto">
                      <Popover open={materialSuchOpen} onOpenChange={onMaterialSuchOpenChange}>
                        <PopoverTrigger asChild>
                          <Button
                            // Remove ref since Button component expects HTMLButtonElement but inputRef is for HTMLInputElement
                            variant="outline"
                            role="combobox"
                            aria-expanded={materialSuchOpen}
                            className="w-full justify-between"
                          >
                            {selectedMaterial && verfügbareMaterialien.length > 0
                              ? (() => {
                                  const material = verfügbareMaterialien.find((m) => m.MATNR === selectedMaterial);
                                  return material ? `${material.MATNR} - ${material.Materialkurztext || 'Kein Text'} (Ø ${material.Kabeldurchmesser}mm)` : "Kabelmaterial auswählen...";
                                })()
                              : "Kabelmaterial auswählen..."}
                            <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0">
                          <Command>
                            <CommandInput 
                                 placeholder="Nach MATNR oder Materialkurztext suchen... (min. 2 Zeichen)"
                                 value={materialSuchWert}
                                 onValueChange={handleSearchChange}
                               />
                            <CommandEmpty>Kein Material gefunden.</CommandEmpty>
                            <CommandList>
                              <CommandGroup>
                                {isSearching && (
                                  <div className="flex items-center justify-center py-4">
                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                    <span className="text-sm text-muted-foreground">Suche läuft...</span>
                                  </div>
                                )}
                                {!isSearching && materialsToShow.map((material) => (
                                  <CommandItem
                                    key={material.MATNR}
                                    value={material.MATNR}
                                    onSelect={(currentValue) => {
                                      onMaterialChange?.(currentValue === selectedMaterial ? "" : currentValue);
                                      const selectedMat = materialsToShow.find(m => m.MATNR === currentValue);
                                      if (selectedMat && onMaterialSelect) {
                                        onMaterialSelect(selectedMat);
                                      }
                                      onMaterialSuchOpenChange?.(false);
                                      onMaterialSuchWertChange?.("");
                                    }}
                                  >
                                    <CheckCircle
                                      className={`mr-2 h-4 w-4 ${
                                        material.MATNR === selectedMaterial ? "opacity-100" : "opacity-0"
                                      }`}
                                    />
                                    <div className="flex flex-col">
                                      <span className="font-medium">{material.MATNR} - {material.Materialkurztext || 'Kein Text'}</span>
                                      <span className="text-sm text-gray-500">Ø {material.Kabeldurchmesser}mm</span>
                                    </div>
                                  </CommandItem>
                                ))}
                                {/* Zeige Hinweis für dynamische Suche oder lokale Filterung */}
                                {!isSearching && materialSuchWert.length >= 2 && searchTotal > 100 && (
                                  <div className="px-2 py-1 text-xs text-gray-500 border-t">
                                    {searchTotal} Ergebnisse gefunden. Nur die ersten 100 werden angezeigt.
                                  </div>
                                )}
                                {!isSearching && materialSuchWert.length < 2 && verfügbareMaterialien.length > 100 && (
                                  <div className="px-2 py-1 text-xs text-gray-500 border-t">
                                    Nur die ersten 100 Materialien werden angezeigt. Verwenden Sie die Suche für spezifischere Ergebnisse.
                                  </div>
                                )}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </div>
                 </div>
               </div>
             </div>
           </div>
         </div>

        {/* Rechte Seite: Individuelle Eingabe */}
        <div 
          className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
            selectedValue === "without-expansion" 
              ? "border-primary bg-white shadow-sm" 
              : "border-border hover:border-primary/50"
          }`}
          onClick={() => handleValueChange("without-expansion")}
        >
          <div className="flex items-center space-x-2 mb-2">
            <RadioGroupItem value="without-expansion" id={`${radioId}-without-expansion`} />
            <Label htmlFor={`${radioId}-without-expansion`} className="text-sm font-medium cursor-pointer">
              Individuelle Eingabe
            </Label>
          </div>
          <div className="text-xs text-muted-foreground">
            Ermöglicht eine manuelle Eingabe der Kabeldaten.
          </div>
        </div>
      </RadioGroup>
    </div>
  )
}
