"use strict";
/**
 * Performance Tests for AI Chatbot Database Integration
 *
 * Focused performance tests to ensure response times meet requirements:
 * - Basic chat responses within 2 seconds
 * - Enhanced chat with database integration within 3 seconds
 * - Concurrent request handling
 * - Load testing scenarios
 *
 * Requirements: 5.5
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const chat_routes_1 = __importDefault(require("../routes/chat.routes"));
const openrouter_service_1 = __importDefault(require("../services/openrouter.service"));
// Mock external dependencies
jest.mock('../services/openrouter.service');
jest.mock('@prisma/client');
describe('Chat Performance Tests', () => {
    let app;
    let mockOpenRouterService;
    beforeEach(() => {
        jest.clearAllMocks();
        // Setup Express app
        app = (0, express_1.default)();
        app.use(express_1.default.json());
        app.use('/api/chat', chat_routes_1.default);
        // Setup mocks with realistic response times
        mockOpenRouterService = openrouter_service_1.default;
        // Mock with variable response times to simulate real conditions
        mockOpenRouterService.generateResponse.mockImplementation(() => new Promise(resolve => {
            const responseTime = Math.random() * 1000 + 500; // 500-1500ms
            setTimeout(() => {
                resolve({
                    response: 'Performance test response',
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: false,
                    hasAnomalies: false,
                    hasEnrichedContext: Math.random() > 0.5,
                    dataTypes: Math.random() > 0.5 ? ['stoerungen'] : []
                });
            }, responseTime);
        }));
    });
    describe('Response Time Requirements', () => {
        it('should respond to basic chat within 2 seconds', async () => {
            const measurements = [];
            const iterations = 5;
            for (let i = 0; i < iterations; i++) {
                const startTime = Date.now();
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat')
                    .send({
                    message: `Performance test ${i + 1}`
                })
                    .expect(200);
                const responseTime = Date.now() - startTime;
                measurements.push(responseTime);
                expect(response.body).toHaveProperty('response');
                expect(responseTime).toBeLessThan(2000);
            }
            const averageTime = measurements.reduce((a, b) => a + b, 0) / measurements.length;
            const maxTime = Math.max(...measurements);
            console.log(`Basic chat - Average: ${averageTime.toFixed(0)}ms, Max: ${maxTime}ms`);
            expect(averageTime).toBeLessThan(1500); // Should be well under 2 seconds on average
        });
        it('should respond to enhanced chat within 3 seconds', async () => {
            const measurements = [];
            const iterations = 5;
            for (let i = 0; i < iterations; i++) {
                const startTime = Date.now();
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: `Enhanced performance test ${i + 1}: Störungen Status`
                })
                    .expect(200);
                const responseTime = Date.now() - startTime;
                measurements.push(responseTime);
                expect(response.body).toHaveProperty('response');
                expect(responseTime).toBeLessThan(3000);
            }
            const averageTime = measurements.reduce((a, b) => a + b, 0) / measurements.length;
            const maxTime = Math.max(...measurements);
            console.log(`Enhanced chat - Average: ${averageTime.toFixed(0)}ms, Max: ${maxTime}ms`);
            expect(averageTime).toBeLessThan(2500); // Should be well under 3 seconds on average
        });
        it('should handle database-heavy queries within acceptable time', async () => {
            const databaseQueries = [
                'Wie viele Störungen haben wir aktuell?',
                'Zeige mir die Versand-Performance heute',
                'Welche Maschinen sind am effizientesten?',
                'Gib mir eine Gesamtübersicht aller Systeme',
                'Wie ist der Service Level dieser Woche?'
            ];
            const measurements = [];
            for (const query of databaseQueries) {
                const startTime = Date.now();
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({ message: query })
                    .expect(200);
                const responseTime = Date.now() - startTime;
                measurements.push(responseTime);
                expect(response.body).toHaveProperty('response');
                expect(responseTime).toBeLessThan(3000);
            }
            const averageTime = measurements.reduce((a, b) => a + b, 0) / measurements.length;
            const maxTime = Math.max(...measurements);
            console.log(`Database queries - Average: ${averageTime.toFixed(0)}ms, Max: ${maxTime}ms`);
            expect(averageTime).toBeLessThan(2500);
            expect(maxTime).toBeLessThan(3000);
        });
    });
    describe('Concurrent Request Handling', () => {
        it('should handle 5 concurrent basic requests efficiently', async () => {
            const startTime = Date.now();
            const requests = Array.from({ length: 5 }, (_, i) => (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({
                message: `Concurrent basic request ${i + 1}`
            }));
            const responses = await Promise.all(requests);
            const totalTime = Date.now() - startTime;
            // All requests should succeed
            responses.forEach((response, index) => {
                expect(response.status).toBe(200);
                expect(response.body).toHaveProperty('response');
            });
            // Total time should be reasonable for concurrent requests
            expect(totalTime).toBeLessThan(4000); // Should not take much longer than single request
            console.log(`5 concurrent basic requests completed in ${totalTime}ms`);
        });
        it('should handle 5 concurrent enhanced requests efficiently', async () => {
            const startTime = Date.now();
            const requests = Array.from({ length: 5 }, (_, i) => (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: `Concurrent enhanced request ${i + 1}: System Status`
            }));
            const responses = await Promise.all(requests);
            const totalTime = Date.now() - startTime;
            // All requests should succeed
            responses.forEach((response, index) => {
                expect(response.status).toBe(200);
                expect(response.body).toHaveProperty('response');
            });
            // Total time should be reasonable for concurrent requests
            expect(totalTime).toBeLessThan(5000); // Slightly longer for enhanced requests
            console.log(`5 concurrent enhanced requests completed in ${totalTime}ms`);
        });
        it('should handle mixed concurrent requests', async () => {
            const startTime = Date.now();
            const requests = [
                (0, supertest_1.default)(app).post('/api/chat').send({ message: 'Basic request 1' }),
                (0, supertest_1.default)(app).post('/api/chat/enhanced').send({ message: 'Enhanced request 1: Störungen' }),
                (0, supertest_1.default)(app).post('/api/chat').send({ message: 'Basic request 2' }),
                (0, supertest_1.default)(app).post('/api/chat/enhanced').send({ message: 'Enhanced request 2: Versand' }),
                (0, supertest_1.default)(app).post('/api/chat/enhanced').send({ message: 'Enhanced request 3: Ablängerei' })
            ];
            const responses = await Promise.all(requests);
            const totalTime = Date.now() - startTime;
            // All requests should succeed
            responses.forEach((response, index) => {
                expect(response.status).toBe(200);
                expect(response.body).toHaveProperty('response');
            });
            expect(totalTime).toBeLessThan(5000);
            console.log(`5 mixed concurrent requests completed in ${totalTime}ms`);
        });
    });
    describe('Load Testing', () => {
        it('should maintain performance under sequential load', async () => {
            const iterations = 10;
            const responseTimes = [];
            for (let i = 0; i < iterations; i++) {
                const startTime = Date.now();
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: `Load test iteration ${i + 1}: System overview`
                })
                    .expect(200);
                const responseTime = Date.now() - startTime;
                responseTimes.push(responseTime);
                expect(response.body).toHaveProperty('response');
            }
            const averageTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
            const maxTime = Math.max(...responseTimes);
            const minTime = Math.min(...responseTimes);
            const variance = responseTimes.reduce((acc, time) => acc + Math.pow(time - averageTime, 2), 0) / responseTimes.length;
            const stdDev = Math.sqrt(variance);
            console.log(`Load test results - Average: ${averageTime.toFixed(0)}ms, Min: ${minTime}ms, Max: ${maxTime}ms, StdDev: ${stdDev.toFixed(0)}ms`);
            // Performance should remain consistent
            expect(averageTime).toBeLessThan(3000);
            expect(maxTime).toBeLessThan(4000);
            expect(stdDev).toBeLessThan(1000); // Response times should be relatively consistent
        });
        it('should handle burst load efficiently', async () => {
            const burstSize = 8;
            const startTime = Date.now();
            // Create burst of requests
            const requests = Array.from({ length: burstSize }, (_, i) => (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: `Burst request ${i + 1}: Performance metrics`
            }));
            const responses = await Promise.all(requests);
            const totalTime = Date.now() - startTime;
            // All requests should succeed
            responses.forEach((response, index) => {
                expect(response.status).toBe(200);
                expect(response.body).toHaveProperty('response');
            });
            // Should handle burst efficiently
            expect(totalTime).toBeLessThan(6000); // 8 requests in under 6 seconds
            console.log(`Burst of ${burstSize} requests completed in ${totalTime}ms`);
            // Average time per request in burst should be reasonable
            const avgTimePerRequest = totalTime / burstSize;
            expect(avgTimePerRequest).toBeLessThan(1000); // Due to concurrency, should be faster per request
        });
    });
    describe('Performance Degradation Scenarios', () => {
        it('should handle slow OpenRouter responses gracefully', async () => {
            // Mock slower OpenRouter responses
            mockOpenRouterService.generateResponse.mockImplementation(() => new Promise(resolve => {
                setTimeout(() => {
                    resolve({
                        response: 'Slow response',
                        timestamp: new Date().toISOString(),
                        model: '@preset/lapp',
                        hasInsights: false,
                        hasAnomalies: false,
                        hasEnrichedContext: false,
                        dataTypes: []
                    });
                }, 2000); // 2 second delay
            }));
            const startTime = Date.now();
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Slow query test'
            })
                .expect(200);
            const responseTime = Date.now() - startTime;
            expect(response.body).toHaveProperty('response');
            expect(responseTime).toBeGreaterThan(2000); // Should reflect the delay
            expect(responseTime).toBeLessThan(3000); // But still within acceptable range
        });
        it('should maintain performance with data enrichment failures', async () => {
            // Mock data enrichment to fail, forcing fallback
            const mockDataEnrichment = jest.fn().mockRejectedValue(new Error('Database unavailable'));
            const measurements = [];
            const iterations = 3;
            for (let i = 0; i < iterations; i++) {
                const startTime = Date.now();
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: `Fallback performance test ${i + 1}`
                })
                    .expect(200);
                const responseTime = Date.now() - startTime;
                measurements.push(responseTime);
                expect(response.body).toHaveProperty('response');
                expect(response.body.dataEnrichmentError).toBe(true);
            }
            const averageTime = measurements.reduce((a, b) => a + b, 0) / measurements.length;
            // Fallback should still be fast
            expect(averageTime).toBeLessThan(2000);
            console.log(`Fallback performance - Average: ${averageTime.toFixed(0)}ms`);
        });
    });
    describe('Memory and Resource Usage', () => {
        it('should not leak memory during repeated requests', async () => {
            const initialMemory = process.memoryUsage();
            // Make many requests to test for memory leaks
            for (let i = 0; i < 20; i++) {
                await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: `Memory test ${i + 1}`
                })
                    .expect(200);
            }
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }
            const finalMemory = process.memoryUsage();
            const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
            console.log(`Memory usage - Initial: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)}MB, Final: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)}MB, Increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
            // Memory increase should be reasonable (less than 50MB for 20 requests)
            expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
        });
    });
    describe('Performance Monitoring Helpers', () => {
        it('should provide performance metrics in response', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Performance monitoring test'
            })
                .expect(200);
            expect(response.body).toHaveProperty('timestamp');
            // Check if enrichment details include timing information
            if (response.body.enrichmentDetails) {
                expect(response.body.enrichmentDetails).toHaveProperty('processingTime');
            }
        });
        it('should track request processing stages', async () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
            await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Stage tracking test'
            })
                .expect(200);
            // Should log different stages of processing
            expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('[CHAT-ENHANCED] Anfrage erhalten'));
            expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('[CHAT-ENHANCED] Antwort gesendet'));
            consoleSpy.mockRestore();
        });
    });
});
