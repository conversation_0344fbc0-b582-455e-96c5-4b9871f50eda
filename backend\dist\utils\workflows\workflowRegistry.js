"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.workflowRegistry = void 0;
exports.getWorkflowDef = getWorkflowDef;
exports.listWorkflows = listWorkflows;
const path_1 = __importDefault(require("path"));
exports.workflowRegistry = {
    bestand: {
        id: 'bestand',
        name: 'Bestand',
        scriptPath: path_1.default.join(process.cwd(), 'backend', 'scripts', 'workflows', 'Bestand', 'Bestand-Workflow.py'),
        env: {},
        logDir: path_1.default.join(process.cwd(), 'backend', 'workflows', 'logs'),
    },
    servicegrad: {
        id: 'servicegrad',
        name: 'Servicegrad',
        scriptPath: path_1.default.join(process.cwd(), 'backend', 'scripts', 'workflows', 'Servicegrad', 'Servicegrad_Workflow.py'),
        env: {},
        logDir: path_1.default.join(process.cwd(), 'backend', 'workflows', 'logs'),
    },
    rueckstandsliste: {
        id: 'rueckstandsliste',
        name: 'Rückstandsliste',
        scriptPath: path_1.default.join(process.cwd(), 'backend', 'scripts', 'workflows', 'Rueckstandsliste', 'Rueckstandsliste_Workflow.py'),
        env: {},
        logDir: path_1.default.join(process.cwd(), 'backend', 'workflows', 'logs'),
    },
    lx03_240: {
        id: 'lx03_240',
        name: 'LX03 Lagertyp 240',
        scriptPath: path_1.default.join(process.cwd(), 'backend', 'scripts', 'workflows', 'LX03', 'LX03_240_Workflow.py'),
        env: {},
        logDir: path_1.default.join(process.cwd(), 'backend', 'workflows', 'logs'),
    },
    lx03_200: {
        id: 'lx03_200',
        name: 'LX03 Lagertyp 200',
        scriptPath: path_1.default.join(process.cwd(), 'backend', 'scripts', 'workflows', 'LX03', 'LX03_200_Workflow.py'),
        env: {},
        logDir: path_1.default.join(process.cwd(), 'backend', 'workflows', 'logs'),
    },
    lx03_rest: {
        id: 'lx03_rest',
        name: 'LX03 Lagertyp Rest',
        scriptPath: path_1.default.join(process.cwd(), 'backend', 'scripts', 'workflows', 'LX03', 'LX03_Rest_Workflow.py'),
        env: {},
        logDir: path_1.default.join(process.cwd(), 'backend', 'workflows', 'logs'),
    },
};
function getWorkflowDef(id) {
    return exports.workflowRegistry[id];
}
function listWorkflows() {
    return Object.values(exports.workflowRegistry).map(w => ({ id: w.id, name: w.name }));
}
