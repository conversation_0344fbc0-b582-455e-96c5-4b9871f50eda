"use strict";
/**
 * Eingabe-Validierungs-Middleware
 *
 * Verwendet Zod für typsichere Validierung aller API-Anfragen
 * und schützt vor ungültigen Eingaben.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validators = exports.commonSchemas = void 0;
exports.createValidationMiddleware = createValidationMiddleware;
exports.sanitizationMiddleware = sanitizationMiddleware;
const zod_1 = require("zod");
/**
 * Erstellt ein Validierungs-Middleware für bestimmte Zod-Schemas
 *
 * @param schemas Objekt mit Schemas für body, query, params
 * @returns Express Middleware-Funktion
 */
function createValidationMiddleware(schemas) {
    return (req, res, next) => {
        try {
            const errors = [];
            // Validiere Request Body
            if (schemas.body) {
                try {
                    req.body = schemas.body.parse(req.body);
                }
                catch (error) {
                    if (error instanceof zod_1.ZodError) {
                        errors.push(...error.errors.map(e => `Body.${e.path.join('.')}: ${e.message}`));
                    }
                }
            }
            // Validiere Query Parameter
            if (schemas.query) {
                try {
                    req.query = schemas.query.parse(req.query);
                }
                catch (error) {
                    if (error instanceof zod_1.ZodError) {
                        errors.push(...error.errors.map(e => `Query.${e.path.join('.')}: ${e.message}`));
                    }
                }
            }
            // Validiere URL Parameter
            if (schemas.params) {
                try {
                    req.params = schemas.params.parse(req.params);
                }
                catch (error) {
                    if (error instanceof zod_1.ZodError) {
                        errors.push(...error.errors.map(e => `Params.${e.path.join('.')}: ${e.message}`));
                    }
                }
            }
            // Falls Validierungsfehler aufgetreten sind
            if (errors.length > 0) {
                console.warn(`[VALIDATION] Validierungsfehler für ${req.method} ${req.originalUrl}:`, errors);
                res.status(400).json({
                    success: false,
                    error: 'Eingabevalidierung fehlgeschlagen',
                    message: 'Die bereitgestellten Daten sind ungültig.',
                    details: errors,
                    code: 'VALIDATION_ERROR'
                });
                return;
            }
            // Validierung erfolgreich
            next();
        }
        catch (error) {
            console.error('[VALIDATION] Unerwarteter Fehler bei der Validierung:', error);
            res.status(500).json({
                success: false,
                error: 'Interner Validierungsfehler',
                message: 'Es ist ein Fehler bei der Eingabevalidierung aufgetreten.',
                code: 'VALIDATION_INTERNAL_ERROR'
            });
            return;
        }
    };
}
/**
 * Vorgefertigte Zod-Schemas für häufige Validierungen
 */
exports.commonSchemas = {
    /**
     * Schema für Datums-Query-Parameter
     */
    dateRangeQuery: zod_1.z.object({
        startDate: zod_1.z.string()
            .optional()
            .refine((date) => !date || /^\d{4}-\d{2}-\d{2}$/.test(date), 'Startdatum muss im Format YYYY-MM-DD sein'),
        endDate: zod_1.z.string()
            .optional()
            .refine((date) => !date || /^\d{4}-\d{2}-\d{2}$/.test(date), 'Enddatum muss im Format YYYY-MM-DD sein')
    }).refine((data) => {
        if (data.startDate && data.endDate) {
            return new Date(data.startDate) <= new Date(data.endDate);
        }
        return true;
    }, 'Startdatum muss vor dem Enddatum liegen'),
    /**
     * Schema für Paginierungs-Parameter
     */
    paginationQuery: zod_1.z.object({
        page: zod_1.z.string()
            .optional()
            .transform((val) => val ? parseInt(val, 10) : 1)
            .refine((num) => num > 0, 'Seitenzahl muss größer als 0 sein'),
        limit: zod_1.z.string()
            .optional()
            .transform((val) => val ? parseInt(val, 10) : 100)
            .refine((num) => num > 0 && num <= 1000, 'Limit muss zwischen 1 und 1000 liegen')
    }),
    /**
     * Schema für ID-Parameter
     */
    idParam: zod_1.z.object({
        id: zod_1.z.string()
            .transform((val) => parseInt(val, 10))
            .refine((num) => !isNaN(num) && num > 0, 'ID muss eine positive Zahl sein')
    }),
    /**
     * Schema für String-basierte IDs
     */
    stringIdParam: zod_1.z.object({
        id: zod_1.z.string()
            .min(1, 'ID darf nicht leer sein')
            .max(255, 'ID ist zu lang')
    }),
    /**
     * Schema für Maschinen-Effizienz-Query
     */
    maschinenEfficiencyQuery: zod_1.z.object({
        machine: zod_1.z.string().optional(),
        startDate: zod_1.z.string()
            .optional()
            .refine((date) => !date || /^\d{4}-\d{2}-\d{2}$/.test(date), 'Startdatum muss im Format YYYY-MM-DD sein'),
        endDate: zod_1.z.string()
            .optional()
            .refine((date) => !date || /^\d{4}-\d{2}-\d{2}$/.test(date), 'Enddatum muss im Format YYYY-MM-DD sein')
    }),
    /**
     * Schema für CSV-Import-Body
     */
    csvImportBody: zod_1.z.object({
        data: zod_1.z.array(zod_1.z.record(zod_1.z.string(), zod_1.z.any()))
            .min(1, 'CSV-Daten dürfen nicht leer sein')
            .max(10000, 'Zu viele Datensätze (Maximum: 10000)'),
        tableName: zod_1.z.string()
            .min(1, 'Tabellenname ist erforderlich')
            .regex(/^[a-zA-Z_][a-zA-Z0-9_]*$/, 'Ungültiger Tabellenname')
    }),
    /**
     * Schema für System-Stats-Query
     */
    systemStatsQuery: zod_1.z.object({
        startDate: zod_1.z.string()
            .optional()
            .refine((date) => !date || /^\d{4}-\d{2}-\d{2}$/.test(date), 'Startdatum muss im Format YYYY-MM-DD sein'),
        endDate: zod_1.z.string()
            .optional()
            .refine((date) => !date || /^\d{4}-\d{2}-\d{2}$/.test(date), 'Enddatum muss im Format YYYY-MM-DD sein'),
        includeDetails: zod_1.z.string()
            .optional()
            .transform((val) => val === 'true')
    })
};
/**
 * Middleware für allgemeine Request-Sanitization
 * Entfernt potentiell gefährliche Zeichen und normalisiert Eingaben
 */
function sanitizationMiddleware(req, res, next) {
    try {
        // Sanitize Query Parameters
        if (req.query) {
            for (const [key, value] of Object.entries(req.query)) {
                if (typeof value === 'string') {
                    // Entferne potentiell gefährliche Zeichen
                    req.query[key] = value
                        .replace(/[<>\"']/g, '') // XSS-Schutz
                        .replace(/;|\-\-|\/\*|\*\//g, '') // SQL-Injection-Schutz
                        .trim();
                }
            }
        }
        // Sanitize Body (nur für String-Werte)
        if (req.body && typeof req.body === 'object') {
            sanitizeObject(req.body);
        }
        next();
    }
    catch (error) {
        console.error('[SANITIZATION] Fehler bei der Eingabe-Sanitization:', error);
        next(); // Weiterleiten auch bei Fehlern, um die App nicht zu blockieren
    }
}
/**
 * Hilfsfunktion zur rekursiven Sanitization von Objekten
 * @param obj Zu sanitisierendes Objekt
 */
function sanitizeObject(obj) {
    for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'string') {
            obj[key] = value
                .replace(/[<>\"']/g, '') // XSS-Schutz
                .replace(/;|\-\-|\/\*|\*\//g, '') // SQL-Injection-Schutz
                .trim();
        }
        else if (typeof value === 'object' && value !== null) {
            sanitizeObject(value);
        }
    }
}
/**
 * Vorgefertigte Validierungs-Middleware für häufige Anwendungsfälle
 */
exports.validators = {
    /**
     * Validierung für Datumsbereich-Anfragen
     */
    dateRange: createValidationMiddleware({
        query: exports.commonSchemas.dateRangeQuery
    }),
    /**
     * Validierung für Paginierung
     */
    pagination: createValidationMiddleware({
        query: exports.commonSchemas.paginationQuery
    }),
    /**
     * Validierung für ID-Parameter
     */
    idParam: createValidationMiddleware({
        params: exports.commonSchemas.idParam
    }),
    /**
     * Validierung für Maschinen-Effizienz-Anfragen
     */
    maschinenEfficiency: createValidationMiddleware({
        query: exports.commonSchemas.maschinenEfficiencyQuery
    }),
    /**
     * Validierung für CSV-Import
     */
    csvImport: createValidationMiddleware({
        body: exports.commonSchemas.csvImportBody
    }),
    /**
     * Validierung für System-Stats
     */
    systemStats: createValidationMiddleware({
        query: exports.commonSchemas.systemStatsQuery
    })
};
