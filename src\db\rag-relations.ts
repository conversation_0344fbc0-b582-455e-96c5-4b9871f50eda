import { relations } from "drizzle-orm/relations";
import { knowledgeBases, documents, chunks, embeddings, similarityResults, ragQueries, categories, documentCategories, chunkMetadata } from "./rag-schema";

export const documentsRelations = relations(documents, ({one, many}) => ({
	knowledgeBase: one(knowledgeBases, {
		fields: [documents.knowledgeBaseId],
		references: [knowledgeBases.id]
	}),
	chunks: many(chunks),
	documentCategories: many(documentCategories),
}));

export const knowledgeBasesRelations = relations(knowledgeBases, ({many}) => ({
	documents: many(documents),
}));

export const chunksRelations = relations(chunks, ({one, many}) => ({
	document: one(documents, {
		fields: [chunks.documentId],
		references: [documents.id]
	}),
	embeddings: many(embeddings),
	similarityResults: many(similarityResults),
	chunkMetadata: many(chunkMetadata),
}));

export const embeddingsRelations = relations(embeddings, ({one}) => ({
	chunk: one(chunks, {
		fields: [embeddings.chunkId],
		references: [chunks.id]
	}),
}));

export const similarityResultsRelations = relations(similarityResults, ({one}) => ({
	chunk: one(chunks, {
		fields: [similarityResults.chunkId],
		references: [chunks.id]
	}),
	ragQuery: one(ragQueries, {
		fields: [similarityResults.queryId],
		references: [ragQueries.id]
	}),
}));

export const ragQueriesRelations = relations(ragQueries, ({many}) => ({
	similarityResults: many(similarityResults),
}));

export const categoriesRelations = relations(categories, ({one, many}) => ({
	category: one(categories, {
		fields: [categories.parentId],
		references: [categories.id],
		relationName: "categories_parentId_categories_id"
	}),
	categories: many(categories, {
		relationName: "categories_parentId_categories_id"
	}),
	documentCategories: many(documentCategories),
}));

export const documentCategoriesRelations = relations(documentCategories, ({one}) => ({
	category: one(categories, {
		fields: [documentCategories.categoryId],
		references: [categories.id]
	}),
	document: one(documents, {
		fields: [documentCategories.documentId],
		references: [documents.id]
	}),
}));

export const chunkMetadataRelations = relations(chunkMetadata, ({one}) => ({
	chunk: one(chunks, {
		fields: [chunkMetadata.chunkId],
		references: [chunks.id]
	}),
}));