# KPI Components - Dokumentation

## Übersicht
Die KPI Components sind interaktive Kartenelemente, die wichtige Leistungskennzahlen (Key Performance Indicators) in visuell ansprechender Form darstellen. Jede KPI-Karte enthält einen Ask JASZ Button für kontextspezifische Erklärungen und Hilfestellungen.

## Implementierte KPI-Karten

### 1. Störungsstatistiken KPIs

#### Gesamt Störungen
- **Zweck**: Zeigt die Gesamtanzahl aller erfassten Störungen im ausgewählten Zeitraum
- **Datenquelle**: Störungsmanagement-Datenbank
- **Berechnung**: COUNT(*) aller Störungseinträge
- **Visualisierung**: Blaue Karte mit AlertTriangle Icon
- **Ask JASZ Integration**: Erklärt die Bedeutung der Gesamtzahl und Trends

#### Aktive Störungen  
- **Zweck**: Anzahl der derzeit noch nicht gelösten Störungen
- **Datenquelle**: Störungen mit Status != 'Geschlossen'
- **Berechnung**: COUNT(*) WHERE status IN ('Offen', 'In Bearbeitung')
- **Visualisierung**: Rote Karte mit TrendingUp Icon
- **Ask JASZ Integration**: Hilft bei der Priorisierung aktiver Störungen

#### Gelöste Störungen
- **Zweck**: Anzahl der erfolgreich behobenen Störungen
- **Datenquelle**: Störungen mit Status 'Gelöst' oder 'Geschlossen'
- **Berechnung**: COUNT(*) WHERE status IN ('Gelöst', 'Geschlossen')
- **Visualisierung**: Grüne Karte mit BarChart3 Icon
- **Ask JASZ Integration**: Erklärt Lösungsraten und -qualität

### 2. Performance-Metriken KPIs

#### Durchschnittliche MTTR (Mean Time To Repair)
- **Zweck**: Durchschnittliche Zeit zur Behebung von Störungen
- **Berechnung**: 
  ```
  AVG(DATEDIFF(MINUTE, created_at, resolved_at))
  WHERE status IN ('Gelöst', 'Geschlossen')
  ```
- **Einheit**: Stunden (automatisch konvertiert von Minuten)
- **Visualisierung**: Orange Karte mit Clock Icon
- **Zielwerte**: < 4 Stunden für kritische, < 24 Stunden für normale Störungen
- **Ask JASZ Integration**: Erklärt MTTR-Berechnung und Optimierungsstrategien

#### Durchschnittliche MTTA (Mean Time To Acknowledge)
- **Zweck**: Durchschnittliche Zeit bis zur ersten Bearbeitung
- **Berechnung**: 
  ```
  AVG(DATEDIFF(MINUTE, created_at, first_response_at))
  ```
- **Einheit**: Stunden (automatisch konvertiert von Minuten)
- **Visualisierung**: Indigo Karte mit Timer Icon
- **Zielwerte**: < 15 Minuten für alle Störungen
- **Ask JASZ Integration**: Hilft bei der Verbesserung der Reaktionszeiten

#### Erstlösungsrate (First Time Fix Rate)
- **Zweck**: Prozentsatz der Störungen, die beim ersten Bearbeitungsversuch gelöst wurden
- **Berechnung**: 
  ```
  (COUNT(*) WHERE first_time_fix = true) / COUNT(*) * 100
  WHERE status IN ('Gelöst', 'Geschlossen')
  ```
- **Einheit**: Prozent
- **Visualisierung**: Cyan Karte mit ShieldCheck Icon
- **Zielwerte**: > 80% für optimale Effizienz
- **Ask JASZ Integration**: Erklärt Faktoren für erfolgreiche Erstlösung

### 3. Erweiterte MTTR-Metriken (Optional)

#### Schnellste Behebung
- **Zweck**: Die kürzeste gemessene Zeit zur Behebung einer Störung
- **Berechnung**: 
  ```
  MIN(DATEDIFF(MINUTE, created_at, resolved_at))
  WHERE status IN ('Gelöst', 'Geschlossen')
  ```
- **Einheit**: Minuten oder Stunden (dynamisch formatiert)
- **Visualisierung**: Gelbe Karte mit Clock Icon
- **Ask JASZ Integration**: Zeigt Best-Practice Beispiele auf

#### MTTR-Verbesserung
- **Zweck**: Prozentuale Verbesserung der MTTR-Zeit im Vergleich zur Vorperiode
- **Berechnung**: 
  ```
  ((Previous_MTTR - Current_MTTR) / Previous_MTTR) * 100
  ```
- **Einheit**: Prozent (positiv = Verbesserung, negativ = Verschlechterung)
- **Visualisierung**: Grüne/Rote Karte je nach Trend mit TrendingUp Icon
- **Ask JASZ Integration**: Analysiert Verbesserungsursachen und -potentiale

## KPI-Karten Design und Funktionalität

### Visuelle Gestaltung
- **SubtlePatternCard**: Einheitliches Design mit dezentem Muster-Hintergrund
- **Farbkodierung**: Jede KPI-Kategorie hat eine eindeutige Farbe
- **Icons**: Aussagekräftige Lucide-Icons für schnelle Erkennung
- **Responsive Layout**: Automatische Anpassung an verschiedene Bildschirmgrößen

### Interaktive Elemente
- **Hover-Effekte**: Visuelles Feedback bei Mouse-over
- **Ask JASZ Button**: Kontextsensitiver Hilfe-Button in jeder Karte
- **Tooltip-System**: Zusätzliche Informationen auf Hover
- **Click-Actions**: Weitere Details durch Klick auf Karte (optional)

### Ask JASZ Integration Details

#### Kontext-Erstellung für KPIs
Jede KPI-Karte erstellt automatisch einen spezifischen Kontext:
```typescript
const context = createKpiContext(
  label,        // KPI-Name (z.B. "Ø MTTR")
  value,        // Aktueller Wert (z.B. "2.5h")
  description   // Beschreibung der Berechnung
);
```

#### Template-Verwendung
Alle KPI-Karten nutzen das `KPI_EXPLANATION` Template:
```typescript
template: "Erkläre mir diese KPI '{title}' mit dem aktuellen Wert {currentValue}. 
Wie wird {title} berechnet? Was bedeutet der Wert {currentValue} für unser System? 
Welche Faktoren beeinflussen diese Kennzahl und wie können wir sie optimieren?"
```

#### Tooltips und Benutzerführung
- **Einheitliche Tooltips**: "Lass dir [KPI-Name] erklären - was bedeutet der Wert [Wert]..."
- **Kontextuelle Hilfe**: Spezifische Erklärungen je nach KPI-Typ
- **Optimierungshinweise**: Konkrete Verbesserungsvorschläge

## Datenqualität und Genauigkeit

### Datenvalidierung
- **Null-Werte**: Behandlung fehlender Daten mit Standardwerten
- **Zeitzone-Normalisierung**: UTC-basierte Zeitberechnungen
- **Datentyp-Konsistenz**: Einheitliche Datentypen für alle Berechnungen
- **Ausreißer-Behandlung**: Filterung unrealistischer Werte

### Performance-Optimierung
- **Caching**: Zwischenspeicherung aufwendiger Berechnungen
- **Indizierung**: Optimierte Datenbankindizes für KPI-Abfragen
- **Lazy Loading**: Bedarfsgerechte Datenladung
- **Debouncing**: Vermeidung redundanter API-Calls

### Echtzeit-Updates
- **Auto-Refresh**: Periodische Aktualisierung der KPI-Werte
- **WebSocket Integration**: Live-Updates für kritische Metriken
- **Change Detection**: Intelligente Erkennung von Datenänderungen
- **Loading States**: Benutzerfreundliche Ladezustände

## Konfiguration und Anpassung

### KPI-Parameter
- **Zeitraum-Auswahl**: Flexible Zeitfenster für KPI-Berechnung
- **Filter-Integration**: Berücksichtigung aktiver Filter
- **Schwellwerte**: Konfigurierbare Warn- und Kritikwerte
- **Formatierung**: Anpassbare Darstellung der Werte

### Business-spezifische Anpassungen
- **Zielwerte**: Definition unternehmensspecifischer Targets
- **Gewichtung**: Prioritätsmäßige Gewichtung verschiedener KPIs
- **Kategorisierung**: Gruppierung nach Geschäftsbereichen
- **Compliance-Metriken**: Branchenspezifische Kennzahlen

### Personalisierung
- **Benutzer-Präferenzen**: Individuelle KPI-Auswahl
- **Dashboard-Layout**: Anpassbare Anordnung der Karten
- **Benachrichtigungen**: Personalisierte Alerts bei Schwellwert-Überschreitungen
- **Export-Optionen**: Benutzerdefinierte Reporting-Formate

## Integration mit anderen Systemen

### Datenquellen
- **Störungsmanagement-DB**: Primäre Quelle für alle störungsrelevanten KPIs
- **SAP-Integration**: Geschäftsprozess-relevante Kennzahlen
- **Monitoring-Systeme**: Infrastructure- und Application-Metriken
- **External APIs**: Integration externer Datenquellen

### Export und Reporting
- **API-Integration**: RESTful APIs für externe Systeme
- **BI-Tool Integration**: Anbindung an Business Intelligence Plattformen
- **Excel-Export**: Datenexport für detaillierte Analysen
- **Automatisierte Reports**: Scheduled KPI-Reports per E-Mail

## Erweiterte Funktionen

### Trend-Analysen
- **Historische Vergleiche**: Entwicklung der KPIs über Zeit
- **Forecasting**: Vorhersage zukünftiger KPI-Werte
- **Anomaly Detection**: Automatische Erkennung ungewöhnlicher Werte
- **Seasonal Adjustment**: Berücksichtigung saisonaler Schwankungen

### Benchmarking
- **Internal Benchmarks**: Vergleich verschiedener Zeiträume/Teams
- **Industry Standards**: Einordnung in Branchenwerte
- **SLA-Compliance**: Überwachung von Service Level Agreements
- **Target Tracking**: Verfolgung der Zielerreichung

### Kollaboration
- **Shared Insights**: Teilen von KPI-Analysen im Team
- **Annotation System**: Kommentare und Notizen zu KPI-Entwicklungen
- **Alert Subscriptions**: Team-basierte Benachrichtigungen
- **Collaborative Analysis**: Gemeinsame Root-Cause-Analyse

## Best Practices für KPI-Management

### KPI-Design Prinzipien
1. **SMART Criteria**: Specific, Measurable, Achievable, Relevant, Time-bound
2. **Business Alignment**: Direkte Verbindung zu Geschäftszielen
3. **Actionable Insights**: KPIs sollten zu konkreten Handlungen führen
4. **Balanced Scorecard**: Ausgewogene Betrachtung verschiedener Dimensionen

### Monitoring-Strategien
1. **Leading vs Lagging**: Balance zwischen predictive und historic KPIs
2. **Real-time vs Batch**: Angemessene Aktualisierungsfrequenz
3. **Granularity**: Richtige Detailebene für verschiedene Zielgruppen
4. **Context Preservation**: Zusätzliche Informationen für KPI-Interpretation

### Kontinuierliche Verbesserung
1. **Regular Reviews**: Periodische Überprüfung der KPI-Relevanz
2. **Stakeholder Feedback**: Einbeziehung der KPI-Nutzer in Optimierung
3. **Technology Updates**: Kontinuierliche Verbesserung der technischen Basis
4. **Process Enhancement**: Optimierung der zugrundeliegenden Prozesse

## Troubleshooting

### Häufige Probleme
- **Null/Undefined Values**: Behandlung fehlender Datenpunkte
- **Performance Issues**: Langsame KPI-Berechnung bei großen Datenmengen
- **Data Inconsistencies**: Abweichungen zwischen verschiedenen Datenquellen
- **Display Errors**: Formatierungs- oder Darstellungsprobleme

### Lösungsansätze
- **Error Handling**: Robuste Fehlerbehandlung mit Fallback-Werten
- **Caching Strategies**: Intelligente Zwischenspeicherung für bessere Performance
- **Data Validation**: Umfassende Validierung vor KPI-Berechnung
- **Monitoring**: Überwachung der KPI-Pipeline selbst