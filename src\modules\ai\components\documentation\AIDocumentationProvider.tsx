import React, { createContext, useContext, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

export interface DocumentationSection {
  id: string;
  title: string;
  content: string;
  category: 'overview' | 'features' | 'tutorials' | 'api' | 'troubleshooting';
  tags: string[];
  lastUpdated: Date;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export interface Tutorial {
  id: string;
  title: string;
  description: string;
  steps: TutorialStep[];
  category: string;
  estimatedTime: number;
  prerequisites: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export interface TutorialStep {
  id: string;
  title: string;
  content: string;
  action?: {
    type: 'click' | 'input' | 'navigate' | 'wait';
    target?: string;
    value?: string;
  };
  validation?: {
    type: 'element' | 'value' | 'state';
    condition: string;
  };
}

export interface HelpContext {
  currentPage: string;
  userRole: string;
  availableFeatures: string[];
}

interface AIDocumentationContextType {
  // Documentation management
  sections: DocumentationSection[];
  tutorials: Tutorial[];
  searchDocumentation: (query: string) => DocumentationSection[];
  getDocumentationByCategory: (category: string) => DocumentationSection[];
  
  // Tutorial management
  currentTutorial: Tutorial | null;
  currentStep: number;
  startTutorial: (tutorialId: string) => void;
  nextStep: () => void;
  previousStep: () => void;
  completeTutorial: () => void;
  exitTutorial: () => void;
  
  // Help system
  showHelp: boolean;
  helpContext: HelpContext;
  toggleHelp: () => void;
  setHelpContext: (context: HelpContext) => void;
  getContextualHelp: () => DocumentationSection[];
  
  // User progress
  completedTutorials: string[];
  markTutorialComplete: (tutorialId: string) => void;
  getUserProgress: () => { completed: number; total: number };
}

const AIDocumentationContext = createContext<AIDocumentationContextType | null>(null);

export const useAIDocumentation = () => {
  const context = useContext(AIDocumentationContext);
  if (!context) {
    throw new Error('useAIDocumentation must be used within AIDocumentationProvider');
  }
  return context;
};

interface AIDocumentationProviderProps {
  children: React.ReactNode;
}

export const AIDocumentationProvider: React.FC<AIDocumentationProviderProps> = ({
  children
}) => {
  const { t } = useTranslation();
  const [sections] = useState<DocumentationSection[]>([]);
  const [tutorials] = useState<Tutorial[]>([]);
  const [currentTutorial, setCurrentTutorial] = useState<Tutorial | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [showHelp, setShowHelp] = useState(false);
  const [helpContext, setHelpContext] = useState<HelpContext>({
    currentPage: '',
    userRole: '',
    availableFeatures: []
  });
  const [completedTutorials, setCompletedTutorials] = useState<string[]>([]);

  const searchDocumentation = useCallback((query: string): DocumentationSection[] => {
    const lowercaseQuery = query.toLowerCase();
    return sections.filter(section =>
      section.title.toLowerCase().includes(lowercaseQuery) ||
      section.content.toLowerCase().includes(lowercaseQuery) ||
      section.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  }, [sections]);

  const getDocumentationByCategory = useCallback((category: string): DocumentationSection[] => {
    return sections.filter(section => section.category === category);
  }, [sections]);

  const startTutorial = useCallback((tutorialId: string) => {
    const tutorial = tutorials.find(t => t.id === tutorialId);
    if (tutorial) {
      setCurrentTutorial(tutorial);
      setCurrentStep(0);
    }
  }, [tutorials]);

  const nextStep = useCallback(() => {
    if (currentTutorial && currentStep < currentTutorial.steps.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  }, [currentTutorial, currentStep]);

  const previousStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  }, [currentStep]);

  const completeTutorial = useCallback(() => {
    if (currentTutorial) {
      markTutorialComplete(currentTutorial.id);
      setCurrentTutorial(null);
      setCurrentStep(0);
    }
  }, [currentTutorial]);

  const exitTutorial = useCallback(() => {
    setCurrentTutorial(null);
    setCurrentStep(0);
  }, []);

  const toggleHelp = useCallback(() => {
    setShowHelp(prev => !prev);
  }, []);

  const getContextualHelp = useCallback((): DocumentationSection[] => {
    return sections.filter(section => {
      // Filter based on current page and user role
      const pageRelevant = section.tags.includes(helpContext.currentPage);
      const roleRelevant = section.tags.includes(helpContext.userRole);
      const featureRelevant = helpContext.availableFeatures.some(feature =>
        section.tags.includes(feature)
      );
      
      return pageRelevant || roleRelevant || featureRelevant;
    });
  }, [sections, helpContext]);

  const markTutorialComplete = useCallback((tutorialId: string) => {
    setCompletedTutorials(prev => {
      if (!prev.includes(tutorialId)) {
        return [...prev, tutorialId];
      }
      return prev;
    });
  }, []);

  const getUserProgress = useCallback(() => {
    return {
      completed: completedTutorials.length,
      total: tutorials.length
    };
  }, [completedTutorials.length, tutorials.length]);

  const value: AIDocumentationContextType = {
    sections,
    tutorials,
    searchDocumentation,
    getDocumentationByCategory,
    currentTutorial,
    currentStep,
    startTutorial,
    nextStep,
    previousStep,
    completeTutorial,
    exitTutorial,
    showHelp,
    helpContext,
    toggleHelp,
    setHelpContext,
    getContextualHelp,
    completedTutorials,
    markTutorialComplete,
    getUserProgress
  };

  return (
    <AIDocumentationContext.Provider value={value}>
      {children}
    </AIDocumentationContext.Provider>
  );
};