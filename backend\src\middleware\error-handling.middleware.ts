/**
 * Error Handling Middleware
 * 
 * Express-Middleware für konsistente Error-Behandlung und
 * standardisierte API-Response-Formate.
 */

import { Request, Response, NextFunction } from 'express';
import { ErrorHandlerService, StandardError, toApiErrorResponse } from '../services/error-handler.service';
import { ApiResponse } from '../types/database.types';

/**
 * Global Error Handler Middleware
 * Fängt alle unbehandelten Fehler ab und formatiert sie einheitlich
 */
export function globalErrorHandler(
  error: Error | StandardError | any,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const errorHandler = ErrorHandlerService.getInstance();
  
  // Bestimme ob es bereits ein StandardError ist
  let standardError: StandardError;
  
  if (error && typeof error === 'object' && 'id' in error && 'type' in error) {
    // Bereits ein StandardError
    standardError = error as StandardError;
  } else {
    // Konvertiere zu StandardError
    standardError = errorHandler.handleError(error, {
      service: 'API',
      method: req.method,
      requestId: req.headers['x-request-id'] as string
    });
  }

  // HTTP-Status-Code basierend auf Error-Typ bestimmen
  const statusCode = getHttpStatusCode(standardError);
  
  // API-Response erstellen
  const apiResponse = toApiErrorResponse(standardError);
  
  // Log Request-Details für besseres Debugging
  console.error(`[API-ERROR] ${req.method} ${req.originalUrl}`, {
    errorId: standardError.id,
    statusCode,
    userAgent: req.headers['user-agent'],
    ip: req.ip,
    body: req.body,
    query: req.query
  });

  // Response senden
  res.status(statusCode).json(apiResponse);
}

/**
 * Async Error Handler Wrapper
 * Wrapper für async Route-Handler um Errors automatisch zu behandeln
 */
export function asyncErrorHandler<T>(
  handler: (req: Request, res: Response, next: NextFunction) => Promise<void>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(handler(req, res, next)).catch(next);
  };
}

/**
 * Request-Handler mit eingebautem Error-Handling
 * Vereinfacht die Error-Behandlung in Route-Handlern
 */
export async function handleRequest<T>(
  req: Request,
  res: Response,
  operation: () => Promise<T>,
  options?: {
    successMessage?: string;
    service?: string;
    method?: string;
  }
): Promise<void> {
  try {
    const result = await operation();
    
    const response: ApiResponse<T> = {
      success: true,
      data: result
    };
    
    res.json(response);
  } catch (error) {
    const errorHandler = ErrorHandlerService.getInstance();
    const standardError = errorHandler.handleError(error, {
      service: options?.service || 'API',
      method: options?.method || req.method,
      requestId: req.headers['x-request-id'] as string
    });
    
    const statusCode = getHttpStatusCode(standardError);
    const apiResponse = toApiErrorResponse(standardError);
    
    res.status(statusCode).json(apiResponse);
  }
}

/**
 * Validation Error Handler
 * Speziell für Validierungsfehler in Request-Daten
 */
export function handleValidationError(
  req: Request,
  res: Response,
  validationErrors: any[]
): void {
  const errorHandler = ErrorHandlerService.getInstance();
  
  const details = {
    validationErrors: validationErrors.map(err => ({
      field: err.param || err.path,
      value: err.value,
      message: err.msg || err.message
    }))
  };
  
  const standardError = errorHandler.handleValidationError(
    'Validierungsfehler in den Eingabedaten',
    details,
    {
      service: 'API',
      method: req.method
    }
  );
  
  const apiResponse = toApiErrorResponse(standardError);
  res.status(400).json(apiResponse);
}

/**
 * Not Found Error Handler
 * Für 404-Fehler
 */
export function handleNotFound(req: Request, res: Response): void {
  const errorHandler = ErrorHandlerService.getInstance();
  
  const standardError = errorHandler.handleNotFoundError(
    'Route',
    req.originalUrl,
    {
      service: 'API',
      method: req.method
    }
  );
  
  const apiResponse = toApiErrorResponse(standardError);
  res.status(404).json(apiResponse);
}

/**
 * Rate Limit Error Handler
 * Erweitert die Standard-Rate-Limit-Responses
 */
export function enhancedRateLimitHandler(req: Request, res: Response): void {
  const errorHandler = ErrorHandlerService.getInstance();
  
  const standardError = errorHandler.handleError(
    new Error('Rate limit exceeded'),
    {
      service: 'API',
      method: req.method
    }
  );
  
  // Überschreibe Standard-Error-Details für Rate-Limiting
  standardError.code = 'RATE_LIMIT_EXCEEDED';
  standardError.userMessage = 'Zu viele Anfragen. Bitte versuchen Sie es später erneut.';
  standardError.suggestions = [
    'Reduzieren Sie die Anzahl der Anfragen',
    'Warten Sie bis zum Reset des Rate-Limits',
    'Kontaktieren Sie den Support bei anhaltenden Problemen'
  ];
  
  const retryAfter = res.getHeader('Retry-After') || 60;
  standardError.details = {
    retryAfter: Number(retryAfter),
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 Minuten
      maxRequests: res.getHeader('X-RateLimit-Limit') || 'unknown'
    }
  };
  
  const apiResponse = toApiErrorResponse(standardError);
  res.status(429).json(apiResponse);
}

/**
 * HTTP-Status-Code basierend auf Error-Typ bestimmen
 */
function getHttpStatusCode(error: StandardError): number {
  switch (error.type) {
    case 'VALIDATION_ERROR':
      return 400; // Bad Request
    
    case 'NOT_FOUND_ERROR':
      return 404; // Not Found
    
    case 'PERMISSION_ERROR':
      return 403; // Forbidden
    
    case 'TIMEOUT_ERROR':
      return 408; // Request Timeout
    
    case 'DATABASE_ERROR':
      return 500; // Internal Server Error
    
    case 'CACHE_ERROR':
      return 500; // Internal Server Error
    
    case 'NETWORK_ERROR':
      return 502; // Bad Gateway
    
    case 'CONFIGURATION_ERROR':
      return 500; // Internal Server Error
    
    case 'BUSINESS_LOGIC_ERROR':
      return 422; // Unprocessable Entity
    
    case 'UNKNOWN_ERROR':
    default:
      return 500; // Internal Server Error
  }
}

/**
 * Express Error Handler für spezifische Error-Typen
 */
export class ExpressErrorHandlers {
  
  /**
   * 404 Handler für nicht gefundene Routen
   */
  static notFound(req: Request, res: Response, next: NextFunction): void {
    handleNotFound(req, res);
  }
  
  /**
   * Allgemeiner Error Handler
   */
  static general(error: any, req: Request, res: Response, next: NextFunction): void {
    globalErrorHandler(error, req, res, next);
  }
  
  /**
   * Unhandled Promise Rejection Handler
   */
  static unhandledRejection(reason: any, promise: Promise<any>): void {
    const errorHandler = ErrorHandlerService.getInstance();
    
    console.error('[UNHANDLED-REJECTION] Unhandled Promise Rejection:', reason);
    
    errorHandler.handleError(reason, {
      service: 'System',
      method: 'unhandledRejection'
    });
  }
  
  /**
   * Uncaught Exception Handler
   */
  static uncaughtException(error: Error): void {
    const errorHandler = ErrorHandlerService.getInstance();
    
    console.error('[UNCAUGHT-EXCEPTION] Uncaught Exception:', error);
    
    errorHandler.handleError(error, {
      service: 'System',
      method: 'uncaughtException'
    });
    
    // Graceful shutdown nach uncaught exception
    console.error('[SYSTEM] Graceful shutdown aufgrund uncaught exception...');
    process.exit(1);
  }
}

/**
 * Error-Handler Setup für Express-App
 */
export function setupErrorHandling(app: any): void {
  // Request-ID-Middleware (für besseres Error-Tracking)
  app.use((req: Request, res: Response, next: NextFunction) => {
    if (!req.headers['x-request-id']) {
      req.headers['x-request-id'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    res.setHeader('X-Request-ID', req.headers['x-request-id']);
    next();
  });

  // 404 Handler (muss vor dem allgemeinen Error Handler stehen)
  app.use(ExpressErrorHandlers.notFound);
  
  // Allgemeiner Error Handler
  app.use(ExpressErrorHandlers.general);
  
  // System-Level Error Handlers
  process.on('unhandledRejection', ExpressErrorHandlers.unhandledRejection);
  process.on('uncaughtException', ExpressErrorHandlers.uncaughtException);
  
  console.log('[ERROR-HANDLING] Error handling middleware konfiguriert');
}

/**
 * Repository Error Handler Decorator
 * Vereinfacht Error-Handling in Repository-Klassen
 */
export function repositoryErrorHandler(serviceName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      try {
        return await method.apply(this, args);
      } catch (error) {
        const errorHandler = ErrorHandlerService.getInstance();
        
        const standardError = errorHandler.handleError(error, {
          service: serviceName,
          method: propertyName
        });
        
        // Für Repository-Fehler, werfe den StandardError weiter
        // damit er von der API-Schicht behandelt werden kann
        throw standardError;
      }
    };

    return descriptor;
  };
}

/**
 * Service Error Handler Decorator
 * Für Service-Klassen
 */
export function serviceErrorHandler(serviceName: string) {
  return repositoryErrorHandler(serviceName); // Gleiche Implementierung
}

/**
 * Type Guards für Error-Handling
 */
export function isStandardError(error: any): error is StandardError {
  return error && 
         typeof error === 'object' && 
         'id' in error && 
         'type' in error && 
         'severity' in error;
}

export function isApiErrorResponse(response: any): response is ApiResponse<any> {
  return response && 
         typeof response === 'object' && 
         'success' in response;
}