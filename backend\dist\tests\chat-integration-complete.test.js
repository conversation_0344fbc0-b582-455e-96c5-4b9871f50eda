"use strict";
/**
 * Complete Integration Tests for AI Chatbot Database Integration
 *
 * End-to-end tests for chat requests with database integration covering:
 * - Various query types (<PERSON><PERSON><PERSON><PERSON>, Dispatch, Cutting, mixed queries)
 * - Performance tests to ensure response times meet requirements
 * - Error scenarios and fallback behavior
 * - Verification that existing chat functionality remains unaffected
 *
 * Requirements: 1.1, 1.2, 1.3, 2.1, 3.1, 4.1, 5.1
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma/client");
const chat_routes_1 = __importDefault(require("../routes/chat.routes"));
const data_enrichment_service_1 = __importDefault(require("../services/data-enrichment.service"));
const openrouter_service_1 = __importDefault(require("../services/openrouter.service"));
// Mock external dependencies
jest.mock('../services/openrouter.service');
jest.mock('@prisma/client');
describe('Complete Chat Integration Tests', () => {
    let app;
    let mockPrisma;
    let mockOpenRouterService;
    beforeEach(() => {
        jest.clearAllMocks();
        // Setup Express app
        app = (0, express_1.default)();
        app.use(express_1.default.json());
        app.use('/api/chat', chat_routes_1.default);
        // Setup mocks
        mockPrisma = new client_1.PrismaClient();
        mockOpenRouterService = openrouter_service_1.default;
        // Default successful OpenRouter response
        mockOpenRouterService.generateResponse.mockResolvedValue({
            response: 'Mocked AI response',
            timestamp: new Date().toISOString(),
            model: '@preset/lapp',
            hasInsights: false,
            hasAnomalies: false,
            hasEnrichedContext: false,
            dataTypes: []
        });
    });
    describe('Basic Chat Functionality (Requirement 5.1)', () => {
        it('should handle basic chat requests without database integration', async () => {
            const startTime = Date.now();
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({
                message: 'Hello, how are you?'
            })
                .expect(200);
            const responseTime = Date.now() - startTime;
            expect(response.body).toHaveProperty('response');
            expect(response.body).toHaveProperty('timestamp');
            expect(response.body).toHaveProperty('model');
            expect(response.body.hasEnrichedContext).toBe(false);
            expect(responseTime).toBeLessThan(5000); // Should respond within 5 seconds
            // Verify OpenRouter was called with basic parameters
            expect(mockOpenRouterService.generateResponse).toHaveBeenCalledWith({
                message: 'Hello, how are you?',
                includeInsights: false,
                includeAnomalies: false
            });
        });
        it('should validate input parameters', async () => {
            // Test empty message
            await (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({ message: '' })
                .expect(400);
            // Test message too long
            const longMessage = 'a'.repeat(1001);
            await (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({ message: longMessage })
                .expect(400);
            // Test missing message
            await (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({})
                .expect(400);
        });
    });
    describe('Störungen Query Integration (Requirements 1.1, 1.2, 2.1)', () => {
        beforeEach(() => {
            // Mock successful database responses for Störungen
            const mockStoerungenData = {
                statistics: {
                    total: 15,
                    active: 3,
                    resolved: 12,
                    critical: 1,
                    high: 2,
                    medium: 8,
                    low: 4
                },
                recentIncidents: [
                    {
                        id: 1,
                        title: 'Server Ausfall',
                        severity: 'CRITICAL',
                        status: 'ACTIVE',
                        createdAt: new Date('2024-01-15T10:00:00Z'),
                        resolvedAt: null
                    }
                ],
                systemStatus: [
                    { system: 'Database', status: 'HEALTHY' },
                    { system: 'API Gateway', status: 'WARNING' }
                ]
            };
            // Mock enriched context response
            mockOpenRouterService.generateResponse.mockResolvedValue({
                response: 'Basierend auf den aktuellen Störungsdaten: Es gibt derzeit 3 aktive Störungen...',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: false,
                hasAnomalies: false,
                hasEnrichedContext: true,
                dataTypes: ['stoerungen']
            });
        });
        it('should handle Störungen-related queries with database integration', async () => {
            const startTime = Date.now();
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie viele Störungen haben wir aktuell?',
                includeInsights: false,
                includeAnomalies: false
            })
                .expect(200);
            const responseTime = Date.now() - startTime;
            expect(response.body).toHaveProperty('response');
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataTypes).toContain('stoerungen');
            expect(response.body.dataEnrichmentUsed).toBe(true);
            expect(response.body.detectedIntents).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    type: 'stoerungen',
                    confidence: expect.any(Number)
                })
            ]));
            expect(responseTime).toBeLessThan(3000); // Should respond within 3 seconds
        });
        it('should handle incident-specific queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Zeige mir kritische Störungen von heute'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataTypes).toContain('stoerungen');
            expect(response.body.detectedIntents).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    type: 'stoerungen',
                    keywords: expect.arrayContaining(['kritische', 'störungen'])
                })
            ]));
        });
        it('should handle system status queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie ist der aktuelle Systemstatus?'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataEnrichmentUsed).toBe(true);
        });
    });
    describe('Dispatch Query Integration (Requirements 1.1, 1.3, 3.1)', () => {
        beforeEach(() => {
            // Mock successful database responses for Dispatch
            mockOpenRouterService.generateResponse.mockResolvedValue({
                response: 'Die aktuellen Versand-KPIs zeigen: Service Level 95.2%, Tonnage 1250t...',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: false,
                hasAnomalies: false,
                hasEnrichedContext: true,
                dataTypes: ['dispatch']
            });
        });
        it('should handle dispatch performance queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie ist unsere Versand-Performance heute?'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataTypes).toContain('dispatch');
            expect(response.body.detectedIntents).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    type: 'dispatch'
                })
            ]));
        });
        it('should handle service level queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie hoch ist unser Service Level?'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataTypes).toContain('dispatch');
        });
        it('should handle tonnage and production queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Zeige mir die Tonnage-Statistiken dieser Woche'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataTypes).toContain('dispatch');
        });
        it('should handle picking and quality metrics', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie sind unsere ATRL und QM-Werte?'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataTypes).toContain('dispatch');
        });
    });
    describe('Cutting Query Integration (Requirements 1.1, 1.3, 4.1)', () => {
        beforeEach(() => {
            // Mock successful database responses for Cutting
            mockOpenRouterService.generateResponse.mockResolvedValue({
                response: 'Die Ablängerei-Performance zeigt: Maschine A1 führt mit 98.5% Effizienz...',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: false,
                hasAnomalies: false,
                hasEnrichedContext: true,
                dataTypes: ['cutting']
            });
        });
        it('should handle cutting performance queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie effizient arbeiten unsere Schneidemaschinen?'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataTypes).toContain('cutting');
            expect(response.body.detectedIntents).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    type: 'cutting'
                })
            ]));
        });
        it('should handle machine efficiency queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Welche Maschinen sind am effizientesten?'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataTypes).toContain('cutting');
        });
        it('should handle warehouse cuts queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Zeige mir die Lager-Schnitt-Daten'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataTypes).toContain('cutting');
        });
    });
    describe('Mixed Query Integration (Requirements 1.1, 5.1)', () => {
        beforeEach(() => {
            // Mock successful database responses for mixed queries
            mockOpenRouterService.generateResponse.mockResolvedValue({
                response: 'Gesamtübersicht: Störungen: 3 aktiv, Versand: 95.2% Service Level, Ablängerei: 98.1% Effizienz...',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: false,
                hasAnomalies: false,
                hasEnrichedContext: true,
                dataTypes: ['stoerungen', 'dispatch', 'cutting']
            });
        });
        it('should handle queries requiring multiple data sources', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Gib mir eine Gesamtübersicht über alle Systeme'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataTypes).toEqual(expect.arrayContaining(['stoerungen', 'dispatch', 'cutting']));
            expect(response.body.detectedIntents.length).toBeGreaterThan(1);
        });
        it('should handle performance overview queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie ist die Performance in allen Bereichen?'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataTypes.length).toBeGreaterThan(1);
        });
        it('should handle KPI dashboard queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Zeige mir alle wichtigen KPIs'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataEnrichmentUsed).toBe(true);
        });
    });
    describe('Time-based Query Integration (Requirements 1.2, 5.1)', () => {
        it('should handle "today" time range queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie viele Störungen hatten wir heute?'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.detectedIntents).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    type: 'stoerungen',
                    keywords: expect.arrayContaining(['heute'])
                })
            ]));
        });
        it('should handle "this week" time range queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Zeige mir die Versand-Performance dieser Woche'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.detectedIntents).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    type: 'dispatch',
                    keywords: expect.arrayContaining(['woche'])
                })
            ]));
        });
        it('should handle "last month" time range queries', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie war die Maschinen-Effizienz letzten Monat?'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.detectedIntents).toEqual(expect.arrayContaining([
                expect.objectContaining({
                    type: 'cutting',
                    keywords: expect.arrayContaining(['monat'])
                })
            ]));
        });
    });
    de;
    scribe('Performance Tests (Requirement 5.5)', () => {
        it('should respond to basic chat within 2 seconds', async () => {
            const startTime = Date.now();
            await (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({
                message: 'Simple question'
            })
                .expect(200);
            const responseTime = Date.now() - startTime;
            expect(responseTime).toBeLessThan(2000);
        });
        it('should respond to enhanced chat with database integration within 3 seconds', async () => {
            const startTime = Date.now();
            await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie viele Störungen haben wir?'
            })
                .expect(200);
            const responseTime = Date.now() - startTime;
            expect(responseTime).toBeLessThan(3000);
        });
        it('should handle concurrent requests efficiently', async () => {
            const startTime = Date.now();
            const requests = Array.from({ length: 5 }, (_, i) => (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: `Query ${i + 1}: Status der Systeme`
            }));
            const responses = await Promise.all(requests);
            const totalTime = Date.now() - startTime;
            // All requests should succeed
            responses.forEach(response => {
                expect(response.status).toBe(200);
            });
            // Total time should be reasonable for concurrent requests
            expect(totalTime).toBeLessThan(5000);
        });
        it('should maintain performance under load', async () => {
            const iterations = 10;
            const responseTimes = [];
            for (let i = 0; i < iterations; i++) {
                const startTime = Date.now();
                await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: `Load test query ${i + 1}`
                })
                    .expect(200);
                responseTimes.push(Date.now() - startTime);
            }
            const averageTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
            const maxTime = Math.max(...responseTimes);
            expect(averageTime).toBeLessThan(3000);
            expect(maxTime).toBeLessThan(5000);
        });
    });
    describe('Error Scenarios and Fallback Behavior (Requirements 1.4, 5.4)', () => {
        it('should handle data enrichment service failures gracefully', async () => {
            // Mock data enrichment service to throw error
            const mockDataEnrichmentService = jest.spyOn(data_enrichment_service_1.default.prototype, 'enrichChatContext');
            mockDataEnrichmentService.mockRejectedValue(new Error('Database connection failed'));
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie viele Störungen haben wir?'
            })
                .expect(200);
            expect(response.body).toHaveProperty('response');
            expect(response.body.dataEnrichmentError).toBe(true);
            expect(response.body.dataEnrichmentUsed).toBe(false);
            expect(response.body.enrichmentDetails).toHaveProperty('error');
            expect(response.body.enrichmentDetails.fallbackUsed).toBe(true);
            mockDataEnrichmentService.mockRestore();
        });
        it('should handle partial repository failures', async () => {
            // Mock partial failure scenario
            mockOpenRouterService.generateResponse.mockResolvedValue({
                response: 'Teilweise Daten verfügbar: Störungen OK, Versand nicht verfügbar...',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: false,
                hasAnomalies: false,
                hasEnrichedContext: true,
                dataTypes: ['stoerungen'] // Only one data type available
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Gesamtübersicht aller Systeme'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataQuality.hasPartialData).toBe(true);
            expect(response.body.dataQuality.hasFullData).toBe(false);
        });
        it('should handle OpenRouter service failures', async () => {
            // Mock OpenRouter service to throw error
            mockOpenRouterService.generateResponse.mockRejectedValue(new Error('OpenRouter API unavailable'));
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Test query'
            })
                .expect(500);
            expect(response.body).toHaveProperty('error');
            expect(response.body.success).toBe(false);
        });
        it('should handle timeout scenarios', async () => {
            // Mock slow response that should timeout
            mockOpenRouterService.generateResponse.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({
                response: 'Delayed response',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: false,
                hasAnomalies: false,
                hasEnrichedContext: false,
                dataTypes: []
            }), 10000)) // 10 second delay
            );
            const startTime = Date.now();
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Slow query'
            })
                .timeout(5000); // 5 second timeout
            const responseTime = Date.now() - startTime;
            // Should either succeed quickly or fail with timeout
            if (response.status === 200) {
                expect(responseTime).toBeLessThan(5000);
            }
            else {
                expect(response.status).toBe(500);
            }
        });
        it('should provide meaningful error messages in German', async () => {
            mockOpenRouterService.generateResponse.mockRejectedValue(new Error('Service unavailable'));
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Test query'
            })
                .expect(500);
            expect(response.body.message).toContain('Chat-Anfrage');
            expect(response.body.error).toBe('Interner Serverfehler');
        });
        it('should handle no data found scenarios', async () => {
            // Mock scenario where no relevant data is found
            mockOpenRouterService.generateResponse.mockResolvedValue({
                response: 'Keine relevanten Daten für Ihre Anfrage gefunden.',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: false,
                hasAnomalies: false,
                hasEnrichedContext: false,
                dataTypes: []
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Unrelated query about weather'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(false);
            expect(response.body.dataEnrichmentUsed).toBe(false);
            expect(response.body.dataTypes).toHaveLength(0);
        });
    });
    describe('Data Quality and Context Verification (Requirements 5.1, 5.2, 5.3)', () => {
        it('should include data freshness indicators', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Aktuelle Störungen'
            })
                .expect(200);
            expect(response.body).toHaveProperty('timestamp');
            expect(response.body.dataQuality).toHaveProperty('hasFullData');
            expect(response.body.dataQuality).toHaveProperty('hasPartialData');
            expect(response.body.dataQuality).toHaveProperty('fallbackUsed');
            expect(response.body.dataQuality).toHaveProperty('availableDataTypes');
        });
        it('should provide context about data sources used', async () => {
            mockOpenRouterService.generateResponse.mockResolvedValue({
                response: 'Basierend auf aktuellen Daten...',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: false,
                hasAnomalies: false,
                hasEnrichedContext: true,
                dataTypes: ['stoerungen', 'dispatch']
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'System overview'
            })
                .expect(200);
            expect(response.body.dataTypes).toEqual(['stoerungen', 'dispatch']);
            expect(response.body.detectedIntents).toBeDefined();
            expect(response.body.enrichmentDetails).toBeDefined();
        });
        it('should handle insights and anomalies flags', async () => {
            mockOpenRouterService.generateResponse.mockResolvedValue({
                response: 'Analyse mit Insights und Anomalien...',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: true,
                hasAnomalies: true,
                hasEnrichedContext: true,
                dataTypes: ['stoerungen']
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Analysiere die Störungen',
                includeInsights: true,
                includeAnomalies: true
            })
                .expect(200);
            expect(response.body.hasInsights).toBe(true);
            expect(response.body.hasAnomalies).toBe(true);
            // Verify OpenRouter was called with correct flags
            expect(mockOpenRouterService.generateResponse).toHaveBeenCalledWith(expect.objectContaining({
                includeInsights: true,
                includeAnomalies: true
            }));
        });
    });
    describe('Backward Compatibility (Requirement 5.1)', () => {
        it('should maintain existing basic chat functionality', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({
                message: 'Hello world'
            })
                .expect(200);
            expect(response.body).toHaveProperty('response');
            expect(response.body).toHaveProperty('timestamp');
            expect(response.body).toHaveProperty('model');
            expect(response.body.hasEnrichedContext).toBe(false);
            // Should not have enhanced features
            expect(response.body).not.toHaveProperty('dataEnrichmentUsed');
            expect(response.body).not.toHaveProperty('detectedIntents');
            expect(response.body).not.toHaveProperty('dataQuality');
        });
        it('should handle enhanced endpoint without breaking existing behavior', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Non-database related question'
            })
                .expect(200);
            expect(response.body).toHaveProperty('response');
            expect(response.body).toHaveProperty('timestamp');
            expect(response.body).toHaveProperty('model');
            // Should have enhanced metadata even if no database context
            expect(response.body).toHaveProperty('dataEnrichmentUsed');
            expect(response.body).toHaveProperty('detectedIntents');
            expect(response.body).toHaveProperty('dataQuality');
        });
        it('should preserve response format consistency', async () => {
            const basicResponse = await (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({ message: 'Test' })
                .expect(200);
            const enhancedResponse = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({ message: 'Test' })
                .expect(200);
            // Both should have core response fields
            ['response', 'timestamp', 'model'].forEach(field => {
                expect(basicResponse.body).toHaveProperty(field);
                expect(enhancedResponse.body).toHaveProperty(field);
            });
            // Enhanced should have additional fields
            ['dataEnrichmentUsed', 'detectedIntents', 'dataQuality'].forEach(field => {
                expect(basicResponse.body).not.toHaveProperty(field);
                expect(enhancedResponse.body).toHaveProperty(field);
            });
        });
    });
    describe('Request Validation and Security', () => {
        it('should validate request body structure', async () => {
            await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Valid message',
                includeInsights: 'invalid_boolean' // Should be boolean
            })
                .expect(400);
        });
        it('should handle malformed JSON', async () => {
            await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send('invalid json')
                .expect(400);
        });
        it('should sanitize input messages', async () => {
            const maliciousMessage = '<script>alert("xss")</script>What are the current incidents?';
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: maliciousMessage
            })
                .expect(200);
            // Verify the service was called (input should be processed)
            expect(mockOpenRouterService.generateResponse).toHaveBeenCalled();
            expect(response.body).toHaveProperty('response');
        });
    });
    describe('Logging and Monitoring', () => {
        it('should log request processing', async () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
            await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Test logging'
            })
                .expect(200);
            expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('[CHAT-ENHANCED] Anfrage erhalten'));
            consoleSpy.mockRestore();
        });
        it('should log response completion', async () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
            await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Test completion logging'
            })
                .expect(200);
            expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('[CHAT-ENHANCED] Antwort gesendet'));
            consoleSpy.mockRestore();
        });
        it('should log errors appropriately', async () => {
            const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
            mockOpenRouterService.generateResponse.mockRejectedValue(new Error('Test error'));
            await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Test error logging'
            })
                .expect(500);
            expect(consoleErrorSpy).toHaveBeenCalledWith(expect.stringContaining('[CHAT-ENHANCED] Fehler:'), expect.any(Error));
            consoleErrorSpy.mockRestore();
        });
    });
});
