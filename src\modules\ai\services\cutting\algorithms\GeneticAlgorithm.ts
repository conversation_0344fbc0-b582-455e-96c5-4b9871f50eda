/**
 * Genetic Algorithm for Cutting Optimization
 * 
 * Implements genetic algorithm for complex cutting optimization problems
 * with multi-objective optimization (waste minimization + efficiency)
 */

import {
  CuttingRequest,
  CuttingPlan,
  DrumAllocation,
  Cut,
  CuttingStep,
  CuttingOrder,
  AvailableDrum
} from '../../../types/cutting';

export interface GeneticAlgorithmConfig {
  populationSize: number;
  generations: number;
  mutationRate: number;
  crossoverRate: number;
  elitismRate: number;
  tournamentSize: number;
  convergenceThreshold: number;
  maxStagnantGenerations: number;
}

export interface Individual {
  chromosome: number[]; // Represents order-to-drum assignments
  fitness: number;
  objectives: {
    wasteMinimization: number;
    efficiency: number;
    timeOptimization: number;
  };
  plan?: CuttingPlan;
}

export interface GeneticOptimizationResult {
  bestSolution: Individual;
  convergenceData: {
    generation: number;
    bestFitness: number;
    averageFitness: number;
    diversity: number;
  }[];
  finalPopulation: Individual[];
  executionTime: number;
  converged: boolean;
}

/**
 * Genetic Algorithm implementation for cutting optimization
 */
export class GeneticAlgorithm {
  private config: GeneticAlgorithmConfig;
  private request!: CuttingRequest; // Definite assignment assertion
  private orderCount!: number;
  private drumCount!: number;

  constructor(config: GeneticAlgorithmConfig) {
    const defaultConfig: GeneticAlgorithmConfig = {
      populationSize: 50,
      generations: 100,
      mutationRate: 0.1,
      crossoverRate: 0.8,
      elitismRate: 0.2,
      tournamentSize: 5,
      convergenceThreshold: 0.001,
      maxStagnantGenerations: 20
    };

    this.config = { ...defaultConfig, ...config };
  }

  /**
   * Run genetic algorithm optimization
   */
  async optimize(request: CuttingRequest): Promise<GeneticOptimizationResult> {
    const startTime = Date.now();
    this.request = request;
    this.orderCount = this.calculateTotalOrders(request.orders);
    this.drumCount = request.availableDrums.length;

    // Initialize population
    let population = this.initializePopulation();

    // Evaluate initial population
    population = await this.evaluatePopulation(population);

    const convergenceData: GeneticOptimizationResult['convergenceData'] = [];
    let bestFitness = Math.max(...population.map(ind => ind.fitness));
    let stagnantGenerations = 0;
    let converged = false;

    // Evolution loop
    for (let generation = 0; generation < this.config.generations; generation++) {
      // Selection, crossover, and mutation
      const newPopulation = await this.evolvePopulation(population);

      // Evaluate new population
      population = await this.evaluatePopulation(newPopulation);

      // Track convergence
      const currentBestFitness = Math.max(...population.map(ind => ind.fitness));
      const averageFitness = population.reduce((sum, ind) => sum + ind.fitness, 0) / population.length;
      const diversity = this.calculatePopulationDiversity(population);

      convergenceData.push({
        generation,
        bestFitness: currentBestFitness,
        averageFitness,
        diversity
      });

      // Check for convergence
      if (Math.abs(currentBestFitness - bestFitness) < this.config.convergenceThreshold) {
        stagnantGenerations++;
      } else {
        stagnantGenerations = 0;
        bestFitness = currentBestFitness;
      }

      if (stagnantGenerations >= this.config.maxStagnantGenerations) {
        converged = true;
        break;
      }
    }

    // Find best solution
    const bestSolution = population.reduce((best, current) =>
      current.fitness > best.fitness ? current : best
    );

    // Generate cutting plan for best solution
    bestSolution.plan = await this.chromosomeToPlan(bestSolution.chromosome);

    const executionTime = Date.now() - startTime;

    return {
      bestSolution,
      convergenceData,
      finalPopulation: population,
      executionTime,
      converged
    };
  }

  /**
   * Initialize random population
   */
  private initializePopulation(): Individual[] {
    const population: Individual[] = [];

    for (let i = 0; i < this.config.populationSize; i++) {
      const chromosome = this.generateRandomChromosome();
      population.push({
        chromosome,
        fitness: 0,
        objectives: {
          wasteMinimization: 0,
          efficiency: 0,
          timeOptimization: 0
        }
      });
    }

    return population;
  }

  /**
   * Generate random chromosome representing order-to-drum assignments
   */
  private generateRandomChromosome(): number[] {
    const chromosome: number[] = [];
    let orderIndex = 0;

    // For each order (considering quantity)
    for (const order of this.request.orders) {
      for (let q = 0; q < order.quantity; q++) {
        // Randomly assign to a drum (0 to drumCount-1)
        chromosome[orderIndex] = Math.floor(Math.random() * this.drumCount);
        orderIndex++;
      }
    }

    return chromosome;
  }

  /**
   * Calculate total number of individual orders (considering quantities)
   */
  private calculateTotalOrders(orders: CuttingOrder[]): number {
    return orders.reduce((sum, order) => sum + order.quantity, 0);
  }

  /**
   * Evaluate fitness for entire population
   */
  private async evaluatePopulation(population: Individual[]): Promise<Individual[]> {
    const evaluatedPopulation: Individual[] = [];

    for (const individual of population) {
      const evaluated = await this.evaluateIndividual(individual);
      evaluatedPopulation.push(evaluated);
    }

    return evaluatedPopulation;
  }

  /**
   * Evaluate fitness for a single individual
   */
  private async evaluateIndividual(individual: Individual): Promise<Individual> {
    try {
      const plan = await this.chromosomeToPlan(individual.chromosome);
      const objectives = this.calculateObjectives(plan);
      const fitness = this.calculateMultiObjectiveFitness(objectives);

      return {
        ...individual,
        fitness,
        objectives,
        plan
      };
    } catch (error) {
      // Invalid chromosome - assign very low fitness
      return {
        ...individual,
        fitness: 0,
        objectives: {
          wasteMinimization: 0,
          efficiency: 0,
          timeOptimization: 0
        }
      };
    }
  }

  /**
   * Convert chromosome to cutting plan
   */
  private async chromosomeToPlan(chromosome: number[]): Promise<CuttingPlan> {
    const drumAllocations: DrumAllocation[] = this.request.availableDrums.map(drum => ({
      drumId: drum.id,
      cuts: [],
      remainingLength: drum.availableLength || drum.remainingLength || 0,
      utilization: 0
    }));

    const cuttingSequence: CuttingStep[] = [];
    let orderIndex = 0;
    let stepNumber = 1;

    // Process each order
    for (const order of this.request.orders) {
      for (let q = 0; q < order.quantity; q++) {
        const drumIndex = chromosome[orderIndex];

        if (drumIndex >= 0 && drumIndex < this.drumCount) {
          const allocation = drumAllocations[drumIndex];
          const drum = this.request.availableDrums[drumIndex];

          // Check if cut can fit
          if (allocation.remainingLength >= order.requiredLength) {
            const cut: Cut = {
              orderId: order.id,
              length: order.requiredLength,
              startPosition: allocation.cuts.reduce((sum, c) => sum + c.length, 0),
              endPosition: allocation.cuts.reduce((sum, c) => sum + c.length, 0) + order.requiredLength
            };

            allocation.cuts.push(cut);
            allocation.remainingLength -= order.requiredLength;
            allocation.utilization = this.calculateUtilization(allocation, drum);

            // Add to cutting sequence
            cuttingSequence.push({
              stepNumber: stepNumber++,
              drumId: drum.id,
              cuts: [cut],
              estimatedTime: this.estimateCuttingTime(order.requiredLength)
            });
          }
        }

        orderIndex++;
      }
    }

    const totalWaste = drumAllocations.reduce((sum, allocation) => sum + allocation.remainingLength, 0);
    const efficiency = this.calculateEfficiency(drumAllocations);
    const estimatedTime = cuttingSequence.reduce((sum, step) => sum + step.estimatedTime, 0);

    return {
      drumAllocations,
      cuttingSequence,
      totalWaste,
      efficiency,
      estimatedTime
    };
  }

  /**
   * Calculate multi-objective fitness values
   */
  private calculateObjectives(plan: CuttingPlan): Individual['objectives'] {
    const totalCapacity = this.request.availableDrums.reduce((sum, drum) =>
      sum + (drum.availableLength || drum.remainingLength || 0), 0
    );

    // Waste minimization (0-1, higher is better)
    const wasteMinimization = totalCapacity > 0 ?
      Math.max(0, 1 - (plan.totalWaste / totalCapacity)) : 0;

    // Efficiency (0-1, higher is better)
    const efficiency = plan.efficiency;

    // Time optimization (0-1, higher is better - lower time is better)
    const maxReasonableTime = this.orderCount * 120; // 2 minutes per cut
    const timeOptimization = Math.max(0, 1 - (plan.estimatedTime / maxReasonableTime));

    return {
      wasteMinimization,
      efficiency,
      timeOptimization
    };
  }

  /**
   * Calculate combined fitness from multiple objectives
   */
  private calculateMultiObjectiveFitness(objectives: Individual['objectives']): number {
    // Weighted sum of objectives
    const weights = {
      wasteMinimization: 0.4,
      efficiency: 0.4,
      timeOptimization: 0.2
    };

    return (
      objectives.wasteMinimization * weights.wasteMinimization +
      objectives.efficiency * weights.efficiency +
      objectives.timeOptimization * weights.timeOptimization
    );
  }

  /**
   * Evolve population through selection, crossover, and mutation
   */
  private async evolvePopulation(population: Individual[]): Promise<Individual[]> {
    const newPopulation: Individual[] = [];

    // Elitism - keep best individuals
    const sortedPopulation = [...population].sort((a, b) => b.fitness - a.fitness);
    const eliteCount = Math.floor(this.config.populationSize * this.config.elitismRate);

    for (let i = 0; i < eliteCount; i++) {
      newPopulation.push({ ...sortedPopulation[i] });
    }

    // Generate offspring through crossover and mutation
    while (newPopulation.length < this.config.populationSize) {
      const parent1 = this.tournamentSelection(population);
      const parent2 = this.tournamentSelection(population);

      let offspring1: Individual;
      let offspring2: Individual;

      if (Math.random() < this.config.crossoverRate) {
        [offspring1, offspring2] = this.crossover(parent1, parent2);
      } else {
        offspring1 = { ...parent1 };
        offspring2 = { ...parent2 };
      }

      // Mutation
      if (Math.random() < this.config.mutationRate) {
        offspring1 = this.mutate(offspring1);
      }
      if (Math.random() < this.config.mutationRate) {
        offspring2 = this.mutate(offspring2);
      }

      newPopulation.push(offspring1);
      if (newPopulation.length < this.config.populationSize) {
        newPopulation.push(offspring2);
      }
    }

    return newPopulation;
  }

  /**
   * Tournament selection
   */
  private tournamentSelection(population: Individual[]): Individual {
    const tournament: Individual[] = [];

    for (let i = 0; i < this.config.tournamentSize; i++) {
      const randomIndex = Math.floor(Math.random() * population.length);
      tournament.push(population[randomIndex]);
    }

    return tournament.reduce((best, current) =>
      current.fitness > best.fitness ? current : best
    );
  }

  /**
   * Single-point crossover
   */
  private crossover(parent1: Individual, parent2: Individual): [Individual, Individual] {
    const crossoverPoint = Math.floor(Math.random() * this.orderCount);

    const offspring1Chromosome = [
      ...parent1.chromosome.slice(0, crossoverPoint),
      ...parent2.chromosome.slice(crossoverPoint)
    ];

    const offspring2Chromosome = [
      ...parent2.chromosome.slice(0, crossoverPoint),
      ...parent1.chromosome.slice(crossoverPoint)
    ];

    return [
      {
        chromosome: offspring1Chromosome,
        fitness: 0,
        objectives: { wasteMinimization: 0, efficiency: 0, timeOptimization: 0 }
      },
      {
        chromosome: offspring2Chromosome,
        fitness: 0,
        objectives: { wasteMinimization: 0, efficiency: 0, timeOptimization: 0 }
      }
    ];
  }

  /**
   * Random mutation
   */
  private mutate(individual: Individual): Individual {
    const mutatedChromosome = [...individual.chromosome];

    // Random gene mutation
    const mutationPoint = Math.floor(Math.random() * this.orderCount);
    mutatedChromosome[mutationPoint] = Math.floor(Math.random() * this.drumCount);

    return {
      chromosome: mutatedChromosome,
      fitness: 0,
      objectives: { wasteMinimization: 0, efficiency: 0, timeOptimization: 0 }
    };
  }

  /**
   * Calculate population diversity
   */
  private calculatePopulationDiversity(population: Individual[]): number {
    if (population.length < 2) return 0;

    let totalDistance = 0;
    let comparisons = 0;

    for (let i = 0; i < population.length; i++) {
      for (let j = i + 1; j < population.length; j++) {
        const distance = this.calculateHammingDistance(
          population[i].chromosome,
          population[j].chromosome
        );
        totalDistance += distance;
        comparisons++;
      }
    }

    return comparisons > 0 ? totalDistance / comparisons : 0;
  }

  /**
   * Calculate Hamming distance between two chromosomes
   */
  private calculateHammingDistance(chromosome1: number[], chromosome2: number[]): number {
    let distance = 0;
    const length = Math.min(chromosome1.length, chromosome2.length);

    for (let i = 0; i < length; i++) {
      if (chromosome1[i] !== chromosome2[i]) {
        distance++;
      }
    }

    return distance;
  }

  /**
   * Calculate drum utilization
   */
  private calculateUtilization(allocation: DrumAllocation, drum: AvailableDrum): number {
    const totalLength = drum.availableLength || drum.remainingLength || 0;
    const usedLength = totalLength - allocation.remainingLength;
    return totalLength > 0 ? usedLength / totalLength : 0;
  }

  /**
   * Calculate overall efficiency
   */
  private calculateEfficiency(allocations: DrumAllocation[]): number {
    const totalCapacity = this.request.availableDrums.reduce((sum, drum) =>
      sum + (drum.availableLength || drum.remainingLength || 0), 0
    );

    const totalUsed = allocations.reduce((sum, allocation) => {
      const drum = this.request.availableDrums.find(d => d.id === allocation.drumId);
      const drumCapacity = drum ? (drum.availableLength || drum.remainingLength || 0) : 0;
      return sum + (drumCapacity - allocation.remainingLength);
    }, 0);

    return totalCapacity > 0 ? totalUsed / totalCapacity : 0;
  }

  /**
   * Estimate cutting time
   */
  private estimateCuttingTime(length: number): number {
    return 30 + (length / 100) * 10; // 30 seconds base + 10 seconds per meter
  }
}