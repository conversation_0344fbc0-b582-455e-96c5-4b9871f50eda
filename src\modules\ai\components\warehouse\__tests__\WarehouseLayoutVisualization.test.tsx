/**
 * Warehouse Layout Visualization Component Tests
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { WarehouseLayoutVisualization } from '../WarehouseLayoutVisualization';
import {
  WarehouseItem,
  WarehouseLayoutAnalysis,
  OptimalPlacement
} from '@/types/warehouse-optimization';

// Mock data
const mockWarehouseItems: WarehouseItem[] = [
  {
    id: 'ITEM_001',
    name: 'Test Kabel',
    category: 'Kabel',
    weight: 2.5,
    dimensions: { length: 100, width: 10, height: 5, volume: 5000 },
    currentLocation: {
      id: 'A-1-1',
      zone: 'A',
      aisle: '1',
      shelf: '1',
      position: '1',
      coordinates: { x: 10, y: 5, z: 0 },
      capacity: 100,
      currentUtilization: 0.8,
      accessibilityScore: 9
    },
    accessFrequency: 15,
    lastAccessed: new Date(),
    relationships: [],
    pickingPriority: 'high'
  }
];

const mockAnalysis: WarehouseLayoutAnalysis = {
  totalItems: 50,
  utilizationRate: 0.75,
  accessibilityScore: 7.2,
  inefficiencies: [
    {
      type: 'misplaced_high_frequency',
      severity: 'medium',
      description: 'Test inefficiency',
      affectedItems: ['ITEM_001'],
      estimatedImpact: 5.5
    }
  ],
  optimizationPotential: 25,
  recommendations: []
};

const mockPlacements: OptimalPlacement[] = [
  {
    itemId: 'ITEM_001',
    currentLocation: mockWarehouseItems[0].currentLocation,
    recommendedLocation: {
      ...mockWarehouseItems[0].currentLocation,
      id: 'A-1-2',
      shelf: '2',
      accessibilityScore: 10
    },
    reason: 'Test placement reason',
    expectedBenefit: {
      timeSavingsPerDay: 3.5,
      efficiencyImprovement: 15,
      costSavings: 1.75,
      pickingDistanceReduction: 20
    },
    confidence: 0.85
  }
];

describe('WarehouseLayoutVisualization', () => {
  const defaultProps = {
    items: mockWarehouseItems,
    analysis: mockAnalysis,
    placements: mockPlacements,
    onItemSelect: vi.fn(),
    onLocationSelect: vi.fn()
  };

  it('renders warehouse layout visualization correctly', () => {
    render(<WarehouseLayoutVisualization {...defaultProps} />);
    
    expect(screen.getByTestId('warehouse-layout-visualization')).toBeInTheDocument();
    expect(screen.getByText('Warehouse Layout Visualisierung')).toBeInTheDocument();
  });

  it('displays warehouse statistics correctly', () => {
    render(<WarehouseLayoutVisualization {...defaultProps} />);
    
    expect(screen.getByText('50 Artikel')).toBeInTheDocument();
    expect(screen.getByText('75.0% Auslastung')).toBeInTheDocument();
    expect(screen.getByText('7.2 Zugänglichkeit')).toBeInTheDocument();
  });

  it('renders warehouse zones correctly', () => {
    render(<WarehouseLayoutVisualization {...defaultProps} />);
    
    expect(screen.getByText('Zone A')).toBeInTheDocument();
    expect(screen.getByText('Zone B')).toBeInTheDocument();
    expect(screen.getByText('Zone C')).toBeInTheDocument();
    expect(screen.getByText('Zone D')).toBeInTheDocument();
  });

  it('switches between different views', () => {
    render(<WarehouseLayoutVisualization {...defaultProps} />);
    
    // Test tab switching
    const heatmapTab = screen.getByText('Heatmap');
    fireEvent.click(heatmapTab);
    
    const inefficienciesTab = screen.getByText('Ineffizienzen');
    fireEvent.click(inefficienciesTab);
    
    expect(screen.getByText('Falsch platzierte häufige Artikel')).toBeInTheDocument();
  });

  it('displays inefficiencies correctly', () => {
    render(<WarehouseLayoutVisualization {...defaultProps} />);
    
    // Switch to inefficiencies tab
    const inefficienciesTab = screen.getByText('Ineffizienzen');
    fireEvent.click(inefficienciesTab);
    
    expect(screen.getByText('Falsch platzierte häufige Artikel')).toBeInTheDocument();
    expect(screen.getByText('Test inefficiency')).toBeInTheDocument();
    expect(screen.getByText('Mittel')).toBeInTheDocument();
  });

  it('handles item selection', () => {
    const onItemSelect = vi.fn();
    render(<WarehouseLayoutVisualization {...defaultProps} onItemSelect={onItemSelect} />);
    
    // Find and click on an item (this would be in the warehouse grid)
    // Note: This is a simplified test - in reality, we'd need to find the specific grid item
    const warehouseGrid = screen.getByTestId('warehouse-layout-visualization');
    expect(warehouseGrid).toBeInTheDocument();
  });

  it('displays item details when selected', () => {
    render(<WarehouseLayoutVisualization {...defaultProps} />);
    
    // The component should show item details when an item is selected
    // This would require simulating item selection first
  });

  it('shows optimization recommendations', () => {
    render(<WarehouseLayoutVisualization {...defaultProps} />);
    
    // Optimization recommendations should be visible in the layout
    // Look for optimization indicators
    const optimizationElements = screen.getAllByTitle(/Optimierungsempfehlung/i);
    expect(optimizationElements.length).toBeGreaterThanOrEqual(0);
  });

  it('handles empty inefficiencies gracefully', () => {
    const propsWithNoInefficiencies = {
      ...defaultProps,
      analysis: {
        ...mockAnalysis,
        inefficiencies: []
      }
    };
    
    render(<WarehouseLayoutVisualization {...propsWithNoInefficiencies} />);
    
    // Switch to inefficiencies tab
    const inefficienciesTab = screen.getByText('Ineffizienzen');
    fireEvent.click(inefficienciesTab);
    
    expect(screen.getByText('Keine Ineffizienzen erkannt')).toBeInTheDocument();
  });

  it('displays accessibility legend correctly', () => {
    render(<WarehouseLayoutVisualization {...defaultProps} />);
    
    expect(screen.getByText('Hohe Zugänglichkeit (8-10)')).toBeInTheDocument();
    expect(screen.getByText('Mittlere Zugänglichkeit (6-7)')).toBeInTheDocument();
    expect(screen.getByText('Niedrige Zugänglichkeit (4-5)')).toBeInTheDocument();
    expect(screen.getByText('Sehr niedrige Zugänglichkeit (1-3)')).toBeInTheDocument();
  });

  it('displays utilization heatmap legend', () => {
    render(<WarehouseLayoutVisualization {...defaultProps} />);
    
    // Switch to heatmap tab
    const heatmapTab = screen.getByText('Heatmap');
    fireEvent.click(heatmapTab);
    
    expect(screen.getByText('Überauslastung (90-100%)')).toBeInTheDocument();
    expect(screen.getByText('Hohe Auslastung (70-89%)')).toBeInTheDocument();
    expect(screen.getByText('Optimale Auslastung (50-69%)')).toBeInTheDocument();
    expect(screen.getByText('Niedrige Auslastung (0-49%)')).toBeInTheDocument();
  });

  it('applies custom className correctly', () => {
    const customClassName = 'custom-warehouse-class';
    render(<WarehouseLayoutVisualization {...defaultProps} className={customClassName} />);
    
    const component = screen.getByTestId('warehouse-layout-visualization');
    expect(component).toHaveClass(customClassName);
  });

  it('handles location selection callback', () => {
    const onLocationSelect = vi.fn();
    render(<WarehouseLayoutVisualization {...defaultProps} onLocationSelect={onLocationSelect} />);
    
    // This would require finding and clicking on a specific location in the grid
    // For now, we just verify the callback is passed correctly
    expect(onLocationSelect).toBeDefined();
  });
});