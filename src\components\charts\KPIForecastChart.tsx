/**
 * KPI Forecast Chart Component
 * 
 * Displays KPI forecasts with confidence intervals and trend indicators.
 */

import React from 'react';
import { Line, LineChart, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Area, AreaChart } from 'recharts';
import { TrendingUp, TrendingDown, Minus, Al<PERSON>Triangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import type { KPIForecast } from '@/types/predictive-analytics';
import { cn } from '@/lib/utils';

interface KPIForecastChartProps {
  forecast: KPIForecast;
  className?: string;
}

/**
 * KPI Forecast Chart Component
 */
export function KPIForecastChart({ forecast, className }: KPIForecastChartProps) {
  // Prepare chart data
  const chartData = [
    // Current value as starting point
    {
      timestamp: new Date().toISOString(),
      actual: forecast.current_value,
      predicted: forecast.current_value,
      confidence_lower: forecast.current_value,
      confidence_upper: forecast.current_value,
      is_current: true
    },
    // Forecast points
    ...forecast.predictions.map(point => ({
      timestamp: point.timestamp,
      actual: null,
      predicted: point.predicted_value,
      confidence_lower: point.confidence_interval_lower,
      confidence_upper: point.confidence_interval_upper,
      is_current: false
    }))
  ];

  // Format timestamp for display
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('de-DE', {
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get trend icon and color
  const getTrendIcon = () => {
    switch (forecast.trend_direction) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  // Get confidence color
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Chart configuration
  const chartConfig = {
    actual: {
      label: 'Aktuell',
      color: 'var(--chart-1)',
    },
    predicted: {
      label: 'Prognose',
      color: 'var(--chart-2)',
    },
    confidence_lower: {
      label: 'Konfidenzintervall (unten)',
      color: 'var(--chart-3)',
    },
    confidence_upper: {
      label: 'Konfidenzintervall (oben)',
      color: 'var(--chart-3)',
    },
  };

  return (
    <Card className={cn("bg-secondary-background text-foreground", className)} data-testid="kpi-forecast-chart">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">
            KPI: {forecast.kpi_id}
          </CardTitle>
          <div className="flex items-center gap-2">
            {getTrendIcon()}
            <Badge 
              variant="outline" 
              className={getConfidenceColor(forecast.overall_confidence)}
            >
              {Math.round(forecast.overall_confidence * 100)}% Konfidenz
            </Badge>
          </div>
        </div>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>Aktueller Wert: <strong>{forecast.current_value.toFixed(2)}</strong></span>
          <span>Modell: {forecast.model_used}</span>
          <span>Horizont: {forecast.forecast_horizon_hours}h</span>
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-64">
          <AreaChart
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 20,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="timestamp"
              tickFormatter={formatTimestamp}
              tick={{ fontSize: 12 }}
              interval="preserveStartEnd"
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => value.toFixed(1)}
            />
            
            {/* Confidence interval area */}
            <Area
              type="monotone"
              dataKey="confidence_upper"
              stroke="none"
              fill="var(--color-confidence_upper)"
              fillOpacity={0.1}
              stackId="confidence"
            />
            <Area
              type="monotone"
              dataKey="confidence_lower"
              stroke="none"
              fill="var(--color-confidence_lower)"
              fillOpacity={0.1}
              stackId="confidence"
            />
            
            {/* Actual values line */}
            <Line
              type="monotone"
              dataKey="actual"
              stroke="var(--color-actual)"
              strokeWidth={3}
              dot={{ fill: "var(--color-actual)", strokeWidth: 2, r: 4 }}
              connectNulls={false}
            />
            
            {/* Predicted values line */}
            <Line
              type="monotone"
              dataKey="predicted"
              stroke="var(--color-predicted)"
              strokeWidth={2}
              strokeDasharray="5 5"
              dot={{ fill: "var(--color-predicted)", strokeWidth: 2, r: 3 }}
            />
            
            <ChartTooltip
              content={({ active, payload, label }) => {
                if (!active || !payload?.length) return null;
                
                const data = payload[0]?.payload;
                if (!data) return null;
                
                return (
                  <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
                    <p className="font-medium mb-2">
                      {formatTimestamp(label as string)}
                    </p>
                    {data.actual !== null && (
                      <div className="flex items-center gap-2 text-sm">
                        <div className="w-3 h-3 rounded-full bg-chart-1"></div>
                        <span>Aktuell: {data.actual.toFixed(2)}</span>
                      </div>
                    )}
                    {data.predicted !== null && (
                      <>
                        <div className="flex items-center gap-2 text-sm">
                          <div className="w-3 h-3 rounded-full bg-chart-2"></div>
                          <span>Prognose: {data.predicted.toFixed(2)}</span>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Konfidenzintervall: {data.confidence_lower.toFixed(2)} - {data.confidence_upper.toFixed(2)}
                        </div>
                      </>
                    )}
                  </div>
                );
              }}
            />
          </AreaChart>
        </ChartContainer>
        
        {/* Forecast Summary */}
        <div className="mt-4 p-3 bg-muted rounded-lg">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <span className="font-medium">Trend:</span>
              <span className="capitalize">{forecast.trend_direction === 'up' ? 'Steigend' : forecast.trend_direction === 'down' ? 'Fallend' : 'Stabil'}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Generiert:</span>
              <span>{new Date(forecast.generated_at).toLocaleString('de-DE')}</span>
            </div>
          </div>
          
          {forecast.overall_confidence < 0.6 && (
            <div className="flex items-center gap-2 mt-2 text-amber-600">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-xs">
                Niedrige Konfidenz - Prognose mit Vorsicht interpretieren
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default KPIForecastChart;