"use strict";
/**
 * Backend In-Memory Cache Service
 *
 * Implementiert Server-seitiges Caching für Datenbankabfragen mit
 * intelligenter Invalidierung und Performance-Optimierung.
 */
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackendCacheKeyGenerator = exports.BackendCacheService = void 0;
exports.getBackendCache = getBackendCache;
exports.destroyBackendCache = destroyBackendCache;
/**
 * Backend Cache Service für Datenbankabfragen
 */
class BackendCacheService {
    constructor(config = {}) {
        this.cache = new Map();
        this.stats = {
            hits: 0,
            misses: 0,
            totalRequests: 0,
            hitRate: 0,
            memoryUsageMB: 0,
            entryCount: 0,
            evictions: 0,
            totalQueries: 0
        };
        this.cleanupTimer = null;
        this.config = {
            defaultTTL: 5 * 60 * 1000, // 5 Minuten Standard
            maxMemoryMB: 50, // 50MB max Memory
            maxEntries: 1000,
            cleanupInterval: 60 * 1000, // 1 Minute
            enableLogging: process.env.NODE_ENV === 'development',
            ...config
        };
        this.startCleanupTimer();
        this.log('Backend Cache Service initialisiert', undefined, `MaxMemory: ${this.config.maxMemoryMB}MB, MaxEntries: ${this.config.maxEntries}`);
    }
    /**
     * Cached Database Query Wrapper
     * @param key Cache-Schlüssel
     * @param queryFunction Datenbankabfrage-Funktion
     * @param ttl Cache TTL (optional)
     * @returns Cached oder frische Daten
     */
    async cachedQuery(key, queryFunction, ttl) {
        this.stats.totalRequests++;
        this.stats.totalQueries++;
        // Performance-Tracking starten
        const queryId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const queryType = this.extractQueryType(key);
        // Versuche Daten aus dem Cache zu laden
        const cachedData = this.get(key);
        if (cachedData !== undefined) {
            // Performance Monitoring für Cache Hit
            this.trackPerformanceHit(queryId, queryType, this.estimateDataSize(cachedData));
            return cachedData;
        }
        // Cache Miss - führe Datenbankabfrage durch
        try {
            const startTime = Date.now();
            const data = await queryFunction();
            const queryTime = Date.now() - startTime;
            const dataSize = this.estimateDataSize(data);
            // Speichere nur gültige Daten im Cache
            if (data !== null && data !== undefined) {
                this.set(key, data, ttl);
            }
            // Performance Monitoring für Cache Miss
            this.trackPerformanceMiss(queryId, queryType, queryTime, dataSize);
            this.log('DB Query executed', key, `Time: ${queryTime}ms`);
            return data;
        }
        catch (error) {
            // Performance Monitoring für Fehler
            this.trackPerformanceError(queryId, queryType, error);
            console.error(`[BACKEND-CACHE] Query failed for key: ${key}`, error);
            throw error;
        }
    }
    /**
     * Daten aus dem Cache abrufen
     */
    get(key) {
        const entry = this.cache.get(key);
        if (!entry) {
            this.stats.misses++;
            this.updateHitRate();
            return undefined;
        }
        // Prüfe TTL
        const now = Date.now();
        if (now > entry.timestamp + entry.ttl) {
            this.cache.delete(key);
            this.stats.misses++;
            this.updateHitRate();
            this.log('Cache EXPIRED', key);
            return undefined;
        }
        // Update Access-Metadaten
        entry.accessCount++;
        entry.lastAccessed = now;
        this.stats.hits++;
        this.updateHitRate();
        this.log('Cache HIT', key);
        return entry.data;
    }
    /**
     * Daten in den Cache einfügen
     */
    set(key, data, ttl) {
        const now = Date.now();
        const actualTTL = ttl || this.config.defaultTTL;
        const dataSize = this.estimateDataSize(data);
        // Prüfe Memory-Limits vor dem Einfügen
        this.enforceMemoryLimits(dataSize);
        const entry = {
            data,
            timestamp: now,
            ttl: actualTTL,
            accessCount: 1,
            lastAccessed: now,
            size: dataSize
        };
        this.cache.set(key, entry);
        this.updateStats();
        this.log('Cache SET', key, `TTL: ${actualTTL}ms, Size: ${Math.round(dataSize / 1024)}KB`);
    }
    /**
     * Cache-Einträge für bestimmte Datentypen invalidieren
     * @param dataTypes Array von Datentypen zum Invalidieren
     */
    invalidateByDataTypes(dataTypes) {
        let invalidatedCount = 0;
        for (const dataType of dataTypes) {
            invalidatedCount += this.deleteByPrefix(dataType);
        }
        if (invalidatedCount > 0) {
            this.log('Cache INVALIDATION', undefined, `${invalidatedCount} entries invalidated for types: ${dataTypes.join(', ')}`);
        }
        return invalidatedCount;
    }
    /**
     * Cache-Einträge mit Prefix löschen
     */
    deleteByPrefix(prefix) {
        let deletedCount = 0;
        for (const key of Array.from(this.cache.keys())) {
            if (key.startsWith(prefix)) {
                this.cache.delete(key);
                deletedCount++;
            }
        }
        if (deletedCount > 0) {
            this.updateStats();
        }
        return deletedCount;
    }
    /**
     * Memory-Limits durchsetzen
     */
    enforceMemoryLimits(newDataSize) {
        const currentMemoryMB = this.stats.memoryUsageMB;
        const newDataSizeMB = newDataSize / (1024 * 1024);
        // Prüfe ob neuer Eintrag Memory-Limit überschreiten würde
        if (currentMemoryMB + newDataSizeMB > this.config.maxMemoryMB) {
            this.evictByMemoryPressure(newDataSizeMB);
        }
        // Prüfe Entry-Limit
        if (this.cache.size >= this.config.maxEntries) {
            this.evictLeastUsed();
        }
    }
    /**
     * Memory-basierte Eviction
     */
    evictByMemoryPressure(requiredMB) {
        const targetMemoryMB = this.config.maxMemoryMB * 0.8; // Evict bis 80% der Kapazität
        let evictedCount = 0;
        // Sortiere Einträge nach Access-Pattern (LRU + Größe)
        const entries = Array.from(this.cache.entries())
            .map(([key, entry]) => ({
            key,
            entry,
            score: entry.lastAccessed + (entry.size / 1024) // Größere Items haben höhere Wahrscheinlichkeit evicted zu werden
        }))
            .sort((a, b) => a.score - b.score);
        for (const { key } of entries) {
            if (this.stats.memoryUsageMB <= targetMemoryMB)
                break;
            this.cache.delete(key);
            evictedCount++;
            this.stats.evictions++;
            this.updateStats();
        }
        if (evictedCount > 0) {
            this.log('Memory EVICTION', undefined, `${evictedCount} entries evicted, Memory: ${this.stats.memoryUsageMB.toFixed(1)}MB`);
        }
    }
    /**
     * LRU Eviction
     */
    evictLeastUsed() {
        if (this.cache.size === 0)
            return;
        let oldestKey = '';
        let oldestAccess = Date.now();
        for (const [key, entry] of Array.from(this.cache.entries())) {
            if (entry.lastAccessed < oldestAccess) {
                oldestAccess = entry.lastAccessed;
                oldestKey = key;
            }
        }
        if (oldestKey) {
            this.cache.delete(oldestKey);
            this.stats.evictions++;
            this.updateStats();
            this.log('LRU EVICTION', oldestKey);
        }
    }
    /**
     * Schätze Datengröße für Memory-Management
     */
    estimateDataSize(data) {
        try {
            // Bessere Größenschätzung als JSON.stringify
            if (Array.isArray(data)) {
                return data.length * 100; // Schätzung: 100 Bytes pro Array-Element
            }
            else if (typeof data === 'object' && data !== null) {
                return Object.keys(data).length * 50; // Schätzung: 50 Bytes pro Object-Property
            }
            else {
                return JSON.stringify(data).length * 2; // UTF-16
            }
        }
        catch (_b) {
            return 1024; // Fallback: 1KB
        }
    }
    /**
     * Cache-Aufräumung
     */
    cleanup() {
        const now = Date.now();
        let expiredCount = 0;
        for (const [key, entry] of Array.from(this.cache.entries())) {
            if (now > entry.timestamp + entry.ttl) {
                this.cache.delete(key);
                expiredCount++;
            }
        }
        if (expiredCount > 0) {
            this.updateStats();
            this.log('Cache CLEANUP', undefined, `${expiredCount} expired entries removed`);
        }
        return expiredCount;
    }
    /**
     * Cache-Statistiken abrufen
     */
    getStats() {
        this.updateStats(); // Aktuelle Stats sicherstellen
        return { ...this.stats };
    }
    /**
     * Cache-Service beenden
     */
    destroy() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        this.cache.clear();
        this.log('Backend Cache Service destroyed');
    }
    /**
     * Private Hilfsmethoden
     */
    updateHitRate() {
        this.stats.hitRate = this.stats.totalRequests > 0
            ? (this.stats.hits / this.stats.totalRequests) * 100
            : 0;
    }
    updateStats() {
        this.stats.entryCount = this.cache.size;
        // Berechne Gesamt-Memory-Nutzung
        let totalSize = 0;
        for (const entry of this.cache.values()) {
            totalSize += entry.size;
        }
        this.stats.memoryUsageMB = totalSize / (1024 * 1024);
        this.updateHitRate();
    }
    startCleanupTimer() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        this.cleanupTimer = setInterval(() => {
            this.cleanup();
        }, this.config.cleanupInterval);
    }
    log(action, key, details) {
        if (!this.config.enableLogging)
            return;
        const logMessage = [
            `[BACKEND-CACHE] ${action}`,
            key && `Key: ${key}`,
            details && `Details: ${details}`,
            `(${this.stats.entryCount} entries, ${this.stats.hitRate.toFixed(1)}% hit rate, ${this.stats.memoryUsageMB.toFixed(1)}MB)`
        ].filter(Boolean).join(' | ');
        console.log(logMessage);
    }
    /**
     * Performance Monitoring Helper Methods
     */
    extractQueryType(key) {
        // Extrahiere Query-Typ aus Cache-Key (z.B. "db:dispatch:getServiceLevelData" -> "dispatch.getServiceLevelData")
        const parts = key.split(':');
        if (parts.length >= 3) {
            return `${parts[1]}.${parts[2]}`;
        }
        return parts[1] || 'unknown';
    }
    trackPerformanceHit(queryId, queryType, dataSize) {
        // Lazy-load Performance Monitor um zirkuläre Abhängigkeiten zu vermeiden
        try {
            const { performanceMonitor } = require('./performance-monitor.service');
            const tracker = performanceMonitor.trackQuery(queryId, queryType);
            tracker.recordCacheHit(dataSize);
        }
        catch (error) {
            // Ignore errors from performance monitoring to not affect core functionality
            if (this.config.enableLogging) {
                console.debug('[BACKEND-CACHE] Performance monitoring fehler (Hit):', error);
            }
        }
    }
    trackPerformanceMiss(queryId, queryType, queryTime, dataSize) {
        try {
            const { performanceMonitor } = require('./performance-monitor.service');
            const tracker = performanceMonitor.trackQuery(queryId, queryType);
            tracker.recordCacheMiss();
            tracker.complete(dataSize);
        }
        catch (error) {
            if (this.config.enableLogging) {
                console.debug('[BACKEND-CACHE] Performance monitoring fehler (Miss):', error);
            }
        }
    }
    trackPerformanceError(queryId, queryType, error) {
        try {
            const { performanceMonitor } = require('./performance-monitor.service');
            const tracker = performanceMonitor.trackQuery(queryId, queryType);
            const errorType = error instanceof Error ? error.constructor.name : 'UnknownError';
            tracker.recordError(errorType);
        }
        catch (perfError) {
            if (this.config.enableLogging) {
                console.debug('[BACKEND-CACHE] Performance monitoring fehler (Error):', perfError);
            }
        }
    }
}
exports.BackendCacheService = BackendCacheService;
/**
 * Cache-Schlüssel-Generator für Backend
 */
class BackendCacheKeyGenerator {
    /**
     * Generiert Cache-Schlüssel für Datenbankabfragen
     */
    static forQuery(table, method, params) {
        const baseKey = `db:${table}:${method}`;
        if (!params || Object.keys(params).length === 0) {
            return baseKey;
        }
        // Parameter sortieren für konsistente Schlüssel
        const sortedParams = Object.keys(params)
            .sort()
            .filter(key => params[key] !== undefined) // Ignoriere undefined Werte
            .map(key => `${key}=${String(params[key])}`)
            .join('&');
        return sortedParams ? `${baseKey}:${sortedParams}` : baseKey;
    }
    /**
     * Cache-Schlüssel für Service-Methoden
     */
    static forService(service, method, params) {
        return this.forQuery(service, method, params);
    }
    /**
     * Cache-Invalidierungskeys für Datentypen
     */
    static getInvalidationKeys(dataTypes) {
        return dataTypes.map(type => `db:${type}`);
    }
}
exports.BackendCacheKeyGenerator = BackendCacheKeyGenerator;
_a = BackendCacheKeyGenerator;
/**
 * Repository-spezifische Cache-Key-Generatoren
 */
BackendCacheKeyGenerator.warehouse = {
    lagerauslastung200: (dateRange) => _a.forQuery('warehouse', 'lagerauslastung200', dateRange),
    lagerauslastung240: (dateRange) => _a.forQuery('warehouse', 'lagerauslastung240', dateRange),
    aril: (dateRange) => _a.forQuery('warehouse', 'aril', dateRange),
    atrl: (dateRange) => _a.forQuery('warehouse', 'atrl', dateRange)
};
BackendCacheKeyGenerator.cutting = {
    ablaengerei: (dateRange) => _a.forQuery('cutting', 'ablaengerei', dateRange),
    weData: (dateRange) => _a.forQuery('cutting', 'weData', dateRange)
};
BackendCacheKeyGenerator.dispatch = {
    serviceLevel: (dateRange) => _a.forQuery('dispatch', 'serviceLevel', dateRange),
    performance: (dateRange) => _a.forQuery('dispatch', 'performance', dateRange),
    picking: (dateRange) => _a.forQuery('dispatch', 'picking', dateRange)
};
// Singleton Cache Instance für Backend
let backendCacheInstance = null;
/**
 * Globale Backend-Cache-Instanz abrufen
 */
function getBackendCache(config) {
    if (!backendCacheInstance) {
        backendCacheInstance = new BackendCacheService(config);
    }
    return backendCacheInstance;
}
/**
 * Backend-Cache zerstören
 */
function destroyBackendCache() {
    if (backendCacheInstance) {
        backendCacheInstance.destroy();
        backendCacheInstance = null;
    }
}
