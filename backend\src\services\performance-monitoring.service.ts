/**
 * Performance Monitoring Service
 * 
 * Monitors and tracks performance metrics for the AI chatbot database integration.
 * Provides insights into query performance, intent recognition accuracy, and system health.
 */

import { EventEmitter } from 'events';

export interface PerformanceMetric {
  id: string;
  timestamp: Date;
  type: 'query' | 'intent' | 'enrichment' | 'response';
  duration: number;
  success: boolean;
  details: Record<string, any>;
}

export interface QueryPerformanceMetric extends PerformanceMetric {
  type: 'query';
  details: {
    queryType: 'stoerungen' | 'dispatch' | 'cutting' | 'combined';
    dataSize: number;
    cacheHit: boolean;
    retryCount: number;
    errorType?: string;
  };
}

export interface IntentRecognitionMetric extends PerformanceMetric {
  type: 'intent';
  details: {
    message: string;
    detectedIntents: string[];
    confidence: number;
    accuracy?: number; // Set when validation is available
    keywords: string[];
  };
}

export interface EnrichmentPerformanceMetric extends PerformanceMetric {
  type: 'enrichment';
  details: {
    requestId: string;
    intentCount: number;
    queryCount: number;
    successfulQueries: number;
    fallbackUsed: boolean;
    dataTypes: string[];
  };
}

export interface ResponseTimeMetric extends PerformanceMetric {
  type: 'response';
  details: {
    endpoint: string;
    enriched: boolean;
    totalTime: number;
    enrichmentTime: number;
    llmTime: number;
    dataSize: number;
  };
}

export interface PerformanceStats {
  totalRequests: number;
  averageResponseTime: number;
  successRate: number;
  cacheHitRate: number;
  intentAccuracy: number;
  queryPerformance: {
    stoerungen: { avg: number; count: number; successRate: number };
    dispatch: { avg: number; count: number; successRate: number };
    cutting: { avg: number; count: number; successRate: number };
  };
  timeRanges: {
    last5min: PerformanceStats;
    last1hour: PerformanceStats;
    last24hours: PerformanceStats;
  };
}

export class PerformanceMonitoringService extends EventEmitter {
  private metrics: PerformanceMetric[] = [];
  private readonly maxMetrics = 10000; // Keep last 10k metrics
  private readonly cleanupInterval = 60 * 60 * 1000; // 1 hour
  private cleanupTimer?: NodeJS.Timeout;

  constructor() {
    super();
    this.startCleanupTimer();
  }

  /**
   * Record a query performance metric
   */
  recordQueryPerformance(
    queryType: 'stoerungen' | 'dispatch' | 'cutting' | 'combined',
    duration: number,
    success: boolean,
    details: {
      dataSize?: number;
      cacheHit?: boolean;
      retryCount?: number;
      errorType?: string;
    } = {}
  ): void {
    const metric: QueryPerformanceMetric = {
      id: this.generateId(),
      timestamp: new Date(),
      type: 'query',
      duration,
      success,
      details: {
        queryType,
        dataSize: details.dataSize || 0,
        cacheHit: details.cacheHit || false,
        retryCount: details.retryCount || 0,
        errorType: details.errorType
      }
    };

    this.addMetric(metric);
    this.emit('queryPerformance', metric);
  }

  /**
   * Record intent recognition performance
   */
  recordIntentRecognition(
    message: string,
    detectedIntents: string[],
    confidence: number,
    duration: number,
    keywords: string[] = [],
    accuracy?: number
  ): void {
    const metric: IntentRecognitionMetric = {
      id: this.generateId(),
      timestamp: new Date(),
      type: 'intent',
      duration,
      success: detectedIntents.length > 0,
      details: {
        message: message.substring(0, 200), // Truncate for privacy
        detectedIntents,
        confidence,
        accuracy,
        keywords
      }
    };

    this.addMetric(metric);
    this.emit('intentRecognition', metric);
  }

  /**
   * Record data enrichment performance
   */
  recordEnrichmentPerformance(
    requestId: string,
    duration: number,
    success: boolean,
    details: {
      intentCount: number;
      queryCount: number;
      successfulQueries: number;
      fallbackUsed: boolean;
      dataTypes: string[];
    }
  ): void {
    const metric: EnrichmentPerformanceMetric = {
      id: this.generateId(),
      timestamp: new Date(),
      type: 'enrichment',
      duration,
      success,
      details: {
        requestId,
        ...details
      }
    };

    this.addMetric(metric);
    this.emit('enrichmentPerformance', metric);
  }

  /**
   * Record overall response time performance
   */
  recordResponseTime(
    endpoint: string,
    enriched: boolean,
    totalTime: number,
    enrichmentTime: number,
    llmTime: number,
    dataSize: number = 0
  ): void {
    const metric: ResponseTimeMetric = {
      id: this.generateId(),
      timestamp: new Date(),
      type: 'response',
      duration: totalTime,
      success: totalTime < (enriched ? 3000 : 2000), // Success based on requirements
      details: {
        endpoint,
        enriched,
        totalTime,
        enrichmentTime,
        llmTime,
        dataSize
      }
    };

    this.addMetric(metric);
    this.emit('responseTime', metric);
  }

  /**
   * Get comprehensive performance statistics
   */
  getPerformanceStats(timeRange?: { start: Date; end: Date }): PerformanceStats {
    const filteredMetrics = timeRange 
      ? this.metrics.filter(m => m.timestamp >= timeRange.start && m.timestamp <= timeRange.end)
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return this.getEmptyStats();
    }

    const responseMetrics = filteredMetrics.filter(m => m.type === 'response') as ResponseTimeMetric[];
    const queryMetrics = filteredMetrics.filter(m => m.type === 'query') as QueryPerformanceMetric[];
    const intentMetrics = filteredMetrics.filter(m => m.type === 'intent') as IntentRecognitionMetric[];

    const totalRequests = responseMetrics.length;
    const averageResponseTime = totalRequests > 0 
      ? responseMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests 
      : 0;
    
    const successRate = totalRequests > 0 
      ? responseMetrics.filter(m => m.success).length / totalRequests 
      : 0;

    const cacheHitRate = queryMetrics.length > 0
      ? queryMetrics.filter(m => m.details.cacheHit).length / queryMetrics.length
      : 0;

    const intentAccuracy = intentMetrics.length > 0
      ? intentMetrics
          .filter(m => m.details.accuracy !== undefined)
          .reduce((sum, m) => sum + (m.details.accuracy || 0), 0) / 
        intentMetrics.filter(m => m.details.accuracy !== undefined).length
      : 0;

    const queryPerformance = {
      stoerungen: this.getQueryTypeStats(queryMetrics, 'stoerungen'),
      dispatch: this.getQueryTypeStats(queryMetrics, 'dispatch'),
      cutting: this.getQueryTypeStats(queryMetrics, 'cutting')
    };

    // Calculate time range stats (avoid recursion by not including nested timeRanges)
    const now = new Date();
    const timeRanges = {
      last5min: {
        ...this.getBasicPerformanceStats({
          start: new Date(now.getTime() - 5 * 60 * 1000),
          end: now
        }),
        timeRanges: {} as any // Empty to avoid recursion
      },
      last1hour: {
        ...this.getBasicPerformanceStats({
          start: new Date(now.getTime() - 60 * 60 * 1000),
          end: now
        }),
        timeRanges: {} as any // Empty to avoid recursion
      },
      last24hours: {
        ...this.getBasicPerformanceStats({
          start: new Date(now.getTime() - 24 * 60 * 60 * 1000),
          end: now
        }),
        timeRanges: {} as any // Empty to avoid recursion
      }
    };

    return {
      totalRequests,
      averageResponseTime,
      successRate,
      cacheHitRate,
      intentAccuracy,
      queryPerformance,
      timeRanges
    };
  }

  /**
   * Get query performance by type
   */
  getQueryPerformanceByType(queryType: 'stoerungen' | 'dispatch' | 'cutting'): {
    averageTime: number;
    successRate: number;
    cacheHitRate: number;
    totalQueries: number;
    recentTrend: 'improving' | 'stable' | 'degrading';
  } {
    const queryMetrics = this.metrics
      .filter(m => m.type === 'query') as QueryPerformanceMetric[];
    
    const typeMetrics = queryMetrics.filter(m => m.details.queryType === queryType);
    
    if (typeMetrics.length === 0) {
      return {
        averageTime: 0,
        successRate: 0,
        cacheHitRate: 0,
        totalQueries: 0,
        recentTrend: 'stable'
      };
    }

    const averageTime = typeMetrics.reduce((sum, m) => sum + m.duration, 0) / typeMetrics.length;
    const successRate = typeMetrics.filter(m => m.success).length / typeMetrics.length;
    const cacheHitRate = typeMetrics.filter(m => m.details.cacheHit).length / typeMetrics.length;

    // Calculate trend (compare last 25% vs previous 25%)
    const sortedMetrics = typeMetrics.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    const quarterSize = Math.floor(sortedMetrics.length / 4);
    
    let recentTrend: 'improving' | 'stable' | 'degrading' = 'stable';
    
    if (quarterSize > 0) {
      const recentMetrics = sortedMetrics.slice(-quarterSize);
      const previousMetrics = sortedMetrics.slice(-quarterSize * 2, -quarterSize);
      
      if (previousMetrics.length > 0) {
        const recentAvg = recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length;
        const previousAvg = previousMetrics.reduce((sum, m) => sum + m.duration, 0) / previousMetrics.length;
        
        const improvement = (previousAvg - recentAvg) / previousAvg;
        
        if (improvement > 0.1) recentTrend = 'improving';
        else if (improvement < -0.1) recentTrend = 'degrading';
      }
    }

    return {
      averageTime,
      successRate,
      cacheHitRate,
      totalQueries: typeMetrics.length,
      recentTrend
    };
  }

  /**
   * Get intent recognition accuracy metrics
   */
  getIntentAccuracyMetrics(): {
    overallAccuracy: number;
    accuracyByIntent: Record<string, number>;
    confidenceDistribution: { low: number; medium: number; high: number };
    totalSamples: number;
  } {
    const intentMetrics = this.metrics
      .filter(m => m.type === 'intent') as IntentRecognitionMetric[];

    if (intentMetrics.length === 0) {
      return {
        overallAccuracy: 0,
        accuracyByIntent: {},
        confidenceDistribution: { low: 0, medium: 0, high: 0 },
        totalSamples: 0
      };
    }

    const metricsWithAccuracy = intentMetrics.filter(m => m.details.accuracy !== undefined);
    const overallAccuracy = metricsWithAccuracy.length > 0
      ? metricsWithAccuracy.reduce((sum, m) => sum + (m.details.accuracy || 0), 0) / metricsWithAccuracy.length
      : 0;

    // Calculate accuracy by intent type
    const accuracyByIntent: Record<string, number> = {};
    const intentGroups = new Map<string, number[]>();
    
    metricsWithAccuracy.forEach(metric => {
      metric.details.detectedIntents.forEach(intent => {
        if (!intentGroups.has(intent)) {
          intentGroups.set(intent, []);
        }
        intentGroups.get(intent)!.push(metric.details.accuracy || 0);
      });
    });

    intentGroups.forEach((accuracies, intent) => {
      accuracyByIntent[intent] = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    });

    // Calculate confidence distribution
    const confidenceDistribution = {
      low: intentMetrics.filter(m => m.details.confidence < 0.5).length,
      medium: intentMetrics.filter(m => m.details.confidence >= 0.5 && m.details.confidence < 0.8).length,
      high: intentMetrics.filter(m => m.details.confidence >= 0.8).length
    };

    return {
      overallAccuracy,
      accuracyByIntent,
      confidenceDistribution,
      totalSamples: intentMetrics.length
    };
  }

  /**
   * Get performance alerts based on thresholds
   */
  getPerformanceAlerts(): Array<{
    type: 'warning' | 'error';
    message: string;
    metric: string;
    value: number;
    threshold: number;
    timestamp: Date;
  }> {
    const alerts: Array<{
      type: 'warning' | 'error';
      message: string;
      metric: string;
      value: number;
      threshold: number;
      timestamp: Date;
    }> = [];

    const stats = this.getPerformanceStats();

    // Response time alerts
    if (stats.averageResponseTime > 3000) {
      alerts.push({
        type: 'error',
        message: 'Average response time exceeds 3 seconds',
        metric: 'averageResponseTime',
        value: stats.averageResponseTime,
        threshold: 3000,
        timestamp: new Date()
      });
    } else if (stats.averageResponseTime > 2500) {
      alerts.push({
        type: 'warning',
        message: 'Average response time approaching threshold',
        metric: 'averageResponseTime',
        value: stats.averageResponseTime,
        threshold: 2500,
        timestamp: new Date()
      });
    }

    // Success rate alerts
    if (stats.successRate < 0.9) {
      alerts.push({
        type: 'error',
        message: 'Success rate below 90%',
        metric: 'successRate',
        value: stats.successRate,
        threshold: 0.9,
        timestamp: new Date()
      });
    } else if (stats.successRate < 0.95) {
      alerts.push({
        type: 'warning',
        message: 'Success rate below 95%',
        metric: 'successRate',
        value: stats.successRate,
        threshold: 0.95,
        timestamp: new Date()
      });
    }

    // Cache hit rate alerts
    if (stats.cacheHitRate < 0.3) {
      alerts.push({
        type: 'warning',
        message: 'Cache hit rate is low, consider cache optimization',
        metric: 'cacheHitRate',
        value: stats.cacheHitRate,
        threshold: 0.3,
        timestamp: new Date()
      });
    }

    return alerts;
  }

  /**
   * Clear all metrics (useful for testing)
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['id', 'timestamp', 'type', 'duration', 'success', 'details'];
      const rows = this.metrics.map(m => [
        m.id,
        m.timestamp.toISOString(),
        m.type,
        m.duration.toString(),
        m.success.toString(),
        JSON.stringify(m.details)
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    return JSON.stringify(this.metrics, null, 2);
  }

  // Private methods

  private addMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);
    
    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  private generateId(): string {
    return `perf_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  private getQueryTypeStats(
    queryMetrics: QueryPerformanceMetric[], 
    queryType: 'stoerungen' | 'dispatch' | 'cutting'
  ): { avg: number; count: number; successRate: number } {
    const typeMetrics = queryMetrics.filter(m => m.details.queryType === queryType);
    
    if (typeMetrics.length === 0) {
      return { avg: 0, count: 0, successRate: 0 };
    }

    const avg = typeMetrics.reduce((sum, m) => sum + m.duration, 0) / typeMetrics.length;
    const successRate = typeMetrics.filter(m => m.success).length / typeMetrics.length;

    return { avg, count: typeMetrics.length, successRate };
  }

  /**
   * Get basic performance statistics without nested time ranges (to avoid recursion)
   */
  private getBasicPerformanceStats(timeRange?: { start: Date; end: Date }): Omit<PerformanceStats, 'timeRanges'> {
    const filteredMetrics = timeRange 
      ? this.metrics.filter(m => m.timestamp >= timeRange.start && m.timestamp <= timeRange.end)
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        successRate: 0,
        cacheHitRate: 0,
        intentAccuracy: 0,
        queryPerformance: {
          stoerungen: { avg: 0, count: 0, successRate: 0 },
          dispatch: { avg: 0, count: 0, successRate: 0 },
          cutting: { avg: 0, count: 0, successRate: 0 }
        }
      };
    }

    const responseMetrics = filteredMetrics.filter(m => m.type === 'response') as ResponseTimeMetric[];
    const queryMetrics = filteredMetrics.filter(m => m.type === 'query') as QueryPerformanceMetric[];
    const intentMetrics = filteredMetrics.filter(m => m.type === 'intent') as IntentRecognitionMetric[];

    const totalRequests = responseMetrics.length;
    const averageResponseTime = totalRequests > 0 
      ? responseMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests 
      : 0;
    
    const successRate = totalRequests > 0 
      ? responseMetrics.filter(m => m.success).length / totalRequests 
      : 0;

    const cacheHitRate = queryMetrics.length > 0
      ? queryMetrics.filter(m => m.details.cacheHit).length / queryMetrics.length
      : 0;

    const intentAccuracy = intentMetrics.length > 0
      ? intentMetrics
          .filter(m => m.details.accuracy !== undefined)
          .reduce((sum, m) => sum + (m.details.accuracy || 0), 0) / 
        intentMetrics.filter(m => m.details.accuracy !== undefined).length
      : 0;

    const queryPerformance = {
      stoerungen: this.getQueryTypeStats(queryMetrics, 'stoerungen'),
      dispatch: this.getQueryTypeStats(queryMetrics, 'dispatch'),
      cutting: this.getQueryTypeStats(queryMetrics, 'cutting')
    };

    return {
      totalRequests,
      averageResponseTime,
      successRate,
      cacheHitRate,
      intentAccuracy,
      queryPerformance
    };
  }

  private getEmptyStats(): PerformanceStats {
    return {
      totalRequests: 0,
      averageResponseTime: 0,
      successRate: 0,
      cacheHitRate: 0,
      intentAccuracy: 0,
      queryPerformance: {
        stoerungen: { avg: 0, count: 0, successRate: 0 },
        dispatch: { avg: 0, count: 0, successRate: 0 },
        cutting: { avg: 0, count: 0, successRate: 0 }
      },
      timeRanges: {
        last5min: {} as PerformanceStats,
        last1hour: {} as PerformanceStats,
        last24hours: {} as PerformanceStats
      }
    };
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
      this.metrics = this.metrics.filter(m => m.timestamp > cutoffTime);
    }, this.cleanupInterval);
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.removeAllListeners();
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitoringService();
export default performanceMonitor;