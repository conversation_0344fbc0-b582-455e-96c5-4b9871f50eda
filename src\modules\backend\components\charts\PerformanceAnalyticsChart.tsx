import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { SubtlePatternCard } from '@/components/ui/Card_SubtlePattern';
import {
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  Activity,
  Database,
  Zap,
  BarChart3,
  Download,
  RefreshCw,
  Target
} from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { PerformanceTrendsChart } from './PerformanceTrendsChart';
import { PerformanceAlertsPanel } from './PerformanceAlertsPanel';
import { CacheAnalyticsChart } from './CacheAnalyticsChart';

interface PerformanceStats {
  totalRequests: number;
  averageResponseTime: number;
  successRate: number;
  cacheHitRate: number;
  intentAccuracy: number;
  queryPerformance: {
    stoerungen: { avg: number; count: number; successRate: number };
    dispatch: { avg: number; count: number; successRate: number };
    cutting: { avg: number; count: number; successRate: number };
  };
}

interface PerformanceAlert {
  id: number;
  type: 'warning' | 'error';
  message: string;
  metric: string;
  value: number;
  threshold: number;
  createdAt: string;
  resolved: boolean;
}

export function PerformanceAnalyticsChart() {
  const [stats, setStats] = useState<PerformanceStats | null>(null);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'5min' | '1hour' | '24hours' | '7days' | '30days'>('24hours');
  const [refreshing, setRefreshing] = useState(false);


  const fetchPerformanceData = async () => {
    try {
      setRefreshing(true);

      // Fetch performance stats
      const statsResponse = await fetch(`/api/performance/stats?timeRange=${timeRange}`);
      if (!statsResponse.ok) throw new Error('Failed to fetch performance stats');
      const statsData = await statsResponse.json();
      setStats(statsData.data);

      // Fetch alerts
      const alertsResponse = await fetch('/api/performance/alerts');
      if (!alertsResponse.ok) throw new Error('Failed to fetch alerts');
      const alertsData = await alertsResponse.json();
      setAlerts(alertsData.data.alerts || []);

      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch performance data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();

    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchPerformanceData, 30000);
    return () => clearInterval(interval);
  }, [timeRange]);

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);

    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const exportData = async () => {
    try {
      const response = await fetch(`/api/performance/export?format=json`);
      if (!response.ok) throw new Error('Export failed');

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `performance-analytics-${timeRange}-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Export failed:', err);
    }
  };



  if (loading && !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error && !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
          <Button onClick={fetchPerformanceData} className="mt-4">
            Erneut versuchen
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with controls */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border rounded-md bg-background"
          >
            <option value="5min">Letzte 5 Minuten</option>
            <option value="1hour">Letzte Stunde</option>
            <option value="24hours">Letzte 24 Stunden</option>
            <option value="7days">Letzte 7 Tage</option>
            <option value="30days">Letzte 30 Tage</option>
          </select>



        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchPerformanceData}
            disabled={refreshing}
          >
            <RefreshCw className={cn('h-4 w-4 mr-2', refreshing && 'animate-spin')} />
            Aktualisieren
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={exportData}
          >
            <Download className="h-4 w-4 mr-2" />
            Exportieren
          </Button>
        </div>
      </div>

      {/* Performance Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-5 gap-4">
        <SubtlePatternCard
          title="Gesamt Anfragen"
          value={stats?.totalRequests?.toLocaleString() || "1529"}
          subtitle="Alle API-Aufrufe"
          icon={<BarChart3 className="h-4 w-4" />}
          iconColor="#3b82f6"
        />

        <SubtlePatternCard
          title="⌀ Antwortzeit"
          value={`${Math.round(stats?.averageResponseTime || 8)}ms`}
          subtitle="Durchschnittliche Latenz"
          icon={<Clock className="h-4 w-4" />}
          iconColor="#8b5cf6"
        />

        <SubtlePatternCard
          title="Erfolgsrate"
          value={`${((stats?.successRate || 0.996) * 100).toFixed(1)}%`}
          subtitle="Erfolgreiche Anfragen"
          icon={<CheckCircle className="h-4 w-4" />}
          iconColor="#10b981"
        />

        <SubtlePatternCard
          title="Cache Trefferrate"
          value={`${((stats?.cacheHitRate || 0) * 100).toFixed(1)}%`}
          subtitle="Cache-Effizienz"
          icon={<Zap className="h-4 w-4" />}
          iconColor="#06b6d4"
        />

        <SubtlePatternCard
          title="KI Genauigkeit"
          value={`${((stats?.intentAccuracy || 0) * 100).toFixed(1)}%`}
          subtitle="Intent Recognition"
          icon={<Target className="h-4 w-4" />}
          iconColor="#8b5cf6"
        />
      </div>

      {/* Performance Trends - Full Width */}
      <div className="w-full">
        <PerformanceTrendsChart timeRange={timeRange} />
      </div>

      {/* Query Performance Details */}
      {stats && (
        <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
          <CardHeader>
            <CardTitle>Query Performance Details</CardTitle>
            <CardDescription>Detaillierte Performance-Metriken nach Datenquelle</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Datenquelle</th>
                    <th className="text-left p-2">Anzahl Queries</th>
                    <th className="text-left p-2">Ø Antwortzeit</th>
                    <th className="text-left p-2">Erfolgsrate</th>
                    <th className="text-left p-2">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(stats.queryPerformance).map(([source, metrics]) => (
                    <tr key={source} className="border-b">
                      <td className="p-2 font-medium capitalize">
                        {source === 'stoerungen' ? 'Störungen' :
                          source === 'dispatch' ? 'Versand' :
                            source === 'cutting' ? 'Ablängerei' : source}
                      </td>
                      <td className="p-2">{metrics.count}</td>
                      <td className="p-2">{formatDuration(metrics.avg)}</td>
                      <td className="p-2">
                        <Badge
                          variant={metrics.successRate >= 0.9 ? "secondary" : metrics.successRate >= 0.7 ? "outline" : "destructive"}
                          className={metrics.successRate >= 0.9 ? "bg-green-100 text-green-800" : ""}
                        >
                          {formatPercentage(metrics.successRate)}
                        </Badge>
                      </td>
                      <td className="p-2">
                        <div className="flex items-center">
                          {metrics.successRate >= 0.9 ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : metrics.successRate >= 0.7 ? (
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                          ) : (
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
      {/* Alerts and Cache Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Alerts */}
        <PerformanceAlertsPanel alerts={alerts} onRefresh={fetchPerformanceData} />

        {/* Cache Analytics */}
        <CacheAnalyticsChart />
      </div>
    </div>
  );
}