/**
 * Supply Chain Analytics Service
 * 
 * AI-powered supply chain analytics service that provides performance monitoring,
 * supplier scoring and ranking, delivery predictions, and optimization recommendations.
 */

import { AIBaseService, AIServiceConfig } from '../base/AIBaseService';
import { AIServiceError } from '../types';
import { SupplierRepository } from '@/repositories/supplier.repository';
import { DeliveryRepository } from '@/repositories/delivery.repository';
import {
    SupplyChainAnalytics,
    SupplierPerformanceAnalytics,
    DeliveryPerformanceAnalytics,
    RiskAnalytics,
    TrendAnalysis,
    PerformanceMetrics,
    DelayReason,
    SeasonalPattern,
    RiskTrend,
    TrendDataPoint
} from '@/types/supply-chain-optimization';

/**
 * Configuration for supply chain analytics service
 */
export interface SupplyChainAnalyticsServiceConfig extends AIServiceConfig {
    monitoringInterval?: number; // Minutes between monitoring cycles
    alertThresholds?: {
        riskScore: number;
        onTimeRate: number;
        qualityRate: number;
        deliveryDelay: number; // days
    };
    trendAnalysisPeriod?: number; // Days to analyze for trends
    performanceWeights?: {
        onTimeDelivery: number;
        quality: number;
        cost: number;
        reliability: number;
    };
}

/**
 * Supply chain alert types
 */
export interface SupplyChainAlert {
    alertId: string;
    type: 'risk_increase' | 'performance_decline' | 'delivery_delay' | 'quality_issue' | 'cost_spike';
    severity: 'low' | 'medium' | 'high' | 'critical';
    supplierId?: string;
    supplierName?: string;
    message: string;
    details: string;
    recommendations: string[];
    createdAt: Date;
    acknowledged: boolean;
}

/**
 * Supply chain recommendation types
 */
export interface SupplyChainRecommendation {
    recommendationId: string;
    type: 'supplier_diversification' | 'inventory_optimization' | 'route_optimization' | 'risk_mitigation';
    priority: 'low' | 'medium' | 'high';
    title: string;
    description: string;
    expectedBenefit: string;
    implementationSteps: string[];
    estimatedCost: number;
    estimatedSavings: number;
    timeframe: string;
    createdAt: Date;
}

/**
 * Supply Chain Analytics Service
 * 
 * Provides comprehensive supply chain analytics including:
 * - Real-time performance monitoring
 * - Supplier performance scoring and ranking
 * - Delivery prediction and risk assessment
 * - Automated alerts and recommendations
 * - Trend analysis and forecasting
 */
export class SupplyChainAnalyticsService extends AIBaseService {
    readonly serviceName = 'SupplyChainAnalyticsService';

    private supplierRepository: SupplierRepository;
    private deliveryRepository: DeliveryRepository;
    private analyticsConfig: SupplyChainAnalyticsServiceConfig;
    private monitoringTimer: NodeJS.Timeout | null = null;
    private alertsCache: Map<string, SupplyChainAlert> = new Map();
    private recommendationsCache: Map<string, SupplyChainRecommendation> = new Map();
    private lastAnalysisRun: Date | null = null;
    protected status: string = 'not_initialized';
    protected logger = {
        info: (msg: string, data?: any) => this.log(msg, data),
        error: (msg: string, data?: any) => this.log(msg, data)
    };

    /**
     * Get health status of the service
     */
    getHealthStatus() {
        return {
            status: this.status,
            details: {
                configuration: this.analyticsConfig,
                lastAnalysisRun: this.lastAnalysisRun,
                alertsCount: this.alertsCache.size,
                recommendationsCount: this.recommendationsCache.size
            }
        };
    }

    constructor(config: SupplyChainAnalyticsServiceConfig = {}) {
        super(config);
        this.supplierRepository = new SupplierRepository();
        this.deliveryRepository = new DeliveryRepository();

        this.analyticsConfig = {
            monitoringInterval: 15, // 15 minutes
            alertThresholds: {
                riskScore: 7,
                onTimeRate: 0.85,
                qualityRate: 0.95,
                deliveryDelay: 2 // days
            },
            trendAnalysisPeriod: 90, // 90 days
            performanceWeights: {
                onTimeDelivery: 0.4,
                quality: 0.3,
                cost: 0.2,
                reliability: 0.1
            },
            ...config
        };
    }

    /**
     * Initialize the supply chain analytics service
     */
    async initialize(): Promise<void> {
        try {
            // Verify repository connections
            await this.supplierRepository.getSupplierStats();
            await this.deliveryRepository.getDeliveryStats();

            // Start monitoring if enabled
            if (this.analyticsConfig.monitoringInterval && this.analyticsConfig.monitoringInterval > 0) {
                this.startMonitoring();
            }

            await super.initialize(this.analyticsConfig);
            this.log('SupplyChainAnalyticsService initialized successfully');
        } catch (error) {
            this.lastError = error instanceof Error ? error : new Error(String(error));
            this.log('Failed to initialize SupplyChainAnalyticsService:', error);
            throw new Error(`Supply chain analytics initialization failed: ${error}`);
        }
    }

    /**
     * Destroy the service and clean up resources
     */
    async destroy(): Promise<void> {
        this.stopMonitoring();
        this.alertsCache.clear();
        this.recommendationsCache.clear();
        await super.destroy();
    }

    /**
     * Get comprehensive supply chain analytics
     */
    async getSupplyChainAnalytics(timeRange: { days: number } = { days: 30 }): Promise<SupplyChainAnalytics> {
        return this.handleAIError(
            async () => {
                this.log(`Generating supply chain analytics for ${timeRange.days} days`);

                // Get overall performance metrics
                const overallPerformance = await this.calculateOverallPerformance(timeRange);

                // Get supplier performance analytics
                const supplierPerformance = await this.getSupplierPerformanceAnalytics(timeRange);

                // Get delivery performance analytics
                const deliveryPerformance = await this.getDeliveryPerformanceAnalytics(timeRange);

                // Get risk analysis
                const riskAnalysis = await this.getRiskAnalytics(timeRange);

                // Get trend analysis
                const trends = await this.getTrendAnalysis(timeRange);

                const analytics: SupplyChainAnalytics = {
                    overallPerformance,
                    supplierPerformance,
                    deliveryPerformance,
                    riskAnalysis,
                    trends
                };

                this.lastAnalysisRun = new Date();
                this.log('Supply chain analytics generated successfully');
                return analytics;
            },
            async () => {
                // Fallback: return basic analytics
                return this.getFallbackAnalytics();
            },
            AIServiceError.OPTIMIZATION_FAILED
        );
    }

    /**
     * Get supplier performance analytics with scoring and ranking
     */
    async getSupplierPerformanceAnalytics(timeRange: { days: number }): Promise<SupplierPerformanceAnalytics[]> {
        try {
            this.log(`Analyzing supplier performance for ${timeRange.days} days`);

            const suppliers = await this.supplierRepository.getAllSuppliers();
            const analytics: SupplierPerformanceAnalytics[] = [];

            if (!suppliers || !Array.isArray(suppliers)) {
                this.logger.error('Suppliers data is not iterable:', suppliers);
                return [];
            }

            for (const supplier of suppliers) {
                const performanceData = await this.supplierRepository.getSupplierPerformance(
                    supplier.id,
                    timeRange
                );

                const deliveryHistory = await this.deliveryRepository.getSupplierDeliveryHistory(
                    supplier.id,
                    timeRange
                );

                // Calculate performance score
                const performanceScore = this.calculateSupplierPerformanceScore(performanceData);

                // Calculate risk score
                const riskScore = this.calculateSupplierRiskScore(performanceData, deliveryHistory);

                // Analyze trends
                const trends = this.analyzeSupplierTrends(deliveryHistory);

                analytics.push({
                    supplierId: supplier.id,
                    supplierName: supplier.name,
                    performanceScore,
                    riskScore,
                    deliveryReliability: performanceData.onTimeDeliveryRate,
                    qualityScore: performanceData.qualityRate * 10, // Convert to 1-10 scale
                    costCompetitiveness: performanceData.costCompetitiveness,
                    trends
                });
            }

            // Sort by performance score (descending)
            return analytics.sort((a, b) => b.performanceScore - a.performanceScore);
        } catch (error) {
            this.logger.error('Failed to get supplier performance analytics:', error);
            throw new Error(`Failed to get supplier performance analytics: ${error}`);
        }
    }

    /**
     * Get delivery performance analytics
     */
    async getDeliveryPerformanceAnalytics(timeRange: { days: number }): Promise<DeliveryPerformanceAnalytics> {
        try {
            const deliveryMetrics = await this.deliveryRepository.getDeliveryPerformanceMetrics(timeRange);
            const delayReasons = await this.deliveryRepository.getDelayReasons(timeRange);
            const seasonalPatterns = await this.deliveryRepository.getSeasonalPatterns();

            return {
                averageDeliveryTime: deliveryMetrics.averageDeliveryTime,
                onTimeRate: deliveryMetrics.onTimeRate,
                delayFrequency: deliveryMetrics.delayFrequency,
                commonDelayReasons: delayReasons.map(reason => ({
                    reason: reason.reason,
                    frequency: reason.frequency,
                    averageDelay: reason.averageDelay,
                    impact: reason.impact as 'low' | 'medium' | 'high'
                })),
                seasonalPatterns: seasonalPatterns.map(pattern => ({
                    season: pattern.season,
                    deliveryTimeFactor: pattern.deliveryTimeFactor,
                    riskFactor: pattern.riskFactor,
                    description: pattern.description
                }))
            };
        } catch (error) {
            this.logger.error('Failed to get delivery performance analytics:', error);
            throw new Error(`Failed to get delivery performance analytics: ${error}`);
        }
    }

    /**
     * Get risk analytics
     */
    async getRiskAnalytics(timeRange: { days: number }): Promise<RiskAnalytics> {
        try {
            const suppliers = await this.supplierRepository.getAllSuppliers();
            const riskSuppliers = await this.supplierRepository.getRiskSuppliers();

            // Calculate overall risk level
            const totalRiskScore = suppliers.reduce((sum, supplier) => {
                const riskScore = this.calculateBasicRiskScore(supplier.performanceMetrics);
                return sum + riskScore;
            }, 0);

            const averageRiskScore = suppliers.length > 0 ? totalRiskScore / suppliers.length : 5;
            const overallRiskLevel = this.categorizeRiskLevel(averageRiskScore);

            // Identify top risk factors
            const topRiskFactors = await this.identifyTopRiskFactors(suppliers);

            // Analyze risk trends
            const riskTrends = await this.analyzeRiskTrends(timeRange);

            // Calculate mitigation effectiveness (mock for now)
            const mitigationEffectiveness = 0.75; // 75% effectiveness

            return {
                overallRiskLevel,
                topRiskFactors,
                riskTrends,
                mitigationEffectiveness
            };
        } catch (error) {
            this.logger.error('Failed to get risk analytics:', error);
            throw new Error(`Failed to get risk analytics: ${error}`);
        }
    }

    /**
     * Get trend analysis
     */
    async getTrendAnalysis(timeRange: { days: number }): Promise<TrendAnalysis[]> {
        try {
            const deliveryTrends = await this.deliveryRepository.getDeliveryTrends(timeRange);
            const trends: TrendAnalysis[] = [];

            if (!deliveryTrends || !Array.isArray(deliveryTrends)) {
                this.logger.error('Delivery trends data is not iterable:', deliveryTrends);
                return [];
            }

            // Analyze on-time delivery trend
            const onTimeDataPoints: TrendDataPoint[] = deliveryTrends.map(trend => ({
                date: trend.week,
                value: trend.onTimeRate * 100,
                context: `${trend.totalDeliveries} Lieferungen`
            }));

            trends.push({
                metric: 'Pünktlichkeitsrate',
                trend: this.calculateTrendDirection(onTimeDataPoints),
                changeRate: this.calculateChangeRate(onTimeDataPoints),
                timeframe: `${timeRange.days} Tage`,
                dataPoints: onTimeDataPoints
            });

            // Analyze average delivery time trend
            const deliveryTimeDataPoints: TrendDataPoint[] = deliveryTrends.map(trend => ({
                date: trend.week,
                value: trend.averageDeliveryTime,
                context: `Durchschnitt: ${trend.averageDeliveryTime.toFixed(1)} Tage`
            }));

            trends.push({
                metric: 'Durchschnittliche Lieferzeit',
                trend: this.calculateTrendDirection(deliveryTimeDataPoints, true), // Reverse for delivery time (lower is better)
                changeRate: this.calculateChangeRate(deliveryTimeDataPoints),
                timeframe: `${timeRange.days} Tage`,
                dataPoints: deliveryTimeDataPoints
            });

            // Analyze cost trend
            const costDataPoints: TrendDataPoint[] = deliveryTrends.map(trend => ({
                date: trend.week,
                value: trend.totalCost,
                context: `Gesamtkosten: €${trend.totalCost.toLocaleString()}`
            }));

            trends.push({
                metric: 'Lieferkosten',
                trend: this.calculateTrendDirection(costDataPoints, true), // Reverse for cost (lower is better)
                changeRate: this.calculateChangeRate(costDataPoints),
                timeframe: `${timeRange.days} Tage`,
                dataPoints: costDataPoints
            });

            return trends;
        } catch (error) {
            this.logger.error('Failed to get trend analysis:', error);
            throw new Error(`Failed to get trend analysis: ${error}`);
        }
    }

    /**
     * Generate supply chain alerts
     */
    async generateAlerts(): Promise<SupplyChainAlert[]> {
        try {
            this.log('Generating supply chain alerts');
            const alerts: SupplyChainAlert[] = [];

            // Check supplier performance alerts
            try {
                const supplierAlerts = await this.checkSupplierPerformanceAlerts();
                alerts.push(...supplierAlerts);
            } catch (error) {
                this.logger.error('Failed to check supplier performance alerts:', error);
            }

            // Check delivery performance alerts
            try {
                const deliveryAlerts = await this.checkDeliveryPerformanceAlerts();
                alerts.push(...deliveryAlerts);
            } catch (error) {
                this.logger.error('Failed to check delivery performance alerts:', error);
            }

            // Check risk alerts
            try {
                const riskAlerts = await this.checkRiskAlerts();
                alerts.push(...riskAlerts);
            } catch (error) {
                this.logger.error('Failed to check risk alerts:', error);
            }

            // Cache alerts
            alerts.forEach(alert => {
                this.alertsCache.set(alert.alertId, alert);
            });

            this.log(`Generated ${alerts.length} supply chain alerts`);
            return alerts;
        } catch (error) {
            this.logger.error('Failed to generate alerts:', error);
            return []; // Return empty array instead of throwing
        }
    }

    /**
     * Generate supply chain recommendations
     */
    async generateRecommendations(): Promise<SupplyChainRecommendation[]> {
        try {
            this.log('Generating supply chain recommendations');
            const recommendations: SupplyChainRecommendation[] = [];

            // Analyze current performance and identify improvement opportunities
            const analytics = await this.getSupplyChainAnalytics({ days: 90 });

            // Generate supplier diversification recommendations
            const diversificationRecs = this.generateSupplierDiversificationRecommendations(analytics);
            recommendations.push(...diversificationRecs);

            // Generate inventory optimization recommendations
            const inventoryRecs = this.generateInventoryOptimizationRecommendations(analytics);
            recommendations.push(...inventoryRecs);

            // Generate route optimization recommendations
            const routeRecs = this.generateRouteOptimizationRecommendations(analytics);
            recommendations.push(...routeRecs);

            // Generate risk mitigation recommendations
            const riskRecs = this.generateRiskMitigationRecommendations(analytics);
            recommendations.push(...riskRecs);

            // Cache recommendations
            recommendations.forEach(rec => {
                this.recommendationsCache.set(rec.recommendationId, rec);
            });

            this.log(`Generated ${recommendations.length} supply chain recommendations`);
            return recommendations;
        } catch (error) {
            this.logger.error('Failed to generate recommendations:', error);
            throw new Error(`Failed to generate recommendations: ${error}`);
        }
    }

    /**
     * Get cached alerts
     */
    getAlerts(): SupplyChainAlert[] {
        return Array.from(this.alertsCache.values()).sort((a, b) =>
            b.createdAt.getTime() - a.createdAt.getTime()
        );
    }

    /**
     * Get cached recommendations
     */
    getRecommendations(): SupplyChainRecommendation[] {
        return Array.from(this.recommendationsCache.values()).sort((a, b) => {
            const priorityOrder = { high: 3, medium: 2, low: 1 };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
        });
    }

    /**
     * Acknowledge an alert
     */
    acknowledgeAlert(alertId: string): boolean {
        const alert = this.alertsCache.get(alertId);
        if (alert) {
            alert.acknowledged = true;
            this.alertsCache.set(alertId, alert);
            return true;
        }
        return false;
    }

    // Private helper methods

    /**
     * Start monitoring timer
     */
    private startMonitoring(): void {
        if (this.monitoringTimer) {
            clearInterval(this.monitoringTimer);
        }

        const intervalMs = (this.analyticsConfig.monitoringInterval || 15) * 60 * 1000;
        this.monitoringTimer = setInterval(async () => {
            try {
                await this.generateAlerts();
                await this.generateRecommendations();
            } catch (error) {
                this.logger.error('Monitoring cycle failed:', error);
            }
        }, intervalMs);

        this.log(`Started monitoring with ${this.analyticsConfig.monitoringInterval} minute intervals`);
    }

    /**
     * Stop monitoring timer
     */
    private stopMonitoring(): void {
        if (this.monitoringTimer) {
            clearInterval(this.monitoringTimer);
            this.monitoringTimer = null;
            this.log('Stopped monitoring');
        }
    }

    /**
     * Calculate overall performance metrics
     */
    private async calculateOverallPerformance(timeRange: { days: number }): Promise<PerformanceMetrics> {
        const deliveryMetrics = await this.deliveryRepository.getDeliveryPerformanceMetrics(timeRange);
        const supplierStats = await this.supplierRepository.getSupplierStats();

        // Calculate cost efficiency (mock calculation)
        const costEfficiency = 0.85 + Math.random() * 0.1; // 85-95%

        // Calculate overall risk score
        const overallRiskScore = Math.max(1, Math.min(10,
            (10 - supplierStats.averagePerformanceScore) +
            (deliveryMetrics.delayFrequency * 5)
        ));

        return {
            onTimeDeliveryRate: deliveryMetrics.onTimeRate,
            averageDeliveryTime: deliveryMetrics.averageDeliveryTime,
            costEfficiency,
            supplierReliability: supplierStats.averagePerformanceScore / 10,
            overallRiskScore
        };
    }

    /**
     * Calculate supplier performance score
     */
    private calculateSupplierPerformanceScore(performanceData: any): number {
        const weights = this.analyticsConfig.performanceWeights!;

        const score =
            (performanceData.onTimeDeliveryRate * weights.onTimeDelivery * 10) +
            (performanceData.qualityRate * weights.quality * 10) +
            (performanceData.costCompetitiveness * weights.cost) +
            (performanceData.reliabilityScore * weights.reliability);

        return Math.min(10, Math.max(1, score));
    }

    /**
     * Calculate supplier risk score
     */
    private calculateSupplierRiskScore(performanceData: any, deliveryHistory: any[]): number {
        let riskScore = 5; // Base risk score

        // Adjust based on performance metrics
        if (performanceData.onTimeDeliveryRate < 0.8) riskScore += 2;
        if (performanceData.qualityRate < 0.95) riskScore += 1.5;
        if (performanceData.financialStabilityScore < 6) riskScore += 2;
        if (performanceData.reliabilityScore < 7) riskScore += 1;

        // Adjust based on delivery history
        const lateDeliveries = deliveryHistory.filter(d => d.wasLate).length;
        const lateRate = deliveryHistory.length > 0 ? lateDeliveries / deliveryHistory.length : 0;
        if (lateRate > 0.2) riskScore += lateRate * 3;

        return Math.min(10, Math.max(1, riskScore));
    }

    /**
     * Analyze supplier trends
     */
    private analyzeSupplierTrends(deliveryHistory: any[]): {
        performance: 'improving' | 'stable' | 'declining';
        risk: 'increasing' | 'stable' | 'decreasing';
    } {
        if (deliveryHistory.length < 10) {
            return { performance: 'stable', risk: 'stable' };
        }

        // Split history into two halves for comparison
        const midPoint = Math.floor(deliveryHistory.length / 2);
        const recent = deliveryHistory.slice(0, midPoint);
        const older = deliveryHistory.slice(midPoint);

        // Calculate performance metrics for each period
        const recentOnTime = recent.filter(d => !d.wasLate).length / recent.length;
        const olderOnTime = older.filter(d => !d.wasLate).length / older.length;

        const performanceChange = recentOnTime - olderOnTime;
        const performance = performanceChange > 0.05 ? 'improving' :
            performanceChange < -0.05 ? 'declining' : 'stable';

        // Risk is inverse of performance
        const risk = performance === 'improving' ? 'decreasing' :
            performance === 'declining' ? 'increasing' : 'stable';

        return { performance, risk };
    }

    /**
     * Calculate basic risk score from performance metrics
     */
    private calculateBasicRiskScore(metrics: any): number {
        let riskScore = 5;

        if (metrics.onTimeDeliveryRate < 0.85) riskScore += 2;
        if (metrics.qualityRate < 0.95) riskScore += 1.5;
        if (metrics.financialStabilityScore < 7) riskScore += 1.5;
        if (metrics.reliabilityScore < 8) riskScore += 1;

        return Math.min(10, Math.max(1, riskScore));
    }

    /**
     * Categorize risk level
     */
    private categorizeRiskLevel(riskScore: number): 'low' | 'medium' | 'high' | 'critical' {
        if (riskScore <= 3) return 'low';
        if (riskScore <= 5) return 'medium';
        if (riskScore <= 7) return 'high';
        return 'critical';
    }

    /**
     * Identify top risk factors
     */
    private async identifyTopRiskFactors(suppliers: any[]): Promise<any[]> {
        const riskFactors = new Map<string, { count: number; totalImpact: number }>();

        if (!suppliers || !Array.isArray(suppliers)) {
            this.logger.error('Suppliers data is not iterable for risk factors:', suppliers);
            return [];
        }

        for (const supplier of suppliers) {
            const metrics = supplier.performanceMetrics;

            if (metrics.onTimeDeliveryRate < 0.85) {
                const existing = riskFactors.get('delivery_performance') || { count: 0, totalImpact: 0 };
                existing.count++;
                existing.totalImpact += (0.85 - metrics.onTimeDeliveryRate) * 10;
                riskFactors.set('delivery_performance', existing);
            }

            if (metrics.qualityRate < 0.95) {
                const existing = riskFactors.get('quality') || { count: 0, totalImpact: 0 };
                existing.count++;
                existing.totalImpact += (0.95 - metrics.qualityRate) * 8;
                riskFactors.set('quality', existing);
            }

            if (metrics.financialStabilityScore < 7) {
                const existing = riskFactors.get('financial_stability') || { count: 0, totalImpact: 0 };
                existing.count++;
                existing.totalImpact += (7 - metrics.financialStabilityScore) * 1.5;
                riskFactors.set('financial_stability', existing);
            }
        }

        return Array.from(riskFactors.entries()).map(([type, data]) => ({
            type,
            severity: data.totalImpact > 10 ? 'high' : data.totalImpact > 5 ? 'medium' : 'low',
            probability: Math.min(1, data.count / suppliers.length),
            description: this.getRiskFactorDescription(type, data.count),
            impact: data.totalImpact
        })).sort((a, b) => b.impact - a.impact).slice(0, 5);
    }

    /**
     * Get risk factor description
     */
    private getRiskFactorDescription(type: string, count: number): string {
        switch (type) {
            case 'delivery_performance':
                return `${count} Lieferanten mit unterdurchschnittlicher Pünktlichkeit`;
            case 'quality':
                return `${count} Lieferanten mit Qualitätsproblemen`;
            case 'financial_stability':
                return `${count} Lieferanten mit finanziellen Risiken`;
            default:
                return `${count} Lieferanten betroffen`;
        }
    }

    /**
     * Analyze risk trends
     */
    private async analyzeRiskTrends(timeRange: { days: number }): Promise<RiskTrend[]> {
        // Mock implementation - in real app, this would analyze historical risk data
        return [
            {
                riskType: 'Lieferantenrisiko',
                trend: 'stable',
                changeRate: 2.5,
                timeframe: `${timeRange.days} Tage`
            },
            {
                riskType: 'Lieferrisiko',
                trend: 'decreasing',
                changeRate: -5.2,
                timeframe: `${timeRange.days} Tage`
            },
            {
                riskType: 'Qualitätsrisiko',
                trend: 'increasing',
                changeRate: 8.1,
                timeframe: `${timeRange.days} Tage`
            }
        ];
    }

    /**
     * Calculate trend direction from data points
     */
    private calculateTrendDirection(dataPoints: TrendDataPoint[], reverse: boolean = false): 'improving' | 'stable' | 'declining' {
        if (dataPoints.length < 2) return 'stable';

        const firstHalf = dataPoints.slice(0, Math.floor(dataPoints.length / 2));
        const secondHalf = dataPoints.slice(Math.floor(dataPoints.length / 2));

        const firstAvg = firstHalf.reduce((sum, point) => sum + point.value, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((sum, point) => sum + point.value, 0) / secondHalf.length;

        const change = (secondAvg - firstAvg) / firstAvg;
        const threshold = 0.05; // 5% change threshold

        if (reverse) {
            if (change < -threshold) return 'improving';
            if (change > threshold) return 'declining';
        } else {
            if (change > threshold) return 'improving';
            if (change < -threshold) return 'declining';
        }

        return 'stable';
    }

    /**
     * Calculate change rate from data points
     */
    private calculateChangeRate(dataPoints: TrendDataPoint[]): number {
        if (dataPoints.length < 2) return 0;

        const firstValue = dataPoints[0].value;
        const lastValue = dataPoints[dataPoints.length - 1].value;

        if (firstValue === 0) return 0;

        return ((lastValue - firstValue) / firstValue) * 100;
    }

    // Alert generation methods

    /**
     * Check supplier performance alerts
     */
    private async checkSupplierPerformanceAlerts(): Promise<SupplyChainAlert[]> {
        const alerts: SupplyChainAlert[] = [];
        const suppliers = await this.supplierRepository.getAllSuppliers();
        const thresholds = this.analyticsConfig.alertThresholds!;

        if (!suppliers || !Array.isArray(suppliers)) {
            this.logger.error('Suppliers data is not iterable for alerts:', suppliers);
            return alerts;
        }

        for (const supplier of suppliers) {
            try {
                const performanceData = await this.supplierRepository.getSupplierPerformance(
                    supplier.id,
                    { days: 30 }
                );

                if (!performanceData) {
                    continue;
                }

                // Check on-time delivery rate
                if (performanceData.onTimeDeliveryRate < thresholds.onTimeRate) {
                    alerts.push({
                        alertId: `ALERT_${Date.now()}_${supplier.id}_ONTIME`,
                        type: 'performance_decline',
                        severity: performanceData.onTimeDeliveryRate < 0.7 ? 'high' : 'medium',
                        supplierId: supplier.id,
                        supplierName: supplier.name,
                        message: `Pünktlichkeitsrate unter Schwellenwert`,
                        details: `${supplier.name} hat eine Pünktlichkeitsrate von ${(performanceData.onTimeDeliveryRate * 100).toFixed(1)}% (Schwellenwert: ${(thresholds.onTimeRate * 100).toFixed(1)}%)`,
                        recommendations: [
                            'Gespräch mit Lieferant über Liefertermine führen',
                            'Alternative Lieferanten evaluieren',
                            'Sicherheitsbestände erhöhen'
                        ],
                        createdAt: new Date(),
                        acknowledged: false
                    });
                }

                // Check quality rate
                if (performanceData.qualityRate < thresholds.qualityRate) {
                    alerts.push({
                        alertId: `ALERT_${Date.now()}_${supplier.id}_QUALITY`,
                        type: 'quality_issue',
                        severity: performanceData.qualityRate < 0.9 ? 'high' : 'medium',
                        supplierId: supplier.id,
                        supplierName: supplier.name,
                        message: `Qualitätsrate unter Schwellenwert`,
                        details: `${supplier.name} hat eine Qualitätsrate von ${(performanceData.qualityRate * 100).toFixed(1)}% (Schwellenwert: ${(thresholds.qualityRate * 100).toFixed(1)}%)`,
                        recommendations: [
                            'Qualitätsprüfungen verstärken',
                            'Lieferantenaudit durchführen',
                            'Qualitätsvereinbarung überarbeiten'
                        ],
                        createdAt: new Date(),
                        acknowledged: false
                    });
                }
            } catch (error) {
                this.logger.error(`Failed to check performance for supplier ${supplier.id}:`, error);
                continue;
            }
        }

        return alerts;
    }

    /**
     * Check delivery performance alerts
     */
    private async checkDeliveryPerformanceAlerts(): Promise<SupplyChainAlert[]> {
        const alerts: SupplyChainAlert[] = [];
        const deliveryMetrics = await this.deliveryRepository.getDeliveryPerformanceMetrics({ days: 7 });
        const thresholds = this.analyticsConfig.alertThresholds!;

        if (!deliveryMetrics) {
            this.logger.error('Delivery metrics data is null');
            return alerts;
        }

        // Check if average delivery time is increasing
        if (deliveryMetrics.averageDeliveryTime > thresholds.deliveryDelay + 5) { // Base + threshold
            alerts.push({
                alertId: `ALERT_${Date.now()}_DELIVERY_TIME`,
                type: 'delivery_delay',
                severity: deliveryMetrics.averageDeliveryTime > 10 ? 'high' : 'medium',
                message: `Durchschnittliche Lieferzeit erhöht`,
                details: `Die durchschnittliche Lieferzeit beträgt ${deliveryMetrics.averageDeliveryTime.toFixed(1)} Tage`,
                recommendations: [
                    'Lieferrouten optimieren',
                    'Lieferantenleistung überprüfen',
                    'Logistikprozesse analysieren'
                ],
                createdAt: new Date(),
                acknowledged: false
            });
        }

        return alerts;
    }

    /**
     * Check risk alerts
     */
    private async checkRiskAlerts(): Promise<SupplyChainAlert[]> {
        const alerts: SupplyChainAlert[] = [];
        const riskSuppliers = await this.supplierRepository.getRiskSuppliers();
        const thresholds = this.analyticsConfig.alertThresholds!;

        if (!riskSuppliers || !Array.isArray(riskSuppliers)) {
            this.logger.error('Risk suppliers data is not iterable for alerts:', riskSuppliers);
            return alerts;
        }

        for (const supplier of riskSuppliers) {
            const riskScore = this.calculateBasicRiskScore(supplier.performanceMetrics);

            if (riskScore >= thresholds.riskScore) {
                alerts.push({
                    alertId: `ALERT_${Date.now()}_${supplier.id}_RISK`,
                    type: 'risk_increase',
                    severity: riskScore >= 8 ? 'critical' : 'high',
                    supplierId: supplier.id,
                    supplierName: supplier.name,
                    message: `Hohes Lieferantenrisiko erkannt`,
                    details: `${supplier.name} hat einen Risikoscore von ${riskScore.toFixed(1)}/10`,
                    recommendations: [
                        'Detaillierte Risikobewertung durchführen',
                        'Alternative Lieferanten identifizieren',
                        'Überwachung verstärken'
                    ],
                    createdAt: new Date(),
                    acknowledged: false
                });
            }
        }

        return alerts;
    }

    // Recommendation generation methods

    /**
     * Generate supplier diversification recommendations
     */
    private generateSupplierDiversificationRecommendations(analytics: SupplyChainAnalytics): SupplyChainRecommendation[] {
        const recommendations: SupplyChainRecommendation[] = [];

        // Check if there are high-risk suppliers
        const highRiskSuppliers = analytics.supplierPerformance.filter(s => s.riskScore >= 7);

        if (highRiskSuppliers.length > 0) {
            recommendations.push({
                recommendationId: `REC_${Date.now()}_DIVERSIFICATION`,
                type: 'supplier_diversification',
                priority: 'high',
                title: 'Lieferantendiversifizierung',
                description: `${highRiskSuppliers.length} Lieferanten mit hohem Risiko identifiziert. Diversifizierung empfohlen.`,
                expectedBenefit: 'Reduzierung des Lieferrisikos um 30-40%',
                implementationSteps: [
                    'Alternative Lieferanten identifizieren und bewerten',
                    'Pilotbestellungen bei neuen Lieferanten durchführen',
                    'Schrittweise Umverteilung der Bestellvolumen',
                    'Überwachung der neuen Lieferantenbeziehungen'
                ],
                estimatedCost: 15000,
                estimatedSavings: 45000,
                timeframe: '3-6 Monate',
                createdAt: new Date()
            });
        }

        return recommendations;
    }

    /**
     * Generate inventory optimization recommendations
     */
    private generateInventoryOptimizationRecommendations(analytics: SupplyChainAnalytics): SupplyChainRecommendation[] {
        const recommendations: SupplyChainRecommendation[] = [];

        // Check if delivery performance is poor
        if (analytics.deliveryPerformance.onTimeRate < 0.85) {
            recommendations.push({
                recommendationId: `REC_${Date.now()}_INVENTORY`,
                type: 'inventory_optimization',
                priority: 'medium',
                title: 'Bestandsoptimierung',
                description: 'Niedrige Pünktlichkeitsrate erfordert Anpassung der Sicherheitsbestände.',
                expectedBenefit: 'Reduzierung von Fehlbeständen um 25%',
                implementationSteps: [
                    'ABC-Analyse der kritischen Artikel durchführen',
                    'Sicherheitsbestände für A-Artikel erhöhen',
                    'Bestellpunkte anpassen',
                    'Überwachung der Bestandsreichweite implementieren'
                ],
                estimatedCost: 8000,
                estimatedSavings: 20000,
                timeframe: '2-3 Monate',
                createdAt: new Date()
            });
        }

        return recommendations;
    }

    /**
     * Generate route optimization recommendations
     */
    private generateRouteOptimizationRecommendations(analytics: SupplyChainAnalytics): SupplyChainRecommendation[] {
        const recommendations: SupplyChainRecommendation[] = [];

        // Check if delivery times are high
        if (analytics.deliveryPerformance.averageDeliveryTime > 7) {
            recommendations.push({
                recommendationId: `REC_${Date.now()}_ROUTE`,
                type: 'route_optimization',
                priority: 'medium',
                title: 'Routenoptimierung',
                description: 'Hohe durchschnittliche Lieferzeiten deuten auf Optimierungspotential hin.',
                expectedBenefit: 'Reduzierung der Lieferzeiten um 15-20%',
                implementationSteps: [
                    'Aktuelle Lieferrouten analysieren',
                    'Optimierungsalgorithmen implementieren',
                    'Neue Routen testen und validieren',
                    'Rollout der optimierten Routen'
                ],
                estimatedCost: 12000,
                estimatedSavings: 25000,
                timeframe: '2-4 Monate',
                createdAt: new Date()
            });
        }

        return recommendations;
    }

    /**
     * Generate risk mitigation recommendations
     */
    private generateRiskMitigationRecommendations(analytics: SupplyChainAnalytics): SupplyChainRecommendation[] {
        const recommendations: SupplyChainRecommendation[] = [];

        // Check overall risk level
        if (analytics.riskAnalysis.overallRiskLevel === 'high' || analytics.riskAnalysis.overallRiskLevel === 'critical') {
            recommendations.push({
                recommendationId: `REC_${Date.now()}_RISK_MITIGATION`,
                type: 'risk_mitigation',
                priority: 'high',
                title: 'Risikominderung',
                description: 'Hohes Gesamtrisiko erfordert sofortige Maßnahmen zur Risikominderung.',
                expectedBenefit: 'Reduzierung des Gesamtrisikos um 40-50%',
                implementationSteps: [
                    'Detaillierte Risikoanalyse aller Lieferanten',
                    'Notfallpläne für kritische Lieferanten entwickeln',
                    'Überwachungssystem für Frühwarnung implementieren',
                    'Regelmäßige Risikobewertungen etablieren'
                ],
                estimatedCost: 20000,
                estimatedSavings: 60000,
                timeframe: '1-3 Monate',
                createdAt: new Date()
            });
        }

        return recommendations;
    }

    /**
     * Get fallback analytics when main analysis fails
     */
    private async getFallbackAnalytics(): Promise<SupplyChainAnalytics> {
        return {
            overallPerformance: {
                onTimeDeliveryRate: 0.8,
                averageDeliveryTime: 7,
                costEfficiency: 0.85,
                supplierReliability: 0.75,
                overallRiskScore: 5
            },
            supplierPerformance: [],
            deliveryPerformance: {
                averageDeliveryTime: 7,
                onTimeRate: 0.8,
                delayFrequency: 0.2,
                commonDelayReasons: [],
                seasonalPatterns: []
            },
            riskAnalysis: {
                overallRiskLevel: 'medium',
                topRiskFactors: [],
                riskTrends: [],
                mitigationEffectiveness: 0.5
            },
            trends: []
        };
    }
}

export default SupplyChainAnalyticsService;