/**
 * RAGSettingsManager - RAG, Vector Database and Embedding Settings
 * 
 * Specialized settings component for RAG-related configurations
 * Extracted from AISettingsManager to focus on RAG, Vector DB and Embedding settings
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Settings,
  Save,
  RotateCcw,
  AlertCircle,
  CheckCircle,
  Database,
  Shield,
  BrainCircuit,
  Loader2
} from 'lucide-react';

import RAGSettingsApiService from '../../services/ragSettingsApi';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
// Tabs import entfernt - verwenden jetzt vertikales Layout ohne verschachtelte Tabs
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

// RAG-specific settings interface
export interface RAGModuleSettings {
  vectorDatabase: {
    dimensions: number;
    similarityThreshold: number;
    maxResults: number;
    enableCache: boolean;
    cacheTTL: number;
  };
  security: {
    enableRateLimit: boolean;
    rateLimitRequests: number;
    rateLimitWindow: number;
    enableInputValidation: boolean;
    enableAuditLogging: boolean;
  };
  services: {
    embedding: {
      model: string;
      batchSize: number;
      enableCache: boolean;
    };
    rag: {
      contextLength: number;
      enableSourceCitation: boolean;
      confidenceThreshold: number;
    };
  };
}

// Default settings for RAG components
const defaultRAGSettings: RAGModuleSettings = {
  vectorDatabase: {
    dimensions: 1536,
    similarityThreshold: 0.7,
    maxResults: 10,
    enableCache: true,
    cacheTTL: 3600000
  },
  security: {
    enableRateLimit: true,
    rateLimitRequests: 100,
    rateLimitWindow: 3600000,
    enableInputValidation: true,
    enableAuditLogging: true
  },
  services: {
    embedding: {
      model: 'text-embedding-3-small',
      batchSize: 100,
      enableCache: true
    },
    rag: {
      contextLength: 8000,
      enableSourceCitation: true,
      confidenceThreshold: 0.8
    }
  }
};

// RAGSettingsManagerProps Interface um className-Prop erweitern
interface RAGSettingsManagerProps {
  onSettingsChange?: (settings: RAGModuleSettings) => void;
  initialSettings?: Partial<RAGModuleSettings>;
  className?: string; // Neue className-Prop hinzugefügt für flexible Styling-Unterstützung
}

// RAGSettingsManager Komponente um className-Prop erweitern
export const RAGSettingsManager: React.FC<RAGSettingsManagerProps> = ({
  onSettingsChange,
  initialSettings,
  className // Neue className-Prop für externe Styling-Kontrolle
}) => {
  const [settings, setSettings] = useState<RAGModuleSettings>(() => ({
    ...defaultRAGSettings,
    ...initialSettings
  }));
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Load settings from database on component mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true);
        setErrorMessage('');
        
        // Load settings from database
        const dbSettings = await RAGSettingsApiService.getActiveSettings();
        
        // Merge with initial settings and defaults
        const mergedSettings = {
          ...defaultRAGSettings,
          ...initialSettings,
          ...dbSettings
        };
        
        setSettings(mergedSettings);
        setHasChanges(false);
        
        console.log('[RAGSettingsManager] Settings loaded from database:', dbSettings);
      } catch (error) {
        console.error('[RAGSettingsManager] Error loading settings:', error);
        setErrorMessage('Fehler beim Laden der Einstellungen aus der Datenbank');
        
        // Fallback to default settings
        setSettings({
          ...defaultRAGSettings,
          ...initialSettings
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [initialSettings]);

  // Update settings helper function
  const updateSettings = (path: string, value: any) => {
    setSettings(prev => {
      const newSettings = { ...prev };
      const keys = path.split('.');
      let current: any = newSettings;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      setHasChanges(true);
      return newSettings;
    });
  };

  // Save settings function
  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus('idle');
    setErrorMessage('');
    
    try {
      // Save settings to database
      await RAGSettingsApiService.saveSettings(
        settings,
        undefined, // userId - can be extended later for user-specific settings
        `settings_${Date.now()}` // unique name
      );
      
      // Call parent callback if provided
      if (onSettingsChange) {
        onSettingsChange(settings);
      }
      
      setHasChanges(false);
      setSaveStatus('success');
      
      console.log('[RAGSettingsManager] Settings saved successfully to database');
      
      // Reset success status after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      console.error('[RAGSettingsManager] Error saving RAG settings:', error);
      setSaveStatus('error');
      setErrorMessage(
        error instanceof Error 
          ? `Fehler beim Speichern: ${error.message}`
          : 'Unbekannter Fehler beim Speichern der Einstellungen'
      );
    } finally {
      setIsSaving(false);
    }
  };

  // Reset settings function
  const handleReset = () => {
    setSettings({ ...defaultRAGSettings, ...initialSettings });
    setHasChanges(false);
    setSaveStatus('idle');
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className={`w-full max-w-6xl mx-auto space-y-6 ${className || ''}`}>
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3 text-muted-foreground">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Lade RAG-Einstellungen...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full max-w-6xl mx-auto space-y-6 ${className || ''}`}>
      {/* Error Alert */}
      {errorMessage && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}
      
      {/* Header */}
      <div className="flex items-center justify-between">  
        <div className="flex items-center gap-3">
          {saveStatus === 'success' && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex items-center gap-2 text-green-600"
            >
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Gespeichert</span>
            </motion.div>
          )}
          
          {saveStatus === 'error' && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex items-center gap-2 text-red-600"
            >
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Fehler beim Speichern</span>
            </motion.div>
          )}
          
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={!hasChanges || isSaving}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Zurücksetzen
          </Button>
          
          <Button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
            variant="accept"
          >
            {isSaving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {isSaving ? 'Speichere...' : 'Speichern'}
          </Button>
        </div>
      </div>

      {/* Settings Content - Vertical Layout */}
      <div className="space-y-6">
        {/* Vector Database Settings */}
        {/* Grid Layout für Vector Database und Security Settings nebeneinander */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          {/* Vector Database Configuration */}
          <Card className="border-slate-800 bg-slate-50 transition-all duration-300 hover:border-slate-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5 text-blue-600" />
                Vector Database Konfiguration
              </CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Einstellungen für die Vektor-Datenbank und Ähnlichkeitssuche
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="dimensions">Vector Dimensionen</Label>
                    <Input
                      id="dimensions"
                      type="number"
                      value={settings.vectorDatabase.dimensions}
                      onChange={(e) => updateSettings('vectorDatabase.dimensions', parseInt(e.target.value))}
                      min={1}
                      max={4096}
                      className="bg-white"
                    />
                    <p className="text-sm text-gray-500">
                      Anzahl der Dimensionen für Embedding-Vektoren (Standard: 1536)
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxResults">Max. Suchergebnisse</Label>
                    <Input
                      id="maxResults"
                      type="number"
                      value={settings.vectorDatabase.maxResults}
                      onChange={(e) => updateSettings('vectorDatabase.maxResults', parseInt(e.target.value))}
                      min={1}
                      max={100}
                      className="bg-white"
                    />
                    <p className="text-sm text-gray-500">
                      Maximale Anzahl der Suchergebnisse pro Abfrage
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label>Similarity Threshold: {settings.vectorDatabase.similarityThreshold}</Label>
                    <div className="mt-2">
                      <Slider
                        value={[settings.vectorDatabase.similarityThreshold]}
                        onValueChange={(value) => updateSettings('vectorDatabase.similarityThreshold', value[0])}
                        min={0}
                        max={1}
                        step={0.01}
                        className="w-full"
                        aria-label="Similarity Threshold"
                      />
                    </div>
                    <p className="text-sm text-gray-500 mt-1">
                      Mindest-Ähnlichkeit für Suchergebnisse (0.0 - 1.0)
                    </p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="enableCache">Vector Cache aktivieren</Label>
                      <p className="text-sm text-gray-500">
                        Zwischenspeicherung für bessere Performance
                      </p>
                    </div>
                    <Switch
                      id="enableCache"
                      checked={settings.vectorDatabase.enableCache}
                      onCheckedChange={(checked) => updateSettings('vectorDatabase.enableCache', checked)}
                    />
                  </div>

                  {settings.vectorDatabase.enableCache && (
                    <div className="space-y-2">
                      <Label htmlFor="cacheTTL">Cache TTL (Minuten)</Label>
                      <Input
                        id="cacheTTL"
                        type="number"
                        value={Math.round(settings.vectorDatabase.cacheTTL / 60000)}
                        onChange={(e) => updateSettings('vectorDatabase.cacheTTL', parseInt(e.target.value) * 60000)}
                        min={1}
                        max={1440}
                        className="bg-white"
                      />
                      <p className="text-sm text-gray-500">
                        Lebensdauer der Cache-Einträge in Minuten
                      </p>
                    </div>
                  )}
                </div>
            </CardContent>
          </Card>

          {/* Security Settings */}
          <Card className="border-slate-800 bg-slate-50 transition-all duration-300 hover:border-slate-200">
            <CardHeader>
               <CardTitle className="flex items-center gap-2">
                 <Shield className="h-5 w-5 text-red-600" />
                 Sicherheitseinstellungen
               </CardTitle>
               <CardDescription className="text-sm text-gray-600">
                 Sicherheits- und Zugriffskontrolle für RAG-Services
               </CardDescription>
             </CardHeader>
            <CardContent className="space-y-6">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Änderungen an Sicherheitseinstellungen erfordern einen Neustart des RAG-Moduls.
                  </AlertDescription>
                </Alert>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="enableRateLimit">Rate Limiting aktivieren</Label>
                      <p className="text-sm text-gray-500">
                        Begrenzung der RAG-Anfragen pro Zeitfenster
                      </p>
                    </div>
                    <Switch
                      id="enableRateLimit"
                      checked={settings.security.enableRateLimit}
                      onCheckedChange={(checked) => updateSettings('security.enableRateLimit', checked)}
                    />
                  </div>

                  {settings.security.enableRateLimit && (
                    <div className="grid grid-cols-1 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="rateLimitRequests">Max. Anfragen</Label>
                        <Input
                          id="rateLimitRequests"
                          type="number"
                          value={settings.security.rateLimitRequests}
                          onChange={(e) => updateSettings('security.rateLimitRequests', parseInt(e.target.value))}
                          min={1}
                          max={10000}
                          className="bg-white"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="rateLimitWindow">Zeitfenster (Minuten)</Label>
                        <Input
                          id="rateLimitWindow"
                          type="number"
                          value={Math.round(settings.security.rateLimitWindow / 60000)}
                          onChange={(e) => updateSettings('security.rateLimitWindow', parseInt(e.target.value) * 60000)}
                          min={1}
                          max={1440}
                          className="bg-white"
                        />
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="enableInputValidation">Eingabe-Validierung</Label>
                      <p className="text-sm text-gray-500">
                        Validierung und Bereinigung von RAG-Eingaben
                      </p>
                    </div>
                    <Switch
                      id="enableInputValidation"
                      checked={settings.security.enableInputValidation}
                      onCheckedChange={(checked) => updateSettings('security.enableInputValidation', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="enableAuditLogging">Audit-Logging</Label>
                      <p className="text-sm text-gray-500">
                        Protokollierung aller RAG-Operationen
                      </p>
                    </div>
                    <Switch
                      id="enableAuditLogging"
                      checked={settings.security.enableAuditLogging}
                      onCheckedChange={(checked) => updateSettings('security.enableAuditLogging', checked)}
                    />
                  </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Service Settings */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Embedding Service */}
          <Card className="border-slate-800 bg-slate-50 transition-all duration-300 hover:border-slate-200">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <BrainCircuit className="h-5 w-5 text-purple-600" />
                Embedding Service
              </CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Konfiguration für Text-Embeddings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="embeddingModel">Embedding Modell</Label>
                  <Select
                    value={settings.services.embedding.model}
                    onValueChange={(value) => updateSettings('services.embedding.model', value)}
                  >
                    <SelectTrigger className="bg-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text-embedding-3-small">text-embedding-3-small</SelectItem>
                      <SelectItem value="text-embedding-3-large">text-embedding-3-large</SelectItem>
                      <SelectItem value="text-embedding-ada-002">text-embedding-ada-002</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="batchSize">Batch-Größe</Label>
                  <Input
                    id="batchSize"
                    type="number"
                    value={settings.services.embedding.batchSize}
                    onChange={(e) => updateSettings('services.embedding.batchSize', parseInt(e.target.value))}
                    min={1}
                    max={1000}
                    className="bg-white"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="embeddingCache">Cache aktivieren</Label>
                  <Switch
                    id="embeddingCache"
                    checked={settings.services.embedding.enableCache}
                    onCheckedChange={(checked) => updateSettings('services.embedding.enableCache', checked)}
                  />
                </div>
            </CardContent>
          </Card>

          {/* RAG Service */}
          <Card className="border-slate-800 bg-slate-50 transition-all duration-300 hover:border-slate-200">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Settings className="h-5 w-5 text-green-600" />
                RAG Service
              </CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Retrieval-Augmented Generation Einstellungen
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="contextLength">Kontext-Länge</Label>
                  <Input
                    id="contextLength"
                    type="number"
                    value={settings.services.rag.contextLength}
                    onChange={(e) => updateSettings('services.rag.contextLength', parseInt(e.target.value))}
                    min={1000}
                    max={32000}
                    className="bg-white"
                  />
                </div>

                <div>
                  <Label>Confidence Threshold: {settings.services.rag.confidenceThreshold}</Label>
                  <div className="mt-2">
                    <Slider
                      value={[settings.services.rag.confidenceThreshold]}
                      onValueChange={(value) => updateSettings('services.rag.confidenceThreshold', value[0])}
                      min={0}
                      max={1}
                      step={0.01}
                      className="w-full"
                      aria-label="Confidence Threshold"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="sourceCitation">Quellen-Zitation</Label>
                  <Switch
                    id="sourceCitation"
                    checked={settings.services.rag.enableSourceCitation}
                    onCheckedChange={(checked) => updateSettings('services.rag.enableSourceCitation', checked)}
                  />
                </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RAGSettingsManager;