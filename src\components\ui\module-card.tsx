import React from 'react';
import { Button } from '@/components/ui/button';
import { 
  LayoutDashboard, 
  AlertTriangle, 
  Database, 
  Bot
} from 'lucide-react';
import { ModuleCard as ModuleCardType } from '@/types/module';

interface ModuleCardProps {
  module: ModuleCardType;
  imageSrc: string;
  imageAlt: string;
  onNavigate: (route: string) => void;
  isAccessible?: boolean; // Flag, ob der Benutzer Zugriff auf dieses Modul hat
  className?: string; // Zusätzliche CSS-Klassen für die Hauptkomponente
}

// Icon mapping for modules
const getIconComponent = (iconType: string): React.ComponentType<any> => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    'LayoutDashboard': LayoutDashboard,
    'AlertTriangle': AlertTriangle,
    'Database': Database,
    'Bot': Bot
  };
  
  return iconMap[iconType] || LayoutDashboard;
};

export const ModuleCard: React.FC<ModuleCardProps> = ({
  module,
  imageSrc,
  imageAlt,
  onNavigate,
  isAccessible = true, // Standardmäßig zugänglich, wenn nicht anders angegeben
  className = '' // Standard: keine zusätzlichen Klassen
}) => {
  const IconComponent = getIconComponent(module.icon);

  const handleClick = () => {
    // Nur navigieren, wenn das Modul zugänglich ist
    if (isAccessible) {
      onNavigate(module.route);
    }
    // Bei nicht zugänglichen Modulen keine Aktion ausführen
  };



  return (
    <div className={`size-full rounded-3xl flex flex-col relative ${!isAccessible ? 'opacity-60' : ''} ${className}`}>
      {/* Module title as clickable button with icon */}
      <Button
        variant="ghost"
        className={`text-lg font-semibold mb-2 rounded-t-xl bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-800 shadow-sm ${
          isAccessible 
            ? 'cursor-pointer' 
            : 'cursor-not-allowed'
        } h-auto py-3 w-full flex items-center justify-center`}
        onClick={handleClick}
      >
        <IconComponent className="mr-2 h-5 w-5" />
        {module.title}
      </Button>
      
      {/* Module image - also clickable */}
      <div 
        className={`${isAccessible ? 'cursor-pointer' : 'cursor-not-allowed'} flex-1 rounded-b-xl overflow-hidden relative`}
        onClick={handleClick}
      >
        <img
          src={imageSrc}
          className="size-full rounded-xl w-[500px] h-[500px] object-cover"
          alt={imageAlt}
        />
        
        {/* Description tooltip deaktiviert */}
        {/* {module.description && (
          <div className="absolute bottom-2 left-2 right-2 bg-black/80 text-white text-sm p-3 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 backdrop-blur-sm">
            {module.description}
          </div>
        )} */}
        
        {/* Hinweis für nicht zugängliche Module */}
        {!isAccessible && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/40 text-white font-medium p-4 text-center">
            <div className="bg-black/70 p-3 rounded-lg">
              Keine Berechtigung für dieses Modul
            </div>
          </div>
        )}
      </div>
    </div>
  );
};