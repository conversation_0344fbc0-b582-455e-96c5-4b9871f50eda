# Berechnung der maximalen Kabellänge

## Verwendung
Das System berechnet aus den Trommeldaten und den vorgegebenen Kabeldaten über die
nachfolgenden mathematischen Formeln die maximale Kabellänge zum Trommeltyp und prüft durch
Vergleich mit der eingegebenen Kabellänge, ob dieser Trommeltyp vorgeschlagen werden kann.

## Funktionsumfang
1. Die Abmessungen der Daten im Trommel- bzw. Kabelmaterialstamm und die zu prüfende
Kabellänge werden vor der Trommelrechnung vom System auf die entsprechende
Recheneinheit umgerechnet (siehe auch Unterschiedliche Maßeinheiten von Kabel- und
Trommeldaten).

2. Das System ermittelt die Hochlagenzahl (HL):

    ```Math
    HL = (Außendurchmesser - Kerndurchmesser - (2 * Freiraum im Kabelstamm)) / (2 *KDMmZ)
    ```

   Der Kabeldurchmesser inklusive Zuschlag (KDMmZ) berechnet sich wie folgt:

    ```Math
    KDMmZ = Kabeldurchmesser * ( 1 + prozentualer Zuschlag Kabeldurchmesser )
    ```
3. Das Ergebnis für die Hochlagenzahl HL wird auf eine ganze Zahl abgerundet.

4. Anschließend errechnet das System den tatsächlichen Trommelfreiraum (Freiraum FR):

    ```Math
    FR = ((Außendurchmesser - Kerndurchmesser) / 2) -HL * KDMmZ
    ```

5. Wenn über die Kabelstammdaten ein Freiraum vergeben wurde, dann überprüft das System,
ob der errechnete Freiraum kleiner als der vorgegebene Freiraum aus den Kabelstammdaten
ist. In diesem Fall wird die in die Formel einlaufende Hochlagenzahl jeweils um eins vermindert 
und so lange erneut der tatsächliche Freiraum errechnet, bis dieser nicht mehr kleiner als der
vorgegebene Freiraum aus dem Materialstamm ist.

6. Wenn über die Kabelstammdaten eine Hochlagenzahl vorgegeben wird, dann überprüft das
System, ob die errechnete (unter Umständen wegen des Freiraums verminderte)
Hochlagenzahl nicht die Hochlagenzahl im Kabelstamm übersteigt. In diesem Fall wird die
Hochlagenzahl laut Kabelstamm weiterverwendet.

7. Mit der folgenden Vergleichsrechnung reduziert das System die Hochlagenzahl nochmals so
lange um eins, bis der Vergleich erfüllt ist:

    ```Math
    HL * KDMmZ * 0,93 < Führungsbogenhöhe + 2 * KDMmZ
    ```

    Der Faktor 0,93 ist ein Defaultwert und berücksichtigt, dass die Kabellagen nicht
    genau übereinander liegen. Sie können diesen Faktor im Customizing der
    Trommelrechnung einstellen.

    Bei der Ermittlung der Trommel geht das System in dieser Vergleichsrechnung
    davon aus, dass der Führungsbogen einer Trommel (falls vorhanden) maximal um
    zwei Hochlagen überwickelt werden darf. Sie können die Anzahl der Hochlagen
    über dem Führungsbogen im Customizing der Trommelrechnung festlegen.

8. Das System errechnet die Ouerlagenzahl (QL):

    ```Math
    QL = Innenbreite der Trommel / KDMmZ
    ```

9. Die Ouerlagenzahl Ol, wird auf eine ganze Zahl abgerundet.

10. Das System errechnet den mittleren Trommelumfang (MU):

    ```Math
    MU = 3,05 * (Kerndurchmesser + HL * KDMmZ)
    ```
	
    Der Faktor 3,05 (= Wildwickelfaktor) ist ein Defaultwert. Der Wildwickelfaktor
    errechnet sich aus der Zahl 0,97 * Pi. Dieser Faktor beruht auf der Empfehlung
    eines wissenschaftlichen Instituts. Sie können den Wildwickelfaktor im Customizing
    der Trommelrechnung einstellen.

11. Der mittlere Trommelumfang MU wird vom System auf eine ganze Zahl gerundet.

12. Mit den so ermittelten Daten errechnet das System mit der folgenden Formel die maximal
mögliche Kabellänge (Kl) für die Trommel:

    ```Math
    KL=HL*QL*MU
    ```

---

# Beispiel Trommelrechnung VBA Script

```VBA
Sub BerechneTrommel()
    Dim wsKabel As Worksheet, wsTrommel As Worksheet
    Set wsKabel = ThisWorkbook.Sheets("Ergebnis")
    Set wsTrommel = ThisWorkbook.Sheets("Trommel Daten")
    
    Dim Kabeldurchmesser As Double
    Dim ProzentualerZuschlag As Double
    Dim Führungsbogenhöhe As Double
    Dim Kabellänge As Double
    Dim Kabelgewicht As Double
    
    Dim TrommelName As String
    Dim Außendurchmesser As Double
    Dim Kerndurchmesser As Double
    Dim FreiraumKabelstamm As Double
    Dim InnenbreiteTrommel As Double
    Dim MaxTraglast As Double
    Dim MaxKabellänge As Double
    
    Dim lastRowKabel As Long, lastRowTrommel As Long
    lastRowKabel = wsKabel.Cells(wsKabel.Rows.Count, 1).End(xlUp).Row
    lastRowTrommel = wsTrommel.Cells(wsTrommel.Rows.Count, 1).End(xlUp).Row
    
    Dim i As Long, j As Long
    Dim KDMmZ As Double, HL As Double, QL As Double, MU As Double
    Dim MaxLengthTemp As Double
    Dim MinAußendurchmesser As Double
    Dim BesteTrommelName As String
    Dim BesteTrommelMaxLänge As Double
    
    ' Schleife durch jede Kabelzeile in Sheet Ergebnis
    For i = 2 To lastRowKabel
        ' Kabelvariablen aus Sheet1 einlesen
        Kabeldurchmesser = CDbl(wsKabel.Cells(i, 3).Value)
        ProzentualerZuschlag = CDbl(wsKabel.Cells(i, 4).Value)
        Führungsbogenhöhe = CDbl(wsKabel.Cells(i, 8).Value)
        Kabellänge = CDbl(wsKabel.Cells(i, 12).Value) ' Kabellänge in Meter
        Kabelgewicht = CDbl(wsKabel.Cells(i, 10).Value) ' Gewicht des Kabels in Spalte J
        
        ' Bedingung für "Ring": Kabellänge <= 250 Meter und Kabelgewicht <= 30 kg
        If Kabellänge <= 250 And Kabelgewicht <= 30 Then
            wsKabel.Cells(i, 14).Value = "Ring" ' Eintrag "Ring" in Spalte N
            wsKabel.Cells(i, 13).Value = "RG"
            wsKabel.Cells(i, 15).Value = "250" ' Eintrag der Kabellänge in Spalte O
        Else
            ' Berechne den Kabeldurchmesser inkl. Zuschlag
            KDMmZ = Kabeldurchmesser * (1 + ProzentualerZuschlag)
            
            MinAußendurchmesser = 0
            BesteTrommelName = "Keine passende Trommel gefunden"
            BesteTrommelMaxLänge = 0 ' Initialisieren der maximalen Kabellänge der besten Trommel
            
            ' Schleife durch jede Trommelart in Sheet Trommel Daten
            For j = 2 To lastRowTrommel
                ' Trommelvariablen aus Sheet2 einlesen
                TrommelName = wsTrommel.Cells(j, 1).Value
                Außendurchmesser = CDbl(wsTrommel.Cells(j, 2).Value)
                Kerndurchmesser = CDbl(wsTrommel.Cells(j, 3).Value)
                FreiraumKabelstamm = CDbl(wsTrommel.Cells(j, 4).Value)
                InnenbreiteTrommel = CDbl(wsTrommel.Cells(j, 5).Value)
                MaxTraglast = CDbl(wsTrommel.Cells(j, 6).Value) ' Maximale Traglast der Trommel
                
                ' Überprüfen, ob das Kabelgewicht die Traglast der Trommel überschreitet
                If Kabelgewicht <= MaxTraglast Then
                    ' Berechnung der maximalen Kabellänge für die aktuelle Trommelart
                    HL = (Außendurchmesser - Kerndurchmesser - (2 * FreiraumKabelstamm)) / (2 * KDMmZ)
                    QL = InnenbreiteTrommel / KDMmZ
                    MU = 3.05 * (Kerndurchmesser + Int(HL) * KDMmZ)
                    
                    ' Überprüfe, ob HL und QL positive Werte sind
                    If HL > 0 And QL > 0 Then
                        MaxLengthTemp = Int(HL) * Int(QL) ' Zwischenschritt für die Anzahl der Lagen
                        MaxKabellänge = (MaxLengthTemp * MU) / 1000  ' Ergebnis in Meter umwandeln
                        
                        ' Überprüfen, ob die maximale Kabellänge die Kabellänge aufnehmen kann
                        If MaxKabellänge >= Kabellänge Then
                            ' Prüfen, ob dies die kleinste passende Trommel ist
                            If MinAußendurchmesser = 0 Or Außendurchmesser < MinAußendurchmesser Then
                                MinAußendurchmesser = Außendurchmesser
                                BesteTrommelName = TrommelName
                                BesteTrommelMaxLänge = MaxKabellänge ' Speichern der maximalen Kabellänge für die beste Trommel
                            End If
                        End If
                    End If
                End If
            Next j
            
            ' Ergebnis in Sheet1 eintragen
            wsKabel.Cells(i, 14).Value = BesteTrommelName ' Eintrag der besten Trommelart in Spalte N
            wsKabel.Cells(i, 15).Value = BesteTrommelMaxLänge ' Eintrag der maximalen Kabellänge in Spalte O
            
            ' Bedingung für die Kennzeichnung in Spalte M
            If BesteTrommelName = "Ring" Then
                Kennzeichen = "RG"
            ElseIf Left(BesteTrommelName, 1) = "L" Then
                Kennzeichen = "DL"
            ElseIf Left(BesteTrommelName, 1) = "I" Then
                Kennzeichen = "DI"
            Else
                Kennzeichen = "" ' Leer lassen, falls keine passende Trommel gefunden
            End If
            
            wsKabel.Cells(i, 13).Value = Kennzeichen ' Eintrag des Kennzeichens in Spalte M
        End If
    Next i
End Sub

Sub InhalteLöschen()
    Dim letzteZeile As Long
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Sheets("Ergebnis") ' Ersetze "Sheet1" mit deinem Blattnamen, falls nötig
    
    ' Definiere die letzte Zeile als 900000 (oder eine andere gewünschte Zahl)
    letzteZeile = 900000
    
    ' Überprüfen, ob die Spalten M, N und O im Bereich 2 bis 900000 bereits leer sind
    If Application.WorksheetFunction.CountA(ws.Range("M2:M" & letzteZeile)) = 0 And _
       Application.WorksheetFunction.CountA(ws.Range("N2:N" & letzteZeile)) = 0 And _
       Application.WorksheetFunction.CountA(ws.Range("O2:O" & letzteZeile)) = 0 Then
        MsgBox "Die Spalten M, N und O sind bereits leer!"
    Else
        ' Lösche die Inhalte in den Spalten M, N und O von Zeile 2 bis Zeile 100000
        ws.Range("M2:M" & letzteZeile).ClearContents
        ws.Range("N2:N" & letzteZeile).ClearContents
        ws.Range("O2:O" & letzteZeile).ClearContents
        MsgBox "Inhalte in den Spalten M, N und O wurden gelöscht!"
    End If
End Sub

Sub BerechneMaximaleKabellänge()
    ' Eingabewerte aus Zellen
    Dim Kabeldurchmesser As Double
    Dim ProzentualerZuschlag As Double
    Dim Außendurchmesser As Double
    Dim Kerndurchmesser As Double
    Dim FreiraumKabelstamm As Double
    Dim Führungsbogenhöhe As Double
    Dim InnenbreiteTrommel As Double
    
    ' Werte aus Zellen in Variablen übernehmen
    Kabeldurchmesser = CDbl(Sheets("Simulation Trommelrechner").Cells(2, 2).Value)
    ProzentualerZuschlag = CDbl(Sheets("Simulation Trommelrechner").Cells(3, 2).Value)
    Außendurchmesser = CDbl(Sheets("Simulation Trommelrechner").Cells(4, 2).Value)
    Kerndurchmesser = CDbl(Sheets("Simulation Trommelrechner").Cells(5, 2).Value)
    FreiraumKabelstamm = CDbl(Sheets("Simulation Trommelrechner").Cells(6, 2).Value)
    Führungsbogenhöhe = CDbl(Sheets("Simulation Trommelrechner").Cells(7, 2).Value)
    InnenbreiteTrommel = CDbl(Sheets("Simulation Trommelrechner").Cells(8, 2).Value)
    
    ' Berechnungen
    Dim KDMmZ As Double, HL As Integer, FR As Double, QL As Integer, MU As Double, KL As Double
    
    ' 1. Kabeldurchmesser inkl. Zuschlag
    KDMmZ = Kabeldurchmesser * (1 + ProzentualerZuschlag)
    
    ' 2. Hochlagenzahl (HL)
    HL = Int((Außendurchmesser - Kerndurchmesser - (2 * FreiraumKabelstamm)) / (2 * KDMmZ))
    
    ' 3. Tatsächlicher Trommelfreiraum (FR)
    FR = (Außendurchmesser - Kerndurchmesser) / 2 - HL * KDMmZ
    
    ' 4. Überprüfung des Freiraums und Anpassung von HL
    Do While FR < FreiraumKabelstamm
        HL = HL - 1
        FR = (Außendurchmesser - Kerndurchmesser) / 2 - HL * KDMmZ
    Loop
    
    ' 5. Querlagenzahl (QL)
    QL = Int(InnenbreiteTrommel / KDMmZ)
    
    ' 6. Mittlerer Trommelumfang (MU) unter Verwendung des Wildwickelfaktors 3,05
    MU = 3.05 * (Kerndurchmesser + HL * KDMmZ)
    
    ' 7. Maximale Kabellänge (KL)
    KL = HL * QL * MU
    
    ' Ergebnis in Zelle B12 anzeigen, umgerechnet in Meter, falls Eingabe in mm
    Sheets("Simulation Trommelrechner").Cells(12, 2).Value = KL / 1000
End Sub
```

