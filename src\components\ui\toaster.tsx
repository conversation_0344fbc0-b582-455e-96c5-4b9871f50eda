import * as React from "react"
import { Toast, ToastProvider, ToastViewport } from "./toast"
import { useToast, type ToasterToast } from "./use-toast"

/**
 * Toaster-Komponente für Benachrichtigungen
 * Zeigt Toast-Benachrichtigungen an verschiedenen Positionen an
 */
const Toaster: React.FC = () => {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(({ id, title, description, action, ...props }: ToasterToast) => (
        <Toast key={id} {...props}>
          <div className="grid gap-1">
            {title && <div className="font-semibold">{title}</div>}
            {description && (
              <div className="text-sm opacity-90">{description}</div>
            )}
          </div>
          {action}
        </Toast>
      ))}
      <ToastViewport />
    </ToastProvider>
  )
}

// Standard-Export für die Toaster-Komponente
export default Toaster;
