export interface SAPWorkflowProcess {
  id: string;
  name: string;
  description: string;
  tcode: string;
  exportDir: string;
  exportBasename: string;
  dbTable: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  lastRun?: Date;
  duration?: number;
  exportPath?: string;
}

export interface WorkflowExecution {
  id: string;
  processId: string;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'completed' | 'error';
  logs: WorkflowLog[];
  exportPath?: string;
  errorMessage?: string;
}

export interface WorkflowLog {
  id: string;
  timestamp: Date;
  level: 'info' | 'warning' | 'error';
  message: string;
  processId: string;
}

export interface WorkflowConfig {
  sapExecutablePath: string;
  sapSystemId: string;
  sapClient: string;
  sapLanguage: string;
  dbPath: string;
}

export interface WorkflowStats {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageDuration: number;
  lastExecution?: Date;
}

export interface WorkflowSchedule {
  enabled: boolean;
  frequency: 'hourly' | 'daily' | 'weekly';
  time?: string; // HH:MM format for daily/weekly
  dayOfWeek?: number; // 0-6 for weekly (0 = Sunday)
  interval?: number; // for hourly (every X hours)
}

export interface WorkflowConfig {
  id: string;
  name: string;
  description: string;
  databasePath?: string;
  exportPath?: string;
  emailRecipients?: string[];
  schedule: WorkflowSchedule;
  isActive: boolean;
  lastModified: Date;
}