import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import * as schema from './schema';
import * as relations from './relations';
import path from 'path';

// Pfad zur SFM Dashboard SQLite-Datenbankdatei
const dbPath = path.join(__dirname, '..', '..', 'backend', 'database', 'sfm_dashboard.db');

// SQLite-Verbindung mit wichtigen PRAGMA-Einstellungen
const sqlite = new Database(dbPath);

// Wichtige PRAGMA-Anweisungen für Performance und Integrität
sqlite.pragma('journal_mode = WAL');
sqlite.pragma('synchronous = NORMAL');
sqlite.pragma('foreign_keys = ON');

// Drizzle-Instanz mit dem Schema erstellen
export const db = drizzle(sqlite, { 
  schema: { ...schema, ...relations },
  logger: process.env.NODE_ENV === 'development'
});

// Export SQLite-Instanz für direkte Queries falls nötig
export const sqliteDB = sqlite;

export * from './schema';
export * from './relations';
