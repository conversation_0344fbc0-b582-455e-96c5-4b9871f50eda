import { eq, desc, count, avg, and, gte, inArray } from 'drizzle-orm';
import { db } from '../db';
import { stoerungen, stoerungsComments, stoerungsAttachment } from '../db/schema';
import { 
  StoerungCreateData, 
  StoerungUpdateData, 
  StoerungWithComments, 
  StoerungCommentCreateData,
  StoerungsStats,
  SystemStatusData,
  SystemStatusUpdateData
} from '../types/stoerungen.types';

// Simple in-memory cache for störungen - unchanged
const simpleCache = new Map<string, { data: any; expires: number }>();

const getCached = <T>(key: string): T | null => {
  const entry = simpleCache.get(key);
  if (entry && entry.expires > Date.now()) {
    return entry.data;
  }
  simpleCache.delete(key);
  return null;
};

const setCache = (key: string, data: any, ttlMs: number = 300000) => {
  simpleCache.set(key, { data, expires: Date.now() + ttlMs });
};

const clearCachePattern = (pattern: string) => {
  for (const key of simpleCache.keys()) {
    if (key.includes(pattern)) {
      simpleCache.delete(key);
    }
  }
};

export class StoerungenRepository {
  private static instance: StoerungenRepository;

  constructor() {}

  static getInstance(): StoerungenRepository {
    if (!StoerungenRepository.instance) {
      StoerungenRepository.instance = new StoerungenRepository();
    }
    return StoerungenRepository.instance;
  }

  async createStoerung(data: StoerungCreateData) {
    const tagsJson = data.tags ? JSON.stringify(data.tags) : null;
    
    const [stoerung] = await db
      .insert(stoerungen)
      .values({
        ...data,
        tags: tagsJson,
        updatedAt: Math.floor(Date.now() / 1000),
      })
      .returning();

    // Fetch comments separately
    const comments = await db
      .select()
      .from(stoerungsComments)
      .where(eq(stoerungsComments.stoerungId, stoerung.id))
      .orderBy(desc(stoerungsComments.createdAt));

    clearCachePattern('stoerungen');
    return this.formatStoerung({ ...stoerung, comments });
  }

  async getStoerungen(options?: {
    status?: string;
    severity?: string;
    category?: string;
    affected_system?: string;
    limit?: number;
    offset?: number;
  }) {
    const cacheKey = `stoerungen:list:${JSON.stringify(options || {})}`;
    const cached = getCached<StoerungWithComments[]>(cacheKey);
    if (cached) return cached;

    // Build where conditions
    const conditions = [];
    if (options?.status) conditions.push(eq(stoerungen.status, options.status));
    if (options?.severity) conditions.push(eq(stoerungen.severity, options.severity));
    if (options?.category) conditions.push(eq(stoerungen.category, options.category));
    if (options?.affected_system) conditions.push(eq(stoerungen.affectedSystem, options.affected_system));

    let query = db
      .select()
      .from(stoerungen)
      .orderBy(desc(stoerungen.createdAt));
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    if (options?.limit) {
      query = query.limit(options.limit);
    }
    
    if (options?.offset) {
      query = query.offset(options.offset);
    }

    const stoerungenList = await query;

    // Fetch comments for each störung
    const stoerungenWithComments = await Promise.all(
      stoerungenList.map(async (stoerung) => {
        const comments = await db
          .select()
          .from(stoerungsComments)
          .where(eq(stoerungsComments.stoerungId, stoerung.id))
          .orderBy(desc(stoerungsComments.createdAt));
        return { ...stoerung, comments };
      })
    );

    const formatted = stoerungenWithComments.map(this.formatStoerung);
    setCache(cacheKey, formatted, 300000); // 5 minutes TTL
    return formatted;
  }

  async getStoerungById(id: number): Promise<StoerungWithComments | null> {
    const cacheKey = `stoerungen:detail:${id}`;
    const cached = getCached<StoerungWithComments>(cacheKey);
    if (cached) return cached;

    const [stoerung] = await db
      .select()
      .from(stoerungen)
      .where(eq(stoerungen.id, id))
      .limit(1);

    if (!stoerung) return null;

    // Fetch comments separately
    const comments = await db
      .select()
      .from(stoerungsComments)
      .where(eq(stoerungsComments.stoerungId, stoerung.id))
      .orderBy(desc(stoerungsComments.createdAt));

    const formatted = this.formatStoerung({ ...stoerung, comments });
    setCache(cacheKey, formatted, 300000);
    return formatted;
  }

  async updateStoerung(id: number, data: StoerungUpdateData) {
    const updateData: any = { ...data };
    
    if (data.tags) {
      updateData.tags = JSON.stringify(data.tags);
    }

    if (data.status === 'RESOLVED' && !data.resolved_at) {
      updateData.resolvedAt = Math.floor(Date.now() / 1000);
    }

    const [stoerung] = await db
      .update(stoerungen)
      .set(updateData)
      .where(eq(stoerungen.id, id))
      .returning();

    // Fetch comments separately
    const comments = await db
      .select()
      .from(stoerungsComments)
      .where(eq(stoerungsComments.stoerungId, stoerung.id))
      .orderBy(desc(stoerungsComments.createdAt));

    clearCachePattern('stoerungen');
    return this.formatStoerung({ ...stoerung, comments });
  }

  async deleteStoerung(id: number): Promise<boolean> {
    try {
      await db
        .delete(stoerungen)
        .where(eq(stoerungen.id, id));
      
      clearCachePattern('stoerungen');
      return true;
    } catch {
      return false;
    }
  }

  async addComment(data: StoerungCommentCreateData) {
    const [comment] = await db
      .insert(stoerungsComments)
      .values(data)
      .returning();

    clearCachePattern('stoerungen');
    return comment;
  }

  async getStoerungsStats(): Promise<StoerungsStats> {
    const cacheKey = 'stoerungen:stats';
    const cached = getCached<StoerungsStats>(cacheKey);
    if (cached) return cached;

    // Parallel execution of all stats queries
    const [
      totalResult,
      activeResult,
      resolvedResult,
      severityStats,
      avgMttrResult,
      recentResult
    ] = await Promise.all([
      // Total count
      db.select({ count: count() }).from(stoerungen),
      
      // Active count
      db.select({ count: count() })
        .from(stoerungen)
        .where(inArray(stoerungen.status, ['NEW', 'IN_PROGRESS'])),
      
      // Resolved count
      db.select({ count: count() })
        .from(stoerungen)
        .where(eq(stoerungen.status, 'RESOLVED')),
      
      // By severity - we need to implement custom groupBy
      db.select({
        severity: stoerungen.severity,
        count: count()
      })
        .from(stoerungen)
        .groupBy(stoerungen.severity),
      
      // Average MTTR
      db.select({ avgMttr: avg(stoerungen.mttrMinutes) })
        .from(stoerungen)
        .where(and(
          eq(stoerungen.status, 'RESOLVED'),
          // mttr_minutes is not null wird durch Drizzle automatisch gehandhabt
        )),
      
      // Recent resolved (last 24h)
      db.select({ count: count() })
        .from(stoerungen)
        .where(and(
          eq(stoerungen.status, 'RESOLVED'),
          gte(stoerungen.resolvedAt, (Date.now() - 24 * 60 * 60 * 1000) / 1000)
        ))
    ]);

    const stats: StoerungsStats = {
      total: totalResult[0].count,
      active: activeResult[0].count,
      resolved: resolvedResult[0].count,
      bySeverity: severityStats.reduce((acc, item) => {
        if (item.severity) {
          acc[item.severity] = item.count;
        }
        return acc;
      }, {} as Record<string, number>),
      avgMttr: avgMttrResult[0].avgMttr || 0,
      recent: recentResult[0].count
    };

    setCache(cacheKey, stats, 300000);
    return stats;
  }

  private formatStoerung(stoerung: any): StoerungWithComments {
    return {
      ...stoerung,
      tags: stoerung.tags ? JSON.parse(stoerung.tags) : [],
      created_at: stoerung.createdAt,
      updated_at: stoerung.updatedAt,
      resolved_at: stoerung.resolvedAt,
      affected_system: stoerung.affectedSystem,
      reported_by: stoerung.reportedBy,
      assigned_to: stoerung.assignedTo,
      mttr_minutes: stoerung.mttrMinutes,
      resolution_steps: stoerung.resolutionSteps,
      root_cause: stoerung.rootCause,
      lessons_learned: stoerung.lessonsLearned,
      comments: stoerung.comments || []
    };
  }
}

// Export singleton instance
export const stoerungenRepository = StoerungenRepository.getInstance();
