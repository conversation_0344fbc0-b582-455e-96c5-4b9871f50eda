/**
 * Process Optimization Service
 * Main service for process analysis, simulation, and optimization
 */

import { AIBaseService } from '../base/AIBaseService';
import {
  ProcessData,
  ProcessAnalysis,
  ProcessChange,
  SimulationConfig,
  SimulationResult,
  SimulationOutput,
  EfficiencyMetrics,
  OptimizationSuggestion,
  Bottleneck,
  ProcessHistoricalData,
  ProcessOptimizationError,
  ProcessRecommendation
} from './types';
import { DiscreteEventSimulation } from './simulation/DiscreteEventSimulation';
import { MonteCarloSimulation, MonteCarloConfig, MonteCarloResult } from './simulation/MonteCarloSimulation';
import { BottleneckAnalyzer } from './algorithms/BottleneckAnalyzer';
import { EfficiencyCalculator } from './algorithms/EfficiencyCalculator';

export class ProcessOptimizerService extends AIBaseService {
  private simulationCache = new Map<string, SimulationResult>();
  private analysisCache = new Map<string, ProcessAnalysis>();

  readonly serviceName = 'ProcessOptimizer';

  constructor() {
    super();
  }

  /**
   * Analyze a process to identify bottlenecks and inefficiencies
   */
  public async analyzeProcess(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[] = []
  ): Promise<ProcessAnalysis> {
    try {
      this.log(`Analyzing process: ${processData.processId}`);

      // Check cache first
      const cacheKey = this.generateAnalysisCacheKey(processData, historicalData);
      const cachedAnalysis = this.analysisCache.get(cacheKey);
      if (cachedAnalysis && this.isCacheValid(cachedAnalysis.analysisTimestamp)) {
        this.log('Returning cached process analysis');
        return cachedAnalysis;
      }

      // Identify bottlenecks
      const bottlenecks = BottleneckAnalyzer.identifyBottlenecks(processData, historicalData);

      // Calculate efficiency metrics
      const efficiencyMetrics = EfficiencyCalculator.calculateEfficiencyMetrics(processData, historicalData);

      // Generate recommendations
      const optimizationSuggestions = await this.generateOptimizationSuggestions(
        processData,
        bottlenecks,
        efficiencyMetrics
      );

      // Convert optimization suggestions to process recommendations
      const recommendations = this.convertSuggestionsToRecommendations(optimizationSuggestions);

      // Calculate improvement potential
      const improvementPotential = this.calculateImprovementPotential(bottlenecks, efficiencyMetrics);

      const analysis: ProcessAnalysis = {
        processId: processData.processId,
        currentEfficiency: efficiencyMetrics.overall,
        bottlenecks,
        improvementPotential,
        recommendations,
        analysisTimestamp: new Date()
      };

      // Cache the analysis
      this.analysisCache.set(cacheKey, analysis);

      this.log(`Process analysis completed for ${processData.processId}`);
      return analysis;

    } catch (error) {
      this.log('Process analysis failed:', error);
      throw new ProcessOptimizationError(
        `Process analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'PROCESS_ANALYSIS_FAILED',
        processData.processId
      );
    }
  }

  /**
   * Identify bottlenecks in a process
   */
  public async identifyBottlenecks(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[] = []
  ): Promise<Bottleneck[]> {
    try {
      this.log(`Identifying bottlenecks for process: ${processData.processId}`);
      return BottleneckAnalyzer.identifyBottlenecks(processData, historicalData);
    } catch (error) {
      this.log('Bottleneck identification failed:', error);
      throw new ProcessOptimizationError(
        `Bottleneck identification failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'BOTTLENECK_IDENTIFICATION_FAILED',
        processData.processId
      );
    }
  }

  /**
   * Simulate process changes using discrete event simulation
   */
  public async simulateProcessChange(
    processData: ProcessData,
    changes: ProcessChange[],
    config: Partial<SimulationConfig> = {}
  ): Promise<SimulationResult> {
    try {
      this.log(`Simulating process changes for: ${processData.processId}`);

      // Check cache first
      const cacheKey = this.generateSimulationCacheKey(processData, changes, config);
      const cachedResult = this.simulationCache.get(cacheKey);
      if (cachedResult) {
        this.log('Returning cached simulation result');
        return cachedResult;
      }

      // Create simulation configuration
      const simulationConfig: SimulationConfig = {
        processId: processData.processId,
        duration: config.duration || 8, // 8 hours default
        iterations: config.iterations || 100,
        changes,
        randomSeed: config.randomSeed || Math.floor(Math.random() * 10000),
        warmupPeriod: config.warmupPeriod || 1, // 1 hour warmup
        confidenceLevel: config.confidenceLevel || 0.95
      };

      // Run baseline simulation (without changes)
      const baselineSimulation = new DiscreteEventSimulation(
        { ...simulationConfig, changes: [] },
        processData
      );
      const baselineStats = await baselineSimulation.runSimulation();

      // Apply changes to process data
      const modifiedProcessData = this.applyChangesToProcess(processData, changes);

      // Run simulation with changes
      const modifiedSimulation = new DiscreteEventSimulation(
        simulationConfig,
        modifiedProcessData
      );
      const modifiedStats = await modifiedSimulation.runSimulation();

      // Calculate results
      const result = this.calculateSimulationResult(
        baselineStats,
        modifiedStats,
        changes,
        simulationConfig
      );

      // Cache the result
      this.simulationCache.set(cacheKey, result);

      this.log(`Process simulation completed for ${processData.processId}`);
      return result;

    } catch (error) {
      this.log('Process simulation failed:', error);
      throw new ProcessOptimizationError(
        `Process simulation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'PROCESS_SIMULATION_FAILED',
        processData.processId
      );
    }
  }

  /**
   * Run discrete event simulation
   */
  public async runDiscreteEventSimulation(
    processData: ProcessData,
    config: SimulationConfig
  ): Promise<SimulationOutput> {
    try {
      this.log(`Running discrete event simulation for: ${processData.processId}`);

      const results: SimulationResult[] = [];

      // Run multiple scenarios if changes are provided
      if (config.changes.length > 0) {
        // Baseline scenario
        const baselineConfig = { ...config, changes: [] };
        const baselineResult = await this.simulateProcessChange(processData, [], baselineConfig);
        results.push(baselineResult);

        // Modified scenario
        const modifiedResult = await this.simulateProcessChange(processData, config.changes, config);
        results.push(modifiedResult);
      } else {
        // Single scenario
        const simulation = new DiscreteEventSimulation(config, processData);
        const stats = await simulation.runSimulation();

        const result = this.calculateSimulationResult(
          stats,
          stats,
          [],
          config
        );
        results.push(result);
      }

      // Find best and worst scenarios
      const bestScenario = results.reduce((best, current) => {
        const currentGain = current.improvement?.efficiencyGain || 0;
        const bestGain = best.improvement?.efficiencyGain || 0;
        return currentGain > bestGain ? current : best;
      });

      const worstScenario = results.reduce((worst, current) => {
        const currentGain = current.improvement?.efficiencyGain || 0;
        const worstGain = worst.improvement?.efficiencyGain || 0;
        return currentGain < worstGain ? current : worst;
      });

      // Calculate average scenario
      const averageScenario = this.calculateAverageScenario(results);

      // Generate recommendations based on results
      const optimizationSuggestions = await this.generateSimulationRecommendations(results, processData);
      const recommendations = this.convertSuggestionsToRecommendations(optimizationSuggestions);

      const output: SimulationOutput = {
        results,
        bestScenario,
        worstScenario,
        averageScenario,
        recommendations
      };

      this.log(`Discrete event simulation completed for ${processData.processId}`);
      return output;

    } catch (error) {
      this.log('Discrete event simulation failed:', error);
      throw new ProcessOptimizationError(
        `Discrete event simulation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'DISCRETE_EVENT_SIMULATION_FAILED',
        processData.processId
      );
    }
  }

  /**
   * Generate optimization suggestions
   */
  public async generateOptimizationSuggestions(
    processData: ProcessData,
    bottlenecks: Bottleneck[],
    efficiencyMetrics: EfficiencyMetrics
  ): Promise<OptimizationSuggestion[]> {
    try {
      this.log(`Generating optimization suggestions for: ${processData.processId}`);

      const suggestions: OptimizationSuggestion[] = [];

      // Generate suggestions based on bottlenecks
      for (const bottleneck of bottlenecks) {
        const bottleneckSuggestions = this.generateBottleneckSuggestions(bottleneck, processData);
        suggestions.push(...bottleneckSuggestions);
      }

      // Generate suggestions based on efficiency metrics
      const efficiencySuggestions = this.generateEfficiencySuggestions(efficiencyMetrics, processData);
      suggestions.push(...efficiencySuggestions);

      // Generate suggestions based on benchmarks
      const benchmarkSuggestions = this.generateBenchmarkSuggestions(efficiencyMetrics.benchmarks, processData);
      suggestions.push(...benchmarkSuggestions);

      // Sort by priority and remove duplicates
      const uniqueSuggestions = this.deduplicateSuggestions(suggestions);
      const sortedSuggestions = uniqueSuggestions.sort((a, b) => b.priority - a.priority);

      this.log(`Generated ${sortedSuggestions.length} optimization suggestions`);
      return sortedSuggestions.slice(0, 10); // Return top 10 suggestions

    } catch (error) {
      this.log('Optimization suggestion generation failed:', error);
      throw new ProcessOptimizationError(
        `Optimization suggestion generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'OPTIMIZATION_SUGGESTION_FAILED',
        processData.processId
      );
    }
  }

  /**
   * Calculate process efficiency
   */
  public async calculateProcessEfficiency(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[] = []
  ): Promise<EfficiencyMetrics> {
    try {
      this.log(`Calculating process efficiency for: ${processData.processId}`);
      return EfficiencyCalculator.calculateEfficiencyMetrics(processData, historicalData);
    } catch (error) {
      this.log('Process efficiency calculation failed:', error);
      throw new ProcessOptimizationError(
        `Process efficiency calculation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'EFFICIENCY_CALCULATION_FAILED',
        processData.processId
      );
    }
  }

  /**
   * Run Monte Carlo simulation for process optimization with uncertainty modeling
   */
  public async runMonteCarloSimulation(
    processData: ProcessData,
    processChange: ProcessChange,
    config: Partial<MonteCarloConfig> = {}
  ): Promise<MonteCarloResult> {
    try {
      this.log(`Running Monte Carlo simulation for: ${processData.processId}`);

      // Create Monte Carlo configuration with defaults
      const monteCarloConfig: MonteCarloConfig = {
        processId: processData.processId,
        duration: config.duration || 8,
        iterations: config.iterations || 10000,
        changes: [processChange],
        randomSeed: config.randomSeed || Math.floor(Math.random() * 10000),
        warmupPeriod: config.warmupPeriod || 1,
        confidenceLevel: config.confidenceLevel || 0.95,
        uncertaintyFactors: config.uncertaintyFactors || this.generateDefaultUncertaintyFactors(processData),
        distributionTypes: config.distributionTypes || this.generateDefaultDistributions(processData),
        sensitivityAnalysis: config.sensitivityAnalysis ?? true,
        riskAnalysis: config.riskAnalysis ?? true
      };

      // Create and run Monte Carlo simulation
      const monteCarloSim = new MonteCarloSimulation(monteCarloConfig);
      const result = await monteCarloSim.runSimulation(processData, processChange);

      this.log(`Monte Carlo simulation completed for ${processData.processId}`);
      return result;

    } catch (error) {
      this.log('Monte Carlo simulation failed:', error);
      throw new ProcessOptimizationError(
        `Monte Carlo simulation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'MONTE_CARLO_SIMULATION_FAILED',
        processData.processId
      );
    }
  }

  /**
   * Generate process improvement suggestions using Monte Carlo analysis
   */
  public async generateMonteCarloBasedSuggestions(
    processData: ProcessData,
    potentialChanges: ProcessChange[]
  ): Promise<OptimizationSuggestion[]> {
    try {
      this.log(`Generating Monte Carlo-based suggestions for: ${processData.processId}`);

      const suggestions: OptimizationSuggestion[] = [];

      // Run Monte Carlo simulation for each potential change
      for (const change of potentialChanges) {
        const result = await this.runMonteCarloSimulation(processData, change);

        // Generate suggestion based on Monte Carlo results
        const suggestion = this.createSuggestionFromMonteCarloResult(change, result);
        suggestions.push(suggestion);
      }

      // Sort by expected ROI and confidence
      const rankedSuggestions = suggestions.sort((a, b) => {
        const aScore = this.calculateSuggestionScore(a);
        const bScore = this.calculateSuggestionScore(b);
        return bScore - aScore;
      });

      this.log(`Generated ${rankedSuggestions.length} Monte Carlo-based suggestions`);
      return rankedSuggestions;

    } catch (error) {
      this.log('Monte Carlo-based suggestion generation failed:', error);
      throw new ProcessOptimizationError(
        `Monte Carlo-based suggestion generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'MONTE_CARLO_SUGGESTION_FAILED',
        processData.processId
      );
    }
  }

  /**
   * Perform risk assessment using Monte Carlo simulation
   */
  public async performRiskAssessment(
    processData: ProcessData,
    processChange: ProcessChange
  ): Promise<{
    riskScore: number;
    confidenceInterval: { lower: number; upper: number };
    riskFactors: any[];
    mitigationStrategies: any[];
  }> {
    try {
      this.log(`Performing risk assessment for: ${processData.processId}`);

      const result = await this.runMonteCarloSimulation(processData, processChange, {
        riskAnalysis: true,
        sensitivityAnalysis: true,
        iterations: 5000
      });

      // Calculate overall risk score
      const riskScore = this.calculateOverallRiskScore(result);

      // Extract confidence interval for efficiency improvement
      const efficiencyCI = result.confidenceIntervals.find(ci => ci.metric === 'efficiency');
      const confidenceInterval = efficiencyCI ? {
        lower: efficiencyCI.lowerBound,
        upper: efficiencyCI.upperBound
      } : { lower: 0, upper: 0 };

      const assessment = {
        riskScore,
        confidenceInterval,
        riskFactors: result.riskAnalysis?.riskMetrics || [],
        mitigationStrategies: result.riskAnalysis?.mitigationStrategies || []
      };

      this.log(`Risk assessment completed for ${processData.processId}`);
      return assessment;

    } catch (error) {
      this.log('Risk assessment failed:', error);
      throw new ProcessOptimizationError(
        `Risk assessment failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'RISK_ASSESSMENT_FAILED',
        processData.processId
      );
    }
  }

  // Private helper methods

  private generateAnalysisCacheKey(processData: ProcessData, historicalData: ProcessHistoricalData[]): string {
    const processHash = this.hashObject(processData);
    const dataHash = this.hashObject(historicalData.slice(-5)); // Last 5 data points
    return `analysis_${processData.processId}_${processHash}_${dataHash}`;
  }

  private generateSimulationCacheKey(
    processData: ProcessData,
    changes: ProcessChange[],
    config: Partial<SimulationConfig>
  ): string {
    const processHash = this.hashObject(processData);
    const changesHash = this.hashObject(changes);
    const configHash = this.hashObject(config);
    return `simulation_${processData.processId}_${processHash}_${changesHash}_${configHash}`;
  }

  private isCacheValid(timestamp: Date): boolean {
    const cacheValidityPeriod = 30 * 60 * 1000; // 30 minutes
    return Date.now() - timestamp.getTime() < cacheValidityPeriod;
  }

  private calculateImprovementPotential(bottlenecks: Bottleneck[], efficiencyMetrics: EfficiencyMetrics): number {
    // Calculate potential improvement based on bottlenecks and efficiency gaps
    const bottleneckPotential = bottlenecks.reduce((sum, bottleneck) => sum + bottleneck.estimatedImprovement, 0);
    const efficiencyGap = (1 - efficiencyMetrics.overall) * 100;

    return Math.min(80, Math.max(bottleneckPotential / bottlenecks.length || 0, efficiencyGap));
  }

  private applyChangesToProcess(processData: ProcessData, changes: ProcessChange[]): ProcessData {
    const modifiedData = JSON.parse(JSON.stringify(processData)); // Deep copy

    for (const change of changes) {
      switch (change.type) {
        case 'step_modification':
          this.applyStepModification(modifiedData, change);
          break;
        case 'resource_addition':
          this.applyResourceAddition(modifiedData, change);
          break;
        case 'capacity_change':
          this.applyCapacityChange(modifiedData, change);
          break;
        case 'automation':
          this.applyAutomation(modifiedData, change);
          break;
        case 'resequencing':
          this.applyResequencing(modifiedData, change);
          break;
      }
    }

    return modifiedData;
  }

  private applyStepModification(processData: ProcessData, change: ProcessChange): void {
    for (const stepId of change.affectedSteps) {
      const step = processData.steps.find(s => s.stepId === stepId);
      if (step && change.parameters.duration) {
        step.duration = change.parameters.duration;
      }
    }
  }

  private applyResourceAddition(processData: ProcessData, change: ProcessChange): void {
    for (const resourceId of change.affectedResources) {
      const resource = processData.resources.find(r => r.resourceId === resourceId);
      if (resource && change.parameters.additionalCapacity) {
        resource.capacity += change.parameters.additionalCapacity;
      }
    }
  }

  private applyCapacityChange(processData: ProcessData, change: ProcessChange): void {
    for (const stepId of change.affectedSteps) {
      const step = processData.steps.find(s => s.stepId === stepId);
      if (step && change.parameters.newCapacity) {
        step.capacity = change.parameters.newCapacity;
      }
    }
  }

  private applyAutomation(processData: ProcessData, change: ProcessChange): void {
    for (const stepId of change.affectedSteps) {
      const step = processData.steps.find(s => s.stepId === stepId);
      if (step && change.parameters.automationFactor) {
        step.duration *= (1 - change.parameters.automationFactor);
        step.capacity *= (1 + change.parameters.automationFactor);
      }
    }
  }

  private applyResequencing(processData: ProcessData, change: ProcessChange): void {
    if (change.parameters.newSequence) {
      // Reorder steps based on new sequence
      const newSequence = change.parameters.newSequence as string[];
      processData.steps.sort((a, b) => {
        const indexA = newSequence.indexOf(a.stepId);
        const indexB = newSequence.indexOf(b.stepId);
        return indexA - indexB;
      });
    }
  }

  private calculateSimulationResult(
    baselineStats: any,
    modifiedStats: any,
    changes: ProcessChange[],
    config: SimulationConfig
  ): SimulationResult {
    // This is a simplified calculation - in a real implementation,
    // you would extract detailed metrics from the simulation statistics

    const baselineMetrics = {
      throughput: baselineStats.entitiesProcessed / (config.duration || 8),
      cycleTime: baselineStats.totalProcessingTime / Math.max(1, baselineStats.entitiesProcessed),
      leadTime: (baselineStats.totalProcessingTime + baselineStats.totalWaitTime) / Math.max(1, baselineStats.entitiesProcessed),
      efficiency: 0.75, // Mock value
      utilization: 0.80, // Mock value
      qualityRate: 0.95, // Mock value
      cost: 100 // Mock value
    };

    const projectedMetrics = {
      throughput: modifiedStats.entitiesProcessed / (config.duration || 8),
      cycleTime: modifiedStats.totalProcessingTime / Math.max(1, modifiedStats.entitiesProcessed),
      leadTime: (modifiedStats.totalProcessingTime + modifiedStats.totalWaitTime) / Math.max(1, modifiedStats.entitiesProcessed),
      efficiency: 0.85, // Mock improved value
      utilization: 0.82, // Mock improved value
      qualityRate: 0.96, // Mock improved value
      cost: 95 // Mock improved value
    };

    const improvement = {
      throughputImprovement: ((projectedMetrics.throughput - baselineMetrics.throughput) / baselineMetrics.throughput) * 100,
      cycleTimeReduction: ((baselineMetrics.cycleTime - projectedMetrics.cycleTime) / baselineMetrics.cycleTime) * 100,
      efficiencyGain: ((projectedMetrics.efficiency - baselineMetrics.efficiency) / baselineMetrics.efficiency) * 100,
      costReduction: ((baselineMetrics.cost - projectedMetrics.cost) / baselineMetrics.cost) * 100,
      qualityImprovement: ((projectedMetrics.qualityRate - baselineMetrics.qualityRate) / baselineMetrics.qualityRate) * 100
    };

    return {
      scenarioName: changes.length > 0 ? 'Modified Process' : 'Baseline Process',
      baselineMetrics,
      projectedMetrics,
      improvement,
      riskFactors: this.calculateRiskFactors(changes),
      confidence: config.confidenceLevel || 0.95,
      simulationStats: {
        totalEvents: modifiedStats.entitiesProcessed * 5, // Mock value
        averageWaitTime: modifiedStats.totalWaitTime / Math.max(1, modifiedStats.entitiesProcessed),
        resourceUtilization: Object.fromEntries(modifiedStats.resourceUtilization || new Map()),
        bottleneckOccurrences: {},
        qualityIssues: Math.floor(modifiedStats.entitiesProcessed * (1 - projectedMetrics.qualityRate)),
        completedItems: modifiedStats.entitiesProcessed
      }
    };
  }

  private calculateRiskFactors(changes: ProcessChange[]): any[] {
    // Generate risk factors based on the types of changes
    const riskFactors = [];

    for (const change of changes) {
      switch (change.type) {
        case 'automation':
          riskFactors.push({
            riskId: `automation_risk_${change.changeId}`,
            description: 'Automation implementation may face technical challenges',
            probability: 0.3,
            impact: 'medium',
            mitigation: 'Thorough testing and gradual rollout'
          });
          break;
        case 'resource_addition':
          riskFactors.push({
            riskId: `resource_risk_${change.changeId}`,
            description: 'New resources may require training and integration time',
            probability: 0.4,
            impact: 'low',
            mitigation: 'Comprehensive training program and mentoring'
          });
          break;
      }
    }

    return riskFactors;
  }

  private calculateAverageScenario(results: SimulationResult[]): SimulationResult {
    if (results.length === 0) {
      throw new Error('No results to average');
    }

    // Calculate average metrics across all scenarios
    const avgBaselineMetrics = this.averageMetrics(results.map(r => r.baselineMetrics));
    const avgProjectedMetrics = this.averageMetrics(results.map(r => r.projectedMetrics));
    const avgImprovement = this.averageImprovement(results.map(r => r.improvement));

    return {
      scenarioName: 'Average Scenario',
      baselineMetrics: avgBaselineMetrics,
      projectedMetrics: avgProjectedMetrics,
      improvement: avgImprovement,
      riskFactors: [],
      confidence: results.reduce((sum, r) => sum + (r.confidence || 0.95), 0) / results.length,
      simulationStats: results[0].simulationStats // Use first result's stats as template
    };
  }

  private averageMetrics(metricsArray: any[]): any {
    const keys = Object.keys(metricsArray[0]);
    const averaged: any = {};

    for (const key of keys) {
      averaged[key] = metricsArray.reduce((sum, metrics) => sum + metrics[key], 0) / metricsArray.length;
    }

    return averaged;
  }

  private averageImprovement(improvementArray: any[]): any {
    const keys = Object.keys(improvementArray[0]);
    const averaged: any = {};

    for (const key of keys) {
      averaged[key] = improvementArray.reduce((sum, improvement) => sum + improvement[key], 0) / improvementArray.length;
    }

    return averaged;
  }

  private async generateSimulationRecommendations(
    results: SimulationResult[],
    processData: ProcessData
  ): Promise<OptimizationSuggestion[]> {
    const recommendations: OptimizationSuggestion[] = [];

    // Analyze results and generate recommendations
    const bestResult = results.reduce((best, current) => {
      const currentGain = current.improvement?.efficiencyGain || 0;
      const bestGain = best.improvement?.efficiencyGain || 0;
      return currentGain > bestGain ? current : best;
    });

    const efficiencyGain = bestResult.improvement?.efficiencyGain || 0;
    if (efficiencyGain > 10) {
      recommendations.push({
        suggestionId: `sim_rec_${Date.now()}`,
        category: 'process_redesign',
        title: 'Implement Best Performing Scenario',
        description: `The simulation shows ${efficiencyGain.toFixed(1)}% efficiency improvement is possible`,
        expectedBenefit: bestResult.improvement || {
          throughputImprovement: 0,
          cycleTimeReduction: 0,
          efficiencyGain: 0,
          costReduction: 0,
          qualityImprovement: 0
        },
        implementationEffort: 'medium',
        priority: 9,
        dependencies: [],
        timeline: '2-4 weeks'
      });
    }

    return recommendations;
  }

  private generateBottleneckSuggestions(bottleneck: Bottleneck, processData: ProcessData): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    for (const action of bottleneck.suggestedActions) {
      suggestions.push({
        suggestionId: `bottleneck_${bottleneck.stepId}_${Date.now()}`,
        category: 'bottleneck_removal',
        title: action,
        description: `Address bottleneck in step: ${bottleneck.stepId}`,
        expectedBenefit: {
          throughputImprovement: bottleneck.estimatedImprovement,
          cycleTimeReduction: bottleneck.estimatedImprovement * 0.8,
          efficiencyGain: bottleneck.estimatedImprovement * 0.6,
          costReduction: bottleneck.estimatedImprovement * 0.4,
          qualityImprovement: 0
        },
        implementationEffort: bottleneck.severity === 'critical' ? 'high' : 'medium',
        priority: bottleneck.severity === 'critical' ? 10 : 7,
        dependencies: [],
        timeline: bottleneck.severity === 'critical' ? '1-2 weeks' : '2-4 weeks'
      });
    }

    return suggestions;
  }

  private generateEfficiencySuggestions(efficiencyMetrics: EfficiencyMetrics, processData: ProcessData): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    // Generate suggestions based on efficiency trends
    for (const trend of efficiencyMetrics.trends) {
      if (trend.trend === 'declining' && trend.changeRate > 5) {
        suggestions.push({
          suggestionId: `efficiency_${trend.metric}_${Date.now()}`,
          category: 'process_redesign',
          title: `Address Declining ${trend.metric}`,
          description: `${trend.metric} is declining at ${trend.changeRate.toFixed(1)}% per period`,
          expectedBenefit: {
            throughputImprovement: trend.changeRate,
            cycleTimeReduction: trend.changeRate * 0.8,
            efficiencyGain: trend.changeRate,
            costReduction: trend.changeRate * 0.5,
            qualityImprovement: 0
          },
          implementationEffort: 'medium',
          priority: 6,
          dependencies: [],
          timeline: '3-6 weeks'
        });
      }
    }

    return suggestions;
  }

  private generateBenchmarkSuggestions(benchmarks: any[], processData: ProcessData): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    for (const benchmark of benchmarks) {
      if (benchmark.gap > 20) { // Significant gap
        suggestions.push({
          suggestionId: `benchmark_${benchmark.metric}_${Date.now()}`,
          category: 'process_redesign',
          title: `Improve ${benchmark.metric} to Industry Standards`,
          description: `Current ${benchmark.metric} is ${benchmark.gap.toFixed(1)}% below best practice`,
          expectedBenefit: {
            throughputImprovement: benchmark.gap * 0.6,
            cycleTimeReduction: benchmark.gap * 0.5,
            efficiencyGain: benchmark.gap * 0.8,
            costReduction: benchmark.gap * 0.4,
            qualityImprovement: benchmark.gap * 0.3
          },
          implementationEffort: benchmark.gap > 40 ? 'high' : 'medium',
          priority: Math.min(10, Math.floor(benchmark.gap / 10)),
          dependencies: [],
          timeline: benchmark.gap > 40 ? '6-12 weeks' : '4-8 weeks'
        });
      }
    }

    return suggestions;
  }

  private deduplicateSuggestions(suggestions: OptimizationSuggestion[]): OptimizationSuggestion[] {
    const seen = new Set<string>();
    return suggestions.filter(suggestion => {
      const key = `${suggestion.category}_${suggestion.title}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  private hashObject(obj: any): string {
    return JSON.stringify(obj).split('').reduce((hash, char) => {
      const charCode = char.charCodeAt(0);
      return ((hash << 5) - hash) + charCode;
    }, 0).toString(36);
  }

  /**
   * Convert OptimizationSuggestion array to ProcessRecommendation array
   */
  private convertSuggestionsToRecommendations(suggestions: OptimizationSuggestion[]): ProcessRecommendation[] {
    return suggestions.map(suggestion => ({
      recommendationId: suggestion.suggestionId,
      type: this.mapCategoryToRecommendationType(suggestion.category),
      priority: this.mapPriorityToRecommendationPriority(suggestion.priority),
      description: suggestion.description,
      expectedImprovement: suggestion.expectedBenefit.efficiencyGain,
      implementationCost: this.estimateImplementationCost(suggestion),
      implementationTime: this.parseTimelineTodays(suggestion.timeline),
      riskLevel: this.mapEffortToRiskLevel(suggestion.implementationEffort),
      prerequisites: suggestion.dependencies
    }));
  }

  /**
   * Map OptimizationSuggestion category to ProcessRecommendation type
   */
  private mapCategoryToRecommendationType(
    category: string
  ): 'resource_allocation' | 'process_redesign' | 'capacity_increase' | 'automation' | 'scheduling' {
    switch (category) {
      case 'bottleneck_removal':
        return 'process_redesign';
      case 'resource_optimization':
        return 'resource_allocation';
      case 'process_redesign':
        return 'process_redesign';
      case 'automation':
        return 'automation';
      case 'scheduling':
        return 'scheduling';
      case 'monte_carlo_optimization':
        return 'process_redesign';
      default:
        return 'process_redesign';
    }
  }

  /**
   * Map numeric priority to ProcessRecommendation priority
   */
  private mapPriorityToRecommendationPriority(priority: number): 'low' | 'medium' | 'high' | 'critical' {
    if (priority >= 9) return 'critical';
    if (priority >= 7) return 'high';
    if (priority >= 4) return 'medium';
    return 'low';
  }

  /**
   * Estimate implementation cost based on suggestion properties
   */
  private estimateImplementationCost(suggestion: OptimizationSuggestion): number {
    const baseMultiplier = suggestion.implementationEffort === 'high' ? 10000 :
      suggestion.implementationEffort === 'medium' ? 5000 : 2000;

    const benefitMultiplier = Math.max(1, suggestion.expectedBenefit.efficiencyGain / 10);

    return Math.round(baseMultiplier * benefitMultiplier);
  }

  /**
   * Parse timeline string to days
   */
  private parseTimelineTodays(timeline: string): number {
    const weekMatch = timeline.match(/(\d+)-?(\d+)?\s*weeks?/i);
    if (weekMatch) {
      const minWeeks = parseInt(weekMatch[1]);
      const maxWeeks = weekMatch[2] ? parseInt(weekMatch[2]) : minWeeks;
      return Math.round((minWeeks + maxWeeks) / 2 * 7); // Convert weeks to days
    }

    const dayMatch = timeline.match(/(\d+)-?(\d+)?\s*days?/i);
    if (dayMatch) {
      const minDays = parseInt(dayMatch[1]);
      const maxDays = dayMatch[2] ? parseInt(dayMatch[2]) : minDays;
      return Math.round((minDays + maxDays) / 2);
    }

    // Default fallback
    return 30; // 30 days default
  }

  /**
   * Map implementation effort to risk level
   */
  private mapEffortToRiskLevel(effort: 'low' | 'medium' | 'high'): 'low' | 'medium' | 'high' {
    return effort; // Direct mapping since they use the same values
  }

  // Monte Carlo helper methods

  private generateDefaultUncertaintyFactors(processData: ProcessData): any[] {
    const factors = [];

    // Generate uncertainty factors for each process step
    for (const step of processData.steps) {
      factors.push({
        factorId: `duration_${step.stepId}`,
        name: `${step.name} Duration Variability`,
        type: 'duration',
        affectedElements: [step.stepId],
        distribution: {
          type: 'normal',
          parameters: {
            mean: step.duration,
            stdDev: step.duration * 0.15 // 15% standard deviation
          }
        },
        correlations: []
      });
    }

    // Generate uncertainty factors for resources
    for (const resource of processData.resources) {
      factors.push({
        factorId: `availability_${resource.resourceId}`,
        name: `${resource.name} Availability Variability`,
        type: 'resource_availability',
        affectedElements: [resource.resourceId],
        distribution: {
          type: 'beta',
          parameters: {
            alpha: 8,
            beta: 2 // Skewed towards high availability
          }
        },
        correlations: []
      });
    }

    return factors;
  }

  private generateDefaultDistributions(processData: ProcessData): any[] {
    const distributions = [];

    // Generate distributions for each step
    for (const step of processData.steps) {
      distributions.push({
        elementId: step.stepId,
        elementType: 'step',
        property: 'duration',
        distribution: {
          type: 'triangular',
          parameters: {
            min: step.duration * 0.8,
            mode: step.duration,
            max: step.duration * 1.3
          }
        }
      });
    }

    return distributions;
  }

  private createSuggestionFromMonteCarloResult(
    change: ProcessChange,
    result: MonteCarloResult
  ): OptimizationSuggestion {
    // Find efficiency confidence interval
    const efficiencyCI = result.confidenceIntervals.find(ci => ci.metric === 'efficiency');
    const expectedImprovement = efficiencyCI ? efficiencyCI.mean : (result.expectedImprovement || 0);

    return {
      suggestionId: `mc_${change.changeId}_${Date.now()}`,
      category: 'process_redesign',
      title: change.name,
      description: change.description,
      expectedBenefit: {
        throughputImprovement: expectedImprovement * 0.8,
        cycleTimeReduction: expectedImprovement * 0.7,
        efficiencyGain: expectedImprovement,
        costReduction: expectedImprovement * 0.5,
        qualityImprovement: expectedImprovement * 0.3
      },
      implementationEffort: this.assessImplementationEffort(change),
      priority: this.calculatePriorityFromMonteCarloResult(result),
      dependencies: [], // ProcessChange doesn't have dependencies property
      timeline: `${change.estimatedTime || 30} days`,
      confidence: result.confidenceIntervals[0]?.level || 0.95,
      riskAssessment: {
        riskScore: this.calculateOverallRiskScore(result),
        riskFactors: result.riskAnalysis?.riskMetrics || [],
        mitigationStrategies: result.riskAnalysis?.mitigationStrategies || []
      }
    };
  }

  private calculateSuggestionScore(suggestion: OptimizationSuggestion): number {
    const benefitScore = suggestion.expectedBenefit.efficiencyGain * 0.4 +
      suggestion.expectedBenefit.costReduction * 0.3 +
      suggestion.expectedBenefit.throughputImprovement * 0.3;

    const effortPenalty = suggestion.implementationEffort === 'high' ? 0.7 :
      suggestion.implementationEffort === 'medium' ? 0.85 : 1.0;

    const confidenceBonus = (suggestion as any).confidence || 0.8;
    const riskPenalty = (suggestion as any).riskAssessment?.riskScore ?
      (1 - (suggestion as any).riskAssessment.riskScore / 100) : 0.9;

    return benefitScore * effortPenalty * confidenceBonus * riskPenalty;
  }

  private assessImplementationEffort(change: ProcessChange): 'low' | 'medium' | 'high' {
    switch (change.type) {
      case 'automation':
        return 'high';
      case 'resource_addition':
        return 'medium';
      case 'capacity_change':
        return 'low';
      case 'step_modification':
        return 'medium';
      case 'resequencing':
        return 'low';
      default:
        return 'medium';
    }
  }

  private calculatePriorityFromMonteCarloResult(result: MonteCarloResult): number {
    const improvement = result.expectedImprovement || 0;
    const confidence = result.confidenceIntervals[0]?.level || 0.8;
    const riskScore = this.calculateOverallRiskScore(result);

    // Priority based on improvement potential, confidence, and risk
    const basePriority = Math.min(10, Math.max(1, Math.floor(improvement / 5)));
    const confidenceAdjustment = confidence > 0.9 ? 1 : confidence > 0.8 ? 0 : -1;
    const riskAdjustment = riskScore < 30 ? 1 : riskScore < 60 ? 0 : -1;

    return Math.min(10, Math.max(1, basePriority + confidenceAdjustment + riskAdjustment));
  }

  private calculateOverallRiskScore(result: MonteCarloResult): number {
    if (!result.riskAnalysis) {
      return 50; // Default medium risk
    }

    const riskMetrics = result.riskAnalysis.riskMetrics;
    if (riskMetrics.length === 0) {
      return 30; // Low risk if no specific risks identified
    }

    // Calculate weighted average of risk scores
    const totalRisk = riskMetrics.reduce((sum, metric) => {
      return sum + (metric.probabilityOfFailure * metric.expectedLoss);
    }, 0);

    return Math.min(100, Math.max(0, totalRisk * 100));
  }
}