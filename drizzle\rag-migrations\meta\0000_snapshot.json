{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "6", "dialect": "sqlite", "tables": {"_prisma_migrations": {"name": "_prisma_migrations", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "checksum": {"autoincrement": false, "name": "checksum", "type": "text", "primaryKey": false, "notNull": true}, "finished_at": {"autoincrement": false, "name": "finished_at", "type": "numeric", "primaryKey": false, "notNull": false}, "migration_name": {"autoincrement": false, "name": "migration_name", "type": "text", "primaryKey": false, "notNull": true}, "logs": {"autoincrement": false, "name": "logs", "type": "text", "primaryKey": false, "notNull": false}, "rolled_back_at": {"autoincrement": false, "name": "rolled_back_at", "type": "numeric", "primaryKey": false, "notNull": false}, "started_at": {"default": "(current_timestamp)", "autoincrement": false, "name": "started_at", "type": "numeric", "primaryKey": false, "notNull": true}, "applied_steps_count": {"default": 0, "autoincrement": false, "name": "applied_steps_count", "type": "integer", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "knowledge_bases": {"name": "knowledge_bases", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"autoincrement": false, "name": "category", "type": "text", "primaryKey": false, "notNull": true}, "isActive": {"default": true, "autoincrement": false, "name": "isActive", "type": "numeric", "primaryKey": false, "notNull": true}, "createdAt": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "createdAt", "type": "numeric", "primaryKey": false, "notNull": true}, "updatedAt": {"autoincrement": false, "name": "updatedAt", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"knowledge_bases_createdAt_idx": {"name": "knowledge_bases_createdAt_idx", "columns": ["createdAt"], "isUnique": false}, "knowledge_bases_isActive_idx": {"name": "knowledge_bases_isActive_idx", "columns": ["isActive"], "isUnique": false}, "knowledge_bases_category_idx": {"name": "knowledge_bases_category_idx", "columns": ["category"], "isUnique": false}, "knowledge_bases_name_key": {"name": "knowledge_bases_name_key", "columns": ["name"], "isUnique": true}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "documents": {"name": "documents", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "knowledge_base_id": {"autoincrement": false, "name": "knowledge_base_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"autoincrement": false, "name": "content", "type": "text", "primaryKey": false, "notNull": true}, "content_type": {"default": "'text/plain'", "autoincrement": false, "name": "content_type", "type": "text", "primaryKey": false, "notNull": true}, "source": {"autoincrement": false, "name": "source", "type": "text", "primaryKey": false, "notNull": true}, "source_path": {"autoincrement": false, "name": "source_path", "type": "text", "primaryKey": false, "notNull": false}, "language": {"default": "'de'", "autoincrement": false, "name": "language", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"autoincrement": false, "name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "hash": {"autoincrement": false, "name": "hash", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"default": true, "autoincrement": false, "name": "isActive", "type": "numeric", "primaryKey": false, "notNull": true}, "status": {"default": "'pending'", "autoincrement": false, "name": "status", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}, "indexed_at": {"autoincrement": false, "name": "indexed_at", "type": "numeric", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"idx_documents_created_at": {"name": "idx_documents_created_at", "columns": ["created_at"], "isUnique": false}, "idx_documents_status": {"name": "idx_documents_status", "columns": ["status"], "isUnique": false}, "idx_documents_source": {"name": "idx_documents_source", "columns": ["source"], "isUnique": false}, "documents_language_idx": {"name": "documents_language_idx", "columns": ["language"], "isUnique": false}, "documents_created_at_idx": {"name": "documents_created_at_idx", "columns": ["created_at"], "isUnique": false}, "documents_source_idx": {"name": "documents_source_idx", "columns": ["source"], "isUnique": false}, "documents_status_idx": {"name": "documents_status_idx", "columns": ["status"], "isUnique": false}, "documents_isActive_idx": {"name": "documents_isActive_idx", "columns": ["isActive"], "isUnique": false}, "documents_hash_idx": {"name": "documents_hash_idx", "columns": ["hash"], "isUnique": false}, "documents_knowledge_base_id_idx": {"name": "documents_knowledge_base_id_idx", "columns": ["knowledge_base_id"], "isUnique": false}, "documents_hash_key": {"name": "documents_hash_key", "columns": ["hash"], "isUnique": true}}, "foreignKeys": {"documents_knowledge_base_id_knowledge_bases_id_fk": {"name": "documents_knowledge_base_id_knowledge_bases_id_fk", "tableFrom": "documents", "tableTo": "knowledge_bases", "columnsFrom": ["knowledge_base_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "chunks": {"name": "chunks", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "document_id": {"autoincrement": false, "name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "chunk_index": {"autoincrement": false, "name": "chunk_index", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"autoincrement": false, "name": "content", "type": "text", "primaryKey": false, "notNull": true}, "token_count": {"autoincrement": false, "name": "token_count", "type": "integer", "primaryKey": false, "notNull": false}, "start_position": {"autoincrement": false, "name": "start_position", "type": "integer", "primaryKey": false, "notNull": false}, "end_position": {"autoincrement": false, "name": "end_position", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_chunks_chunk_index": {"name": "idx_chunks_chunk_index", "columns": ["chunk_index"], "isUnique": false}, "idx_chunks_document_id": {"name": "idx_chunks_document_id", "columns": ["document_id"], "isUnique": false}, "chunks_created_at_idx": {"name": "chunks_created_at_idx", "columns": ["created_at"], "isUnique": false}, "chunks_chunk_index_idx": {"name": "chunks_chunk_index_idx", "columns": ["chunk_index"], "isUnique": false}, "chunks_document_id_idx": {"name": "chunks_document_id_idx", "columns": ["document_id"], "isUnique": false}}, "foreignKeys": {"chunks_document_id_documents_id_fk": {"name": "chunks_document_id_documents_id_fk", "tableFrom": "chunks", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "embeddings": {"name": "embeddings", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "chunk_id": {"autoincrement": false, "name": "chunk_id", "type": "text", "primaryKey": false, "notNull": true}, "vector": {"autoincrement": false, "name": "vector", "type": "blob", "primaryKey": false, "notNull": true}, "model_name": {"default": "'text-embedding-3-small'", "autoincrement": false, "name": "model_name", "type": "text", "primaryKey": false, "notNull": true}, "dimension": {"default": 1536, "autoincrement": false, "name": "dimension", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_embeddings_model_name": {"name": "idx_embeddings_model_name", "columns": ["model_name"], "isUnique": false}, "idx_embeddings_chunk_id": {"name": "idx_embeddings_chunk_id", "columns": ["chunk_id"], "isUnique": false}, "embeddings_model_name_idx": {"name": "embeddings_model_name_idx", "columns": ["model_name"], "isUnique": false}, "embeddings_chunk_id_idx": {"name": "embeddings_chunk_id_idx", "columns": ["chunk_id"], "isUnique": false}}, "foreignKeys": {"embeddings_chunk_id_chunks_id_fk": {"name": "embeddings_chunk_id_chunks_id_fk", "tableFrom": "embeddings", "tableTo": "chunks", "columnsFrom": ["chunk_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "rag_queries": {"name": "rag_queries", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "query": {"autoincrement": false, "name": "query", "type": "text", "primaryKey": false, "notNull": true}, "query_embedding": {"autoincrement": false, "name": "query_embedding", "type": "blob", "primaryKey": false, "notNull": false}, "intent": {"autoincrement": false, "name": "intent", "type": "text", "primaryKey": false, "notNull": false}, "language": {"default": "'de'", "autoincrement": false, "name": "language", "type": "text", "primaryKey": false, "notNull": true}, "results_found": {"autoincrement": false, "name": "results_found", "type": "integer", "primaryKey": false, "notNull": true}, "top_similarity": {"autoincrement": false, "name": "top_similarity", "type": "real", "primaryKey": false, "notNull": false}, "response_generated": {"default": false, "autoincrement": false, "name": "response_generated", "type": "numeric", "primaryKey": false, "notNull": true}, "execution_time_ms": {"autoincrement": false, "name": "execution_time_ms", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"rag_queries_response_generated_idx": {"name": "rag_queries_response_generated_idx", "columns": ["response_generated"], "isUnique": false}, "rag_queries_language_idx": {"name": "rag_queries_language_idx", "columns": ["language"], "isUnique": false}, "rag_queries_created_at_idx": {"name": "rag_queries_created_at_idx", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "similarity_results": {"name": "similarity_results", "columns": {"id": {"autoincrement": true, "name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "query_id": {"autoincrement": false, "name": "query_id", "type": "text", "primaryKey": false, "notNull": true}, "chunk_id": {"autoincrement": false, "name": "chunk_id", "type": "text", "primaryKey": false, "notNull": true}, "similarity": {"autoincrement": false, "name": "similarity", "type": "real", "primaryKey": false, "notNull": true}, "rank": {"autoincrement": false, "name": "rank", "type": "integer", "primaryKey": false, "notNull": true}, "used": {"default": false, "autoincrement": false, "name": "used", "type": "numeric", "primaryKey": false, "notNull": true}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"similarity_results_used_idx": {"name": "similarity_results_used_idx", "columns": ["used"], "isUnique": false}, "similarity_results_rank_idx": {"name": "similarity_results_rank_idx", "columns": ["rank"], "isUnique": false}, "similarity_results_similarity_idx": {"name": "similarity_results_similarity_idx", "columns": ["similarity"], "isUnique": false}, "similarity_results_chunk_id_idx": {"name": "similarity_results_chunk_id_idx", "columns": ["chunk_id"], "isUnique": false}, "similarity_results_query_id_idx": {"name": "similarity_results_query_id_idx", "columns": ["query_id"], "isUnique": false}}, "foreignKeys": {"similarity_results_chunk_id_chunks_id_fk": {"name": "similarity_results_chunk_id_chunks_id_fk", "tableFrom": "similarity_results", "tableTo": "chunks", "columnsFrom": ["chunk_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "similarity_results_query_id_rag_queries_id_fk": {"name": "similarity_results_query_id_rag_queries_id_fk", "tableFrom": "similarity_results", "tableTo": "rag_queries", "columnsFrom": ["query_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "categories": {"name": "categories", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "text", "primaryKey": false, "notNull": false}, "parent_id": {"autoincrement": false, "name": "parent_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"categories_parent_id_idx": {"name": "categories_parent_id_idx", "columns": ["parent_id"], "isUnique": false}, "categories_name_key": {"name": "categories_name_key", "columns": ["name"], "isUnique": true}}, "foreignKeys": {"categories_parent_id_categories_id_fk": {"name": "categories_parent_id_categories_id_fk", "tableFrom": "categories", "tableTo": "categories", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "document_categories": {"name": "document_categories", "columns": {"document_id": {"autoincrement": false, "name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "category_id": {"autoincrement": false, "name": "category_id", "type": "text", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {"document_categories_document_id_category_id_pk": {"columns": ["document_id", "category_id"], "name": "document_categories_document_id_category_id_pk"}}, "indexes": {}, "foreignKeys": {"document_categories_category_id_categories_id_fk": {"name": "document_categories_category_id_categories_id_fk", "tableFrom": "document_categories", "tableTo": "categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}, "document_categories_document_id_documents_id_fk": {"name": "document_categories_document_id_documents_id_fk", "tableFrom": "document_categories", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "chunk_metadata": {"name": "chunk_metadata", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "chunk_id": {"autoincrement": false, "name": "chunk_id", "type": "text", "primaryKey": false, "notNull": true}, "key": {"autoincrement": false, "name": "key", "type": "text", "primaryKey": false, "notNull": true}, "value": {"autoincrement": false, "name": "value", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_chunk_metadata_key": {"name": "idx_chunk_metadata_key", "columns": ["key"], "isUnique": false}, "idx_chunk_metadata_chunk_id": {"name": "idx_chunk_metadata_chunk_id", "columns": ["chunk_id"], "isUnique": false}, "chunk_metadata_key_idx": {"name": "chunk_metadata_key_idx", "columns": ["key"], "isUnique": false}, "chunk_metadata_chunk_id_idx": {"name": "chunk_metadata_chunk_id_idx", "columns": ["chunk_id"], "isUnique": false}}, "foreignKeys": {"chunk_metadata_chunk_id_chunks_id_fk": {"name": "chunk_metadata_chunk_id_chunks_id_fk", "tableFrom": "chunk_metadata", "tableTo": "chunks", "columnsFrom": ["chunk_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "search_history": {"name": "search_history", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "query": {"autoincrement": false, "name": "query", "type": "text", "primaryKey": false, "notNull": true}, "query_embedding": {"autoincrement": false, "name": "query_embedding", "type": "blob", "primaryKey": false, "notNull": false}, "results_count": {"autoincrement": false, "name": "results_count", "type": "integer", "primaryKey": false, "notNull": false}, "top_similarity_score": {"autoincrement": false, "name": "top_similarity_score", "type": "real", "primaryKey": false, "notNull": false}, "response_time_ms": {"autoincrement": false, "name": "response_time_ms", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"autoincrement": false, "name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"idx_search_history_created_at": {"name": "idx_search_history_created_at", "columns": ["created_at"], "isUnique": false}, "search_history_created_at_idx": {"name": "search_history_created_at_idx", "columns": ["created_at"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "rag_settings": {"name": "rag_settings", "columns": {"id": {"autoincrement": false, "name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"default": false, "autoincrement": false, "name": "isActive", "type": "numeric", "primaryKey": false, "notNull": true}, "vector_database_config": {"autoincrement": false, "name": "vector_database_config", "type": "text", "primaryKey": false, "notNull": true}, "security_config": {"autoincrement": false, "name": "security_config", "type": "text", "primaryKey": false, "notNull": true}, "services_config": {"autoincrement": false, "name": "services_config", "type": "text", "primaryKey": false, "notNull": true}, "version": {"default": "'1.0.0'", "autoincrement": false, "name": "version", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"default": "(CURRENT_TIMESTAMP)", "autoincrement": false, "name": "created_at", "type": "numeric", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "numeric", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"rag_settings_created_at_idx": {"name": "rag_settings_created_at_idx", "columns": ["created_at"], "isUnique": false}, "rag_settings_user_id_idx": {"name": "rag_settings_user_id_idx", "columns": ["user_id"], "isUnique": false}, "rag_settings_isActive_idx": {"name": "rag_settings_isActive_idx", "columns": ["isActive"], "isUnique": false}, "rag_settings_name_idx": {"name": "rag_settings_name_idx", "columns": ["name"], "isUnique": false}, "rag_settings_name_key": {"name": "rag_settings_name_key", "columns": ["name"], "isUnique": true}}, "foreignKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}