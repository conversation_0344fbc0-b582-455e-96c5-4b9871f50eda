import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import {
  Search,
  Filter,
  Clock,
  User,
  MapPin,
  Tag,
  MessageSquare,
  Edit,
  CheckCircle,
  RefreshCw,
  MoreVertical,
  BookOpen,
  ClipboardCheck
} from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { Stoerung, StoerungStatus, STATUS_COLORS } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';
import { LinkRunbookDialog } from '../runbooks/LinkRunbookDialog';
import { PostIncidentReview } from '../bereitschaft/PostIncidentReview';

interface StoerungsListProps {
  dateRange?: DateRange;
  refreshKey?: number;
  onRefresh: () => void;
}

const STATUS_LABELS: Record<StoerungStatus, string> = {
  [StoerungStatus.NEU]: 'Neu',
  [StoerungStatus.IN_BEARBEITUNG]: 'In Bearbeitung',
  [StoerungStatus.ESKALIERT_L2]: 'Eskaliert (L2)',
  [StoerungStatus.ESKALIERT_L3]: 'Eskaliert (L3)',
  [StoerungStatus.GELÖST]: 'Gelöst',
  [StoerungStatus.ABGESCHLOSSEN]: 'Abgeschlossen',
  [StoerungStatus.REVIEW]: 'Review'
};

// Backend-kompatible Status-Werte (wie im Bearbeitungsformular)
const BACKEND_STATUS_OPTIONS = [
  { value: 'NEW', label: 'Neu', enumValue: StoerungStatus.NEU, color: 'text-blue-600' },
  { value: 'IN_PROGRESS', label: 'In Bearbeitung', enumValue: StoerungStatus.IN_BEARBEITUNG, color: 'text-orange-600' },
  { value: 'RESOLVED', label: 'Gelöst', enumValue: StoerungStatus.GELÖST, color: 'text-green-600' }
];

export const StoerungsList: React.FC<StoerungsListProps> = React.memo(({
  dateRange,
  refreshKey = 0,
  onRefresh
}) => {
  const [stoerungen, setStoerungen] = useState<Stoerung[]>([]);
  const [filteredStoerungen, setFilteredStoerungen] = useState<Stoerung[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLinkRunbookDialogOpen, setIsLinkRunbookDialogOpen] = useState(false);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [selectedStoerung, setSelectedStoerung] = useState<Stoerung | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [systemFilter, setSystemFilter] = useState<string>('all');

  useEffect(() => {
    const fetchStoerungen = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await stoerungenService.getStoerungen();
        setStoerungen(data);
      } catch (err) {
        console.error('Error fetching störungen:', err);
        setError('Fehler beim Laden der Störungen');
      } finally {
        setLoading(false);
      }
    };

    fetchStoerungen();
  }, [refreshKey]);

  // Apply filters
  useEffect(() => {
    let filtered = [...stoerungen];

    if (searchQuery) {
      filtered = filtered.filter(stoerung =>
        stoerung.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        stoerung.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(stoerung => stoerung.status === statusFilter);
    }

    if (severityFilter !== 'all') {
      filtered = filtered.filter(stoerung => stoerung.severity === severityFilter);
    }

    if (systemFilter !== 'all') {
      filtered = filtered.filter(stoerung => stoerung.affected_system === systemFilter);
    }

    if (dateRange?.from && dateRange?.to) {
      filtered = filtered.filter(stoerung => {
        const createdDate = new Date(stoerung.created_at);
        return createdDate >= dateRange.from! && createdDate <= dateRange.to!;
      });
    }

    setFilteredStoerungen(filtered);
  }, [stoerungen, searchQuery, statusFilter, severityFilter, systemFilter, dateRange]);

  const getSeverityBadge = (severity: string) => {
    const variants = {
      LOW: 'bg-green-100 text-green-800 border-green-200',
      MEDIUM: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      HIGH: 'bg-orange-100 text-orange-800 border-orange-200',
      CRITICAL: 'bg-red-100 text-red-800 border-red-200',
    };
    const labels = {
      LOW: 'Niedrig',
      MEDIUM: 'Mittel',
      HIGH: 'Hoch',
      CRITICAL: 'Kritisch',
    };
    return (
      <Badge className={`${variants[severity as keyof typeof variants]} text-xs`}>
        {labels[severity as keyof typeof labels]}
      </Badge>
    );
  };

  const getStatusBadge = (status: StoerungStatus) => {
    const colorClass = STATUS_COLORS[status]?.replace('#', '') || 'gray-500';
    return (
      <Badge style={{ 
        backgroundColor: `var(--status-${status.toLowerCase()}-bg, #${colorClass}1A)`,
        color: `var(--status-${status.toLowerCase()}-text, #${colorClass})`,
        borderColor: `var(--status-${status.toLowerCase()}-border, #${colorClass}33)`
       }} className={`text-xs`}>
        {STATUS_LABELS[status]}
      </Badge>
    );
  };

  const formatTimeAgo = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffMinutes < 1) return 'Gerade eben';
    if (diffMinutes < 60) return `vor ${diffMinutes} Min`;
    if (diffMinutes < 1440) return `vor ${Math.floor(diffMinutes / 60)} Std`;
    return `vor ${Math.floor(diffMinutes / 1440)} Tagen`;
  };

  const handleStatusUpdate = async (stoerungId: number, newBackendStatus: string) => {
    try {
      // Konvertiere Backend-Status zurück zu Frontend-Enum für die Anzeige
      const backendToFrontendMapping: { [key: string]: StoerungStatus } = {
        'NEW': StoerungStatus.NEU,
        'IN_PROGRESS': StoerungStatus.IN_BEARBEITUNG,
        'RESOLVED': StoerungStatus.GELÖST
      };
      
      const frontendStatus = backendToFrontendMapping[newBackendStatus] || StoerungStatus.NEU;
      
      await stoerungenService.updateStoerung(stoerungId, { status: frontendStatus });
      onRefresh();
    } catch (err) {
      console.error('Error updating störung status:', err);
    }
  };

  const getAvailableActions = (status: StoerungStatus) => {
    // Alle drei Haupt-Status verfügbar machen (wie im Bearbeitungsformular)
    const availableOptions = BACKEND_STATUS_OPTIONS.filter(option => option.enumValue !== status);
    return availableOptions;
  };

  const handleOpenLinkRunbookDialog = (stoerung: Stoerung) => {
    setSelectedStoerung(stoerung);
    setIsLinkRunbookDialogOpen(true);
  };

  const handleOpenReviewDialog = (stoerung: Stoerung) => {
    setSelectedStoerung(stoerung);
    setIsReviewDialogOpen(true);
  };

  const handleEdit = (stoerung: Stoerung) => {
    setSelectedStoerung(stoerung);
    setIsEditDialogOpen(true);
  };

  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">Störungsliste</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="max-h-[700px] overflow-hidden border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Störungsliste ({filteredStoerungen.length})
          </CardTitle>

          <div className="flex flex-wrap gap-2 pt-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Suchen..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-56 bg-white"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle Status</SelectItem>
                {Object.values(StoerungStatus).map(status => (
                  <SelectItem key={status} value={status}>{STATUS_LABELS[status]}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={severityFilter} onValueChange={setSeverityFilter}>
              <SelectTrigger className="w-44">
                <SelectValue placeholder="Schweregrad" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle Schweregrade</SelectItem>
                <SelectItem value="LOW">Niedrig</SelectItem>
                <SelectItem value="MEDIUM">Mittel</SelectItem>
                <SelectItem value="HIGH">Hoch</SelectItem>
                <SelectItem value="CRITICAL">Kritisch</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>

        <CardContent>
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-sm text-red-800">
              {error}
            </div>
          )}

          <div className="space-y-3 max-h-[60vh] overflow-y-auto">
            {filteredStoerungen.map((stoerung) => {
              const availableActions = getAvailableActions(stoerung.status);
              return (
                <div
                  key={stoerung.id}
                  className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 mb-1">{stoerung.title}</h4>
                      {stoerung.description && (
                        <p className="text-sm text-gray-600 mb-2">{stoerung.description}</p>
                      )}
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      {getSeverityBadge(stoerung.severity)}
                      {getStatusBadge(stoerung.status)}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs text-gray-500 mb-3">
                    {/* ... other details ... */}
                  </div>

                  <div className="flex items-center justify-between">
                    {/* ... comments and mttr ... */}
                    <div className="flex items-center gap-2">
                      <Button 
                        size="sm" 
                        variant="ghost" 
                        className="text-xs"
                        onClick={() => handleEdit(stoerung)}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Bearbeiten
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button size="sm" variant="outline" className="text-xs">
                            Aktionen
                            <MoreVertical className="h-3 w-3 ml-1" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="bg-white">
                          {availableActions.map(action => (
                            <DropdownMenuItem 
                              key={action.value} 
                              onClick={() => handleStatusUpdate(stoerung.id, action.value)}
                              className={`${action.color} hover:bg-gray-50`}
                            >
                              {action.label}
                            </DropdownMenuItem>
                          ))}
                           <DropdownMenuSeparator />
                           <DropdownMenuItem onClick={() => handleOpenLinkRunbookDialog(stoerung)}>
                                <BookOpen className="h-3 w-3 mr-2" />
                                Runbook verlinken
                           </DropdownMenuItem>
                           {(stoerung.status === StoerungStatus.ABGESCHLOSSEN || stoerung.status === StoerungStatus.REVIEW) && (
                               <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleOpenReviewDialog(stoerung)}>
                                    <ClipboardCheck className="h-3 w-3 mr-2" />
                                    Review durchführen
                                </DropdownMenuItem>
                               </>
                           )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {filteredStoerungen.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              <Filter className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>
                {stoerungen.length === 0
                  ? 'Keine Störungen vorhanden'
                  : 'Keine Störungen entsprechen den aktuellen Filtern'
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
      {isLinkRunbookDialogOpen && selectedStoerung && (
        <LinkRunbookDialog stoerung={selectedStoerung} onClose={() => setIsLinkRunbookDialogOpen(false)} />
      )}
      {isReviewDialogOpen && selectedStoerung && (
          <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
              <DialogContent className="sm:max-w-4xl">
                  <PostIncidentReview stoerung={selectedStoerung} onClose={() => setIsReviewDialogOpen(false)} />
              </DialogContent>
          </Dialog>
      )}
      {isEditDialogOpen && selectedStoerung && (
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
              <DialogContent className="sm:max-w-4xl">
                  <div className="p-4">
                      <h3 className="text-lg font-medium mb-4">Störung bearbeiten</h3>
                      <p className="text-sm text-gray-500">Bearbeitungsfunktionalität wird noch implementiert.</p>
                  </div>
              </DialogContent>
          </Dialog>
      )}
    </>
  );
});

StoerungsList.displayName = 'StoerungsList';