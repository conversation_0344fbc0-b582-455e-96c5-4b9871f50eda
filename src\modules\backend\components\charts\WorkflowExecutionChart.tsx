import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { TrendingUp } from 'lucide-react';

interface WorkflowExecutionChartProps {
  data: Array<{
    date: string;
    successful: number;
    failed: number;
    total: number;
  }>;
}

export function WorkflowExecutionChart({ data }: WorkflowExecutionChartProps) {
  const chartConfig = {
    successful: {
      label: "Erfolgreich",
      color: "var(--chart-1)",
    },
    failed: {
      label: "Fehlgeschlagen",
      color: "var(--chart-2)",
    },
  } satisfies ChartConfig;

  const totalExecutions = data.reduce((sum, item) => sum + item.total, 0);
  const totalSuccessful = data.reduce((sum, item) => sum + item.successful, 0);
  const successRate = totalExecutions > 0 ? (totalSuccessful / totalExecutions) * 100 : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Ausführungen der letzten 7 Tage</CardTitle>
        <CardDescription>Erfolgreiche vs. fehlgeschlagene Ausführungen</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart accessibilityLayer data={data}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="dashed" />}
            />
            <Bar dataKey="successful" fill="var(--color-successful)" radius={4} />
            <Bar dataKey="failed" fill="var(--color-failed)" radius={4} />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 leading-none font-medium">
          Erfolgsrate: {successRate.toFixed(1)}% <TrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Zeigt Workflow-Ausführungen der letzten 7 Tage
        </div>
      </CardFooter>
    </Card>
  );
}