/**
 * System-API-Routen
 * 
 * Diese Routen stellen System-Statistiken und -Informationen bereit.
 * V2: Erweitert um Cache-Monitoring und Performance-Metriken
 */

import express from 'express';
import { validators, sanitizationMiddleware } from '../middleware/validation.middleware';
import { rateLimitConfig } from '../middleware/rate-limiting.middleware';
import { DatabaseService } from '../services/database.service';

const router = express.Router();

// Basis-Middleware für alle System-Routen
router.use(sanitizationMiddleware);
router.use(rateLimitConfig.general);

// DatabaseService instanziieren für Cache-Statistiken
let databaseService: DatabaseService;
try {
  databaseService = new DatabaseService();
} catch (error) {
  console.error('[SYSTEM] Fehler beim Initialisieren des DatabaseService:', error);
}

/**
 * System-Statistiken Route
 * Erweiterte Validierung für System-Stats-Anfragen
 */
router.get('/stats', validators.systemStats, async (req, res) => {
  try {
    const { startDate, endDate, includeDetails } = req.query as {
      startDate?: string;
      endDate?: string;
      includeDetails?: boolean;
    };

    // Basis-Statistiken
    const stats = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version,
      nodeEnv: process.env.NODE_ENV,
      // Weitere System-Metriken können hier hinzugefügt werden
    };

    // Erweiterte Details falls angefordert
    if (includeDetails) {
      (stats as any).detailed = {
        cpuUsage: process.cpuUsage(),
        platform: process.platform,
        arch: process.arch,
        // Weitere Details...
      };
    }

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('[SYSTEM] Fehler beim Abrufen der System-Statistiken:', error);
    res.status(500).json({
      success: false,
      error: 'Fehler beim Abrufen der System-Statistiken',
      code: 'SYSTEM_STATS_ERROR'
    });
  }
});

/**
 * Cache-Statistiken Route
 * Zeigt Performance-Metriken des Backend-Caches
 */
router.get('/cache/stats', async (req, res) => {
  try {
    if (!databaseService) {
      throw new Error('DatabaseService nicht verfügbar');
    }

    const cacheStats = databaseService.getCacheStats();
    
    // Erweiterte Cache-Metriken berechnen
    const enhancedStats = {
      ...cacheStats,
      performanceMetrics: {
        missRatio: 100 - cacheStats.hitRate,
        averageMemoryPerEntry: cacheStats.entryCount > 0 
          ? cacheStats.memoryUsageMB / cacheStats.entryCount 
          : 0,
        queriesPerEntry: cacheStats.entryCount > 0
          ? cacheStats.totalQueries / cacheStats.entryCount
          : 0,
        hitRateGrade: getHitRateGrade(cacheStats.hitRate)
      },
      recommendations: generateCacheRecommendations(cacheStats)
    };

    res.json({
      success: true,
      data: enhancedStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('[SYSTEM] Fehler beim Abrufen der Cache-Statistiken:', error);
    res.status(500).json({
      success: false,
      error: 'Fehler beim Abrufen der Cache-Statistiken',
      code: 'CACHE_STATS_ERROR'
    });
  }
});

/**
 * Cache-Invalidierung Route
 * Invalidiert Cache für bestimmte Datentypen
 */
router.post('/cache/invalidate', async (req, res) => {
  try {
    if (!databaseService) {
      throw new Error('DatabaseService nicht verfügbar');
    }

    const { dataTypes } = req.body as { dataTypes?: string[] };
    const invalidatedCount = databaseService.invalidateCache(dataTypes);

    res.json({
      success: true,
      data: {
        message: `${invalidatedCount} Cache-Einträge invalidiert`,
        invalidatedCount,
        dataTypes: dataTypes || ['dispatch', 'warehouse', 'cutting']
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('[SYSTEM] Fehler beim Invalidieren des Caches:', error);
    res.status(500).json({
      success: false,
      error: 'Fehler beim Invalidieren des Caches',
      code: 'CACHE_INVALIDATE_ERROR'
    });
  }
});

/**
 * Cache komplett leeren (Development/Testing)
 */
router.post('/cache/clear', async (req, res) => {
  try {
    if (!databaseService) {
      throw new Error('DatabaseService nicht verfügbar');
    }

    if (process.env.NODE_ENV === 'production') {
      return res.status(403).json({
        success: false,
        error: 'Cache-Clearing ist in der Produktion nicht erlaubt',
        code: 'OPERATION_NOT_ALLOWED'
      });
    }

    databaseService.clearAllCache();

    res.json({
      success: true,
      data: {
        message: 'Cache komplett geleert',
        warning: 'Cache wird beim nächsten Zugriff neu initialisiert'
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('[SYSTEM] Fehler beim Leeren des Caches:', error);
    res.status(500).json({
      success: false,
      error: 'Fehler beim Leeren des Caches',
      code: 'CACHE_CLEAR_ERROR'
    });
  }
});

/**
 * Hilfsfunktionen für Cache-Metriken
 */
function getHitRateGrade(hitRate: number): string {
  if (hitRate >= 90) return 'Excellent';
  if (hitRate >= 80) return 'Good';
  if (hitRate >= 70) return 'Fair';
  if (hitRate >= 60) return 'Poor';
  return 'Critical';
}

function generateCacheRecommendations(stats: any): string[] {
  const recommendations: string[] = [];
  
  if (stats.hitRate < 70) {
    recommendations.push('Hit-Rate ist niedrig. TTL-Werte überprüfen oder Cache-Größe erhöhen.');
  }
  
  if (stats.memoryUsageMB > 80) {
    recommendations.push('Memory-Verbrauch ist hoch. Eviction-Strategie überprüfen.');
  }
  
  if (stats.evictions > stats.totalRequests * 0.1) {
    recommendations.push('Viele Evictions erkannt. maxEntries oder maxMemoryMB erhöhen.');
  }
  
  if (stats.entryCount > 1500) {
    recommendations.push('Viele Cache-Einträge. Cleanup-Interval reduzieren.');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Cache-Performance ist optimal.');
  }
  
  return recommendations;
}

export default router;