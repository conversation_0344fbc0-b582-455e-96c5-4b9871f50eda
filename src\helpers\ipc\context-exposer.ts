import { exposeWindowContext } from "./window/window-context";
import { exposeTeamsContext } from "./teams/teams-context";
import { exposeShellContext } from "./shell/shell-context";

/**
 * Stellt alle Kontexte im Renderer-Prozess zur Verfügung
 * Database Context entfernt - Frontend verwendet direkt API Service
 * Theme Context entfernt - App verwendet festes Light-Theme
 */
export default function exposeContexts() {
  const electronAPI = {
    window: exposeWindowContext(),
    teams: exposeTeamsContext(),
    shell: exposeShellContext(),
  };

  console.log('Alle Kontexte wurden im Renderer-Prozess verfügbar gemacht (Database IPC, Theme entfernt, Teams und Shell hinzugefügt).');
  return electronAPI;
}
