# Live Monitoring - Tab Dokumentation

## Übersicht
Der Live Monitoring Tab bietet eine Echtzeit-Übersicht über den aktuellen Zustand aller kritischen Systeme im Leitstand. Diese Ansicht dient der proaktiven Überwachung und ermöglicht die frühzeitige Erkennung potenzieller Probleme.

## Hauptkomponente: System Status Heatmap

### Zweck und Funktion
Die **System Status Heatmap** ist das zentrale Element des Live Monitoring Tabs. Sie visualisiert den Gesundheitszustand aller überwachten Systeme in einer übersichtlichen, farbkodierten Matrix.

### Farbkodierung
- **🟢 Grün**: System läuft stabil, alle Parameter im Normalbereich
- **🟡 Gelb**: Warnungen vorhanden, Aufmerksamkeit erforderlich
- **🔴 Rot**: Kritische Störung, sofortiges Handeln notwendig
- **⚫ Grau**: System offline oder keine Daten verfügbar

### Interaktive Funktionen
- **Hover-Effekt**: Zeigt detaillierte Informationen beim Überfahren
- **Klick-Navigation**: Führt zu spezifischen System-Details
- **Auto-Refresh**: Automatische Aktualisierung alle 30 Sekunden
- **Manual Refresh**: Sofortige Aktualisierung per Refresh-Button

## Überwachte Systemkategorien

### Produktionssysteme
- **ERP-Systeme**: SAP-Module und Datenbank-Status
- **Fertigungslinien**: Maschinenstatus und Produktionsflow
- **Qualitätssicherung**: Messsysteme und Prüfstände
- **Automatisierung**: SPS-Systeme und Robotersteuerungen

### IT-Infrastruktur
- **Server**: Prozessor, RAM, Festplatte, Netzwerk
- **Netzwerk**: Switch-Status, Bandbreite, Latenz
- **Storage**: SAN/NAS-Systeme, Backup-Status
- **Virtualisierung**: VM-Host-Status, Ressourcenverteilung

### Gebäudetechnik
- **Stromversorgung**: USV-Systeme, Netzqualität
- **Klimatechnik**: Temperatur, Luftfeuchtigkeit, Lüftung
- **Sicherheitssysteme**: Brandmelde-, Einbruch-, Videoüberwachung
- **Zutrittskontrolle**: Türsysteme, Zeiterfassung

## Datenquellen und Integration

### Monitoring-Systeme
- **Nagios/Icinga**: Infrastruktur-Monitoring
- **PRTG**: Netzwerk- und Bandbreiten-Überwachung
- **Zabbix**: Server- und Application-Monitoring
- **SAP Solution Manager**: ERP-System-Überwachung

### Datenaktualisierung
- **Real-time**: Kritische Systeme (< 1 Minute)
- **Near real-time**: Standard-Systeme (1-5 Minuten)
- **Periodisch**: Nicht-kritische Systeme (5-15 Minuten)
- **On-demand**: Manuelle Abfrage bei Bedarf

## Benutzerinteraktion

### Navigation
- **Zoom-Funktion**: Vergrößerung spezifischer Bereiche
- **Filter-Optionen**: Nach System-Typ, Standort, Priorität
- **Suchfunktion**: Schnelles Auffinden bestimmter Systeme
- **Vollbild-Modus**: Maximale Übersicht auf großen Displays

### Alerting und Benachrichtigungen
- **Visual Alerts**: Blinkende/pulsierende Anzeigen bei kritischen Zuständen
- **Audio Alerts**: Optionale Tonsignale für neue kritische Störungen
- **Desktop Notifications**: Browser-Benachrichtigungen bei wichtigen Events
- **Mobile Push**: Weiterleitung an Smartphone-Apps

## Alarm-Management

### Severity-Level
1. **Critical**: Sofortige Aktion erforderlich (< 5 Min Reaktionszeit)
2. **Major**: Dringende Bearbeitung nötig (< 30 Min Reaktionszeit)
3. **Minor**: Zeitnahe Bearbeitung (< 2 Stunden Reaktionszeit)
4. **Warning**: Überwachung verstärken (< 24 Stunden Reaktionszeit)
5. **Info**: Zur Kenntnisnahme (keine direkte Aktion erforderlich)

### Eskalationsregeln
- **Auto-Eskalation**: Nach definierten Zeiträumen ohne Bestätigung
- **Abhängigkeiten**: Unterdrückung von Folge-Alarmen
- **Wartungszeiten**: Automatische Stummschaltung während geplanter Arbeiten
- **Urlaubs-/Schichtplanung**: Dynamische Umleitung an verfügbare Mitarbeiter

## Performance-Optimierung

### Darstellungs-Optimierungen
- **Lazy Loading**: Nur sichtbare Bereiche werden geladen
- **Caching**: Zwischenspeicherung häufig abgerufener Daten
- **Komprimierung**: Optimierte Datenübertragung
- **Progressive Enhancement**: Grundfunktionen auch bei langsameren Verbindungen

### Skalierbarkeit
- **Clustering**: Verteilung auf mehrere Server bei hoher Last
- **Load Balancing**: Gleichmäßige Verteilung der Anfragen
- **Database Partitioning**: Aufteilung großer Datenmengen
- **CDN-Integration**: Schnelle Auslieferung statischer Inhalte

## Best Practices für Operatoren

### Arbeitsweise
1. **Regelmäßige Kontrolle**: Mindestens alle 15 Minuten Blick auf Heatmap
2. **Trend-Beobachtung**: Verschlechterungen frühzeitig erkennen
3. **Präventive Maßnahmen**: Bei Warnungen proaktiv handeln
4. **Dokumentation**: Auffälligkeiten und Maßnahmen protokollieren

### Reaktionszeiten
- **Kritisch**: Sofortige Reaktion und Eskalation
- **Major**: Binnen 30 Minuten erste Maßnahmen eingeleitet
- **Minor**: Spätestens in nächster Schicht bearbeiten
- **Warning**: In Tagesplanung aufnehmen

## Konfiguration und Anpassung

### Personalisierung
- **Dashboard-Layout**: Anordnung der Systemkacheln anpassen
- **Farbschema**: Persönliche Präferenzen für Farbkodierung
- **Refresh-Intervalle**: Individuelle Aktualisierungsraten
- **Filter-Presets**: Gespeicherte Filtereinstellungen für häufige Ansichten

### Administrative Einstellungen
- **Schwellwerte**: Definition von Warn- und Kritikwerten
- **Gruppierungen**: Logische Zusammenfassung verwandter Systeme
- **Berechtigungen**: Sichtbarkeit verschiedener Systembereiche
- **Integration**: Anbindung neuer Monitoring-Quellen

## Troubleshooting

### Häufige Probleme
- **Keine Daten sichtbar**: Monitoring-Services prüfen, Netzwerkverbindung kontrollieren
- **Veraltete Anzeige**: Manual Refresh durchführen, Browser-Cache leeren
- **Performance-Probleme**: Weniger Details anzeigen, Zeitraum einschränken
- **Falsche Farben**: Schwellwerte überprüfen, Kalibrierung der Sensoren

### Fehlerbehebung
- **Service-Restart**: Neustart der Monitoring-Dienste
- **Database-Cleanup**: Bereinigung alter/korrupter Daten
- **Cache-Reset**: Zurücksetzen des Application-Cache
- **Configuration-Reload**: Neuladen der Konfigurationsdateien