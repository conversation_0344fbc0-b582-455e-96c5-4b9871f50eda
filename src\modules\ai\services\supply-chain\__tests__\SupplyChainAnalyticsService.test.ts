/**
 * Supply Chain Analytics Service Tests
 * 
 * Unit tests for the supply chain analytics service
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SupplyChainAnalyticsService } from '../SupplyChainAnalyticsService';
import { SupplierRepository } from '@/repositories/supplier.repository';
import { DeliveryRepository } from '@/repositories/delivery.repository';
import { AIServiceError } from '../../types';

// Mock repositories
vi.mock('@/repositories/supplier.repository');
vi.mock('@/repositories/delivery.repository');

describe('SupplyChainAnalyticsService', () => {
  let service: SupplyChainAnalyticsService;
  let mockSupplierRepository: vi.Mocked<SupplierRepository>;
  let mockDeliveryRepository: vi.Mocked<DeliveryRepository>;

  beforeEach(async () => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Create service instance
    service = new SupplyChainAnalyticsService({
      monitoringInterval: 0, // Disable monitoring for tests
      alertThresholds: {
        riskScore: 7,
        onTimeRate: 0.85,
        qualityRate: 0.95,
        deliveryDelay: 2
      },
      trendAnalysisPeriod: 30,
      performanceWeights: {
        onTimeDelivery: 0.4,
        quality: 0.3,
        cost: 0.2,
        reliability: 0.1
      }
    });

    // Setup mock repositories - inject them into the service
    mockSupplierRepository = vi.mocked(new SupplierRepository());
    mockDeliveryRepository = vi.mocked(new DeliveryRepository());
    
    // Replace the repositories in the service
    (service as any).supplierRepository = mockSupplierRepository;
    (service as any).deliveryRepository = mockDeliveryRepository;

    // Mock repository methods
    mockSupplierRepository.getSupplierStats.mockResolvedValue({
      totalSuppliers: 10,
      activeSuppliers: 8,
      averagePerformanceScore: 7.5,
      topPerformers: 3,
      riskSuppliers: 1
    });

    mockDeliveryRepository.getDeliveryStats.mockResolvedValue({
      totalDeliveries: 100,
      onTimeDeliveries: 85,
      averageDeliveryTime: 6.5,
      totalDistance: 10000,
      totalCost: 25000,
      activeRoutes: 5
    });

    mockSupplierRepository.getAllSuppliers.mockResolvedValue([
      {
        id: 'SUP_001',
        name: 'Test Supplier 1',
        location: 'Hamburg',
        category: 'Electronics',
        contactInfo: {
          email: '<EMAIL>',
          phone: '+49 123 456789',
          address: 'Test Address 1',
          contactPerson: 'John Doe'
        },
        performanceMetrics: {
          onTimeDeliveryRate: 0.9,
          qualityRate: 0.98,
          responseTime: 8,
          financialStabilityScore: 8,
          reliabilityScore: 9,
          costCompetitiveness: 7
        }
      },
      {
        id: 'SUP_002',
        name: 'Test Supplier 2',
        location: 'Berlin',
        category: 'Components',
        contactInfo: {
          email: '<EMAIL>',
          phone: '+49 987 654321',
          address: 'Test Address 2',
          contactPerson: 'Jane Smith'
        },
        performanceMetrics: {
          onTimeDeliveryRate: 0.75,
          qualityRate: 0.92,
          responseTime: 16,
          financialStabilityScore: 6,
          reliabilityScore: 7,
          costCompetitiveness: 8
        }
      }
    ]);

    mockSupplierRepository.getRiskSuppliers.mockResolvedValue([
      {
        id: 'SUP_002',
        name: 'Test Supplier 2',
        location: 'Berlin',
        category: 'Components',
        contactInfo: {
          email: '<EMAIL>',
          phone: '+49 987 654321',
          address: 'Test Address 2',
          contactPerson: 'Jane Smith'
        },
        performanceMetrics: {
          onTimeDeliveryRate: 0.75,
          qualityRate: 0.92,
          responseTime: 16,
          financialStabilityScore: 6,
          reliabilityScore: 7,
          costCompetitiveness: 8
        }
      }
    ]);

    mockDeliveryRepository.getDeliveryPerformanceMetrics.mockResolvedValue({
      onTimeRate: 0.85,
      averageDeliveryTime: 6.5,
      totalDeliveries: 100,
      delayFrequency: 0.15,
      averageDelay: 2.5
    });

    mockDeliveryRepository.getDelayReasons.mockResolvedValue([
      {
        reason: 'Wetterbedingungen',
        frequency: 15,
        percentage: 30,
        averageDelay: 2.5,
        impact: 'medium'
      },
      {
        reason: 'Verkehrsstau',
        frequency: 10,
        percentage: 20,
        averageDelay: 1.8,
        impact: 'low'
      }
    ]);

    mockDeliveryRepository.getSeasonalPatterns.mockResolvedValue([
      {
        season: 'Winter',
        months: [11, 0, 1],
        deliveryTimeFactor: 1.3,
        riskFactor: 1.4,
        description: 'Winter: Längere Lieferzeiten, höheres Risiko'
      }
    ]);

    mockDeliveryRepository.getDeliveryTrends.mockResolvedValue([
      {
        week: new Date('2024-01-01'),
        onTimeRate: 0.85,
        averageDeliveryTime: 6.5,
        totalDeliveries: 25,
        totalDistance: 2500,
        totalCost: 6250
      },
      {
        week: new Date('2024-01-08'),
        onTimeRate: 0.88,
        averageDeliveryTime: 6.2,
        totalDeliveries: 28,
        totalDistance: 2800,
        totalCost: 7000
      }
    ]);

    // Initialize service
    await service.initialize();
  });

  afterEach(async () => {
    await service.destroy();
  });

  describe('Service Initialization', () => {
    it('should initialize successfully', async () => {
      const newService = new SupplyChainAnalyticsService();
      await expect(newService.initialize()).resolves.not.toThrow();
      await newService.destroy();
    });

    it('should handle initialization failure', async () => {
      mockSupplierRepository.getSupplierStats.mockRejectedValue(new Error('Database error'));
      
      const newService = new SupplyChainAnalyticsService();
      await expect(newService.initialize()).rejects.toThrow('Supply chain analytics initialization failed');
      await newService.destroy();
    });

    it('should configure monitoring correctly', async () => {
      const config = {
        monitoringInterval: 30,
        alertThresholds: {
          riskScore: 8,
          onTimeRate: 0.9,
          qualityRate: 0.98,
          deliveryDelay: 1
        }
      };

      const newService = new SupplyChainAnalyticsService(config);
      await newService.initialize();
      
      const health = newService.getHealthStatus();
      expect(health.details.configuration.monitoringInterval).toBe(30);
      expect(health.details.configuration.alertThresholds.riskScore).toBe(8);
      
      await newService.destroy();
    });
  });

  describe('Supply Chain Analytics', () => {
    it('should generate comprehensive analytics', async () => {
      const analytics = await service.getSupplyChainAnalytics({ days: 30 });

      expect(analytics).toBeDefined();
      expect(analytics.overallPerformance).toBeDefined();
      expect(analytics.supplierPerformance).toBeInstanceOf(Array);
      expect(analytics.deliveryPerformance).toBeDefined();
      expect(analytics.riskAnalysis).toBeDefined();
      expect(analytics.trends).toBeInstanceOf(Array);

      // Verify overall performance metrics
      expect(analytics.overallPerformance.onTimeDeliveryRate).toBeGreaterThan(0);
      expect(analytics.overallPerformance.onTimeDeliveryRate).toBeLessThanOrEqual(1);
      expect(analytics.overallPerformance.averageDeliveryTime).toBeGreaterThan(0);
      expect(analytics.overallPerformance.costEfficiency).toBeGreaterThan(0);
      expect(analytics.overallPerformance.costEfficiency).toBeLessThanOrEqual(1);
      expect(analytics.overallPerformance.supplierReliability).toBeGreaterThan(0);
      expect(analytics.overallPerformance.supplierReliability).toBeLessThanOrEqual(1);
      expect(analytics.overallPerformance.overallRiskScore).toBeGreaterThan(0);
      expect(analytics.overallPerformance.overallRiskScore).toBeLessThanOrEqual(10);
    });

    it('should handle analytics generation with no data', async () => {
      mockSupplierRepository.getAllSuppliers.mockResolvedValue([]);
      mockDeliveryRepository.getDeliveryPerformanceMetrics.mockResolvedValue({
        onTimeRate: 0,
        averageDeliveryTime: 0,
        totalDeliveries: 0,
        delayFrequency: 0,
        averageDelay: 0
      });

      const analytics = await service.getSupplyChainAnalytics({ days: 30 });

      expect(analytics).toBeDefined();
      expect(analytics.supplierPerformance).toHaveLength(0);
      expect(analytics.overallPerformance.onTimeDeliveryRate).toBe(0);
    });

    it('should handle analytics generation errors gracefully', async () => {
      mockSupplierRepository.getAllSuppliers.mockRejectedValue(new Error('Database error'));

      const analytics = await service.getSupplyChainAnalytics({ days: 30 });

      // Should return fallback analytics
      expect(analytics).toBeDefined();
      expect(analytics.overallPerformance.onTimeDeliveryRate).toBe(0.8);
      expect(analytics.supplierPerformance).toHaveLength(0);
    });
  });

  describe('Supplier Performance Analytics', () => {
    it('should analyze supplier performance correctly', async () => {
      mockSupplierRepository.getSupplierPerformance.mockImplementation(async (supplierId) => {
        if (supplierId === 'SUP_001') {
          return {
            supplierId: 'SUP_001',
            onTimeDeliveryRate: 0.9,
            qualityRate: 0.98,
            responseTime: 8,
            financialStabilityScore: 8,
            reliabilityScore: 9,
            costCompetitiveness: 7,
            totalOrders: 50,
            totalValue: 100000,
            lastOrderDate: new Date(),
            performanceTrend: 'improving'
          };
        }
        return {
          supplierId: 'SUP_002',
          onTimeDeliveryRate: 0.75,
          qualityRate: 0.92,
          responseTime: 16,
          financialStabilityScore: 6,
          reliabilityScore: 7,
          costCompetitiveness: 8,
          totalOrders: 30,
          totalValue: 60000,
          lastOrderDate: new Date(),
          performanceTrend: 'declining'
        };
      });

      mockDeliveryRepository.getSupplierDeliveryHistory.mockResolvedValue([
        {
          deliveryId: 'DEL_001',
          supplierId: 'SUP_001',
          orderDate: new Date('2024-01-01'),
          deliveryDate: new Date('2024-01-08'),
          promisedTime: 7,
          actualTime: 7,
          wasLate: false,
          productType: 'Electronics',
          urgency: 'medium',
          quantity: 100,
          value: 1000
        }
      ]);

      const supplierAnalytics = await service.getSupplierPerformanceAnalytics({ days: 30 });

      expect(supplierAnalytics).toHaveLength(2);
      
      // Should be sorted by performance score (descending)
      expect(supplierAnalytics[0].performanceScore).toBeGreaterThanOrEqual(supplierAnalytics[1].performanceScore);
      
      // Verify supplier analytics structure
      supplierAnalytics.forEach(supplier => {
        expect(supplier.supplierId).toBeDefined();
        expect(supplier.supplierName).toBeDefined();
        expect(supplier.performanceScore).toBeGreaterThan(0);
        expect(supplier.performanceScore).toBeLessThanOrEqual(10);
        expect(supplier.riskScore).toBeGreaterThan(0);
        expect(supplier.riskScore).toBeLessThanOrEqual(10);
        expect(supplier.deliveryReliability).toBeGreaterThanOrEqual(0);
        expect(supplier.deliveryReliability).toBeLessThanOrEqual(1);
        expect(supplier.qualityScore).toBeGreaterThan(0);
        expect(supplier.qualityScore).toBeLessThanOrEqual(10);
        expect(supplier.costCompetitiveness).toBeGreaterThan(0);
        expect(supplier.costCompetitiveness).toBeLessThanOrEqual(10);
        expect(supplier.trends).toBeDefined();
        expect(['improving', 'stable', 'declining']).toContain(supplier.trends.performance);
        expect(['increasing', 'stable', 'decreasing']).toContain(supplier.trends.risk);
      });
    });

    it('should calculate performance scores correctly', async () => {
      mockSupplierRepository.getSupplierPerformance.mockResolvedValue({
        supplierId: 'SUP_TEST',
        onTimeDeliveryRate: 1.0, // Perfect
        qualityRate: 1.0, // Perfect
        responseTime: 4,
        financialStabilityScore: 10, // Perfect
        reliabilityScore: 10, // Perfect
        costCompetitiveness: 10, // Perfect
        totalOrders: 100,
        totalValue: 200000,
        lastOrderDate: new Date(),
        performanceTrend: 'improving'
      });

      mockDeliveryRepository.getSupplierDeliveryHistory.mockResolvedValue([]);

      const supplierAnalytics = await service.getSupplierPerformanceAnalytics({ days: 30 });
      const perfectSupplier = supplierAnalytics.find(s => s.supplierId === 'SUP_001');

      expect(perfectSupplier).toBeDefined();
      if (perfectSupplier) {
        expect(perfectSupplier.performanceScore).toBeGreaterThan(8); // Should be high
        expect(perfectSupplier.riskScore).toBeLessThan(3); // Should be low risk
      }
    });
  });

  describe('Alert Generation', () => {
    it('should generate performance alerts', async () => {
      mockSupplierRepository.getSupplierPerformance.mockImplementation(async (supplierId) => {
        if (supplierId === 'SUP_002') {
          return {
            supplierId: 'SUP_002',
            onTimeDeliveryRate: 0.7, // Below threshold
            qualityRate: 0.9, // Below threshold
            responseTime: 24,
            financialStabilityScore: 5,
            reliabilityScore: 6,
            costCompetitiveness: 6,
            totalOrders: 20,
            totalValue: 40000,
            lastOrderDate: new Date(),
            performanceTrend: 'declining'
          };
        }
        return {
          supplierId: 'SUP_001',
          onTimeDeliveryRate: 0.9,
          qualityRate: 0.98,
          responseTime: 8,
          financialStabilityScore: 8,
          reliabilityScore: 9,
          costCompetitiveness: 7,
          totalOrders: 50,
          totalValue: 100000,
          lastOrderDate: new Date(),
          performanceTrend: 'stable'
        };
      });

      const alerts = await service.generateAlerts();

      expect(alerts).toBeInstanceOf(Array);
      expect(alerts.length).toBeGreaterThan(0);

      // Should have alerts for SUP_002
      const performanceAlerts = alerts.filter(a => a.supplierId === 'SUP_002');
      expect(performanceAlerts.length).toBeGreaterThan(0);

      // Verify alert structure
      alerts.forEach(alert => {
        expect(alert.alertId).toBeDefined();
        expect(['risk_increase', 'performance_decline', 'delivery_delay', 'quality_issue', 'cost_spike']).toContain(alert.type);
        expect(['low', 'medium', 'high', 'critical']).toContain(alert.severity);
        expect(alert.message).toBeDefined();
        expect(alert.details).toBeDefined();
        expect(alert.recommendations).toBeInstanceOf(Array);
        expect(alert.createdAt).toBeInstanceOf(Date);
        expect(alert.acknowledged).toBe(false);
      });
    });

    it('should generate delivery performance alerts', async () => {
      mockDeliveryRepository.getDeliveryPerformanceMetrics.mockResolvedValue({
        onTimeRate: 0.75,
        averageDeliveryTime: 12, // High delivery time
        totalDeliveries: 100,
        delayFrequency: 0.25,
        averageDelay: 4
      });

      const alerts = await service.generateAlerts();

      const deliveryAlerts = alerts.filter(a => a.type === 'delivery_delay');
      expect(deliveryAlerts.length).toBeGreaterThan(0);

      const deliveryAlert = deliveryAlerts[0];
      expect(deliveryAlert.message).toContain('Lieferzeit');
      expect(deliveryAlert.severity).toBeDefined();
    });

    it('should generate risk alerts', async () => {
      const alerts = await service.generateAlerts();

      const riskAlerts = alerts.filter(a => a.type === 'risk_increase');
      expect(riskAlerts.length).toBeGreaterThan(0);

      const riskAlert = riskAlerts[0];
      expect(riskAlert.supplierId).toBe('SUP_002');
      expect(riskAlert.severity).toBeDefined();
      expect(riskAlert.recommendations.length).toBeGreaterThan(0);
    });

    it('should handle alert acknowledgment', () => {
      const alertId = 'TEST_ALERT_001';
      
      // Mock an alert in the cache
      service['alertsCache'].set(alertId, {
        alertId,
        type: 'risk_increase',
        severity: 'medium',
        message: 'Test alert',
        details: 'Test details',
        recommendations: ['Test recommendation'],
        createdAt: new Date(),
        acknowledged: false
      });

      const result = service.acknowledgeAlert(alertId);
      expect(result).toBe(true);

      const alert = service['alertsCache'].get(alertId);
      expect(alert?.acknowledged).toBe(true);
    });

    it('should handle non-existent alert acknowledgment', () => {
      const result = service.acknowledgeAlert('NON_EXISTENT_ALERT');
      expect(result).toBe(false);
    });
  });

  describe('Recommendation Generation', () => {
    it('should generate recommendations based on analytics', async () => {
      const recommendations = await service.generateRecommendations();

      expect(recommendations).toBeInstanceOf(Array);
      expect(recommendations.length).toBeGreaterThan(0);

      // Verify recommendation structure
      recommendations.forEach(rec => {
        expect(rec.recommendationId).toBeDefined();
        expect(['supplier_diversification', 'inventory_optimization', 'route_optimization', 'risk_mitigation']).toContain(rec.type);
        expect(['low', 'medium', 'high']).toContain(rec.priority);
        expect(rec.title).toBeDefined();
        expect(rec.description).toBeDefined();
        expect(rec.expectedBenefit).toBeDefined();
        expect(rec.implementationSteps).toBeInstanceOf(Array);
        expect(rec.implementationSteps.length).toBeGreaterThan(0);
        expect(rec.estimatedCost).toBeGreaterThanOrEqual(0);
        expect(rec.estimatedSavings).toBeGreaterThanOrEqual(0);
        expect(rec.timeframe).toBeDefined();
        expect(rec.createdAt).toBeInstanceOf(Date);
      });
    });

    it('should generate supplier diversification recommendations for high-risk suppliers', async () => {
      // Mock high-risk supplier scenario
      mockSupplierRepository.getSupplierPerformance.mockImplementation(async (supplierId) => {
        return {
          supplierId,
          onTimeDeliveryRate: 0.6, // Poor performance
          qualityRate: 0.85, // Poor quality
          responseTime: 48,
          financialStabilityScore: 4, // Poor financial stability
          reliabilityScore: 5,
          costCompetitiveness: 6,
          totalOrders: 15,
          totalValue: 30000,
          lastOrderDate: new Date(),
          performanceTrend: 'declining'
        };
      });

      const recommendations = await service.generateRecommendations();

      const diversificationRecs = recommendations.filter(r => r.type === 'supplier_diversification');
      expect(diversificationRecs.length).toBeGreaterThan(0);

      const rec = diversificationRecs[0];
      expect(rec.priority).toBe('high');
      expect(rec.title).toContain('Diversifizierung');
      expect(rec.estimatedSavings).toBeGreaterThan(rec.estimatedCost);
    });

    it('should generate inventory optimization recommendations for poor delivery performance', async () => {
      mockDeliveryRepository.getDeliveryPerformanceMetrics.mockResolvedValue({
        onTimeRate: 0.75, // Below threshold
        averageDeliveryTime: 8,
        totalDeliveries: 100,
        delayFrequency: 0.25,
        averageDelay: 3
      });

      const recommendations = await service.generateRecommendations();

      const inventoryRecs = recommendations.filter(r => r.type === 'inventory_optimization');
      expect(inventoryRecs.length).toBeGreaterThan(0);

      const rec = inventoryRecs[0];
      expect(rec.title).toContain('Bestand');
      expect(rec.implementationSteps.length).toBeGreaterThan(0);
    });

    it('should cache recommendations', async () => {
      const recommendations1 = await service.generateRecommendations();
      const cachedRecommendations = service.getRecommendations();

      expect(cachedRecommendations).toEqual(recommendations1);
      expect(cachedRecommendations.length).toBe(recommendations1.length);
    });
  });

  describe('Trend Analysis', () => {
    it('should analyze trends correctly', async () => {
      const trends = await service.getTrendAnalysis({ days: 30 });

      expect(trends).toBeInstanceOf(Array);
      expect(trends.length).toBeGreaterThan(0);

      trends.forEach(trend => {
        expect(trend.metric).toBeDefined();
        expect(['improving', 'stable', 'declining']).toContain(trend.trend);
        expect(typeof trend.changeRate).toBe('number');
        expect(trend.timeframe).toBeDefined();
        expect(trend.dataPoints).toBeInstanceOf(Array);
        
        trend.dataPoints.forEach(point => {
          expect(point.date).toBeInstanceOf(Date);
          expect(typeof point.value).toBe('number');
          expect(point.context).toBeDefined();
        });
      });
    });

    it('should calculate trend directions correctly', async () => {
      // Mock improving trend data
      mockDeliveryRepository.getDeliveryTrends.mockResolvedValue([
        {
          week: new Date('2024-01-01'),
          onTimeRate: 0.80,
          averageDeliveryTime: 7.0,
          totalDeliveries: 20,
          totalDistance: 2000,
          totalCost: 5000
        },
        {
          week: new Date('2024-01-08'),
          onTimeRate: 0.85,
          averageDeliveryTime: 6.5,
          totalDeliveries: 25,
          totalDistance: 2500,
          totalCost: 6250
        },
        {
          week: new Date('2024-01-15'),
          onTimeRate: 0.90,
          averageDeliveryTime: 6.0,
          totalDeliveries: 30,
          totalDistance: 3000,
          totalCost: 7500
        }
      ]);

      const trends = await service.getTrendAnalysis({ days: 21 });

      const onTimeTrend = trends.find(t => t.metric === 'Pünktlichkeitsrate');
      expect(onTimeTrend).toBeDefined();
      if (onTimeTrend) {
        expect(onTimeTrend.trend).toBe('improving');
        expect(onTimeTrend.changeRate).toBeGreaterThan(0);
      }

      const deliveryTimeTrend = trends.find(t => t.metric === 'Durchschnittliche Lieferzeit');
      expect(deliveryTimeTrend).toBeDefined();
      if (deliveryTimeTrend) {
        expect(deliveryTimeTrend.trend).toBe('improving'); // Lower delivery time is better
      }
    });
  });

  describe('Service Health and Management', () => {
    it('should provide health status', () => {
      const health = service.getHealthStatus();

      expect(health).toBeDefined();
      expect(health.status).toBeDefined();
      expect(health.details).toBeDefined();
      expect(health.details.configuration).toBeDefined();
      expect(health.details.configuration.monitoringInterval).toBe(0); // Disabled for tests
      expect(health.details.configuration.alertThresholds).toBeDefined();
    });

    it('should handle service destruction', async () => {
      await expect(service.destroy()).resolves.not.toThrow();
    });

    it('should clear caches on destroy', async () => {
      // Add some data to caches
      await service.generateAlerts();
      await service.generateRecommendations();

      expect(service.getAlerts().length).toBeGreaterThan(0);
      expect(service.getRecommendations().length).toBeGreaterThan(0);

      await service.destroy();

      // Caches should be cleared
      expect(service.getAlerts().length).toBe(0);
      expect(service.getRecommendations().length).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle repository errors gracefully', async () => {
      mockSupplierRepository.getAllSuppliers.mockRejectedValue(new Error('Database connection failed'));

      // Should not throw, but return fallback data
      const analytics = await service.getSupplyChainAnalytics({ days: 30 });
      expect(analytics).toBeDefined();
      expect(analytics.overallPerformance.onTimeDeliveryRate).toBe(0.8); // Fallback value
    });

    it('should handle alert generation errors', async () => {
      mockSupplierRepository.getAllSuppliers.mockRejectedValue(new Error('Database error'));

      const alerts = await service.generateAlerts();
      expect(alerts).toBeInstanceOf(Array);
      // Should return empty array or handle gracefully
    });

    it('should handle recommendation generation errors', async () => {
      mockSupplierRepository.getAllSuppliers.mockRejectedValue(new Error('Database error'));

      const recommendations = await service.generateRecommendations();
      expect(recommendations).toBeInstanceOf(Array);
      // Should return empty array or handle gracefully
    });
  });

  describe('Configuration', () => {
    it('should use default configuration values', () => {
      const defaultService = new SupplyChainAnalyticsService();
      const health = defaultService.getHealthStatus();
      
      expect(health.details.configuration.monitoringInterval).toBe(15);
      expect(health.details.configuration.alertThresholds.riskScore).toBe(7);
      expect(health.details.configuration.alertThresholds.onTimeRate).toBe(0.85);
      expect(health.details.configuration.trendAnalysisPeriod).toBe(90);
    });

    it('should override default configuration', () => {
      const customService = new SupplyChainAnalyticsService({
        monitoringInterval: 30,
        alertThresholds: {
          riskScore: 8,
          onTimeRate: 0.9,
          qualityRate: 0.98,
          deliveryDelay: 1
        },
        trendAnalysisPeriod: 60
      });
      
      const health = customService.getHealthStatus();
      
      expect(health.details.configuration.monitoringInterval).toBe(30);
      expect(health.details.configuration.alertThresholds.riskScore).toBe(8);
      expect(health.details.configuration.alertThresholds.onTimeRate).toBe(0.9);
      expect(health.details.configuration.trendAnalysisPeriod).toBe(60);
    });
  });
});