/**
 * Report Builder Component
 * 
 * Interactive form for creating and editing report templates
 */

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog-shadcn';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Plus,
  Trash2,
  Save,
  X,
  AlertTriangle,
  FileText,
  BarChart3,
  Calendar,
  Users,
  Settings
} from 'lucide-react';
import { ReportSectionBuilder } from './ReportSectionBuilder';
import { ReportScheduleBuilder } from './ReportScheduleBuilder';
import type {
  ReportTemplate,
  ReportBuilderFormData,
  ReportSectionFormData,
  ReportScheduleFormData
} from '@/types/reporting';
import type { ReportingService } from '../../services/reporting/ReportingService';

interface ReportBuilderProps {
  template?: ReportTemplate;
  onSave: (template: Omit<ReportTemplate, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  onCancel: () => void;
  reportingService: ReportingService | null;
}

/**
 * Report Builder Component
 */
export const ReportBuilder: React.FC<ReportBuilderProps> = ({
  template,
  onSave,
  onCancel,
  reportingService
}) => {
  // Form state
  const [formData, setFormData] = useState<ReportBuilderFormData>(() => ({
    name: template?.name || '',
    description: template?.description || '',
    type: template?.type || 'kpi',
    department: template?.department || 'all',
    format: template?.format || 'pdf',
    sections: template?.sections.map(section => ({
      title: section.title,
      type: section.type,
      dataSource: section.dataSource,
      chartType: section.chartType,
      aggregation: section.aggregation,
      timeRange: section.timeRange,
      filters: section.filters
    })) || [],
    schedule: template?.schedule ? {
      frequency: template.schedule.frequency,
      time: template.schedule.time,
      dayOfWeek: template.schedule.dayOfWeek,
      dayOfMonth: template.schedule.dayOfMonth,
      timezone: template.schedule.timezone,
      isActive: template.schedule.isActive
    } : undefined,
    recipients: template?.recipients || [],
    isActive: template?.isActive ?? true
  }));

  const [activeTab, setActiveTab] = useState('basic');
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newRecipient, setNewRecipient] = useState('');

  /**
   * Handle form field changes
   */
  const handleFieldChange = useCallback((field: keyof ReportBuilderFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  /**
   * Handle section changes
   */
  const handleSectionChange = useCallback((index: number, section: ReportSectionFormData) => {
    setFormData(prev => ({
      ...prev,
      sections: prev.sections.map((s, i) => i === index ? section : s)
    }));
  }, []);

  /**
   * Add new section
   */
  const handleAddSection = useCallback(() => {
    const newSection: ReportSectionFormData = {
      title: `Abschnitt ${formData.sections.length + 1}`,
      type: 'chart',
      dataSource: 'delivery',
      chartType: 'line',
      aggregation: 'sum',
      timeRange: 'month'
    };

    setFormData(prev => ({
      ...prev,
      sections: [...prev.sections, newSection]
    }));
  }, [formData.sections.length]);

  /**
   * Remove section
   */
  const handleRemoveSection = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      sections: prev.sections.filter((_, i) => i !== index)
    }));
  }, []);

  /**
   * Handle schedule changes
   */
  const handleScheduleChange = useCallback((schedule: ReportScheduleFormData | undefined) => {
    setFormData(prev => ({
      ...prev,
      schedule
    }));
  }, []);

  /**
   * Add recipient
   */
  const handleAddRecipient = useCallback(() => {
    if (newRecipient.trim() && !formData.recipients.includes(newRecipient.trim())) {
      setFormData(prev => ({
        ...prev,
        recipients: [...prev.recipients, newRecipient.trim()]
      }));
      setNewRecipient('');
    }
  }, [newRecipient, formData.recipients]);

  /**
   * Remove recipient
   */
  const handleRemoveRecipient = useCallback((email: string) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.filter(r => r !== email)
    }));
  }, []);

  /**
   * Validate form data
   */
  const validateForm = useCallback((): string | null => {
    if (!formData.name.trim()) {
      return 'Name ist erforderlich';
    }

    if (!formData.description.trim()) {
      return 'Beschreibung ist erforderlich';
    }

    if (formData.sections.length === 0) {
      return 'Mindestens ein Abschnitt ist erforderlich';
    }

    for (let i = 0; i < formData.sections.length; i++) {
      const section = formData.sections[i];
      if (!section.title.trim()) {
        return `Titel für Abschnitt ${i + 1} ist erforderlich`;
      }
      if (!section.dataSource) {
        return `Datenquelle für Abschnitt ${i + 1} ist erforderlich`;
      }
    }

    if (formData.schedule?.isActive) {
      if (!formData.schedule.time) {
        return 'Zeit für geplante Berichte ist erforderlich';
      }
      if (formData.schedule.frequency === 'weekly' && formData.schedule.dayOfWeek === undefined) {
        return 'Wochentag für wöchentliche Berichte ist erforderlich';
      }
      if (formData.schedule.frequency === 'monthly' && !formData.schedule.dayOfMonth) {
        return 'Tag des Monats für monatliche Berichte ist erforderlich';
      }
    }

    return null;
  }, [formData]);

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setSaving(true);
      setError(null);

      const templateData: Omit<ReportTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        type: formData.type,
        department: formData.department,
        format: formData.format,
        sections: formData.sections.map((section, index) => ({
          id: `section-${index}`,
          title: section.title.trim(),
          type: section.type,
          dataSource: section.dataSource,
          query: undefined,
          chartType: section.chartType,
          aggregation: section.aggregation,
          timeRange: section.timeRange,
          filters: section.filters,
          order: index
        })),
        schedule: formData.schedule ? {
          frequency: formData.schedule.frequency,
          time: formData.schedule.time,
          dayOfWeek: formData.schedule.dayOfWeek,
          dayOfMonth: formData.schedule.dayOfMonth,
          timezone: formData.schedule.timezone,
          isActive: formData.schedule.isActive
        } : undefined,
        recipients: formData.recipients,
        isActive: formData.isActive
      };

      await onSave(templateData);
    } catch (err) {
      console.error('Failed to save template:', err);
      setError('Fehler beim Speichern der Vorlage');
    } finally {
      setSaving(false);
    }
  }, [formData, validateForm, onSave]);

  return (
    <Dialog open={true} onOpenChange={() => onCancel()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            {template ? 'Vorlage bearbeiten' : 'Neue Vorlage erstellen'}
          </DialogTitle>
          <DialogDescription>
            Erstellen Sie eine Berichtsvorlage mit benutzerdefinierten Abschnitten und Planungsoptionen.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Grundlagen</TabsTrigger>
            <TabsTrigger value="sections">Abschnitte</TabsTrigger>
            <TabsTrigger value="schedule">Planung</TabsTrigger>
            <TabsTrigger value="distribution">Verteilung</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleFieldChange('name', e.target.value)}
                  placeholder="z.B. Monatlicher KPI-Bericht"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Typ</Label>
                <Select value={formData.type} onValueChange={(value) => handleFieldChange('type', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="kpi">KPI-Bericht</SelectItem>
                    <SelectItem value="performance">Leistungsbericht</SelectItem>
                    <SelectItem value="analysis">Analysebericht</SelectItem>
                    <SelectItem value="custom">Benutzerdefiniert</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="department">Abteilung</Label>
                <Select value={formData.department} onValueChange={(value) => handleFieldChange('department', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Alle Abteilungen</SelectItem>
                    <SelectItem value="dispatch">Versand</SelectItem>
                    <SelectItem value="cutting">Ablängerei</SelectItem>
                    <SelectItem value="incoming-goods">Wareneingang</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="format">Format</Label>
                <Select value={formData.format} onValueChange={(value) => handleFieldChange('format', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pdf">PDF</SelectItem>
                    <SelectItem value="excel">Excel</SelectItem>
                    <SelectItem value="html">HTML</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Beschreibung *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleFieldChange('description', e.target.value)}
                placeholder="Beschreiben Sie den Zweck und Inhalt dieses Berichts..."
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => handleFieldChange('isActive', checked)}
              />
              <Label htmlFor="isActive">Vorlage aktivieren</Label>
            </div>
          </TabsContent>

          <TabsContent value="sections" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Berichtsabschnitte</h3>
              <Button onClick={handleAddSection} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Abschnitt hinzufügen
              </Button>
            </div>

            {formData.sections.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Keine Abschnitte vorhanden
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Fügen Sie Abschnitte hinzu, um Ihren Bericht zu strukturieren.
                  </p>
                  <Button onClick={handleAddSection}>
                    <Plus className="h-4 w-4 mr-2" />
                    Ersten Abschnitt hinzufügen
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {formData.sections.map((section, index) => (
                  <Card key={index}>
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-base">
                          Abschnitt {index + 1}: {section.title || 'Unbenannt'}
                        </CardTitle>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRemoveSection(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <ReportSectionBuilder
                        section={section}
                        onChange={(updatedSection) => handleSectionChange(index, updatedSection)}
                      />
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            <ReportScheduleBuilder
              schedule={formData.schedule}
              onChange={handleScheduleChange}
            />
          </TabsContent>

          <TabsContent value="distribution" className="space-y-4">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">E-Mail-Empfänger</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Fügen Sie E-Mail-Adressen hinzu, die automatisch generierte Berichte erhalten sollen.
                </p>

                <div className="flex space-x-2 mb-4">
                  <Input
                    value={newRecipient}
                    onChange={(e) => setNewRecipient(e.target.value)}
                    placeholder="E-Mail-Adresse eingeben..."
                    onKeyPress={(e) => e.key === 'Enter' && handleAddRecipient()}
                  />
                  <Button onClick={handleAddRecipient} disabled={!newRecipient.trim()}>
                    <Plus className="h-4 w-4 mr-2" />
                    Hinzufügen
                  </Button>
                </div>

                {formData.recipients.length === 0 ? (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Keine Empfänger konfiguriert
                      </h3>
                      <p className="text-gray-600">
                        Berichte werden nur manuell generiert und nicht automatisch versendet.
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="space-y-2">
                    {formData.recipients.map((email) => (
                      <div key={email} className="flex items-center justify-between p-2 border rounded">
                        <span className="text-sm">{email}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRemoveRecipient(email)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onCancel} disabled={saving}>
            Abbrechen
          </Button>
          <Button onClick={handleSubmit} disabled={saving}>
            {saving ? (
              <>
                <Settings className="h-4 w-4 mr-2 animate-spin" />
                Speichern...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Speichern
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ReportBuilder;