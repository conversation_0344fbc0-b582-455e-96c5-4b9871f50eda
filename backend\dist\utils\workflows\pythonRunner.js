"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runPythonWorkflow = runPythonWorkflow;
exports.tailFile = tailFile;
const child_process_1 = require("child_process");
const fs_1 = __importDefault(require("fs"));
function runPythonWorkflow(scriptPath, env, events) {
    const pythonCmd = process.env.PYTHON_PATH || 'python';
    const proc = (0, child_process_1.spawn)(pythonCmd, [scriptPath], {
        cwd: process.cwd(),
        env: { ...process.env, ...(env !== null && env !== void 0 ? env : {}) },
        shell: process.platform === 'win32',
        windowsHide: true,
    });
    proc.stdout.on('data', (d) => {
        var _a;
        const line = d.toString();
        (_a = events === null || events === void 0 ? void 0 : events.onStdout) === null || _a === void 0 ? void 0 : _a.call(events, line);
    });
    proc.stderr.on('data', (d) => {
        var _a;
        const line = d.toString();
        (_a = events === null || events === void 0 ? void 0 : events.onStderr) === null || _a === void 0 ? void 0 : _a.call(events, line);
    });
    proc.on('close', (code) => {
        var _a;
        (_a = events === null || events === void 0 ? void 0 : events.onClose) === null || _a === void 0 ? void 0 : _a.call(events, code);
    });
    return proc;
}
// Einfacher Tail-Reader für Logdateien
function tailFile(filePath, onLine) {
    let position = 0;
    const interval = setInterval(() => {
        try {
            const stat = fs_1.default.statSync(filePath);
            const size = stat.size;
            if (size > position) {
                const fd = fs_1.default.openSync(filePath, 'r');
                const buf = Buffer.alloc(size - position);
                fs_1.default.readSync(fd, buf, 0, size - position, position);
                fs_1.default.closeSync(fd);
                const chunk = buf.toString('utf-8');
                position = size;
                chunk.split(/\r?\n/).filter(Boolean).forEach(line => onLine(line));
            }
        }
        catch (_a) {
            // ignore
        }
    }, 1000);
    return () => clearInterval(interval);
}
