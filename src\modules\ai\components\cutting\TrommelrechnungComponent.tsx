/**
 * Trommelrechnung-Komponente
 * 
 * Benutzeroberfläche für die Berechnung der maximalen Kabellänge auf Trommeln
 * Integriert Materialdaten und Trommeldaten aus JSON-Dateien
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calculator, Cable, Disc, AlertTriangle, Search, CheckCircle, X } from 'lucide-react';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { toast } from 'sonner';
import RadioComp from '@/components/RadioComp';
import TrommelRadioComp from '@/components/TrommelRadioComp';
import TrommelrechnungService, {
  TrommelrechnungInput,
  TrommelrechnungResult,
  KabelMaterialData,
  TrommelData
} from '../../services/cutting/TrommelrechnungService';

interface TrommelrechnungComponentProps {
  className?: string;
}

export function TrommelrechnungComponent({ className }: TrommelrechnungComponentProps) {
  // State für Eingabemodus (manuell oder aus Liste)
  const [kabelInputMode, setKabelInputMode] = useState<'manual' | 'list'>('list');
  const [trommelInputMode, setTrommelInputMode] = useState<'manual' | 'list'>('list');
  
  // State für verfügbare Daten
  const [verfügbareKabelmaterialien, setVerfügbareKabelmaterialien] = useState<any[]>([]);
  const [verfügbareTrammeln, setVerfügbareTrammeln] = useState<TrommelData[]>([]);
  
  // State für ausgewählte Daten
  const [selectedKabelMaterial, setSelectedKabelMaterial] = useState<string>('');
  const [selectedTrommel, setSelectedTrommel] = useState<string>('');
  const [kabelMaterialSuchOpen, setKabelMaterialSuchOpen] = useState(false);
  const [kabelMaterialSuchWert, setKabelMaterialSuchWert] = useState('');
  
  // State für geladene Materialdaten (um async Aufrufe zu vermeiden)
  const [selectedKabelMaterialData, setSelectedKabelMaterialData] = useState<KabelMaterialData | null>(null);
  const [selectedTrommelData, setSelectedTrommelData] = useState<TrommelData | null>(null);
  
  // State für manuelle Eingaben
  const [manuelleKabelDaten, setManuelleKabelDaten] = useState({
    kabeldurchmesser_mm: 10,
    prozentualer_zuschlag: 0.05,
    bruttogewicht_pro_meter_kg: 0.1
  });
  
  const [manuelleTrommelDaten, setManuelleTrommelDaten] = useState({
    außendurchmesser_mm: 800,
    kerndurchmesser_mm: 200,
    freiraum_mm: 20,
    führungsbogenhöhe_mm: 0,
    innenbreite_mm: 300,
    max_tragkraft_kg: 500
  });
  
  // State für Berechnung
  const [isCalculating, setIsCalculating] = useState(false);
  const [berechnungsResult, setBerechnungsResult] = useState<TrommelrechnungResult | null>(null);
  
  // Lade verfügbare Daten beim Komponenten-Mount
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🔄 Lade Trommeldaten aus der Datenbank...');
        
        // Lade nur Trommeldaten beim Start - Materialdaten werden dynamisch geladen
        const trommeln = await TrommelrechnungService.getVerfügbareTrommeln();
        
        console.log(`✅ ${trommeln.length} Trommeln geladen`);
        
        // Lade nur die ersten 100 Materialien für die initiale Anzeige
        const { materials: initialMaterials } = await TrommelrechnungService.searchKabelmaterialien('', 100);
        console.log(`✅ ${initialMaterials.length} initiale Kabelmaterialien geladen`);
        
        setVerfügbareKabelmaterialien(initialMaterials);
        setVerfügbareTrammeln(trommeln);
        
        // Setze Standardwerte
        if (initialMaterials.length > 0) {
          setSelectedKabelMaterial(initialMaterials[0].MATNR);
        }
        if (trommeln.length > 0) {
          setSelectedTrommel(trommeln[0].Trommelname);
        }
        
        toast.success('Material- und Trommeldaten erfolgreich aus der Datenbank geladen');
      } catch (error) {
        console.error('❌ Fehler beim Laden der Daten aus der Datenbank:', error);
        toast.error('Fehler beim Laden der Daten aus der Datenbank. Bitte versuchen Sie es erneut.');
      }
    };
    
    loadData();
  }, []);
  
  // Lade Kabelmaterialdaten wenn sich die Auswahl ändert
  useEffect(() => {
    const loadKabelMaterialData = async () => {
      if (selectedKabelMaterial && kabelInputMode === 'list') {
        try {
          const material = await TrommelrechnungService.findKabelmaterial(selectedKabelMaterial);
          setSelectedKabelMaterialData(material);
        } catch (error) {
          console.error('Fehler beim Laden der Kabelmaterialdaten:', error);
          setSelectedKabelMaterialData(null);
        }
      } else {
        setSelectedKabelMaterialData(null);
      }
    };
    
    loadKabelMaterialData();
  }, [selectedKabelMaterial, kabelInputMode]);
  
  // Lade Trommeldaten wenn sich die Auswahl ändert
  useEffect(() => {
    const loadTrommelData = async () => {
      if (selectedTrommel && trommelInputMode === 'list') {
        try {
          const trommel = await TrommelrechnungService.findTrommel(selectedTrommel);
          setSelectedTrommelData(trommel);
        } catch (error) {
          console.error('Fehler beim Laden der Trommeldaten:', error);
          setSelectedTrommelData(null);
        }
      } else {
        setSelectedTrommelData(null);
      }
    };
    
    loadTrommelData();
  }, [selectedTrommel, trommelInputMode]);
  
  // Hole aktuelle Kabeldaten basierend auf Eingabemodus
  const getCurrentKabelData = useCallback(async () => {
    if (kabelInputMode === 'manual') {
      return manuelleKabelDaten;
    } else {
      const material = await TrommelrechnungService.findKabelmaterial(selectedKabelMaterial);
      if (material) {
        return {
          kabeldurchmesser_mm: material.Kabeldurchmesser,
          prozentualer_zuschlag: material.Zuschlag_Kabedurchmesser,
          bruttogewicht_pro_meter_kg: material.Bruttogewicht_pro_m
        };
      }
    }
    return manuelleKabelDaten;
  }, [kabelInputMode, selectedKabelMaterial, manuelleKabelDaten]);
  
  // Hole aktuelle Trommeldaten basierend auf Eingabemodus
  const getCurrentTrommelData = useCallback(async () => {
    if (trommelInputMode === 'manual') {
      return manuelleTrommelDaten;
    } else {
      const trommel = await TrommelrechnungService.findTrommel(selectedTrommel);
      if (trommel) {
        return {
          außendurchmesser_mm: trommel.Außendurchmesser,
          kerndurchmesser_mm: trommel.Kerndurchmesser,
          freiraum_mm: trommel.Freiraum_mm,
          führungsbogenhöhe_mm: 0, // Standardwert, kann angepasst werden
          innenbreite_mm: trommel.Wickelbreite_mm,
          max_tragkraft_kg: trommel.Max_Tragkraft_Kg
        };
      }
    }
    return manuelleTrommelDaten;
  }, [trommelInputMode, selectedTrommel, manuelleTrommelDaten]);
  
  // Berechne maximale Kabellänge
  const handleBerechnung = useCallback(async () => {
    setIsCalculating(true);
    
    try {
      const kabelData = await getCurrentKabelData();
      const trommelData = await getCurrentTrommelData();
      
      const input: TrommelrechnungInput = {
        kabeldurchmesser_mm: kabelData.kabeldurchmesser_mm,
        prozentualer_zuschlag: kabelData.prozentualer_zuschlag,
        bruttogewicht_pro_meter_kg: kabelData.bruttogewicht_pro_meter_kg,
        außendurchmesser_mm: trommelData.außendurchmesser_mm,
        kerndurchmesser_mm: trommelData.kerndurchmesser_mm,
        freiraum_mm: trommelData.freiraum_mm,
        führungsbogenhöhe_mm: trommelData.führungsbogenhöhe_mm,
        innenbreite_mm: trommelData.innenbreite_mm,
        max_tragkraft_kg: trommelData.max_tragkraft_kg
      };
      
      const result = TrommelrechnungService.berechneMaximaleKabellänge(input);
      setBerechnungsResult(result);
      
      if (result.ist_geeignet) {
        toast.success(`Berechnung erfolgreich! Maximale Länge: ${result.maximale_kabellänge_m}m`);
      } else {
        toast.warning('Trommel ist für diese Konfiguration nicht optimal geeignet');
      }
      
    } catch (error) {
      console.error('Fehler bei der Berechnung:', error);
      toast.error('Fehler bei der Berechnung der Kabellänge');
    } finally {
      setIsCalculating(false);
    }
  }, [getCurrentKabelData, getCurrentTrommelData]);
  
  return (
    <>
    <Card className={`w-full ${className} border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200`}>
      {/* Header */}
      <CardHeader>
        <div className="flex items-center gap-2">
          <img 
            src="/src/assets/calculator.png" 
            alt="Calculator" 
            className="h-13 w-13 object-contain"
          />
          <div className="flex flex-col">
            <CardTitle className="text-2xl">
              Trommelrechnung
            </CardTitle>
            <CardDescription className="text-gray-600">
              Maximale Kabellänge auf Trommel berechnen
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:divide-x lg:divide-[#ff7a05]/40">
          {/* Left Side - Kabeldaten */}
          <div className="space-y-4 lg:pr-8">
            <div className="flex items-center gap-2">
              <Cable className="h-5 w-5" />
              <h3 className="text-lg font-semibold">Kabeldaten</h3>
            </div>
            <p className="text-sm text-gray-600">
              Wähle entweder Kabeldaten aus den Stammdaten oder gib die Kabeldaten individuell ein.
            </p>
            
            {/* Eingabemodus Auswahl mit RadioComp */}
            <div className="space-y-4">
              <RadioComp 
                value={kabelInputMode === 'list' ? 'with-expansion' : 'without-expansion'}
                onChange={(value: string) => setKabelInputMode(value === 'with-expansion' ? 'list' : 'manual')}
                verfügbareMaterialien={verfügbareKabelmaterialien}
                selectedMaterial={selectedKabelMaterial}
                onMaterialChange={setSelectedKabelMaterial}
                materialSuchOpen={kabelMaterialSuchOpen}
                onMaterialSuchOpenChange={setKabelMaterialSuchOpen}
                materialSuchWert={kabelMaterialSuchWert}
                onMaterialSuchWertChange={setKabelMaterialSuchWert}
                onMaterialSelect={(material) => {
                  setManuelleKabelDaten({
                    kabeldurchmesser_mm: material.Kabeldurchmesser,
                    prozentualer_zuschlag: material.Zuschlag_Kabedurchmesser,
                    bruttogewicht_pro_meter_kg: material.Bruttogewicht_pro_m
                  });
                }}
              />
            </div>
                 
            {/* Eingabefelder - immer sichtbar, aber bei 'list' gesperrt und automatisch befüllt */}
            {/* Grid Layout mit 3 Spalten für Kabeldaten */}
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label>Kabeldurchmesser (mm)</Label>
                <Input
                  type="number"
                  step="0.1"
                  value={kabelInputMode === 'list' && selectedKabelMaterialData ? 
                    selectedKabelMaterialData.Kabeldurchmesser : manuelleKabelDaten.kabeldurchmesser_mm}
                  onChange={(e) => {
                    if (kabelInputMode === 'manual') {
                      setManuelleKabelDaten(prev => ({
                        ...prev,
                        kabeldurchmesser_mm: parseFloat(e.target.value) || 0
                      }));
                    }
                  }}
                  disabled={kabelInputMode === 'list'}
                  className={kabelInputMode === 'list' ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
                />
              </div>
              <div>
                <Label>Prozentualer Zuschlag (0.05 = 5%)</Label>
                <Input
                  type="number"
                  step="0.01"
                  value={kabelInputMode === 'list' && selectedKabelMaterialData ? 
                    selectedKabelMaterialData.Zuschlag_Kabedurchmesser : manuelleKabelDaten.prozentualer_zuschlag}
                  onChange={(e) => {
                    if (kabelInputMode === 'manual') {
                      setManuelleKabelDaten(prev => ({
                        ...prev,
                        prozentualer_zuschlag: parseFloat(e.target.value) || 0
                      }));
                    }
                  }}
                  disabled={kabelInputMode === 'list'}
                  className={kabelInputMode === 'list' ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
                />
              </div>
              <div>
                <Label>Bruttogewicht pro Meter (kg/m)</Label>
                <Input
                  type="number"
                  step="0.001"
                  value={kabelInputMode === 'list' && selectedKabelMaterialData ? 
                    selectedKabelMaterialData.Bruttogewicht_pro_m : manuelleKabelDaten.bruttogewicht_pro_meter_kg}
                  onChange={(e) => {
                    if (kabelInputMode === 'manual') {
                      setManuelleKabelDaten(prev => ({
                        ...prev,
                        bruttogewicht_pro_meter_kg: parseFloat(e.target.value) || 0
                      }));
                    }
                  }}
                  disabled={kabelInputMode === 'list'}
                  className={kabelInputMode === 'list' ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
                />
              </div>
            </div>
          </div>
              
          {/* Right Side - Trommeldaten */}
          <div className="space-y-4 lg:pl-8">
            <div className="flex items-center gap-2">
              <Disc className="h-5 w-5" />
              <h3 className="text-lg font-semibold">Trommeldaten</h3>
            </div>
            <p className="text-sm text-gray-600"> 
              Wähle entweder Trommeldaten aus den Stammdaten oder gib die Trommeldaten individuell ein.
            </p>
            <div className="space-y-4">
              {/* Eingabemodus Auswahl mit TrommelRadioComp */}
              <TrommelRadioComp 
                value={trommelInputMode === 'list' ? 'with-expansion' : 'without-expansion'}
                onChange={(value: string) => setTrommelInputMode(value === 'with-expansion' ? 'list' : 'manual')}
                trommeln={verfügbareTrammeln}
                selectedTrommel={selectedTrommel}
                onTrommelChange={setSelectedTrommel}
              />
                  
              {/* Eingabefelder - immer sichtbar, aber bei 'list' gesperrt und automatisch befüllt */}
              {/* Grid Layout mit 3x2 für Trommeldaten */}
              <div className="grid grid-cols-3 grid-rows-2 gap-4">
                <div>
                  <Label>Außendurchmesser (mm)</Label>
                  <Input
                    type="number"
                    value={trommelInputMode === 'list' && selectedTrommelData ? 
                    selectedTrommelData.Außendurchmesser : manuelleTrommelDaten.außendurchmesser_mm}
                    onChange={(e) => {
                      if (trommelInputMode === 'manual') {
                        setManuelleTrommelDaten(prev => ({
                          ...prev,
                          außendurchmesser_mm: parseInt(e.target.value) || 0
                        }));
                      }
                    }}
                    disabled={trommelInputMode === 'list'}
                    className={trommelInputMode === 'list' ? 'bg-gray-100 cursor-not-allowed' : trommelInputMode === 'manual' ? 'bg-white' : ''}
                  />
                </div>
                <div>
                  <Label>Kerndurchmesser (mm)</Label>
                  <Input
                    type="number"
                    value={trommelInputMode === 'list' && selectedTrommelData ? 
                    selectedTrommelData.Kerndurchmesser : manuelleTrommelDaten.kerndurchmesser_mm}
                    onChange={(e) => {
                      if (trommelInputMode === 'manual') {
                        setManuelleTrommelDaten(prev => ({
                          ...prev,
                          kerndurchmesser_mm: parseInt(e.target.value) || 0
                        }));
                      }
                    }}
                    disabled={trommelInputMode === 'list'}
                    className={trommelInputMode === 'list' ? 'bg-gray-100 cursor-not-allowed' : trommelInputMode === 'manual' ? 'bg-white' : ''}
                  />
                </div>
                <div>
                  <Label>Freiraum (mm)</Label>
                  <Input
                    type="number"
                    value={trommelInputMode === 'list' && selectedTrommelData ? 
                    selectedTrommelData.Freiraum_mm : manuelleTrommelDaten.freiraum_mm}
                    onChange={(e) => {
                      if (trommelInputMode === 'manual') {
                        setManuelleTrommelDaten(prev => ({
                          ...prev,
                          freiraum_mm: parseInt(e.target.value) || 0
                        }));
                      }
                    }}
                    disabled={trommelInputMode === 'list'}
                    className={trommelInputMode === 'list' ? 'bg-gray-100 cursor-not-allowed' : trommelInputMode === 'manual' ? 'bg-white' : ''}
                  />
                </div>
                <div>
                  <Label>Führungsbogenhöhe (mm)</Label>
                  <Input
                    type="number"
                    value={trommelInputMode === 'list' && selectedTrommelData ? 
                    0 : manuelleTrommelDaten.führungsbogenhöhe_mm}
                    onChange={(e) => {
                      if (trommelInputMode === 'manual') {
                        setManuelleTrommelDaten(prev => ({
                          ...prev,
                          führungsbogenhöhe_mm: parseInt(e.target.value) || 0
                        }));
                      }
                    }}
                    disabled={trommelInputMode === 'list'}
                    className={trommelInputMode === 'list' ? 'bg-gray-100 cursor-not-allowed' : trommelInputMode === 'manual' ? 'bg-white' : ''}
                  />
                </div>
                <div>
                  <Label>Innenbreite (mm)</Label>
                  <Input
                    type="number"
                    value={trommelInputMode === 'list' && selectedTrommelData ? 
                    selectedTrommelData.Wickelbreite_mm : manuelleTrommelDaten.innenbreite_mm}
                    onChange={(e) => {
                      if (trommelInputMode === 'manual') {
                        setManuelleTrommelDaten(prev => ({
                          ...prev,
                          innenbreite_mm: parseInt(e.target.value) || 0
                        }));
                      }
                    }}
                    disabled={trommelInputMode === 'list'}
                    className={trommelInputMode === 'list' ? 'bg-gray-100 cursor-not-allowed' : trommelInputMode === 'manual' ? 'bg-white' : ''}
                  />
                </div>
                <div>
                  <Label>Max. Tragkraft (kg)</Label>
                  <Input
                    type="number"
                    value={trommelInputMode === 'list' && selectedTrommelData ? 
                    selectedTrommelData.Max_Tragkraft_Kg : manuelleTrommelDaten.max_tragkraft_kg}
                    onChange={(e) => {
                      if (trommelInputMode === 'manual') {
                        setManuelleTrommelDaten(prev => ({
                          ...prev,
                          max_tragkraft_kg: parseInt(e.target.value) || 0
                        }));
                      }
                    }}
                    disabled={trommelInputMode === 'list'}
                    className={trommelInputMode === 'list' ? 'bg-gray-100 cursor-not-allowed' : trommelInputMode === 'manual' ? 'bg-white' : ''}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Berechnungsbereich - zentriert unter der Trennlinie */}
        <div className="border-t border-[#ff7a05]/40 pt-6 mt-6">
          <div className="max-w-md mx-auto space-y-4">

            
            <Button
              onClick={handleBerechnung}
              disabled={isCalculating}
              className="w-full"
              variant="sfm"
            >
              {isCalculating ? 'Berechne...' : 'Maximale Kabellänge berechnen'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
       

      
      {/* Ergebnisse */}
      {berechnungsResult && (
        <Card className={`border-2 ${berechnungsResult?.ist_geeignet ? 'border-green-500' : 'border-red-500'}`}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {berechnungsResult?.ist_geeignet ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                )}
                Berechnungsergebnis
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setBerechnungsResult(null)}
                className="h-8 w-8 p-0 hover:bg-gray-100"
                title="Ergebnisse löschen"
              >
                <X className="h-4 w-4" />
              </Button>
            </CardTitle>
            <CardDescription>
              {berechnungsResult?.empfehlung}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Hauptergebnis */}
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-3xl font-bold text-[#ff7a05]">
                {berechnungsResult?.maximale_kabellänge_m} m
              </div>
              <div className="text-sm text-gray-600">Maximale Kabellänge</div>
            </div>
            
            <Separator className="bg-[#ff7a05]" />
            
            {/* Berechnungsdetails */}
            <div>
              <h4 className="font-semibold mb-2">Berechnungsdetails</h4>
              <div className="grid grid-cols-6 gap-4 text-sm">
                <div>
                  <strong>Kabeldurchmesser mit Zuschlag:</strong><br />
                  {berechnungsResult?.berechnungsdetails?.kdmmz} mm
                </div>
                <div>
                  <strong>Hochlagenzahl:</strong><br />
                  {berechnungsResult?.berechnungsdetails?.hochlagenzahl}
                </div>
                <div>
                  <strong>Querlagenzahl:</strong><br />
                  {berechnungsResult?.berechnungsdetails?.querlagenzahl}
                </div>
                <div>
                  <strong>Mittlerer Trommelumfang:</strong><br />
                  {berechnungsResult?.berechnungsdetails?.mittlerer_trommelumfang} mm
                </div>
                <div>
                  <strong>Tatsächlicher Freiraum:</strong><br />
                  {berechnungsResult?.berechnungsdetails?.tatsächlicher_freiraum} mm
                </div>
                <div>
                  <strong>Gesamtgewicht:</strong><br />
                  {berechnungsResult?.berechnungsdetails?.gewicht_gesamt_kg} kg
                </div>
              </div>
            </div>
            
            {/* Tragkraft-Status */}
            <div className="flex items-center gap-2">
              {berechnungsResult?.berechnungsdetails?.tragkraft_ausreichend ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-600" />
              )}
              <span className="text-sm">
                Tragkraft {berechnungsResult?.berechnungsdetails?.tragkraft_ausreichend ? 'ausreichend' : 'unzureichend'}
              </span>
            </div>
            
            {/* Warnungen */}
            {berechnungsResult?.warnungen && berechnungsResult.warnungen.length > 0 && (
              <div>
                <h4 className="font-semibold mb-2 text-yellow-600">Warnungen</h4>
                {berechnungsResult.warnungen.map((warnung, index) => (
                  <Alert key={index} className="mb-2">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>{warnung}</AlertDescription>
                  </Alert>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </>
  );
}

export default TrommelrechnungComponent;