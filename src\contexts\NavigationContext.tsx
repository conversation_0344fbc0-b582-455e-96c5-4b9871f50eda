import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useLocation } from '@tanstack/react-router';
import { NavigationConfig } from '@/config/navigation/types';
import { 
    dashboardNavigationConfig, 
    stoerungenNavigationConfig, 
    backendNavigationConfig,
    aiNavigationConfig
} from '@/config/navigation';

type ModuleType = 'dashboard' | 'stoerungen' | 'backend' | 'ai' | null;

interface NavigationContextType {
    currentModule: ModuleType;
    currentNavigationConfig: NavigationConfig | null;
    setCurrentModule: (module: ModuleType) => void;
    getNavigationForModule: (module: ModuleType) => NavigationConfig | null;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

interface NavigationProviderProps {
    children: ReactNode;
}

export const NavigationProvider = ({ children }: NavigationProviderProps) => {
    const [currentModule, setCurrentModule] = useState<ModuleType>(null);
    const location = useLocation();

    // Mapping von Modulen zu Navigation-Configs
    const getNavigationForModule = (module: ModuleType): NavigationConfig | null => {
        switch (module) {
            case 'dashboard':
                return dashboardNavigationConfig;
            case 'stoerungen':
                return stoerungenNavigationConfig;
            case 'backend':
                return backendNavigationConfig;
            case 'ai':
                return aiNavigationConfig;
            default:
                return null;
        }
    };

    // Aktuelles Modul basierend auf der Route bestimmen
    useEffect(() => {
        const pathname = location.pathname;
        
        if (pathname.startsWith('/modules/dashboard')) {
            setCurrentModule('dashboard');
        } else if (pathname.startsWith('/modules/stoerungen')) {
            setCurrentModule('stoerungen');
        } else if (pathname.startsWith('/modules/backend')) {
            setCurrentModule('backend');
        } else if (pathname.startsWith('/modules/ai')) {
            setCurrentModule('ai');
        }
        // Für globale Seiten wie /settings oder /user-settings behalten wir das aktuelle Modul bei
        // und ändern es NICHT
    }, [location.pathname]);

    const currentNavigationConfig = getNavigationForModule(currentModule);

    return (
        <NavigationContext.Provider value={{
            currentModule,
            currentNavigationConfig,
            setCurrentModule,
            getNavigationForModule
        }}>
            {children}
        </NavigationContext.Provider>
    );
};

export const useNavigationContext = () => {
    const context = useContext(NavigationContext);
    if (context === undefined) {
        throw new Error('useNavigationContext must be used within a NavigationProvider');
    }
    return context;
};