/**
 * Lazy Loading Configuration for Modules
 * 
 * This file implements lazy loading for all modules to improve initial bundle size
 * and application startup performance.
 */

import { lazy } from 'react';

// Dashboard Module Lazy Loading
export const LazyDashboardPage = lazy(() => import('./dashboard/pages/DashboardPage'));
export const LazyDispatchPage = lazy(() => import('./dashboard/pages/DispatchPage'));
export const LazyCuttingPage = lazy(() => import('./dashboard/pages/CuttingPage'));
export const LazyIncomingGoodsPage = lazy(() => import('./dashboard/pages/IncomingGoodsPage'));
export const LazyCSRPage = lazy(() => import('./dashboard/pages/CSRPage'));
export const LazyArilPage = lazy(() => import('./dashboard/pages/ArilPage'));
export const LazyAtrlPage = lazy(() => import('./dashboard/pages/AtrlPage'));
export const LazyMachinesPage = lazy(() => import('./dashboard/pages/MachinesPage'));

// Störungen Module Lazy Loading
export const LazyStoerungenPage = lazy(() => import('./stoerungen/pages/StoerungenPage'));

// Backend Module Lazy Loading
export const LazySystemPage = lazy(() => import('./backend/pages/SystemPage'));
export const LazyWorkflowPage = lazy(() => import('./backend/pages/WorkflowPage'));

// AI Module Lazy Loading
export const LazyAIChatPage = lazy(() => import('./ai/pages/RAGManagementPage'));
export const LazyCuttingOptimizationPage = lazy(() => import('./ai/pages/CuttingOptimizationPage'));

/**
 * Module Loading Wrapper Component
 * Provides consistent loading state and error boundary for lazy-loaded modules
 */
import React, { Suspense } from 'react';
import { ErrorBoundary } from '@/components/ErrorBoundary';

interface ModuleWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const DefaultLoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
    <span className="ml-4 text-lg">Modul wird geladen...</span>
  </div>
);

export const ModuleWrapper: React.FC<ModuleWrapperProps> = ({ 
  children, 
  fallback = <DefaultLoadingFallback /> 
}) => {
  return (
    <ErrorBoundary>
      <Suspense fallback={fallback}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
};

/**
 * Preload function for critical modules
 * Can be called to preload modules before user navigation
 */
export const preloadModule = (moduleName: string) => {
  switch (moduleName) {
      case 'dashboard-home':
        return import('./dashboard/pages/DashboardPage');
      case 'dashboard-dispatch':
        return import('./dashboard/pages/DispatchPage');
      case 'dashboard-cutting':
        return import('./dashboard/pages/CuttingPage');
      case 'dashboard-incoming-goods':
        return import('./dashboard/pages/IncomingGoodsPage');
      case 'dashboard-csr':
        return import('./dashboard/pages/CSRPage');
      case 'stoerungen':
        return import('./stoerungen/pages/StoerungenPage');
      case 'backend-system':
        return import('./backend/pages/SystemPage');
      case 'backend-workflow':
        return import('./backend/pages/WorkflowPage');
      case 'ai-chat':
        return import('./ai/pages/RAGManagementPage');
      case 'ai-cutting-optimization':
        return import('./ai/pages/CuttingOptimizationPage');
      default:
        console.warn(`Unknown module for preloading: ${moduleName}`);
        return Promise.resolve();
    }
};

/**
 * Preload critical modules on application startup
 * This can be called in the main App component to preload essential modules
 */
export const preloadCriticalModules = () => {
  // Preload the most commonly accessed modules
  preloadModule('dashboard-home');
  
  // Preload based on user role (this would be called after authentication)
  // This is a placeholder for role-based preloading
};

/**
 * Module Bundle Analysis Helper
 * Helps identify which modules are loaded and their sizes
 */
export const getLoadedModules = () => {
  const modules = {
    dashboard: {
      loaded: false,
      components: ['HomePage', 'DispatchPage', 'CuttingPage', 'IncomingGoodsPage', 'CSRPage', 'ArilPage', 'AtrlPage', 'MachinesPage']
    },
    stoerungen: {
      loaded: false,
      components: ['StoerungenPage']
    },
    backend: {
      loaded: false,
      components: ['SystemPage', 'WorkflowPage']
    },
    ai: {
      loaded: false,
      components: ['AIComponents', 'ChatComponents']
    }
  };

  // This would be enhanced with actual module loading detection
  return modules;
};