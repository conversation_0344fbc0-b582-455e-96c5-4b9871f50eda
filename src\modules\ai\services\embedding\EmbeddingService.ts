/**
 * Embedding Service
 * 
 * Handles text embedding generation using OpenRouter API with caching and batch processing.
 * Provides efficient embedding operations for RAG and semantic search.
 */

import axios from 'axios';
import { AIBaseService, AIServiceConfig } from '../base/AIBaseService';
import { AIServiceError } from '../types';
import { getCache, CacheKeyGenerator } from '@/services/cache.service';

/**
 * Embedding Service Configuration
 */
export interface EmbeddingServiceConfig extends AIServiceConfig {
  apiKey?: string;
  apiUrl?: string;
  model?: string;
  maxBatchSize?: number;
  maxRetries?: number;
}

/**
 * Embedding request interface
 */
export interface EmbeddingRequest {
  text: string;
  model?: string;
}

/**
 * Embedding response interface
 */
export interface EmbeddingResponse {
  embedding: number[];
  model: string;
  usage: {
    promptTokens: number;
    totalTokens: number;
  };
}

/**
 * Batch embedding request interface
 */
export interface BatchEmbeddingRequest {
  texts: string[];
  model?: string;
}

/**
 * Batch embedding response interface
 */
export interface BatchEmbeddingResponse {
  embeddings: number[][];
  model: string;
  usage: {
    promptTokens: number;
    totalTokens: number;
  };
}

/**
 * Embedding Service for generating text embeddings via OpenRouter
 */
export class EmbeddingService extends AIBaseService {
  readonly serviceName = 'EmbeddingService';
  private embeddingConfig: EmbeddingServiceConfig;

  constructor(config: EmbeddingServiceConfig = {}) {
    super(config);
    this.embeddingConfig = {
      // Vite verwendet import.meta.env anstelle von process.env
      apiKey: import.meta.env.VITE_OPENROUTER_API_KEY || '',
      apiUrl: 'https://openrouter.ai/api/v1/embeddings',
      model: 'text-embedding-3-small',
      maxBatchSize: 100,
      maxRetries: 3,
      ...config
    };
  }

  /**
   * Initialize the embedding service
   */
  protected async onInitialize(): Promise<void> {
    if (!this.embeddingConfig.apiKey) {
      throw new Error('OpenRouter API key is required for embedding service');
    }

    this.log('Embedding service initialized successfully');
  }

  /**
   * Generate embedding for a single text
   */
  async createEmbedding(text: string, model?: string): Promise<number[]> {
    const cache = getCache();
    const cacheKey = CacheKeyGenerator.forDataType('embedding', {
      model: model || this.embeddingConfig.model,
      textHash: this.hashText(text)
    });

    // Check cache first
    const cached = cache.get<number[]>(cacheKey);
    if (cached) {
      this.performanceMetrics.cacheHits++;
      this.log(`Cache hit for embedding (${text.length} chars)`);
      return cached;
    }

    this.performanceMetrics.cacheMisses++;

    return this.handleAIError(
      async () => {
        const response = await this.requestEmbedding({
          text,
          model: model || this.embeddingConfig.model
        });

        // Cache the result
        cache.set(cacheKey, response.embedding, this.aiConfig.vectorCacheTTL);

        this.log(`Generated embedding for text (${text.length} chars)`);
        return response.embedding;
      },
      async () => {
        // Fallback: return zero vector
        this.log('Embedding generation failed, returning zero vector');
        const dimensions = this.aiConfig.vectorDimensions || 1536;
        const fallbackEmbedding = new Array(dimensions).fill(0);

        // Cache fallback with shorter TTL
        cache.set(cacheKey, fallbackEmbedding, 60000); // 1 minute TTL for fallbacks

        return fallbackEmbedding;
      },
      AIServiceError.EMBEDDING_FAILED
    );
  }

  /**
   * Generate embeddings for multiple texts in batch
   */
  async createEmbeddingsBatch(texts: string[], model?: string): Promise<number[][]> {
    const batchSize = this.embeddingConfig.maxBatchSize || 100;
    const results: number[][] = [];

    // Process in batches
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      const batchResults = await this.processBatch(batch, model);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * Process a batch of texts for embedding
   */
  private async processBatch(texts: string[], model?: string): Promise<number[][]> {
    const cache = getCache();
    const actualModel = model || this.embeddingConfig.model;

    // Check cache for each text first
    const cachedResults: (number[] | null)[] = texts.map(text => {
      const cacheKey = CacheKeyGenerator.forDataType('embedding', {
        model: actualModel,
        textHash: this.hashText(text)
      });
      return cache.get<number[]>(cacheKey) || null;
    });

    // Identify texts that need embedding
    const uncachedIndices: number[] = [];
    const uncachedTexts: string[] = [];

    cachedResults.forEach((cached, index) => {
      if (!cached) {
        uncachedIndices.push(index);
        uncachedTexts.push(texts[index]);
      } else {
        this.performanceMetrics.cacheHits++;
      }
    });

    if (uncachedTexts.length === 0) {
      // All results were cached
      this.log(`All ${texts.length} embeddings found in cache`);
      return cachedResults as number[][];
    }

    this.performanceMetrics.cacheMisses += uncachedTexts.length;

    return this.handleAIError(
      async () => {
        const response = await this.requestBatchEmbedding({
          texts: uncachedTexts,
          model: actualModel
        });

        // Cache the new embeddings
        response.embeddings.forEach((embedding, index) => {
          const originalIndex = uncachedIndices[index];
          const text = texts[originalIndex];
          const cacheKey = CacheKeyGenerator.forDataType('embedding', {
            model: actualModel,
            textHash: this.hashText(text)
          });
          cache.set(cacheKey, embedding, this.aiConfig.vectorCacheTTL);
        });

        // Merge cached and new results
        const finalResults: number[][] = [];
        let newEmbeddingIndex = 0;

        for (let i = 0; i < texts.length; i++) {
          if (cachedResults[i]) {
            finalResults[i] = cachedResults[i]!;
          } else {
            finalResults[i] = response.embeddings[newEmbeddingIndex++];
          }
        }

        this.log(`Generated ${response.embeddings.length} new embeddings, used ${texts.length - uncachedTexts.length} cached`);
        return finalResults;
      },
      async () => {
        // Fallback: generate individual embeddings
        this.log('Batch embedding failed, falling back to individual requests');
        const fallbackResults: number[][] = [];

        for (const text of texts) {
          try {
            const embedding = await this.createEmbedding(text, model);
            fallbackResults.push(embedding);
          } catch (error) {
            // If individual embedding fails, use zero vector
            const dimensions = this.aiConfig.vectorDimensions || 1536;
            fallbackResults.push(new Array(dimensions).fill(0));
          }
        }

        return fallbackResults;
      },
      AIServiceError.EMBEDDING_FAILED
    );
  }

  /**
   * Request single embedding from OpenRouter API with retry logic
   */
  private async requestEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const maxRetries = this.embeddingConfig.maxRetries || 3;
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Add exponential backoff delay for retries
        if (attempt > 1) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 2), 10000); // Max 10s delay
          await new Promise(resolve => setTimeout(resolve, delay));
          this.log(`Retrying embedding request (attempt ${attempt}/${maxRetries}) after ${delay}ms delay`);
        }

        const response = await axios.post(
          this.embeddingConfig.apiUrl!,
          {
            model: request.model || this.embeddingConfig.model || 'text-embedding-3-small',
            input: request.text
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.embeddingConfig.apiKey}`,
              'HTTP-Referer': 'https://lapp-sfm.com',
              'X-Title': 'Lapp SFM Dashboard'
            },
            timeout: this.config.timeout || 30000
          }
        );

        if (!response.data || !response.data.data || !response.data.data[0]) {
          throw new Error('Invalid embedding response format');
        }

        return {
          embedding: response.data.data[0].embedding,
          model: response.data.model,
          usage: {
            promptTokens: response.data.usage?.prompt_tokens || 0,
            totalTokens: response.data.usage?.total_tokens || 0
          }
        };
      } catch (error: any) {
        lastError = error;

        // Don't retry on certain errors
        if (error.response?.status === 401 || error.response?.status === 403) {
          throw new Error(`Authentication failed: ${error.response?.data?.error || error.message}`);
        }

        // For rate limiting, wait longer before retry
        if (error.response?.status === 429) {
          const retryAfter = error.response.headers['retry-after'];
          const delay = retryAfter ? parseInt(retryAfter) * 1000 : 60000; // Default 1 minute

          if (attempt < maxRetries) {
            this.log(`Rate limited, waiting ${delay}ms before retry`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        }

        // Log the error and continue to next attempt
        this.log(`Embedding request failed (attempt ${attempt}/${maxRetries}):`, error.message);

        if (attempt === maxRetries) {
          throw lastError;
        }
      }
    }

    throw lastError!;
  }

  /**
   * Request batch embeddings from OpenRouter API with retry logic
   */
  private async requestBatchEmbedding(request: BatchEmbeddingRequest): Promise<BatchEmbeddingResponse> {
    const maxRetries = this.embeddingConfig.maxRetries || 3;
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Add exponential backoff delay for retries
        if (attempt > 1) {
          const delay = Math.min(2000 * Math.pow(2, attempt - 2), 20000); // Max 20s delay for batch
          await new Promise(resolve => setTimeout(resolve, delay));
          this.log(`Retrying batch embedding request (attempt ${attempt}/${maxRetries}) after ${delay}ms delay`);
        }

        const response = await axios.post(
          this.embeddingConfig.apiUrl!,
          {
            model: request.model || this.embeddingConfig.model || 'text-embedding-3-small',
            input: request.texts
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.embeddingConfig.apiKey}`,
              'HTTP-Referer': 'https://lapp-sfm.com',
              'X-Title': 'Lapp SFM Dashboard'
            },
            timeout: this.config.timeout || 60000 // Longer timeout for batch requests
          }
        );

        if (!response.data || !response.data.data) {
          throw new Error('Invalid batch embedding response format');
        }

        const embeddings = response.data.data.map((item: any) => item.embedding);

        return {
          embeddings,
          model: response.data.model,
          usage: {
            promptTokens: response.data.usage?.prompt_tokens || 0,
            totalTokens: response.data.usage?.total_tokens || 0
          }
        };
      } catch (error: any) {
        lastError = error;

        // Don't retry on certain errors
        if (error.response?.status === 401 || error.response?.status === 403) {
          throw new Error(`Authentication failed: ${error.response?.data?.error || error.message}`);
        }

        // For rate limiting, wait longer before retry
        if (error.response?.status === 429) {
          const retryAfter = error.response.headers['retry-after'];
          const delay = retryAfter ? parseInt(retryAfter) * 1000 : 120000; // Default 2 minutes for batch

          if (attempt < maxRetries) {
            this.log(`Batch rate limited, waiting ${delay}ms before retry`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        }

        // Log the error and continue to next attempt
        this.log(`Batch embedding request failed (attempt ${attempt}/${maxRetries}):`, error.message);

        if (attempt === maxRetries) {
          throw lastError;
        }
      }
    }

    throw lastError!;
  }

  /**
   * Calculate cosine similarity between two embeddings
   */
  calculateSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimensions');
    }

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);
    return magnitude === 0 ? 0 : dotProduct / magnitude;
  }

  /**
   * Validate embedding dimensions
   */
  validateEmbedding(embedding: number[]): boolean {
    const expectedDimensions = this.aiConfig.vectorDimensions || 1536;

    if (!Array.isArray(embedding)) {
      return false;
    }

    if (embedding.length !== expectedDimensions) {
      return false;
    }

    // Check if all values are numbers
    return embedding.every(value => typeof value === 'number' && !isNaN(value));
  }

  /**
   * Normalize embedding vector
   */
  normalizeEmbedding(embedding: number[]): number[] {
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));

    if (magnitude === 0) {
      return embedding;
    }

    return embedding.map(val => val / magnitude);
  }

  /**
   * Hash text for caching
   */
  private hashText(text: string): string {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Health check for embedding service
   */
  protected async onHealthCheck(): Promise<void> {
    if (!this.embeddingConfig.apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    // Test embedding generation with a simple text
    try {
      await this.withTimeout(
        async () => {
          const testEmbedding = await this.createEmbedding('test');
          if (!this.validateEmbedding(testEmbedding)) {
            throw new Error('Invalid test embedding generated');
          }
        },
        5000 // 5 second timeout for health check
      );
    } catch (error) {
      throw new Error(`Embedding service health check failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Clear embedding cache
   */
  clearEmbeddingCache(): number {
    const cache = getCache();
    return cache.deleteByPrefix(CacheKeyGenerator.prefixFor('data') + 'embedding');
  }

  /**
   * Get embedding service statistics
   */
  getEmbeddingStats() {
    const performance = this.getPerformanceMetrics();
    const cache = getCache();
    const cacheStats = cache.getStats();

    return {
      ...performance,
      model: this.embeddingConfig.model,
      dimensions: this.aiConfig.vectorDimensions,
      maxBatchSize: this.embeddingConfig.maxBatchSize,
      cache: {
        hitRate: cacheStats.hitRate,
        entryCount: cacheStats.entryCount,
        memoryUsage: cacheStats.memoryUsage
      }
    };
  }
}