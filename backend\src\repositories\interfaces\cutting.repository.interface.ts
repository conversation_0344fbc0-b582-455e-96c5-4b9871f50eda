/**
 * Cutting Repository Interface
 * 
 * Repository-Schnittstelle für Schnitt-/Cutting-Operationen
 * einschließlich Ablaengerei und Maschineneffizienz.
 */

import { BaseRepository, RepositoryStats } from './base.repository.interface';
import { DateRange } from './dispatch.repository.interface';
import { AblaengereiDataPoint, WEDataPoint } from '../../types/database.types';

export interface CuttingRepository {
  /**
   * Ablaengerei-Daten abrufen
   */
  getAblaengereiData(dateRange?: DateRange): Promise<AblaengereiDataPoint[]>;

  /**
   * Wareneingang (WE) Daten
   */
  getWEData(dateRange?: DateRange): Promise<WEDataPoint[]>;

  /**
   * Schnittdaten für Charts
   */
  getCuttingChartData(dateRange?: DateRange): Promise<CuttingChartData[]>;

  /**
   * Lager-Cuts Chart-Daten
   */
  getLagerCutsChartData(dateRange?: DateRange): Promise<LagerCutsChartData[]>;

  /**
   * Maschinen-Effizienz-Daten
   */
  getMaschinenEfficiency(dateRange?: DateRange): Promise<MachineEfficiencyData[]>;

  /**
   * Schnittdaten (Rohdaten)
   */
  getSchnitteData(): Promise<any[]>;

  /**
   * Cutting-Performance-Übersicht
   */
  getCuttingPerformanceOverview(dateRange?: DateRange): Promise<CuttingPerformanceOverview>;

  /**
   * Top-Performance-Maschinen
   */
  getTopPerformingMachines(limit?: number): Promise<TopMachine[]>;

  /**
   * Maschinen-Auslastungsanalyse
   */
  getMachineUtilizationAnalysis(): Promise<MachineUtilization[]>;

  /**
   * Repository-Statistiken abrufen
   */
  getStats(): Promise<RepositoryStats>;

  /**
   * Repository-Cache invalidieren
   */
  invalidateCache(key?: string): Promise<void>;
}

/**
 * Cutting Chart Daten
 */
export interface CuttingChartData {
  name: string;
  date: string;
  cutTT: number;
  cutTR: number;
  cutRR: number;
  pickCut: number;
}

/**
 * Lager-Cuts Chart Daten
 */
export interface LagerCutsChartData {
  name: string;
  date: string;
  lagerSumme: number;
  cutLagerKSumme: number;
  cutLagerRSumme: number;
}

/**
 * Maschinen-Effizienz-Daten
 */
export interface MachineEfficiencyData {
  Datum: string;
  Machine: string;
  sollSchnitte: number;
  tagesSchnitte: number;
  istSchnitteProStunde: number;
  effizienzProzent: number;
}

/**
 * Cutting-Performance-Übersicht
 */
export interface CuttingPerformanceOverview {
  totalCuts: {
    cutTT: number;
    cutTR: number;
    cutRR: number;
    pickCut: number;
  };
  averageEfficiency: number;
  topMachine: {
    name: string;
    efficiency: number;
  };
  periodSummary: {
    startDate: string;
    endDate: string;
    totalDays: number;
    averageCutsPerDay: number;
  };
  trends: {
    efficiency: 'improving' | 'declining' | 'stable';
    volume: 'increasing' | 'decreasing' | 'stable';
  };
}

/**
 * Top-Maschinen
 */
export interface TopMachine {
  name: string;
  averageEfficiency: number;
  totalCuts: number;
  uptime: number;
  rank: number;
}

/**
 * Maschinen-Auslastung
 */
export interface MachineUtilization {
  machineName: string;
  plannedHours: number;
  actualHours: number;
  utilizationRate: number;
  efficiency: number;
  status: 'optimal' | 'underutilized' | 'overutilized';
}