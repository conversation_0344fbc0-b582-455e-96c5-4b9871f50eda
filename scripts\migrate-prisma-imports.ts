#!/usr/bin/env tsx

/**
 * Batch-Migration-Skript für Prisma → Drizzle
 * 
 * Dieses Skript ersetzt systematisch alle Prisma-Imports und -Aufrufe
 * durch die entsprechenden Drizzle-Equivalents
 */

import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

interface MigrationRule {
  from: RegExp;
  to: string;
  description: string;
}

// Migration Rules für Prisma → Drizzle
const migrationRules: MigrationRule[] = [
  // Import-Statements
  {
    from: /import\s+{\s*PrismaClient.*?\s*}\s+from\s+['"]@prisma-sfm-dashboard\/client['"];?/g,
    to: "import { db } from '../db';\nimport { eq, desc, count, and, gte, lte, inArray } from 'drizzle-orm';",
    description: "PrismaClient Import → Drizzle Import"
  },
  {
    from: /import\s+{\s*.*?PrismaClient.*?\s*}\s+from\s+['"]@prisma.*?['"];?/g,
    to: "import { db } from '../db';\nimport { eq, desc, count, and, gte, lte, inArray } from 'drizzle-orm';",
    description: "Alle Prisma Imports → Drizzle Imports"
  },

  // Constructor und Instance-Erstellung
  {
    from: /private\s+prisma:\s*PrismaClient;?/g,
    to: "// Drizzle db instance from ../db",
    description: "Prisma Property → Comment"
  },
  {
    from: /this\.prisma\s*=\s*new\s+PrismaClient\([^)]*\);?/g,
    to: "// Using shared Drizzle db instance",
    description: "PrismaClient Constructor → Comment"
  },
  {
    from: /new\s+PrismaClient\([^)]*\)/g,
    to: "/* Replaced with Drizzle db */",
    description: "PrismaClient Constructor → Comment"
  },

  // Einfache Query-Ersetzungen
  {
    from: /this\.prisma\.(\w+)\.findMany\(\s*\)/g,
    to: "db.select().from($1)",
    description: "findMany() → select().from()"
  },
  {
    from: /this\.prisma\.(\w+)\.findUnique\(\s*{\s*where:\s*{\s*id:\s*(\w+)\s*}\s*}\s*\)/g,
    to: "db.select().from($1).where(eq($1.id, $2)).limit(1)",
    description: "findUnique by ID → select with where"
  },
  {
    from: /this\.prisma\.(\w+)\.create\(\s*{\s*data:\s*([^}]+)\s*}\s*\)/g,
    to: "db.insert($1).values($2).returning()",
    description: "create() → insert().values().returning()"
  },
  {
    from: /this\.prisma\.(\w+)\.update\(\s*{\s*where:\s*{\s*id:\s*(\w+)\s*},\s*data:\s*([^}]+)\s*}\s*\)/g,
    to: "db.update($1).set($3).where(eq($1.id, $2)).returning()",
    description: "update() → update().set().where()"
  },
  {
    from: /this\.prisma\.(\w+)\.delete\(\s*{\s*where:\s*{\s*id:\s*(\w+)\s*}\s*}\s*\)/g,
    to: "db.delete($1).where(eq($1.id, $2))",
    description: "delete() → delete().where()"
  },

  // Connect/Disconnect
  {
    from: /this\.prisma\.\$connect\(\)/g,
    to: "Promise.resolve() /* Drizzle auto-connects */",
    description: "$connect() → Promise.resolve()"
  },
  {
    from: /this\.prisma\.\$disconnect\(\)/g,
    to: "Promise.resolve() /* Drizzle auto-disconnects */",
    description: "$disconnect() → Promise.resolve()"
  },

  // Constructor Parameters
  {
    from: /constructor\(\s*prisma:\s*PrismaClient\s*\)\s*{/g,
    to: "constructor() {",
    description: "Remove Prisma constructor parameter"
  }
];

/**
 * Findet alle TypeScript-Dateien im Backend-Verzeichnis
 */
async function findBackendFiles(): Promise<string[]> {
  const patterns = [
    'backend/src/**/*.ts',
    'src/**/*.ts'
  ];

  const files: string[] = [];
  for (const pattern of patterns) {
    try {
      const matches = await glob(pattern, { cwd: process.cwd() });
      files.push(...matches);
    } catch (error) {
      console.warn(`Pattern ${pattern} nicht gefunden:`, error);
    }
  }

  // Filter out test files and node_modules
  return files.filter(file => 
    !file.includes('node_modules') &&
    !file.includes('.test.') &&
    !file.includes('.spec.') &&
    file.endsWith('.ts')
  );
}

/**
 * Wendet Migration-Rules auf eine Datei an
 */
function migrateFileContent(content: string, filename: string): { 
  content: string; 
  changes: number; 
  appliedRules: string[]; 
} {
  let migratedContent = content;
  let totalChanges = 0;
  const appliedRules: string[] = [];

  for (const rule of migrationRules) {
    const matches = migratedContent.match(rule.from);
    if (matches) {
      migratedContent = migratedContent.replace(rule.from, rule.to);
      totalChanges += matches.length;
      appliedRules.push(rule.description);
    }
  }

  return {
    content: migratedContent,
    changes: totalChanges,
    appliedRules
  };
}

/**
 * Haupt-Migration-Funktion
 */
async function runMigration() {
  console.log('🔄 Starting Prisma → Drizzle batch migration...\n');

  try {
    const files = await findBackendFiles();
    console.log(`📁 Found ${files.length} TypeScript files to scan\n`);

    let totalFiles = 0;
    let migratedFiles = 0;
    let totalChanges = 0;

    for (const file of files) {
      totalFiles++;
      
      try {
        const fullPath = path.resolve(file);
        const content = fs.readFileSync(fullPath, 'utf-8');
        
        // Check if file contains Prisma references
        if (!content.includes('prisma') && !content.includes('PrismaClient')) {
          continue; // Skip files without Prisma references
        }

        const { content: migratedContent, changes, appliedRules } = migrateFileContent(content, file);
        
        if (changes > 0) {
          // Create backup
          const backupPath = `${fullPath}.prisma-backup`;
          fs.writeFileSync(backupPath, content);
          
          // Write migrated content
          fs.writeFileSync(fullPath, migratedContent);
          
          migratedFiles++;
          totalChanges += changes;
          
          console.log(`✅ ${file}`);
          console.log(`   📊 Changes: ${changes}`);
          console.log(`   🔧 Rules: ${appliedRules.join(', ')}`);
          console.log(`   💾 Backup: ${path.basename(backupPath)}`);
          console.log('');
        }
      } catch (error) {
        console.error(`❌ Error processing ${file}:`, error);
      }
    }

    console.log('\n🎉 Migration Summary:');
    console.log(`   📁 Total files scanned: ${totalFiles}`);
    console.log(`   ✅ Files migrated: ${migratedFiles}`);
    console.log(`   🔧 Total changes: ${totalChanges}`);
    console.log('   💾 Backups created with .prisma-backup extension');
    
    if (migratedFiles > 0) {
      console.log('\n⚠️  Next steps:');
      console.log('   1. Test the migrated code');
      console.log('   2. Fix any compilation errors');
      console.log('   3. Remove .prisma-backup files if everything works');
      console.log('   4. Update imports and table references manually if needed');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration if called directly
if (require.main === module) {
  runMigration();
}

export { runMigration, migrationRules };
