/**
 * API-Konfigurations-Service
 * 
 * Verwaltet die sichere Laden der API-Konfiguration vom Main-Process
 * und stellt sie dem ApiService zur Verfügung.
 */

import { AppConfig } from '@/helpers/ipc/config/config-context';

/**
 * API-Konfiguration mit Standardwerten
 */
let apiConfig: AppConfig = {
  apiKey: 'sfm_api_a7c4f2e8b9d6c3a1f5e9b2d7c4a8f6e3b1d9c5a7f2e8b4d6c9a3f7e1b5d2c8a4f6e9b3d7c1a5f8e2b6d4c',
  apiBaseUrl: 'http://localhost:3001/api',
  environment: 'development',
  version: '1.0.0'
};

/**
 * Flag um zu verfolgen ob die Konfiguration geladen wurde
 */
let configLoaded = false;

/**
 * Promise für das Laden der Konfiguration (verhindert mehrfaches Laden)
 */
let configPromise: Promise<AppConfig> | null = null;

/**
 * Lädt die API-Konfiguration vom Main-Process
 * @returns Promise mit der geladenen Konfiguration
 */
export async function loadApiConfig(): Promise<AppConfig> {
  // Verhindere mehrfaches gleichzeitiges Laden
  if (configPromise) {
    return configPromise;
  }

  // Wenn bereits geladen, gib die aktuelle Konfiguration zurück
  if (configLoaded) {
    return apiConfig;
  }

  configPromise = (async () => {
    try {
      // Prüfe ob configAPI verfügbar ist (Electron-Umgebung)
      if (typeof window !== 'undefined' && (window as any).configAPI) {
        console.log('[CONFIG] Lade Konfiguration vom Main-Process...');
        
        const loadedConfig = await (window as any).configAPI.getConfig();
        apiConfig = loadedConfig;
        configLoaded = true;
        
        console.log('[CONFIG] ✅ Konfiguration erfolgreich geladen:', {
          apiBaseUrl: apiConfig.apiBaseUrl,
          environment: apiConfig.environment,
          version: apiConfig.version,
          hasApiKey: !!apiConfig.apiKey
        });

        // Lausche auf Konfigurationsänderungen
        (window as any).configAPI.onConfigUpdated((newConfig: AppConfig) => {
          console.log('[CONFIG] 🔄 Konfiguration aktualisiert');
          apiConfig = newConfig;
        });
        
        return apiConfig;
      } else {
        // Fallback für Nicht-Electron-Umgebungen (z.B. Tests)
        console.warn('[CONFIG] ⚠️ Electron configAPI nicht verfügbar, verwende Fallback-Konfiguration');
        configLoaded = true;
        return apiConfig;
      }
    } catch (error) {
      console.error('[CONFIG] ❌ Fehler beim Laden der Konfiguration:', error);
      // Bei Fehlern verwende Standardkonfiguration
      configLoaded = true;
      return apiConfig;
    } finally {
      configPromise = null;
    }
  })();

  return configPromise;
}

/**
 * Gibt die aktuelle API-Konfiguration zurück
 * Lädt sie falls notwendig vom Main-Process
 * @returns Promise mit der API-Konfiguration
 */
export async function getApiConfig(): Promise<AppConfig> {
  if (!configLoaded) {
    await loadApiConfig();
  }
  return apiConfig;
}

/**
 * Gibt die aktuelle API-Konfiguration synchron zurück
 * Sollte nur verwendet werden, wenn sicher ist, dass die Konfiguration bereits geladen wurde
 * @returns Aktuelle API-Konfiguration
 */
export function getApiConfigSync(): AppConfig {
  if (!configLoaded) {
    console.warn('[CONFIG] ⚠️ Konfiguration noch nicht geladen, verwende Standardwerte');
  }
  return apiConfig;
}

/**
 * Überprüft ob die API-Konfiguration geladen wurde
 * @returns True wenn die Konfiguration geladen wurde
 */
export function isConfigLoaded(): boolean {
  return configLoaded;
}

/**
 * Erstellt authentifizierte Headers mit JWT Token oder API-Konfiguration
 * @param additionalHeaders Zusätzliche Headers
 * @returns Headers-Objekt mit Authentifizierung
 */
export function createAuthHeaders(additionalHeaders: Record<string, string> = {}): Record<string, string> {
  const config = getApiConfigSync();
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...additionalHeaders,
  };

  // Check if we're in a browser environment (renderer process)
  if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
    // Check for JWT token first (for user authentication)
    const jwtToken = localStorage.getItem('auth_token');
    
    if (jwtToken) {
      // Use JWT token for authenticated user requests
      headers['Authorization'] = `Bearer ${jwtToken}`;
      return headers;
    }
  }
  
  // For main process or when no JWT token is available, use API key
  if (config.apiKey) {
    headers['Authorization'] = `Bearer ${config.apiKey}`;
    headers['X-API-Key'] = config.apiKey;
  } else {
    console.warn('[CONFIG] ⚠️ Weder JWT-Token noch API-Schlüssel verfügbar');
  }

  return headers;
}