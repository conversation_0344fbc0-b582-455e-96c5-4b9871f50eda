import { sqliteTable, AnySQLiteColumn, index, integer, numeric, real, text, uniqueIndex } from "drizzle-orm/sqlite-core"
  import { sql } from "drizzle-orm"

export const alleDaten = sqliteTable("alle_daten", {
	id: integer().primaryKey({ autoIncrement: true }),
	datum: numeric().notNull(),
	weAtrl: integer("we_atrl"),
	cutkLagerAtrl: integer("cutk_lager_atrl"),
	cutrLagerAtrl: integer("cutr_lager_atrl"),
	loLoAtrl: integer("lo_lo_atrl"),
	umlAtrl: integer("uml_atrl"),
	waPosAtrl: integer("wa_pos_atrl"),
	lagerCutAtrl: integer("lager_cut_atrl"),
	plaetzeBelAtrl: integer("plaetze_bel_atrl"),
	syspAtrl: integer("sysp_atrl"),
	syspRucksAtrl: integer("sysp_rucks_atrl"),
	sysstEinzAtrl: integer("sysst_einz_atrl"),
	syspEinzBelAtrl: integer("sysp_einz_bel_atrl"),
	tromBelAtrl: integer("trom_bel_atrl"),
	fuellgradAtrl: real("fuellgrad_atrl"),
	allBewAtrl: integer("all_bew_atrl"),
	weGesamtAtrl: integer("we_gesamt_atrl"),
	weManl: integer("we_manl"),
	cutLagerKundeManl: integer("cut_lager_kunde_manl"),
	cutLagerRest995Manl: integer("cut_lager_rest_995_manl"),
	lagerolLagero312Manl: integer("lagerol_lagero_312_manl"),
	auslKommiManl: integer("ausl_kommi_manl"),
	rueckKommiManl: integer("rueck_kommi_manl"),
	waPos601Manl: integer("wa_pos_601_manl"),
	lagerCut994Manl: integer("lager_cut_994_manl"),
	plaetzeBelManl: integer("plaetze_bel_manl"),
	fuellgradManl: real("fuellgrad_manl"),
	allBewManl: integer("all_bew_manl"),
	weManl2: integer("we_manl_2"),
	cutLagerKunde996Aril: integer("cut_lager_kunde_996_aril"),
	cutLagerRestAril: integer("cut_lager_rest_aril"),
	waPos601Aril: integer("wa_pos_601_aril"),
	umlAril: integer("uml_aril"),
	lagerCutAril: integer("lager_cut_aril"),
	plaetzeBelAril: integer("plaetze_bel_aril"),
	systabStkAril: integer("systab_stk_aril"),
	systabRucksStkAril: integer("systab_rucks_stk_aril"),
	systabEinzelStkAril: integer("systab_einzel_stk_aril"),
	systabBelegtEinzelAril: integer("systab_belegt_einzel_aril"),
	ringBelegtAril: integer("ring_belegt_aril"),
	fuellgradAril: real("fuellgrad_aril"),
	bewAril: integer("bew_aril"),
	taSesamtAbl: integer("ta_sesamt_abl"),
	taTtAbl: integer("ta_tt_abl"),
	taTrAbl: integer("ta_tr_abl"),
	taRrAbl: integer("ta_rr_abl"),
	schnitteGesAbl: integer("schnitte_ges_abl"),
	cuttopickAbl: integer("cuttopick_abl"),
	atrlSapNi: integer("atrl_sap_ni"),
	atrlWitronNio: integer("atrl_witron_nio"),
	atrlSiemensNio: integer("atrl_siemens_nio"),
	atrlProzessNio: integer("atrl_prozess_nio"),
	atrlSonstNio: integer("atrl_sonst_nio"),
	arilSapNio: integer("aril_sap_nio"),
	arilWitronNio: integer("aril_witron_nio"),
	arilSiemensNio: integer("aril_siemens_nio"),
	arilNio: integer("aril_nio"),
	aril2Nio: integer("aril2_nio"),
	"atrlStörungVerf": integer("atrl_störung_verf"),
	"arilStörungVerf": integer("aril_störung_verf"),
	"ftsRbgMfrStörungVerf": integer("fts_rbg_mfr_störung_verf"),
	"ftsRbgStörungVerf": integer("fts_rbg_störung_verf"),
	"ftsStörungVerf": integer("fts_störung_verf"),
	"arilAtrlFtsStörungVerf": integer("aril_atrl_fts_störung_verf"),
	"itmStörungVerf": integer("itm_störung_verf"),
	"sapStörungVerf": integer("sap_störung_verf"),
	servicegrad: real(),
	waEinzelPos: integer("wa_einzel_pos"),
	we: integer(),
	wa: integer(),
	waLagebest: integer("wa_lagebest"),
	waMehrPos: integer("wa_mehr_pos"),
	weGesManlAtrl: integer("we_ges_manl_atrl"),
	waGesAblManlAtrlAril: integer("wa_ges_abl_manl_atrl_aril"),
},
(table) => [
	index("alle_daten_datum_idx").on(table.datum),
]);

export const manL = sqliteTable("ManL", {
	id: integer().primaryKey({ autoIncrement: true }),
	datum: numeric("Datum"),
	wareneingang: integer("Wareneingang"),
	cuttingLagerKunde: integer(),
	cuttingLagerRest: integer(),
	ruecklagerungVonKommi: integer(),
	waTaPos: integer(),
	lagerCutting: integer(),
	belegtePlaetze: integer(),
	auslastung: real("Auslastung"),
	alleBewegungen: integer(),
},
(table) => [
	index("ManL_Datum_idx").on(table.datum),
]);

export const schnitte = sqliteTable("schnitte", {
	id: integer().primaryKey(),
	datum: text("Datum"),
	m5RH1: integer("M5-R-H1"),
	m6TH1: integer("M6-T-H1"),
	m7RH1: integer("M7-R-H1"),
	m8TH1: integer("M8-T-H1"),
	m9RH1: integer("M9-R-H1"),
	m10TH1: integer("M10-T-H1"),
	m11RH1: integer("M11-R-H1"),
	m12TH1: integer("M12-T-H1"),
	m13RH1: integer("M13-R-H1"),
	m14TH1: integer("M14-T-H1"),
	m15RH1: integer("M15-R-H1"),
	m16TH1: integer("M16-T-H1"),
	m17RH1: integer("M17-R-H1"),
	m18TH1: integer("M18-T-H1"),
	m19TH1: integer("M19-T-H1"),
	m20TH1: integer("M20-T-H1"),
	m21RH1: integer("M21-R-H1"),
	m23TH1: integer("M23-T-H1"),
	m25RrH1: integer("M25-RR-H1"),
	m26TH1: integer("M26-T-H1"),
	sumH1: integer("Sum-H1"),
	m1TH3: integer("M1-T-H3"),
	m2TH3: integer("M2-T-H3"),
	m3RH3: integer("M3-R-H3"),
	m4TH3: integer("M4-T-H3"),
	m22TH3: integer("M22-T-H3"),
	m24TH3: integer("M24-T-H3"),
	m27RH3: integer("M27-R-H3"),
	sumH3: integer("Sum-H3"),
},
(table) => [
	index("schnitte_Datum_idx").on(table.datum),
]);

export const maschinen = sqliteTable("maschinen", {
	id: integer().primaryKey({ autoIncrement: true }),
	machine: text("Machine"),
	schnitteProStd: real(),
});

export const bestandRest = sqliteTable("bestandRest", {
	id: integer().primaryKey({ autoIncrement: true }),
	lagertyp: text("Lagertyp"),
	lagerplatz: text("Lagerplatz"),
	material: text("Material"),
	charge: text("Charge"),
	dauer: real("Dauer"),
	lagerbereich: text("Lagerbereich"),
	lagerplatztyp: text("Lagerplatztyp"),
	lagerplatzaufteilung: text("Lagerplatzaufteilung"),
	auslagerungssperre: text("Auslagerungssperre"),
	einlagerungssperre: text("Einlagerungssperre"),
	sperrgrund: text("Sperrgrund"),
	letzteBewegung: text("Letzte Bewegung"),
	uhrzeit: text("Uhrzeit"),
	taNummer: text("TA-Nummer"),
	taPosition: text("TA-Position"),
	"letzterÄnderer": text("Letzter Änderer"),
	"letzteÄnderung": text("Letzte Änderung"),
	wareneingangsdatum: text("Wareneingangsdatum"),
	weNummer: text("WE-Nummer"),
	wePosition: text("WE-Position"),
	lieferung: text("Lieferung"),
	position: text("Position"),
	lagereinheitentyp: text("Lagereinheitentyp"),
	gesamtbestand: real("Gesamtbestand"),
	lagereinheit: text("Lagereinheit"),
	aufnahmeDatum: text(),
	aufnahmeZeit: text(),
	importTimestamp: numeric("import_timestamp").default(sql`(CURRENT_TIMESTAMP)`),
},
(table) => [
	index("bestandRest_import_timestamp_idx").on(table.importTimestamp),
	index("bestandRest_aufnahmeDatum_idx").on(table.aufnahmeDatum),
]);

export const auslastung240 = sqliteTable("auslastung240", {
	id: integer().primaryKey({ autoIncrement: true }),
	aufnahmeDatum: text(),
	aufnahmeZeit: text(),
	maxPlaetze: text(),
	auslastung: text(),
	maxA: text(),
	maxB: text(),
	maxC: text(),
	auslastungA: text(),
	auslastungB: text(),
	auslastungC: text(),
	importTimestamp: numeric("import_timestamp").default(sql`(CURRENT_TIMESTAMP)`),
},
(table) => [
	index("auslastung240_import_timestamp_idx").on(table.importTimestamp),
	index("auslastung240_aufnahmeDatum_idx").on(table.aufnahmeDatum),
]);

export const auslastung200 = sqliteTable("auslastung200", {
	id: integer().primaryKey({ autoIncrement: true }),
	aufnahmeDatum: text(),
	aufnahmeZeit: text(),
	maxPlaetze: text(),
	auslastung: text(),
	maxA: text(),
	maxB: text(),
	maxC: text(),
	auslastungA: text(),
	auslastungB: text(),
	auslastungC: text(),
	importTimestamp: numeric("import_timestamp").default(sql`(CURRENT_TIMESTAMP)`),
},
(table) => [
	index("auslastung200_import_timestamp_idx").on(table.importTimestamp),
	index("auslastung200_aufnahmeDatum_idx").on(table.aufnahmeDatum),
]);

export const stoerungen = sqliteTable("Stoerungen", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	title: text().notNull(),
	description: text(),
	severity: text().notNull(),
	status: text().notNull(),
	category: text(),
	affectedSystem: text("affected_system"),
	location: text(),
	reportedBy: text("reported_by"),
	assignedTo: text("assigned_to"),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").notNull(),
	resolvedAt: numeric("resolved_at"),
	mttrMinutes: integer("mttr_minutes"),
	tags: text(),
	resolutionSteps: text("resolution_steps"),
	rootCause: text("root_cause"),
	lessonsLearned: text("lessons_learned"),
},
(table) => [
	index("Stoerungen_affected_system_idx").on(table.affectedSystem),
	index("Stoerungen_severity_idx").on(table.severity),
	index("Stoerungen_status_idx").on(table.status),
	index("Stoerungen_created_at_idx").on(table.createdAt),
]);

export const stoerungsComments = sqliteTable("StoerungsComments", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	stoerungId: integer("stoerung_id").notNull(),
	userId: text("user_id"),
	comment: text().notNull(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	caretakerId: text("caretaker_id").default("3"),
},
(table) => [
	index("StoerungsComments_created_at_idx").on(table.createdAt),
	index("StoerungsComments_stoerung_id_idx").on(table.stoerungId),
]);

export const atrL = sqliteTable("ATrL", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	datum: text("Datum"),
	umlagerungen: integer(),
	waTaPositionen: integer(),
	belegtePlaetze: integer(),
	davonSystempaletten: integer(),
	systempalettenstapelRucksackpaetzen: integer("SystempalettenstapelRucksackpaetzen"),
	systempalettenstapelEinzel: integer("SystempalettenstapelEinzel"),
	plaetzeSystempalettenstapelEinzel: integer("PlaetzeSystempalettenstapelEinzel"),
	plaetzeMitTrommelBelegt: integer(),
	auslastung: real("Auslastung"),
	bewegungen: integer("Bewegungen"),
	einlagerungAblKunde: integer("EinlagerungAblKunde"),
	einlagerungAblRest: integer("EinlagerungAblRest"),
	auslagerungAbl: integer("AuslagerungAbl"),
	weAtrl: integer(),
},
(table) => [
	index("ATrL_Datum_idx").on(table.datum),
]);

export const ablaengerei = sqliteTable("Ablaengerei", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	datum: text("Datum"),
	cutLagerK220: integer().default(0),
	cutLagerR220: integer().default(0),
	lagerCut220: integer().default(0),
	cutLagerK240: integer().default(0),
	cutLagerR240: integer().default(0),
	lagerCut240: integer().default(0),
	cutTt: integer().default(0),
	cutTr: integer().default(0),
	cutRr: integer().default(0),
	cutGesamt: integer().default(0),
	pickCut: integer().default(0),
	cutLager200: integer().default(0),
	cutLagerK200: integer().default(0),
	lagerCut200: integer().default(0),
},
(table) => [
	index("Ablaengerei_Datum_idx").on(table.datum),
]);

export const system = sqliteTable("System", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	datum: text("Datum"),
	nioSapAtrl: integer(),
	nioWitronAtrl: integer(),
	nioSiemensAtrl: integer(),
	nioProzessAtrl: integer(),
	nioSonstigesAtrl: integer(),
	nioSapAril: integer(),
	nioWitronAril: integer(),
	nioSiemensAril: integer(),
	nioProzessAril: integer(),
	nioSonstigesAril: integer(),
	verfuegbarkeitAnzahlStoerungenAtrl: real(),
	verfuegbarkeitAnzahlStoerungenAril: real(),
	verfuegbarkeitAtrlFtRbgMfr1: real("verfuegbarkeitAtrl_FT_RBG_MFR1"),
	verfuegbarkeitArilFtRbg: real("verfuegbarkeitAril_FT_RBG"),
	verfuegbarkeitFts: real(),
	gesamtverfuegbarkeitAtrLARiLFts: real("gesamtverfuegbarkeit_AtrL_ARiL_FTS"),
	verfuegbarkeitItm: real(),
	verfuegbarkeitSap: real(),
	verfuegbarkeitServicegrad: real(),
	weGesamtAtrlManl: integer("weGesamtAtrl_Manl"),
	waTaPosGesamtAtrlManlArilAbl: integer("waTaPosGesamt_Atrl_Manl_Aril_Abl"),
},
(table) => [
	index("System_Datum_idx").on(table.datum),
]);

export const we = sqliteTable("WE", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	datum: text("Datum"),
	weAtrl: integer().default(0),
	weManl: integer().default(0),
},
(table) => [
	index("WE_Datum_idx").on(table.datum),
]);

export const dispatchData = sqliteTable("dispatch_data", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	datum: text(),
	tag: integer(),
	monat: integer(),
	kw: integer(),
	jahr: integer(),
	servicegrad: real(),
	ausgeliefertLup: integer("ausgeliefert_lup"),
	rueckstaendig: integer(),
	produzierteTonnagen: real("produzierte_tonnagen"),
	direktverladungKiaa: integer("direktverladung_kiaa"),
	umschlag: integer(),
	kgProColli: real("kg_pro_colli"),
	elefanten: integer(),
	atrl: integer(),
	aril: integer(),
	fuellgradAril: real("fuellgrad_aril"),
	qmAngenommen: integer("qm_angenommen"),
	qmAbgelehnt: integer("qm_abgelehnt"),
	qmOffen: integer("qm_offen"),
	mitarbeiterStd: real("mitarbeiter_std"),
},
(table) => [
	index("dispatch_data_datum_idx").on(table.datum),
]);

export const systemStatus = sqliteTable("SystemStatus", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	systemName: text("system_name").notNull(),
	status: text().notNull(),
	lastCheck: numeric("last_check").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	metadata: text(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").notNull(),
});

export const prismaMigrations = sqliteTable("_prisma_migrations", {
	id: text().primaryKey().notNull(),
	checksum: text().notNull(),
	finishedAt: numeric("finished_at"),
	migrationName: text("migration_name").notNull(),
	logs: text(),
	rolledBackAt: numeric("rolled_back_at"),
	startedAt: numeric("started_at").default(sql`(current_timestamp)`).notNull(),
	appliedStepsCount: integer("applied_steps_count").default(0).notNull(),
});

export const role = sqliteTable("Role", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	name: text().notNull(),
},
(table) => [
	uniqueIndex("Role_name_key").on(table.name),
]);

export const roleToUser = sqliteTable("_RoleToUser", {
	a: integer("A").notNull(),
	b: integer("B").notNull(),
},
(table) => [
	index().on(table.b),
	uniqueIndex("_RoleToUser_AB_unique").on(table.a, table.b),
]);

export const cacheStatistics = sqliteTable("cache_statistics", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	totalEntries: integer().notNull(),
	totalSize: integer().notNull(),
	hitRate: real().notNull(),
	missRate: real().notNull(),
	evictionCount: integer().notNull(),
	averageAccessTime: real().notNull(),
	timestamp: numeric().default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("cache_statistics_timestamp_idx").on(table.timestamp),
]);

export const performanceMetrics = sqliteTable("performance_metrics", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	metricId: text().notNull(),
	type: text().notNull(),
	timestamp: numeric().default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	duration: integer().notNull(),
	success: numeric().notNull(),
	details: text().notNull(),
	createdAt: numeric().default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("performance_metrics_success_idx").on(table.success),
	index("performance_metrics_type_idx").on(table.type),
	index("performance_metrics_timestamp_idx").on(table.timestamp),
	uniqueIndex("performance_metrics_metricId_key").on(table.metricId),
]);

export const responseTimeMetrics = sqliteTable("response_time_metrics", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	metricId: text().notNull(),
	endpoint: text().notNull(),
	enriched: numeric().notNull(),
	totalTime: integer().notNull(),
	enrichmentTime: integer().notNull(),
	llmTime: integer().notNull(),
	dataSize: integer().notNull(),
	success: numeric().notNull(),
	timestamp: numeric().default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("response_time_metrics_success_idx").on(table.success),
	uniqueIndex("response_time_metrics_metricId_key").on(table.metricId),
	index("response_time_metrics_timestamp_idx").on(table.timestamp),
	index("response_time_metrics_endpoint_idx").on(table.endpoint),
	index("response_time_metrics_enriched_idx").on(table.enriched),
]);

export const intentRecognitionMetrics = sqliteTable("intent_recognition_metrics", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	metricId: text().notNull(),
	messageHash: text().notNull(),
	detectedIntents: text().notNull(),
	confidence: real().notNull(),
	accuracy: real(),
	keywords: text().notNull(),
	duration: integer().notNull(),
	timestamp: numeric().default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("intent_recognition_metrics_timestamp_idx").on(table.timestamp),
	index("intent_recognition_metrics_confidence_idx").on(table.confidence),
	uniqueIndex("intent_recognition_metrics_metricId_key").on(table.metricId),
	index("intent_recognition_metrics_accuracy_idx").on(table.accuracy),
]);

export const user = sqliteTable("User", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	email: text().notNull(),
	username: text().notNull(),
	name: text(),
	passwordHash: text().notNull(),
	createdAt: numeric().default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric().notNull(),
},
(table) => [
	uniqueIndex("User_username_key").on(table.username),
	uniqueIndex("User_email_key").on(table.email),
]);

export const workflowLogs = sqliteTable("workflow_logs", {
	id: text().primaryKey().notNull(),
	timestamp: text().notNull(),
	level: text().notNull(),
	message: text().notNull(),
	workflowId: text("workflow_id").notNull(),
	executionId: text("execution_id"),
	details: text(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("idx_workflow_logs_created_at").on(table.createdAt),
	index("idx_workflow_logs_execution_id").on(table.executionId),
	index("idx_workflow_logs_level").on(table.level),
	index("idx_workflow_logs_timestamp").on(table.timestamp),
	index("idx_workflow_logs_workflow_id").on(table.workflowId),
	index("workflow_logs_created_at_idx").on(table.createdAt),
	index("workflow_logs_execution_id_idx").on(table.executionId),
	index("workflow_logs_level_idx").on(table.level),
	index("workflow_logs_timestamp_idx").on(table.timestamp),
	index("workflow_logs_workflow_id_idx").on(table.workflowId),
]);

export const workflowExecutions = sqliteTable("workflow_executions", {
	id: text().primaryKey().notNull(),
	workflowId: text("workflow_id").notNull(),
	startTime: numeric("start_time").notNull(),
	endTime: numeric("end_time"),
	status: text().notNull(),
	durationSeconds: integer("duration_seconds"),
	exportPath: text("export_path"),
	errorMessage: text("error_message"),
	recordsProcessed: integer("records_processed"),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("idx_workflow_executions_created_at").on(table.createdAt),
	index("idx_workflow_executions_status").on(table.status),
	index("idx_workflow_executions_start_time").on(table.startTime),
	index("idx_workflow_executions_workflow_id").on(table.workflowId),
	index("workflow_executions_created_at_idx").on(table.createdAt),
	index("workflow_executions_status_idx").on(table.status),
	index("workflow_executions_start_time_idx").on(table.startTime),
	index("workflow_executions_workflow_id_idx").on(table.workflowId),
]);

export const workflowConfigs = sqliteTable("workflow_configs", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	tcode: text(),
	exportDir: text("export_dir"),
	exportBasename: text("export_basename"),
	dbTable: text("db_table"),
	enabled: numeric().default(true).notNull(),
	scheduleCron: text("schedule_cron"),
	lastRun: numeric("last_run"),
	nextRun: numeric("next_run"),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").notNull(),
},
(table) => [
	index("idx_workflow_configs_last_run").on(table.lastRun),
	index("idx_workflow_configs_next_run").on(table.nextRun),
	index("idx_workflow_configs_enabled").on(table.enabled),
	index("workflow_configs_last_run_idx").on(table.lastRun),
	index("workflow_configs_next_run_idx").on(table.nextRun),
	index("workflow_configs_enabled_idx").on(table.enabled),
]);

export const enrichmentPerformanceMetrics = sqliteTable("enrichment_performance_metrics", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	metricId: text().notNull(),
	requestId: text().notNull(),
	intentCount: integer().notNull(),
	queryCount: integer().notNull(),
	successfulQueries: integer().notNull(),
	fallbackUsed: numeric().notNull(),
	dataTypes: text().notNull(),
	duration: integer().notNull(),
	success: numeric().notNull(),
	timestamp: numeric().default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("enrichment_performance_metrics_fallbackUsed_idx").on(table.fallbackUsed),
	index("enrichment_performance_metrics_success_idx").on(table.success),
	index("enrichment_performance_metrics_timestamp_idx").on(table.timestamp),
	uniqueIndex("enrichment_performance_metrics_metricId_key").on(table.metricId),
]);

export const performanceAlerts = sqliteTable("performance_alerts", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	type: text().notNull(),
	message: text().notNull(),
	metric: text().notNull(),
	value: real().notNull(),
	threshold: real().notNull(),
	resolved: numeric().notNull(),
	resolvedAt: numeric(),
	createdAt: numeric().default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("performance_alerts_createdAt_idx").on(table.createdAt),
	index("performance_alerts_resolved_idx").on(table.resolved),
	index("performance_alerts_type_idx").on(table.type),
]);

export const queryPerformanceMetrics = sqliteTable("query_performance_metrics", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	metricId: text().notNull(),
	queryType: text().notNull(),
	duration: integer().notNull(),
	success: numeric().notNull(),
	dataSize: integer(),
	cacheHit: numeric().notNull(),
	retryCount: integer().default(0).notNull(),
	errorType: text(),
	timestamp: numeric().default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("query_performance_metrics_success_idx").on(table.success),
	index("query_performance_metrics_cacheHit_idx").on(table.cacheHit),
	index("query_performance_metrics_timestamp_idx").on(table.timestamp),
	index("query_performance_metrics_queryType_idx").on(table.queryType),
	uniqueIndex("query_performance_metrics_metricId_key").on(table.metricId),
]);

export const ariL = sqliteTable("ARiL", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	datum: text("Datum"),
	waTaPositionen: integer(),
	umlagerungen: integer("Umlagerungen"),
	belegtePlaetze: integer(),
	systemtablareRuecksackStk: integer(),
	systemtablareGesamtStk: integer(),
	systemtablareEinzelBelegt: integer(),
	belegtRinge: integer(),
	auslastung: real("Auslastung"),
	alleBewegungen: integer(),
	cuttingLagerKunde: integer(),
	cuttingLagerRest: integer(),
	lagerCutting: integer(),
},
(table) => [
	index("ARiL_Datum_idx").on(table.datum),
]);

export const bereitschaftsPersonen = sqliteTable("bereitschafts_personen", {
	id: integer().primaryKey({ autoIncrement: true }),
	name: text().notNull(),
	telefon: text().notNull(),
	email: text().notNull(),
	abteilung: text().notNull(),
	aktiv: numeric().default(1).notNull(),
	reihenfolge: integer().default(0).notNull(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("idx_bereitschafts_personen_reihenfolge").on(table.reihenfolge),
	index("idx_bereitschafts_personen_aktiv").on(table.aktiv),
]);

export const bereitschaftsKonfiguration = sqliteTable("bereitschafts_konfiguration", {
	id: integer().primaryKey({ autoIncrement: true }),
	wechselTag: integer("wechsel_tag").default(5).notNull(),
	wechselUhrzeit: text("wechsel_uhrzeit").default("08:00").notNull(),
	rotationAktiv: numeric("rotation_aktiv").default(1).notNull(),
	benachrichtigungTage: integer("benachrichtigung_tage").default(2).notNull(),
	emailBenachrichtigung: numeric("email_benachrichtigung").default(1).notNull(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
});

export const bestand200 = sqliteTable("bestand200", {
	id: integer().primaryKey({ autoIncrement: true }),
	lagertyp: text("Lagertyp"),
	lagerplatz: text("Lagerplatz"),
	material: text("Material"),
	charge: text("Charge"),
	dauer: real("Dauer"),
	lagerbereich: text("Lagerbereich"),
	lagerplatztyp: text("Lagerplatztyp"),
	lagerplatzaufteilung: text("Lagerplatzaufteilung"),
	auslagerungssperre: text("Auslagerungssperre"),
	einlagerungssperre: text("Einlagerungssperre"),
	sperrgrund: text("Sperrgrund"),
	letzteBewegung: text("Letzte_Bewegung"),
	uhrzeit: text("Uhrzeit"),
	taNummer: text("TA_Nummer"),
	taPosition: text("TA_Position"),
	"letzterÄnderer": text("Letzter_änderer"),
	"letzteÄnderung": text("Letzte_änderung"),
	wareneingangsdatum: text("Wareneingangsdatum"),
	weNummer: text("WE_Nummer"),
	wePosition: text("WE_Position"),
	lieferung: text("Lieferung"),
	position: text("Position"),
	lagereinheitentyp: text("Lagereinheitentyp"),
	gesamtbestand: real("Gesamtbestand"),
	lagereinheit: text("Lagereinheit"),
	aufnahmeDatum: text(),
	aufnahmeZeit: text(),
	maxPlaetze: text(),
	auslastung: text(),
	maxA: text(),
	maxB: text(),
	maxC: text(),
	auslastungA: text(),
	auslastungB: text(),
	auslastungC: text(),
	importTimestamp: numeric("import_timestamp").default(sql`(CURRENT_TIMESTAMP)`),
},
(table) => [
	uniqueIndex("ux_bestand200_Lagerplatz_Material_Charge_TA_Nummer_WE_Nummer_WE_Position_aufnahmeDatum_aufnahmeZeit").on(table.lagerplatz, table.material, table.charge, table.taNummer, table.weNummer, table.wePosition, table.aufnahmeDatum, table.aufnahmeZeit),
	index("bestand200_import_timestamp_idx").on(table.importTimestamp),
	index("bestand200_aufnahmeDatum_idx").on(table.aufnahmeDatum),
]);

export const bestand240 = sqliteTable("bestand240", {
	id: integer().primaryKey({ autoIncrement: true }),
	lagertyp: text("Lagertyp"),
	lagerplatz: text("Lagerplatz"),
	material: text("Material"),
	charge: text("Charge"),
	dauer: real("Dauer"),
	lagerbereich: text("Lagerbereich"),
	lagerplatztyp: text("Lagerplatztyp"),
	lagerplatzaufteilung: text("Lagerplatzaufteilung"),
	auslagerungssperre: text("Auslagerungssperre"),
	einlagerungssperre: text("Einlagerungssperre"),
	sperrgrund: text("Sperrgrund"),
	letzteBewegung: text("Letzte_Bewegung"),
	uhrzeit: text("Uhrzeit"),
	taNummer: text("TA_Nummer"),
	taPosition: text("TA_Position"),
	"letzterÄnderer": text("Letzter_Änderer"),
	"letzteÄnderung": text("Letzte_Änderung"),
	wareneingangsdatum: text("Wareneingangsdatum"),
	weNummer: text("WE_Nummer"),
	wePosition: text("WE_Position"),
	lieferung: text("Lieferung"),
	position: text("Position"),
	lagereinheitentyp: text("Lagereinheitentyp"),
	gesamtbestand: real("Gesamtbestand"),
	lagereinheit: text("Lagereinheit"),
	aufnahmeDatum: text(),
	aufnahmeZeit: text(),
	maxPlaetze: text(),
	auslastung: text(),
	maxA: text(),
	maxB: text(),
	maxC: text(),
	auslastungA: text(),
	auslastungB: text(),
	auslastungC: text(),
	importTimestamp: numeric("import_timestamp").default(sql`(CURRENT_TIMESTAMP)`),
},
(table) => [
	uniqueIndex("ux_bestand240_Lagerplatz_Material_Charge_TA-Nummer_WE-Nummer_WE-Position_aufnahmeDatum_aufnahmeZeit").on(table.lagerplatz, table.material, table.charge, table.taNummer, table.weNummer, table.wePosition, table.aufnahmeDatum, table.aufnahmeZeit),
	uniqueIndex("ux_bestand240_Lagerplatz_Material_Charge_TA_Nummer_WE_Nummer_WE_Position_aufnahmeDatum_aufnahmeZeit").on(table.lagerplatz, table.material, table.charge, table.taNummer, table.weNummer, table.wePosition, table.aufnahmeDatum, table.aufnahmeZeit),
	index("bestand240_import_timestamp_idx").on(table.importTimestamp),
	index("bestand240_aufnahmeDatum_idx").on(table.aufnahmeDatum),
]);

export const materialdaten = sqliteTable("materialdaten", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	matnr: text().notNull(),
	materialkurztext: text(),
	kabeldurchmesser: real(),
	zuschlagKabeldurchmesser: real(),
	biegefaktor: real(),
	ringauslieferung: text(),
	kleinsterErlauberFreiraum: real(),
	bruttogewicht: real(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("materialdaten_matnr_idx").on(table.matnr),
	uniqueIndex("materialdaten_matnr_key").on(table.matnr),
]);

export const trommeldaten = sqliteTable("trommeldaten", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	trommeldaten: text().notNull(),
	aussendurchmesser: integer(),
	kerndurchmesser: integer(),
	freiraumMm: integer("freiraum_mm"),
	wickelbreiteMm: integer("wickelbreite_mm"),
	maxTragkraftKg: integer("maxTragkraft_Kg"),
	maxLaenge: integer("max_Laenge"),
	maxGewicht: integer("max_Gewicht"),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("trommeldaten_trommeldaten_idx").on(table.trommeldaten),
]);

export const bereitschaftsAusnahmen = sqliteTable("bereitschafts_ausnahmen", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	personId: integer("person_id").notNull(),
	von: numeric().notNull(),
	bis: numeric().notNull(),
	grund: text().notNull(),
	ersatzPersonId: integer("ersatz_person_id"),
	aktiv: numeric().default(true).notNull(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("idx_bereitschafts_ausnahmen_person_id").on(table.personId),
	index("idx_bereitschafts_ausnahmen_von_bis").on(table.von, table.bis),
	index("idx_bereitschafts_ausnahmen_aktiv").on(table.aktiv),
]);

export const bereitschaftsWochen = sqliteTable("bereitschafts_wochen", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	personId: integer("person_id").notNull(),
	wochenStart: numeric("wochen_start").notNull(),
	wochenEnde: numeric("wochen_ende").notNull(),
	von: numeric().notNull(),
	bis: numeric().notNull(),
	aktiv: numeric().default(true).notNull(),
	notiz: text(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("idx_bereitschafts_wochen_person_id").on(table.personId),
	index("idx_bereitschafts_wochen_wochen_start").on(table.wochenStart),
	index("idx_bereitschafts_wochen_aktiv").on(table.aktiv),
	index("idx_bereitschafts_wochen_von_bis").on(table.von, table.bis),
]);

export const stoerungsAttachment = sqliteTable("StoerungsAttachment", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	stoerungId: integer("stoerung_id").notNull(),
	filename: text().notNull(),
	storedName: text("stored_name").notNull(),
	filePath: text("file_path").notNull(),
	fileSize: integer("file_size").notNull(),
	mimeType: text("mime_type").notNull(),
	fileType: text("file_type").notNull(),
	uploadedBy: text("uploaded_by"),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").notNull(),
},
(table) => [
	index("StoerungsAttachment_created_at_idx").on(table.createdAt),
	index("StoerungsAttachment_file_type_idx").on(table.fileType),
	index("StoerungsAttachment_stoerung_id_idx").on(table.stoerungId),
]);

export const systemStatusMessage = sqliteTable("SystemStatusMessage", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	category: text().notNull(),
	status: text().notNull(),
	title: text().notNull(),
	description: text().notNull(),
	priority: integer().default(1).notNull(),
	isActive: integer("is_active").default(1).notNull(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	systemStatusId: integer("system_status_id"),
},
(table) => [
	index("SystemStatusMessage_system_status_id_idx").on(table.systemStatusId),
	index("SystemStatusMessage_category_status_idx").on(table.category, table.status),
]);

export const runbook = sqliteTable("Runbook", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	title: text().notNull(),
	content: text().notNull(),
	affectedSystems: text("affected_systems"),
	tags: text(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").notNull(),
},
(table) => [
	index("Runbook_updated_at_idx").on(table.updatedAt),
	index("Runbook_created_at_idx").on(table.createdAt),
]);

