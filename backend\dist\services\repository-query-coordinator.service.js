"use strict";
/**
 * Repository Query Coordinator Service
 *
 * Coordinates queries across multiple repositories based on detected intent
 * and provides data aggregation and formatting for LLM consumption.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RepositoryQueryCoordinator = void 0;
const stoerungen_repository_1 = require("../repositories/stoerungen.repository");
const dispatch_repository_1 = require("../repositories/dispatch.repository");
const cutting_repository_1 = require("../repositories/cutting.repository");
const stoerungen_data_integration_service_1 = require("./stoerungen-data-integration.service");
const performance_monitoring_service_1 = __importDefault(require("./performance-monitoring.service"));
const data_cache_service_1 = __importDefault(require("./data-cache.service"));
/**
 * Default configuration for the query coordinator
 */
const DEFAULT_CONFIG = {
    maxConcurrentQueries: 3,
    queryTimeout: 5000, // 5 seconds
    retryAttempts: 2,
    retryDelay: 1000, // 1 second
    enableMetrics: true,
    enableCaching: true
};
class RepositoryQueryCoordinator {
    constructor(prisma, config = {}) {
        this.stoerungenRepo = stoerungen_repository_1.StoerungenRepository.getInstance(prisma);
        this.stoerungenIntegration = new stoerungen_data_integration_service_1.StoerungenDataIntegrationService(prisma);
        this.dispatchRepo = new dispatch_repository_1.DispatchRepositoryImpl(prisma);
        this.cuttingRepo = new cutting_repository_1.CuttingRepositoryImpl(prisma);
        this.config = { ...DEFAULT_CONFIG, ...config };
        this.metrics = {
            totalQueries: 0,
            successfulQueries: 0,
            failedQueries: 0,
            averageQueryTime: 0,
            cacheHitRate: 0,
            lastExecutionTime: new Date()
        };
    }
    /**
     * Execute queries across multiple repositories based on detected intents
     */
    async executeQueries(intents, options = {}) {
        const startTime = Date.now();
        try {
            console.log(`🔄 [QUERY-COORDINATOR] Executing ${intents.length} queries`);
            // Check cache first if caching is enabled
            if (this.config.enableCaching) {
                const cachedResults = await data_cache_service_1.default.getCachedQueryResults(intents);
                if (cachedResults) {
                    console.log(`🎯 [QUERY-COORDINATOR] Using cached query results`);
                    // Record cache hit performance for each result
                    cachedResults.forEach(result => {
                        performance_monitoring_service_1.default.recordQueryPerformance(result.dataType, result.queryTime || 0, result.success, { cacheHit: true, dataSize: this.estimateResultSize(result) });
                    });
                    // Update metrics
                    this.updateMetrics(startTime, cachedResults, true);
                    return cachedResults;
                }
            }
            // Group intents by repository type to optimize execution
            const groupedIntents = this.groupIntentsByRepository(intents);
            // Create query promises for parallel execution
            const queryPromises = this.createQueryPromises(groupedIntents, options);
            // Execute queries with timeout and error handling
            const results = await this.executeQueriesWithTimeout(queryPromises);
            // Cache results if caching is enabled and we have successful results
            if (this.config.enableCaching && results.some(r => r.success)) {
                await data_cache_service_1.default.cacheQueryResults(intents, results);
            }
            // Record performance metrics for each result
            results.forEach(result => {
                performance_monitoring_service_1.default.recordQueryPerformance(result.dataType, result.queryTime || 0, result.success, {
                    cacheHit: false,
                    dataSize: this.estimateResultSize(result),
                    retryCount: result.retryAttempts || 0,
                    errorType: result.error ? this.categorizeError(result.error) : undefined
                });
            });
            // Update metrics
            this.updateMetrics(startTime, results, false);
            console.log(`✅ [QUERY-COORDINATOR] Completed ${results.length} queries in ${Date.now() - startTime}ms`);
            return results;
        }
        catch (error) {
            console.error('❌ [QUERY-COORDINATOR] Error executing queries:', error);
            this.metrics.failedQueries++;
            // Record failed query performance
            intents.forEach(intent => {
                performance_monitoring_service_1.default.recordQueryPerformance(intent.type === 'general' ? 'combined' : intent.type, Date.now() - startTime, false, {
                    cacheHit: false,
                    errorType: this.categorizeError(error instanceof Error ? error.message : 'Unknown error')
                });
            });
            throw error;
        }
    }
    /**
     * Execute queries for Störungen data using specialized integration service
     */
    async queryStoerungenData(intent) {
        const startTime = Date.now();
        const queryId = `stoerungen_${Date.now()}`;
        try {
            console.log(`🔍 [QUERY-COORDINATOR] [${queryId}] Querying Störungen data with enhanced integration`);
            // Check if repository is available
            if (!this.stoerungenRepo || !this.stoerungenIntegration) {
                throw new Error('Störungen repository or integration service not available');
            }
            // Prepare query options based on intent
            const queryOptions = {
                timeRange: intent.timeRange,
                includeTrends: true,
                includeSystemStatus: true,
                includeRecentIncidents: true,
                maxIncidents: 10,
                severityFilter: this.extractSeverityFilter(intent),
                statusFilter: this.extractStatusFilter(intent)
            };
            // Use the specialized Störungen integration service with timeout
            const stoerungenData = await Promise.race([
                this.stoerungenIntegration.getStoerungenDataForAI(queryOptions),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Störungen query timeout')), this.config.queryTimeout * 0.8))
            ]);
            const queryTime = Date.now() - startTime;
            console.log(`✅ [QUERY-COORDINATOR] [${queryId}] Störungen data retrieved in ${queryTime}ms`);
            return {
                dataType: 'stoerungen',
                data: {
                    statistics: stoerungenData.statistics,
                    systemStatus: stoerungenData.systemStatus,
                    recentIncidents: stoerungenData.recentIncidents,
                    trends: stoerungenData.trends
                },
                summary: stoerungenData.summary,
                timestamp: new Date(),
                success: true,
                queryTime,
                intent: intent.type
            };
        }
        catch (error) {
            const queryTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error(`❌ [QUERY-COORDINATOR] [${queryId}] Error querying Störungen data (${queryTime}ms):`, errorMessage);
            // Attempt fallback to basic repository if integration service fails
            if (!errorMessage.includes('timeout') && !errorMessage.includes('not available')) {
                try {
                    console.log(`🔄 [QUERY-COORDINATOR] [${queryId}] Attempting fallback to basic Störungen repository`);
                    const basicData = await this.getBasicStoerungenData(intent);
                    return {
                        dataType: 'stoerungen',
                        data: basicData,
                        summary: 'Störungsdaten (Basis-Modus): Eingeschränkte Informationen verfügbar',
                        timestamp: new Date(),
                        success: true,
                        queryTime: Date.now() - startTime,
                        intent: intent.type,
                        fallback: true
                    };
                }
                catch (fallbackError) {
                    console.error(`❌ [QUERY-COORDINATOR] [${queryId}] Fallback also failed:`, fallbackError);
                }
            }
            return {
                dataType: 'stoerungen',
                data: this.getEmptyStoerungenData(),
                summary: this.getStoerungenErrorSummary(errorMessage),
                timestamp: new Date(),
                success: false,
                error: errorMessage,
                queryTime,
                intent: intent.type,
                fallback: true
            };
        }
    }
    /**
     * Execute queries for Dispatch data
     */
    async queryDispatchData(intent) {
        const startTime = Date.now();
        const queryId = `dispatch_${Date.now()}`;
        try {
            console.log(`🔍 [QUERY-COORDINATOR] [${queryId}] Querying Dispatch data`);
            // Check if repository is available
            if (!this.dispatchRepo) {
                throw new Error('Dispatch repository not available');
            }
            const dateRange = intent.timeRange || this.getDefaultDateRange();
            // Execute queries with individual timeouts and graceful degradation
            const queryPromises = [
                this.executeWithTimeout(() => this.dispatchRepo.getServiceLevelData(dateRange), 'ServiceLevel'),
                this.executeWithTimeout(() => this.dispatchRepo.getDailyPerformanceData(dateRange), 'Performance'),
                this.executeWithTimeout(() => this.dispatchRepo.getPickingData(dateRange), 'Picking'),
                this.executeWithTimeout(() => this.dispatchRepo.getReturnsData(), 'Returns'),
                this.executeWithTimeout(() => this.dispatchRepo.getPerformanceMetrics(dateRange), 'Metrics')
            ];
            const results = await Promise.allSettled(queryPromises);
            // Extract successful results and handle partial failures
            const serviceLevel = results[0].status === 'fulfilled' ? results[0].value : [];
            const performance = results[1].status === 'fulfilled' ? results[1].value : [];
            const picking = results[2].status === 'fulfilled' ? results[2].value : [];
            const returns = results[3].status === 'fulfilled' ? results[3].value : [];
            const metrics = results[4].status === 'fulfilled' ? results[4].value : { totalDeliveries: 0, averagePickingRate: 0, averageServiceLevel: 0, totalTonnage: 0 };
            // Check if we have any successful data
            const hasAnyData = results.some(r => r.status === 'fulfilled');
            const failedQueries = results.filter(r => r.status === 'rejected').length;
            if (failedQueries > 0) {
                console.warn(`⚠️ [QUERY-COORDINATOR] [${queryId}] ${failedQueries}/5 Dispatch queries failed`);
            }
            const summary = hasAnyData
                ? this.formatDispatchSummary(serviceLevel, performance, picking, returns, metrics, failedQueries > 0)
                : 'Alle Versanddaten-Abfragen fehlgeschlagen';
            const queryTime = Date.now() - startTime;
            console.log(`✅ [QUERY-COORDINATOR] [${queryId}] Dispatch data retrieved in ${queryTime}ms (${5 - failedQueries}/5 successful)`);
            return {
                dataType: 'dispatch',
                data: { serviceLevel, performance, picking, returns, metrics },
                summary,
                timestamp: new Date(),
                success: hasAnyData,
                queryTime,
                intent: intent.type,
                partialFailure: failedQueries > 0
            };
        }
        catch (error) {
            const queryTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error(`❌ [QUERY-COORDINATOR] [${queryId}] Error querying Dispatch data (${queryTime}ms):`, errorMessage);
            return {
                dataType: 'dispatch',
                data: this.getEmptyDispatchData(),
                summary: this.getDispatchErrorSummary(errorMessage),
                timestamp: new Date(),
                success: false,
                error: errorMessage,
                queryTime,
                intent: intent.type,
                fallback: true
            };
        }
    }
    /**
     * Execute queries for Cutting data
     */
    async queryCuttingData(intent) {
        const startTime = Date.now();
        const queryId = `cutting_${Date.now()}`;
        try {
            console.log(`🔍 [QUERY-COORDINATOR] [${queryId}] Querying Cutting data`);
            // Check if repository is available
            if (!this.cuttingRepo) {
                throw new Error('Cutting repository not available');
            }
            const dateRange = intent.timeRange;
            // Execute queries with individual timeouts and graceful degradation
            const queryPromises = [
                this.executeWithTimeout(() => this.cuttingRepo.getCuttingChartData(dateRange), 'CuttingChart'),
                this.executeWithTimeout(() => this.cuttingRepo.getMaschinenEfficiency(dateRange), 'Efficiency'),
                this.executeWithTimeout(() => this.cuttingRepo.getCuttingPerformanceOverview(dateRange), 'Overview'),
                this.executeWithTimeout(() => this.cuttingRepo.getTopPerformingMachines(5), 'TopMachines')
            ];
            const results = await Promise.allSettled(queryPromises);
            // Extract successful results and handle partial failures
            const cuttingData = results[0].status === 'fulfilled' ? results[0].value : [];
            const efficiency = results[1].status === 'fulfilled' ? results[1].value : [];
            const overview = results[2].status === 'fulfilled' ? results[2].value : { totalCuts: { cutTT: 0, cutTR: 0, cutRR: 0, pickCut: 0 }, averageEfficiency: 0 };
            const topMachines = results[3].status === 'fulfilled' ? results[3].value : [];
            // Check if we have any successful data
            const hasAnyData = results.some(r => r.status === 'fulfilled');
            const failedQueries = results.filter(r => r.status === 'rejected').length;
            if (failedQueries > 0) {
                console.warn(`⚠️ [QUERY-COORDINATOR] [${queryId}] ${failedQueries}/4 Cutting queries failed`);
            }
            const summary = hasAnyData
                ? this.formatCuttingSummary(cuttingData, efficiency, overview, topMachines, failedQueries > 0)
                : 'Alle Schnittdaten-Abfragen fehlgeschlagen';
            const queryTime = Date.now() - startTime;
            console.log(`✅ [QUERY-COORDINATOR] [${queryId}] Cutting data retrieved in ${queryTime}ms (${4 - failedQueries}/4 successful)`);
            return {
                dataType: 'cutting',
                data: { cuttingData, efficiency, overview, topMachines },
                summary,
                timestamp: new Date(),
                success: hasAnyData,
                queryTime,
                intent: intent.type,
                partialFailure: failedQueries > 0
            };
        }
        catch (error) {
            const queryTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error(`❌ [QUERY-COORDINATOR] [${queryId}] Error querying Cutting data (${queryTime}ms):`, errorMessage);
            return {
                dataType: 'cutting',
                data: this.getEmptyCuttingData(),
                summary: this.getCuttingErrorSummary(errorMessage),
                timestamp: new Date(),
                success: false,
                error: errorMessage,
                queryTime,
                intent: intent.type,
                fallback: true
            };
        }
    }
    /**
     * Execute a query with timeout
     */
    async executeWithTimeout(queryFn, queryName, timeoutMs = this.config.queryTimeout * 0.7) {
        return Promise.race([
            queryFn(),
            new Promise((_, reject) => setTimeout(() => reject(new Error(`${queryName} query timeout after ${timeoutMs}ms`)), timeoutMs))
        ]);
    }
    /**
     * Aggregate and format query results for LLM consumption
     */
    aggregateQueryResults(results) {
        var _a, _b, _c;
        const successfulResults = results.filter(r => r.success);
        const failedResults = results.filter(r => !r.success);
        const aggregatedData = {
            stoerungen: ((_a = successfulResults.find(r => r.dataType === 'stoerungen')) === null || _a === void 0 ? void 0 : _a.data) || null,
            dispatch: ((_b = successfulResults.find(r => r.dataType === 'dispatch')) === null || _b === void 0 ? void 0 : _b.data) || null,
            cutting: ((_c = successfulResults.find(r => r.dataType === 'cutting')) === null || _c === void 0 ? void 0 : _c.data) || null
        };
        const combinedSummary = this.createCombinedSummary(successfulResults);
        return {
            results: successfulResults,
            aggregatedData,
            summary: combinedSummary,
            totalQueries: results.length,
            successfulQueries: successfulResults.length,
            failedQueries: failedResults.length,
            errors: failedResults.map(r => r.error).filter(Boolean),
            timestamp: new Date(),
            queryMetrics: {
                totalTime: results.reduce((sum, r) => sum + (r.queryTime || 0), 0),
                averageTime: results.length > 0 ?
                    results.reduce((sum, r) => sum + (r.queryTime || 0), 0) / results.length : 0,
                dataTypes: [...new Set(successfulResults.map(r => r.dataType))]
            }
        };
    }
    /**
     * Get query execution metrics
     */
    getMetrics() {
        return { ...this.metrics };
    }
    /**
     * Reset metrics
     */
    resetMetrics() {
        this.metrics = {
            totalQueries: 0,
            successfulQueries: 0,
            failedQueries: 0,
            averageQueryTime: 0,
            cacheHitRate: 0,
            lastExecutionTime: new Date()
        };
    }
    // Private helper methods
    groupIntentsByRepository(intents) {
        const grouped = new Map();
        intents.forEach(intent => {
            const repoType = intent.type === 'general' ? 'all' : intent.type;
            if (repoType === 'all') {
                // For general intents, add to all repository types
                ['stoerungen', 'dispatch', 'cutting'].forEach(type => {
                    if (!grouped.has(type))
                        grouped.set(type, []);
                    grouped.get(type).push({ ...intent, type: type });
                });
            }
            else {
                if (!grouped.has(repoType))
                    grouped.set(repoType, []);
                grouped.get(repoType).push(intent);
            }
        });
        return grouped;
    }
    createQueryPromises(groupedIntents, options) {
        const promises = [];
        groupedIntents.forEach((intents, repoType) => {
            // Use the first intent for each repository type (they should be similar)
            const intent = intents[0];
            switch (repoType) {
                case 'stoerungen':
                    promises.push(this.queryStoerungenData(intent));
                    break;
                case 'dispatch':
                    promises.push(this.queryDispatchData(intent));
                    break;
                case 'cutting':
                    promises.push(this.queryCuttingData(intent));
                    break;
            }
        });
        return promises;
    }
    async executeQueriesWithTimeout(queryPromises) {
        const results = [];
        // Execute queries with individual timeouts and retry logic
        const timeoutPromises = queryPromises.map((promise, index) => this.executeQueryWithRetry(promise, index));
        const settledResults = await Promise.allSettled(timeoutPromises);
        settledResults.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                results.push(result.value);
            }
            else {
                console.error(`❌ [QUERY-COORDINATOR] Query ${index} failed after retries:`, result.reason);
                // Create fallback result based on query type
                const fallbackResult = this.createFallbackQueryResult(index, result.reason);
                results.push(fallbackResult);
            }
        });
        return results;
    }
    /**
     * Execute a single query with retry logic and timeout
     */
    async executeQueryWithRetry(queryPromise, queryIndex) {
        let lastError;
        for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
            try {
                console.log(`🔄 [QUERY-COORDINATOR] Executing query ${queryIndex}, attempt ${attempt}/${this.config.retryAttempts}`);
                // Create timeout promise
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error(`Query timeout after ${this.config.queryTimeout}ms`)), this.config.queryTimeout);
                });
                // Race between query and timeout
                const result = await Promise.race([queryPromise, timeoutPromise]);
                console.log(`✅ [QUERY-COORDINATOR] Query ${queryIndex} succeeded on attempt ${attempt}`);
                return result;
            }
            catch (error) {
                lastError = error;
                console.warn(`⚠️ [QUERY-COORDINATOR] Query ${queryIndex} failed on attempt ${attempt}:`, (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error');
                // Don't retry on the last attempt
                if (attempt < this.config.retryAttempts) {
                    console.log(`🔄 [QUERY-COORDINATOR] Retrying query ${queryIndex} in ${this.config.retryDelay}ms...`);
                    await this.delay(this.config.retryDelay);
                }
            }
        }
        // All attempts failed
        throw lastError;
    }
    /**
     * Create fallback query result when all attempts fail
     */
    createFallbackQueryResult(queryIndex, error) {
        const errorMessage = (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error';
        const isTimeout = errorMessage.includes('timeout');
        const isNetworkError = errorMessage.includes('network') || errorMessage.includes('connection');
        let fallbackSummary = 'Daten momentan nicht verfügbar';
        let fallbackData = null;
        if (isTimeout) {
            fallbackSummary = 'Datenabfrage dauerte zu lange - System ist überlastet';
        }
        else if (isNetworkError) {
            fallbackSummary = 'Netzwerkverbindung zur Datenbank unterbrochen';
        }
        else {
            fallbackSummary = 'Datenbankfehler aufgetreten - Service wird wiederhergestellt';
        }
        return {
            dataType: `query_${queryIndex}`,
            data: fallbackData,
            summary: fallbackSummary,
            timestamp: new Date(),
            success: false,
            error: errorMessage,
            queryTime: this.config.queryTimeout,
            intent: 'unknown',
            fallback: true,
            retryAttempts: this.config.retryAttempts
        };
    }
    /**
     * Utility method for delays
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    async getRecentIncidents(timeRange) {
        try {
            const options = {
                limit: 10,
                offset: 0
            };
            // If timeRange is provided, we could filter by it, but the current repository
            // doesn't support date filtering in getStoerungen method
            return await this.stoerungenRepo.getStoerungen(options);
        }
        catch (error) {
            console.error('Error fetching recent incidents:', error);
            return [];
        }
    }
    getDefaultDateRange() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 7); // Last 7 days
        return {
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0]
        };
    }
    extractSeverityFilter(intent) {
        const severityKeywords = ['critical', 'kritisch', 'high', 'hoch', 'medium', 'mittel', 'low', 'niedrig'];
        const filters = [];
        intent.keywords.forEach(keyword => {
            const lowerKeyword = keyword.toLowerCase();
            if (lowerKeyword.includes('critical') || lowerKeyword.includes('kritisch')) {
                filters.push('CRITICAL');
            }
            else if (lowerKeyword.includes('high') || lowerKeyword.includes('hoch')) {
                filters.push('HIGH');
            }
            else if (lowerKeyword.includes('medium') || lowerKeyword.includes('mittel')) {
                filters.push('MEDIUM');
            }
            else if (lowerKeyword.includes('low') || lowerKeyword.includes('niedrig')) {
                filters.push('LOW');
            }
        });
        return [...new Set(filters)]; // Remove duplicates
    }
    extractStatusFilter(intent) {
        const filters = [];
        intent.keywords.forEach(keyword => {
            const lowerKeyword = keyword.toLowerCase();
            if (lowerKeyword.includes('active') || lowerKeyword.includes('aktiv')) {
                filters.push('NEW', 'IN_PROGRESS');
            }
            else if (lowerKeyword.includes('resolved') || lowerKeyword.includes('gelöst') || lowerKeyword.includes('behoben')) {
                filters.push('RESOLVED');
            }
            else if (lowerKeyword.includes('new') || lowerKeyword.includes('neu')) {
                filters.push('NEW');
            }
            else if (lowerKeyword.includes('progress') || lowerKeyword.includes('bearbeitung')) {
                filters.push('IN_PROGRESS');
            }
        });
        return [...new Set(filters)]; // Remove duplicates
    }
    formatStoerungenSummary(stats, systemStatus, recentIncidents) {
        const criticalSystems = systemStatus.filter(s => s.status === 'ERROR').length;
        const warningSystems = systemStatus.filter(s => s.status === 'WARNING').length;
        return `Störungen: ${stats.total} gesamt (${stats.active} aktiv, ${stats.resolved} gelöst). ` +
            `Schweregrade: ${stats.critical} kritisch, ${stats.high} hoch, ${stats.medium} mittel, ${stats.low} niedrig. ` +
            `MTTR: ${stats.avg_mttr_minutes} Min. Systemstatus: ${criticalSystems} Fehler, ${warningSystems} Warnungen. ` +
            `${recentIncidents.length} aktuelle Vorfälle.`;
    }
    formatDispatchSummary(serviceLevel, performance, picking, returns, metrics, hasPartialFailure = false) {
        const avgServiceLevel = serviceLevel.length > 0
            ? serviceLevel.reduce((sum, item) => sum + item.servicegrad, 0) / serviceLevel.length
            : 0;
        const totalTonnage = performance.reduce((sum, item) => sum + item.value, 0);
        const avgATRL = picking.length > 0
            ? picking.reduce((sum, item) => sum + item.atrl, 0) / picking.length
            : 0;
        const totalReturns = returns.reduce((sum, item) => sum + item.value, 0);
        let summary = `Versand: Servicegrad ${(avgServiceLevel * 100).toFixed(1)}%, Tonnage ${totalTonnage.toFixed(0)}t, ` +
            `ATRL ${avgATRL.toFixed(1)}, Retouren ${totalReturns}. ` +
            `Performance-Metriken: ${metrics.totalDeliveries} Lieferungen, ${metrics.averagePickingRate.toFixed(1)} Kommissionierungsrate.`;
        if (hasPartialFailure) {
            summary += ' (Einige Daten eingeschränkt verfügbar)';
        }
        return summary;
    }
    formatCuttingSummary(cuttingData, efficiency, overview, topMachines, hasPartialFailure = false) {
        const totalCuts = cuttingData.reduce((sum, item) => sum + item.cutTT + item.cutTR + item.cutRR + item.pickCut, 0);
        const avgEfficiency = efficiency.length > 0
            ? efficiency.reduce((sum, item) => sum + item.effizienzProzent, 0) / efficiency.length
            : 0;
        const bestMachine = topMachines.length > 0 ? topMachines[0] : null;
        let summary = `Ablängerei: ${totalCuts} Gesamtschnitte, Effizienz ${avgEfficiency.toFixed(1)}%, ` +
            `${efficiency.length} Maschinen aktiv. ` +
            `Beste Maschine: ${bestMachine ? `${bestMachine.name} (${bestMachine.averageEfficiency.toFixed(1)}%)` : 'N/A'}. ` +
            `Übersicht: ${overview.totalCuts.cutTT + overview.totalCuts.cutTR + overview.totalCuts.cutRR + overview.totalCuts.pickCut} Schnitte.`;
        if (hasPartialFailure) {
            summary += ' (Einige Maschinendaten nicht verfügbar)';
        }
        return summary;
    }
    createCombinedSummary(results) {
        if (results.length === 0) {
            return 'Keine Daten verfügbar.';
        }
        const summaries = results.map(r => r.summary).filter(Boolean);
        let combined = `SYSTEMÜBERSICHT (${new Date().toLocaleString('de-DE')}):\n\n`;
        results.forEach(result => {
            const title = result.dataType.toUpperCase();
            combined += `${title}:\n${result.summary}\n\n`;
        });
        // Add cross-system insights
        if (results.length > 1) {
            combined += 'SYSTEMÜBERGREIFENDE ANALYSE:\n';
            combined += this.generateCrossSystemInsights(results);
        }
        return combined;
    }
    generateCrossSystemInsights(results) {
        var _a, _b, _c, _d, _e, _f, _g, _h;
        const insights = [];
        const stoerungenResult = results.find(r => r.dataType === 'stoerungen');
        const dispatchResult = results.find(r => r.dataType === 'dispatch');
        const cuttingResult = results.find(r => r.dataType === 'cutting');
        // Analyze correlations between systems
        if ((stoerungenResult === null || stoerungenResult === void 0 ? void 0 : stoerungenResult.success) && (dispatchResult === null || dispatchResult === void 0 ? void 0 : dispatchResult.success)) {
            const activeIncidents = ((_b = (_a = stoerungenResult.data) === null || _a === void 0 ? void 0 : _a.stats) === null || _b === void 0 ? void 0 : _b.active) || 0;
            const serviceLevel = ((_d = (_c = dispatchResult.data) === null || _c === void 0 ? void 0 : _c.metrics) === null || _d === void 0 ? void 0 : _d.averageServiceLevel) || 0;
            if (activeIncidents > 5 && serviceLevel < 90) {
                insights.push('Hohe Störungsanzahl könnte Servicegrad beeinträchtigen');
            }
        }
        if ((cuttingResult === null || cuttingResult === void 0 ? void 0 : cuttingResult.success) && (dispatchResult === null || dispatchResult === void 0 ? void 0 : dispatchResult.success)) {
            const cuttingEfficiency = ((_f = (_e = cuttingResult.data) === null || _e === void 0 ? void 0 : _e.overview) === null || _f === void 0 ? void 0 : _f.averageEfficiency) || 0;
            const tonnage = ((_h = (_g = dispatchResult.data) === null || _g === void 0 ? void 0 : _g.metrics) === null || _h === void 0 ? void 0 : _h.totalTonnage) || 0;
            if (cuttingEfficiency > 100 && tonnage > 1000) {
                insights.push('Hohe Schnitt-Effizienz unterstützt starke Versandleistung');
            }
        }
        if (insights.length === 0) {
            insights.push('Alle Systeme arbeiten im normalen Bereich');
        }
        return insights.join('. ') + '.';
    }
    /**
     * Get basic Störungen data as fallback
     */
    async getBasicStoerungenData(intent) {
        try {
            const recentIncidents = await this.stoerungenRepo.getStoerungen({ limit: 5, offset: 0 });
            return {
                statistics: {
                    total: recentIncidents.length,
                    active: recentIncidents.filter((s) => s.status !== 'RESOLVED').length,
                    resolved: recentIncidents.filter((s) => s.status === 'RESOLVED').length
                },
                systemStatus: [],
                recentIncidents: recentIncidents.slice(0, 3),
                trends: null,
                message: 'Basis-Störungsdaten verfügbar'
            };
        }
        catch (error) {
            throw new Error('Basic Störungen data also unavailable');
        }
    }
    /**
     * Get empty Störungen data structure
     */
    getEmptyStoerungenData() {
        return {
            statistics: { total: 0, active: 0, resolved: 0, critical: 0, high: 0, medium: 0, low: 0 },
            systemStatus: [],
            recentIncidents: [],
            trends: null,
            message: 'Störungsdaten momentan nicht verfügbar'
        };
    }
    /**
     * Get error summary for Störungen failures
     */
    getStoerungenErrorSummary(errorMessage) {
        if (errorMessage.includes('timeout')) {
            return 'Störungsdaten-Abfrage dauerte zu lange. Das System ist möglicherweise überlastet.';
        }
        else if (errorMessage.includes('not available') || errorMessage.includes('connection')) {
            return 'Störungsdatenbank ist momentan nicht erreichbar. Service wird wiederhergestellt.';
        }
        else {
            return 'Fehler beim Abrufen der Störungsdaten. Bitte versuchen Sie es in wenigen Minuten erneut.';
        }
    }
    /**
     * Get empty Dispatch data structure
     */
    getEmptyDispatchData() {
        return {
            serviceLevel: [],
            performance: [],
            picking: [],
            returns: [],
            metrics: { totalDeliveries: 0, averagePickingRate: 0, averageServiceLevel: 0, totalTonnage: 0 },
            message: 'Versanddaten momentan nicht verfügbar'
        };
    }
    /**
     * Get error summary for Dispatch failures
     */
    getDispatchErrorSummary(errorMessage) {
        if (errorMessage.includes('timeout')) {
            return 'Versanddaten-Abfrage dauerte zu lange. Bitte versuchen Sie es später erneut.';
        }
        else if (errorMessage.includes('not available') || errorMessage.includes('connection')) {
            return 'Versanddatenbank ist momentan nicht erreichbar. Service wird wiederhergestellt.';
        }
        else {
            return 'Fehler beim Abrufen der Versanddaten. System arbeitet an der Wiederherstellung.';
        }
    }
    /**
     * Get empty Cutting data structure
     */
    getEmptyCuttingData() {
        return {
            cuttingData: [],
            efficiency: [],
            overview: { totalCuts: { cutTT: 0, cutTR: 0, cutRR: 0, pickCut: 0 }, averageEfficiency: 0 },
            topMachines: [],
            message: 'Schnittdaten momentan nicht verfügbar'
        };
    }
    /**
     * Get error summary for Cutting failures
     */
    getCuttingErrorSummary(errorMessage) {
        if (errorMessage.includes('timeout')) {
            return 'Schnittdaten-Abfrage dauerte zu lange. Maschinen-Datenbank ist überlastet.';
        }
        else if (errorMessage.includes('not available') || errorMessage.includes('connection')) {
            return 'Schnittdatenbank ist momentan nicht erreichbar. Verbindung wird wiederhergestellt.';
        }
        else {
            return 'Fehler beim Abrufen der Schnittdaten. Maschinen-Interface wird neu gestartet.';
        }
    }
    updateMetrics(startTime, results, cacheHit = false) {
        const executionTime = Date.now() - startTime;
        const successfulCount = results.filter(r => r.success).length;
        const failedCount = results.filter(r => !r.success).length;
        const fallbackCount = results.filter(r => r.fallback).length;
        this.metrics.totalQueries += results.length;
        this.metrics.successfulQueries += successfulCount;
        this.metrics.failedQueries += failedCount;
        // Update average query time
        const totalTime = (this.metrics.averageQueryTime * (this.metrics.totalQueries - results.length)) + executionTime;
        this.metrics.averageQueryTime = totalTime / this.metrics.totalQueries;
        // Update cache hit rate if applicable
        if (cacheHit) {
            const currentCacheHits = this.metrics.cacheHitRate * (this.metrics.totalQueries - results.length);
            this.metrics.cacheHitRate = (currentCacheHits + results.length) / this.metrics.totalQueries;
        }
        this.metrics.lastExecutionTime = new Date();
        // Log comprehensive metrics
        console.log(`📊 [QUERY-COORDINATOR] Metrics - Total: ${this.metrics.totalQueries}, ` +
            `Success: ${this.metrics.successfulQueries}, Failed: ${this.metrics.failedQueries}, ` +
            `Fallback: ${fallbackCount}, Cache Hit Rate: ${(this.metrics.cacheHitRate * 100).toFixed(1)}%, ` +
            `Avg Time: ${this.metrics.averageQueryTime.toFixed(0)}ms`);
        // Log warning if failure rate is high
        const failureRate = this.metrics.failedQueries / this.metrics.totalQueries;
        if (failureRate > 0.3) {
            console.warn(`⚠️ [QUERY-COORDINATOR] High failure rate detected: ${(failureRate * 100).toFixed(1)}%`);
        }
    }
    /**
     * Estimate the size of a query result for performance tracking
     */
    estimateResultSize(result) {
        try {
            return JSON.stringify(result.data).length;
        }
        catch (_a) {
            return 1024; // Default size estimate
        }
    }
    /**
     * Categorize error types for better monitoring
     */
    categorizeError(errorMessage) {
        const message = errorMessage.toLowerCase();
        if (message.includes('timeout'))
            return 'timeout';
        if (message.includes('connection') || message.includes('network'))
            return 'connection';
        if (message.includes('not found') || message.includes('404'))
            return 'not_found';
        if (message.includes('permission') || message.includes('unauthorized'))
            return 'permission';
        if (message.includes('database') || message.includes('sql'))
            return 'database';
        if (message.includes('memory') || message.includes('out of'))
            return 'resource';
        return 'unknown';
    }
}
exports.RepositoryQueryCoordinator = RepositoryQueryCoordinator;
exports.default = RepositoryQueryCoordinator;
