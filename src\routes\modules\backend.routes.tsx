import { createRoute } from "@tanstack/react-router";
import { Outlet } from "@tanstack/react-router";
import { ModuleGuard } from '@/components/auth';
import { backendModuleConfig } from '@/modules/backend/module.config';
import { RootRoute } from '../__root';
import { 
  LazySystemPage,
  LazyWorkflowPage,
  ModuleWrapper
} from '@/modules/lazy-loading';

/**
 * Backend & Automatisierung Module Routes
 * 
 * Definiert alle Routen für das Backend & Automatisierung-Modul mit rollenbasierter Zugriffskontrolle.
 * Alle Routen sind unter /modules/backend/* organisiert.
 */

// Basis-Route für das Backend-Modul
const backendBaseRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: 'modules/backend',
  component: () => (
    <ModuleGuard moduleId={backendModuleConfig.id} requiredRoles={backendModuleConfig.requiredRoles}>
      <Outlet />
    </ModuleGuard>
  )
});

// Index-Route für das Backend-Modul (Standard: System)
const backendIndexRoute = createRoute({
  getParentRoute: () => backendBaseRoute,
  path: '/',
  component: () => (
    <ModuleWrapper>
      <LazySystemPage />
    </ModuleWrapper>
  )
});

// System-Route
const systemRoute = createRoute({
  getParentRoute: () => backendBaseRoute,
  path: '/system',
  component: () => (
    <ModuleWrapper>
      <LazySystemPage />
    </ModuleWrapper>
  )
});

// Workflow-Route
const workflowRoute = createRoute({
  getParentRoute: () => backendBaseRoute,
  path: '/workflows',
  component: () => (
    <ModuleWrapper>
      <LazyWorkflowPage />
    </ModuleWrapper>
  )
});

// Exportiere alle Backend-Routen als Array
export const backendRoutes = [
  backendBaseRoute.addChildren([
    backendIndexRoute,
    systemRoute,
    workflowRoute
  ])
];
