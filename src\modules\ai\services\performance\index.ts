import { AIPerformanceOptimizer } from './AIPerformanceOptimizer';
import { AICacheService } from '../caching/AICacheService';
import { ResourceTracker } from './ResourceTracker';
import { AIPerformanceMonitor } from './AIPerformanceMonitor';

// Cache Services
export { AICacheService } from '../caching/AICacheService';
export type {
  CacheEntry,
  CacheStats,
  CacheConfig
} from '../caching/AICacheService';

// Performance Monitoring
export { AIPerformanceMonitor } from './AIPerformanceMonitor';
export type {
  PerformanceMetric,
  PerformanceStats,
  ResourceUsage,
  PerformanceAlert
} from './AIPerformanceMonitor';

// Resource Tracking
export { ResourceTracker } from './ResourceTracker';
export type {
  ResourceMetrics,
  ResourceThresholds,
  OptimizationRecommendation
} from './ResourceTracker';

// Performance Optimization
export { AIPerformanceOptimizer } from './AIPerformanceOptimizer';
export type {
  OptimizationStrategy,
  OptimizationResult,
  AutoOptimizationConfig
} from './AIPerformanceOptimizer';

// Performance Decorators
export {
  MonitorPerformance,
  Cacheable,
  Memoize,
  RateLimit,
  Retry,
  CircuitBreaker,
  setPerformanceMonitor,
  setCacheService
} from '../../decorators/performance';

// Performance Dashboard Component
export { AIPerformanceDashboard } from '../../components/performance/AIPerformanceDashboard';

/**
 * Initialize the AI Performance System
 * 
 * This function sets up the complete performance monitoring and optimization system
 * for AI services, including caching, monitoring, resource tracking, and optimization.
 * 
 * @param cacheService - The cache service instance
 * @returns Configured performance system components
 */
export function initializeAIPerformanceSystem(cacheService: any) {
  const performanceMonitor = new AIPerformanceMonitor();
  const resourceTracker = new ResourceTracker();
  const aiCacheService = new AICacheService(cacheService, {
    maxSize: 1000,
    defaultTtl: 3600000, // 1 hour
    maxMemoryUsage: 200 * 1024 * 1024, // 200MB
    evictionPolicy: 'lru',
    compressionEnabled: true
  });

  const performanceOptimizer = new AIPerformanceOptimizer(
    performanceMonitor,
    resourceTracker,
    aiCacheService
  );

  // Set up decorators
  const { setPerformanceMonitor, setCacheService } = require('../../decorators/performance');
  setPerformanceMonitor(performanceMonitor);
  setCacheService(aiCacheService);

  return {
    performanceMonitor,
    resourceTracker,
    aiCacheService,
    performanceOptimizer
  };
}

/**
 * Performance System Configuration
 */
export interface AIPerformanceSystemConfig {
  cache: {
    maxSize: number;
    defaultTtl: number;
    maxMemoryUsage: number;
    evictionPolicy: 'lru' | 'lfu' | 'ttl';
    compressionEnabled: boolean;
  };
  monitoring: {
    maxMetricsHistory: number;
    resourceCollectionInterval: number;
    alertThresholds: {
      highLatency: number;
      highErrorRate: number;
      memoryWarning: number;
      memoryCritical: number;
      cpuWarning: number;
      cpuCritical: number;
    };
  };
  optimization: {
    autoOptimizationEnabled: boolean;
    aggressiveness: 'conservative' | 'moderate' | 'aggressive';
    optimizationInterval: number;
  };
}

/**
 * Default configuration for the AI Performance System
 */
export const DEFAULT_PERFORMANCE_CONFIG: AIPerformanceSystemConfig = {
  cache: {
    maxSize: 1000,
    defaultTtl: 3600000, // 1 hour
    maxMemoryUsage: 200 * 1024 * 1024, // 200MB
    evictionPolicy: 'lru',
    compressionEnabled: true
  },
  monitoring: {
    maxMetricsHistory: 10000,
    resourceCollectionInterval: 30000, // 30 seconds
    alertThresholds: {
      highLatency: 5000, // 5 seconds
      highErrorRate: 0.1, // 10%
      memoryWarning: 100 * 1024 * 1024, // 100MB
      memoryCritical: 500 * 1024 * 1024, // 500MB
      cpuWarning: 70, // 70%
      cpuCritical: 90 // 90%
    }
  },
  optimization: {
    autoOptimizationEnabled: true,
    aggressiveness: 'moderate',
    optimizationInterval: 300000 // 5 minutes
  }
};

/**
 * Create a configured AI Performance System
 * 
 * @param cacheService - The underlying cache service
 * @param config - Optional configuration overrides
 * @returns Configured performance system
 */
export function createAIPerformanceSystem(
  cacheService: any,
  config: Partial<AIPerformanceSystemConfig> = {}
) {
  const finalConfig = {
    ...DEFAULT_PERFORMANCE_CONFIG,
    ...config,
    cache: { ...DEFAULT_PERFORMANCE_CONFIG.cache, ...config.cache },
    monitoring: { ...DEFAULT_PERFORMANCE_CONFIG.monitoring, ...config.monitoring },
    optimization: { ...DEFAULT_PERFORMANCE_CONFIG.optimization, ...config.optimization }
  };

  const performanceMonitor = new AIPerformanceMonitor();
  const resourceTracker = new ResourceTracker();

  // Configure resource tracker thresholds
  resourceTracker.setThresholds({
    memoryWarning: finalConfig.monitoring.alertThresholds.memoryWarning,
    memoryCritical: finalConfig.monitoring.alertThresholds.memoryCritical,
    cpuWarning: finalConfig.monitoring.alertThresholds.cpuWarning,
    cpuCritical: finalConfig.monitoring.alertThresholds.cpuCritical,
    diskSpaceWarning: 1024 * 1024 * 1024, // 1GB
    diskSpaceCritical: 100 * 1024 * 1024, // 100MB
    networkLatencyWarning: 1000, // 1 second
    networkLatencyCritical: 5000 // 5 seconds
  });

  const aiCacheService = new AICacheService(cacheService, finalConfig.cache);

  const performanceOptimizer = new AIPerformanceOptimizer(
    performanceMonitor,
    resourceTracker,
    aiCacheService
  );

  // Configure auto-optimization
  performanceOptimizer.updateAutoOptimizationConfig({
    enabled: finalConfig.optimization.autoOptimizationEnabled,
    aggressiveness: finalConfig.optimization.aggressiveness,
    optimizationInterval: finalConfig.optimization.optimizationInterval,
    maxMemoryUsage: finalConfig.cache.maxMemoryUsage,
    maxCpuUsage: finalConfig.monitoring.alertThresholds.cpuWarning,
    minCacheHitRate: 0.7
  });

  // Set up decorators
  const { setPerformanceMonitor, setCacheService } = require('../../decorators/performance');
  setPerformanceMonitor(performanceMonitor);
  setCacheService(aiCacheService);

  return {
    performanceMonitor,
    resourceTracker,
    aiCacheService,
    performanceOptimizer,
    config: finalConfig
  };
}