/**
 * Warehouse Optimization Types
 * 
 * Type definitions for AI-powered warehouse optimization service
 */

/**
 * Warehouse Item with movement and placement data
 */
export interface WarehouseItem {
  id: string;
  name: string;
  category: string;
  weight: number;
  dimensions: ItemDimensions;
  currentLocation: StorageLocation;
  accessFrequency: number; // Access frequency per day
  lastAccessed: Date;
  relationships: ItemRelationship[];
  pickingPriority: 'high' | 'medium' | 'low';
}

/**
 * Item dimensions for placement optimization
 */
export interface ItemDimensions {
  length: number;
  width: number;
  height: number;
  volume: number;
}

/**
 * Storage location in warehouse
 */
export interface StorageLocation {
  id: string;
  zone: string;
  aisle: string;
  shelf: string;
  position: string;
  coordinates: LocationCoordinates;
  capacity: number;
  currentUtilization: number;
  accessibilityScore: number; // 1-10, higher is more accessible
}

/**
 * 3D coordinates for location mapping
 */
export interface LocationCoordinates {
  x: number;
  y: number;
  z: number;
}

/**
 * Item relationship for co-location optimization
 */
export interface ItemRelationship {
  relatedItemId: string;
  relationshipType: 'frequently_picked_together' | 'same_order' | 'complementary';
  strength: number; // 0-1, higher means stronger relationship
}

/**
 * Warehouse layout analysis result
 */
export interface WarehouseLayoutAnalysis {
  totalItems: number;
  utilizationRate: number;
  accessibilityScore: number;
  inefficiencies: LayoutInefficiency[];
  optimizationPotential: number; // Percentage improvement possible
  recommendations: LayoutRecommendation[];
}

/**
 * Layout inefficiency detection
 */
export interface LayoutInefficiency {
  type: 'misplaced_high_frequency' | 'poor_accessibility' | 'suboptimal_grouping' | 'underutilized_space';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedItems: string[];
  estimatedImpact: number; // Time lost per day in minutes
}

/**
 * Layout improvement recommendation
 */
export interface LayoutRecommendation {
  id: string;
  type: 'relocate_item' | 'reorganize_zone' | 'optimize_grouping' | 'improve_accessibility';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  affectedItems: string[];
  estimatedBenefit: OptimizationBenefit;
  implementationEffort: 'low' | 'medium' | 'high';
  implementationSteps: string[];
}

/**
 * Optimization benefit metrics
 */
export interface OptimizationBenefit {
  timeSavingsPerDay: number; // Minutes saved per day
  efficiencyImprovement: number; // Percentage improvement
  costSavings: number; // Estimated cost savings per month
  pickingDistanceReduction: number; // Percentage reduction in picking distance
}

/**
 * Optimal item placement result
 */
export interface OptimalPlacement {
  itemId: string;
  currentLocation: StorageLocation;
  recommendedLocation: StorageLocation;
  reason: string;
  expectedBenefit: OptimizationBenefit;
  confidence: number; // 0-1, confidence in recommendation
}

/**
 * Picking route optimization request
 */
export interface PickingRouteRequest {
  orderId: string;
  items: PickingItem[];
  startLocation: LocationCoordinates;
  endLocation?: LocationCoordinates;
  constraints: PickingConstraints;
}

/**
 * Item to be picked
 */
export interface PickingItem {
  itemId: string;
  quantity: number;
  location: StorageLocation;
  priority: 'high' | 'medium' | 'low';
  weight: number;
  fragile: boolean;
}

/**
 * Picking constraints
 */
export interface PickingConstraints {
  maxWeight: number;
  maxVolume: number;
  timeLimit?: number; // Minutes
  avoidZones?: string[];
  preferredSequence?: 'weight_ascending' | 'weight_descending' | 'fragile_first' | 'priority_first';
}

/**
 * Optimized picking route
 */
export interface OptimizedPickingRoute {
  routeId: string;
  orderId: string;
  sequence: PickingStep[];
  totalDistance: number;
  estimatedTime: number; // Minutes
  efficiency: number; // 0-1, route efficiency score
  alternativeRoutes: AlternativeRoute[];
}

/**
 * Individual picking step
 */
export interface PickingStep {
  stepNumber: number;
  itemId: string;
  location: StorageLocation;
  quantity: number;
  distanceFromPrevious: number;
  estimatedTime: number;
  instructions: string;
}

/**
 * Alternative route option
 */
export interface AlternativeRoute {
  routeId: string;
  description: string;
  totalDistance: number;
  estimatedTime: number;
  efficiency: number;
  tradeoffs: string[];
}

/**
 * Warehouse efficiency analysis
 */
export interface WarehouseEfficiencyAnalysis {
  overallEfficiency: number; // 0-1, overall warehouse efficiency
  pickingEfficiency: number;
  storageEfficiency: number;
  accessibilityEfficiency: number;
  bottlenecks: EfficiencyBottleneck[];
  trends: EfficiencyTrend[];
  benchmarks: EfficiencyBenchmark[];
}

/**
 * Efficiency bottleneck identification
 */
export interface EfficiencyBottleneck {
  type: 'congested_aisle' | 'poor_item_placement' | 'inefficient_routing' | 'underutilized_space';
  location: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  impact: number; // Time lost per day in minutes
  description: string;
  recommendations: string[];
}

/**
 * Efficiency trend analysis
 */
export interface EfficiencyTrend {
  metric: string;
  period: 'daily' | 'weekly' | 'monthly';
  trend: 'improving' | 'declining' | 'stable';
  changeRate: number; // Percentage change
  dataPoints: EfficiencyDataPoint[];
}

/**
 * Efficiency data point
 */
export interface EfficiencyDataPoint {
  date: Date;
  value: number;
  context?: string;
}

/**
 * Efficiency benchmark
 */
export interface EfficiencyBenchmark {
  metric: string;
  currentValue: number;
  industryAverage: number;
  bestPractice: number;
  gap: number;
  recommendations: string[];
}

/**
 * Warehouse optimization configuration
 */
export interface WarehouseOptimizationConfig {
  analysisDepth: 'basic' | 'detailed' | 'comprehensive';
  optimizationGoals: OptimizationGoal[];
  constraints: OptimizationConstraints;
  preferences: OptimizationPreferences;
}

/**
 * Optimization goal
 */
export interface OptimizationGoal {
  type: 'minimize_picking_time' | 'maximize_space_utilization' | 'improve_accessibility' | 'reduce_travel_distance';
  weight: number; // 0-1, relative importance
  target?: number; // Target value if applicable
}

/**
 * Optimization constraints
 */
export interface OptimizationConstraints {
  maxRelocations: number;
  budgetLimit?: number;
  timeframe: number; // Days to implement changes
  restrictedZones: string[];
  minimumAccessibility: number;
}

/**
 * Optimization preferences
 */
export interface OptimizationPreferences {
  prioritizeHighFrequencyItems: boolean;
  maintainItemGroupings: boolean;
  minimizeDisruption: boolean;
  considerSeasonality: boolean;
}

/**
 * Warehouse optimization result
 */
export interface WarehouseOptimizationResult {
  analysisId: string;
  timestamp: Date;
  currentState: WarehouseLayoutAnalysis;
  optimizedState: WarehouseLayoutAnalysis;
  recommendations: LayoutRecommendation[];
  placements: OptimalPlacement[];
  expectedBenefits: OptimizationBenefit;
  implementationPlan: ImplementationPlan;
  riskAssessment: RiskAssessment;
}

/**
 * Implementation plan for optimization
 */
export interface ImplementationPlan {
  phases: ImplementationPhase[];
  totalDuration: number; // Days
  totalCost: number;
  resourceRequirements: ResourceRequirement[];
  milestones: Milestone[];
}

/**
 * Implementation phase
 */
export interface ImplementationPhase {
  phaseNumber: number;
  name: string;
  description: string;
  duration: number; // Days
  dependencies: number[]; // Phase numbers this depends on
  tasks: ImplementationTask[];
  expectedBenefit: OptimizationBenefit;
}

/**
 * Implementation task
 */
export interface ImplementationTask {
  taskId: string;
  name: string;
  description: string;
  estimatedHours: number;
  requiredSkills: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Resource requirement
 */
export interface ResourceRequirement {
  type: 'personnel' | 'equipment' | 'materials' | 'time';
  description: string;
  quantity: number;
  unit: string;
  cost?: number;
}

/**
 * Implementation milestone
 */
export interface Milestone {
  name: string;
  description: string;
  targetDate: Date;
  dependencies: string[];
  successCriteria: string[];
}

/**
 * Risk assessment for optimization
 */
export interface RiskAssessment {
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  risks: OptimizationRisk[];
  mitigationStrategies: MitigationStrategy[];
}

/**
 * Optimization risk
 */
export interface OptimizationRisk {
  type: 'operational_disruption' | 'cost_overrun' | 'timeline_delay' | 'performance_degradation';
  probability: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  mitigationActions: string[];
}

/**
 * Risk mitigation strategy
 */
export interface MitigationStrategy {
  riskType: string;
  strategy: string;
  actions: string[];
  cost: number;
  effectiveness: number; // 0-1, expected effectiveness
}