/**
 * Module Routes Index
 * 
 * Exports all module-specific routes for the modular architecture.
 * Each module has its own route file with protected routes using ModuleGuard.
 */

export { dashboardRoutes } from './dashboard.routes';
export { stoerungenRoutes } from './stoerungen.routes';
export { backendRoutes } from './backend.routes';
export { aiRoutes } from './ai.routes';

// Combine all module routes
import { dashboardRoutes } from './dashboard.routes';
import { stoerungenRoutes } from './stoerungen.routes';
import { backendRoutes } from './backend.routes';
import { aiRoutes } from './ai.routes';

export const allModuleRoutes = [
  ...dashboardRoutes,
  ...stoerungenRoutes,
  ...backendRoutes,
  ...aiRoutes,
];