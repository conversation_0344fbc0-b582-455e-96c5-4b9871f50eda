/**
 * Hilfsfunktionen für die Formatierung von Datumsangaben
 */

/**
 * Formatiert ein Datum mit Wochentag in einer zweiten Zeile unter dem Datum im Format:
 * "01.01."
 * "Mo"
 * 
 * @param dateValue - Das zu formatierende Datum (als Date-Objekt oder String)
 * @returns Formatiertes Datum mit Wochentag in zwei Zeilen
 */
export function formatDateWithWeekday(dateValue: Date | string): string {
  try {
    const date = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
    
    // Wochentage auf Deutsch
    const weekdays = ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'];
    const weekday = weekdays[date.getDay()];
    
    // Datum im Format "TT.MM."
    const dateStr = date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
    
    // Rückgabe mit Zeilenumbruch, damit der Wochentag unter dem Datum steht
    return `${dateStr}\n${weekday}`;
  } catch (error) {
    console.error('Fehler bei der Datumsformatierung:', error);
    return String(dateValue);
  }
}
