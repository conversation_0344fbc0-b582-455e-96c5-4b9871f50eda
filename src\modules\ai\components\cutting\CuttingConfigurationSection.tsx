/**
 * CuttingConfigurationSection
 *
 * Enthält die komplette UI für den Tab "Konfiguration":
 * - Algorithmus-Einstellungen und Verschnitt-Parameter
 * - Lieferung (Artikelliste mit Materialauswahl)
 * - Verfügbare <PERSON> (mit Trommel-Auswahl)
 * - Optimieren-Button und Export nach Erfolg
 * - Manuelle Trommelauswahl und Vergleichsergebnis
 *
 * Reine Präsentationskomponente – erhält alle Daten/Handler als Props.
 * So bleibt `CuttingOptimizationPage` schlank und testbar.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import MaterialSelector from '@/components/MaterialSelector';
import TrommelSelector from '@/components/TrommelSelector';
import {
  Plus,
  Minus,
  RefreshCw,
  CheckCircle,
  Download,
  Calendar as CalendarIcon,
  Package,
  Hash,
  Layers,
  Database,
  Trash2,
  Brain,
  Zap,
  Eye
} from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { DrumComparisonDisplay } from './DrumComparisonDisplay';
import AvailableDrumsTable from './AvailableDrumsTable';
import { inventoryService } from '../../services/inventory/InventoryService';
import { availableDrumsService, type AvailableDrum } from '../../services/cutting/available-drums/AvailableDrumsService';
import type {
  CuttingOrder,
  DrumInventory,
  CuttingConstraints,
  Delivery,
  DeliveryItem,
  ManualDrumSelection,
  DrumComparisonResult,
} from '../../services/types';
import type { AITrommelSelection, AITrommelauswahlResult } from '../../services/cutting/CuttingOptimizerService';

interface CuttingConfigurationSectionProps {
  // Algorithmus & Constraints
  selectedAlgorithm: 'first-fit' | 'best-fit' | 'genetic';
  onAlgorithmChange: (algo: 'first-fit' | 'best-fit' | 'genetic') => void;
  wasteType: 'length' | 'percentage';
  onWasteTypeChange: (t: 'length' | 'percentage') => void;
  constraints: CuttingConstraints;
  onConstraintsChange: (c: CuttingConstraints) => void;

  // Lieferung
  delivery: Delivery;
  verfügbareKabelmaterialien: any[];
  kabelMaterialSuchOpen: boolean;
  kabelMaterialSuchWert: string;
  onMaterialSuchOpenChange: (open: boolean) => void;
  onMaterialSuchWertChange: (val: string) => void;
  onKabelMaterialChange: (value: string) => void;
  onItemMaterialSelect: (itemId: string, material: any) => void;
  onAddDeliveryItem: () => void;
  onRemoveDeliveryItem: (itemId: string) => void;
  onUpdateDeliveryItem: (itemId: string, updates: Partial<DeliveryItem>) => void;

  // Trommeln
  drums: DrumInventory[];
  verfügbareTrommeln: any[];
  selectedTrommel: string;
  trommelSuchOpen: boolean;
  trommelSuchWert: string;
  selectedCableType: string;
  onAddDrum: () => void;
  onRemoveDrum: (drumId: string) => void;
  onUpdateDrum: (drumId: string, updates: Partial<DrumInventory>) => void;
  onTrommelChange: (val: string) => void;
  onTrommelSuchOpenChange: (open: boolean) => void;
  onTrommelSuchWertChange: (val: string) => void;
  onTrommelSelect: (trommel: any) => void;

  // Optimierung & Export
  isOptimizing: boolean;
  onOptimize: () => void;
  hasPlan: boolean;
  onExportPlan: (format: 'pdf' | 'excel') => void;

  // Manuelle Auswahl & Vergleich
  manualDrumSelections: ManualDrumSelection[];
  selectionDate: string;
  onSelectionsChange: (s: ManualDrumSelection[]) => void;
  onDateChange: (d: string) => void;
  onAddManualSelection: () => void;
  onUpdateManualSelection: (index: number, s: ManualDrumSelection) => void;
  onRemoveManualSelection: (index: number) => void;
  comparisonResult: DrumComparisonResult | null;
  showComparison: boolean;

  // KI-Trommelauswahl
  aiTrommelauswahlEnabled: boolean;
  onAITrommelauswahlToggle: (enabled: boolean) => void;
  aiTrommelEmpfehlungen: AITrommelSelection[] | null;
  onPreviewAISelection: () => void;
  isLoadingAISelection: boolean;

  // Verfügbare Trommeln aus Datenbank
  availableDrumsFromDB: AvailableDrum[];
  onAvailableDrumsSelected: (drums: AvailableDrum[]) => void;
}

export default function CuttingConfigurationSection(props: CuttingConfigurationSectionProps) {
  const {
    // Algorithmus & Constraints
    selectedAlgorithm,
    onAlgorithmChange,
    wasteType,
    onWasteTypeChange,
    constraints,
    onConstraintsChange,

    // Lieferung
    delivery,
    verfügbareKabelmaterialien,
    kabelMaterialSuchOpen,
    kabelMaterialSuchWert,
    onMaterialSuchOpenChange,
    onMaterialSuchWertChange,
    onKabelMaterialChange,
    onItemMaterialSelect,
    onAddDeliveryItem,
    onRemoveDeliveryItem,
    onUpdateDeliveryItem,

    // Trommeln
    drums,
    verfügbareTrommeln,
    selectedTrommel,
    trommelSuchOpen,
    trommelSuchWert,
    selectedCableType,
    onAddDrum,
    onRemoveDrum,
    onUpdateDrum,
    onTrommelChange,
    onTrommelSuchOpenChange,
    onTrommelSuchWertChange,
    onTrommelSelect,

    // Optimierung & Export
    isOptimizing,
    onOptimize,
    hasPlan,
    onExportPlan,

    // Manuelle Auswahl & Vergleich
    manualDrumSelections,
    selectionDate,
    onSelectionsChange,
    onDateChange,
    onAddManualSelection,
    onUpdateManualSelection,
    onRemoveManualSelection,
    comparisonResult,
    showComparison,
  } = props;

  // State für die manuelle Trommelauswahl
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    selectionDate ? new Date(selectionDate) : new Date()
  );
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  // Aktualisiere das Datum wenn sich selectionDate ändert
  useEffect(() => {
    if (selectionDate) {
      setSelectedDate(new Date(selectionDate));
    }
  }, [selectionDate]);

  // Handle Datumsänderung
  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      onDateChange(format(date, 'yyyy-MM-dd'));
      setIsCalendarOpen(false);
    }
  };

  // Handle Eingabefeld-Änderungen für eine manuelle Auswahl mit automatischer Datenabfrage
  const handleManualInputChange = async (
    index: number,
    field: keyof ManualDrumSelection,
    value: string | number
  ) => {
    const updatedSelection = { ...manualDrumSelections[index] };
    (updatedSelection as any)[field] = value;
    
    // Automatische Datenabfrage bei Chargennummer-Eingabe
    if (field === 'chargeNumber' && typeof value === 'string' && value.trim() !== '') {
      try {
        const drumData = await inventoryService.getDrumsByCharges([value.trim()]);
        if (drumData && drumData.length > 0) {
          const drum = drumData[0];
          // Automatisches Befüllen der anderen Felder basierend auf Datenbankdaten
          updatedSelection.chargeNumber = value.trim();
          updatedSelection.material = drum.material || '';
          updatedSelection.gesamtbestand = drum.gesamtbestand || 0;
          updatedSelection.lagereinheitentyp = drum.lagereinheitentyp || '';
        }
      } catch (error) {
        console.error('Fehler beim Laden der Trommeldetails:', error);
        // Bei Fehler nur die eingegebene Chargennummer setzen
        updatedSelection.chargeNumber = typeof value === 'string' ? value.trim() : String(value);
      }
    }
    
    onUpdateManualSelection(index, updatedSelection);
  };

  // Validierung einer manuellen Auswahl
  const isManualSelectionValid = (selection: ManualDrumSelection): boolean => {
    return !!(
      selection.chargeNumber &&
      selection.material &&
      selection.gesamtbestand > 0 &&
      selection.lagereinheitentyp
    );
  };

  // Zähle gültige manuelle Auswahlen
  const validManualSelectionsCount = manualDrumSelections.filter(isManualSelectionValid).length;

  return (
    <div className="space-y-6">
      {/* Einziger großer Container mit drei Spalten und vertikalen Trennlinien */}
      <Card className="border-slate-800 bg-slate-50 py-6 px-0 transition-all duration-300 hover:border-slate-200">
        <CardHeader>
          <div className="flex items-center gap-2">
            <div className="flex flex-col">
              <CardTitle className="text-2xl">Konfiguration</CardTitle>
              <CardDescription className="text-gray-600">
                Algorithmus, Lieferung und verfügbare Trommeln konfigurieren
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:divide-x lg:divide-[#ff7a05]/40">
            {/* Erste Spalte - Algorithmus-Einstellungen */}
            <div className="space-y-4 lg:pr-8 -py-2">
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold">Algorithmus-Einstellungen</h3>
              </div>
              <p className="text-sm text-gray-600 -mt-3">
                Wählen den gewünschten Optimierungsalgorithmus und den Parameter.
              </p>
              {/* Algorithmus, Verschnitt-Typ und Max Verschnitt in einer Reihe mit drei Spalten */}
              <div className="grid gap-2" style={{gridTemplateColumns: '1fr 0.2fr 1fr 0.1fr 1fr'}}>
                {/* Algorithmus */}
                <div>
                  <Label htmlFor="algorithm">Algorithmus</Label>
                  <Select value={selectedAlgorithm} onValueChange={onAlgorithmChange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="first-fit">First-Fit (Schnell)</SelectItem>
                      <SelectItem value="best-fit">Best-Fit (Ausgewogen)</SelectItem>
                      <SelectItem value="genetic">Genetisch (Optimal)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Spacer zwischen Algorithmus und Verschnitt-Typ */}
                <div></div>
                
                <div>
                  <Label htmlFor="wasteType">Verschnitt-Typ</Label>
                  <Select value={wasteType} onValueChange={onWasteTypeChange}>
                    <SelectTrigger className="bg-white">
                      <SelectValue placeholder="Verschnitt-Typ wählen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="length">In (m)</SelectItem>
                      <SelectItem value="percentage">In (%)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Spacer zwischen Verschnitt-Typ und Max Verschnitt */}
                <div></div>

                {wasteType === 'length' ? (
                  <div>
                    <Label htmlFor="maxWasteLength">Max Verschnitt in (m)</Label>
                    <Input
                      id="maxWasteLength"
                      type="number"
                      step="0.1"
                      className="bg-white"
                      value={constraints.maxWasteLength || 1.0}
                      onChange={(e) =>
                        onConstraintsChange({
                          ...constraints,
                          maxWasteLength: parseFloat(e.target.value) || 1.0,
                        })
                      }
                    />
                  </div>
                ) : (
                  <div>
                    <Label htmlFor="maxWaste">Max Verschnitt (%)</Label>
                    <Input
                      id="maxWaste"
                      type="number"
                      className="bg-white"
                      value={constraints.maxWastePercentage || 15}
                      onChange={(e) =>
                        onConstraintsChange({
                          ...constraints,
                          maxWastePercentage: parseInt(e.target.value) || 15,
                        })
                      }
                    />
                  </div>
                )}
              </div>

              {/* Dynamische Algorithmus-Beschreibung */}
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  {selectedAlgorithm === 'first-fit' &&
                    'First-Fit Algorithmus: Schnelle Lösung, die Schnitte in der ersten passenden Trommel platziert. Ideal für große Datenmengen mit akzeptabler Verschnittrate.'}
                  {selectedAlgorithm === 'best-fit' &&
                    'Best-Fit Algorithmus: Ausgewogene Lösung, die den besten verfügbaren Platz für jeden Schnitt sucht. Bietet gute Balance zwischen Geschwindigkeit und Optimierung.'}
                  {selectedAlgorithm === 'genetic' &&
                    'Genetischer Algorithmus: Hochoptimierte Lösung durch evolutionäre Optimierung. Liefert beste Ergebnisse bei minimaler Verschnittrate, benötigt mehr Rechenzeit.'}
                </p>
              </div>
            </div>

            {/* Zweite Spalte - Lieferung */}
            <div className="space-y-6 lg:pr-8">
              {/* Lieferungsartikel */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-semibold">Lieferung</h3>
                </div>
                <p className="text-sm text-gray-600 -mt-3">Konfigurieren Sie die Kabeltypen für die Lieferung</p>
                
                {/* Auswahldatum direkt unter der Beschreibung */}
                <div className="space-y-2">
                  <Label htmlFor="selection-date">Auswahldatum</Label>
                  <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {selectedDate ? (
                          format(selectedDate, 'PPP', { locale: de })
                        ) : (
                          <span>Datum auswählen</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-white" align="start">
                      <Calendar
                        mode="single"
                        selected={selectedDate}
                        onSelect={handleDateSelect}
                        initialFocus
                        locale={de}
                        className="bg-white"
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-4">
                  {delivery.items.map((item, index) => (
                    <div key={item.id} className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Artikel {index + 1}</span>
                        <div className="flex gap-2">
                          <Button onClick={onAddDeliveryItem} size="sm" variant="ghost" className="h-8 w-8 p-0 hover:bg-green-100">
                            <Plus className="h-4 w-4 text-green-600" />
                          </Button>
                          {delivery.items.length > 1 && (
                            <Button
                              onClick={() => onRemoveDeliveryItem(item.id)}
                              size="sm"
                              variant="ghost"
                              className="h-8 w-8 p-0 hover:bg-red-100"
                            >
                              <Minus className="h-4 w-4 text-red-600" />
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* Drei-Spalten-Layout für Kabeltyp, Länge und Anzahl */}
                       <div className="grid grid-cols-3 gap-3">
                         <div>
                           <Label>Kabeltyp</Label>
                           <MaterialSelector
                             selectedMaterial={item.cableType || ''}
                             materialSuchOpen={kabelMaterialSuchOpen}
                             materialSuchWert={kabelMaterialSuchWert}
                             verfügbareMaterialien={verfügbareKabelmaterialien}
                             onMaterialChange={onKabelMaterialChange}
                             onMaterialSelect={(material) => onItemMaterialSelect(item.id, material)}
                             onMaterialSuchOpenChange={onMaterialSuchOpenChange}
                             onMaterialSuchWertChange={onMaterialSuchWertChange}
                           />
                         </div>
                         <div>
                           <Label>Länge (m)</Label>
                           <Input
                             type="number"
                             step="0.1"
                             className="bg-white"
                             value={item.requiredLength}
                             onChange={(e) => onUpdateDeliveryItem(item.id, { requiredLength: parseFloat(e.target.value) || 0 })}
                           />
                         </div>
                         <div>
                           <Label>Anzahl der Schnitte</Label>
                           <Input
                             type="number"
                             className="bg-white"
                             value={item.quantity}
                             onChange={(e) => onUpdateDeliveryItem(item.id, { quantity: parseInt(e.target.value) || 1 })}
                           />
                         </div>
                       </div>
                       
                       {/* Trommelauswahl direkt unter dem Kabeltyp */}
                       <div className="mt-3 pt-3 border-t border-gray-200">
                         <div className="flex items-center justify-between mb-3">
                           <Label className="text-sm font-medium flex items-center gap-1">
                             <Package className="h-4 w-4" />
                             Trommelauswahl für Artikel {index + 1}
                           </Label>
                           <Button
                             onClick={onAddManualSelection}
                             size="sm"
                             variant="outline"
                             className="flex items-center gap-1 text-xs"
                           >
                             <Plus className="h-3 w-3" />
                             Trommel hinzufügen
                           </Button>
                         </div>

                         {manualDrumSelections.length === 0 ? (
                           <div className="text-center py-4 text-gray-500 bg-gray-50 rounded-lg">
                             <Package className="h-6 w-6 mx-auto mb-1 opacity-50" />
                             <p className="text-xs">Noch keine Trommeln ausgewählt</p>
                           </div>
                         ) : (
                           <div className="space-y-2">
                             {manualDrumSelections.map((selection, selectionIndex) => (
                               <div key={selectionIndex} className="border border-dashed border-gray-300 rounded-lg p-2 bg-gray-50">
                                 <div className="flex flex-col sm:grid sm:grid-cols-2 gap-2 mb-2">
                                   {/* Chargennummer */}
                                 <div className="space-y-1">
                                   <Label className="flex items-center gap-1 text-xs">
                                     <Hash className="h-3 w-3" />
                                     Chargennummer
                                   </Label>
                                   <Input
                                     value={selection.chargeNumber}
                                     onChange={(e) => handleManualInputChange(selectionIndex, 'chargeNumber', e.target.value)}
                                     placeholder="z.B. CH-2024-001"
                                     className={`text-xs h-8 ${!selection.chargeNumber ? 'border-red-300' : ''}`}
                                   />
                                 </div>

                                   {/* Material - automatisch aus Datenbank */}
                                   <div className="space-y-1">
                                     <Label className="flex items-center gap-1 text-xs">
                                       <Layers className="h-3 w-3" />
                                       Material - auto
                                     </Label>
                                     <div className={`text-xs h-8 px-3 py-2 border rounded-md flex items-center ${
                                       selection.material ? 'bg-green-50 border-green-200 text-green-800' : 'bg-gray-50 border-gray-200 text-gray-500'
                                     }`}>
                                       {selection.material || 'Material wird automatisch geladen'}
                                     </div>
                                   </div>
                                 </div>
                                 
                                 {/* Hinweistext über die volle Breite */}
                                 <div className="mb-2">
                                   <p className="text-xs text-gray-500">
                                     💡 Material, Bestand und Trommeltyp werden automatisch aus der Datenbank geladen
                                   </p>
                                 </div>

                                 <div className="flex flex-col sm:grid sm:grid-cols-2 lg:grid-cols-4 gap-2">
                                   {/* Gesamtbestand - automatisch aus Datenbank */}
                                   <div className="space-y-1">
                                     <Label className="flex items-center gap-1 text-xs">
                                       <Database className="h-3 w-3" />
                                       Bestand (m)
                                     </Label>
                                     <div className={`text-xs h-8 px-3 py-2 border rounded-md flex items-center ${
                                       selection.gesamtbestand > 0 ? 'bg-green-50 border-green-200 text-green-800' : 'bg-gray-50 border-gray-200 text-gray-500'
                                     }`}>
                                       {selection.gesamtbestand > 0 ? `${selection.gesamtbestand} m` : 'Automatisch'}
                                     </div>
                                   </div>

                                   {/* Lagereinheitentyp - automatisch aus Datenbank */}
                                   <div className="space-y-1">
                                     <Label className="flex items-center gap-1 text-xs">
                                       <Package className="h-3 w-3" />
                                       Trommeltyp
                                     </Label>
                                     <div className={`text-xs h-8 px-3 py-2 border rounded-md flex items-center ${
                                       selection.lagereinheitentyp ? 'bg-green-50 border-green-200 text-green-800' : 'bg-gray-50 border-gray-200 text-gray-500'
                                     }`}>
                                       {selection.lagereinheitentyp || 'Automatisch'}
                                     </div>
                                   </div>

                                   {/* Status */}
                                   <div className="space-y-1 px-10">
                                     <Label className="text-xs">Status</Label>
                                     <div className="flex items-center h-8">
                                       {isManualSelectionValid(selection) ? (
                                         <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs px-2 py-1">
                                           ✓ Gültig
                                         </Badge>
                                       ) : (
                                         <Badge variant="destructive" className="text-xs px-2 py-1">
                                           ⚠ Unvollständig
                                         </Badge>
                                       )}
                                     </div>
                                   </div>

                                   {/* Entfernen Button */}
                                   <div className="space-y-1">
                                     <Label className="text-xs opacity-0">Aktion</Label>
                                     <Button
                                       onClick={() => onRemoveManualSelection(selectionIndex)}
                                       variant="destructive"
                                       size="sm"
                                       className="h-8 w-full flex items-center gap-1 text-xs"
                                     >
                                       <Trash2 className="h-3 w-3" />
                                       Entfernen
                                     </Button>
                                   </div>
                                 </div>
                               </div>
                             ))}
                           </div>
                         )}

                         {/* Zusammenfassung der manuellen Auswahlen */}
                         {manualDrumSelections.length > 0 && (
                           <div className="grid grid-cols-2 gap-2 text-center text-xs mt-2">
                             <div className="p-2 bg-gray-100 rounded">
                               <div className="font-bold">{manualDrumSelections.length}</div>
                               <div className="text-gray-600">Gesamt</div>
                             </div>
                             <div className="p-2 bg-green-50 rounded">
                               <div className="font-bold text-green-600">{validManualSelectionsCount}</div>
                               <div className="text-gray-600">Gültig</div>
                             </div>
                           </div>
                         )}
                       </div>
                    </div>
                  ))}
                </div>
              </div>


            </div>

            {/* Dritte Spalte - Verfügbare Trommeln */}
            <div className="space-y-4 lg:-pl-2">
              {/* Neue Sektion: KI-Trommelauswahl */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-blue-600" />
                  <h3 className="text-lg font-semibold">KI-Trommelauswahl</h3>
                </div>
                <p className="text-sm text-gray-600 -mt-3">
                  Lassen Sie die KI automatisch die optimalen Trommeln für Ihre Bestellungen auswählen.
                </p>
                
                {/* KI-Toggle */}
                <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">KI-Auswahl aktivieren</span>
                  </div>
                  <Button
                    onClick={() => props.onAITrommelauswahlToggle(!props.aiTrommelauswahlEnabled)}
                    variant={props.aiTrommelauswahlEnabled ? "default" : "outline"}
                    size="sm"
                    className={props.aiTrommelauswahlEnabled ? "bg-blue-600 hover:bg-blue-700" : ""}
                  >
                    {props.aiTrommelauswahlEnabled ? "Aktiviert" : "Aktivieren"}
                  </Button>
                </div>

                {/* KI-Empfehlungen Vorschau */}
                {props.aiTrommelauswahlEnabled && (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">KI-Empfehlungen</span>
                      <Button
                        onClick={props.onPreviewAISelection}
                        disabled={props.isLoadingAISelection || props.delivery.items.length === 0}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                      >
                        {props.isLoadingAISelection ? (
                          <RefreshCw className="h-3 w-3 animate-spin" />
                        ) : (
                          <Eye className="h-3 w-3" />
                        )}
                        {props.isLoadingAISelection ? "Lädt..." : "Vorschau"}
                      </Button>
                    </div>

                    {/* KI-Empfehlungen anzeigen */}
                    {props.aiTrommelEmpfehlungen && props.aiTrommelEmpfehlungen.length > 0 && (
                      <div className="space-y-2 max-h-60 overflow-y-auto">
                        {props.aiTrommelEmpfehlungen.map((empfehlung, index) => (
                          <div key={index} className="p-3 bg-white border border-gray-200 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium text-sm">{empfehlung.kabeltyp}</span>
                              <Badge 
                                variant={empfehlung.confidence >= 0.8 ? "default" : empfehlung.confidence >= 0.6 ? "secondary" : "destructive"}
                                className="text-xs"
                              >
                                {Math.round(empfehlung.confidence * 100)}% Vertrauen
                              </Badge>
                            </div>
                            <div className="text-xs text-gray-600 space-y-1">
                              <div><strong>Trommel:</strong> {empfehlung.empfohlene_trommel}</div>
                              <div><strong>Max. Länge:</strong> {empfehlung.maximale_länge}m</div>
                              <div><strong>Grund:</strong> {empfehlung.grund}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Keine Empfehlungen */}
                    {props.aiTrommelEmpfehlungen && props.aiTrommelEmpfehlungen.length === 0 && (
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-center">
                        <p className="text-sm text-yellow-800">
                          Keine KI-Empfehlungen verfügbar. Stellen Sie sicher, dass Bestellungen vorhanden sind.
                        </p>
                      </div>
                    )}

                    {/* Hinweis */}
                    {!props.aiTrommelEmpfehlungen && (
                      <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg text-center">
                        <p className="text-sm text-gray-600">
                          Klicken Sie auf "Vorschau", um KI-Empfehlungen zu laden.
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Neue Sektion: Verfügbare Trommeln aus Datenbank */}
              <div className="space-y-4 lg:-pl-2">
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-semibold">Zu Vergleichende Trommel auswählen</h3>
                </div>
                <p className="text-sm text-gray-600 -mt-3">
                  Wählen die verfügbare Trommeln aus der Datenbank.
                </p>
                <AvailableDrumsTable
                  selectedDate={props.selectionDate}
                  selectedMaterial={props.delivery.items.length > 0 ? props.delivery.items[0].cableType : ''}
                  minLength={props.delivery.items.length > 0 ? props.delivery.items[0].requiredLength : 0}
                  onDrumsSelected={props.onAvailableDrumsSelected}
                  selectedDrums={props.availableDrumsFromDB}
                />
              </div>
            </div>
          </div>

          {/* Optimierungsbereich - zentriert unter der Trennlinie */}
          <div className="border-t border-[#ff7a05]/40 pt-6 mt-6">
            <div className="max-w-2xl mx-auto space-y-4">
              <Button onClick={onOptimize} disabled={isOptimizing} variant="sfm" className="w-full flex items-center gap-2">
                {isOptimizing ? <RefreshCw className="h-4 w-4 animate-spin" /> : <CheckCircle className="h-4 w-4" />}
                {isOptimizing ? 'Optimiere...' : 'Optimieren'}
              </Button>

              {hasPlan && !isOptimizing && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-semibold text-green-800">Optimierung erfolgreich abgeschlossen!</span>
                  </div>
                  <p className="text-sm text-green-700 mb-4">
                    Die Ergebnisse können in den Tabs Visualisierung, Alternative Lösungen und Verschnitt-Analyse angeschaut oder als PDF/Excel exportiert werden.
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button onClick={() => onExportPlan('pdf')} variant="outline" className="flex items-center gap-2">
                      <Download className="h-4 w-4" />
                      PDF Export
                    </Button>
                    <Button onClick={() => onExportPlan('excel')} variant="outline" className="flex items-center gap-2">
                      <Download className="h-4 w-4" />
                      Excel Export
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vergleichsergebnisse anzeigen */}
      {comparisonResult && showComparison && <DrumComparisonDisplay comparisonResult={comparisonResult} />}
    </div>
  );
}
