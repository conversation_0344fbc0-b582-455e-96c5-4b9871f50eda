# SystemStatus Tabelle - Setup und Verwaltung

## Überblick

Die `SystemStatus` Tabelle verwaltet den Status aller Systeme, <PERSON>sch<PERSON> und Anlagen, die in der SystemStatusHeatmap angezeigt werden. Diese Tabelle ist bereits im Prisma Schema definiert, muss aber mit den echten Systemen initialisiert werden.

## Tabellen-Schema

```prisma
model SystemStatus {
  id          Int      @id @default(autoincrement())
  system_name String
  status      String   // 'OK', 'WARNING', 'ERROR'
  last_check  DateTime @default(now())
  metadata    String?  // JSON-String mit zusätzlichen Informationen
  created_at  DateTime @default(now())
  updated_at  DateTime
}
```

## Initialisierung der Tabelle

### Option 1: TypeScript Seeder (Empfohlen)

```bash
# Im backend/ Verzeichnis ausführen
cd backend
npx ts-node prisma/seed-system-status.ts
```

### Option 2: SQL Script

```bash
# SQLite Datenbank direkt bearbeiten
sqlite3 path/to/your/database.db < prisma/seed-system-status.sql
```

### Option 3: Prisma Studio

1. Öffne Prisma Studio: `npx prisma studio`
2. Navigiere zur `SystemStatus` Tabelle
3. Füge manuell Systeme hinzu

## Enthaltene Systeme

Nach der Initialisierung enthält die Tabelle folgende Systeme:

### Produktionsanlagen
- Ablängmaschine A01, B02, C03
- Automatisches Ringlager
- Automatisches Trommellager

### IT-Systeme
- SAP System
- Witron WCS
- Siemens SPS
- Backup-System

### Transport & Logistik
- FTS Flotte
- Förderanlage Zone A, B, C
- Scanner Station 1, 2, 3

### Infrastruktur
- Server-01 (Primary)
- Server-02 (Backup)
- Netzwerk Core Switch
- USV System
- Klimaanlage Halle 1, 2

### Qualitätssysteme
- QM-System

## Status-Werte

- **OK**: System funktioniert normal (grün)
- **WARNING**: Leichte Probleme oder Wartung erforderlich (gelb)
- **ERROR**: System nicht verfügbar oder kritischer Fehler (rot)

## Metadata-Struktur

Jedes System kann zusätzliche Metadaten im JSON-Format speichern:

```json
{
  "type": "cutting_machine",
  "location": "Halle 1",
  "model": "A01",
  "capacity_per_hour": 150,
  "utilization": 78.5
}
```

## API-Endpunkte

### Alle Systemstatus abrufen
```
GET /api/stoerungen/system/status
```

### Systemstatus aktualisieren
```
POST /api/stoerungen/system/status
Content-Type: application/json

{
  "system_name": "Ablängmaschine A01",
  "status": "WARNING",
  "metadata": {
    "issue": "Wartung erforderlich",
    "estimated_downtime": "2 hours"
  }
}
```

## Frontend-Integration

Die SystemStatusHeatmap-Komponente (`src/components/charts/SystemStatusHeatmap.tsx`) ruft automatisch die Daten über den `stoerungenService` ab:

```typescript
const data = await stoerungenService.getSystemStatus();
```

## Automatische Updates

Für automatische Status-Updates können Sie:

1. **Cron-Jobs** einrichten, die regelmäßig den Status prüfen
2. **Monitoring-Systeme** integrieren, die bei Problemen den Status aktualisieren
3. **Webhooks** von externen Systemen verwenden
4. **Scheduled Tasks** in der Anwendung implementieren

## Troubleshooting

### Problem: Heatmap zeigt keine Daten
1. Prüfen Sie, ob die SystemStatus-Tabelle Daten enthält:
   ```sql
   SELECT COUNT(*) FROM SystemStatus;
   ```

2. Prüfen Sie die Backend-API:
   ```bash
   curl http://localhost:3001/api/stoerungen/system/status
   ```

3. Prüfen Sie die Browser-Konsole auf Fehler

### Problem: Status wird nicht aktualisiert
1. Prüfen Sie die `last_check` und `updated_at` Timestamps
2. Stellen Sie sicher, dass die API-Aufrufe erfolgreich sind
3. Cache könnte aktiv sein (30 Sekunden TTL)

## Wartung

### Regelmäßige Aufgaben
- Status-Updates für alle Systeme
- Bereinigung alter Einträge (falls gewünscht)
- Backup der Konfiguration

### Monitoring
- Überwachen Sie die API-Performance
- Prüfen Sie regelmäßig die Datenqualität
- Stellen Sie sicher, dass alle kritischen Systeme erfasst sind

## Erweiterung

Um neue Systeme hinzuzufügen:

1. **Über API**:
   ```bash
   curl -X POST http://localhost:3001/api/stoerungen/system/status \
     -H "Content-Type: application/json" \
     -d '{"system_name": "Neues System", "status": "OK"}'
   ```

2. **Über Prisma**:
   ```typescript
   await prisma.systemStatus.create({
     data: {
       system_name: "Neues System",
       status: "OK",
       metadata: JSON.stringify({ type: "new_type" }),
       updated_at: new Date()
     }
   });
   ```

3. **Über Prisma Studio**: Manuell hinzufügen

---

**Hinweis**: Nach der Initialisierung sollten die Mock-Daten in der SystemStatusHeatmap-Komponente nicht mehr verwendet werden. Die Komponente wurde bereits entsprechend angepasst.