import { Request, Response } from 'express';
import { WorkflowService } from '../services/workflowService';
import { WorkflowLogEntry } from '../types/workflow.types';

export class WorkflowController {
  private workflowService: WorkflowService;

  constructor() {
    this.workflowService = new WorkflowService();
  }

  /**
   * GET /api/workflows/logs
   * Lädt alle Workflow-Logs oder Logs für einen bestimmten Workflow
   */
  async getLogs(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId, limit = 100 } = req.query;
      
      let logs: WorkflowLogEntry[];
      
      if (workflowId && typeof workflowId === 'string') {
        logs = await this.workflowService.getWorkflowLogs(workflowId, Number(limit));
      } else {
        logs = await this.workflowService.getAllLogs(Number(limit));
      }

      res.json({
        success: true,
        data: logs,
        count: logs.length
      });
    } catch (error) {
      console.error('[WorkflowController] <PERSON><PERSON> beim <PERSON>den der Logs:', error);
      res.status(500).json({
        success: false,
        error: '<PERSON><PERSON> beim Laden der Workflow-Logs',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/workflows/processes
   * Lädt alle verfügbaren Workflow-Prozesse
   */
  async getProcesses(req: Request, res: Response): Promise<void> {
    try {
      const processes = await this.workflowService.getProcesses();
      
      res.json({
        success: true,
        data: processes,
        count: processes.length
      });
    } catch (error) {
      console.error('[WorkflowController] Fehler beim Laden der Prozesse:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Laden der Workflow-Prozesse',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * POST /api/workflows/execute
   * Führt einen Workflow-Prozess aus
   */
  async executeProcess(req: Request, res: Response): Promise<void> {
    try {
      const { processId } = req.body;
      
      if (!processId) {
        res.status(400).json({
          success: false,
          error: 'Prozess-ID ist erforderlich'
        });
        return;
      }

      const result = await this.workflowService.executeProcess(processId);
      
      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('[WorkflowController] Fehler beim Ausführen des Prozesses:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Ausführen des Workflow-Prozesses',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/workflows/stats
   * Lädt Workflow-Statistiken
   */
  async getStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await this.workflowService.getStats();
      
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('[WorkflowController] Fehler beim Laden der Statistiken:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Laden der Workflow-Statistiken',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/workflows/executions
   * Lädt Workflow-Ausführungen
   */
  async getExecutions(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId, limit = 50 } = req.query;
      
      const executions = await this.workflowService.getExecutions(
        workflowId as string,
        Number(limit)
      );
      
      res.json({
        success: true,
        data: executions,
        count: executions.length
      });
    } catch (error) {
      console.error('[WorkflowController] Fehler beim Laden der Ausführungen:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Laden der Workflow-Ausführungen',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }
}