#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');
const os = require('os');
const fs = require('fs');

// Funktion zum Überprüfen, ob wir in WSL oder Windows sind
function isWSL() {
  if (process.platform !== 'linux') return false;
  
  try {
    const osRelease = fs.readFileSync('/proc/version', 'utf8');
    return osRelease.toLowerCase().includes('microsoft') || osRelease.toLowerCase().includes('wsl');
  } catch (err) {
    return false;
  }
}

// Hauptfunktion zum Starten der App
function startApp() {
  console.log('Starte Shopfloor Management Dashboard...');
  
  try {
    if (isWSL()) {
      console.log('WSL-Umgebung erkannt. Starte in WSL-Modus...');
      // In WSL verwenden wir den direkten Pfad zu electron-forge
      const npxPath = path.join(__dirname, 'node_modules', '.bin', 'electron-forge');
      execSync(`${npxPath} start`, { stdio: 'inherit' });
    } else {
      console.log('Windows-Umgebung erkannt. Starte in Windows-Modus...');
      // In Windows verwenden wir npx, um electron-forge zu finden
      execSync('npx electron-forge start', { stdio: 'inherit' });
    }
  } catch (error) {
    console.error('Fehler beim Starten der Anwendung:', error.message);
    
    if (!isWSL() && error.message.includes('electron-forge')) {
      console.log('\nEs scheint, dass electron-forge nicht gefunden wurde.');
      console.log('Versuche, es lokal zu installieren...');
      
      try {
        execSync('npm install --no-save @electron-forge/cli', { stdio: 'inherit' });
        console.log('Installation abgeschlossen. Starte die Anwendung erneut...');
        execSync('npx electron-forge start', { stdio: 'inherit' });
      } catch (installError) {
        console.error('Fehler bei der Installation:', installError.message);
        console.log('\nBitte führen Sie "npm install" aus und versuchen Sie es erneut.');
      }
    }
  }
}

// Starte die App
startApp();
