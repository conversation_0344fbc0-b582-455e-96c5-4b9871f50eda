"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRepository = void 0;
const client_1 = require("@prisma-sfm-dashboard/client");
class UserRepository {
    constructor() {
        this.prisma = new client_1.PrismaClient();
    }
    async findByEmailOrUsername(email, username) {
        return await this.prisma.user.findFirst({
            where: {
                OR: [
                    { email: email },
                    { username: username }
                ]
            }
        });
    }
    async findByEmail(email) {
        return await this.prisma.user.findUnique({
            where: { email }
        });
    }
    async findByUsername(username) {
        return await this.prisma.user.findUnique({
            where: { username },
            include: { roles: true } // Rollen mit laden
        });
    }
    async createUser(userData) {
        return await this.prisma.user.create({
            data: userData
        });
    }
    async findById(id) {
        return await this.prisma.user.findUnique({
            where: { id },
            include: { roles: true } // Rollen mit laden
        });
    }
}
exports.UserRepository = UserRepository;
