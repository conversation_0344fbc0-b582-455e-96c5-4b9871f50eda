# JASZ - Leitstand App Dokumentation

## Übersicht

JASZ ist der AI-Assistant für die Leitstand-Anwendu<PERSON> von <PERSON>pp. Die Anwendung dient der Überwachung und Verwaltung von Störungen, Workflows und KPI-Metriken in der Produktionsumgebung.

## Hauptbereiche der Anwendung

### 1. Störungsmanagement (STÖRUNGEN)

#### Live Monitoring
- **Zweck**: Echtzeit-Überwachung aller Systemzustände
- **Funktionen**:
  - System-Status Heatmap: Zeigt den aktuellen Zustand aller Systeme in einer visuellen Karte
  - Kritische Systeme werden rot markiert, stabile Systeme grün
  - Interaktive Übersicht mit Drill-Down-Möglichkeiten
- **Anwendung**: Ermöglicht sofortige Erkennung von Problemen und deren Priorisierung

#### Störungsmanagement
- **Zweck**: Zentrale Verwaltung aller Störungsprozesse
- **Funktionen**:
  - Störungen erfassen und bearbeiten
  - Bereitschaftspläne verwalten
  - Eskalationsprozesse steuern
  - Team-Zuordnungen konfigurieren
- **Workflows**: 
  - Neue Störung melden → Automatische Zuweisung → Bearbeitung → Lösung → Dokumentation

#### Analytics Dashboard
- **Zweck**: Umfassende Analyse der Störungsdaten
- **KPI-Metriken**:
  - **Gesamt Störungen**: Gesamtanzahl aller erfassten Störungen im Zeitraum
  - **Aktive Störungen**: Derzeit ungelöste Störungen
  - **Gelöste Störungen**: Erfolgreich behobene Störungen
  - **MTTR (Mean Time To Repair)**: Durchschnittliche Reparaturzeit
  - **MTTA (Mean Time To Acknowledge)**: Durchschnittliche Bestätigungszeit
  - **Erstlösungsrate**: Prozent der beim ersten Versuch gelösten Störungen

#### Runbooks
- **Zweck**: Standardisierte Prozeduren für die Störungsbehebung
- **Funktionen**:
  - Runbooks erstellen und bearbeiten
  - Schritt-für-Schritt Anleitungen
  - Best Practices dokumentieren
  - Prozeduren verwalten

### 2. Workflow-Management (WORKFLOWS)

#### SAP Workflows
- **Zweck**: Verwaltung SAP-basierter Automatisierungen
- **Funktionen**:
  - Workflow-Status in Echtzeit überwachen
  - Automatisierte Prozesse verwalten
  - Konfigurationen anpassen
  - System-Integrationen überwachen

#### Workflow Übersicht
- **Zweck**: Gesamtüberblick aller Workflow-Systeme
- **Funktionen**:
  - Status aller Workflows
  - Performance-Übersicht
  - Abhängigkeiten visualisieren
  - Gesamtsystem-Gesundheit bewerten

#### Performance Analytics
- **Zweck**: Detaillierte Performance-Analyse
- **Funktionen**:
  - Workflow-Performance messen
  - Bottlenecks identifizieren
  - Trend-Analysen durchführen
  - Optimierungspotentiale erkennen

#### System Logs
- **Zweck**: Umfassende Log-Analyse
- **Funktionen**:
  - System-Logs einsehen
  - Fehler-Protokolle analysieren
  - Debug-Informationen auswerten
  - Audit-Trails verfolgen

## KPI-Definitionen und Berechnungen

### Störungs-KPIs

1. **MTTR (Mean Time To Repair)**
   - Definition: Durchschnittliche Zeit von der Störungsmeldung bis zur vollständigen Behebung
   - Berechnung: Summe aller Reparaturzeiten / Anzahl gelöster Störungen
   - Zielwert: < 4 Stunden für kritische Störungen

2. **MTTA (Mean Time To Acknowledge)**
   - Definition: Durchschnittliche Zeit bis zur ersten Reaktion auf eine Störung
   - Berechnung: Summe aller Reaktionszeiten / Anzahl aller Störungen
   - Zielwert: < 15 Minuten für kritische Störungen

3. **Erstlösungsrate (First Time Fix Rate)**
   - Definition: Prozentsatz der Störungen, die beim ersten Bearbeitungsversuch gelöst wurden
   - Berechnung: (Störungen beim ersten Versuch gelöst / Gesamtzahl Störungen) × 100
   - Zielwert: > 80%

4. **Verfügbarkeit**
   - Definition: Prozentsatz der Zeit, in der Systeme verfügbar waren
   - Berechnung: (Verfügbare Zeit / Gesamtzeit) × 100
   - Zielwert: > 99.5%

## Bereitschaftsdienst

### Bereitschaftsplanung
- **Zweck**: Sicherstellung 24/7-Verfügbarkeit von Experten
- **Funktionen**:
  - Schichtpläne erstellen
  - Vertretungsregelungen definieren
  - Eskalationsmatrix konfigurieren
  - Kontaktdaten verwalten

### Eskalationsprozesse
1. **Level 1**: Bereitschaftsdienst (0-15 Min)
2. **Level 2**: Teamleiter (15-30 Min)  
3. **Level 3**: Management (30-60 Min)
4. **Level 4**: Externe Experten (> 60 Min)

## Systemarchitektur

### Frontend
- React-basierte Anwendung
- Responsive Design für alle Geräte
- Echtzeit-Updates via WebSocket
- Modulare Komponentenarchitektur

### Backend
- Node.js/Express API
- SQLite Datenbank
- RESTful Services
- RAG-System für AI-Unterstützung

### AI-Integration
- **JASZ AI-Assistant**: Kontextuelle Hilfe und Analyse
- **RAG-System**: Wissensdatenbank mit Quellenangaben
- **OpenRouter API**: Externe AI-Modell Integration

## Troubleshooting

### Häufige Probleme und Lösungen

1. **System reagiert nicht**
   - Prüfe Netzwerkverbindung
   - Kontrolliere Backend-Status
   - Lade Seite neu (Ctrl+F5)

2. **KPIs werden nicht aktualisiert**
   - Prüfe Datenbank-Verbindung
   - Kontrolliere Daten-Import-Prozesse
   - Überprüfe Zeitstempel der letzten Aktualisierung

3. **Charts laden nicht**
   - Prüfe Browser-Konsole auf JavaScript-Fehler
   - Kontrolliere API-Endpoints
   - Verifiziere Datenformat

## Best Practices

### Störungsbearbeitung
1. **Sofortige Bestätigung**: Störungen innerhalb von 15 Minuten bestätigen
2. **Klare Kommunikation**: Status-Updates alle 30 Minuten
3. **Dokumentation**: Alle Schritte zur Problemlösung dokumentieren
4. **Post-Incident Review**: Nach kritischen Störungen Lessons Learned erstellen

### Monitoring
1. **Proaktive Überwachung**: Präventive Checks statt reaktive Maßnahmen
2. **Automatisierung**: Wiederkehrende Tasks automatisieren
3. **Dashboards**: Wichtige Metriken immer sichtbar halten
4. **Alerts**: Intelligente Benachrichtigungen ohne False Positives

## Integration mit externen Systemen

### SAP-Integration
- Automatischer Datenimport aus SAP-Systemen
- Workflow-Synchronisation
- Echtzeit-Status-Updates

### Monitoring-Tools
- Integration mit bestehenden Monitoring-Lösungen
- Automatische Störungserkennung
- Korrelation von Events aus verschiedenen Quellen

## Sicherheit und Compliance

### Datenschutz
- Alle Daten werden lokal gespeichert
- Keine sensiblen Daten in Cloud-Services
- Zugriffskontrollen nach Least-Privilege-Prinzip

### Audit-Trails
- Vollständige Protokollierung aller Änderungen
- Nachverfolgbare Benutzeraktionen
- Compliance mit internen Richtlinien