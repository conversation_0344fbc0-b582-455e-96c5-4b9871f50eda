/**
 * AI Service Health Hook Tests
 */

import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

import { useAIServiceHealth, useAIServiceStatus, useAISystemHealth } from '../useAIServiceHealth';

// Mock AI services
vi.mock('../../services', () => ({
  RAGService: vi.fn().mockImplementation(() => ({
    healthCheck: vi.fn().mockResolvedValue({
      isHealthy: true,
      status: 'healthy',
      lastCheck: new Date(),
      uptime: 3600000,
      version: '1.0.0',
      performance: {
        averageResponseTime: 250,
        successRate: 0.95,
        totalRequests: 1000,
        cacheHitRate: 0.8
      }
    })
  })),
  CuttingOptimizerService: vi.fn().mockImplementation(() => ({
    healthCheck: vi.fn().mockResolvedValue({
      isHealthy: true,
      status: 'healthy',
      lastCheck: new Date(),
      uptime: 3600000,
      version: '1.0.0',
      performance: {
        averageResponseTime: 500,
        successRate: 0.92,
        totalRequests: 500,
        cacheHitRate: 0.7
      }
    })
  })),
  InventoryIntelligenceService: vi.fn().mockImplementation(() => ({
    healthCheck: vi.fn().mockResolvedValue({
      isHealthy: false,
      status: 'error',
      lastCheck: new Date(),
      uptime: 1800000,
      version: '1.0.0',
      performance: {
        averageResponseTime: 1000,
        successRate: 0.75,
        totalRequests: 200,
        cacheHitRate: 0.5
      }
    })
  })),
  WarehouseOptimizerService: vi.fn().mockImplementation(() => ({
    healthCheck: vi.fn().mockResolvedValue({
      isHealthy: true,
      status: 'healthy',
      lastCheck: new Date(),
      uptime: 3600000,
      version: '1.0.0'
    })
  })),
  SupplyChainOptimizerService: vi.fn().mockImplementation(() => ({
    healthCheck: vi.fn().mockResolvedValue({
      isHealthy: true,
      status: 'healthy',
      lastCheck: new Date(),
      uptime: 3600000,
      version: '1.0.0'
    })
  })),
  ReportingService: vi.fn().mockImplementation(() => ({
    healthCheck: vi.fn().mockResolvedValue({
      isHealthy: true,
      status: 'healthy',
      lastCheck: new Date(),
      uptime: 3600000,
      version: '1.0.0'
    })
  }))
}));

describe('useAIServiceHealth', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('initializes with loading state', () => {
    const { result } = renderHook(() => useAIServiceHealth());

    expect(result.current.isLoading).toBe(true);
    expect(result.current.serviceStatuses).toEqual({});
    expect(result.current.error).toBe(null);
    expect(result.current.lastUpdated).toBe(null);
  });

  it('loads service statuses successfully', async () => {
    const { result } = renderHook(() => useAIServiceHealth());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.serviceStatuses).toBeDefined();
    expect(Object.keys(result.current.serviceStatuses)).toContain('chat');
    expect(Object.keys(result.current.serviceStatuses)).toContain('cutting');
    expect(Object.keys(result.current.serviceStatuses)).toContain('inventory');
    expect(result.current.error).toBe(null);
    expect(result.current.lastUpdated).toBeInstanceOf(Date);
  });

  it('handles service health check failures gracefully', async () => {
    // Mock one service to fail
    const { RAGService } = await import('../../services');
    const mockRAGService = new (RAGService as any)();
    mockRAGService.healthCheck.mockRejectedValue(new Error('Service unavailable'));

    const { result } = renderHook(() => useAIServiceHealth());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should still have service statuses, but with failed status for the failing service
    expect(result.current.serviceStatuses.chat).toBeDefined();
    expect(result.current.serviceStatuses.chat.isHealthy).toBe(false);
    expect(result.current.error).toBe(null); // Individual service failures don't cause overall error
  });

  it('refreshes statuses manually', async () => {
    const { result } = renderHook(() => useAIServiceHealth());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    const initialLastUpdated = result.current.lastUpdated;

    // Wait a bit and refresh
    vi.advanceTimersByTime(1000);
    await result.current.refreshStatuses();

    await waitFor(() => {
      expect(result.current.lastUpdated).not.toEqual(initialLastUpdated);
    });
  });

  it('auto-refreshes statuses periodically', async () => {
    const { result } = renderHook(() => useAIServiceHealth());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    const initialLastUpdated = result.current.lastUpdated;

    // Fast-forward 5 minutes
    vi.advanceTimersByTime(5 * 60 * 1000);

    await waitFor(() => {
      expect(result.current.lastUpdated).not.toEqual(initialLastUpdated);
    });
  });

  it('cleans up intervals on unmount', () => {
    const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
    const { unmount } = renderHook(() => useAIServiceHealth());

    unmount();

    expect(clearIntervalSpy).toHaveBeenCalled();
  });
});

describe('useAIServiceStatus', () => {
  it('returns status for specific service', async () => {
    const { result } = renderHook(() => useAIServiceStatus('chat'));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.status).toBeDefined();
    expect(result.current.status?.isHealthy).toBe(true);
  });

  it('returns undefined for non-existent service', async () => {
    const { result } = renderHook(() => useAIServiceStatus('non-existent'));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.status).toBeUndefined();
  });
});

describe('useAISystemHealth', () => {
  it('calculates system health metrics correctly', async () => {
    const { result } = renderHook(() => useAISystemHealth());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.systemHealth).toBeDefined();
    expect(result.current.systemHealth.totalServices).toBeGreaterThan(0);
    expect(result.current.systemHealth.healthyServices).toBeGreaterThan(0);
    expect(result.current.healthPercentage).toBeGreaterThan(0);
    expect(result.current.healthPercentage).toBeLessThanOrEqual(100);
  });

  it('calculates performance metrics correctly', async () => {
    const { result } = renderHook(() => useAISystemHealth());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    const { systemHealth } = result.current;
    expect(systemHealth.averageResponseTime).toBeGreaterThan(0);
    expect(systemHealth.totalRequests).toBeGreaterThan(0);
    expect(systemHealth.overallSuccessRate).toBeGreaterThan(0);
    expect(systemHealth.overallSuccessRate).toBeLessThanOrEqual(1);
  });

  it('handles empty service statuses', () => {
    // Mock empty service statuses
    vi.doMock('../useAIServiceHealth', () => ({
      useAIServiceHealth: () => ({
        serviceStatuses: {},
        isLoading: false,
        error: null
      })
    }));

    const { result } = renderHook(() => useAISystemHealth());

    expect(result.current.systemHealth.totalServices).toBe(0);
    expect(result.current.healthPercentage).toBe(0);
  });
});