import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import * as ragSchema from './rag-schema';
import * as ragRelations from './rag-relations';
import path from 'path';

// Pfad zur RAG Knowledge SQLite-Datenbankdatei
const dbPath = path.join(__dirname, '..', '..', 'backend', 'database', 'rag_knowledge.db');

// SQLite-Verbindung mit wichtigen PRAGMA-Einstellungen
const sqlite = new Database(dbPath);

// Wichtige PRAGMA-Anweisungen für Performance und Integrität
sqlite.pragma('journal_mode = WAL');
sqlite.pragma('synchronous = NORMAL');
sqlite.pragma('foreign_keys = ON');

// Drizzle-Instanz mit dem RAG-Schema erstellen
export const ragDb = drizzle(sqlite, { 
  schema: { ...ragSchema, ...ragRelations },
  logger: process.env.NODE_ENV === 'development'
});

// Export SQLite-Instanz für direkte Queries falls nötig
export const ragSqliteDB = sqlite;

export * from './rag-schema';
export * from './rag-relations';
