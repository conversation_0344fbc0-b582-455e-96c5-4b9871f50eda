import os
import subprocess
import time
import traceback
import logging
from logging.handlers import RotatingFileHandler
import win32com.client
import pandas as pd
import sqlite3
from datetime import datetime

# --- METADATEN / LOGGING ---
WORKFLOW_ID = "bestand"
SCRIPT_DATE = datetime.now()

# Basisverzeichnis für Logs relativ zum Repo (falls Verzeichnis nicht existiert, wird es erstellt)
LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "..", "workflows", "logs")
os.makedirs(LOG_DIR, exist_ok=True)
LOG_FILENAME = os.path.join(LOG_DIR, f"Bestand_Workflow_Log_{SCRIPT_DATE.strftime('%Y%m%d_%H%M%S')}.txt")

def _setup_logging():
    logger = logging.getLogger(WORKFLOW_ID)
    logger.setLevel(logging.INFO)
    # Doppeltes Handler-Setup vermeiden
    if not logger.handlers:
        formatter = logging.Formatter("%(asctime)s | workflow=%(name)s | level=%(levelname)s | step=%(module)s.%(funcName)s | %(message)s")
        file_handler = RotatingFileHandler(LOG_FILENAME, maxBytes=5 * 1024 * 1024, backupCount=5, encoding="utf-8")
        file_handler.setFormatter(formatter)
        stream_handler = logging.StreamHandler()
        stream_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        logger.addHandler(stream_handler)
    return logger

logger = _setup_logging()

# --- KONFIGURATION ---
SAP_EXECUTABLE_PATH = r"C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe"
SAP_SYSTEM_ID = "PS4"
SAP_CLIENT = "009"
SAP_LANGUAGE = "DE"
DB_PATH = "sap_datenbank.db"  # Hinweis: optional relativ auf Daten-Ordner anpassen

# Standard-Parameter (können später per Umgebungsvariablen/Args überschrieben werden)
DEFAULT_LGNUM = os.environ.get("LGNUM", "512")

# -- Konfiguration für den BST 240 Prozess --
FIRST_TCODE = "/nlx03"
FIRST_EXPORT_DIR = os.environ.get("EXPORT_DIR_240", r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\BST")
FIRST_EXPORT_BASENAME = "BST_240"
FIRST_DB_TABLE = "lx03_bin_status"
FIRST_LGTYP_LOW = os.environ.get("LGTYP_240", "240")

# -- Konfiguration für den BST 200 Prozess --
SECOND_TCODE = "/nlx03"
SECOND_EXPORT_DIR = os.environ.get("EXPORT_DIR_200", r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\BST")
SECOND_EXPORT_BASENAME = "BST_200"
SECOND_DB_TABLE = "lx03_bin_status"
SECOND_LGTYP_LOW = os.environ.get("LGTYP_200", "200")

# -- Konfiguration für den BST REST Prozess --
THIRD_TCODE = "/nlx03"
THIRD_EXPORT_DIR = os.environ.get("EXPORT_DIR_REST", r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\BST")
THIRD_EXPORT_BASENAME = "BST_REST"
THIRD_DB_TABLE = "lx03_bin_status"
THIRD_LGTYP_LOW = os.environ.get("LGTYP_REST_LOW", "241")
THIRD_LGTYP_HIGH = os.environ.get("LGTYP_REST_HIGH", "999")
# --- ENDE DER KONFIGURATION ---


def close_excel():
    """Schließt alle laufenden Excel-Prozesse robust mit taskkill."""
    logger.info("Versuche, alle Excel-Prozesse zu schließen...")
    try:
        subprocess.run(['taskkill', '/F', '/IM', 'excel.exe'], check=True, capture_output=True, text=True)
        logger.info("Excel wurde erfolgreich geschlossen.")
    except subprocess.CalledProcessError as e:
        if e.stderr and ("not found" in e.stderr or "Der Prozess" in e.stderr):
            logger.info("Excel war nicht geöffnet, nichts zu tun.")
        else:
            logger.error(f"Fehler beim Schließen von Excel: {e.stderr}")
    except FileNotFoundError:
        logger.error("Fehler: Der Befehl 'taskkill' wurde nicht gefunden.")


def wait_for_element(session, element_id, timeout=30):
    """Wartet aktiv darauf, dass ein UI-Element in der SAP-Sitzung erscheint."""
    logger.info(f"Warte auf Element mit ID: {element_id} (Timeout {timeout}s)...")
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            element = session.findById(element_id)
            logger.info("Element gefunden.")
            return element
        except Exception:
            time.sleep(0.5)
    raise TimeoutError(f"Zeitüberschreitung: Element '{element_id}' wurde nach {timeout} Sekunden nicht gefunden.")


def get_sap_session():
    """Stellt eine Verbindung zu einer SAP-Sitzung her oder startet SAP."""
    logger.info("Verbinde mit SAP GUI...")
    try:
        sap_gui_auto = win32com.client.GetObject("SAPGUI")
        application = sap_gui_auto.GetScriptingEngine
        connection = application.Children(0)
        session = connection.Children(0)
        logger.info("Bestehende SAP GUI-Instanz gefunden.")
    except Exception:
        logger.info("SAP GUI nicht gefunden. Starte SAP über sapshcut.exe...")
        command = f'"{SAP_EXECUTABLE_PATH}" -system="{SAP_SYSTEM_ID}" -client="{SAP_CLIENT}" -language="{SAP_LANGUAGE}" -maxgui'
        subprocess.Popen(command)
        logger.info("Warte 15 Sekunden, bis SAP gestartet ist...")
        time.sleep(15)
        sap_gui_auto = win32com.client.GetObject("SAPGUI")
        application = sap_gui_auto.GetScriptingEngine
        connection = application.Children(0)
        session = connection.Children(0)
    wait_for_element(session, "wnd[0]")
    logger.info("SAP-Sitzung erfolgreich verbunden und bereit.")
    return session


def run_first_process(session):
    """Führt den LX03 Lagertyp 240 SAP-Prozess aus."""
    try:
        logger.info("Starte Prozess: LX03 Lagertyp 240")
        timestamp_str = datetime.now().strftime("%d%m%Y_%H%M%S")
        export_full_filename = f"{FIRST_EXPORT_BASENAME}_{timestamp_str}.xlsx"

        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(1)
        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(2)

        logger.info(f"Starte Transaktion: {FIRST_TCODE}")
        session.findById("wnd[0]/tbar[0]/okcd").text = FIRST_TCODE
        session.findById("wnd[0]").sendVKey(0)
        time.sleep(2)

        # Felder ausfüllen
        wait_for_element(session, "wnd[0]/usr/chkPMITB").selected = True
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGNUM").text = DEFAULT_LGNUM
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGTYP-LOW").text = FIRST_LGTYP_LOW
        
        # Bericht ausführen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[8]").press()
        time.sleep(2)

        # Layout auswählen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[33]").press()
        time.sleep(2)

        label = wait_for_element(session, "wnd[1]/usr/lbl[14,5]")
        label.setFocus()
        session.findById("wnd[1]").sendVKey(2)  # "Auswählen"
        time.sleep(2)

        # Export
        wait_for_element(session, "wnd[0]").findById("mbar/menu[0]/menu[1]/menu[1]").select()
        time.sleep(2)

        export_config_field = wait_for_element(session, "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME")
        export_config_field.text = FIRST_EXPORT_BASENAME
        session.findById("wnd[1]/tbar[0]/btn[20]").press()
        time.sleep(2)

        path_field = wait_for_element(session, "wnd[1]/usr/ctxtDY_PATH")
        os.makedirs(FIRST_EXPORT_DIR, exist_ok=True)
        path_field.text = FIRST_EXPORT_DIR
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = export_full_filename
        session.findById("wnd[1]/tbar[0]/btn[0]").press()

        full_export_path = os.path.join(FIRST_EXPORT_DIR, export_full_filename)
        logger.info(f"Export gestartet: {full_export_path}")
        time.sleep(5)
        
        logger.info("Prozess LX03 Lagertyp 240 abgeschlossen.")
        return full_export_path
    except Exception as e:
        logger.exception(f"Fehler im Prozess LX03 Lagertyp 240: {e}")
        return None


def write_excel_to_sqlite(excel_path, db_path, table_name):
    """Liest eine Excel-Datei und schreibt sie in eine SQLite-Datenbank."""
    if not excel_path or not os.path.exists(excel_path):
        logger.error(f"Excel-Datei nicht gefunden: {excel_path}")
        return False
    try:
        logger.info(f"Lese Daten aus Excel: {excel_path}")
        df = pd.read_excel(excel_path, engine='openpyxl')
        logger.info(f"Excel gelesen. Zeilen: {df.shape[0]}, Spalten: {df.shape[1]}")
        conn = sqlite3.connect(db_path)
        df.to_sql(table_name, conn, if_exists='replace', index=False)
        conn.close()
        logger.info(f"Daten erfolgreich in Tabelle '{table_name}' geschrieben.")
        return True
    except Exception as e:
        logger.exception(f"Fehler beim Verarbeiten der Datei: {e}")
        return False

def run_second_process(session):
    """Führt den LX03 Lagertyp 200 SAP-Prozess aus."""
    try:
        logger.info("Starte Prozess: LX03 Lagertyp 200")
        timestamp_str = datetime.now().strftime("%d%m%Y_%H%M%S")
        export_full_filename = f"{SECOND_EXPORT_BASENAME}_{timestamp_str}.xlsx"

        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(1)
        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(2)

        logger.info(f"Starte Transaktion: {SECOND_TCODE}")
        session.findById("wnd[0]/tbar[0]/okcd").text = SECOND_TCODE
        session.findById("wnd[0]").sendVKey(0)
        time.sleep(2)

        # Felder ausfüllen
        wait_for_element(session, "wnd[0]/usr/chkPMITB").selected = True
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGNUM").text = DEFAULT_LGNUM
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGTYP-LOW").text = SECOND_LGTYP_LOW
        
        # Bericht ausführen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[8]").press()
        time.sleep(2)

        # Layout auswählen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[33]").press()
        time.sleep(2)

        label = wait_for_element(session, "wnd[1]/usr/lbl[14,5]")
        label.setFocus()
        session.findById("wnd[1]").sendVKey(2)  # "Auswählen"
        time.sleep(2)

        # Export
        wait_for_element(session, "wnd[0]").findById("mbar/menu[0]/menu[1]/menu[1]").select()
        time.sleep(2)

        export_config_field = wait_for_element(session, "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME")
        export_config_field.text = SECOND_EXPORT_BASENAME
        session.findById("wnd[1]/tbar[0]/btn[20]").press()
        time.sleep(2)

        path_field = wait_for_element(session, "wnd[1]/usr/ctxtDY_PATH")
        os.makedirs(SECOND_EXPORT_DIR, exist_ok=True)
        path_field.text = SECOND_EXPORT_DIR
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = export_full_filename
        session.findById("wnd[1]/tbar[0]/btn[0]").press()

        full_export_path = os.path.join(SECOND_EXPORT_DIR, export_full_filename)
        logger.info(f"Export gestartet: {full_export_path}")
        time.sleep(5)
        
        logger.info("Prozess LX03 Lagertyp 200 abgeschlossen.")
        return full_export_path
    except Exception as e:
        logger.exception(f"Fehler im Prozess LX03 Lagertyp 200: {e}")
        return None


def write_excel_to_sqlite(excel_path, db_path, table_name):
    """Liest eine Excel-Datei und schreibt sie in eine SQLite-Datenbank."""
    if not excel_path or not os.path.exists(excel_path):
        print(f"Fehler: Die Excel-Datei '{excel_path}' wurde nicht gefunden.")
        return False
    try:
        print(f"Lese Daten aus '{excel_path}'...")
        df = pd.read_excel(excel_path, engine='openpyxl')
        conn = sqlite3.connect(db_path)
        df.to_sql(table_name, conn, if_exists='replace', index=False)
        conn.close()
        print(f"Daten erfolgreich in Tabelle '{table_name}' geschrieben.")
        return True
    except Exception as e:
        print(f"Ein Fehler beim Verarbeiten der Datei ist aufgetreten: {e}")
        return False
    
def run_third_process(session):
    """Führt den LX03 Lagertyp REST SAP-Prozess aus (241-999)."""
    try:
        logger.info("Starte Prozess: LX03 Lagertyp REST (241-999)")
        timestamp_str = datetime.now().strftime("%d%m%Y_%H%M%S")
        export_full_filename = f"{THIRD_EXPORT_BASENAME}_{timestamp_str}.xlsx"

        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(1)
        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        time.sleep(2)

        logger.info(f"Starte Transaktion: {THIRD_TCODE}")
        session.findById("wnd[0]/tbar[0]/okcd").text = THIRD_TCODE
        session.findById("wnd[0]").sendVKey(0)
        time.sleep(2)

        # Felder ausfüllen
        wait_for_element(session, "wnd[0]/usr/chkPMITB").selected = True
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGNUM").text = DEFAULT_LGNUM
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGTYP-LOW").text = THIRD_LGTYP_LOW
        wait_for_element(session, "wnd[0]/usr/ctxtS1_LGTYP-HIGH").text = THIRD_LGTYP_HIGH
        
        # Bericht ausführen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[8]").press()
        time.sleep(2)

        # Layout auswählen
        wait_for_element(session, "wnd[0]/tbar[1]/btn[33]").press()
        time.sleep(2)

        label = wait_for_element(session, "wnd[1]/usr/lbl[14,5]")
        label.setFocus()
        session.findById("wnd[1]").sendVKey(2)  # "Auswählen"
        time.sleep(2)

        # Export
        wait_for_element(session, "wnd[0]").findById("mbar/menu[0]/menu[1]/menu[1]").select()
        time.sleep(2)

        export_config_field = wait_for_element(session, "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME")
        export_config_field.text = THIRD_EXPORT_BASENAME
        session.findById("wnd[1]/tbar[0]/btn[20]").press()
        time.sleep(2)

        path_field = wait_for_element(session, "wnd[1]/usr/ctxtDY_PATH")
        os.makedirs(THIRD_EXPORT_DIR, exist_ok=True)
        path_field.text = THIRD_EXPORT_DIR
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = export_full_filename
        session.findById("wnd[1]/tbar[0]/btn[0]").press()

        full_export_path = os.path.join(THIRD_EXPORT_DIR, export_full_filename)
        logger.info(f"Export gestartet: {full_export_path}")
        time.sleep(5)
        
        logger.info("Prozess LX03 Lagertyp REST abgeschlossen.")
        return full_export_path
    except Exception as e:
        logger.exception(f"Fehler im Prozess LX03 Lagertyp REST: {e}")
        return None


# Duplikat vermeiden – Funktion bereits oben definiert
    
if __name__ == "__main__":
    logger.info("=" * 20 + " STARTE WORKFLOW BESTAND " + "=" * 20)
    overall_success = True
    try:
        sap_session = get_sap_session()
        if sap_session:
            # --- LX03 LAGERTYP 240 ---
            logger.info("=" * 10 + " STARTE: LX03 LAGERTYP 240 " + "=" * 10)
            file_240 = run_first_process(sap_session)
            if file_240:
                if write_excel_to_sqlite(file_240, DB_PATH, FIRST_DB_TABLE):
                    logger.info("Import für 240 abgeschlossen.")
                else:
                    overall_success = False
                logger.info("Warte 3 Sekunden für Excel-Cleanup...")
                time.sleep(3)
                close_excel()
            else:
                overall_success = False

            # --- LX03 LAGERTYP 200 ---
            logger.info("=" * 10 + " STARTE: LX03 LAGERTYP 200 " + "=" * 10)
            file_200 = run_second_process(sap_session)
            if file_200:
                if write_excel_to_sqlite(file_200, DB_PATH, SECOND_DB_TABLE):
                    logger.info("Import für 200 abgeschlossen.")
                else:
                    overall_success = False
                logger.info("Warte 3 Sekunden für Excel-Cleanup...")
                time.sleep(3)
                close_excel()
            else:
                overall_success = False

            # --- LX03 LAGERTYP REST ---
            logger.info("=" * 10 + " STARTE: LX03 LAGERTYP REST " + "=" * 10)
            file_rest = run_third_process(sap_session)
            if file_rest:
                if write_excel_to_sqlite(file_rest, DB_PATH, THIRD_DB_TABLE):
                    logger.info("Import für REST abgeschlossen.")
                else:
                    overall_success = False
                logger.info("Warte 3 Sekunden für Excel-Cleanup...")
                time.sleep(3)
                close_excel()
            else:
                overall_success = False

            if overall_success:
                logger.info("Alle Prozesse erfolgreich abgeschlossen!")
            else:
                logger.error("Ein oder mehrere Prozesse sind fehlgeschlagen. Siehe Log für Details.")
    except Exception as e:
        logger.exception(f"Kritischer Fehler im Hauptprozess: {e}")
    finally:
        logger.info(f"LOG_DATEI: {LOG_FILENAME}")