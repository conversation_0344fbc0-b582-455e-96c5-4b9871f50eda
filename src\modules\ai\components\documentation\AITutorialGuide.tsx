import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Play,
  Pause,
  SkipForward,
  SkipBack,
  X,
  CheckCircle,
  Circle,
  ArrowRight,
  ArrowLeft,
  Target,
  Clock,
  BookOpen
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useAIDocumentation, Tutorial, TutorialStep } from './AIDocumentationProvider';

interface AITutorialGuideProps {
  className?: string;
}

export const AITutorialGuide: React.FC<AITutorialGuideProps> = ({ className }) => {
  const { t } = useTranslation();
  const {
    currentTutorial,
    currentStep,
    nextStep,
    previousStep,
    completeTutorial,
    exitTutorial
  } = useAIDocumentation();

  const [isPlaying, setIsPlaying] = useState(false);
  const [highlightedElement, setHighlightedElement] = useState<string | null>(null);

  useEffect(() => {
    if (currentTutorial && isPlaying) {
      const step = currentTutorial.steps[currentStep];
      if (step?.action?.target) {
        setHighlightedElement(step.action.target);
        highlightElement(step.action.target);
      }
    }
  }, [currentTutorial, currentStep, isPlaying]);

  const highlightElement = (selector: string) => {
    // Remove existing highlights
    document.querySelectorAll('.tutorial-highlight').forEach(el => {
      el.classList.remove('tutorial-highlight');
    });

    // Add highlight to target element
    const element = document.querySelector(selector);
    if (element) {
      element.classList.add('tutorial-highlight');
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const executeStepAction = (step: TutorialStep) => {
    if (!step.action) return;

    const { type, target, value } = step.action;
    const element = target ? document.querySelector(target) : null;

    switch (type) {
      case 'click':
        if (element && element instanceof HTMLElement) {
          element.click();
        }
        break;
      case 'input':
        if (element && element instanceof HTMLInputElement && value) {
          element.value = value;
          element.dispatchEvent(new Event('input', { bubbles: true }));
        }
        break;
      case 'navigate':
        if (value) {
          window.location.hash = value;
        }
        break;
      case 'wait':
        // Wait is handled by the tutorial flow
        break;
    }
  };

  const validateStep = (step: TutorialStep): boolean => {
    if (!step.validation) return true;

    const { type, condition } = step.validation;

    switch (type) {
      case 'element':
        return !!document.querySelector(condition);
      case 'value':
        const element = document.querySelector(condition);
        return element instanceof HTMLInputElement && element.value.length > 0;
      case 'state':
        // Custom state validation would be implemented here
        return true;
      default:
        return true;
    }
  };

  const handleNextStep = () => {
    if (!currentTutorial) return;

    const step = currentTutorial.steps[currentStep];

    // Execute step action if auto-playing
    if (isPlaying && step.action) {
      executeStepAction(step);
    }

    // Validate step completion
    if (validateStep(step)) {
      if (currentStep === currentTutorial.steps.length - 1) {
        completeTutorial();
        setIsPlaying(false);
      } else {
        nextStep();
      }
    }
  };

  const handlePreviousStep = () => {
    previousStep();
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  const getProgressPercentage = () => {
    if (!currentTutorial) return 0;
    return ((currentStep + 1) / currentTutorial.steps.length) * 100;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatEstimatedTime = (minutes: number) => {
    if (minutes < 60) {
      return t('ai.tutorial.time.minutes', { count: minutes });
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) {
      return t('ai.tutorial.time.hours', { count: hours });
    }
    return t('ai.tutorial.time.hoursMinutes', { hours, minutes: remainingMinutes });
  };

  if (!currentTutorial) {
    return null;
  }

  const currentStepData = currentTutorial.steps[currentStep];

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 50 }}
        className={`fixed bottom-4 left-4 right-4 max-w-2xl mx-auto z-50 ${className}`}
      >
        <Card className="shadow-2xl border-2 border-blue-200 bg-white">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <BookOpen className="h-5 w-5 text-blue-600" />
                <div>
                  <CardTitle className="text-lg">{currentTutorial.title}</CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge
                      variant="secondary"
                      className={getDifficultyColor(currentTutorial.difficulty)}
                    >
                      {t(`ai.tutorial.difficulty.${currentTutorial.difficulty}`)}
                    </Badge>
                    <div className="flex items-center gap-1 text-sm text-gray-500">
                      <Clock className="h-3 w-3" />
                      {formatEstimatedTime(currentTutorial.estimatedTime)}
                    </div>
                  </div>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={exitTutorial}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Progress */}
            <div className="mt-4">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>
                  {t('ai.tutorial.step')} {currentStep + 1} {t('ai.tutorial.of')} {currentTutorial.steps.length}
                </span>
                <span>{Math.round(getProgressPercentage())}%</span>
              </div>
              <Progress value={getProgressPercentage()} className="h-2" />
            </div>
          </CardHeader>

          <CardContent>
            {/* Current Step */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <Target className="h-4 w-4 text-blue-600" />
                {currentStepData.title}
              </h3>
              <div className="prose prose-sm max-w-none">
                <p className="text-gray-700 leading-relaxed">
                  {currentStepData.content}
                </p>
              </div>

              {/* Step Action Indicator */}
              {currentStepData.action && (
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2 text-sm text-blue-800">
                    <Circle className="h-3 w-3" />
                    {t(`ai.tutorial.actions.${currentStepData.action.type}`)}
                    {currentStepData.action.target && (
                      <code className="bg-blue-100 px-1 rounded text-xs">
                        {currentStepData.action.target}
                      </code>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreviousStep}
                  disabled={currentStep === 0}
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  {t('ai.tutorial.previous')}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={togglePlayback}
                  className="px-3"
                >
                  {isPlaying ? (
                    <Pause className="h-4 w-4" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                </Button>
              </div>

              <div className="flex items-center gap-2">
                {currentStep === currentTutorial.steps.length - 1 ? (
                  <Button
                    onClick={completeTutorial}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    {t('ai.tutorial.complete')}
                  </Button>
                ) : (
                  <Button onClick={handleNextStep}>
                    {t('ai.tutorial.next')}
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                )}
              </div>
            </div>

            {/* Step List */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <div className="flex items-center gap-2 overflow-x-auto pb-2">
                {currentTutorial.steps.map((step, index) => (
                  <div
                    key={step.id}
                    className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs whitespace-nowrap ${index === currentStep
                        ? 'bg-blue-100 text-blue-800'
                        : index < currentStep
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-600'
                      }`}
                  >
                    {index < currentStep ? (
                      <CheckCircle className="h-3 w-3" />
                    ) : index === currentStep ? (
                      <Target className="h-3 w-3" />
                    ) : (
                      <Circle className="h-3 w-3" />
                    )}
                    <span>{step.title}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Tutorial Highlight Styles */}
      <style>{`
        .tutorial-highlight {
          position: relative !important;
          z-index: 1000 !important;
          box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2) !important;
          border-radius: 4px !important;
          transition: all 0.3s ease !important;
        }
        
        .tutorial-highlight::before {
          content: '' !important;
          position: absolute !important;
          top: -4px !important;
          left: -4px !important;
          right: -4px !important;
          bottom: -4px !important;
          background: rgba(59, 130, 246, 0.1) !important;
          border-radius: 8px !important;
          animation: tutorial-pulse 2s infinite !important;
        }
        
        @keyframes tutorial-pulse {
          0%, 100% { opacity: 0.1; }
          50% { opacity: 0.3; }
        }
      `}</style>
    </AnimatePresence>
  );
};