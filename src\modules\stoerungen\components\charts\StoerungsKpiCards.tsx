import React from 'react';
import { SubtlePatternCard } from '@/components/ui/Card_SubtlePattern';
import { cn } from '@/lib/utils';
import { 
  AlertTriangle, 
  TrendingUp, 
  BarChart3, 
  Clock, 
  Timer, 
  ShieldCheck 
} from 'lucide-react';

interface KpiCardData {
  label: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  subtitle?: string;
  className?: string;
  valueClassName?: string;
}

interface StoerungsKpiCardsProps {
  stats: {
    total: number;
    active: number;
    resolved: number;
    avg_mttr_minutes: number;
    avg_mtta_minutes: number;
    first_time_fix_rate: number;
  };
  mttrMetrics?: {
    fastestResolution: number;
    improvement: number;
  };
  className?: string;
}

export const StoerungsKpiCards: React.FC<StoerungsKpiCardsProps> = React.memo(({ 
  stats, 
  mttrMetrics,
  className = ''
}) => {
  // Grundlegende KPI-Karten
  const baseKpiData: KpiCardData[] = [
    { 
      label: 'Gesamt Störungen', 
      value: stats.total, 
      icon: <AlertTriangle className="h-5 w-5" />, 
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      valueClassName: 'text-blue-600'
    },
    { 
      label: 'Aktive Störungen', 
      value: stats.active, 
      icon: <TrendingUp className="h-5 w-5" />, 
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      valueClassName: 'text-red-600'
    },
    { 
      label: 'Gelöste Störungen', 
      value: stats.resolved, 
      icon: <BarChart3 className="h-5 w-5" />, 
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      valueClassName: 'text-green-600'
    },
    { 
      label: 'Ø MTTR', 
      value: `${Math.round(stats.avg_mttr_minutes / 60 * 10) / 10}h`, 
      icon: <Clock className="h-5 w-5" />, 
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      valueClassName: 'text-orange-600'
    },
    { 
      label: 'Ø MTTA', 
      value: `${Math.round(stats.avg_mtta_minutes / 60 * 10) / 10}h`, 
      icon: <Timer className="h-5 w-5" />, 
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      valueClassName: 'text-indigo-600'
    },
    { 
      label: 'Erstlösungsrate', 
      value: `${stats.first_time_fix_rate}%`, 
      icon: <ShieldCheck className="h-5 w-5" />, 
      color: 'text-cyan-600',
      bgColor: 'bg-cyan-50',
      valueClassName: 'text-cyan-600'
    }
  ];

  // Zusätzliche MTTR-Metriken, falls verfügbar
  const mttrCards: KpiCardData[] = mttrMetrics ? [
    { 
      label: 'Schnellste Behebung', 
      value: formatMTTR(mttrMetrics.fastestResolution), 
      icon: <Clock className="h-5 w-5 text-yellow-600" />, 
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      className: 'bg-yellow-50',
      valueClassName: 'text-yellow-600',
      subtitle: 'Schnellste gemessene Behebungszeit'
    },
    { 
      label: 'Verbesserung', 
      value: `${mttrMetrics.improvement > 0 ? '+' : ''}${mttrMetrics.improvement}%`, 
      icon: <TrendingUp className="h-5 w-5" />, 
      color: mttrMetrics.improvement > 0 ? 'text-green-600' : 'text-red-600',
      bgColor: 'bg-green-50',
      className: 'bg-green-50',
      valueClassName: mttrMetrics.improvement > 0 ? 'text-green-600' : 'text-red-600',
      subtitle: 'Verbesserung der durchschnittlichen Behebungszeit'
    }
  ] : [];

  // Kombiniere die Karten
  const kpiData = [...baseKpiData, ...mttrCards];

  return (
    <div className={cn('kpi-cards-container w-full', className)}>
      <div className="flex flex-nowrap gap-4">
      {kpiData.map((kpi, index) => {
        const { label, value, icon, subtitle, className: kpiClassName, valueClassName } = kpi;
        
        return (
          <SubtlePatternCard
            key={index}
            title={label}
            value={value}
            icon={icon}
            subtitle={subtitle || ''}
            className={cn('h-full flex-1 min-w-0', kpiClassName)}
            valueClassName={valueClassName}
          />
        );
      })}
      </div>
    </div>
  );
});

// Hilfsfunktion zur Formatierung der MTTR-Zeit
function formatMTTR(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours === 0) return `${mins}m`;
  return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
}

StoerungsKpiCards.displayName = 'StoerungsKpiCards';
