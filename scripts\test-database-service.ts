#!/usr/bin/env tsx

/**
 * Test-Skript für den Drizzle DatabaseService
 */

import { databaseService } from '../src/services/database.service';

async function testDatabaseService() {
  console.log('🧪 Testing Drizzle DatabaseService...\n');

  try {
    // Test 1: Service Health
    console.log('🔍 Testing service health...');
    const health = await databaseService.getServiceHealth();
    console.log(`   ✅ Service status: ${health.status}`);
    console.log(`      - Response time: ${health.responseTime}ms`);
    console.log(`      - Version: ${health.version}`);
    console.log(`      - Databases: ${health.databases.join(', ')}`);

    // Test 2: Database Connection
    console.log('\n🔗 Testing database connections...');
    const connected = await databaseService.connect();
    console.log(`   ${connected ? '✅' : '❌'} Connection test: ${connected ? 'Success' : 'Failed'}`);

    // Test 3: System Stats
    console.log('\n📊 Testing system statistics...');
    const stats = await databaseService.getSystemStats();
    console.log(`   ✅ System Stats retrieved:`);
    console.log(`      - Total Störungen: ${stats.totalStoerungen}`);
    console.log(`      - Active Störungen: ${stats.activeStoerungen}`);
    console.log(`      - User Count: ${stats.userCount}`);
    console.log(`      - Last Activity: ${stats.lastActivity}`);

    // Test 4: Cache Funktionalität
    console.log('\n⚡ Testing cache functionality...');
    const cacheStatsBefore = databaseService.getCacheStats();
    console.log(`   ✅ Cache stats before: ${cacheStatsBefore.entries} entries`);
    
    // Zweite Abfrage zum Cache-Test
    await databaseService.getSystemStats();
    const cacheStatsAfter = databaseService.getCacheStats();
    console.log(`   ✅ Cache stats after: ${cacheStatsAfter.entries} entries`);
    console.log(`      - Cache type: ${cacheStatsAfter.type}`);

    // Test 5: Service Level Data
    console.log('\n📈 Testing service level data...');
    const serviceLevelData = await databaseService.getServiceLevelData();
    console.log(`   ✅ Service level data: ${serviceLevelData.length} records`);
    if (serviceLevelData.length > 0) {
      console.log(`      - Sample record: ${JSON.stringify(serviceLevelData[0])}`);
    }

    // Test 6: Daily Performance Data
    console.log('\n📊 Testing daily performance data...');
    const performanceData = await databaseService.getDailyPerformanceData();
    console.log(`   ✅ Performance data: ${performanceData.length} records`);

    // Test 7: Warehouse Data
    console.log('\n🏭 Testing warehouse data...');
    const warehouseData = await databaseService.getWarehouseData();
    console.log(`   ✅ Warehouse data: ${warehouseData.length} records`);

    // Test 8: RAG Knowledge Bases
    console.log('\n🧠 Testing RAG knowledge bases...');
    const knowledgeBases = await databaseService.getKnowledgeBases();
    console.log(`   ✅ Knowledge bases: ${knowledgeBases.length} records`);
    if (knowledgeBases.length > 0) {
      console.log(`      - Sample KB: ${JSON.stringify(knowledgeBases[0].name || 'Unnamed')}`);
    }

    // Test 9: Cache Invalidation
    console.log('\n🧹 Testing cache invalidation...');
    const clearedEntries = databaseService.invalidateCache(['dispatch', 'warehouse']);
    console.log(`   ✅ Cache invalidation: ${clearedEntries} entries cleared`);

    console.log('\n🎉 All DatabaseService tests passed successfully!');
    console.log('\n📊 Final Performance Summary:');
    console.log(`   - Service Health: ${health.status}`);
    console.log(`   - Total Response Time: ${health.responseTime}ms`);
    console.log(`   - Cache Efficiency: Active`);
    console.log(`   - Database Connections: Working`);
    console.log(`   - Data Sources: SFM + RAG`);

  } catch (error) {
    console.error('❌ DatabaseService test failed:', error);
    process.exit(1);
  }
}

// Run the test
testDatabaseService();
