#!/usr/bin/env python3
"""
Migrations-Skript für die Datenbank-Reorganisation
Verschiebt bestehende Datenbank-Dateien in die neue Backend-Struktur
"""

import os
import shutil
from pathlib import Path

def migrate_database():
    """Migriert die Datenbank in die neue Backend-Struktur"""
    
    print("=== Datenbank-Migration gestartet ===\n")
    
    # Pfade definieren
    backend_dir = Path(__file__).parent.parent
    new_db_dir = backend_dir / "database"
    new_db_path = new_db_dir / "sap_datenbank.db"
    
    # Mögliche alte Pfade
    old_paths = [
        Path("sap_datenbank.db"),  # Aktuelles Verzeichnis
        Path("../../../sap_datenbank.db"),  # Projekt-Root
        Path("src/modules/backend/components/workflows/prozess/sap_datenbank.db"),  # Alter Pfad
    ]
    
    # Erstelle neues Database-Verzeichnis
    new_db_dir.mkdir(exist_ok=True)
    print(f"✅ Database-Verzeichnis erstellt: {new_db_dir}")
    
    # Suche nach bestehender Datenbank
    found_db = None
    for old_path in old_paths:
        if old_path.exists():
            found_db = old_path
            break
    
    if found_db:
        print(f"📁 Bestehende Datenbank gefunden: {found_db}")
        
        if new_db_path.exists():
            # Backup der neuen Datei erstellen
            backup_path = new_db_path.with_suffix('.db.backup')
            shutil.copy2(new_db_path, backup_path)
            print(f"💾 Backup erstellt: {backup_path}")
        
        # Datenbank verschieben
        shutil.copy2(found_db, new_db_path)
        print(f"✅ Datenbank migriert nach: {new_db_path}")
        
        # Überprüfe Migration
        if new_db_path.exists():
            file_size = new_db_path.stat().st_size
            print(f"📊 Migrierte Datenbank-Größe: {file_size} bytes")
            
            # Alte Datei wird IMMER beibehalten - niemals löschen!
            print(f"📁 Alte Datenbank-Datei wird beibehalten: {found_db}")
            print("⚠️  WICHTIG: Die alte Datei wird als Backup beibehalten und niemals gelöscht!")
        
    else:
        print("ℹ️ Keine bestehende Datenbank gefunden. Neue Datenbank wird beim ersten Start erstellt.")
    
    print(f"\n=== Migration abgeschlossen ===")
    print(f"Neue Datenbank-Pfad: {new_db_path}")
    print("Die Workflow-Skripte verwenden jetzt automatisch den neuen Pfad.")

if __name__ == "__main__":
    migrate_database()