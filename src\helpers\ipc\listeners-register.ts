import { BrowserWindow } from "electron";
import { addWindowEventListeners } from "./window/window-listeners";
import { registerConfigHandlers } from "./config/config-handlers";
import { registerTeamsHandlers } from "./teams/teams-handlers";
import { registerShellHandlers } from "./shell/shell-handlers";

/**
 * Registriert alle Event-Listener für die IPC-Kommunikation
 * @param mainWindow Das Hauptfenster der Anwendung
 */
export default function registerListeners(mainWindow: BrowserWindow) {
  // Registriere Fenster-Listener
  addWindowEventListeners(mainWindow);
  
  // Registriere Konfigurations-Handler
  registerConfigHandlers();
  
  // Registriere Teams-Handler
  registerTeamsHandlers();
  
  // Registriere Shell-Handler
  registerShellHandlers();

  console.log('Alle IPC-Handler wurden registriert (Database und Theme Handler entfernt, Teams und Shell Handler hinzugefügt).');
}
