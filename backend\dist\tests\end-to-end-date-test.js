"use strict";
/**
 * End-to-End Test für spezifische Datumsabfragen
 */
Object.defineProperty(exports, "__esModule", { value: true });
const data_enrichment_service_1 = require("../services/data-enrichment.service");
const client_1 = require("@prisma/client");
async function testEndToEndDateQueries() {
    console.log('🚀 End-to-End Date Query Test...\n');
    const prisma = new client_1.PrismaClient();
    const service = new data_enrichment_service_1.DataEnrichmentService(prisma);
    const testQueries = [
        {
            query: 'Servicegrad vom 17.04.2025',
            expectedIntent: 'dispatch',
            expectedDate: '2025-04-17'
        },
        {
            query: 'Störungen am 2025-04-17',
            expectedIntent: 'stoerungen',
            expectedDate: '2025-04-17'
        },
        {
            query: 'Schnittdaten für April 2025',
            expectedIntent: 'cutting',
            expectedDateRange: ['2025-04-01', '2025-04-30']
        }
    ];
    for (const testCase of testQueries) {
        console.log(`🔍 Testing: "${testCase.query}"`);
        try {
            // Test the complete enrichment flow
            const enrichedContext = await service.enrichChatContext(testCase.query);
            console.log(`  📊 Enrichment Results:`);
            console.log(`    - Has Data: ${enrichedContext.hasData}`);
            console.log(`    - Data Types: ${enrichedContext.dataTypes.join(', ')}`);
            console.log(`    - Detected Intents: ${enrichedContext.detectedIntents.length}`);
            if (enrichedContext.detectedIntents.length > 0) {
                const intent = enrichedContext.detectedIntents[0];
                console.log(`    - Intent Type: ${intent.type}`);
                console.log(`    - Confidence: ${intent.confidence.toFixed(2)}`);
                console.log(`    - Keywords: ${intent.keywords.join(', ')}`);
                if (intent.timeRange) {
                    console.log(`    - Time Range: ${intent.timeRange.startDate} to ${intent.timeRange.endDate}`);
                    // Verify expected results
                    if (testCase.expectedDate) {
                        const dateMatches = intent.timeRange.startDate === testCase.expectedDate;
                        console.log(`    - Date Match: ${dateMatches ? '✅' : '❌'} (Expected: ${testCase.expectedDate})`);
                    }
                    if (testCase.expectedDateRange) {
                        const rangeMatches = intent.timeRange.startDate === testCase.expectedDateRange[0] &&
                            intent.timeRange.endDate === testCase.expectedDateRange[1];
                        console.log(`    - Range Match: ${rangeMatches ? '✅' : '❌'} (Expected: ${testCase.expectedDateRange[0]} to ${testCase.expectedDateRange[1]})`);
                    }
                }
                const intentMatches = intent.type === testCase.expectedIntent;
                console.log(`    - Intent Match: ${intentMatches ? '✅' : '❌'} (Expected: ${testCase.expectedIntent})`);
            }
            if (enrichedContext.databaseContext) {
                console.log(`    - Database Context: ${enrichedContext.databaseContext.substring(0, 100)}...`);
            }
            if (enrichedContext.fallbackReason) {
                console.log(`    - Fallback Reason: ${enrichedContext.fallbackReason}`);
            }
        }
        catch (error) {
            console.log(`    💥 Error: ${error}`);
        }
        console.log('');
    }
    await prisma.$disconnect();
    console.log('✅ End-to-End date query test completed!');
}
// Run the test
testEndToEndDateQueries().catch(console.error);
