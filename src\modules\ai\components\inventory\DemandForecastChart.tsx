import React, { memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Area, ReferenceLine } from 'recharts';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, TrendingDown, Minus, Calendar, Target, AlertCircle } from 'lucide-react';
import { DemandForecast, SeasonalityPattern } from '../../services/inventory/types';

interface DemandForecastChartProps {
  forecast: DemandForecast;
  seasonality?: SeasonalityPattern;
  historicalData?: Array<{
    date: Date;
    actualDemand: number;
  }>;
  onDateSelect?: (date: Date) => void;
}

/**
 * Demand Forecast Chart Component
 * 
 * Displays comprehensive demand forecasting visualization including:
 * - Historical vs predicted demand line chart
 * - Confidence intervals
 * - Seasonal patterns
 * - Trend analysis
 * - Forecast accuracy metrics
 */
export const DemandForecastChart = memo(function DemandForecastChart({
  forecast,
  seasonality,
  historicalData = [],
  onDateSelect
}: DemandForecastChartProps) {

  // Prepare combined historical and forecast data
  const combinedData = React.useMemo(() => {
    const historical = historicalData.map(item => ({
      date: item.date.toISOString().split('T')[0],
      fullDate: item.date,
      actualDemand: item.actualDemand,
      predictedDemand: null,
      lowerBound: null,
      upperBound: null,
      type: 'historical' as const
    }));

    const predictions = forecast.predictions.map(prediction => ({
      date: prediction.date.toISOString().split('T')[0],
      fullDate: prediction.date,
      actualDemand: null,
      predictedDemand: prediction.predictedDemand,
      lowerBound: prediction.confidenceInterval.lower,
      upperBound: prediction.confidenceInterval.upper,
      seasonalAdjustment: prediction.seasonalAdjustment || 0,
      type: 'forecast' as const
    }));

    // Combine and sort by date
    return [...historical, ...predictions].sort((a, b) =>
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );
  }, [historicalData, forecast.predictions]);

  // Calculate trend metrics
  const trendIcon = forecast.trendDirection === 'increasing' ? TrendingUp :
    forecast.trendDirection === 'decreasing' ? TrendingDown : Minus;
  const trendColor = forecast.trendDirection === 'increasing' ? 'text-green-600' :
    forecast.trendDirection === 'decreasing' ? 'text-red-600' : 'text-gray-600';

  // Calculate forecast summary metrics
  const totalForecastDemand = forecast.predictions.reduce((sum, p) => sum + p.predictedDemand, 0);
  const averageDailyDemand = totalForecastDemand / forecast.predictions.length;
  const maxDemand = Math.max(...forecast.predictions.map(p => p.predictedDemand));
  const minDemand = Math.min(...forecast.predictions.map(p => p.predictedDemand));



  // Chart configuration
  const chartConfig = {
    actualDemand: {
      label: "Tatsächlicher Bedarf",
      color: "var(--chart-1)",
    },
    predictedDemand: {
      label: "Prognostizierter Bedarf",
      color: "var(--chart-2)",
    },
    confidenceInterval: {
      label: "Konfidenzintervall",
      color: "var(--chart-3)",
    },
    seasonal: {
      label: "Saisonaler Faktor",
      color: "var(--chart-4)",
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-[#ff7a05]">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Ø Tagesbedarf</p>
                <p className="text-2xl font-bold text-blue-600">
                  {Math.round(averageDailyDemand)}
                </p>
                <p className="text-xs text-gray-500">Stück/Tag</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#ff7a05]">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              {React.createElement(trendIcon, { className: `h-5 w-5 ${trendColor}` })}
              <div>
                <p className="text-sm font-medium text-gray-600">Trend</p>
                <p className={`text-2xl font-bold ${trendColor}`}>
                  {forecast.trendDirection === 'increasing' ? '+' :
                    forecast.trendDirection === 'decreasing' ? '-' : '='}
                </p>
                <p className="text-xs text-gray-500 capitalize">
                  {forecast.trendDirection === 'increasing' ? 'Steigend' :
                    forecast.trendDirection === 'decreasing' ? 'Fallend' : 'Stabil'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#ff7a05]">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Prognosegenauigkeit</p>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round(forecast.accuracy * 100)}%
                </p>
                <p className="text-xs text-gray-500">{forecast.forecastMethod}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#ff7a05]">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Konfidenz</p>
                <p className="text-2xl font-bold text-orange-600">
                  {Math.round(forecast.confidence * 100)}%
                </p>
                <p className="text-xs text-gray-500">
                  {forecast.predictions.length} Tage
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Forecast Chart */}
      <Card className="border-[#ff7a05]">
        <CardHeader>
          <CardTitle>Bedarfsprognose</CardTitle>
          <CardDescription>
            Historische Daten vs. Prognostizierter Bedarf mit Konfidenzintervall
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-80 w-full">
            <LineChart data={combinedData} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis
                dataKey="date"
                className="text-xs font-bold"
                tick={{ fontSize: 10 }}
                tickFormatter={(value) => {
                  const date = new Date(value);
                  return `${date.getDate()}.${date.getMonth() + 1}`;
                }}
              />
              <YAxis
                className="text-xs font-bold"
                tick={{ fill: "#000000" }}
                label={{
                  value: "Bedarf (Stück)",
                  angle: -90,
                  position: "insideLeft",
                  style: { textAnchor: "middle", fontSize: 12 }
                }}
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    labelFormatter={(label) => {
                      const date = new Date(label);
                      return `Datum: ${date.toLocaleDateString('de-DE')}`;
                    }}
                    formatter={(value, name) => [
                      value ? `${Math.round(Number(value))} Stück` : 'N/A',
                      name === 'actualDemand' ? 'Tatsächlich' :
                        name === 'predictedDemand' ? 'Prognose' :
                          name === 'lowerBound' ? 'Untergrenze' : 'Obergrenze'
                    ]}
                  />
                }
              />

              {/* Confidence Interval Area */}
              <Area
                dataKey="upperBound"
                stroke="none"
                fill={chartConfig.confidenceInterval.color}
                fillOpacity={0.2}
              />
              <Area
                dataKey="lowerBound"
                stroke="none"
                fill="white"
                fillOpacity={1}
              />

              {/* Historical Demand Line */}
              <Line
                type="monotone"
                dataKey="actualDemand"
                stroke={chartConfig.actualDemand.color}
                strokeWidth={2}
                dot={{ fill: chartConfig.actualDemand.color, strokeWidth: 2, r: 3 }}
                connectNulls={false}
              />

              {/* Predicted Demand Line */}
              <Line
                type="monotone"
                dataKey="predictedDemand"
                stroke={chartConfig.predictedDemand.color}
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={{ fill: chartConfig.predictedDemand.color, strokeWidth: 2, r: 3 }}
                connectNulls={false}
              />

              {/* Reference line for today */}
              <ReferenceLine
                x={new Date().toISOString().split('T')[0]}
                stroke="#666"
                strokeDasharray="2 2"
                label={{ value: "Heute", position: "top" }}
              />
            </LineChart>
          </ChartContainer>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Seasonal Patterns */}
        {seasonality?.hasSeasonality && (
          <Card className="border-[#ff7a05]">
            <CardHeader>
              <CardTitle>Saisonale Muster</CardTitle>
              <CardDescription>
                Erkannte saisonale Schwankungen (Stärke: {Math.round(seasonality.strength * 100)}%)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {seasonality.patterns.map((pattern, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium capitalize">
                        {pattern.type === 'daily' ? 'Täglich' :
                          pattern.type === 'weekly' ? 'Wöchentlich' :
                            pattern.type === 'monthly' ? 'Monatlich' :
                              pattern.type === 'quarterly' ? 'Quartalsweise' : 'Jährlich'}
                      </span>
                      <Badge variant="outline">
                        {pattern.cycle} Tage Zyklus
                      </Badge>
                    </div>
                    <Progress value={pattern.amplitude * 100} className="h-2" />
                    <p className="text-xs text-gray-600">
                      Amplitude: {Math.round(pattern.amplitude * 100)}%,
                      Phase: {Math.round(pattern.phase)} Tage
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Forecast Accuracy Metrics */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle>Prognose-Metriken</CardTitle>
            <CardDescription>
              Detaillierte Genauigkeitsanalyse
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Gesamtgenauigkeit</span>
                <div className="flex items-center space-x-2">
                  <Progress value={forecast.accuracy * 100} className="w-20 h-2" />
                  <span className="text-sm font-bold">{Math.round(forecast.accuracy * 100)}%</span>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Konfidenz</span>
                <div className="flex items-center space-x-2">
                  <Progress value={forecast.confidence * 100} className="w-20 h-2" />
                  <span className="text-sm font-bold">{Math.round(forecast.confidence * 100)}%</span>
                </div>
              </div>

              <div className="pt-2 border-t">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Max. Bedarf</p>
                    <p className="font-bold">{Math.round(maxDemand)} Stück</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Min. Bedarf</p>
                    <p className="font-bold">{Math.round(minDemand)} Stück</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Gesamt Prognose</p>
                    <p className="font-bold">{Math.round(totalForecastDemand)} Stück</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Prognosezeitraum</p>
                    <p className="font-bold">{forecast.predictions.length} Tage</p>
                  </div>
                </div>
              </div>

              <div className="pt-2 border-t">
                <p className="text-xs text-gray-600">
                  Methode: {forecast.forecastMethod}
                </p>
                <p className="text-xs text-gray-600">
                  Erstellt: {forecast.generatedAt.toLocaleDateString('de-DE')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Forecast Table */}
      <Card className="border-[#ff7a05]">
        <CardHeader>
          <CardTitle>Detaillierte Prognose</CardTitle>
          <CardDescription>
            Tägliche Bedarfsprognose mit Konfidenzintervallen
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="max-h-64 overflow-y-auto">
            <div className="grid grid-cols-5 gap-2 text-xs font-medium text-gray-600 pb-2 border-b">
              <div>Datum</div>
              <div>Prognose</div>
              <div>Untergrenze</div>
              <div>Obergrenze</div>
              <div>Saisonal</div>
            </div>
            <div className="space-y-1 pt-2">
              {forecast.predictions.slice(0, 30).map((prediction, index) => (
                <div
                  key={index}
                  className="grid grid-cols-5 gap-2 text-xs py-1 hover:bg-gray-50 rounded cursor-pointer"
                  onClick={() => onDateSelect?.(prediction.date)}
                >
                  <div className="font-medium">
                    {prediction.date.toLocaleDateString('de-DE')}
                  </div>
                  <div className="font-bold text-blue-600">
                    {Math.round(prediction.predictedDemand)}
                  </div>
                  <div className="text-gray-600">
                    {Math.round(prediction.confidenceInterval.lower)}
                  </div>
                  <div className="text-gray-600">
                    {Math.round(prediction.confidenceInterval.upper)}
                  </div>
                  <div className="text-purple-600">
                    {prediction.seasonalAdjustment ?
                      `${prediction.seasonalAdjustment > 0 ? '+' : ''}${Math.round(prediction.seasonalAdjustment)}` :
                      '-'
                    }
                  </div>
                </div>
              ))}
              {forecast.predictions.length > 30 && (
                <p className="text-xs text-gray-500 text-center pt-2">
                  ... und {forecast.predictions.length - 30} weitere Tage
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
});