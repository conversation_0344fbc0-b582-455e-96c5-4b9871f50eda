import { ipcMain } from 'electron';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

// Load Teams configuration from .env.teams file
let TEAMS_WEBHOOK_URL = process.env.TEAMS_WEBHOOK_URL || '';

// Try to load from .env.teams file if not set via environment
if (!TEAMS_WEBHOOK_URL) {
  try {
    const envTeamsPath = path.join(process.cwd(), '.env.teams');
    if (fs.existsSync(envTeamsPath)) {
      const envContent = fs.readFileSync(envTeamsPath, 'utf8');
      const match = envContent.match(/TEAMS_WEBHOOK_URL=(.+)/);
      if (match && match[1]) {
        TEAMS_WEBHOOK_URL = match[1].trim();
        console.log('[TEAMS] Webhook URL aus .env.teams geladen');
      }
    }
  } catch (error) {
    console.warn('[TEAMS] Konnte .env.teams nicht laden:', error);
  }
}

interface StoerungTeamsData {
  title: string;
  description?: string;
  severity: string;
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
  status: string;
  send_protocol: boolean;
}

/**
 * Sendet eine Störungsmeldung an Microsoft Teams
 */
async function sendStoerungToTeams(data: StoerungTeamsData): Promise<{ success: boolean; error?: string }> {
  if (!TEAMS_WEBHOOK_URL) {
    console.warn('[TEAMS] Webhook URL nicht konfiguriert');
    return { success: false, error: 'Teams Webhook URL nicht konfiguriert' };
  }

  try {
    // Schweregrad-Mapping für bessere Darstellung
    const severityMap = {
      LOW: { label: 'Niedrig', color: 'good' },
      MEDIUM: { label: 'Mittel', color: 'warning' },
      HIGH: { label: 'Hoch', color: 'attention' },
      CRITICAL: { label: 'Kritisch', color: 'attention' }
    };

    const severityInfo = severityMap[data.severity as keyof typeof severityMap] || severityMap.LOW;

    // Erstelle Adaptive Card für Teams
    const teamsMessage = {
      type: 'message',
      attachments: [
        {
          contentType: 'application/vnd.microsoft.card.adaptive',
          contentUrl: null,
          content: {
            type: 'AdaptiveCard',
            body: [
              {
                type: 'TextBlock',
                size: 'large',
                weight: 'bolder',
                text: '🚨 Neue Störungsmeldung',
                color: data.severity === 'CRITICAL' ? 'attention' : 'default'
              },
              {
                type: 'TextBlock',
                size: 'medium',
                weight: 'bolder',
                text: data.title,
                wrap: true
              },
              {
                type: 'FactSet',
                facts: [
                  {
                    title: 'Schweregrad:',
                    value: `**${severityInfo.label}**`
                  },
                  {
                    title: 'Status:',
                    value: data.status === 'NEW' ? 'Neu' : data.status
                  },
                  ...(data.category ? [{
                    title: 'Kategorie:',
                    value: data.category
                  }] : []),
                  ...(data.affected_system ? [{
                    title: 'Betroffenes System:',
                    value: data.affected_system
                  }] : []),
                  ...(data.location ? [{
                    title: 'Standort:',
                    value: data.location
                  }] : []),
                  ...(data.reported_by ? [{
                    title: 'Gemeldet von:',
                    value: data.reported_by
                  }] : []),
                  {
                    title: 'Zeitpunkt:',
                    value: new Date().toLocaleString('de-DE')
                  }
                ]
              },
              ...(data.description ? [{
                type: 'TextBlock',
                text: '**Beschreibung:**',
                weight: 'bolder',
                spacing: 'medium'
              }, {
                type: 'TextBlock',
                text: data.description,
                wrap: true,
                spacing: 'small'
              }] : []),
              ...(data.send_protocol ? [{
                type: 'TextBlock',
                text: '📧 Störungsprotokoll wird automatisch versendet',
                color: 'accent',
                spacing: 'medium'
              }] : [])
            ],
            $schema: 'http://adaptivecards.io/schemas/adaptive-card.json',
            version: '1.4'
          }
        }
      ]
    };

    console.log('[TEAMS] Sende Störungsmeldung an Teams...');
    const response = await axios.post(TEAMS_WEBHOOK_URL, teamsMessage, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000 // 10 Sekunden Timeout
    });

    if (response.status === 200) {
      console.log('[TEAMS] Störungsmeldung erfolgreich an Teams gesendet');
      return { success: true };
    } else {
      console.error('[TEAMS] Unerwarteter Status Code:', response.status);
      return { success: false, error: `HTTP ${response.status}` };
    }

  } catch (error) {
    console.error('[TEAMS] Fehler beim Senden an Teams:', error);
    
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        return { success: false, error: 'Timeout beim Senden an Teams' };
      } else if (error.response) {
        return { success: false, error: `Teams API Fehler: ${error.response.status}` };
      } else if (error.request) {
        return { success: false, error: 'Keine Antwort von Teams erhalten' };
      }
    }
    
    return { success: false, error: error instanceof Error ? error.message : 'Unbekannter Fehler' };
  }
}

/**
 * Registriert die Teams IPC Handler
 */
export function registerTeamsHandlers(): void {
  console.log('[TEAMS] Registriere Teams IPC Handler...');

  // Handler für das Senden von Störungsmeldungen an Teams
  ipcMain.handle('teams:send-stoerung', async (_event, data: StoerungTeamsData) => {
    try {
      return await sendStoerungToTeams(data);
    } catch (error) {
      console.error('[TEAMS] Fehler im IPC Handler:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unbekannter Fehler' 
      };
    }
  });

  // Handler für das Testen der Teams-Verbindung
  ipcMain.handle('teams:test-connection', async () => {
    if (!TEAMS_WEBHOOK_URL) {
      return { success: false, error: 'Teams Webhook URL nicht konfiguriert' };
    }

    try {
      const testMessage = {
        type: 'message',
        attachments: [
          {
            contentType: 'application/vnd.microsoft.card.adaptive',
            contentUrl: null,
            content: {
              type: 'AdaptiveCard',
              body: [
                {
                  type: 'TextBlock',
                  size: 'medium',
                  weight: 'bolder',
                  text: '✅ Teams-Verbindung Test'
                },
                {
                  type: 'TextBlock',
                  text: 'Diese Nachricht bestätigt, dass die Verbindung zu Microsoft Teams funktioniert.',
                  wrap: true
                },
                {
                  type: 'TextBlock',
                  text: `Zeitpunkt: ${new Date().toLocaleString('de-DE')}`,
                  size: 'small',
                  color: 'accent'
                }
              ],
              $schema: 'http://adaptivecards.io/schemas/adaptive-card.json',
              version: '1.4'
            }
          }
        ]
      };

      const response = await axios.post(TEAMS_WEBHOOK_URL, testMessage, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      });

      return { success: response.status === 200 };
    } catch (error) {
      console.error('[TEAMS] Test-Verbindung fehlgeschlagen:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Verbindungstest fehlgeschlagen' 
      };
    }
  });

  console.log('[TEAMS] Teams IPC Handler erfolgreich registriert');
}