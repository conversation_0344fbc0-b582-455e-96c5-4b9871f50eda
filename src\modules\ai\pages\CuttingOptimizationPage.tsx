import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useMaterialData } from '../hooks/useMaterialData';
import SmoothTab from '@/components/Animation/kokonutui/smooth-tab';
import { ChartErrorBoundary } from '@/components/ErrorBoundary';
import { CuttingPlanVisualization } from '../components/cutting/CuttingPlanVisualization';
import { CuttingAlternativesChart } from '../components/cutting/CuttingAlternativesChart';
import { WasteAnalysisChart } from '../components/cutting/WasteAnalysisChart';
import { TrommelrechnungComponent } from '../components/cutting/TrommelrechnungComponent';
import { DrumComparisonDisplay } from '../components/cutting/DrumComparisonDisplay';
import { AIQualityAnalysis } from '../components/cutting/AIQualityAnalysis';
import { ManualDrumSelectionForm } from '../components/cutting/ManualDrumSelectionForm';
import CuttingConfigurationSection from '../components/cutting/CuttingConfigurationSection';
import CuttingVisualizationSection from '../components/cutting/CuttingVisualizationSection';
import CuttingAlternativesSection from '../components/cutting/CuttingAlternativesSection';
import CuttingAnalysisSection from '../components/cutting/CuttingAnalysisSection';
import { CuttingOptimizerService } from '../services/cutting/CuttingOptimizerService';
import TrommelrechnungService from '../services/cutting/TrommelrechnungService';
import {
    CuttingRequest,
    CuttingPlan,
    CuttingAlternative,
    WasteAnalysis,
    CuttingOrder,
    DrumInventory,
    CuttingConstraints,
    Delivery,
    DeliveryItem,
    ManualDrumSelection,
    DrumComparisonResult
} from '../services/types';
import { Scissors, Download, RefreshCw, AlertTriangle, CheckCircle, Plus, Minus, Calendar, Target, Brain } from 'lucide-react';
import { toast } from 'sonner';
import MaterialSelector from '@/components/MaterialSelector';
import TrommelSelector from '@/components/TrommelSelector';
import { InventoryService } from '../services/inventory/InventoryService';
import { DrumComparisonService } from '../services/comparison/DrumComparisonService';
import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';

/**
 * Cutting Optimization Page
 * 
 * Provides AI-powered cutting optimization with visualization and export functionality.
 * Integrates with existing cutting department routes and authentication.
 */
export default function CuttingOptimizationPage() {
    const { t } = useTranslation();
    const { cableTypes, isLoading: isMaterialDataLoading } = useMaterialData();

    // State management
    const [isOptimizing, setIsOptimizing] = useState(false);
    const [currentPlan, setCurrentPlan] = useState<CuttingPlan | null>(null);
    const [alternatives, setAlternatives] = useState<CuttingAlternative[]>([]);
    const [wasteAnalysis, setWasteAnalysis] = useState<WasteAnalysis | null>(null);
    const [selectedAlgorithm, setSelectedAlgorithm] = useState<'first-fit' | 'best-fit' | 'genetic'>('best-fit');
    const [activeTab, setActiveTab] = useState('trommelrechnung');
    const [currentOrders, setCurrentOrders] = useState<CuttingOrder[]>([]);

    // Form state for cutting request - Neue Lieferungslogik
    const [delivery, setDelivery] = useState<Delivery>({
        id: 'delivery-1',
        items: [
            {
                id: 'item-1',
                cableType: '0010000', // MATNR aus Materialdaten
                requiredLength: 1.5, // 150cm = 1.5m
                quantity: 5,
                priority: 'medium'
            }
        ]
    });

    const [drums, setDrums] = useState<DrumInventory[]>([
        {
            id: 'drum-1',
            cableType: '0010000', // MATNR aus Materialdaten
            totalLength: 10.0, // 1000cm = 10.0m
            availableLength: 8.0, // 800cm = 8.0m
            quality: 'A'
        }
    ]);

    const [constraints, setConstraints] = useState<CuttingConstraints>({
        maxWasteLength: 1.0, // 1.0m maximaler Verschnitt
        maxWastePercentage: 15,
        allowMixedTypes: false
    });

    // State für die Auswahl des Verschnitt-Typs
    const [wasteType, setWasteType] = useState<'length' | 'percentage'>('length');

    // State für MaterialSelector Funktionalität
    const [kabelMaterialSuchOpen, setKabelMaterialSuchOpen] = useState(false);
    const [kabelMaterialSuchWert, setKabelMaterialSuchWert] = useState('');
    const [verfügbareKabelmaterialien, setVerfügbareKabelmaterialien] = useState<any[]>([]);

    // State für TrommelSelector Funktionalität
    const [selectedTrommel, setSelectedTrommel] = useState('');
    const [trommelSuchOpen, setTrommelSuchOpen] = useState(false);
    const [trommelSuchWert, setTrommelSuchWert] = useState('');
    const [verfügbareTrommeln, setVerfügbareTrommeln] = useState<any[]>([]);

    // State für manuelle Trommelauswahl und Vergleich
    const [manualDrumSelections, setManualDrumSelections] = useState<ManualDrumSelection[]>([]);
    const [selectionDate, setSelectionDate] = useState<string>(new Date().toISOString().split('T')[0]);
    const [comparisonResult, setComparisonResult] = useState<DrumComparisonResult | null>(null);
    const [showComparison, setShowComparison] = useState(false);

    // Services für Bestandsabfrage und Vergleich
    const [inventoryService] = useState(() => new InventoryService());
    const [optimizerService] = useState(() => new CuttingOptimizerService({
        defaultAlgorithm: selectedAlgorithm,
        enableAdvancedOptimization: true,
        maxOptimizationTime: 30000
    }));
    const [comparisonService] = useState(() => new DrumComparisonService(inventoryService, optimizerService));

    // Funktionen für manuelle Trommelauswahl
    const addManualDrumSelection = useCallback(() => {
        const newSelection: ManualDrumSelection = {
            chargeNumber: '',
            material: '',
            lagereinheitentyp: '',
            gesamtbestand: 0,
            selectionReason: '',
            aufnahmedatum: selectionDate
        };
        setManualDrumSelections(prev => [...prev, newSelection]);
    }, [selectionDate]);

    const updateManualDrumSelection = useCallback((index: number, selection: ManualDrumSelection) => {
        setManualDrumSelections(prev =>
            prev.map((item, i) => i === index ? selection : item)
        );
    }, []);

    const removeManualDrumSelection = useCallback((index: number) => {
        setManualDrumSelections(prev => prev.filter((_, i) => i !== index));
    }, []);

    // Funktion um den ersten ausgewählten Kabeltyp aus den Lieferungen zu ermitteln
    const getSelectedCableTypeFromOrders = useCallback(() => {
        const itemWithCableType = delivery.items.find(item => item.cableType && item.cableType.trim() !== '');
        return itemWithCableType?.cableType || '';
    }, [delivery]);

    // Initialize service on component mount
    React.useEffect(() => {
        const initService = async () => {
            try {
                await optimizerService.initialize();
            } catch (error) {
                console.error('Failed to initialize cutting optimizer service:', error);
                toast.error('Fehler beim Initialisieren des Optimierungsdienstes');
            }
        };

        initService();
    }, [optimizerService]);

    // Event Handler für MaterialSelector
    const handleKabelMaterialChange = useCallback((value: string) => {
        setKabelMaterialSuchWert(value);
    }, []);

    const handleKabelMaterialSelect = useCallback((material: any) => {
        // Aktualisiere das erste DeliveryItem mit dem ausgewählten Material
        if (delivery.items.length > 0) {
            const updatedDelivery = {
                ...delivery,
                items: delivery.items.map((item, index) =>
                    index === 0 ? { ...item, cableType: material.MATNR } : item
                )
            };
            setDelivery(updatedDelivery);

            // Synchronisiere den Kabeltyp auch für alle Trommeln
            const updatedDrums = drums.map(drum => ({
                ...drum,
                cableType: material.MATNR
            }));
            setDrums(updatedDrums);
        }
    }, [delivery, drums]);

    // Funktion für die Materialauswahl pro DeliveryItem
    const handleItemMaterialSelect = useCallback((itemId: string, material: any) => {
        const updatedDelivery = {
            ...delivery,
            items: delivery.items.map(item =>
                item.id === itemId ? { ...item, cableType: material.MATNR } : item
            )
        };
        setDelivery(updatedDelivery);
    }, [delivery]);

    // Handler für Übernehmen-Button: Überträgt Kabeltyp von Lieferung zu verfügbarer Trommel
    const handleÜbernehmenKabeltyp = useCallback((itemId: string) => {
        const item = delivery.items.find(i => i.id === itemId);
        if (!item || !item.cableType) {
            toast.error('Kein Kabeltyp in der Lieferung ausgewählt');
            return;
        }

        // Aktualisiere die erste verfügbare Trommel mit dem Kabeltyp der Lieferung
        if (verfügbareTrommeln.length > 0) {
            const updatedTrommeln = [...verfügbareTrommeln];
            updatedTrommeln[0] = {
                ...updatedTrommeln[0],
                Kabeltyp: item.cableType
            };
            setVerfügbareTrommeln(updatedTrommeln);
            toast.success(`Kabeltyp ${item.cableType} zur Trommel übertragen`);
        } else {
            toast.error('Keine verfügbare Trommel gefunden');
        }
    }, [delivery.items, verfügbareTrommeln]);

    // Lade die ersten 100 Kabelmaterialien beim Komponenten-Mount
    React.useEffect(() => {
        const loadInitialMaterials = async () => {
            try {
                const { materials } = await TrommelrechnungService.searchKabelmaterialien('', 100);
                setVerfügbareKabelmaterialien(materials);
            } catch (error) {
                console.error('Fehler beim Laden der Kabelmaterialien:', error);
                setVerfügbareKabelmaterialien([]);
            }
        };

        loadInitialMaterials();
    }, []);

    // Lade alle verfügbaren Trommeln ohne Filterung
    React.useEffect(() => {
        const loadAllTrommeln = async () => {
            try {
                const allTrommeln = await TrommelrechnungService.getVerfügbareTrommeln();
                setVerfügbareTrommeln(allTrommeln);
            } catch (error) {
                console.error('Fehler beim Laden der Trommeln:', error);
                setVerfügbareTrommeln([]);
            }
        };

        loadAllTrommeln();
    }, []); // Keine Abhängigkeiten - lädt nur einmal beim Mount

    // Handler für TrommelSelector
    const handleTrommelChange = useCallback((value: string) => {
        setSelectedTrommel(value);
    }, []);

    const handleTrommelSelect = useCallback((trommel: any) => {
        // Hier können weitere Aktionen bei Trommelauswahl implementiert werden
        console.log('Trommel ausgewählt:', trommel);
    }, []);

    // Handler für Vergleich zwischen KI-Auswahl und manueller Auswahl
    const handleCompareSelections = useCallback(async () => {
        if (!currentPlan || manualDrumSelections.length === 0) {
            toast.error('Bitte führen Sie zuerst eine Optimierung durch und wählen Sie manuelle Trommeln aus');
            return;
        }

        try {
            // Konvertiere verfügbare Trommeln zu AvailableDrum Format
            const aiSuggestions = drums.map(drum => ({
                id: drum.id,
                material: drum.cableType,
                availableLength: drum.availableLength,
                remainingLength: drum.availableLength
            }));

            const result = await comparisonService.compareSelections(
                delivery.id,
                delivery.items,
                aiSuggestions,
                manualDrumSelections,
                new Date(selectionDate)
            );

            setComparisonResult(result);
            setShowComparison(true);
            toast.success(`Vergleich abgeschlossen! Qualitätsbewertung: ${result.qualityScore}%`);
        } catch (error) {
            console.error('Fehler beim Vergleich:', error);
            toast.error('Fehler beim Vergleich der Trommelauswahlen');
        }
    }, [currentPlan, manualDrumSelections, drums, delivery, selectionDate, comparisonService]);

    // Handler für automatisches Laden verfügbarer Trommeln basierend auf Lieferung
    const loadAvailableDrumsForDelivery = useCallback(async () => {
        if (delivery.items.length === 0) {
            toast.error('Bitte fügen Sie zuerst Artikel zur Lieferung hinzu');
            return;
        }

        try {
            const materials = delivery.items.map(item => item.cableType).filter(Boolean);
            const availableDrums = await inventoryService.getAvailableDrumsForDelivery(
                materials,
                new Date(selectionDate)
            );

            // Konvertiere zu ManualDrumSelection Format
            const suggestions = availableDrums.slice(0, 5).map(drum => ({
                chargeNumber: drum.charge || '',
                material: drum.material || '',
                gesamtbestand: drum.gesamtbestand || 0,
                lagereinheitentyp: drum.lagereinheitentyp || '',
                aufnahmedatum: selectionDate
            }));

            setManualDrumSelections(suggestions);
            toast.success(`${suggestions.length} verfügbare Trommeln geladen`);
        } catch (error) {
            console.error('Fehler beim Laden der Trommeln:', error);
            toast.error('Fehler beim Laden der verfügbaren Trommeln');
        }
    }, [delivery.items, selectionDate, inventoryService]);

    // Synchronisiere Kabeltyp von Lieferungen zu Trommeln
    React.useEffect(() => {
        const selectedCableType = getSelectedCableTypeFromOrders();
        if (selectedCableType && selectedCableType.trim() !== '') {
            // Aktualisiere alle Trommeln mit dem ausgewählten Kabeltyp
            const updatedDrums = drums.map(drum => ({
                ...drum,
                cableType: selectedCableType
            }));
            setDrums(updatedDrums);
        }
    }, [delivery, getSelectedCableTypeFromOrders, drums]);

    /**
     * Handle cutting optimization
     */
    const handleOptimize = useCallback(async () => {
        if (delivery.items.length === 0 || drums.length === 0) {
            toast.error('Bitte fügen Sie mindestens einen Kabeltyp und eine Trommel hinzu');
            return;
        }

        setIsOptimizing(true);

        try {
            // Konvertiere Delivery Items zu CuttingOrders für die Optimierung
            const orders: CuttingOrder[] = delivery.items.map(item => ({
                id: item.id,
                cableType: item.cableType,
                requiredLength: item.requiredLength,
                quantity: item.quantity,
                priority: item.priority,
                deadline: delivery.deadline
            }));

            // Speichere die aktuellen Orders für die Visualisierung
            setCurrentOrders(orders);

            const request: CuttingRequest = {
                orders,
                availableDrums: drums,
                constraints,
                priorities: orders.map((order, index) => ({
                    orderId: order.id,
                    priority: index + 1,
                    weight: delivery.items.find(item => item.id === order.id)?.priority === 'high' ? 3 :
                        delivery.items.find(item => item.id === order.id)?.priority === 'medium' ? 2 : 1
                }))
            };

            // Update algorithm if changed
            await optimizerService.initialize({ defaultAlgorithm: selectedAlgorithm });

            // Run optimization
            const plan = await optimizerService.optimizeCuttingPlan(request as unknown as import("@/modules/ai/types/cutting").CuttingRequest);
            setCurrentPlan(plan);

            // Calculate waste analysis
            const waste = await optimizerService.calculateWaste(plan);
            setWasteAnalysis(waste);

            // Generate alternatives
            const alts = await optimizerService.simulateAlternatives(request as unknown as import("@/modules/ai/types/cutting").CuttingRequest);
            setAlternatives(alts);

            toast.success(`Optimierung abgeschlossen! Effizienz: ${(plan.efficiency * 100).toFixed(1)}%`);

        } catch (error) {
            console.error('Optimization failed:', error);
            toast.error('Optimierung fehlgeschlagen: ' + (error instanceof Error ? error.message : 'Unbekannter Fehler'));
        } finally {
            setIsOptimizing(false);
        }
    }, [delivery, drums, constraints, selectedAlgorithm, optimizerService]);

    /**
     * Add new delivery item (Kabeltyp zur Lieferung hinzufügen)
     * Fügt automatisch auch eine neue Trommel mit dem gleichen Kabeltyp hinzu
     */
    const addDeliveryItem = useCallback(() => {
        const selectedCableType = cableTypes.length > 0 ? cableTypes[0].value : '0010000';

        // Neues Lieferungs-Item hinzufügen
        const newItem: DeliveryItem = {
            id: `item-${delivery.items.length + 1}`,
            cableType: selectedCableType,
            requiredLength: 1.0, // 100cm = 1.0m
            quantity: 1,
            priority: 'medium'
        };

        // Neue Trommel mit dem gleichen Kabeltyp hinzufügen
        const newDrum: DrumInventory = {
            id: `drum-${drums.length + 1}`,
            cableType: selectedCableType,
            totalLength: 10.0, // 1000cm = 10.0m
            availableLength: 10.0, // 1000cm = 10.0m
            quality: 'A'
        };

        // Beide Updates gleichzeitig durchführen
        setDelivery({
            ...delivery,
            items: [...delivery.items, newItem]
        });
        setDrums([...drums, newDrum]);

        toast.success('Kabeltyp zur Lieferung und verfügbaren Trommeln hinzugefügt');
    }, [delivery, drums, cableTypes]);

    /**
     * Remove delivery item (Kabeltyp aus Lieferung entfernen)
     */
    const removeDeliveryItem = useCallback((itemId: string) => {
        // Verhindere das Entfernen des letzten Items
        if (delivery.items.length <= 1) {
            toast.error('Mindestens ein Kabeltyp muss in der Lieferung verbleiben');
            return;
        }
        setDelivery({
            ...delivery,
            items: delivery.items.filter(item => item.id !== itemId)
        });
    }, [delivery]);

    /**
     * Update delivery item (Kabeltyp in Lieferung aktualisieren)
     */
    const updateDeliveryItem = useCallback((itemId: string, updates: Partial<DeliveryItem>) => {
        setDelivery({
            ...delivery,
            items: delivery.items.map(item =>
                item.id === itemId ? { ...item, ...updates } : item
            )
        });
    }, [delivery]);

    /**
     * Add new drum
     */
    const addDrum = useCallback(() => {
        const newDrum: DrumInventory = {
            id: `drum-${drums.length + 1}`,
            cableType: cableTypes.length > 0 ? cableTypes[0].value : '0010000',
            totalLength: 10.0, // 1000cm = 10.0m
            availableLength: 10.0, // 1000cm = 10.0m
            quality: 'A'
        };
        setDrums([...drums, newDrum]);
    }, [drums, cableTypes]);

    /**
     * Remove drum
     */
    const removeDrum = useCallback((drumId: string) => {
        setDrums(drums.filter(drum => drum.id !== drumId));
    }, [drums]);

    /**
     * Update drum
     */
    const updateDrum = useCallback((drumId: string, updates: Partial<DrumInventory>) => {
        setDrums(drums.map(drum =>
            drum.id === drumId ? { ...drum, ...updates } : drum
        ));
    }, [drums]);

    /**
     * Export cutting plan as PDF
     */
    const exportPDF = useCallback(async () => {
        if (!currentPlan) {
            toast.error('Kein Schnittplan zum Exportieren verfügbar');
            return;
        }

        try {
            const doc = new jsPDF();
            const pageWidth = doc.internal.pageSize.width;
            const margin = 20;
            let yPosition = margin;

            // Header
            doc.setFontSize(20);
            doc.setFont('helvetica', 'bold');
            doc.text('SCHNEID-OPTIMIERUNG BERICHT', pageWidth / 2, yPosition, { align: 'center' });
            yPosition += 20;

            // Datum und Algorithmus
            doc.setFontSize(12);
            doc.setFont('helvetica', 'normal');
            doc.text(`Erstellt am: ${new Date().toLocaleDateString('de-DE')}`, margin, yPosition);
            yPosition += 10;
            doc.text(`Algorithmus: ${selectedAlgorithm}`, margin, yPosition);
            yPosition += 20;

            // Plan Details
            doc.setFontSize(16);
            doc.setFont('helvetica', 'bold');
            doc.text('Schnittplan Details', margin, yPosition);
            yPosition += 15;

            doc.setFontSize(12);
            doc.setFont('helvetica', 'normal');
            doc.text(`Effizienz: ${(currentPlan.efficiency * 100).toFixed(1)}%`, margin, yPosition);
            yPosition += 10;
            // Calculate total length from drum allocations
            const totalLength = currentPlan.drumAllocations.reduce((sum, drum) => {
                return sum + drum.cuts.reduce((cutSum, cut) => cutSum + cut.length, 0);
            }, 0);
            const totalCuts = currentPlan.drumAllocations.reduce((sum, drum) => sum + drum.cuts.length, 0);

            doc.text(`Gesamtlänge: ${totalLength.toFixed(2)} m`, margin, yPosition);
            yPosition += 10;
            doc.text(`Anzahl Schnitte: ${totalCuts}`, margin, yPosition);
            yPosition += 20;

            // Waste Analysis
            if (wasteAnalysis) {
                doc.setFontSize(16);
                doc.setFont('helvetica', 'bold');
                doc.text('Verschnitt-Analyse', margin, yPosition);
                yPosition += 15;

                doc.setFontSize(12);
                doc.setFont('helvetica', 'normal');
                doc.text(`Verschnitt-Rate: ${(wasteAnalysis.wastePercentage * 100).toFixed(1)}%`, margin, yPosition);
                yPosition += 10;
                doc.text(`Gesamtverschnitt: ${wasteAnalysis.totalWaste.toFixed(2)} m`, margin, yPosition);
                yPosition += 20;
            }

            // Save PDF
            const fileName = `schnittplan-${new Date().toISOString().split('T')[0]}.pdf`;
            doc.save(fileName);
            toast.success('PDF erfolgreich exportiert');

        } catch (error) {
            console.error('PDF Export failed:', error);
            toast.error('PDF Export fehlgeschlagen');
        }
    }, [currentPlan, wasteAnalysis, selectedAlgorithm]);

    /**
     * Export cutting plan as Excel
     */
    const exportExcel = useCallback(async () => {
        if (!currentPlan) {
            toast.error('Kein Schnittplan zum Exportieren verfügbar');
            return;
        }

        try {
            // Calculate total length and cuts from drum allocations
            const totalLength = currentPlan.drumAllocations.reduce((sum, drum) => {
                return sum + drum.cuts.reduce((cutSum, cut) => cutSum + cut.length, 0);
            }, 0);
            const totalCuts = currentPlan.drumAllocations.reduce((sum, drum) => sum + drum.cuts.length, 0);

            // Create workbook
            const wb = XLSX.utils.book_new();

            // Overview Sheet
            const overviewData = [
                ['SCHNEID-OPTIMIERUNG BERICHT'],
                [''],
                ['Erstellt am:', new Date().toLocaleDateString('de-DE')],
                ['Algorithmus:', selectedAlgorithm],
                ['Effizienz:', `${(currentPlan.efficiency * 100).toFixed(1)}%`],
                ['Gesamtlänge:', `${totalLength.toFixed(2)} m`],
                ['Anzahl Schnitte:', totalCuts],
                ['']
            ];

            if (wasteAnalysis) {
                overviewData.push(
                    ['VERSCHNITT-ANALYSE'],
                    ['Verschnitt-Rate:', `${(wasteAnalysis.wastePercentage * 100).toFixed(1)}%`],
                    ['Gesamtverschnitt:', `${wasteAnalysis.totalWaste.toFixed(2)} m`]
                );
            }

            const overviewWs = XLSX.utils.aoa_to_sheet(overviewData);
            XLSX.utils.book_append_sheet(wb, overviewWs, 'Übersicht');

            // Save Excel file
            const fileName = `schnittplan-${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, fileName);
            toast.success('Excel erfolgreich exportiert');

        } catch (error) {
            console.error('Excel Export failed:', error);
            toast.error('Excel Export fehlgeschlagen');
        }
    }, [currentPlan, wasteAnalysis, selectedAlgorithm, alternatives]);

    // Tab configuration
    const tabs = [
        { id: 'trommelrechnung', title: 'Trommelrechnung', icon: Target, color: 'bg-blue-500 hover:bg-blue-600' },
        { id: 'configuration', title: 'Konfiguration', icon: Brain, color: 'bg-purple-500 hover:bg-purple-600' },
        { id: 'visualization', title: 'Visualisierung', icon: CheckCircle, color: 'bg-green-500 hover:bg-green-600' },
        { id: 'alternatives', title: 'Alternativen', icon: AlertTriangle, color: 'bg-orange-500 hover:bg-orange-600' },
        { id: 'analysis', title: 'Analyse', icon: Scissors, color: 'bg-red-500 hover:bg-red-600' }
    ];

    const tabContent = {
        trommelrechnung: (
            <div className="space-y-6">
                <TrommelrechnungComponent />
            </div>
        ),
        configuration: (
            <CuttingConfigurationSection
                // Algorithmus & Constraints
                selectedAlgorithm={selectedAlgorithm}
                onAlgorithmChange={setSelectedAlgorithm}
                wasteType={wasteType}
                onWasteTypeChange={setWasteType}
                constraints={constraints}
                onConstraintsChange={setConstraints}
                
                // Lieferung
                delivery={delivery}
                verfügbareKabelmaterialien={verfügbareKabelmaterialien}
                kabelMaterialSuchOpen={kabelMaterialSuchOpen}
                kabelMaterialSuchWert={kabelMaterialSuchWert}
                onMaterialSuchOpenChange={setKabelMaterialSuchOpen}
                onMaterialSuchWertChange={setKabelMaterialSuchWert}
                onKabelMaterialChange={handleKabelMaterialChange}
                onItemMaterialSelect={handleItemMaterialSelect}
                onAddDeliveryItem={addDeliveryItem}
                onRemoveDeliveryItem={removeDeliveryItem}
                onUpdateDeliveryItem={updateDeliveryItem}
                
                // Trommeln
                drums={drums}
                verfügbareTrommeln={verfügbareTrommeln}
                selectedTrommel={selectedTrommel}
                trommelSuchOpen={trommelSuchOpen}
                trommelSuchWert={trommelSuchWert}
                selectedCableType={getSelectedCableTypeFromOrders()}
                onAddDrum={addDrum}
                onRemoveDrum={removeDrum}
                onUpdateDrum={updateDrum}
                onTrommelChange={handleTrommelChange}
                onTrommelSuchOpenChange={setTrommelSuchOpen}
                onTrommelSuchWertChange={setTrommelSuchWert}
                onTrommelSelect={handleTrommelSelect}
                
                // Optimierung & Export
                isOptimizing={isOptimizing}
                onOptimize={handleOptimize}
                hasPlan={!!currentPlan}
                onExportPlan={(format) => format === 'pdf' ? exportPDF() : exportExcel()}
                
                // Manuelle Auswahl & Vergleich
                manualDrumSelections={manualDrumSelections}
                selectionDate={selectionDate}
                onSelectionsChange={setManualDrumSelections}
                onDateChange={setSelectionDate}
                onAddManualSelection={addManualDrumSelection}
                onUpdateManualSelection={updateManualDrumSelection}
                onRemoveManualSelection={removeManualDrumSelection}
                comparisonResult={comparisonResult}
                showComparison={showComparison}
                
                // Placeholder für zusätzliche Props
                aiTrommelauswahlEnabled={false}
                onAITrommelauswahlToggle={() => {}}
                aiTrommelEmpfehlungen={null}
                onPreviewAISelection={() => {}}
                isLoadingAISelection={false}
                availableDrumsFromDB={[]}
                onAvailableDrumsSelected={() => {}}
            />
        ),
        visualization: (
            <CuttingVisualizationSection
                plan={currentPlan}
                drums={drums}
                orders={currentOrders}
            />
        ),
        alternatives: (
            <CuttingAlternativesSection
                alternatives={alternatives}
                onSelectAlternative={(alt) => setCurrentPlan(alt.plan)}
            />
        ),
        analysis: (
            <CuttingAnalysisSection
                wasteAnalysis={wasteAnalysis}
                plan={currentPlan}
            />
        )
    };

    return (
        <div className="w-full p-6 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
                        <Scissors className="h-8 w-8 text-blue-600" />
                        {t('cutting.optimization.title', 'Schneid-Optimierung')}
                    </h1>
                    <p className="text-gray-600 mt-2">
                        {t('cutting.optimization.description', 'KI-gestützte Optimierung für Kabelschnitte mit Trommelrechnung')}
                    </p>
                </div>
                <div className="flex gap-2">
                    <Button
                        onClick={exportPDF}
                        variant="outline"
                        disabled={!currentPlan}
                        className="flex items-center gap-2"
                    >
                        <Download className="h-4 w-4" />
                        PDF
                    </Button>
                    <Button
                        onClick={exportExcel}
                        variant="outline"
                        disabled={!currentPlan}
                        className="flex items-center gap-2"
                    >
                        <Download className="h-4 w-4" />
                        Excel
                    </Button>
                </div>
            </div>

            {/* Smooth Tabs in schmalerer Breite */}
            <div className="max-w-4xl mx-auto">
                <SmoothTab
                    items={tabs}
                    defaultTabId={activeTab}
                    onChange={setActiveTab}
                    hideCardContent={true}
                />
            </div>

            {/* Tab Content direkt ohne äußere Card - volle Breite */}
            <div className="w-full mt-6">
                {tabContent[activeTab as keyof typeof tabContent]}
            </div>
        </div>
    );
}