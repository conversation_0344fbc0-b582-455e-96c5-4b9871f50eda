import { sqliteTable, AnySQLiteColumn, text, numeric, integer, index, uniqueIndex, foreignKey, blob, real, primaryKey } from "drizzle-orm/sqlite-core"
  import { sql } from "drizzle-orm"

export const knowledgeBases = sqliteTable("knowledge_bases", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	name: text().notNull(),
	description: text(),
	category: text().notNull(),
	isActive: numeric().default(true).notNull(),
	createdAt: numeric().default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric().notNull(),
},
(table) => [
	index("knowledge_bases_createdAt_idx").on(table.createdAt),
	index("knowledge_bases_isActive_idx").on(table.isActive),
	index("knowledge_bases_category_idx").on(table.category),
	uniqueIndex("knowledge_bases_name_key").on(table.name),
]);

export const documents = sqliteTable("documents", {
	id: text().primaryKey().notNull(),
	knowledgeBaseId: integer("knowledge_base_id").notNull().references(() => knowledgeBases.id, { onDelete: "cascade", onUpdate: "cascade" } ),
	title: text().notNull(),
	content: text().notNull(),
	contentType: text("content_type").default("text/plain").notNull(),
	source: text().notNull(),
	sourcePath: text("source_path"),
	language: text().default("de").notNull(),
	metadata: text(),
	hash: text(),
	isActive: numeric().default(true).notNull(),
	status: text().default("pending").notNull(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	indexedAt: numeric("indexed_at"),
},
(table) => [
	index("idx_documents_created_at").on(table.createdAt),
	index("idx_documents_status").on(table.status),
	index("idx_documents_source").on(table.source),
	index("documents_language_idx").on(table.language),
	index("documents_created_at_idx").on(table.createdAt),
	index("documents_source_idx").on(table.source),
	index("documents_status_idx").on(table.status),
	index("documents_isActive_idx").on(table.isActive),
	index("documents_hash_idx").on(table.hash),
	index("documents_knowledge_base_id_idx").on(table.knowledgeBaseId),
	uniqueIndex("documents_hash_key").on(table.hash),
]);

export const chunks = sqliteTable("chunks", {
	id: text().primaryKey().notNull(),
	documentId: text("document_id").notNull().references(() => documents.id, { onDelete: "cascade", onUpdate: "cascade" } ),
	chunkIndex: integer("chunk_index").notNull(),
	content: text().notNull(),
	tokenCount: integer("token_count"),
	startPosition: integer("start_position"),
	endPosition: integer("end_position"),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("idx_chunks_chunk_index").on(table.chunkIndex),
	index("idx_chunks_document_id").on(table.documentId),
	index("chunks_created_at_idx").on(table.createdAt),
	index("chunks_chunk_index_idx").on(table.chunkIndex),
	index("chunks_document_id_idx").on(table.documentId),
]);

export const embeddings = sqliteTable("embeddings", {
	id: text().primaryKey().notNull(),
	chunkId: text("chunk_id").notNull().references(() => chunks.id, { onDelete: "cascade", onUpdate: "cascade" } ),
	vector: blob().notNull(),
	modelName: text("model_name").default("text-embedding-3-small").notNull(),
	dimension: integer().default(1536).notNull(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("idx_embeddings_model_name").on(table.modelName),
	index("idx_embeddings_chunk_id").on(table.chunkId),
	index("embeddings_model_name_idx").on(table.modelName),
	index("embeddings_chunk_id_idx").on(table.chunkId),
]);

export const ragQueries = sqliteTable("rag_queries", {
	id: text().primaryKey().notNull(),
	query: text().notNull(),
	queryEmbedding: blob("query_embedding"),
	intent: text(),
	language: text().default("de").notNull(),
	resultsFound: integer("results_found").notNull(),
	topSimilarity: real("top_similarity"),
	responseGenerated: numeric("response_generated").notNull(),
	executionTimeMs: integer("execution_time_ms").notNull(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("rag_queries_response_generated_idx").on(table.responseGenerated),
	index("rag_queries_language_idx").on(table.language),
	index("rag_queries_created_at_idx").on(table.createdAt),
]);

export const similarityResults = sqliteTable("similarity_results", {
	id: integer().primaryKey({ autoIncrement: true }).notNull(),
	queryId: text("query_id").notNull().references(() => ragQueries.id, { onDelete: "cascade", onUpdate: "cascade" } ),
	chunkId: text("chunk_id").notNull().references(() => chunks.id, { onDelete: "cascade", onUpdate: "cascade" } ),
	similarity: real().notNull(),
	rank: integer().notNull(),
	used: numeric().notNull(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("similarity_results_used_idx").on(table.used),
	index("similarity_results_rank_idx").on(table.rank),
	index("similarity_results_similarity_idx").on(table.similarity),
	index("similarity_results_chunk_id_idx").on(table.chunkId),
	index("similarity_results_query_id_idx").on(table.queryId),
]);

export const categories = sqliteTable("categories", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	parentId: text("parent_id"),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("categories_parent_id_idx").on(table.parentId),
	uniqueIndex("categories_name_key").on(table.name),
	foreignKey(() => ({
			columns: [table.parentId],
			foreignColumns: [table.id],
			name: "categories_parent_id_categories_id_fk"
		})).onUpdate("cascade").onDelete("set null"),
]);

export const documentCategories = sqliteTable("document_categories", {
	documentId: text("document_id").notNull().references(() => documents.id, { onDelete: "cascade", onUpdate: "cascade" } ),
	categoryId: text("category_id").notNull().references(() => categories.id, { onDelete: "cascade", onUpdate: "cascade" } ),
},
(table) => [
	primaryKey({ columns: [table.documentId, table.categoryId], name: "document_categories_document_id_category_id_pk"})
]);

export const chunkMetadata = sqliteTable("chunk_metadata", {
	id: text().primaryKey().notNull(),
	chunkId: text("chunk_id").notNull().references(() => chunks.id, { onDelete: "cascade", onUpdate: "cascade" } ),
	key: text().notNull(),
	value: text(),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("idx_chunk_metadata_key").on(table.key),
	index("idx_chunk_metadata_chunk_id").on(table.chunkId),
	index("chunk_metadata_key_idx").on(table.key),
	index("chunk_metadata_chunk_id_idx").on(table.chunkId),
]);

export const searchHistory = sqliteTable("search_history", {
	id: text().primaryKey().notNull(),
	query: text().notNull(),
	queryEmbedding: blob("query_embedding"),
	resultsCount: integer("results_count"),
	topSimilarityScore: real("top_similarity_score"),
	responseTimeMs: integer("response_time_ms"),
	userId: text("user_id"),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
},
(table) => [
	index("idx_search_history_created_at").on(table.createdAt),
	index("search_history_created_at_idx").on(table.createdAt),
]);

export const ragSettings = sqliteTable("rag_settings", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	isActive: numeric().notNull(),
	vectorDatabaseConfig: text("vector_database_config").notNull(),
	securityConfig: text("security_config").notNull(),
	servicesConfig: text("services_config").notNull(),
	version: text().default("1.0.0").notNull(),
	userId: text("user_id"),
	createdAt: numeric("created_at").default(sql`(CURRENT_TIMESTAMP)`).notNull(),
	updatedAt: numeric("updated_at").notNull(),
},
(table) => [
	index("rag_settings_created_at_idx").on(table.createdAt),
	index("rag_settings_user_id_idx").on(table.userId),
	index("rag_settings_isActive_idx").on(table.isActive),
	index("rag_settings_name_idx").on(table.name),
	uniqueIndex("rag_settings_name_key").on(table.name),
]);

