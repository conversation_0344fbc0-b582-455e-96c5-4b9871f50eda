import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON>le, CardFooter } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import LangToggle from "@/components/LangToggle";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Settings, Database, Table as TableIcon, AlertCircle, Eye, Monitor, Bell, Shield, Palette, Globe, HardDrive, Wifi, Save } from "lucide-react";
import SmoothTab from "@/components/Animation/kokonutui/smooth-tab";
import useDatabase from "@/hooks/useDatabase";
import TableDataDialog from "@/components/TableDataDialog";

// Typdefinitionen
interface TableInfo {
  name: string;
}

interface TableData {
  columns: string[];
  rows: any[];
}
/**
 * Einstellungsseite für das Shopfloor-Management-Dashboard
 * 
 * Ermöglicht die Anpassung verschiedener Einstellungen wie Sprache und Theme.
 * Bietet auch eine Schnittstelle zur Datenbankverwaltung.
 */
export default function SettingsPage() {
  // Tab state
  const [activeTab, setActiveTab] = useState("general");

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  // Database settings
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [selectedTable, setSelectedTable] = useState<string>("");
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [customDbPath, setCustomDbPath] = useState<string>('D:/my_ai/4-Lapp/SFM-Electron/database/sfm_dashboard.db');
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);

  // General settings
  const [generalSettings, setGeneralSettings] = useState({
    autoStart: true,
    minimizeToTray: false,
    closeToTray: true,
    autoUpdate: true,
    crashReporting: true,
    analytics: false
  });

  // Display settings
  const [displaySettings, setDisplaySettings] = useState({
    theme: "system",
    fontSize: "medium",
    language: "de",
    highContrast: false,
    animations: true,
    compactMode: false
  });

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState({
    desktopNotifications: true,
    soundNotifications: true,
    emailNotifications: false,
    systemAlerts: true,
    maintenanceAlerts: true,
    errorAlerts: true
  });

  // Performance settings
  const [performanceSettings, setPerformanceSettings] = useState({
    hardwareAcceleration: true,
    backgroundSync: true,
    cacheSize: "500",
    maxMemoryUsage: "2048",
    autoCleanup: true,
    debugMode: false
  });

  // Network settings
  const [networkSettings, setNetworkSettings] = useState({
    proxyEnabled: false,
    proxyHost: "",
    proxyPort: "",
    timeout: "30",
    retryAttempts: "3",
    offlineMode: false
  });

  // Security settings
  const [securitySettings, setSecuritySettings] = useState({
    autoLock: true,
    lockTimeout: "15",
    requirePassword: true,
    encryptData: true,
    auditLogging: true,
    sessionTimeout: "60"
  });

  const {
    connect,
    getTables,
    getTableData,
    isLoading,
    error,
    isConnected
  } = useDatabase();

  // Settings handlers
  const handleGeneralChange = (field: string, value: boolean | string) => {
    setGeneralSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleDisplayChange = (field: string, value: string | boolean) => {
    setDisplaySettings(prev => ({ ...prev, [field]: value }));
  };

  const handleNotificationChange = (field: string, value: boolean) => {
    setNotificationSettings(prev => ({ ...prev, [field]: value }));
  };

  const handlePerformanceChange = (field: string, value: string | boolean) => {
    setPerformanceSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleNetworkChange = (field: string, value: string | boolean) => {
    setNetworkSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleSecurityChange = (field: string, value: string | boolean) => {
    setSecuritySettings(prev => ({ ...prev, [field]: value }));
  };

  // Save settings
  const handleSaveSettings = async (settingsType: string) => {
    try {
      // Here you would save settings to backend/localStorage
      console.log(`${settingsType} settings saved:`,
        settingsType === "general" ? generalSettings :
          settingsType === "display" ? displaySettings :
            settingsType === "notifications" ? notificationSettings :
              settingsType === "performance" ? performanceSettings :
                settingsType === "network" ? networkSettings :
                  settingsType === "security" ? securitySettings : null
      );
      alert(`${settingsType} Einstellungen erfolgreich gespeichert!`);
    } catch (error) {
      console.error(`Fehler beim Speichern der ${settingsType} Einstellungen:`, error);
      alert(`Fehler beim Speichern der ${settingsType} Einstellungen.`);
    }
  };

  // Datenbankverbindung herstellen
  const connectToDatabase = async () => {
    try {
      const success = await connect(customDbPath);
      if (success) {
        const tablesList = await getTables();
        setTables(tablesList);
      }
    } catch (err) {
      console.error('Fehler beim Verbinden mit der Datenbank:', err);
    }
  };

  // Tabellendaten laden
  const loadTableData = async (tableName: string) => {
    if (!tableName) return;

    try {
      const data = await getTableData(tableName);
      setTableData(data);
    } catch (err) {
      console.error('Fehler beim Laden der Tabellendaten:', err);
    }
  };

  // Tabelle wechseln
  const handleTableChange = (value: string) => {
    setSelectedTable(value);
    loadTableData(value);
  };

  // Dialog öffnen, um Tabellendaten anzuzeigen
  const openTableDataDialog = () => {
    if (selectedTable && tableData) {
      setIsDialogOpen(true);
    }
  };

  // Dialog schließen
  const closeTableDataDialog = () => {
    setIsDialogOpen(false);
  };

  // Initialen Verbindungsaufbau
  useEffect(() => {
    // Automatische Verbindung beim ersten Laden der Komponente
    const init = async () => {
      await connectToDatabase();
    };

    init();

    return () => {
      // Aufräumarbeiten hier, falls nötig
    };
  }, []);

  return (
    <div className="w-full bg-bg min-h-screen p-8">
      <div className="max-w-5xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between w-full mb-8">
          <div className="flex-1">
            <h1 className="text-4xl font-heading tracking-tight text-text flex items-center gap-2">
              <Settings className="h-8 w-8" />
              Application Settings
            </h1>
            <p className="text-lg text-text font-base mt-2 opacity-70">Konfiguriere die Anwendung nach deinen Bedürfnissen</p>
          </div>
        </div>

        {/* Smooth Tabs */}
        <div className="space-y-6">
          <div className="flex justify-center">
            <SmoothTab
              items={[
                {
                  id: "general",
                  title: "Allgemein",
                  icon: Settings,
                  color: "bg-blue-500 hover:bg-blue-600",
                },
                {
                  id: "display",
                  title: "Darstellung",
                  icon: Monitor,
                  color: "bg-green-500 hover:bg-green-600",
                },
                {
                  id: "notifications",
                  title: "Benachrichtigungen",
                  icon: Bell,
                  color: "bg-orange-500 hover:bg-orange-600",
                },
                {
                  id: "performance",
                  title: "Leistung",
                  icon: HardDrive,
                  color: "bg-purple-500 hover:bg-purple-600",
                },
                {
                  id: "network",
                  title: "Netzwerk",
                  icon: Wifi,
                  color: "bg-cyan-500 hover:bg-cyan-600",
                },
                {
                  id: "security",
                  title: "Sicherheit",
                  icon: Shield,
                  color: "bg-red-500 hover:bg-red-600",
                },
                {
                  id: "database",
                  title: "Datenbank",
                  icon: Database,
                  color: "bg-indigo-500 hover:bg-indigo-600",
                },
              ]}
              defaultTabId="general"
              onChange={handleTabChange}
              hideCardContent={true}
            />
          </div>

          {/* Tab Content */}
          <div className="mt-6">
            {activeTab === "general" && (
              <Card className="border-t-4 border-t-blue-500">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-blue-500">
                    <Settings className="h-5 w-5" />
                    Allgemeine Einstellungen
                  </CardTitle>
                  <CardDescription>
                    Grundlegende Anwendungseinstellungen und Verhalten.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="auto-start">Automatischer Start</Label>
                        <p className="text-sm text-muted-foreground">
                          Anwendung beim Systemstart automatisch starten.
                        </p>
                      </div>
                      <Switch
                        id="auto-start"
                        checked={generalSettings.autoStart}
                        onCheckedChange={(checked) => handleGeneralChange("autoStart", checked)}
                        className="data-[state=checked]:bg-blue-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="minimize-tray">In Systemleiste minimieren</Label>
                        <p className="text-sm text-muted-foreground">
                          Anwendung in die Systemleiste minimieren statt schließen.
                        </p>
                      </div>
                      <Switch
                        id="minimize-tray"
                        checked={generalSettings.minimizeToTray}
                        onCheckedChange={(checked) => handleGeneralChange("minimizeToTray", checked)}
                        className="data-[state=checked]:bg-blue-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="close-tray">Beim Schließen in Systemleiste</Label>
                        <p className="text-sm text-muted-foreground">
                          Anwendung beim Schließen in der Systemleiste belassen.
                        </p>
                      </div>
                      <Switch
                        id="close-tray"
                        checked={generalSettings.closeToTray}
                        onCheckedChange={(checked) => handleGeneralChange("closeToTray", checked)}
                        className="data-[state=checked]:bg-blue-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="auto-update">Automatische Updates</Label>
                        <p className="text-sm text-muted-foreground">
                          Updates automatisch herunterladen und installieren.
                        </p>
                      </div>
                      <Switch
                        id="auto-update"
                        checked={generalSettings.autoUpdate}
                        onCheckedChange={(checked) => handleGeneralChange("autoUpdate", checked)}
                        className="data-[state=checked]:bg-blue-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="crash-reporting">Absturzberichte senden</Label>
                        <p className="text-sm text-muted-foreground">
                          Automatische Absturzberichte zur Verbesserung senden.
                        </p>
                      </div>
                      <Switch
                        id="crash-reporting"
                        checked={generalSettings.crashReporting}
                        onCheckedChange={(checked) => handleGeneralChange("crashReporting", checked)}
                        className="data-[state=checked]:bg-blue-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="analytics">Nutzungsstatistiken</Label>
                        <p className="text-sm text-muted-foreground">
                          Anonyme Nutzungsstatistiken zur Verbesserung teilen.
                        </p>
                      </div>
                      <Switch
                        id="analytics"
                        checked={generalSettings.analytics}
                        onCheckedChange={(checked) => handleGeneralChange("analytics", checked)}
                        className="data-[state=checked]:bg-blue-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="acceptb" onClick={() => handleSaveSettings("general")} className="ml-auto flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {activeTab === "display" && (
              <Card className="border-t-4 border-t-green-500">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-green-500">
                    <Monitor className="h-5 w-5" />
                    Darstellungseinstellungen
                  </CardTitle>
                  <CardDescription>
                    Anpassen der visuellen Darstellung und Benutzeroberfläche.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="theme">Theme</Label>
                      <Select
                        value={displaySettings.theme}
                        onValueChange={(value) => handleDisplayChange("theme", value)}
                      >
                        <SelectTrigger id="theme">
                          <SelectValue placeholder="Wähle ein Theme" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Hell</SelectItem>
                          <SelectItem value="dark">Dunkel</SelectItem>
                          <SelectItem value="system">System</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="font-size">Schriftgröße</Label>
                      <Select
                        value={displaySettings.fontSize}
                        onValueChange={(value) => handleDisplayChange("fontSize", value)}
                      >
                        <SelectTrigger id="font-size">
                          <SelectValue placeholder="Wähle eine Schriftgröße" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="small">Klein</SelectItem>
                          <SelectItem value="medium">Mittel</SelectItem>
                          <SelectItem value="large">Groß</SelectItem>
                          <SelectItem value="extra-large">Sehr groß</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="language">Sprache</Label>
                      <Select
                        value={displaySettings.language}
                        onValueChange={(value) => handleDisplayChange("language", value)}
                      >
                        <SelectTrigger id="language">
                          <SelectValue placeholder="Wähle eine Sprache" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="de">Deutsch</SelectItem>
                          <SelectItem value="en">English</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="high-contrast">Hoher Kontrast</Label>
                        <p className="text-sm text-muted-foreground">
                          Erhöhter Kontrast für bessere Lesbarkeit.
                        </p>
                      </div>
                      <Switch
                        id="high-contrast"
                        checked={displaySettings.highContrast}
                        onCheckedChange={(checked) => handleDisplayChange("highContrast", checked)}
                        className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="animations">Animationen</Label>
                        <p className="text-sm text-muted-foreground">
                          Animationen und Übergänge aktivieren.
                        </p>
                      </div>
                      <Switch
                        id="animations"
                        checked={displaySettings.animations}
                        onCheckedChange={(checked) => handleDisplayChange("animations", checked)}
                        className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="compact-mode">Kompakter Modus</Label>
                        <p className="text-sm text-muted-foreground">
                          Reduzierte Abstände für mehr Inhalt auf dem Bildschirm.
                        </p>
                      </div>
                      <Switch
                        id="compact-mode"
                        checked={displaySettings.compactMode}
                        onCheckedChange={(checked) => handleDisplayChange("compactMode", checked)}
                        className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="acceptb" onClick={() => handleSaveSettings("display")} className="ml-auto flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {activeTab === "notifications" && (
              <Card className="border-t-4 border-t-orange-500">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-orange-500">
                    <Bell className="h-5 w-5" />
                    Benachrichtigungseinstellungen
                  </CardTitle>
                  <CardDescription>
                    Konfiguriere, wie und wann du benachrichtigt werden möchtest.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="desktop-notifications">Desktop-Benachrichtigungen</Label>
                        <p className="text-sm text-muted-foreground">
                          Benachrichtigungen auf dem Desktop anzeigen.
                        </p>
                      </div>
                      <Switch
                        id="desktop-notifications"
                        checked={notificationSettings.desktopNotifications}
                        onCheckedChange={(checked) => handleNotificationChange("desktopNotifications", checked)}
                        className="data-[state=checked]:bg-orange-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="sound-notifications">Ton-Benachrichtigungen</Label>
                        <p className="text-sm text-muted-foreground">
                          Akustische Signale bei Benachrichtigungen.
                        </p>
                      </div>
                      <Switch
                        id="sound-notifications"
                        checked={notificationSettings.soundNotifications}
                        onCheckedChange={(checked) => handleNotificationChange("soundNotifications", checked)}
                        className="data-[state=checked]:bg-orange-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="email-notifications">E-Mail-Benachrichtigungen</Label>
                        <p className="text-sm text-muted-foreground">
                          Wichtige Ereignisse per E-Mail erhalten.
                        </p>
                      </div>
                      <Switch
                        id="email-notifications"
                        checked={notificationSettings.emailNotifications}
                        onCheckedChange={(checked) => handleNotificationChange("emailNotifications", checked)}
                        className="data-[state=checked]:bg-orange-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <Separator />

                    <h3 className="text-lg font-medium">Benachrichtigungstypen</h3>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="system-alerts">Systemwarnungen</Label>
                      <Switch
                        id="system-alerts"
                        checked={notificationSettings.systemAlerts}
                        onCheckedChange={(checked) => handleNotificationChange("systemAlerts", checked)}
                        className="data-[state=checked]:bg-orange-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="maintenance-alerts">Wartungsmeldungen</Label>
                      <Switch
                        id="maintenance-alerts"
                        checked={notificationSettings.maintenanceAlerts}
                        onCheckedChange={(checked) => handleNotificationChange("maintenanceAlerts", checked)}
                        className="data-[state=checked]:bg-orange-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="error-alerts">Fehlermeldungen</Label>
                      <Switch
                        id="error-alerts"
                        checked={notificationSettings.errorAlerts}
                        onCheckedChange={(checked) => handleNotificationChange("errorAlerts", checked)}
                        className="data-[state=checked]:bg-orange-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="acceptb" onClick={() => handleSaveSettings("notifications")} className="ml-auto flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {activeTab === "performance" && (
              <Card className="border-t-4 border-t-purple-500">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-purple-500">
                    <HardDrive className="h-5 w-5" />
                    Leistungseinstellungen
                  </CardTitle>
                  <CardDescription>
                    Optimiere die Anwendungsleistung und Ressourcennutzung.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="hardware-acceleration">Hardware-Beschleunigung</Label>
                        <p className="text-sm text-muted-foreground">
                          GPU für bessere Leistung verwenden.
                        </p>
                      </div>
                      <Switch
                        id="hardware-acceleration"
                        checked={performanceSettings.hardwareAcceleration}
                        onCheckedChange={(checked) => handlePerformanceChange("hardwareAcceleration", checked)}
                        className="data-[state=checked]:bg-purple-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="background-sync">Hintergrund-Synchronisation</Label>
                        <p className="text-sm text-muted-foreground">
                          Daten im Hintergrund synchronisieren.
                        </p>
                      </div>
                      <Switch
                        id="background-sync"
                        checked={performanceSettings.backgroundSync}
                        onCheckedChange={(checked) => handlePerformanceChange("backgroundSync", checked)}
                        className="data-[state=checked]:bg-purple-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="auto-cleanup">Automatische Bereinigung</Label>
                        <p className="text-sm text-muted-foreground">
                          Temporäre Dateien automatisch löschen.
                        </p>
                      </div>
                      <Switch
                        id="auto-cleanup"
                        checked={performanceSettings.autoCleanup}
                        onCheckedChange={(checked) => handlePerformanceChange("autoCleanup", checked)}
                        className="data-[state=checked]:bg-purple-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="debug-mode">Debug-Modus</Label>
                        <p className="text-sm text-muted-foreground">
                          Erweiterte Protokollierung für Entwickler.
                        </p>
                      </div>
                      <Switch
                        id="debug-mode"
                        checked={performanceSettings.debugMode}
                        onCheckedChange={(checked) => handlePerformanceChange("debugMode", checked)}
                        className="data-[state=checked]:bg-purple-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <Separator />

                    <div className="grid gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="cache-size">Cache-Größe (MB)</Label>
                        <Input
                          id="cache-size"
                          type="number"
                          value={performanceSettings.cacheSize}
                          onChange={(e) => handlePerformanceChange("cacheSize", e.target.value)}
                          placeholder="500"
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="max-memory">Maximaler Speicherverbrauch (MB)</Label>
                        <Input
                          id="max-memory"
                          type="number"
                          value={performanceSettings.maxMemoryUsage}
                          onChange={(e) => handlePerformanceChange("maxMemoryUsage", e.target.value)}
                          placeholder="2048"
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="acceptb" onClick={() => handleSaveSettings("performance")} className="ml-auto flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {activeTab === "network" && (
              <Card className="border-t-4 border-t-cyan-500">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-cyan-500">
                    <Wifi className="h-5 w-5" />
                    Netzwerkeinstellungen
                  </CardTitle>
                  <CardDescription>
                    Konfiguriere Netzwerkverbindungen und Proxy-Einstellungen.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="proxy-enabled">Proxy verwenden</Label>
                        <p className="text-sm text-muted-foreground">
                          Internetverbindung über Proxy-Server.
                        </p>
                      </div>
                      <Switch
                        id="proxy-enabled"
                        checked={networkSettings.proxyEnabled}
                        onCheckedChange={(checked) => handleNetworkChange("proxyEnabled", checked)}
                        className="data-[state=checked]:bg-cyan-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    {networkSettings.proxyEnabled && (
                      <div className="grid gap-4 pl-4 border-l-2 border-cyan-200">
                        <div className="grid gap-2">
                          <Label htmlFor="proxy-host">Proxy-Host</Label>
                          <Input
                            id="proxy-host"
                            value={networkSettings.proxyHost}
                            onChange={(e) => handleNetworkChange("proxyHost", e.target.value)}
                            placeholder="proxy.example.com"
                          />
                        </div>

                        <div className="grid gap-2">
                          <Label htmlFor="proxy-port">Proxy-Port</Label>
                          <Input
                            id="proxy-port"
                            type="number"
                            value={networkSettings.proxyPort}
                            onChange={(e) => handleNetworkChange("proxyPort", e.target.value)}
                            placeholder="8080"
                          />
                        </div>
                      </div>
                    )}

                    <Separator />

                    <div className="grid gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="timeout">Verbindungs-Timeout (Sekunden)</Label>
                        <Input
                          id="timeout"
                          type="number"
                          value={networkSettings.timeout}
                          onChange={(e) => handleNetworkChange("timeout", e.target.value)}
                          placeholder="30"
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="retry-attempts">Wiederholungsversuche</Label>
                        <Input
                          id="retry-attempts"
                          type="number"
                          value={networkSettings.retryAttempts}
                          onChange={(e) => handleNetworkChange("retryAttempts", e.target.value)}
                          placeholder="3"
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="offline-mode">Offline-Modus</Label>
                        <p className="text-sm text-muted-foreground">
                          Anwendung ohne Internetverbindung verwenden.
                        </p>
                      </div>
                      <Switch
                        id="offline-mode"
                        checked={networkSettings.offlineMode}
                        onCheckedChange={(checked) => handleNetworkChange("offlineMode", checked)}
                        className="data-[state=checked]:bg-cyan-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="acceptb" onClick={() => handleSaveSettings("network")} className="ml-auto flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {activeTab === "security" && (
              <Card className="border-t-4 border-t-red-500">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-red-500">
                    <Shield className="h-5 w-5" />
                    Sicherheitseinstellungen
                  </CardTitle>
                  <CardDescription>
                    Konfiguriere Sicherheitsrichtlinien und Zugriffskontrollen.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="auto-lock">Automatische Sperre</Label>
                        <p className="text-sm text-muted-foreground">
                          Anwendung nach Inaktivität automatisch sperren.
                        </p>
                      </div>
                      <Switch
                        id="auto-lock"
                        checked={securitySettings.autoLock}
                        onCheckedChange={(checked) => handleSecurityChange("autoLock", checked)}
                        className="data-[state=checked]:bg-red-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="require-password">Passwort erforderlich</Label>
                        <p className="text-sm text-muted-foreground">
                          Passwort für den Zugriff auf die Anwendung erforderlich.
                        </p>
                      </div>
                      <Switch
                        id="require-password"
                        checked={securitySettings.requirePassword}
                        onCheckedChange={(checked) => handleSecurityChange("requirePassword", checked)}
                        className="data-[state=checked]:bg-red-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="encrypt-data">Daten verschlüsseln</Label>
                        <p className="text-sm text-muted-foreground">
                          Lokale Daten verschlüsselt speichern.
                        </p>
                      </div>
                      <Switch
                        id="encrypt-data"
                        checked={securitySettings.encryptData}
                        onCheckedChange={(checked) => handleSecurityChange("encryptData", checked)}
                        className="data-[state=checked]:bg-red-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="audit-logging">Audit-Protokollierung</Label>
                        <p className="text-sm text-muted-foreground">
                          Benutzeraktivitäten für Sicherheitszwecke protokollieren.
                        </p>
                      </div>
                      <Switch
                        id="audit-logging"
                        checked={securitySettings.auditLogging}
                        onCheckedChange={(checked) => handleSecurityChange("auditLogging", checked)}
                        className="data-[state=checked]:bg-red-500 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>

                    <Separator />

                    <div className="grid gap-4">
                      <div className="grid gap-2">
                        <Label htmlFor="lock-timeout">Sperr-Timeout (Minuten)</Label>
                        <Input
                          id="lock-timeout"
                          type="number"
                          value={securitySettings.lockTimeout}
                          onChange={(e) => handleSecurityChange("lockTimeout", e.target.value)}
                          placeholder="15"
                        />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="session-timeout">Sitzungs-Timeout (Minuten)</Label>
                        <Input
                          id="session-timeout"
                          type="number"
                          value={securitySettings.sessionTimeout}
                          onChange={(e) => handleSecurityChange("sessionTimeout", e.target.value)}
                          placeholder="60"
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="acceptb" onClick={() => handleSaveSettings("security")} className="ml-auto flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {activeTab === "database" && (
              <Card className="border-t-4 border-t-blue-500">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-blue-500">
                    <Database className="h-5 w-5" />
                    Datenbank-Verwaltung
                  </CardTitle>
                  <CardDescription>Verbindung zur SQLite-Datenbank herstellen und verwalten</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {error && (
                    <div className="p-4 bg-red-50 text-red-600 rounded-md flex items-start gap-2">
                      <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="font-medium">Fehler</p>
                        <p className="text-sm">{error}</p>
                      </div>
                    </div>
                  )}

                  {!isConnected ? (
                    <div className="space-y-4">
                      <p className="text-muted-foreground">
                        Verbinden Sie sich mit der SQLite-Datenbank, um Tabellen anzuzeigen und zu verwalten.
                      </p>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Datenbankpfad</Label>
                        <div className="flex gap-2">
                          <Input
                            type="text"
                            value={customDbPath}
                            onChange={(e) => setCustomDbPath(e.target.value)}
                            placeholder="Pfad zur Datenbankdatei"
                            className="flex-1"
                          />
                        </div>
                      </div>

                      <Button
                        onClick={connectToDatabase}
                        disabled={isLoading}
                        className="flex items-center gap-2"
                      >
                        <Database className="h-4 w-4" />
                        {isLoading ? 'Verbindung wird hergestellt...' : 'Mit Datenbank verbinden'}
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 text-green-500">
                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                        <span>Mit Datenbank sfm_dashboard.db verbunden</span>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Tabelle auswählen</Label>
                        <div className="flex gap-2">
                          <Select value={selectedTable} onValueChange={handleTableChange}>
                            <SelectTrigger className="w-64">
                              <SelectValue placeholder="Tabelle auswählen" />
                            </SelectTrigger>
                            <SelectContent>
                              {tables.map((table) => (
                                <SelectItem key={table.name} value={table.name}>
                                  <div className="flex items-center gap-2">
                                    <TableIcon className="h-4 w-4" />
                                    {table.name}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>

                          {selectedTable && (
                            <Button
                              variant="outline"
                              onClick={openTableDataDialog}
                              title="Tabellendaten anzeigen"
                              className="flex items-center gap-2"
                            >
                              <Eye className="h-4 w-4" />
                              Daten anzeigen
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Dialog für Tabellendaten */}
                  <TableDataDialog
                    isOpen={isDialogOpen}
                    onClose={closeTableDataDialog}
                    tableName={selectedTable}
                    tableData={tableData}
                  />
                </CardContent>
                <CardFooter>
                  <Button variant="acceptb" onClick={() => handleSaveSettings("database")} className="ml-auto flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Speichern
                  </Button>
                </CardFooter>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}  
