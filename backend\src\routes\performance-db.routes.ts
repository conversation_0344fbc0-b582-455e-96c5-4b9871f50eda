/**
 * Database-Persistent Performance Monitoring Routes
 * 
 * Erweiterte Performance-Monitoring-Endpunkte, die mit der datenbankpersistenten
 * Version des Performance Monitoring Service arbeiten.
 */

import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma-sfm-dashboard/client';
import DatabasePerformanceMonitoringService from '../services/performance-monitoring-db.service';
import dataCache from '../services/data-cache.service';

const router = Router();
const prisma = new PrismaClient();

// Initialize database performance monitoring service
const dbPerformanceMonitor = new DatabasePerformanceMonitoringService(prisma, {
  enableInMemoryCache: true,
  inMemoryCacheSize: 1000,
  batchSize: 25,
  flushInterval: 30 * 1000, // 30 seconds
  retentionDays: 90, // 3 months
  enableAggregation: true
});

/**
 * Get comprehensive performance statistics from database
 */
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const timeRange = req.query.timeRange as string;
    let dateRange: { start: Date; end: Date } | undefined;

    if (timeRange) {
      const now = new Date();
      switch (timeRange) {
        case '5min':
          dateRange = {
            start: new Date(now.getTime() - 5 * 60 * 1000),
            end: now
          };
          break;
        case '1hour':
          dateRange = {
            start: new Date(now.getTime() - 60 * 60 * 1000),
            end: now
          };
          break;
        case '24hours':
          dateRange = {
            start: new Date(now.getTime() - 24 * 60 * 60 * 1000),
            end: now
          };
          break;
        case '7days':
          dateRange = {
            start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
            end: now
          };
          break;
        case '30days':
          dateRange = {
            start: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
            end: now
          };
          break;
      }
    }

    const stats = await dbPerformanceMonitor.getPerformanceStats(dateRange);
    
    res.json({
      success: true,
      data: stats,
      timeRange: timeRange || 'all',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting performance stats from database:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve performance statistics from database'
    });
  }
});

/**
 * Get performance trends over time
 */
router.get('/trends/:metric', async (req: Request, res: Response) => {
  try {
    const metric = req.params.metric as 'response_time' | 'success_rate' | 'cache_hit_rate';
    const period = req.query.period as 'hour' | 'day' | 'week' || 'hour';
    const limit = parseInt(req.query.limit as string) || 24;

    if (!['response_time', 'success_rate', 'cache_hit_rate'].includes(metric)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid metric. Must be one of: response_time, success_rate, cache_hit_rate'
      });
    }

    const trends = await dbPerformanceMonitor.getPerformanceTrends(metric, period, limit);
    
    res.json({
      success: true,
      data: {
        metric,
        period,
        trends,
        count: trends.length
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error(`Error getting ${req.params.metric} trends:`, error);
    res.status(500).json({
      success: false,
      error: `Failed to retrieve ${req.params.metric} trends`
    });
  }
});

/**
 * Get detailed query performance metrics from database
 */
router.get('/query-details', async (req: Request, res: Response) => {
  try {
    const queryType = req.query.type as string;
    const limit = parseInt(req.query.limit as string) || 100;
    const offset = parseInt(req.query.offset as string) || 0;

    const whereClause = queryType ? { queryType } : {};

    const queryMetrics = await prisma.queryPerformanceMetric.findMany({
      where: whereClause,
      orderBy: { timestamp: 'desc' },
      take: limit,
      skip: offset,
      select: {
        queryType: true,
        duration: true,
        success: true,
        dataSize: true,
        cacheHit: true,
        retryCount: true,
        errorType: true,
        timestamp: true
      }
    });

    const totalCount = await prisma.queryPerformanceMetric.count({
      where: whereClause
    });

    res.json({
      success: true,
      data: {
        metrics: queryMetrics,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting detailed query performance:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve detailed query performance metrics'
    });
  }
});

/**
 * Get performance alerts from database
 */
router.get('/alerts', async (req: Request, res: Response) => {
  try {
    const resolved = req.query.resolved === 'true';
    const type = req.query.type as string;
    const limit = parseInt(req.query.limit as string) || 50;

    const whereClause: any = { resolved };
    if (type && ['warning', 'error'].includes(type)) {
      whereClause.type = type;
    }

    const alerts = await prisma.performanceAlert.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      take: limit
    });

    const summary = await prisma.performanceAlert.groupBy({
      by: ['type'],
      where: { resolved: false },
      _count: { type: true }
    });

    res.json({
      success: true,
      data: {
        alerts,
        summary: {
          total: alerts.length,
          byType: summary.reduce((acc, item) => {
            acc[item.type] = item._count.type;
            return acc;
          }, {} as Record<string, number>)
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting performance alerts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve performance alerts'
    });
  }
});

/**
 * Create a new performance alert
 */
router.post('/alerts', async (req: Request, res: Response) => {
  try {
    const { type, message, metric, value, threshold } = req.body;

    if (!type || !message || !metric || value === undefined || threshold === undefined) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: type, message, metric, value, threshold'
      });
    }

    if (!['warning', 'error'].includes(type)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid alert type. Must be warning or error'
      });
    }

    await dbPerformanceMonitor.createPerformanceAlert(type, message, metric, value, threshold);

    res.json({
      success: true,
      message: 'Performance alert created successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error creating performance alert:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create performance alert'
    });
  }
});

/**
 * Resolve performance alerts
 */
router.patch('/alerts/:id/resolve', async (req: Request, res: Response) => {
  try {
    const alertId = parseInt(req.params.id);

    const alert = await prisma.performanceAlert.update({
      where: { id: alertId },
      data: {
        resolved: true,
        resolvedAt: new Date()
      }
    });

    res.json({
      success: true,
      data: alert,
      message: 'Alert resolved successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error resolving alert:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to resolve alert'
    });
  }
});

/**
 * Get cache statistics (combined with database metrics)
 */
router.get('/cache', async (req: Request, res: Response) => {
  try {
    // Get current cache stats
    const cacheStats = dataCache.getStats();
    const frequentPatterns = dataCache.getFrequentPatterns();

    // Get historical cache statistics from database
    const historicalStats = await prisma.cacheStatistic.findMany({
      orderBy: { timestamp: 'desc' },
      take: 24 // Last 24 records
    });

    res.json({
      success: true,
      data: {
        current: {
          stats: cacheStats,
          frequentPatterns,
          recommendations: generateCacheRecommendations(cacheStats, frequentPatterns)
        },
        historical: historicalStats,
        trends: calculateCacheTrends(historicalStats)
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting cache statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve cache statistics'
    });
  }
});

/**
 * Save current cache statistics to database
 */
router.post('/cache/snapshot', async (req: Request, res: Response) => {
  try {
    const cacheStats = dataCache.getStats();

    await prisma.cacheStatistic.create({
      data: {
        totalEntries: cacheStats.totalEntries,
        totalSize: cacheStats.totalSize,
        hitRate: cacheStats.hitRate,
        missRate: cacheStats.missRate,
        evictionCount: cacheStats.evictionCount,
        averageAccessTime: cacheStats.averageAccessTime
      }
    });

    res.json({
      success: true,
      message: 'Cache statistics snapshot saved',
      data: cacheStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error saving cache snapshot:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to save cache statistics snapshot'
    });
  }
});

/**
 * Export performance metrics from database
 */
router.get('/export', async (req: Request, res: Response) => {
  try {
    const format = req.query.format as 'json' | 'csv' || 'json';
    const startDate = req.query.startDate as string;
    const endDate = req.query.endDate as string;
    const metricTypes = req.query.types as string;

    if (!['json', 'csv'].includes(format)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid format. Must be json or csv'
      });
    }

    let timeRange: { start: Date; end: Date } | undefined;
    if (startDate && endDate) {
      timeRange = {
        start: new Date(startDate),
        end: new Date(endDate)
      };
    }

    const types = metricTypes ? metricTypes.split(',') : undefined;
    const exportData = await dbPerformanceMonitor.exportMetrics(format, timeRange, types);

    const filename = `performance-metrics-${Date.now()}.${format}`;
    
    if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    } else {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    }

    res.send(exportData);
  } catch (error) {
    console.error('Error exporting performance metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to export performance metrics'
    });
  }
});

/**
 * Clean up old performance metrics
 */
router.post('/cleanup', async (req: Request, res: Response) => {
  try {
    const result = await dbPerformanceMonitor.cleanupOldMetrics();

    res.json({
      success: true,
      data: result,
      message: `Cleaned up ${result.deleted} old performance metrics`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error cleaning up old metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clean up old metrics'
    });
  }
});

/**
 * Get database performance summary
 */
router.get('/summary', async (req: Request, res: Response) => {
  try {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Get counts for different metric types
    const [
      totalQueries,
      totalResponses,
      totalIntents,
      totalEnrichments,
      recentQueries,
      recentResponses,
      activeAlerts
    ] = await Promise.all([
      prisma.queryPerformanceMetric.count(),
      prisma.responseTimeMetric.count(),
      prisma.intentRecognitionMetric.count(),
      prisma.enrichmentPerformanceMetric.count(),
      prisma.queryPerformanceMetric.count({
        where: { timestamp: { gte: last24Hours } }
      }),
      prisma.responseTimeMetric.count({
        where: { timestamp: { gte: last24Hours } }
      }),
      prisma.performanceAlert.count({
        where: { resolved: false }
      })
    ]);

    // Get average response times
    const avgResponseTime = await prisma.responseTimeMetric.aggregate({
      where: { timestamp: { gte: last24Hours } },
      _avg: { totalTime: true }
    });

    // Get success rates
    const successfulResponses = await prisma.responseTimeMetric.count({
      where: {
        timestamp: { gte: last24Hours },
        success: true
      }
    });

    const successRate = recentResponses > 0 ? successfulResponses / recentResponses : 0;

    const summary = {
      totals: {
        queries: totalQueries,
        responses: totalResponses,
        intents: totalIntents,
        enrichments: totalEnrichments
      },
      last24Hours: {
        queries: recentQueries,
        responses: recentResponses,
        averageResponseTime: avgResponseTime._avg.totalTime || 0,
        successRate: successRate
      },
      alerts: {
        active: activeAlerts
      },
      cache: dataCache.getStats()
    };

    res.json({
      success: true,
      data: summary,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting performance summary:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve performance summary'
    });
  }
});

// Helper functions

function generateCacheRecommendations(stats: any, patterns: any[]): string[] {
  const recommendations: string[] = [];
  
  if (stats.hitRate < 0.3) {
    recommendations.push('Cache hit rate is low. Consider increasing cache TTL or reviewing query patterns.');
  }
  
  if (stats.totalSize > 80 * 1024 * 1024) { // 80MB
    recommendations.push('Cache size is approaching limits. Consider implementing more aggressive eviction policies.');
  }
  
  if (patterns.length > 0) {
    const lowEfficiencyPatterns = patterns.filter(p => p.cacheEffectiveness < 0.5);
    if (lowEfficiencyPatterns.length > 0) {
      recommendations.push(`${lowEfficiencyPatterns.length} query patterns have low cache effectiveness.`);
    }
  }
  
  if (stats.evictionCount > stats.totalEntries * 0.5) {
    recommendations.push('High eviction rate detected. Consider increasing cache size or adjusting TTL values.');
  }
  
  return recommendations;
}

function calculateCacheTrends(historicalStats: any[]): any {
  if (historicalStats.length < 2) {
    return { hitRate: 'stable', size: 'stable', performance: 'stable' };
  }

  const recent = historicalStats.slice(0, Math.floor(historicalStats.length / 2));
  const older = historicalStats.slice(Math.floor(historicalStats.length / 2));

  const recentAvgHitRate = recent.reduce((sum, s) => sum + s.hitRate, 0) / recent.length;
  const olderAvgHitRate = older.reduce((sum, s) => sum + s.hitRate, 0) / older.length;

  const hitRateTrend = recentAvgHitRate > olderAvgHitRate * 1.1 ? 'improving' :
                      recentAvgHitRate < olderAvgHitRate * 0.9 ? 'degrading' : 'stable';

  return {
    hitRate: hitRateTrend,
    size: 'stable', // Simplified
    performance: hitRateTrend
  };
}

// Cleanup on process exit
process.on('beforeExit', async () => {
  dbPerformanceMonitor.destroy();
  await prisma.$disconnect();
});

export default router;