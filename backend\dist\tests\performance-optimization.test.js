"use strict";
/**
 * Performance Optimization Tests
 *
 * Tests to validate the effectiveness of performance monitoring and optimization features.
 * Covers caching, metrics collection, and response time improvements.
 *
 * Requirements: 5.5
 */
Object.defineProperty(exports, "__esModule", { value: true });
const data_cache_service_1 = require("../services/data-cache.service");
const performance_monitoring_service_1 = require("../services/performance-monitoring.service");
const data_enrichment_service_1 = require("../services/data-enrichment.service");
// Mock Prisma
jest.mock('@prisma/client');
describe('Performance Optimization Tests', () => {
    let cacheService;
    let performanceMonitor;
    let dataEnrichmentService;
    beforeEach(() => {
        jest.clearAllMocks();
        // Create fresh instances for each test
        cacheService = new data_cache_service_1.DataCacheService({
            maxSize: 10 * 1024 * 1024, // 10MB for testing
            maxEntries: 100,
            defaultTTL: 60 * 1000, // 1 minute for testing
            cleanupInterval: 5 * 1000, // 5 seconds for testing
            enableMetrics: true
        });
        performanceMonitor = new performance_monitoring_service_1.PerformanceMonitoringService();
        // Mock Prisma client
        const mockPrisma = {};
        dataEnrichmentService = new data_enrichment_service_1.DataEnrichmentService(mockPrisma, {
            cacheEnabled: true,
            maxQueryTime: 2000
        });
    });
    afterEach(() => {
        cacheService.destroy();
        performanceMonitor.destroy();
    });
    describe('Cache Performance', () => {
        it('should improve response times with caching', async () => {
            const testIntents = [{
                    type: 'stoerungen',
                    keywords: ['störungen', 'status'],
                    confidence: 0.8
                }];
            const mockResults = [{
                    dataType: 'stoerungen',
                    data: { statistics: { total: 5, active: 2 } },
                    summary: 'Test data',
                    timestamp: new Date(),
                    success: true,
                    queryTime: 500,
                    intent: 'stoerungen'
                }];
            // First request - cache miss
            const startTime1 = Date.now();
            const cachedResults1 = await cacheService.getCachedQueryResults(testIntents);
            const missTime = Date.now() - startTime1;
            expect(cachedResults1).toBeNull();
            // Cache the results
            await cacheService.cacheQueryResults(testIntents, mockResults);
            // Second request - cache hit
            const startTime2 = Date.now();
            const cachedResults2 = await cacheService.getCachedQueryResults(testIntents);
            const hitTime = Date.now() - startTime2;
            expect(cachedResults2).toEqual(mockResults);
            expect(hitTime).toBeLessThan(missTime); // Cache hit should be faster
            expect(hitTime).toBeLessThan(10); // Should be very fast (< 10ms)
        });
        it('should handle cache eviction efficiently', async () => {
            const testData = Array.from({ length: 150 }, (_, i) => ({
                intents: [{
                        type: 'stoerungen',
                        keywords: [`test${i}`],
                        confidence: 0.8
                    }],
                results: [{
                        dataType: 'stoerungen',
                        data: { test: `data${i}` },
                        summary: `Summary ${i}`,
                        timestamp: new Date(),
                        success: true,
                        queryTime: 100,
                        intent: 'stoerungen'
                    }]
            }));
            // Fill cache beyond capacity
            for (const { intents, results } of testData) {
                await cacheService.cacheQueryResults(intents, results);
            }
            const stats = cacheService.getStats();
            // Should have evicted entries to stay within limits
            expect(stats.totalEntries).toBeLessThanOrEqual(100);
            expect(stats.evictionCount).toBeGreaterThan(0);
        });
        it('should optimize cache based on usage patterns', async () => {
            // Create entries with different access patterns
            const frequentIntents = [{
                    type: 'stoerungen',
                    keywords: ['frequent'],
                    confidence: 0.9
                }];
            const rareIntents = [{
                    type: 'dispatch',
                    keywords: ['rare'],
                    confidence: 0.7
                }];
            const mockResults = [{
                    dataType: 'stoerungen',
                    data: { test: 'data' },
                    summary: 'Test',
                    timestamp: new Date(),
                    success: true,
                    queryTime: 100,
                    intent: 'stoerungen'
                }];
            // Cache both entries
            await cacheService.cacheQueryResults(frequentIntents, mockResults);
            await cacheService.cacheQueryResults(rareIntents, mockResults);
            // Access frequent entry multiple times
            for (let i = 0; i < 15; i++) {
                await cacheService.getCachedQueryResults(frequentIntents);
            }
            // Access rare entry only once
            await cacheService.getCachedQueryResults(rareIntents);
            // Wait for entries to age
            await new Promise(resolve => setTimeout(resolve, 100));
            // Optimize cache
            const optimization = cacheService.optimizeCache();
            expect(optimization.optimizations.length).toBeGreaterThan(0);
        });
        it('should provide accurate cache statistics', async () => {
            const testIntents = [{
                    type: 'cutting',
                    keywords: ['efficiency'],
                    confidence: 0.8
                }];
            const mockResults = [{
                    dataType: 'cutting',
                    data: { efficiency: 95 },
                    summary: 'High efficiency',
                    timestamp: new Date(),
                    success: true,
                    queryTime: 200,
                    intent: 'cutting'
                }];
            // Initial stats
            const initialStats = cacheService.getStats();
            expect(initialStats.totalEntries).toBe(0);
            expect(initialStats.hitRate).toBe(0);
            // Cache miss
            await cacheService.getCachedQueryResults(testIntents);
            // Cache entry
            await cacheService.cacheQueryResults(testIntents, mockResults);
            // Cache hit
            await cacheService.getCachedQueryResults(testIntents);
            const finalStats = cacheService.getStats();
            expect(finalStats.totalEntries).toBe(1);
            expect(finalStats.hitRate).toBeGreaterThan(0);
            expect(finalStats.missRate).toBeGreaterThan(0);
        });
    });
    describe('Performance Monitoring', () => {
        it('should record query performance metrics', async () => {
            const queryType = 'stoerungen';
            const duration = 150;
            const success = true;
            performanceMonitor.recordQueryPerformance(queryType, duration, success, {
                dataSize: 1024,
                cacheHit: false,
                retryCount: 0
            });
            const stats = performanceMonitor.getPerformanceStats();
            expect(stats.queryPerformance.stoerungen.count).toBe(1);
            expect(stats.queryPerformance.stoerungen.avg).toBe(duration);
            expect(stats.queryPerformance.stoerungen.successRate).toBe(1);
        });
        it('should record intent recognition accuracy', async () => {
            const message = 'Wie viele Störungen haben wir?';
            const detectedIntents = ['stoerungen'];
            const confidence = 0.85;
            const duration = 50;
            const accuracy = 0.9;
            performanceMonitor.recordIntentRecognition(message, detectedIntents, confidence, duration, ['störungen'], accuracy);
            const accuracyMetrics = performanceMonitor.getIntentAccuracyMetrics();
            expect(accuracyMetrics.overallAccuracy).toBe(accuracy);
            expect(accuracyMetrics.totalSamples).toBe(1);
            expect(accuracyMetrics.accuracyByIntent.stoerungen).toBe(accuracy);
        });
        it('should track response time performance', async () => {
            const endpoint = '/api/chat/enhanced';
            const enriched = true;
            const totalTime = 1500;
            const enrichmentTime = 800;
            const llmTime = 700;
            const dataSize = 2048;
            performanceMonitor.recordResponseTime(endpoint, enriched, totalTime, enrichmentTime, llmTime, dataSize);
            const stats = performanceMonitor.getPerformanceStats();
            expect(stats.totalRequests).toBe(1);
            expect(stats.averageResponseTime).toBe(totalTime);
            expect(stats.successRate).toBe(1); // Should be successful (< 3000ms for enriched)
        });
        it('should generate performance alerts', async () => {
            // Record some slow responses to trigger alerts
            for (let i = 0; i < 5; i++) {
                performanceMonitor.recordResponseTime('/api/chat/enhanced', true, 3500, // Exceeds 3 second threshold
                2000, 1500, 1024);
            }
            const alerts = performanceMonitor.getPerformanceAlerts();
            expect(alerts.length).toBeGreaterThan(0);
            expect(alerts.some(alert => alert.type === 'error')).toBe(true);
            expect(alerts.some(alert => alert.metric === 'averageResponseTime')).toBe(true);
        });
        it('should track performance trends', async () => {
            // Record improving performance over time
            const baseTimes = [2000, 1800, 1600, 1400, 1200];
            for (const time of baseTimes) {
                performanceMonitor.recordQueryPerformance('dispatch', time, true, {
                    cacheHit: false
                });
            }
            const queryPerf = performanceMonitor.getQueryPerformanceByType('dispatch');
            expect(queryPerf.recentTrend).toBe('improving');
            expect(queryPerf.totalQueries).toBe(5);
            expect(queryPerf.successRate).toBe(1);
        });
    });
    describe('Integration Performance', () => {
        it('should demonstrate cache effectiveness in real scenarios', async () => {
            // Simulate common query patterns
            const commonQueries = [
                { type: 'stoerungen', keywords: ['störungen', 'status'] },
                { type: 'dispatch', keywords: ['versand', 'performance'] },
                { type: 'cutting', keywords: ['ablängerei', 'efficiency'] }
            ];
            const mockResults = commonQueries.map((intent, index) => ({
                dataType: intent.type,
                data: { mockData: `data${index}` },
                summary: `Summary for ${intent.type}`,
                timestamp: new Date(),
                success: true,
                queryTime: 300 + index * 100,
                intent: intent.type
            }));
            // First round - all cache misses
            const firstRoundTimes = [];
            for (let i = 0; i < commonQueries.length; i++) {
                const startTime = Date.now();
                // Simulate cache miss and subsequent caching
                const cached = await cacheService.getCachedQueryResults([commonQueries[i]]);
                expect(cached).toBeNull();
                await cacheService.cacheQueryResults([commonQueries[i]], [mockResults[i]]);
                firstRoundTimes.push(Date.now() - startTime);
            }
            // Second round - all cache hits
            const secondRoundTimes = [];
            for (let i = 0; i < commonQueries.length; i++) {
                const startTime = Date.now();
                const cached = await cacheService.getCachedQueryResults([commonQueries[i]]);
                expect(cached).not.toBeNull();
                secondRoundTimes.push(Date.now() - startTime);
            }
            // Cache hits should be significantly faster
            const avgFirstRound = firstRoundTimes.reduce((a, b) => a + b, 0) / firstRoundTimes.length;
            const avgSecondRound = secondRoundTimes.reduce((a, b) => a + b, 0) / secondRoundTimes.length;
            expect(avgSecondRound).toBeLessThan(avgFirstRound);
            expect(avgSecondRound).toBeLessThan(20); // Cache hits should be very fast
            // Verify cache statistics
            const stats = cacheService.getStats();
            expect(stats.hitRate).toBeGreaterThan(0);
            expect(stats.totalEntries).toBe(3);
        });
        it('should handle concurrent requests efficiently with caching', async () => {
            const testIntent = [{
                    type: 'stoerungen',
                    keywords: ['concurrent', 'test'],
                    confidence: 0.8
                }];
            const mockResult = [{
                    dataType: 'stoerungen',
                    data: { concurrent: true },
                    summary: 'Concurrent test data',
                    timestamp: new Date(),
                    success: true,
                    queryTime: 400,
                    intent: 'stoerungen'
                }];
            // Cache the data first
            await cacheService.cacheQueryResults(testIntent, mockResult);
            // Make concurrent requests
            const concurrentRequests = 10;
            const startTime = Date.now();
            const promises = Array.from({ length: concurrentRequests }, () => cacheService.getCachedQueryResults(testIntent));
            const results = await Promise.all(promises);
            const totalTime = Date.now() - startTime;
            // All requests should succeed
            results.forEach(result => {
                expect(result).toEqual(mockResult);
            });
            // Should handle concurrent requests efficiently
            expect(totalTime).toBeLessThan(100); // Should be very fast with caching
            const stats = cacheService.getStats();
            expect(stats.hitRate).toBe(1); // All should be cache hits
        });
        it('should optimize performance under load', async () => {
            const loadTestSize = 50;
            const queryTypes = ['stoerungen', 'dispatch', 'cutting'];
            // Generate load test data
            const loadTestData = Array.from({ length: loadTestSize }, (_, i) => ({
                intents: [{
                        type: queryTypes[i % 3],
                        keywords: [`load${i}`],
                        confidence: 0.7 + (i % 3) * 0.1
                    }],
                results: [{
                        dataType: queryTypes[i % 3],
                        data: { loadTest: i },
                        summary: `Load test ${i}`,
                        timestamp: new Date(),
                        success: true,
                        queryTime: 200 + (i % 5) * 50,
                        intent: queryTypes[i % 3]
                    }]
            }));
            // Execute load test
            const startTime = Date.now();
            for (const { intents, results } of loadTestData) {
                // Record performance metrics
                performanceMonitor.recordQueryPerformance(intents[0].type, results[0].queryTime, results[0].success, { cacheHit: false });
                // Cache results
                await cacheService.cacheQueryResults(intents, results);
            }
            const loadTestTime = Date.now() - startTime;
            // Verify performance under load
            expect(loadTestTime).toBeLessThan(5000); // Should complete within 5 seconds
            const stats = performanceMonitor.getPerformanceStats();
            expect(stats.totalRequests).toBe(loadTestSize);
            expect(stats.successRate).toBe(1);
            const cacheStats = cacheService.getStats();
            expect(cacheStats.totalEntries).toBeGreaterThan(0);
            // Test cache optimization under load
            const optimization = cacheService.optimizeCache();
            expect(optimization.optimizations.length).toBeGreaterThanOrEqual(0);
        });
    });
    describe('Memory and Resource Optimization', () => {
        it('should manage memory efficiently', async () => {
            const initialMemory = process.memoryUsage();
            // Create many cache entries
            for (let i = 0; i < 200; i++) {
                const intents = [{
                        type: 'stoerungen',
                        keywords: [`memory${i}`],
                        confidence: 0.8
                    }];
                const results = [{
                        dataType: 'stoerungen',
                        data: { memoryTest: i, largeData: 'x'.repeat(1000) },
                        summary: `Memory test ${i}`,
                        timestamp: new Date(),
                        success: true,
                        queryTime: 100,
                        intent: 'stoerungen'
                    }];
                await cacheService.cacheQueryResults(intents, results);
            }
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }
            const finalMemory = process.memoryUsage();
            const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
            // Memory increase should be reasonable (less than 50MB)
            expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
            // Cache should have managed size through eviction
            const stats = cacheService.getStats();
            expect(stats.totalEntries).toBeLessThanOrEqual(100); // Should respect max entries
        });
        it('should clean up expired entries automatically', async () => {
            // Create cache service with very short TTL for testing
            const shortTTLCache = new data_cache_service_1.DataCacheService({
                defaultTTL: 100, // 100ms
                cleanupInterval: 50 // 50ms cleanup
            });
            const testIntents = [{
                    type: 'dispatch',
                    keywords: ['cleanup', 'test'],
                    confidence: 0.8
                }];
            const mockResults = [{
                    dataType: 'dispatch',
                    data: { cleanup: true },
                    summary: 'Cleanup test',
                    timestamp: new Date(),
                    success: true,
                    queryTime: 150,
                    intent: 'dispatch'
                }];
            // Cache the data
            await shortTTLCache.cacheQueryResults(testIntents, mockResults);
            let stats = shortTTLCache.getStats();
            expect(stats.totalEntries).toBe(1);
            // Wait for expiration and cleanup
            await new Promise(resolve => setTimeout(resolve, 200));
            // Entry should be cleaned up
            const cachedData = await shortTTLCache.getCachedQueryResults(testIntents);
            expect(cachedData).toBeNull();
            stats = shortTTLCache.getStats();
            expect(stats.totalEntries).toBe(0);
            shortTTLCache.destroy();
        });
    });
});
