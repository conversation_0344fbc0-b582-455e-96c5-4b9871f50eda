/**
 * AI Security Service
 * 
 * Handles authentication, authorization, input validation, and security auditing
 * for all AI module operations. Integrates with existing RBAC system.
 */

import {
  AISecurityContext,
  AIFeatureAccess,
  AIInputValidationResult,
  AISecurityAuditLog,
  SecureAIRequest,
  AI_PERMISSIONS,
  INPUT_VALIDATION_RULES,
  SecurityRiskLevel
} from '@/modules/ai/types/security';
import { User } from '@/hooks/useAuth';
import { IService } from '@/services/base.service';

export class AISecurityService implements IService {
  readonly serviceName = 'AISecurityService';
  private auditLogs: AISecurityAuditLog[] = [];
  private readonly maxAuditLogs = 10000;

  /**
   * Creates security context for a user with AI permissions
   */
  createSecurityContext(user: User): AISecurityContext {
    const permissions = this.getUserPermissions(user.roles);
    const featureAccess = this.evaluateFeatureAccess(user.roles);

    return {
      userId: user.id,
      username: user.username,
      roles: user.roles,
      permissions,
      featureAccess
    };
  }

  /**
   * Checks if user has access to specific AI feature
   */
  hasFeatureAccess(securityContext: AISecurityContext, featureId: string): boolean {
    const access = securityContext.featureAccess[featureId];
    return access?.hasAccess ?? false;
  }

  /**
   * Validates and sanitizes AI input for security
   */
  validateInput(input: string, feature: string): AIInputValidationResult {
    const violations: string[] = [];
    let riskLevel: SecurityRiskLevel = SecurityRiskLevel.LOW;
    let sanitizedInput = input.trim();

    // Apply validation rules
    for (const rule of INPUT_VALIDATION_RULES) {
      if (rule.pattern.test(input)) {
        violations.push(rule.message);
        if (rule.riskLevel === SecurityRiskLevel.HIGH) {
          riskLevel = SecurityRiskLevel.HIGH;
        } else if (rule.riskLevel === SecurityRiskLevel.MEDIUM && riskLevel === SecurityRiskLevel.LOW) {
          riskLevel = SecurityRiskLevel.MEDIUM;
        }
      }
    }

    // Basic sanitization
    sanitizedInput = this.sanitizeInput(sanitizedInput);

    return {
      isValid: violations.length === 0 || riskLevel !== SecurityRiskLevel.HIGH,
      sanitizedInput,
      violations,
      riskLevel
    };
  }

  /**
   * Processes secure AI request with full security checks
   */
  async processSecureRequest(
    user: User,
    request: Omit<SecureAIRequest, 'userId' | 'timestamp'>
  ): Promise<{ allowed: boolean; reason?: string; sanitizedInput?: string }> {
    const securityContext = this.createSecurityContext(user);

    // Check feature access
    if (!this.hasFeatureAccess(securityContext, request.feature)) {
      await this.logSecurityEvent({
        userId: user.id,
        username: user.username,
        action: 'access_denied',
        feature: request.feature,
        input: request.input,
        result: 'denied',
        reason: 'Insufficient permissions',
        timestamp: new Date()
      });

      return {
        allowed: false,
        reason: 'Keine Berechtigung für diese KI-Funktion'
      };
    }

    // Validate input
    const validation = this.validateInput(request.input, request.feature);

    if (!validation.isValid) {
      await this.logSecurityEvent({
        userId: user.id,
        username: user.username,
        action: 'input_validation_failed',
        feature: request.feature,
        input: request.input,
        result: 'denied',
        reason: `Input validation failed: ${validation.violations.join(', ')}`,
        timestamp: new Date()
      });

      return {
        allowed: false,
        reason: `Eingabe nicht zulässig: ${validation.violations.join(', ')}`
      };
    }

    // Log successful security check
    await this.logSecurityEvent({
      userId: user.id,
      username: user.username,
      action: 'security_check_passed',
      feature: request.feature,
      input: request.input,
      result: 'success',
      timestamp: new Date()
    });

    return {
      allowed: true,
      sanitizedInput: validation.sanitizedInput
    };
  }

  /**
   * Gets security audit logs for monitoring
   */
  getAuditLogs(limit: number = 100): AISecurityAuditLog[] {
    return this.auditLogs
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Gets security statistics
   */
  getSecurityStats(): {
    totalRequests: number;
    deniedRequests: number;
    highRiskAttempts: number;
    topViolations: { violation: string; count: number }[];
  } {
    const totalRequests = this.auditLogs.length;
    const deniedRequests = this.auditLogs.filter(log => log.result === 'denied').length;
    const highRiskAttempts = this.auditLogs.filter(log =>
      log.reason?.includes('HIGH') || log.reason?.includes('SQL') || log.reason?.includes('Script')
    ).length;

    // Count violation types
    const violationCounts: Record<string, number> = {};
    this.auditLogs.forEach(log => {
      if (log.result === 'denied' && log.reason) {
        violationCounts[log.reason] = (violationCounts[log.reason] || 0) + 1;
      }
    });

    const topViolations = Object.entries(violationCounts)
      .map(([violation, count]) => ({ violation, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalRequests,
      deniedRequests,
      highRiskAttempts,
      topViolations
    };
  }

  /**
   * Private helper methods
   */
  private getUserPermissions(roles: string[]) {
    return Object.values(AI_PERMISSIONS).filter(permission =>
      permission.requiredRoles.some(role => roles.includes(role))
    );
  }

  private evaluateFeatureAccess(roles: string[]): Record<string, AIFeatureAccess> {
    const featureAccess: Record<string, AIFeatureAccess> = {};

    Object.values(AI_PERMISSIONS).forEach(permission => {
      const hasAccess = permission.requiredRoles.some(role => roles.includes(role));
      const requiredRole = permission.requiredRoles.find(role => roles.includes(role));

      featureAccess[permission.id] = {
        featureId: permission.id,
        hasAccess,
        requiredRole,
        reason: hasAccess ? undefined : `Benötigt eine der Rollen: ${permission.requiredRoles.join(', ')}`
      };
    });

    return featureAccess;
  }

  private sanitizeInput(input: string): string {
    // Remove potentially dangerous characters and patterns
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/['";]/g, '') // Remove quotes and semicolons
      .replace(/\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b/gi, '') // Remove SQL keywords
      .trim();
  }

  private async logSecurityEvent(event: Omit<AISecurityAuditLog, 'id'>): Promise<void> {
    // Add unique ID
    const logEntry: AISecurityAuditLog = {
      ...event,
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };

    this.auditLogs.push(logEntry);

    // Maintain log size limit
    if (this.auditLogs.length > this.maxAuditLogs) {
      this.auditLogs = this.auditLogs.slice(-this.maxAuditLogs);
    }

    // In production, this would also log to a persistent store
    console.log('[AI Security]', logEntry);
  }
}

export const aiSecurityService = new AISecurityService();