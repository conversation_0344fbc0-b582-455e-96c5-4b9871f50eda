const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const { PrismaClient } = require('@prisma-sfm-dashboard/client');

// Initialize Prisma client
const prisma = new PrismaClient();

async function testDatabaseUpload() {
  console.log('🧪 Testing Database Upload Functionality...');
  
  try {
    // 1. Check if server is running
    console.log('\n1. Checking server health...');
    const healthResponse = await axios.get('http://localhost:3001/api/health');
    console.log('✅ Server is running:', healthResponse.data);
    
    // 2. Create a test störung first
    console.log('\n2. Creating test störung...');
    const testStoerung = {
      title: 'Test Störung für Upload',
      description: 'Test Störung um Upload-Funktionalität zu testen',
      severity: 'MEDIUM',
      status: 'NEW',
      category: 'System',
      affected_system: 'Test System',
      location: 'Test Location',
      reported_by: 'Test User'
    };
    
    const createResponse = await axios.post('http://localhost:3001/api/stoerungen', testStoerung);
    const stoerungId = createResponse.data.data.id;
    console.log('✅ Test störung created with ID:', stoerungId);
    
    // 3. Create a test image file
    console.log('\n3. Creating test image file...');
    const testImagePath = path.join(__dirname, 'test-upload-image.jpg');
    const testImageContent = Buffer.from('fake-image-content-for-testing', 'utf8');
    fs.writeFileSync(testImagePath, testImageContent);
    console.log('✅ Test image file created:', testImagePath);
    
    // 4. Test file upload
    console.log('\n4. Testing file upload...');
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));
    formData.append('uploaded_by', 'test-user');
    
    const uploadResponse = await axios.post(
      `http://localhost:3001/api/stoerungen/${stoerungId}/upload-image`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
        },
      }
    );
    
    console.log('✅ Upload response:', uploadResponse.data);
    const attachmentId = uploadResponse.data.data.id;
    
    // 5. Verify database entry
    console.log('\n5. Verifying database entry...');
    const attachment = await prisma.stoerungsAttachment.findUnique({
      where: { id: attachmentId },
      include: {
        stoerung: {
          select: {
            id: true,
            title: true
          }
        }
      }
    });
    
    if (attachment) {
      console.log('✅ Database entry found:');
      console.log('   - ID:', attachment.id);
      console.log('   - Filename:', attachment.filename);
      console.log('   - Stored name:', attachment.stored_name);
      console.log('   - File path:', attachment.file_path);
      console.log('   - File size:', attachment.file_size, 'bytes');
      console.log('   - MIME type:', attachment.mime_type);
      console.log('   - File type:', attachment.file_type);
      console.log('   - Uploaded by:', attachment.uploaded_by);
      console.log('   - Created at:', attachment.created_at);
      console.log('   - Linked to störung:', attachment.stoerung.title);
    } else {
      console.log('❌ No database entry found for attachment ID:', attachmentId);
    }
    
    // 6. Test file accessibility via API
    console.log('\n6. Testing file accessibility via API...');
    const fileUrl = `http://localhost:3001/api/uploads/stoerungen/${attachment.stored_name}`;
    try {
      const fileResponse = await axios.get(fileUrl);
      console.log('✅ File accessible via API:', fileUrl);
      console.log('   - Status:', fileResponse.status);
      console.log('   - Content-Type:', fileResponse.headers['content-type']);
    } catch (fileError) {
      console.log('❌ File not accessible via API:', fileUrl);
      console.log('   - Error:', fileError.message);
    }
    
    // 7. Get all attachments for the störung
    console.log('\n7. Getting all attachments for störung...');
    const allAttachments = await prisma.stoerungsAttachment.findMany({
      where: { stoerung_id: stoerungId },
      orderBy: { created_at: 'desc' }
    });
    
    console.log('✅ Found', allAttachments.length, 'attachment(s) for störung ID:', stoerungId);
    allAttachments.forEach((att, index) => {
      console.log(`   ${index + 1}. ${att.filename} (${att.file_size} bytes, ${att.mime_type})`);
    });
    
    // 8. Cleanup
    console.log('\n8. Cleaning up...');
    
    // Delete attachment from database
    await prisma.stoerungsAttachment.delete({
      where: { id: attachmentId }
    });
    console.log('✅ Attachment deleted from database');
    
    // Delete test störung
    await prisma.stoerungen.delete({
      where: { id: stoerungId }
    });
    console.log('✅ Test störung deleted');
    
    // Delete test files
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
      console.log('✅ Test image file deleted');
    }
    
    // Delete uploaded file if it exists
    if (attachment && fs.existsSync(attachment.file_path)) {
      fs.unlinkSync(attachment.file_path);
      console.log('✅ Uploaded file deleted from disk');
    }
    
    console.log('\n🎉 All tests passed! Database upload functionality is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   - Status:', error.response.status);
      console.error('   - Data:', error.response.data);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDatabaseUpload();