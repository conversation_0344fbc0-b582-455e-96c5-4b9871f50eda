import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Plus,
  Trash2,
  Calendar as CalendarIcon,
  Package,
  Hash,
  Layers,
  Database
} from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { ManualDrumSelection } from '../../types/cutting';

interface ManualDrumSelectionFormProps {
  selections: ManualDrumSelection[];
  selectionDate: string;
  availableMaterials: string[];
  availableDrumTypes: string[];
  onSelectionsChange: (selections: ManualDrumSelection[]) => void;
  onDateChange: (date: string) => void;
  onAddSelection: () => void;
  onUpdateSelection: (index: number, selection: ManualDrumSelection) => void;
  onRemoveSelection: (index: number) => void;
  className?: string;
}

/**
 * Formular-Komponente für die manuelle Trommelauswahl
 * Ermöglicht das Hinzufügen, Bearbeiten und Entfernen von manuell ausgewählten Trommeln
 */
export const ManualDrumSelectionForm: React.FC<ManualDrumSelectionFormProps> = ({
  selections,
  selectionDate,
  availableMaterials,
  availableDrumTypes,
  onSelectionsChange,
  onDateChange,
  onAddSelection,
  onUpdateSelection,
  onRemoveSelection,
  className = ''
}) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    selectionDate ? new Date(selectionDate) : new Date()
  );
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  // Aktualisiere das Datum wenn sich selectionDate ändert
  useEffect(() => {
    if (selectionDate) {
      setSelectedDate(new Date(selectionDate));
    }
  }, [selectionDate]);

  // Handle Datumsänderung
  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      onDateChange(format(date, 'yyyy-MM-dd'));
      setIsCalendarOpen(false);
    }
  };

  // Handle Eingabefeld-Änderungen für eine Auswahl
  const handleInputChange = (
    index: number,
    field: keyof ManualDrumSelection,
    value: string | number
  ) => {
    const updatedSelection = { ...selections[index] };
    (updatedSelection as any)[field] = value;
    onUpdateSelection(index, updatedSelection);
  };

  // Validierung einer Auswahl
  const isSelectionValid = (selection: ManualDrumSelection): boolean => {
    return !!(
      selection.chargeNumber &&
      selection.material &&
      selection.gesamtbestand > 0 &&
      selection.lagereinheitentyp
    );
  };

  // Zähle gültige Auswahlen
  const validSelectionsCount = selections.filter(isSelectionValid).length;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Manuelle Trommelauswahl
          {validSelectionsCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {validSelectionsCount} gültige Auswahl{validSelectionsCount !== 1 ? 'en' : ''}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Datum-Auswahl */}
        <div className="space-y-2">
          <Label htmlFor="selection-date">Auswahldatum</Label>
          <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {selectedDate ? (
                  format(selectedDate, 'PPP', { locale: de })
                ) : (
                  <span>Datum auswählen</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                initialFocus
                locale={de}
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Trommel-Auswahlen */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-base font-medium">Trommelauswahlen</Label>
            <Button
              onClick={onAddSelection}
              size="sm"
              className="flex items-center gap-1"
            >
              <Plus className="h-4 w-4" />
              Trommel hinzufügen
            </Button>
          </div>

          {selections.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>Noch keine Trommeln ausgewählt</p>
              <p className="text-sm">Klicken Sie auf "Trommel hinzufügen" um zu beginnen</p>
            </div>
          ) : (
            <div className="space-y-4">
              {selections.map((selection, index) => (
                <Card key={index} className="border-2 border-dashed">
                  <CardContent className="pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {/* Chargennummer */}
                      <div className="space-y-2">
                        <Label className="flex items-center gap-1">
                          <Hash className="h-3 w-3" />
                          Chargennummer
                        </Label>
                        <Input
                          value={selection.chargeNumber}
                          onChange={(e) => handleInputChange(index, 'chargeNumber', e.target.value)}
                          placeholder="z.B. CH-2024-001"
                          className={!selection.chargeNumber ? 'border-red-300' : ''}
                        />
                      </div>

                      {/* Material */}
                      <div className="space-y-2">
                        <Label className="flex items-center gap-1">
                          <Layers className="h-3 w-3" />
                          Material
                        </Label>
                        <Select
                          value={selection.material}
                          onValueChange={(value) => handleInputChange(index, 'material', value)}
                        >
                          <SelectTrigger className={!selection.material ? 'border-red-300' : ''}>
                            <SelectValue placeholder="Material auswählen" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableMaterials.map((material) => (
                              <SelectItem key={material} value={material}>
                                {material}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Gesamtbestand */}
                      <div className="space-y-2">
                        <Label className="flex items-center gap-1">
                          <Database className="h-3 w-3" />
                          Bestand (m)
                        </Label>
                        <Input
                          type="number"
                          value={selection.gesamtbestand}
                          onChange={(e) => handleInputChange(index, 'gesamtbestand', parseFloat(e.target.value) || 0)}
                          placeholder="0"
                          min="0"
                          step="0.1"
                          className={selection.gesamtbestand <= 0 ? 'border-red-300' : ''}
                        />
                      </div>

                      {/* Lagereinheitentyp */}
                      <div className="space-y-2">
                        <Label className="flex items-center gap-1">
                          <Package className="h-3 w-3" />
                          Trommeltyp
                        </Label>
                        <Select
                          value={selection.lagereinheitentyp}
                          onValueChange={(value) => handleInputChange(index, 'lagereinheitentyp', value)}
                        >
                          <SelectTrigger className={!selection.lagereinheitentyp ? 'border-red-300' : ''}>
                            <SelectValue placeholder="Typ auswählen" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableDrumTypes.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Aufnahmedatum (optional) */}
                    <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Aufnahmedatum (optional)</Label>
                        <Input
                          type="date"
                          value={selection.aufnahmedatum || ''}
                          onChange={(e) => handleInputChange(index, 'aufnahmedatum', e.target.value)}
                        />
                      </div>

                      {/* Aktionen */}
                      <div className="flex items-end justify-end">
                        <Button
                          onClick={() => onRemoveSelection(index)}
                          variant="destructive"
                          size="sm"
                          className="flex items-center gap-1"
                        >
                          <Trash2 className="h-4 w-4" />
                          Entfernen
                        </Button>
                      </div>
                    </div>

                    {/* Validierungsstatus */}
                    <div className="mt-3 pt-3 border-t">
                      {isSelectionValid(selection) ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          ✓ Gültige Auswahl
                        </Badge>
                      ) : (
                        <Badge variant="destructive">
                          ⚠ Unvollständige Angaben
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Zusammenfassung */}
        {selections.length > 0 && (
          <div className="pt-4 border-t">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="text-lg font-bold">{selections.length}</div>
                <div className="text-sm text-gray-600">Gesamt</div>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-bold text-green-600">{validSelectionsCount}</div>
                <div className="text-sm text-gray-600">Gültig</div>
              </div>
              <div className="p-3 bg-red-50 rounded-lg">
                <div className="text-lg font-bold text-red-600">{selections.length - validSelectionsCount}</div>
                <div className="text-sm text-gray-600">Unvollständig</div>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg">
                <div className="text-lg font-bold text-blue-600">
                  {selections.reduce((sum, s) => sum + (s.gesamtbestand || 0), 0).toFixed(1)}m
                </div>
                <div className="text-sm text-gray-600">Gesamtlänge</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ManualDrumSelectionForm;