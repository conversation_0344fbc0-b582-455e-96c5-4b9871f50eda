OPENROUTER_API_KEY=your_openrouter_api_key
OPENROUTER_PRESET_MODEL=your_openrouter_preset_model

# Datenbank-Konfiguration
DATABASE_URL=your_database_url

# Server-Konfiguration
PORT=your_port
NODE_ENV=your_node_env

# CORS-Konfiguration
CORS_ORIGIN="http://localhost:your_port,http://localhost:your_port"
CORS_METHODS="GET,POST,PUT,DELETE,OPTIONS"
CORS_ALLOWED_HEADERS="Content-Type,Authorization"

# Logging
LOG_LEVEL="debug"
ENABLE_QUERY_LOGGING="true"

# API Security
API_SECRET_KEY=your_api_secret_key
NODE_ENV=your_node_env
API_PORT=your_api_port

# Microsoft Teams
TEAMS_ERBHOOK=your_teams_webhook_url

# RAG Embeddings
# OPENAI_API_KEY=your_openai_api_key_optional
OLLAMA_BASE_URL=http://localhost:11434
RAG_EMBEDDING_PROVIDER=ollama
RAG_EMBEDDING_MODEL=mxbai-embed-large:latest
