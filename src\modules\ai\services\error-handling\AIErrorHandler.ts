/**
 * AI Error Handler Service
 * Comprehensive error handling, recovery, and logging for AI services
 */

import {
  AIServiceError,
  AIServiceErrorCode,
  AIServiceErrorSeverity,
  ErrorRecoveryStrategy,
  FallbackStrategy,
  ErrorHandlingConfig
} from '../../types/errors';
import { getErrorMessage, formatErrorForUser, formatErrorForTechnical } from '../../utils/errorMessages';
import { AIOperationLogger } from './AIOperationLogger';

export class AIErrorHandler {
  private static instance: AIErrorHandler;
  private recoveryStrategies: Map<AIServiceErrorCode, ErrorRecoveryStrategy> = new Map();
  private fallbackStrategies: Map<string, FallbackStrategy[]> = new Map();
  private logger: AIOperationLogger;
  private config: ErrorHandlingConfig;

  private constructor() {
    this.logger = AIOperationLogger.getInstance();
    this.config = this.getDefaultConfig();
    this.initializeRecoveryStrategies();
  }

  public static getInstance(): AIErrorHandler {
    if (!AIErrorHandler.instance) {
      AIErrorHandler.instance = new AIErrorHandler();
    }
    return AIErrorHandler.instance;
  }

  private getDefaultConfig(): ErrorHandlingConfig {
    return {
      enableLogging: true,
      enableRetries: true,
      maxRetries: 3,
      retryDelay: 1000,
      enableFallbacks: true,
      logLevel: 'error',
      enableUserNotifications: true,
      enableTechnicalLogs: true
    };
  }

  public configure(config: Partial<ErrorHandlingConfig>): void {
    this.config = { ...this.config, ...config };
  }

  public createError(
    code: AIServiceErrorCode,
    service: string,
    operation: string,
    originalError?: Error,
    context?: Record<string, any>
  ): AIServiceError {
    const severity = this.determineSeverity(code);
    const recoverable = this.isRecoverable(code);

    const error: AIServiceError = {
      name: 'AIServiceError',
      message: getErrorMessage(code, 'technical'),
      code,
      severity,
      service,
      operation,
      timestamp: new Date(),
      context,
      originalError,
      recoverable,
      userMessage: getErrorMessage(code, 'user'),
      technicalMessage: getErrorMessage(code, 'technical')
    };

    // Log the error
    if (this.config.enableLogging) {
      this.logger.logError(error);
    }

    return error;
  }

  public async handleError<T>(
    error: AIServiceError,
    originalInput?: any,
    fallbackServiceName?: string
  ): Promise<T | null> {
    try {
      // Log error handling attempt
      if (this.config.enableTechnicalLogs) {
        console.error(`[AI Error Handler] Handling error: ${error.code} in ${error.service}.${error.operation}`, {
          error,
          originalInput,
          fallbackServiceName
        });
      }

      // Try recovery strategies first
      if (this.config.enableRetries && error.recoverable) {
        const recoveryResult = await this.attemptRecovery<T>(error, originalInput);
        if (recoveryResult !== null) {
          return recoveryResult;
        }
      }

      // Try fallback strategies
      if (this.config.enableFallbacks && fallbackServiceName) {
        const fallbackResult = await this.attemptFallback<T>(error, originalInput, fallbackServiceName);
        if (fallbackResult !== null) {
          return fallbackResult;
        }
      }

      // If all recovery attempts fail, log and return null
      this.logger.logFailedRecovery(error, originalInput);
      return null;

    } catch (handlingError) {
      console.error('[AI Error Handler] Error during error handling:', handlingError);
      return null;
    }
  }

  private async attemptRecovery<T>(error: AIServiceError, originalInput?: any): Promise<T | null> {
    const strategy = this.recoveryStrategies.get(error.code);
    if (!strategy || !strategy.canRecover(error)) {
      return null;
    }

    let attempts = 0;
    while (attempts < strategy.maxRetries) {
      try {
        attempts++;

        if (attempts > 1) {
          await this.delay(strategy.retryDelay * attempts);
        }

        const result = await strategy.recover(error, originalInput);

        // Log successful recovery
        this.logger.logSuccessfulRecovery(error, attempts);
        return result as T;

      } catch (recoveryError) {
        if (attempts >= strategy.maxRetries) {
          this.logger.logFailedRecoveryAttempt(error, attempts, recoveryError);
          break;
        }
      }
    }

    return null;
  }

  private async attemptFallback<T>(
    error: AIServiceError,
    originalInput: any,
    serviceName: string
  ): Promise<T | null> {
    const strategies = this.fallbackStrategies.get(serviceName) || [];
    const applicableStrategies = strategies
      .filter(strategy => strategy.canHandle(error))
      .sort((a, b) => b.priority - a.priority);

    for (const strategy of applicableStrategies) {
      try {
        const result = await strategy.execute(originalInput, error);

        // Log successful fallback
        this.logger.logSuccessfulFallback(error, strategy.name);
        return result as T;

      } catch (fallbackError) {
        this.logger.logFailedFallbackAttempt(error, strategy.name, fallbackError);
        continue;
      }
    }

    return null;
  }

  public registerRecoveryStrategy(code: AIServiceErrorCode, strategy: ErrorRecoveryStrategy): void {
    this.recoveryStrategies.set(code, strategy);
  }

  public registerFallbackStrategy(serviceName: string, strategy: FallbackStrategy): void {
    if (!this.fallbackStrategies.has(serviceName)) {
      this.fallbackStrategies.set(serviceName, []);
    }
    this.fallbackStrategies.get(serviceName)!.push(strategy);
  }

  private determineSeverity(code: AIServiceErrorCode): AIServiceErrorSeverity {
    const criticalErrors = [
      AIServiceErrorCode.SERVICE_UNAVAILABLE,
      AIServiceErrorCode.INITIALIZATION_FAILED,
      AIServiceErrorCode.DATABASE_CONNECTION_FAILED
    ];

    const highErrors = [
      AIServiceErrorCode.API_KEY_INVALID,
      AIServiceErrorCode.KNOWLEDGE_BASE_ERROR,
      AIServiceErrorCode.MODEL_TRAINING_FAILED
    ];

    const mediumErrors = [
      AIServiceErrorCode.API_TIMEOUT,
      AIServiceErrorCode.OPTIMIZATION_FAILED,
      AIServiceErrorCode.PREDICTION_FAILED
    ];

    if (criticalErrors.includes(code)) return AIServiceErrorSeverity.CRITICAL;
    if (highErrors.includes(code)) return AIServiceErrorSeverity.HIGH;
    if (mediumErrors.includes(code)) return AIServiceErrorSeverity.MEDIUM;
    return AIServiceErrorSeverity.LOW;
  }

  private isRecoverable(code: AIServiceErrorCode): boolean {
    const nonRecoverableErrors = [
      AIServiceErrorCode.API_KEY_INVALID,
      AIServiceErrorCode.CONFIGURATION_ERROR,
      AIServiceErrorCode.INVALID_PARAMETERS,
      AIServiceErrorCode.DATA_VALIDATION_FAILED
    ];

    return !nonRecoverableErrors.includes(code);
  }

  private initializeRecoveryStrategies(): void {
    // API Rate Limit Recovery
    this.registerRecoveryStrategy(AIServiceErrorCode.API_RATE_LIMIT_EXCEEDED, {
      canRecover: () => true,
      recover: async (error, context) => {
        // Wait longer for rate limit recovery
        await this.delay(5000);
        throw new Error('Retry after rate limit delay');
      },
      maxRetries: 3,
      retryDelay: 5000
    });

    // API Timeout Recovery
    this.registerRecoveryStrategy(AIServiceErrorCode.API_TIMEOUT, {
      canRecover: () => true,
      recover: async (error, context) => {
        // Retry with shorter timeout or simpler request
        throw new Error('Retry with adjusted parameters');
      },
      maxRetries: 2,
      retryDelay: 2000
    });

    // Vector Search Recovery
    this.registerRecoveryStrategy(AIServiceErrorCode.VECTOR_SEARCH_FAILED, {
      canRecover: () => true,
      recover: async (error, context) => {
        // Try with different search parameters
        throw new Error('Retry with fallback search parameters');
      },
      maxRetries: 2,
      retryDelay: 1000
    });

    // Database Connection Recovery
    this.registerRecoveryStrategy(AIServiceErrorCode.DATABASE_CONNECTION_FAILED, {
      canRecover: () => true,
      recover: async (error, context) => {
        // Attempt to reconnect to database
        throw new Error('Retry database connection');
      },
      maxRetries: 3,
      retryDelay: 2000
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public getErrorSummary(): {
    totalErrors: number;
    errorsByService: Record<string, number>;
    errorsByCode: Partial<Record<AIServiceErrorCode, number>>;
    errorsBySeverity: Partial<Record<AIServiceErrorSeverity, number>>;
  } {
    return this.logger.getErrorSummary();
  }

  public formatUserError(error: any, context?: string): string {
    return formatErrorForUser(error, context);
  }

  public formatTechnicalError(error: any): string {
    return formatErrorForTechnical(error);
  }
}