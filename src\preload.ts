import { contextBridge } from 'electron';
import exposeContexts from './helpers/ipc/context-exposer';
import { exposeConfigAPI } from './helpers/ipc/config/config-context';

const electronAPI = exposeContexts();

// Expose Config API
exposeConfigAPI();

if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', electronAPI);
  } catch (error) {
    console.error('Failed to expose electronAPI:', error);
  }
} else {
  // @ts-ignore (define in dts)
  window.electronAPI = electronAPI;
}
