/**
 * AI Operation Logger Service
 * Comprehensive logging and audit trail for AI operations
 */

import {
  AIOperationLog,
  AIServiceError,
  AIServiceErrorCode,
  AIServiceErrorSeverity
} from '../../types/errors';

export class AIOperationLogger {
  private static instance: AIOperationLogger;
  private logs: AIOperationLog[] = [];
  private errorCounts: Map<AIServiceErrorCode, number> = new Map();
  private serviceCounts: Map<string, number> = new Map();
  private severityCounts: Map<AIServiceErrorSeverity, number> = new Map();
  private maxLogEntries = 10000; // Prevent memory overflow

  private constructor() {
    this.initializeCounters();
  }

  public static getInstance(): AIOperationLogger {
    if (!AIOperationLogger.instance) {
      AIOperationLogger.instance = new AIOperationLogger();
    }
    return AIOperationLogger.instance;
  }

  private initializeCounters(): void {
    // Initialize error code counters
    Object.values(AIServiceErrorCode).forEach(code => {
      this.errorCounts.set(code, 0);
    });

    // Initialize severity counters
    Object.values(AIServiceErrorSeverity).forEach(severity => {
      this.severityCounts.set(severity, 0);
    });
  }

  public startOperation(
    service: string,
    operation: string,
    operationType: AIOperationLog['operationType'],
    inputData?: Record<string, any>,
    userId?: string,
    sessionId?: string
  ): string {
    const operationId = this.generateOperationId();

    const log: AIOperationLog = {
      id: operationId,
      service,
      operation,
      operationType,
      startTime: new Date(),
      success: false, // Will be updated on completion
      inputData: this.sanitizeData(inputData),
      userId,
      sessionId,
      metadata: {
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Server',
        timestamp: Date.now()
      }
    };

    this.addLog(log);
    return operationId;
  }

  public completeOperation(
    operationId: string,
    success: boolean,
    outputData?: Record<string, any>,
    error?: AIServiceError
  ): void {
    const logIndex = this.logs.findIndex(log => log.id === operationId);
    if (logIndex === -1) {
      console.warn(`[AI Logger] Operation ${operationId} not found for completion`);
      return;
    }

    const log = this.logs[logIndex];
    const endTime = new Date();

    log.endTime = endTime;
    log.duration = endTime.getTime() - log.startTime.getTime();
    log.success = success;
    log.outputData = this.sanitizeData(outputData);
    log.error = error;

    // Update counters if there was an error
    if (error) {
      this.updateErrorCounters(error);
    }

    // Update service counter
    const currentCount = this.serviceCounts.get(log.service) || 0;
    this.serviceCounts.set(log.service, currentCount + 1);

    // Log to console based on success/failure
    if (success) {
      console.info(`[AI Operation] ${log.service}.${log.operation} completed successfully in ${log.duration}ms`);
    } else {
      console.error(`[AI Operation] ${log.service}.${log.operation} failed after ${log.duration}ms`, {
        error: error?.code,
        message: error?.technicalMessage
      });
    }
  }

  public logError(error: AIServiceError): void {
    console.error(`[AI Error] ${error.service}.${error.operation}: ${error.code}`, {
      severity: error.severity,
      userMessage: error.userMessage,
      technicalMessage: error.technicalMessage,
      context: error.context,
      originalError: error.originalError?.message
    });

    this.updateErrorCounters(error);
  }

  public logSuccessfulRecovery(error: AIServiceError, attempts: number): void {
    console.info(`[AI Recovery] Successfully recovered from ${error.code} after ${attempts} attempts`, {
      service: error.service,
      operation: error.operation
    });
  }

  public logFailedRecovery(error: AIServiceError, originalInput?: any): void {
    console.error(`[AI Recovery] Failed to recover from ${error.code}`, {
      service: error.service,
      operation: error.operation,
      severity: error.severity,
      hadInput: !!originalInput
    });
  }

  public logFailedRecoveryAttempt(error: AIServiceError, attempt: number, recoveryError: any): void {
    console.warn(`[AI Recovery] Recovery attempt ${attempt} failed for ${error.code}`, {
      service: error.service,
      operation: error.operation,
      recoveryError: recoveryError?.message
    });
  }

  public logSuccessfulFallback(error: AIServiceError, fallbackStrategy: string): void {
    console.info(`[AI Fallback] Successfully used fallback strategy '${fallbackStrategy}' for ${error.code}`, {
      service: error.service,
      operation: error.operation
    });
  }

  public logFailedFallbackAttempt(error: AIServiceError, fallbackStrategy: string, fallbackError: any): void {
    console.warn(`[AI Fallback] Fallback strategy '${fallbackStrategy}' failed for ${error.code}`, {
      service: error.service,
      operation: error.operation,
      fallbackError: fallbackError?.message
    });
  }

  public getOperationLogs(
    service?: string,
    operationType?: AIOperationLog['operationType'],
    limit = 100
  ): AIOperationLog[] {
    let filteredLogs = [...this.logs];

    if (service) {
      filteredLogs = filteredLogs.filter(log => log.service === service);
    }

    if (operationType) {
      filteredLogs = filteredLogs.filter(log => log.operationType === operationType);
    }

    return filteredLogs
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit);
  }

  public getErrorLogs(limit = 100): AIOperationLog[] {
    return this.logs
      .filter(log => !log.success && log.error)
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit);
  }

  public getPerformanceMetrics(service?: string): {
    averageDuration: number;
    successRate: number;
    totalOperations: number;
    errorRate: number;
  } {
    let relevantLogs = this.logs.filter(log => log.duration !== undefined);

    if (service) {
      relevantLogs = relevantLogs.filter(log => log.service === service);
    }

    if (relevantLogs.length === 0) {
      return {
        averageDuration: 0,
        successRate: 0,
        totalOperations: 0,
        errorRate: 0
      };
    }

    const totalDuration = relevantLogs.reduce((sum, log) => sum + (log.duration || 0), 0);
    const successfulOperations = relevantLogs.filter(log => log.success).length;

    return {
      averageDuration: totalDuration / relevantLogs.length,
      successRate: (successfulOperations / relevantLogs.length) * 100,
      totalOperations: relevantLogs.length,
      errorRate: ((relevantLogs.length - successfulOperations) / relevantLogs.length) * 100
    };
  }

  public getErrorSummary(): {
    totalErrors: number;
    errorsByService: Record<string, number>;
    errorsByCode: Partial<Record<AIServiceErrorCode, number>>;
    errorsBySeverity: Partial<Record<AIServiceErrorSeverity, number>>;
  } {
    const errorsByService: Record<string, number> = {};
    const errorsByCode: Partial<Record<AIServiceErrorCode, number>> = {};
    const errorsBySeverity: Partial<Record<AIServiceErrorSeverity, number>> = {};

    // Convert Maps to objects
    this.serviceCounts.forEach((count, service) => {
      errorsByService[service] = count;
    });

    this.errorCounts.forEach((count, code) => {
      errorsByCode[code] = count;
    });

    this.severityCounts.forEach((count, severity) => {
      errorsBySeverity[severity] = count;
    });

    const totalErrors = Array.from(this.errorCounts.values()).reduce((sum, count) => sum + count, 0);

    return {
      totalErrors,
      errorsByService,
      errorsByCode,
      errorsBySeverity
    };
  }

  public clearLogs(): void {
    this.logs = [];
    this.initializeCounters();
    console.info('[AI Logger] All logs cleared');
  }

  public exportLogs(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      return this.exportLogsAsCSV();
    }
    return JSON.stringify(this.logs, null, 2);
  }

  private exportLogsAsCSV(): string {
    if (this.logs.length === 0) {
      return 'No logs available';
    }

    const headers = [
      'ID', 'Service', 'Operation', 'Type', 'Start Time', 'End Time',
      'Duration (ms)', 'Success', 'Error Code', 'Error Message', 'User ID'
    ];

    const rows = this.logs.map(log => [
      log.id,
      log.service,
      log.operation,
      log.operationType,
      log.startTime.toISOString(),
      log.endTime?.toISOString() || '',
      log.duration?.toString() || '',
      log.success.toString(),
      log.error?.code || '',
      log.error?.userMessage || '',
      log.userId || ''
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  private addLog(log: AIOperationLog): void {
    this.logs.push(log);

    // Prevent memory overflow by removing old logs
    if (this.logs.length > this.maxLogEntries) {
      this.logs = this.logs.slice(-this.maxLogEntries);
    }
  }

  private updateErrorCounters(error: AIServiceError): void {
    // Update error code counter
    const currentErrorCount = this.errorCounts.get(error.code) || 0;
    this.errorCounts.set(error.code, currentErrorCount + 1);

    // Update severity counter
    const currentSeverityCount = this.severityCounts.get(error.severity) || 0;
    this.severityCounts.set(error.severity, currentSeverityCount + 1);
  }

  private generateOperationId(): string {
    return `ai_op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private sanitizeData(data?: Record<string, any>): Record<string, any> | undefined {
    if (!data) return undefined;

    // Remove sensitive information
    const sensitiveKeys = ['password', 'token', 'apiKey', 'secret', 'key'];
    const sanitized = { ...data };

    const sanitizeObject = (obj: any): any => {
      if (typeof obj !== 'object' || obj === null) return obj;

      if (Array.isArray(obj)) {
        return obj.map(sanitizeObject);
      }

      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          result[key] = '[REDACTED]';
        } else if (typeof value === 'object') {
          result[key] = sanitizeObject(value);
        } else {
          result[key] = value;
        }
      }
      return result;
    };

    return sanitizeObject(sanitized);
  }
}