/**
 * Predictive Analytics Types
 * 
 * Type definitions for predictive analytics service including KPI monitoring,
 * time series forecasting, anomaly detection, and alert management.
 */

/**
 * KPI Data Point
 */
export interface KPIDataPoint {
  timestamp: string;
  value: number;
  metadata?: Record<string, any>;
}

/**
 * KPI Definition
 */
export interface KPI {
  id: string;
  name: string;
  description?: string;
  unit: string;
  department: 'dispatch' | 'cutting' | 'incoming-goods' | 'all';
  category: 'performance' | 'quality' | 'efficiency' | 'cost' | 'time';
  calculation_method?: string;
  target_value?: number;
  warning_threshold?: number;
  critical_threshold?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * KPI Monitoring Result
 */
export interface KPIMonitoringResult {
  kpi: KPI;
  current_value: number;
  previous_value?: number;
  change_percentage?: number;
  trend_direction: 'up' | 'down' | 'stable';
  status: 'normal' | 'warning' | 'critical';
  last_updated: string;
  data_points: KPIDataPoint[];
}

/**
 * Time Series Forecast Point
 */
export interface ForecastPoint {
  timestamp: string;
  predicted_value: number;
  confidence_interval_lower: number;
  confidence_interval_upper: number;
  confidence_score: number;
}

/**
 * KPI Forecast Result
 */
export interface KPIForecast {
  kpi_id: string;
  forecast_horizon_hours: number;
  current_value: number;
  predictions: ForecastPoint[];
  trend_direction: 'up' | 'down' | 'stable';
  overall_confidence: number;
  model_used: string;
  generated_at: string;
}

/**
 * Anomaly Detection Result
 */
export interface KPIAnomaly {
  id: string;
  kpi_id: string;
  timestamp: string;
  actual_value: number;
  expected_value: number;
  deviation_score: number;
  anomaly_type: 'spike' | 'drop' | 'trend_break' | 'seasonal_deviation';
  severity: 'low' | 'medium' | 'high';
  confidence: number;
  detected_at: string;
  is_resolved: boolean;
}

/**
 * Performance Pattern
 */
export interface PerformancePattern {
  pattern_id: string;
  department_id: string;
  pattern_type: 'seasonal' | 'cyclical' | 'trend' | 'irregular';
  description: string;
  frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
  strength: number; // 0-1 scale
  detected_at: string;
  kpis_affected: string[];
}

/**
 * Alert Configuration
 */
export interface AlertConfig {
  id: string;
  kpi_id: string;
  alert_type: 'threshold' | 'anomaly' | 'forecast' | 'pattern';
  conditions: AlertCondition[];
  notification_channels: ('email' | 'dashboard' | 'webhook')[];
  is_active: boolean;
  created_by: string;
  created_at: string;
}

/**
 * Alert Condition
 */
export interface AlertCondition {
  field: string;
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte' | 'between';
  value: number | number[];
  duration_minutes?: number; // How long condition must persist
}

/**
 * Alert Instance
 */
export interface Alert {
  id: string;
  alert_config_id: string;
  kpi_id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  current_value: number;
  threshold_value?: number;
  triggered_at: string;
  acknowledged_at?: string;
  resolved_at?: string;
  acknowledged_by?: string;
  status: 'active' | 'acknowledged' | 'resolved';
}

/**
 * Predictive Alert
 */
export interface PredictiveAlert {
  id: string;
  kpi_id: string;
  alert_type: 'threshold_breach' | 'anomaly_prediction' | 'capacity_limit';
  severity: 'low' | 'medium' | 'high' | 'critical';
  predicted_issue: string;
  probability: number; // 0-1 scale
  time_to_impact_hours: number;
  current_value: number;
  predicted_value: number;
  recommendations: string[];
  generated_at: string;
  expires_at: string;
  is_active: boolean;
}

/**
 * Capacity Forecast
 */
export interface CapacityForecast {
  department_id: string;
  resource_type: 'personnel' | 'equipment' | 'storage' | 'processing';
  current_capacity: number;
  current_utilization: number;
  predicted_demand: ForecastPoint[];
  capacity_gaps: CapacityGap[];
  recommendations: CapacityRecommendation[];
  forecast_generated_at: string;
}

/**
 * Capacity Gap
 */
export interface CapacityGap {
  start_time: string;
  end_time: string;
  required_capacity: number;
  available_capacity: number;
  gap_percentage: number;
  impact_severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Capacity Recommendation
 */
export interface CapacityRecommendation {
  type: 'increase_capacity' | 'redistribute_load' | 'schedule_adjustment';
  description: string;
  estimated_cost?: number;
  estimated_benefit?: number;
  implementation_time_days?: number;
  priority: 'low' | 'medium' | 'high';
}

/**
 * Resource Allocation Plan
 */
export interface AllocationPlan {
  plan_id: string;
  department_id: string;
  resources: ResourceAllocation[];
  optimization_objective: 'cost' | 'efficiency' | 'quality' | 'balanced';
  expected_improvement: number;
  implementation_cost: number;
  roi_estimate: number;
  generated_at: string;
}

/**
 * Resource Allocation
 */
export interface ResourceAllocation {
  resource_id: string;
  resource_type: 'personnel' | 'equipment' | 'storage' | 'processing';
  current_allocation: number;
  recommended_allocation: number;
  allocation_change: number;
  justification: string;
}

/**
 * Resource Definition
 */
export interface Resource {
  id: string;
  name: string;
  type: 'personnel' | 'equipment' | 'storage' | 'processing';
  department_id: string;
  capacity: number;
  current_utilization: number;
  cost_per_hour?: number;
  availability_schedule?: string;
  metadata?: Record<string, any>;
}

/**
 * Time Series Analysis Configuration
 */
export interface TimeSeriesConfig {
  seasonality_detection: boolean;
  trend_detection: boolean;
  anomaly_detection: boolean;
  forecast_horizon_hours: number;
  confidence_level: number; // 0.8, 0.9, 0.95, 0.99
  model_type: 'arima' | 'exponential_smoothing' | 'linear_regression' | 'auto';
}

/**
 * Anomaly Detection Configuration
 */
export interface AnomalyDetectionConfig {
  method: 'statistical' | 'isolation_forest' | 'z_score' | 'iqr';
  sensitivity: 'low' | 'medium' | 'high';
  window_size_hours: number;
  min_anomaly_duration_minutes: number;
}

/**
 * Predictive Analytics Service Configuration
 */
export interface PredictiveAnalyticsConfig {
  time_series: TimeSeriesConfig;
  anomaly_detection: AnomalyDetectionConfig;
  alert_evaluation_interval_minutes: number;
  data_retention_days: number;
  max_forecast_points: number;
}

/**
 * Service Status and Health
 */
export interface PredictiveAnalyticsStatus {
  service_name: string;
  is_healthy: boolean;
  last_forecast_run: string;
  last_anomaly_check: string;
  active_alerts_count: number;
  monitored_kpis_count: number;
  model_accuracy?: Record<string, number>;
  last_error?: string;
}