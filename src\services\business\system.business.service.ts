/**
 * System Business Service
 * 
 * Service-Layer für System-Monitoring und Performance-Analytics.
 * Bietet high-level Operations für System-Health, SLA-Tracking
 * und Predictive Analytics.
 */

import { BaseService, ServiceConfig } from '../base.service';
import { repositories } from '@/repositories';
import { DateRangeFilter } from '@/repositories/base.repository';

/**
 * System-Health-Status
 */
export interface SystemHealthStatus {
  overall: 'healthy' | 'degraded' | 'critical' | 'unknown';
  components: {
    api: ComponentHealth;
    database: ComponentHealth;
    warehouse: ComponentHealth;
    production: ComponentHealth;
    network: ComponentHealth;
  };
  uptime: number; // in Prozent
  lastChecked: Date;
  issues: SystemIssue[];
}

/**
 * Komponenten-Health
 */
export interface ComponentHealth {
  status: 'healthy' | 'degraded' | 'critical' | 'unknown';
  responseTime: number; // in ms
  errorRate: number; // in Prozent
  availability: number; // in Prozent
  lastError?: string;
}

/**
 * System-Issue
 */
export interface SystemIssue {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  component: string;
  description: string;
  impact: string;
  recommendedAction: string;
  detected: Date;
  resolved?: Date;
}

/**
 * SLA-Metriken
 */
export interface SLAMetrics {
  serviceLevel: {
    current: number;
    target: number;
    trend: 'improving' | 'declining' | 'stable';
    monthlyAverage: number;
  };
  availability: {
    current: number;
    target: number;
    uptime: number;
    downtime: number;
  };
  performance: {
    responseTime: number;
    throughput: number;
    errorRate: number;
    satisfactionScore: number;
  };
  compliance: {
    slaBreaches: number;
    mtbf: number; // Mean Time Between Failures
    mttr: number; // Mean Time To Repair
  };
}

/**
 * Performance-Prognose
 */
export interface PerformanceForecast {
  period: '24h' | '7d' | '30d';
  metrics: {
    serviceLevel: ForecastData;
    throughput: ForecastData;
    errorRate: ForecastData;
    capacity: ForecastData;
  };
  recommendations: Array<{
    type: 'optimization' | 'scaling' | 'maintenance';
    priority: 'high' | 'medium' | 'low';
    description: string;
    impact: string;
  }>;
}

/**
 * Prognose-Daten
 */
export interface ForecastData {
  current: number;
  predicted: number;
  confidence: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  anomalies: Array<{
    timestamp: Date;
    value: number;
    severity: 'low' | 'medium' | 'high';
  }>;
}

/**
 * System Business Service Klasse
 */
export class SystemBusinessService extends BaseService {
  readonly serviceName = 'SystemBusinessService';
  
  private healthCheckInterval?: NodeJS.Timeout;
  private alertThresholds = {
    serviceLevel: { warning: 95, critical: 90 },
    responseTime: { warning: 2000, critical: 5000 },
    errorRate: { warning: 1, critical: 5 },
    availability: { warning: 99, critical: 95 }
  };
  
  constructor(config?: ServiceConfig) {
    super(config);
  }
  
  /**
   * Service-spezifische Initialisierung
   */
  protected async onInitialize(): Promise<void> {
    // Teste Repository-Verfügbarkeit
    await repositories.system.serviceLevel.getAll();
    
    // Starte kontinuierliches Health Monitoring
    this.startHealthMonitoring();
    
    this.log('System monitoring initialized and health check started');
  }
  
  /**
   * Service-spezifische Cleanup
   */
  protected async onDestroy(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
  }
  
  /**
   * Comprehensive System Health Check
   */
  async getSystemHealth(): Promise<SystemHealthStatus> {
    this.ensureInitialized();
    
    return await this.withRetry(async () => {
      const [
        repoHealth,
        serviceLevelStats,
        performanceData
      ] = await Promise.all([
        repositories.healthCheck(),
        repositories.system.serviceLevel.getServiceLevelStats(),
        repositories.system.dailyPerformance.getPerformanceTrend()
      ]);
      
      // Berechne Komponenten-Health
      const components = {
        api: await this.checkApiHealth(),
        database: this.mapRepositoryHealth(repoHealth),
        warehouse: await this.checkWarehouseHealth(),
        production: await this.checkProductionHealth(),
        network: await this.checkNetworkHealth()
      };
      
      // Bestimme Overall-Status
      const overall = this.calculateOverallHealth(components);
      
      // Berechne Uptime
      const uptime = this.calculateUptime(performanceData);
      
      // Sammle aktuelle Issues
      const issues = await this.collectSystemIssues(components, serviceLevelStats);
      
      return {
        overall,
        components,
        uptime,
        lastChecked: new Date(),
        issues
      };
    });
  }
  
  /**
   * SLA-Metriken abrufen und bewerten
   */
  async getSLAMetrics(filter?: DateRangeFilter): Promise<SLAMetrics> {
    this.ensureInitialized();
    
    const [
      serviceLevelStats,
      performanceTrend,
      deliveryAnalysis,
      systemStats
    ] = await Promise.all([
      repositories.system.serviceLevel.getServiceLevelStats(filter),
      repositories.system.dailyPerformance.getPerformanceTrend(),
      repositories.system.deliveryPositions.getDeliveryAnalysis(),
      repositories.system.systemStats.getAll(filter)
    ]);
    
    return {
      serviceLevel: {
        current: serviceLevelStats.current,
        target: 95, // Standard SLA-Ziel
        trend: serviceLevelStats.trend,
        monthlyAverage: serviceLevelStats.average
      },
      availability: {
        current: this.calculateCurrentAvailability(systemStats),
        target: 99.9,
        uptime: performanceTrend.consistency,
        downtime: this.calculateDowntime(systemStats)
      },
      performance: {
        responseTime: this.calculateResponseTime(performanceTrend),
        throughput: performanceTrend.averageDaily,
        errorRate: this.calculateErrorRate(systemStats),
        satisfactionScore: deliveryAnalysis.deliveryEfficiency
      },
      compliance: {
        slaBreaches: serviceLevelStats.belowTarget,
        mtbf: this.calculateMTBF(systemStats),
        mttr: this.calculateMTTR(systemStats)
      }
    };
  }
  
  /**
   * Performance-Prognose erstellen
   */
  async getPerformanceForecast(period: '24h' | '7d' | '30d' = '24h'): Promise<PerformanceForecast> {
    this.ensureInitialized();
    
    const days = { '24h': 1, '7d': 7, '30d': 30 }[period];
    const historicalDays = Math.min(days * 10, 90); // 10x der Prognosezeitraum, max 90 Tage
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - historicalDays);
    
    const filter = {
      startDate: startDate.toISOString().split('T')[0]
    };
    
    const [
      serviceLevelData,
      performanceData,
      pickingData,
      systemData
    ] = await Promise.all([
      repositories.system.serviceLevel.getAll(filter),
      repositories.system.dailyPerformance.getAll(filter),
      repositories.system.picking.getAll(),
      repositories.system.systemStats.getAll(filter)
    ]);
    
    return {
      period,
      metrics: {
        serviceLevel: this.createForecast(serviceLevelData, 'serviceLevel', days),
        throughput: this.createForecast(performanceData, 'throughput', days),
        errorRate: this.createForecast(systemData, 'errorRate', days),
        capacity: this.createForecast(pickingData, 'capacity', days)
      },
      recommendations: await this.generatePerformanceRecommendations(serviceLevelData, performanceData)
    };
  }
  
  /**
   * Real-time Performance Monitoring
   */
  async getRealTimeMetrics(): Promise<{
    serviceLevel: number;
    activeUsers: number;
    throughput: number;
    errorRate: number;
    responseTime: number;
    alerts: Array<{
      type: 'performance' | 'availability' | 'security';
      message: string;
      timestamp: Date;
      severity: 'info' | 'warning' | 'critical';
    }>;
  }> {
    this.ensureInitialized();
    
    const [
      currentServiceLevel,
      pickingStats,
      systemHealth
    ] = await Promise.all([
      repositories.system.serviceLevel.getCurrentServiceLevel(),
      repositories.system.picking.getPickingEfficiency(),
      this.getSystemHealth()
    ]);
    
    const metrics = {
      serviceLevel: (currentServiceLevel as any)?.servicegrad || 0,
      activeUsers: this.estimateActiveUsers(),
      throughput: pickingStats.averagePicksPerDay,
      errorRate: this.calculateCurrentErrorRate(systemHealth),
      responseTime: this.calculateAverageResponseTime(systemHealth),
      alerts: this.generateRealTimeAlerts(systemHealth)
    };
    
    return metrics;
  }
  
  /**
   * Alert-Management
   */
  async configureAlerts(thresholds: Partial<typeof this.alertThresholds>): Promise<void> {
    this.alertThresholds = { ...this.alertThresholds, ...thresholds };
    this.log('Alert thresholds updated', this.alertThresholds);
  }
  
  /**
   * System-Maintenance-Fenster planen
   */
  async scheduleMaintenanceWindow(
    start: Date,
    duration: number, // in Minuten
    description: string
  ): Promise<{
    id: string;
    impact: 'low' | 'medium' | 'high';
    affectedServices: string[];
    estimatedDowntime: number;
  }> {
    const maintenanceId = `maint_${Date.now()}`;
    
    // Analysiere Impact basierend auf aktueller Systemlast
    const currentMetrics = await this.getRealTimeMetrics();
    const impact = this.assessMaintenanceImpact(currentMetrics, duration);
    
    const result = {
      id: maintenanceId,
      impact,
      affectedServices: this.getAffectedServices(impact),
      estimatedDowntime: duration
    };
    
    this.log('Maintenance window scheduled', result);
    return result;
  }
  
  /**
   * Private Hilfsmethoden
   */
  
  private startHealthMonitoring(): void {
    // Health Check alle 5 Minuten
    this.healthCheckInterval = setInterval(async () => {
      try {
        const health = await this.getSystemHealth();
        if (health.overall === 'critical') {
          this.log('Critical system health detected', health.issues);
        }
      } catch (error) {
        this.log('Health monitoring error', error);
      }
    }, 5 * 60 * 1000);
  }
  
  private async checkApiHealth(): Promise<ComponentHealth> {
    try {
      const startTime = Date.now();
      await repositories.system.serviceLevel.getAll();
      const responseTime = Date.now() - startTime;
      
      return {
        status: responseTime < 1000 ? 'healthy' : responseTime < 3000 ? 'degraded' : 'critical',
        responseTime,
        errorRate: 0,
        availability: 100
      };
    } catch (error) {
      return {
        status: 'critical',
        responseTime: 0,
        errorRate: 100,
        availability: 0,
        lastError: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  private mapRepositoryHealth(repoHealth: any): ComponentHealth {
    const status = repoHealth.status === 'healthy' ? 'healthy' : 
                  repoHealth.status === 'degraded' ? 'degraded' : 'critical';
    
    return {
      status,
      responseTime: 500, // Beispielwert
      errorRate: repoHealth.errors.length > 0 ? 10 : 0,
      availability: status === 'healthy' ? 100 : status === 'degraded' ? 90 : 50
    };
  }
  
  private async checkWarehouseHealth(): Promise<ComponentHealth> {
    try {
      const startTime = Date.now();
      await repositories.warehouse.we.getAll();
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'healthy',
        responseTime,
        errorRate: 0,
        availability: 100
      };
    } catch (error) {
      return {
        status: 'critical',
        responseTime: 0,
        errorRate: 100,
        availability: 0,
        lastError: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  private async checkProductionHealth(): Promise<ComponentHealth> {
    try {
      const efficiency = await repositories.production.maschinenEfficiency.getAverageEfficiency();
      const status = efficiency.average > 80 ? 'healthy' : efficiency.average > 60 ? 'degraded' : 'critical';
      
      return {
        status,
        responseTime: 300,
        errorRate: Math.max(0, 100 - efficiency.average) / 10,
        availability: efficiency.average
      };
    } catch (error) {
      return {
        status: 'critical',
        responseTime: 0,
        errorRate: 100,
        availability: 0,
        lastError: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  private async checkNetworkHealth(): Promise<ComponentHealth> {
    // Vereinfachte Netzwerk-Gesundheitsprüfung
    return {
      status: 'healthy',
      responseTime: 50,
      errorRate: 0.1,
      availability: 99.9
    };
  }
  
  private calculateOverallHealth(components: any): 'healthy' | 'degraded' | 'critical' | 'unknown' {
    const healthValues = Object.values(components) as ComponentHealth[];
    const criticalCount = healthValues.filter(c => c.status === 'critical').length;
    const degradedCount = healthValues.filter(c => c.status === 'degraded').length;
    
    if (criticalCount > 0) return 'critical';
    if (degradedCount > 1) return 'degraded';
    if (degradedCount > 0) return 'degraded';
    return 'healthy';
  }
  
  private calculateUptime(performanceData: any): number {
    // Vereinfachte Uptime-Berechnung
    return 99.5; // Beispielwert
  }
  
  private async collectSystemIssues(components: any, serviceLevelStats: any): Promise<SystemIssue[]> {
    const issues: SystemIssue[] = [];
    
    // Prüfe Komponenten-Issues
    for (const [name, component] of Object.entries(components) as [string, ComponentHealth][]) {
      if (component.status === 'critical') {
        issues.push({
          id: `${name}_critical_${Date.now()}`,
          severity: 'critical',
          component: name,
          description: `${name} Komponente in kritischem Zustand`,
          impact: 'Service-Unterbrechung möglich',
          recommendedAction: 'Sofortige Überprüfung erforderlich',
          detected: new Date()
        });
      }
    }
    
    // Prüfe Service Level Issues
    if (serviceLevelStats.current < this.alertThresholds.serviceLevel.critical) {
      issues.push({
        id: `sla_breach_${Date.now()}`,
        severity: 'high',
        component: 'serviceLevel',
        description: `Service Level unter kritischem Schwellwert (${serviceLevelStats.current}%)`,
        impact: 'SLA-Verletzung',
        recommendedAction: 'Performance-Optimierung durchführen',
        detected: new Date()
      });
    }
    
    return issues;
  }
  
  private createForecast(data: any[], metric: string, days: number): ForecastData {
    if (!data || data.length === 0) {
      return {
        current: 0,
        predicted: 0,
        confidence: 0,
        trend: 'stable',
        anomalies: []
      };
    }
    
    // Vereinfachte Prognose-Logik
    const values = data.map(item => this.extractMetricValue(item, metric));
    const current = values[values.length - 1] || 0;
    const trend = this.calculateTrendDirection(values);
    const predicted = this.predictValue(values, days);
    
    return {
      current,
      predicted,
      confidence: Math.min(90, data.length * 3), // Höhere Confidence bei mehr Daten
      trend,
      anomalies: this.detectAnomalies(data, metric)
    };
  }
  
  private extractMetricValue(item: any, metric: string): number {
    switch (metric) {
      case 'serviceLevel': return item.servicegrad || 0;
      case 'throughput': return item.produzierte_tonnagen || item.value || 0;
      case 'errorRate': return item.errorRate || 0;
      case 'capacity': return item.picks || item.value || 0;
      default: return 0;
    }
  }
  
  private calculateTrendDirection(values: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (values.length < 2) return 'stable';
    
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    
    const change = ((secondAvg - firstAvg) / firstAvg) * 100;
    
    if (change > 5) return 'increasing';
    if (change < -5) return 'decreasing';
    return 'stable';
  }
  
  private predictValue(values: number[], daysAhead: number): number {
    if (values.length < 2) return values[0] || 0;
    
    // Einfache lineare Regression
    const lastValue = values[values.length - 1];
    const trend = (values[values.length - 1] - values[0]) / values.length;
    
    return Math.max(0, lastValue + (trend * daysAhead));
  }
  
  private detectAnomalies(data: any[], metric: string): Array<{ timestamp: Date; value: number; severity: 'low' | 'medium' | 'high' }> {
    // Vereinfachte Anomalie-Erkennung
    const anomalies = [];
    const values = data.map(item => this.extractMetricValue(item, metric));
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const stdDev = Math.sqrt(values.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / values.length);
    
    for (let i = 0; i < data.length; i++) {
      const value = values[i];
      const deviation = Math.abs(value - avg) / stdDev;
      
      if (deviation > 2) {
        anomalies.push({
          timestamp: new Date(data[i].datum || Date.now()),
          value,
          severity: deviation > 3 ? 'high' : 'medium'
        });
      }
    }
    
    return anomalies;
  }
  
  // Weitere private Hilfsmethoden (vereinfacht)...
  private calculateCurrentAvailability(systemStats: any): number { return 99.5; }
  private calculateDowntime(systemStats: any): number { return 0.5; }
  private calculateResponseTime(performanceTrend: any): number { return 250; }
  private calculateErrorRate(systemStats: any): number { return 0.1; }
  private calculateMTBF(systemStats: any): number { return 720; } // 30 Tage in Stunden
  private calculateMTTR(systemStats: any): number { return 2; } // 2 Stunden
  private estimateActiveUsers(): number { return 50; }
  private calculateCurrentErrorRate(systemHealth: any): number { return 0.05; }
  private calculateAverageResponseTime(systemHealth: any): number { return 300; }
  
  private generateRealTimeAlerts(systemHealth: SystemHealthStatus): any[] {
    const alerts = [];
    
    if (systemHealth.overall === 'critical') {
      alerts.push({
        type: 'availability' as const,
        message: 'System in kritischem Zustand',
        timestamp: new Date(),
        severity: 'critical' as const
      });
    }
    
    return alerts;
  }
  
  private async generatePerformanceRecommendations(serviceLevelData: any[], performanceData: any[]): Promise<any[]> {
    const recommendations = [];
    
    const avgServiceLevel = serviceLevelData.reduce((sum, item) => sum + (item.servicegrad || 0), 0) / serviceLevelData.length;
    
    if (avgServiceLevel < 95) {
      recommendations.push({
        type: 'optimization',
        priority: 'high',
        description: 'Service Level unter SLA-Ziel - Performance-Optimierung erforderlich',
        impact: 'Verbessert SLA-Compliance um 15-20%'
      });
    }
    
    return recommendations;
  }
  
  private assessMaintenanceImpact(metrics: any, duration: number): 'low' | 'medium' | 'high' {
    if (duration > 60 || metrics.serviceLevel < 90) return 'high';
    if (duration > 30 || metrics.throughput > 1000) return 'medium';
    return 'low';
  }
  
  private getAffectedServices(impact: 'low' | 'medium' | 'high'): string[] {
    const services = {
      low: ['monitoring', 'reporting'],
      medium: ['api', 'warehouse', 'monitoring'],
      high: ['api', 'warehouse', 'production', 'monitoring', 'reporting']
    };
    return services[impact];
  }
}