import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui/avatar';
import type { ComponentProps, HTMLAttributes } from 'react';
import { cn } from '@/lib/utils';
import type { UIMessage } from 'ai';

export type MessageProps = HTMLAttributes<HTMLDivElement> & {
  from: UIMessage['role'];
};

export const Message = ({ className, from, ...props }: MessageProps) => (
  <div
    className={cn(
      'group flex w-full items-start gap-3 py-4',
      from === 'user' ? 'is-user justify-end' : 'is-assistant justify-start',
      className,
    )}
    {...props}
  />
);

export type MessageContentProps = HTMLAttributes<HTMLDivElement>;

export const MessageContent = ({
  children,
  className,
  ...props
}: MessageContentProps) => (
  <div
    className={cn(
      'flex flex-col gap-2 rounded-lg text-sm px-4 py-3 overflow-hidden border shadow-sm',
      'group-[.is-user]:bg-green-100 group-[.is-user]:text-gray-900 group-[.is-user]:border-gray-200',
      'group-[.is-assistant]:bg-blue-100 group-[.is-assistant]:text-gray-900 group-[.is-assistant]:border-gray-200',
      'dark:group-[.is-assistant]:bg-gray-800 dark:group-[.is-assistant]:text-gray-100 dark:group-[.is-assistant]:border-gray-700',
      className,
    )}
    {...props}
  >
    <div>{children}</div>
  </div>
);

export type MessageAvatarProps = ComponentProps<typeof Avatar> & {
  src: string;
  name?: string;
};

export const MessageAvatar = ({
  src,
  name,
  className,
  ...props
}: MessageAvatarProps) => (
  <Avatar
    className={cn('size-8 ring-1 ring-border flex-shrink-0', className)}
    {...props}
  >
    <AvatarImage alt="" className="mt-0 mb-0" src={src} />
    <AvatarFallback>{name?.slice(0, 2) || 'ME'}</AvatarFallback>
  </Avatar>
);
