/**
 * AI Error Boundary Component Tests
 * Tests for React error boundary functionality
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { AIErrorBoundary, withAIErrorBoundary } from '../AIErrorBoundary';
import { AIErrorHandler } from '../../../services/error-handling/AIErrorHandler';

// Mock the error handler
vi.mock('../../../services/error-handling/AIErrorHandler');

// Test component that throws an error
const ThrowError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test component error');
  }
  return <div>Working component</div>;
};

describe('AIErrorBoundary', () => {
  let mockErrorHandler: any;

  beforeEach(() => {
    mockErrorHandler = {
      createError: vi.fn().mockReturnValue({
        code: 'SERVICE_UNAVAILABLE',
        service: 'TestService',
        operation: 'render',
        userMessage: 'Test error message'
      })
    };

    vi.mocked(AIErrorHandler.getInstance).mockReturnValue(mockErrorHandler);
    
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Normal Operation', () => {
    it('should render children when no error occurs', () => {
      render(
        <AIErrorBoundary serviceName="TestService">
          <ThrowError shouldThrow={false} />
        </AIErrorBoundary>
      );

      expect(screen.getByText('Working component')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should catch and display error when child component throws', () => {
      render(
        <AIErrorBoundary serviceName="TestService">
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      expect(screen.getByText('KI-Service Fehler')).toBeInTheDocument();
      expect(screen.getByText('Ein unerwarteter Fehler ist aufgetreten')).toBeInTheDocument();
      expect(screen.getByText('Der KI-Service konnte nicht geladen werden')).toBeInTheDocument();
    });

    it('should display service name in error UI', () => {
      render(
        <AIErrorBoundary serviceName="CustomService">
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      expect(screen.getByText('CustomService')).toBeInTheDocument();
    });

    it('should display error ID', () => {
      render(
        <AIErrorBoundary serviceName="TestService">
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      expect(screen.getByText(/Fehler-ID:/)).toBeInTheDocument();
    });

    it('should display timestamp', () => {
      render(
        <AIErrorBoundary serviceName="TestService">
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      expect(screen.getByText(/Zeit:/)).toBeInTheDocument();
    });

    it('should create structured AI error', () => {
      const mockOnError = vi.fn();

      render(
        <AIErrorBoundary serviceName="TestService" onError={mockOnError}>
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      expect(mockErrorHandler.createError).toHaveBeenCalledWith(
        'SERVICE_UNAVAILABLE',
        'TestService',
        'render',
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String),
          errorBoundary: true
        })
      );
    });

    it('should call custom error handler when provided', () => {
      const mockOnError = vi.fn();

      render(
        <AIErrorBoundary serviceName="TestService" onError={mockOnError}>
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      expect(mockOnError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      );
    });
  });

  describe('Error UI Interactions', () => {
    it('should allow retry and reset error state', () => {
      const { rerender } = render(
        <AIErrorBoundary serviceName="TestService">
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      // Error should be displayed
      expect(screen.getByText('KI-Service Fehler')).toBeInTheDocument();

      // Click retry button
      const retryButton = screen.getByText('Erneut versuchen');
      fireEvent.click(retryButton);

      // Re-render with working component
      rerender(
        <AIErrorBoundary serviceName="TestService">
          <ThrowError shouldThrow={false} />
        </AIErrorBoundary>
      );

      // Should show working component now
      expect(screen.getByText('Working component')).toBeInTheDocument();
    });

    it('should show error report dialog when report button clicked', () => {
      // Mock window.alert
      const mockAlert = vi.spyOn(window, 'alert').mockImplementation(() => {});

      render(
        <AIErrorBoundary serviceName="TestService">
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      const reportButton = screen.getByText('Fehler melden');
      fireEvent.click(reportButton);

      expect(mockAlert).toHaveBeenCalledWith(
        expect.stringContaining('Fehlerbericht wurde erstellt')
      );

      mockAlert.mockRestore();
    });
  });

  describe('Development Mode', () => {
    it('should show technical details in development mode', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      render(
        <AIErrorBoundary serviceName="TestService">
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      expect(screen.getByText('Technische Details (nur in Entwicklung)')).toBeInTheDocument();

      process.env.NODE_ENV = originalEnv;
    });

    it('should not show technical details in production mode', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      render(
        <AIErrorBoundary serviceName="TestService">
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      expect(screen.queryByText('Technische Details (nur in Entwicklung)')).not.toBeInTheDocument();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Custom Fallback', () => {
    it('should render custom fallback when provided', () => {
      const customFallback = <div>Custom error fallback</div>;

      render(
        <AIErrorBoundary serviceName="TestService" fallback={customFallback}>
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      expect(screen.getByText('Custom error fallback')).toBeInTheDocument();
      expect(screen.queryByText('KI-Service Fehler')).not.toBeInTheDocument();
    });
  });

  describe('Default Service Name', () => {
    it('should use default service name when not provided', () => {
      render(
        <AIErrorBoundary>
          <ThrowError shouldThrow={true} />
        </AIErrorBoundary>
      );

      expect(mockErrorHandler.createError).toHaveBeenCalledWith(
        'SERVICE_UNAVAILABLE',
        'AIComponent',
        'render',
        expect.any(Error),
        expect.any(Object)
      );
    });
  });
});

describe('withAIErrorBoundary HOC', () => {
  let mockErrorHandler: any;

  beforeEach(() => {
    mockErrorHandler = {
      createError: vi.fn().mockReturnValue({
        code: 'SERVICE_UNAVAILABLE',
        service: 'TestService',
        operation: 'render'
      })
    };

    vi.mocked(AIErrorHandler.getInstance).mockReturnValue(mockErrorHandler);
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should wrap component with error boundary', () => {
    const WrappedComponent = withAIErrorBoundary(ThrowError, 'TestService');

    render(<WrappedComponent shouldThrow={false} />);

    expect(screen.getByText('Working component')).toBeInTheDocument();
  });

  it('should catch errors in wrapped component', () => {
    const WrappedComponent = withAIErrorBoundary(ThrowError, 'TestService');

    render(<WrappedComponent shouldThrow={true} />);

    expect(screen.getByText('KI-Service Fehler')).toBeInTheDocument();
  });

  it('should use custom fallback in HOC', () => {
    const customFallback = <div>HOC custom fallback</div>;
    const WrappedComponent = withAIErrorBoundary(ThrowError, 'TestService', customFallback);

    render(<WrappedComponent shouldThrow={true} />);

    expect(screen.getByText('HOC custom fallback')).toBeInTheDocument();
  });

  it('should set correct display name', () => {
    const TestComponent = () => <div>Test</div>;
    TestComponent.displayName = 'TestComponent';
    
    const WrappedComponent = withAIErrorBoundary(TestComponent, 'TestService');

    expect(WrappedComponent.displayName).toBe('withAIErrorBoundary(TestComponent)');
  });

  it('should handle component without display name', () => {
    const WrappedComponent = withAIErrorBoundary(ThrowError, 'TestService');

    expect(WrappedComponent.displayName).toBe('withAIErrorBoundary(ThrowError)');
  });
});