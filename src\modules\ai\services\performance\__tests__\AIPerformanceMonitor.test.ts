import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { AIPerformanceMonitor } from '../AIPerformanceMonitor';

describe('AIPerformanceMonitor', () => {
  let performanceMonitor: AIPerformanceMonitor;

  beforeEach(() => {
    performanceMonitor = new AIPerformanceMonitor();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  describe('Operation Monitoring', () => {
    it('should track operation start and end', () => {
      const operationId = 'test-op-1';
      const operationName = 'testOperation';

      performanceMonitor.startOperation(operationId, operationName);
      
      // Simulate operation duration
      vi.advanceTimersByTime(1000);
      
      const metric = performanceMonitor.endOperation(operationId, true);

      expect(metric).toBeDefined();
      expect(metric?.operationName).toBe(operationName);
      expect(metric?.duration).toBe(1000);
      expect(metric?.success).toBe(true);
    });

    it('should handle operation failures', () => {
      const operationId = 'test-op-2';
      const operationName = 'failingOperation';
      const errorMessage = 'Operation failed';

      performanceMonitor.startOperation(operationId, operationName);
      vi.advanceTimersByTime(500);
      
      const metric = performanceMonitor.endOperation(operationId, false, errorMessage);

      expect(metric?.success).toBe(false);
      expect(metric?.errorMessage).toBe(errorMessage);
      expect(metric?.duration).toBe(500);
    });

    it('should return null for unknown operation IDs', () => {
      const metric = performanceMonitor.endOperation('unknown-id', true);
      expect(metric).toBeNull();
    });

    it('should record completed operations directly', () => {
      const operationName = 'directOperation';
      const duration = 2000;
      const memoryUsage = 1024 * 1024; // 1MB

      performanceMonitor.recordOperation(
        operationName,
        duration,
        true,
        memoryUsage,
        undefined,
        { custom: 'metadata' }
      );

      const stats = performanceMonitor.getOperationStats(operationName);
      expect(stats.totalCalls).toBe(1);
      expect(stats.averageDuration).toBe(duration);
      expect(stats.averageMemoryUsage).toBe(memoryUsage);
    });
  });

  describe('Performance Statistics', () => {
    beforeEach(() => {
      // Add some test data
      performanceMonitor.recordOperation('operation1', 1000, true);
      performanceMonitor.recordOperation('operation1', 1500, true);
      performanceMonitor.recordOperation('operation1', 2000, false, undefined, 'Error occurred');
      performanceMonitor.recordOperation('operation2', 500, true);
    });

    it('should calculate operation statistics correctly', () => {
      const stats = performanceMonitor.getOperationStats('operation1');

      expect(stats.totalCalls).toBe(3);
      expect(stats.successfulCalls).toBe(2);
      expect(stats.failedCalls).toBe(1);
      expect(stats.averageDuration).toBe(1500); // (1000 + 1500 + 2000) / 3
      expect(stats.minDuration).toBe(1000);
      expect(stats.maxDuration).toBe(2000);
      expect(stats.errorRate).toBeCloseTo(0.33, 2); // 1/3
    });

    it('should calculate percentiles correctly', () => {
      // Add more data points for better percentile calculation
      for (let i = 0; i < 100; i++) {
        performanceMonitor.recordOperation('percentileTest', i * 10, true);
      }

      const stats = performanceMonitor.getOperationStats('percentileTest');
      expect(stats.p95Duration).toBe(940); // 95th percentile
      expect(stats.p99Duration).toBe(980); // 99th percentile
    });

    it('should return empty stats for non-existent operations', () => {
      const stats = performanceMonitor.getOperationStats('nonExistent');
      
      expect(stats.totalCalls).toBe(0);
      expect(stats.averageDuration).toBe(0);
      expect(stats.errorRate).toBe(0);
    });

    it('should get all operation statistics', () => {
      const allStats = performanceMonitor.getAllOperationStats();
      
      expect(allStats).toHaveLength(2);
      expect(allStats.map(s => s.operationName)).toContain('operation1');
      expect(allStats.map(s => s.operationName)).toContain('operation2');
    });

    it('should filter statistics by time window', () => {
      const now = Date.now();
      vi.setSystemTime(now);

      // Add recent operation
      performanceMonitor.recordOperation('recentOp', 1000, true);

      // Go back in time and add old operation
      vi.setSystemTime(now - 7200000); // 2 hours ago
      performanceMonitor.recordOperation('oldOp', 1000, true);

      vi.setSystemTime(now);

      // Get stats for last hour (3600000ms)
      const recentStats = performanceMonitor.getAllOperationStats(3600000);
      const operationNames = recentStats.map(s => s.operationName);
      
      expect(operationNames).toContain('recentOp');
      expect(operationNames).not.toContain('oldOp');
    });
  });

  describe('Resource Monitoring', () => {
    it('should collect current resource usage', () => {
      const usage = performanceMonitor.getCurrentResourceUsage();

      expect(usage).toHaveProperty('timestamp');
      expect(usage).toHaveProperty('memoryUsage');
      expect(usage).toHaveProperty('cpuUsage');
      expect(usage).toHaveProperty('activeOperations');
      expect(usage.timestamp).toBeTypeOf('number');
    });

    it('should track resource history', () => {
      // Trigger resource collection
      vi.advanceTimersByTime(30000); // Advance by collection interval

      const history = performanceMonitor.getResourceHistory();
      expect(history.length).toBeGreaterThan(0);
    });

    it('should filter resource history by time window', () => {
      const now = Date.now();
      vi.setSystemTime(now);

      // Trigger multiple collections
      vi.advanceTimersByTime(30000);
      vi.advanceTimersByTime(30000);
      vi.advanceTimersByTime(30000);

      const allHistory = performanceMonitor.getResourceHistory();
      const recentHistory = performanceMonitor.getResourceHistory(60000); // Last minute

      expect(recentHistory.length).toBeLessThanOrEqual(allHistory.length);
    });
  });

  describe('Performance Alerts', () => {
    it('should generate high latency alerts', () => {
      // Record operation with high latency
      performanceMonitor.recordOperation('slowOperation', 6000, true); // 6 seconds

      const alerts = performanceMonitor.getAlerts();
      const latencyAlert = alerts.find(a => a.type === 'high_latency');

      expect(latencyAlert).toBeDefined();
      expect(latencyAlert?.operationName).toBe('slowOperation');
      expect(latencyAlert?.severity).toBe('critical');
    });

    it('should generate high error rate alerts', () => {
      // Record multiple failed operations
      for (let i = 0; i < 15; i++) {
        performanceMonitor.recordOperation('errorProneOp', 1000, i < 5); // 10 failures out of 15
      }

      const alerts = performanceMonitor.getAlerts();
      const errorAlert = alerts.find(a => a.type === 'high_error_rate');

      expect(errorAlert).toBeDefined();
      expect(errorAlert?.operationName).toBe('errorProneOp');
    });

    it('should filter alerts by severity', () => {
      // Generate alerts of different severities
      performanceMonitor.recordOperation('criticalOp', 12000, true); // Critical latency
      performanceMonitor.recordOperation('highOp', 6000, true); // High latency

      const criticalAlerts = performanceMonitor.getAlerts('critical');
      const highAlerts = performanceMonitor.getAlerts('high');

      expect(criticalAlerts.length).toBeGreaterThan(0);
      expect(highAlerts.length).toBeGreaterThan(0);
      expect(criticalAlerts.every(a => a.severity === 'critical')).toBe(true);
      expect(highAlerts.every(a => a.severity === 'high')).toBe(true);
    });

    it('should clear alerts', () => {
      // Generate an alert
      performanceMonitor.recordOperation('alertOp', 6000, true);
      
      const alertsBefore = performanceMonitor.getAlerts();
      expect(alertsBefore.length).toBeGreaterThan(0);

      performanceMonitor.clearAlerts();
      
      const alertsAfter = performanceMonitor.getAlerts();
      expect(alertsAfter.length).toBe(0);
    });

    it('should clear specific alerts by ID', () => {
      // Generate multiple alerts
      performanceMonitor.recordOperation('alertOp1', 6000, true);
      performanceMonitor.recordOperation('alertOp2', 7000, true);

      const alerts = performanceMonitor.getAlerts();
      expect(alerts.length).toBe(2);

      const alertIdToClear = alerts[0].id;
      performanceMonitor.clearAlerts([alertIdToClear]);

      const remainingAlerts = performanceMonitor.getAlerts();
      expect(remainingAlerts.length).toBe(1);
      expect(remainingAlerts[0].id).not.toBe(alertIdToClear);
    });
  });

  describe('Optimization Recommendations', () => {
    it('should generate recommendations for high error rates', () => {
      // Create operation with high error rate
      for (let i = 0; i < 20; i++) {
        performanceMonitor.recordOperation('errorOp', 1000, i < 15); // 25% error rate
      }

      const recommendations = performanceMonitor.getOptimizationRecommendations();
      const errorRecommendation = recommendations.find(r => 
        r.includes('errorOp') && r.includes('error rate')
      );

      expect(errorRecommendation).toBeDefined();
    });

    it('should generate recommendations for high latency', () => {
      performanceMonitor.recordOperation('slowOp', 4000, true);

      const recommendations = performanceMonitor.getOptimizationRecommendations();
      const latencyRecommendation = recommendations.find(r => 
        r.includes('slowOp') && r.includes('latency')
      );

      expect(latencyRecommendation).toBeDefined();
    });

    it('should generate recommendations for inconsistent performance', () => {
      // Create operation with high variance
      performanceMonitor.recordOperation('inconsistentOp', 1000, true);
      performanceMonitor.recordOperation('inconsistentOp', 5000, true); // High P95

      const recommendations = performanceMonitor.getOptimizationRecommendations();
      const varianceRecommendation = recommendations.find(r => 
        r.includes('inconsistentOp') && r.includes('inconsistent')
      );

      expect(varianceRecommendation).toBeDefined();
    });

    it('should generate recommendations for high memory usage', () => {
      const highMemoryUsage = 60 * 1024 * 1024; // 60MB
      performanceMonitor.recordOperation('memoryOp', 1000, true, highMemoryUsage);

      const recommendations = performanceMonitor.getOptimizationRecommendations();
      const memoryRecommendation = recommendations.find(r => 
        r.includes('memoryOp') && r.includes('memory')
      );

      expect(memoryRecommendation).toBeDefined();
    });
  });

  describe('Cleanup and Maintenance', () => {
    it('should cleanup old metrics', () => {
      const now = Date.now();
      vi.setSystemTime(now);

      // Add old metrics
      vi.setSystemTime(now - 7200000); // 2 hours ago
      performanceMonitor.recordOperation('oldOp', 1000, true);

      vi.setSystemTime(now);
      performanceMonitor.recordOperation('newOp', 1000, true);

      // Trigger cleanup
      vi.advanceTimersByTime(300000); // 5 minutes

      const stats = performanceMonitor.getAllOperationStats();
      const operationNames = stats.map(s => s.operationName);
      
      expect(operationNames).toContain('newOp');
      // Old operations should be cleaned up (depending on cleanup logic)
    });

    it('should limit metrics history size', () => {
      // Add many metrics to test size limit
      for (let i = 0; i < 15000; i++) {
        performanceMonitor.recordOperation(`op${i}`, 1000, true);
      }

      // The internal metrics array should be limited
      // This is implementation-dependent, but we can check that it doesn't grow indefinitely
      const stats = performanceMonitor.getAllOperationStats();
      expect(stats.length).toBeLessThan(15000); // Should be limited by cleanup
    });
  });
});