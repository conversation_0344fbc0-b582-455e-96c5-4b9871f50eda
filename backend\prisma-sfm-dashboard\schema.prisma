// Prisma Schema für SFM Dashboard Datenbank
// Gemäß PrismaRegeln.md für separate Datenbanken

generator client {
  provider      = "prisma-client-js"
  output        = "../node_modules/@prisma-sfm-dashboard/client"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider     = "sqlite"
  url          = env("DATABASE_URL_SFM_DASHBOARD")
  relationMode = "prisma"
}

model ARiL {
  id                        Int     @id @default(autoincrement())
  Datum                     String?
  waTaPositionen            Int?
  Umlagerungen              Int?
  belegtePlaetze            Int?
  systemtablareRuecksackStk Int?
  systemtablareGesamtStk    Int?
  systemtablareEinzelBelegt Int?
  belegtRinge               Int?
  Auslastung                Float?
  alleBewegungen            Int?
  cuttingLagerKunde         Int?
  cuttingLagerRest          Int?
  lagerCutting              Int?

  @@index([Datum])
}

model ATrL {
  id                                  Int     @id @default(autoincrement())
  Datum                               String?
  umlagerungen                        Int?
  waTaPositionen                      Int?
  belegtePlaetze                      Int?
  davonSystempaletten                 Int?
  SystempalettenstapelRucksackpaetzen Int?
  SystempalettenstapelEinzel          Int?
  PlaetzeSystempalettenstapelEinzel   Int?
  plaetzeMitTrommelBelegt             Int?
  Auslastung                          Float?
  Bewegungen                          Int?
  EinlagerungAblKunde                 Int?
  EinlagerungAblRest                  Int?
  AuslagerungAbl                      Int?
  weAtrl                              Int?

  @@index([Datum])
}

model Ablaengerei {
  id           Int     @id @default(autoincrement())
  Datum        String?
  cutLagerK220 Int?    @default(0)
  cutLagerR220 Int?    @default(0)
  lagerCut220  Int?    @default(0)
  cutLagerK240 Int?    @default(0)
  cutLagerR240 Int?    @default(0)
  lagerCut240  Int?    @default(0)
  cutTT        Int?    @default(0)
  cutTR        Int?    @default(0)
  cutRR        Int?    @default(0)
  cutGesamt    Int?    @default(0)
  pickCut      Int?    @default(0)
  cutLager200  Int?    @default(0)
  cutLagerK200 Int?    @default(0)
  lagerCut200  Int?    @default(0)

  @@index([Datum])
}

model ManL {
  id                    Int       @id @default(autoincrement())
  Datum                 DateTime?
  Wareneingang          Int?
  cuttingLagerKunde     Int?
  cuttingLagerRest      Int?
  ruecklagerungVonKommi Int?
  waTaPos               Int?
  lagerCutting          Int?
  belegtePlaetze        Int?
  Auslastung            Float?
  alleBewegungen        Int?

  @@index([Datum])
}

model Stoerungen {
<<<<<<< HEAD
  id               Int                   @id @default(autoincrement())
  title            String
  description      String?
  severity         String
  status           String
  category         String?
  affected_system  String?
  location         String?
  reported_by      String?
  assigned_to      String?
  created_at       DateTime              @default(now())
  updated_at       DateTime
  resolved_at      DateTime?
  mttr_minutes     Int?
  tags             String?
  resolution_steps String?
  root_cause       String?
  lessons_learned  String?
  attachments      StoerungsAttachment[]
=======
  id              Int       @id @default(autoincrement())
  title           String
  description     String?
  severity        String
  status          String
  category        String?
  affected_system String?
  location        String?
  reported_by     String?
  assigned_to     String?
  created_at      DateTime  @default(now())
  updated_at      DateTime
  resolved_at     DateTime?
  mttr_minutes    Int?
  tags            String?
>>>>>>> parent of 99336204 (feat: add scrollbar styling and improve UI components)

  @@index([affected_system])
  @@index([severity])
  @@index([status])
  @@index([created_at])
}

model StoerungsComments {
  id           Int      @id @default(autoincrement())
  stoerung_id  Int
  user_id      String?
  comment      String
  created_at   DateTime @default(now())
  caretaker_id String?  @default("3")

  @@index([created_at])
  @@index([stoerung_id])
}

model StoerungsAttachment {
  id          Int        @id @default(autoincrement())
  stoerung_id Int
  filename    String
  stored_name String
  file_path   String
  file_size   Int
  mime_type   String
  file_type   String
  uploaded_by String?
  created_at  DateTime   @default(now())
  updated_at  DateTime   @updatedAt
  stoerung    Stoerungen @relation(fields: [stoerung_id], references: [id], onDelete: Cascade)

  @@index([stoerung_id])
  @@index([file_type])
  @@index([created_at])
}

model System {
  id                                 Int     @id @default(autoincrement())
  Datum                              String?
  nioSapAtrl                         Int?
  nioWitronAtrl                      Int?
  nioSiemensAtrl                     Int?
  nioProzessAtrl                     Int?
  nioSonstigesAtrl                   Int?
  nioSapAril                         Int?
  nioWitronAril                      Int?
  nioSiemensAril                     Int?
  nioProzessAril                     Int?
  nioSonstigesAril                   Int?
  verfuegbarkeitAnzahlStoerungenAtrl Float?
  verfuegbarkeitAnzahlStoerungenAril Float?
  verfuegbarkeitAtrl_FT_RBG_MFR1     Float?
  verfuegbarkeitAril_FT_RBG          Float?
  verfuegbarkeitFTS                  Float?
  gesamtverfuegbarkeit_AtrL_ARiL_FTS Float?
  verfuegbarkeitITM                  Float?
  verfuegbarkeitSAP                  Float?
  verfuegbarkeitServicegrad          Float?
  weGesamtAtrl_Manl                  Int?
  waTaPosGesamt_Atrl_Manl_Aril_Abl   Int?

  @@index([Datum])
}

model SystemStatus {
  id             Int                   @id @default(autoincrement())
  system_name    String
  status         String
  last_check     DateTime              @default(now())
  metadata       String?
  created_at     DateTime              @default(now())
  updated_at     DateTime
  statusMessages SystemStatusMessage[]
}

model WE {
  id     Int     @id @default(autoincrement())
  Datum  String?
  weAtrl Int?    @default(0)
  weManl Int?    @default(0)

  @@index([Datum])
}

model alle_daten {
  id                         Int      @id @default(autoincrement())
  datum                      DateTime
  we_atrl                    Int?
  cutk_lager_atrl            Int?
  cutr_lager_atrl            Int?
  lo_lo_atrl                 Int?
  uml_atrl                   Int?
  wa_pos_atrl                Int?
  lager_cut_atrl             Int?
  plaetze_bel_atrl           Int?
  sysp_atrl                  Int?
  sysp_rucks_atrl            Int?
  sysst_einz_atrl            Int?
  sysp_einz_bel_atrl         Int?
  trom_bel_atrl              Int?
  fuellgrad_atrl             Float?
  all_bew_atrl               Int?
  we_gesamt_atrl             Int?
  we_manl                    Int?
  cut_lager_kunde_manl       Int?
  cut_lager_rest_995_manl    Int?
  lagerol_lagero_312_manl    Int?
  ausl_kommi_manl            Int?
  rueck_kommi_manl           Int?
  wa_pos_601_manl            Int?
  lager_cut_994_manl         Int?
  plaetze_bel_manl           Int?
  fuellgrad_manl             Float?
  all_bew_manl               Int?
  we_manl_2                  Int?
  cut_lager_kunde_996_aril   Int?
  cut_lager_rest_aril        Int?
  wa_pos_601_aril            Int?
  uml_aril                   Int?
  lager_cut_aril             Int?
  plaetze_bel_aril           Int?
  systab_stk_aril            Int?
  systab_rucks_stk_aril      Int?
  systab_einzel_stk_aril     Int?
  systab_belegt_einzel_aril  Int?
  ring_belegt_aril           Int?
  fuellgrad_aril             Float?
  bew_aril                   Int?
  ta_sesamt_abl              Int?
  ta_tt_abl                  Int?
  ta_tr_abl                  Int?
  ta_rr_abl                  Int?
  schnitte_ges_abl           Int?
  cuttopick_abl              Int?
  atrl_sap_ni                Int?
  atrl_witron_nio            Int?
  atrl_siemens_nio           Int?
  atrl_prozess_nio           Int?
  atrl_sonst_nio             Int?
  aril_sap_nio               Int?
  aril_witron_nio            Int?
  aril_siemens_nio           Int?
  aril_nio                   Int?
  aril2_nio                  Int?
  atrl_st_rung_verf          Int?     @map("atrl_störung_verf")
  aril_st_rung_verf          Int?     @map("aril_störung_verf")
  fts_rbg_mfr_st_rung_verf   Int?     @map("fts_rbg_mfr_störung_verf")
  fts_rbg_st_rung_verf       Int?     @map("fts_rbg_störung_verf")
  fts_st_rung_verf           Int?     @map("fts_störung_verf")
  aril_atrl_fts_st_rung_verf Int?     @map("aril_atrl_fts_störung_verf")
  itm_st_rung_verf           Int?     @map("itm_störung_verf")
  sap_st_rung_verf           Int?     @map("sap_störung_verf")
  servicegrad                Float?
  wa_einzel_pos              Int?
  we                         Int?
  wa                         Int?
  wa_lagebest                Int?
  wa_mehr_pos                Int?
  we_ges_manl_atrl           Int?
  wa_ges_abl_manl_atrl_aril  Int?

  @@index([datum])
}

model auslastung200 {
  id               Int       @id @default(autoincrement())
  aufnahmeDatum    String?
  aufnahmeZeit     String?
  maxPlaetze       String?
  auslastung       String?
  maxA             String?
  maxB             String?
  maxC             String?
  auslastungA      String?
  auslastungB      String?
  auslastungC      String?
  import_timestamp DateTime? @default(now())

  @@index([import_timestamp])
  @@index([aufnahmeDatum])
}

model auslastung240 {
  id               Int       @id @default(autoincrement())
  aufnahmeDatum    String?
  aufnahmeZeit     String?
  maxPlaetze       String?
  auslastung       String?
  maxA             String?
  maxB             String?
  maxC             String?
  auslastungA      String?
  auslastungB      String?
  auslastungC      String?
  import_timestamp DateTime? @default(now())

  @@index([import_timestamp])
  @@index([aufnahmeDatum])
}

model bestand200 {
  id                   Int       @id @default(autoincrement())
  Lagertyp             String?
  Lagerplatz           String?
  Material             String?
  Charge               String?
  Dauer                Float?
  Lagerbereich         String?
  Lagerplatztyp        String?
  Lagerplatzaufteilung String?
  Auslagerungssperre   String?
  Einlagerungssperre   String?
  Sperrgrund           String?
  Letzte_Bewegung      String?   @map("Letzte Bewegung")
  Uhrzeit              String?
  TA_Nummer            String?   @map("TA-Nummer")
  TA_Position          String?   @map("TA-Position")
  Letzter_änderer      String?   @map("Letzter Änderer")
  Letzte_änderung      String?   @map("Letzte Änderung")
  Wareneingangsdatum   String?
  WE_Nummer            String?   @map("WE-Nummer")
  WE_Position          String?   @map("WE-Position")
  Lieferung            String?
  Position             String?
  Lagereinheitentyp    String?
  Gesamtbestand        Float?
  Lagereinheit         String?
  aufnahmeDatum        String?
  aufnahmeZeit         String?
  maxPlaetze           String?
  auslastung           String?
  maxA                 String?
  maxB                 String?
  maxC                 String?
  auslastungA          String?
  auslastungB          String?
  auslastungC          String?
  import_timestamp     DateTime? @default(now())

  @@index([import_timestamp])
  @@index([aufnahmeDatum])
}

model bestand240 {
  id                   Int       @id @default(autoincrement())
  Lagertyp             String?
  Lagerplatz           String?
  Material             String?
  Charge               String?
  Dauer                Float?
  Lagerbereich         String?
  Lagerplatztyp        String?
  Lagerplatzaufteilung String?
  Auslagerungssperre   String?
  Einlagerungssperre   String?
  Sperrgrund           String?
  Letzte_Bewegung      String?   @map("Letzte Bewegung")
  Uhrzeit              String?
  TA_Nummer            String?   @map("TA-Nummer")
  TA_Position          String?   @map("TA-Position")
  Letzter__nderer      String?   @map("Letzter Änderer")
  Letzte__nderung      String?   @map("Letzte Änderung")
  Wareneingangsdatum   String?
  WE_Nummer            String?   @map("WE-Nummer")
  WE_Position          String?   @map("WE-Position")
  Lieferung            String?
  Position             String?
  Lagereinheitentyp    String?
  Gesamtbestand        Float?
  Lagereinheit         String?
  aufnahmeDatum        String?
  aufnahmeZeit         String?
  maxPlaetze           String?
  auslastung           String?
  maxA                 String?
  maxB                 String?
  maxC                 String?
  auslastungA          String?
  auslastungB          String?
  auslastungC          String?
  import_timestamp     DateTime? @default(now())

  @@index([import_timestamp])
  @@index([aufnahmeDatum])
}

model bestandRest {
  id                   Int       @id @default(autoincrement())
  Lagertyp             String?
  Lagerplatz           String?
  Material             String?
  Charge               String?
  Dauer                Float?
  Lagerbereich         String?
  Lagerplatztyp        String?
  Lagerplatzaufteilung String?
  Auslagerungssperre   String?
  Einlagerungssperre   String?
  Sperrgrund           String?
  Letzte_Bewegung      String?   @map("Letzte Bewegung")
  Uhrzeit              String?
  TA_Nummer            String?   @map("TA-Nummer")
  TA_Position          String?   @map("TA-Position")
  Letzter__nderer      String?   @map("Letzter Änderer")
  Letzte__nderung      String?   @map("Letzte Änderung")
  Wareneingangsdatum   String?
  WE_Nummer            String?   @map("WE-Nummer")
  WE_Position          String?   @map("WE-Position")
  Lieferung            String?
  Position             String?
  Lagereinheitentyp    String?
  Gesamtbestand        Float?
  Lagereinheit         String?
  aufnahmeDatum        String?
  aufnahmeZeit         String?
  import_timestamp     DateTime? @default(now())

  @@index([import_timestamp])
  @@index([aufnahmeDatum])
}

model DispatchData {
  id                   Int     @id @default(autoincrement())
  datum                String?
  tag                  Int?
  monat                Int?
  kw                   Int?
  jahr                 Int?
  servicegrad          Float?
  ausgeliefert_lup     Int?
  rueckstaendig        Int?
  produzierte_tonnagen Float?
  direktverladung_kiaa Int?
  umschlag             Int?
  kg_pro_colli         Float?
  elefanten            Int?
  atrl                 Int?
  aril                 Int?
  fuellgrad_aril       Float?
  qm_angenommen        Int?
  qm_abgelehnt         Int?
  qm_offen             Int?
  mitarbeiter_std      Float?

  @@index([datum])
  @@map("dispatch_data")
}

model maschinen {
  id             Int     @id @default(autoincrement())
  Machine        String? @unique(map: "sqlite_autoindex_maschinen_1")
  schnitteProStd Float?
}

model schnitte {
  id        Int     @id @default(autoincrement())
  Datum     String?
  M5_R_H1   Int?    @map("M5-R-H1")
  M6_T_H1   Int?    @map("M6-T-H1")
  M7_R_H1   Int?    @map("M7-R-H1")
  M8_T_H1   Int?    @map("M8-T-H1")
  M9_R_H1   Int?    @map("M9-R-H1")
  M10_T_H1  Int?    @map("M10-T-H1")
  M11_R_H1  Int?    @map("M11-R-H1")
  M12_T_H1  Int?    @map("M12-T-H1")
  M13_R_H1  Int?    @map("M13-R-H1")
  M14_T_H1  Int?    @map("M14-T-H1")
  M15_R_H1  Int?    @map("M15-R-H1")
  M16_T_H1  Int?    @map("M16-T-H1")
  M17_R_H1  Int?    @map("M17-R-H1")
  M18_T_H1  Int?    @map("M18-T-H1")
  M19_T_H1  Int?    @map("M19-T-H1")
  M20_T_H1  Int?    @map("M20-T-H1")
  M21_R_H1  Int?    @map("M21-R-H1")
  M23_T_H1  Int?    @map("M23-T-H1")
  M25_RR_H1 Int?    @map("M25-RR-H1")
  M26_T_H1  Int?    @map("M26-T-H1")
  Sum_H1    Int?    @map("Sum-H1")
  M1_T_H3   Int?    @map("M1-T-H3")
  M2_T_H3   Int?    @map("M2-T-H3")
  M3_R_H3   Int?    @map("M3-R-H3")
  M4_T_H3   Int?    @map("M4-T-H3")
  M22_T_H3  Int?    @map("M22-T-H3")
  M24_T_H3  Int?    @map("M24-T-H3")
  M27_R_H3  Int?    @map("M27-R-H3")
  Sum_H3    Int?    @map("Sum-H3")

  @@index([Datum])
}

model User {
  id           Int      @id @default(autoincrement())
  email        String   @unique
  username     String   @unique
  name         String?
  passwordHash String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  roles        Role[]
}

model Role {
  id    Int    @id @default(autoincrement())
  name  String @unique
  users User[]
}

model RoleToUser {
  A Int
  B Int

  @@unique([A, B], map: "_RoleToUser_AB_unique")
  @@index([B], map: "_RoleToUser_B_index")
  @@map("_RoleToUser")
}

model PerformanceMetric {
  id        Int      @id @default(autoincrement())
  metricId  String   @unique
  type      String
  timestamp DateTime @default(now())
  duration  Int
  success   Boolean
  details   String
  createdAt DateTime @default(now())

  @@index([type])
  @@index([timestamp])
  @@index([success])
  @@map("performance_metrics")
}

model QueryPerformanceMetric {
  id         Int      @id @default(autoincrement())
  metricId   String   @unique
  queryType  String
  duration   Int
  success    Boolean
  dataSize   Int?
  cacheHit   Boolean  @default(false)
  retryCount Int      @default(0)
  errorType  String?
  timestamp  DateTime @default(now())

  @@index([queryType])
  @@index([timestamp])
  @@index([cacheHit])
  @@index([success])
  @@map("query_performance_metrics")
}

model IntentRecognitionMetric {
  id              Int      @id @default(autoincrement())
  metricId        String   @unique
  messageHash     String
  detectedIntents String
  confidence      Float
  accuracy        Float?
  keywords        String
  duration        Int
  timestamp       DateTime @default(now())

  @@index([timestamp])
  @@index([confidence])
  @@index([accuracy])
  @@map("intent_recognition_metrics")
}

model EnrichmentPerformanceMetric {
  id                Int      @id @default(autoincrement())
  metricId          String   @unique
  requestId         String
  intentCount       Int
  queryCount        Int
  successfulQueries Int
  fallbackUsed      Boolean  @default(false)
  dataTypes         String
  duration          Int
  success           Boolean
  timestamp         DateTime @default(now())

  @@index([timestamp])
  @@index([success])
  @@index([fallbackUsed])
  @@map("enrichment_performance_metrics")
}

model ResponseTimeMetric {
  id             Int      @id @default(autoincrement())
  metricId       String   @unique
  endpoint       String
  enriched       Boolean
  totalTime      Int
  enrichmentTime Int
  llmTime        Int
  dataSize       Int
  success        Boolean
  timestamp      DateTime @default(now())

  @@index([endpoint])
  @@index([timestamp])
  @@index([enriched])
  @@index([success])
  @@map("response_time_metrics")
}

model PerformanceAlert {
  id         Int       @id @default(autoincrement())
  type       String
  message    String
  metric     String
  value      Float
  threshold  Float
  resolved   Boolean   @default(false)
  resolvedAt DateTime?
  createdAt  DateTime  @default(now())

  @@index([type])
  @@index([resolved])
  @@index([createdAt])
  @@map("performance_alerts")
}

model CacheStatistic {
  id                Int      @id @default(autoincrement())
  totalEntries      Int
  totalSize         Int
  hitRate           Float
  missRate          Float
  evictionCount     Int
  averageAccessTime Float
  timestamp         DateTime @default(now())

  @@index([timestamp])
  @@map("cache_statistics")
}

model WorkflowLog {
  id          String   @id
  timestamp   String
  level       String
  message     String
  workflowId  String   @map("workflow_id")
  executionId String?  @map("execution_id")
  details     String?
  createdAt   DateTime @default(now()) @map("created_at")

  @@index([workflowId])
  @@index([timestamp])
  @@index([level])
  @@index([executionId])
  @@index([createdAt])
  @@index([createdAt], map: "idx_workflow_logs_created_at")
  @@index([executionId], map: "idx_workflow_logs_execution_id")
  @@index([level], map: "idx_workflow_logs_level")
  @@index([timestamp], map: "idx_workflow_logs_timestamp")
  @@index([workflowId], map: "idx_workflow_logs_workflow_id")
  @@map("workflow_logs")
}

model WorkflowExecution {
  id               String    @id
  workflowId       String    @map("workflow_id")
  startTime        DateTime  @map("start_time")
  endTime          DateTime? @map("end_time")
  status           String
  durationSeconds  Int?      @map("duration_seconds")
  exportPath       String?   @map("export_path")
  errorMessage     String?   @map("error_message")
  recordsProcessed Int?      @map("records_processed")
  createdAt        DateTime  @default(now()) @map("created_at")

  @@index([workflowId])
  @@index([startTime])
  @@index([status])
  @@index([createdAt])
  @@index([createdAt], map: "idx_workflow_executions_created_at")
  @@index([status], map: "idx_workflow_executions_status")
  @@index([startTime], map: "idx_workflow_executions_start_time")
  @@index([workflowId], map: "idx_workflow_executions_workflow_id")
  @@map("workflow_executions")
}

model WorkflowConfig {
  id             String    @id
  name           String
  description    String?
  tcode          String?
  exportDir      String?   @map("export_dir")
  exportBasename String?   @map("export_basename")
  dbTable        String?   @map("db_table")
  enabled        Boolean   @default(true)
  scheduleCron   String?   @map("schedule_cron")
  lastRun        DateTime? @map("last_run")
  nextRun        DateTime? @map("next_run")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")

  @@index([enabled])
  @@index([nextRun])
  @@index([lastRun])
  @@index([lastRun], map: "idx_workflow_configs_last_run")
  @@index([nextRun], map: "idx_workflow_configs_next_run")
  @@index([enabled], map: "idx_workflow_configs_enabled")
  @@map("workflow_configs")
}

model BereitschaftsPerson {
  id                     Int                     @id @default(autoincrement())
  name                   String
  telefon                String
  email                  String
  abteilung              String
  aktiv                  Boolean                 @default(true)
  reihenfolge            Int                     @default(0)
  createdAt              DateTime                @default(now()) @map("created_at")
  updatedAt              DateTime                @default(now()) @updatedAt @map("updated_at")
  bereitschaftsWochen    BereitschaftsWoche[]
  bereitschaftsAusnahmen BereitschaftsAusnahme[]

  @@index([reihenfolge], map: "idx_bereitschafts_personen_reihenfolge")
  @@index([aktiv], map: "idx_bereitschafts_personen_aktiv")
  @@map("bereitschafts_personen")
}

model BereitschaftsWoche {
  id          Int                 @id @default(autoincrement())
  personId    Int                 @map("person_id")
  wochenStart DateTime            @map("wochen_start")
  wochenEnde  DateTime            @map("wochen_ende")
  von         DateTime
  bis         DateTime
  aktiv       Boolean             @default(true)
  notiz       String?
  createdAt   DateTime            @default(now()) @map("created_at")
  updatedAt   DateTime            @default(now()) @updatedAt @map("updated_at")
  person      BereitschaftsPerson @relation(fields: [personId], references: [id], onDelete: Cascade)

  @@index([von, bis], map: "idx_bereitschafts_wochen_von_bis")
  @@index([aktiv], map: "idx_bereitschafts_wochen_aktiv")
  @@index([wochenStart], map: "idx_bereitschafts_wochen_wochen_start")
  @@index([personId], map: "idx_bereitschafts_wochen_person_id")
  @@map("bereitschafts_wochen")
}

model BereitschaftsAusnahme {
  id             Int                 @id @default(autoincrement())
  personId       Int                 @map("person_id")
  von            DateTime
  bis            DateTime
  grund          String
  ersatzPersonId Int?                @map("ersatz_person_id")
  aktiv          Boolean             @default(true)
  createdAt      DateTime            @default(now()) @map("created_at")
  updatedAt      DateTime            @default(now()) @updatedAt @map("updated_at")
  person         BereitschaftsPerson @relation(fields: [personId], references: [id], onDelete: Cascade)

  @@index([aktiv], map: "idx_bereitschafts_ausnahmen_aktiv")
  @@index([von, bis], map: "idx_bereitschafts_ausnahmen_von_bis")
  @@index([personId], map: "idx_bereitschafts_ausnahmen_person_id")
  @@map("bereitschafts_ausnahmen")
}

model BereitschaftsKonfiguration {
  id                    Int      @id @default(autoincrement())
  wechsel_tag           Int      @default(5)
  wechselUhrzeit        String   @default("08:00") @map("wechsel_uhrzeit")
  rotationAktiv         Boolean  @default(true) @map("rotation_aktiv")
  benachrichtigungTage  Int      @default(2) @map("benachrichtigung_tage")
  emailBenachrichtigung Boolean  @default(true) @map("email_benachrichtigung")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("bereitschafts_konfiguration")
}

model Materialdaten {
  id                        Int      @id @default(autoincrement())
  matnr                     String   @unique
  materialkurztext          String?
  kabeldurchmesser          Float?
  zuschlagKabeldurchmesser  Float?
  biegefaktor               Float?
  ringauslieferung          Boolean? @default(false)
  kleinsterErlauberFreiraum Float?
  bruttogewicht             Float?
  createdAt                 DateTime @default(now()) @map("created_at")
  updatedAt                 DateTime @updatedAt @map("updated_at")

  @@index([matnr])
  @@map("materialdaten")
}

model Trommeldaten {
  id                 Int      @id @default(autoincrement())
  trommeldaten       String   @unique
  aussendurchmesser  Int?
  kerndurchmesser    Int?
  freiraum_mm        Int?
  wickelbreite_mm    Int?
  maxTragkraft_Kg    Int?
  max_Laenge         Int?
  max_Gewicht        Int?
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  @@index([trommeldaten])
  @@map("trommeldaten")
<<<<<<< HEAD
}

model SystemStatusMessage {
  id               Int           @id @default(autoincrement())
  category         String
  status           String
  title            String
  description      String
  priority         Int           @default(1)
  is_active        Int           @default(1)
  created_at       DateTime      @default(now())
  updated_at       DateTime      @default(now()) @updatedAt
  system_status_id Int?
  systemStatus     SystemStatus? @relation(fields: [system_status_id], references: [id], onDelete: Cascade)

  @@index([system_status_id])
  @@index([category, status])
}

model Runbook {
  id               Int      @id @default(autoincrement())
  title            String
  content          String   // Markdown content
  affected_systems String?  // JSON array as string
  tags             String?  // JSON array as string
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt

  @@index([created_at])
  @@index([updated_at])
}
=======
}
>>>>>>> parent of 99336204 (feat: add scrollbar styling and improve UI components)
