# Berechnung der maximalen Kabellänge

## Verwendung
Das System berechnet aus den Trommeldaten und den vorgegebenen Kabeldaten über die
nachfolgenden mathematischen Formeln die maximale Kabellänge zum Trommeltyp und prüft durch
Vergleich mit der eingegebenen Kabellänge, ob dieser Trommeltyp vorgeschlagen werden kann.

## Funktionsumfang
1. Die Abmessungen der Daten im Trommel- bzw. Kabelmaterialstamm und die zu prüfende
Kabellänge werden vor der Trommelrechnung vom System auf die entsprechende
Recheneinheit umgerechnet (siehe auch Unterschiedliche Maßeinheiten von Kabel- und
Trommeldaten).

2. Das System ermittelt die Hochlagenzahl (HL):

    ```Math
    HL = (Außendurchmesser - Kerndurchmesser - (2 * Freiraum im Kabelstamm)) / (2 *KDMmZ)
    ```

   Der Kabeldurchmesser inklusive Zuschlag (KDMmZ) berechnet sich wie folgt:

    ```Math
    KDMmZ = Kabeldurchmesser * ( 1 + prozentualer Zuschlag Kabeldurchmesser )
    ```
3. Das Ergebnis für die Hochlagenzahl HL wird auf eine ganze Zahl abgerundet.

4. Anschließend errechnet das System den tatsächlichen Trommelfreiraum (Freiraum FR):

    ```Math
    FR = ((Außendurchmesser - Kerndurchmesser) / 2) -HL * KDMmZ
    ```

5. Wenn über die Kabelstammdaten ein Freiraum vergeben wurde, dann überprüft das System,
ob der errechnete Freiraum kleiner als der vorgegebene Freiraum aus den Kabelstammdaten
ist. In diesem Fall wird die in die Formel einlaufende Hochlagenzahl jeweils um eins vermindert 
und so lange erneut der tatsächliche Freiraum errechnet, bis dieser nicht mehr kleiner als der
vorgegebene Freiraum aus dem Materialstamm ist.

6. Wenn über die Kabelstammdaten eine Hochlagenzahl vorgegeben wird, dann überprüft das
System, ob die errechnete (unter Umständen wegen des Freiraums verminderte)
Hochlagenzahl nicht die Hochlagenzahl im Kabelstamm übersteigt. In diesem Fall wird die
Hochlagenzahl laut Kabelstamm weiterverwendet.

7. Mit der folgenden Vergleichsrechnung reduziert das System die Hochlagenzahl nochmals so
lange um eins, bis der Vergleich erfüllt ist:

    ```Math
    HL * KDMmZ * 0,93 < Führungsbogenhöhe + 2 * KDMmZ
    ```

    Der Faktor 0,93 ist ein Defaultwert und berücksichtigt, dass die Kabellagen nicht
    genau übereinander liegen. Sie können diesen Faktor im Customizing der
    Trommelrechnung einstellen.

    Bei der Ermittlung der Trommel geht das System in dieser Vergleichsrechnung
    davon aus, dass der Führungsbogen einer Trommel (falls vorhanden) maximal um
    zwei Hochlagen überwickelt werden darf. Sie können die Anzahl der Hochlagen
    über dem Führungsbogen im Customizing der Trommelrechnung festlegen.

8. Das System errechnet die Ouerlagenzahl (QL):

    ```Math
    QL = Innenbreite der Trommel / KDMmZ
    ```

9. Die Ouerlagenzahl Ol, wird auf eine ganze Zahl abgerundet.

10. Das System errechnet den mittleren Trommelumfang (MU):

    ```Math
    MU = 3,05 * (Kerndurchmesser + HL * KDMmZ)
    ```
	
    Der Faktor 3,05 (= Wildwickelfaktor) ist ein Defaultwert. Der Wildwickelfaktor
    errechnet sich aus der Zahl 0,97 * Pi. Dieser Faktor beruht auf der Empfehlung
    eines wissenschaftlichen Instituts. Sie können den Wildwickelfaktor im Customizing
    der Trommelrechnung einstellen.

11. Der mittlere Trommelumfang MU wird vom System auf eine ganze Zahl gerundet.

12. Mit den so ermittelten Daten errechnet das System mit der folgenden Formel die maximal
mögliche Kabellänge (Kl) für die Trommel:

    ```Math
    KL=HL*QL*MU
    ```

