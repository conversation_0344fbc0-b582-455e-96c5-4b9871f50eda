/**
 * Warehouse Layout Visualization Component
 * 
 * Displays warehouse layout with item placements, accessibility scores,
 * and optimization recommendations.
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
    MapPin, 
    Package, 
    TrendingUp, 
    AlertTriangle, 
    CheckCircle,
    Zap,
    BarChart3,
    Settings
} from 'lucide-react';
import {
    WarehouseItem,
    StorageLocation,
    WarehouseLayoutAnalysis,
    OptimalPlacement,
    LayoutInefficiency
} from '@/types/warehouse-optimization';
import { cn } from '@/lib/utils';

interface WarehouseLayoutVisualizationProps {
    items: WarehouseItem[];
    analysis: WarehouseLayoutAnalysis;
    placements: OptimalPlacement[];
    onItemSelect?: (itemId: string) => void;
    onLocationSelect?: (locationId: string) => void;
    className?: string;
}

/**
 * Warehouse Layout Visualization Component
 */
export function WarehouseLayoutVisualization({
    items,
    analysis,
    placements,
    onItemSelect,
    onLocationSelect,
    className
}: WarehouseLayoutVisualizationProps) {
    const [selectedView, setSelectedView] = useState<'layout' | 'heatmap' | 'inefficiencies'>('layout');
    const [selectedItem, setSelectedItem] = useState<string | null>(null);
    const [selectedZone, setSelectedZone] = useState<string | null>(null);

    // Create warehouse grid layout
    const warehouseGrid = useMemo(() => {
        const zones = ['A', 'B', 'C', 'D'];
        const aisles = Array.from({ length: 8 }, (_, i) => i + 1);
        const shelves = Array.from({ length: 6 }, (_, i) => i + 1);

        return zones.map(zone => ({
            zone,
            aisles: aisles.map(aisle => ({
                aisle,
                shelves: shelves.map(shelf => {
                    const locationId = `${zone}-${aisle}-${shelf}`;
                    const item = items.find(item => item.currentLocation.id === locationId);
                    const placement = placements.find(p => p.itemId === item?.id);
                    const inefficiency = analysis.inefficiencies.find(ineff => 
                        ineff.affectedItems.includes(item?.id || '')
                    );

                    return {
                        locationId,
                        item,
                        placement,
                        inefficiency,
                        accessibilityScore: item?.currentLocation.accessibilityScore || Math.floor(Math.random() * 10) + 1,
                        utilization: item?.currentLocation.currentUtilization || Math.random()
                    };
                })
            }))
        }));
    }, [items, placements, analysis.inefficiencies]);

    // Get color for accessibility score
    const getAccessibilityColor = (score: number) => {
        if (score >= 8) return 'bg-green-500';
        if (score >= 6) return 'bg-yellow-500';
        if (score >= 4) return 'bg-orange-500';
        return 'bg-red-500';
    };

    // Get color for utilization
    const getUtilizationColor = (utilization: number) => {
        if (utilization >= 0.9) return 'bg-red-500';
        if (utilization >= 0.7) return 'bg-yellow-500';
        if (utilization >= 0.5) return 'bg-green-500';
        return 'bg-blue-500';
    };

    // Get inefficiency color
    const getInefficiencyColor = (severity: string) => {
        switch (severity) {
            case 'critical': return 'bg-red-600';
            case 'high': return 'bg-orange-500';
            case 'medium': return 'bg-yellow-500';
            case 'low': return 'bg-blue-500';
            default: return 'bg-gray-400';
        }
    };

    // Handle item click
    const handleItemClick = (item: WarehouseItem | undefined) => {
        if (item) {
            setSelectedItem(item.id);
            onItemSelect?.(item.id);
        }
    };

    // Handle location click
    const handleLocationClick = (locationId: string) => {
        onLocationSelect?.(locationId);
    };

    // Get zone statistics
    const getZoneStats = (zone: string) => {
        const zoneItems = items.filter(item => item.currentLocation.zone === zone);
        const avgAccessibility = zoneItems.reduce((sum, item) => sum + item.currentLocation.accessibilityScore, 0) / zoneItems.length || 0;
        const avgUtilization = zoneItems.reduce((sum, item) => sum + item.currentLocation.currentUtilization, 0) / zoneItems.length || 0;
        const inefficiencies = analysis.inefficiencies.filter(ineff => 
            ineff.affectedItems.some(itemId => zoneItems.some(item => item.id === itemId))
        ).length;

        return {
            itemCount: zoneItems.length,
            avgAccessibility: avgAccessibility.toFixed(1),
            avgUtilization: (avgUtilization * 100).toFixed(1),
            inefficiencies
        };
    };

    return (
        <Card className={cn("bg-secondary-background text-foreground", className)} data-testid="warehouse-layout-visualization">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="text-lg font-semibold flex items-center gap-2">
                        <MapPin className="h-5 w-5 text-[#ff7a05]" />
                        Warehouse Layout Visualisierung
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        <Badge variant="outline">
                            {items.length} Artikel
                        </Badge>
                        <Badge variant="outline">
                            {analysis.utilizationRate.toFixed(1)}% Auslastung
                        </Badge>
                        <Badge variant="outline">
                            {analysis.accessibilityScore.toFixed(1)} Zugänglichkeit
                        </Badge>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <Tabs value={selectedView} onValueChange={(value) => setSelectedView(value as any)}>
                    <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="layout">Layout</TabsTrigger>
                        <TabsTrigger value="heatmap">Heatmap</TabsTrigger>
                        <TabsTrigger value="inefficiencies">Ineffizienzen</TabsTrigger>
                    </TabsList>

                    {/* Layout View */}
                    <TabsContent value="layout" className="space-y-4">
                        <div className="grid grid-cols-4 gap-4">
                            {warehouseGrid.map(zone => (
                                <div key={zone.zone} className="border rounded-lg p-3">
                                    <div className="flex items-center justify-between mb-3">
                                        <h3 className="font-semibold">Zone {zone.zone}</h3>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => setSelectedZone(selectedZone === zone.zone ? null : zone.zone)}
                                        >
                                            <Settings className="h-4 w-4" />
                                        </Button>
                                    </div>
                                    
                                    {selectedZone === zone.zone && (
                                        <div className="mb-3 p-2 bg-muted rounded text-xs">
                                            {(() => {
                                                const stats = getZoneStats(zone.zone);
                                                return (
                                                    <div className="grid grid-cols-2 gap-2">
                                                        <div>Artikel: {stats.itemCount}</div>
                                                        <div>Zugang: {stats.avgAccessibility}</div>
                                                        <div>Auslastung: {stats.avgUtilization}%</div>
                                                        <div>Probleme: {stats.inefficiencies}</div>
                                                    </div>
                                                );
                                            })()}
                                        </div>
                                    )}

                                    <div className="grid grid-cols-6 gap-1">
                                        {zone.aisles.slice(0, 4).map(aisle => 
                                            aisle.shelves.slice(0, 4).map(shelf => (
                                                <div
                                                    key={shelf.locationId}
                                                    className={cn(
                                                        "w-6 h-6 rounded cursor-pointer border-2 transition-all hover:scale-110",
                                                        shelf.item ? "border-gray-600" : "border-gray-300",
                                                        selectedItem === shelf.item?.id ? "ring-2 ring-[#ff7a05]" : "",
                                                        shelf.item ? getAccessibilityColor(shelf.accessibilityScore) : "bg-gray-100"
                                                    )}
                                                    onClick={() => {
                                                        if (shelf.item) {
                                                            handleItemClick(shelf.item);
                                                        } else {
                                                            handleLocationClick(shelf.locationId);
                                                        }
                                                    }}
                                                    title={shelf.item ? 
                                                        `${shelf.item.name} - Zugang: ${shelf.accessibilityScore}/10` : 
                                                        `Leerer Platz: ${shelf.locationId}`
                                                    }
                                                >
                                                    {shelf.placement && (
                                                        <div className="w-full h-full flex items-center justify-center">
                                                            <TrendingUp className="h-3 w-3 text-white" />
                                                        </div>
                                                    )}
                                                    {shelf.inefficiency && (
                                                        <div className="absolute -top-1 -right-1">
                                                            <AlertTriangle className="h-3 w-3 text-red-500" />
                                                        </div>
                                                    )}
                                                </div>
                                            ))
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Legend */}
                        <div className="flex flex-wrap gap-4 text-sm">
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-green-500 rounded"></div>
                                <span>Hohe Zugänglichkeit (8-10)</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                                <span>Mittlere Zugänglichkeit (6-7)</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-orange-500 rounded"></div>
                                <span>Niedrige Zugänglichkeit (4-5)</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-red-500 rounded"></div>
                                <span>Sehr niedrige Zugänglichkeit (1-3)</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <TrendingUp className="h-4 w-4 text-white bg-gray-600 rounded p-0.5" />
                                <span>Optimierungsempfehlung</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <AlertTriangle className="h-4 w-4 text-red-500" />
                                <span>Ineffizienz</span>
                            </div>
                        </div>
                    </TabsContent>

                    {/* Heatmap View */}
                    <TabsContent value="heatmap" className="space-y-4">
                        <div className="grid grid-cols-4 gap-4">
                            {warehouseGrid.map(zone => (
                                <div key={zone.zone} className="border rounded-lg p-3">
                                    <h3 className="font-semibold mb-3">Zone {zone.zone} - Auslastung</h3>
                                    <div className="grid grid-cols-6 gap-1">
                                        {zone.aisles.slice(0, 4).map(aisle => 
                                            aisle.shelves.slice(0, 4).map(shelf => (
                                                <div
                                                    key={shelf.locationId}
                                                    className={cn(
                                                        "w-6 h-6 rounded cursor-pointer border transition-all hover:scale-110",
                                                        getUtilizationColor(shelf.utilization)
                                                    )}
                                                    onClick={() => handleLocationClick(shelf.locationId)}
                                                    title={`Auslastung: ${(shelf.utilization * 100).toFixed(1)}%`}
                                                />
                                            ))
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Heatmap Legend */}
                        <div className="flex flex-wrap gap-4 text-sm">
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-red-500 rounded"></div>
                                <span>Überauslastung (90-100%)</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                                <span>Hohe Auslastung (70-89%)</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-green-500 rounded"></div>
                                <span>Optimale Auslastung (50-69%)</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-4 bg-blue-500 rounded"></div>
                                <span>Niedrige Auslastung (0-49%)</span>
                            </div>
                        </div>
                    </TabsContent>

                    {/* Inefficiencies View */}
                    <TabsContent value="inefficiencies" className="space-y-4">
                        <div className="space-y-3">
                            {analysis.inefficiencies.length === 0 ? (
                                <div className="flex items-center justify-center h-32">
                                    <div className="text-center">
                                        <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
                                        <p className="text-muted-foreground">
                                            Keine Ineffizienzen erkannt
                                        </p>
                                    </div>
                                </div>
                            ) : (
                                analysis.inefficiencies.map((inefficiency, index) => (
                                    <div
                                        key={index}
                                        className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                                    >
                                        <div className="flex items-center justify-between mb-2">
                                            <div className="flex items-center gap-2">
                                                <AlertTriangle className="h-4 w-4 text-orange-500" />
                                                <span className="font-medium">
                                                    {inefficiency.type === 'misplaced_high_frequency' ? 'Falsch platzierte häufige Artikel' :
                                                     inefficiency.type === 'poor_accessibility' ? 'Schlechte Zugänglichkeit' :
                                                     inefficiency.type === 'suboptimal_grouping' ? 'Suboptimale Gruppierung' :
                                                     'Untergenutzte Fläche'}
                                                </span>
                                            </div>
                                            <Badge className={getInefficiencyColor(inefficiency.severity)}>
                                                {inefficiency.severity === 'critical' ? 'Kritisch' :
                                                 inefficiency.severity === 'high' ? 'Hoch' :
                                                 inefficiency.severity === 'medium' ? 'Mittel' : 'Niedrig'}
                                            </Badge>
                                        </div>
                                        
                                        <p className="text-sm text-muted-foreground mb-3">
                                            {inefficiency.description}
                                        </p>
                                        
                                        <div className="grid grid-cols-3 gap-4 text-sm">
                                            <div>
                                                <span className="text-muted-foreground">Betroffene Artikel:</span>
                                                <p className="font-medium">{inefficiency.affectedItems.length}</p>
                                            </div>
                                            <div>
                                                <span className="text-muted-foreground">Zeitverlust/Tag:</span>
                                                <p className="font-medium">{inefficiency.estimatedImpact.toFixed(1)} Min</p>
                                            </div>
                                            <div>
                                                <span className="text-muted-foreground">Schweregrad:</span>
                                                <p className="font-medium capitalize">{inefficiency.severity}</p>
                                            </div>
                                        </div>
                                        
                                        <div className="mt-3 flex flex-wrap gap-1">
                                            {inefficiency.affectedItems.slice(0, 5).map(itemId => {
                                                const item = items.find(i => i.id === itemId);
                                                return item ? (
                                                    <Badge
                                                        key={itemId}
                                                        variant="secondary"
                                                        className="text-xs cursor-pointer"
                                                        onClick={() => handleItemClick(item)}
                                                    >
                                                        {item.name.length > 15 ? item.name.substring(0, 15) + '...' : item.name}
                                                    </Badge>
                                                ) : null;
                                            })}
                                            {inefficiency.affectedItems.length > 5 && (
                                                <Badge variant="secondary" className="text-xs">
                                                    +{inefficiency.affectedItems.length - 5} weitere
                                                </Badge>
                                            )}
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    </TabsContent>
                </Tabs>

                {/* Selected Item Details */}
                {selectedItem && (
                    <div className="mt-4 p-3 bg-muted rounded-lg">
                        {(() => {
                            const item = items.find(i => i.id === selectedItem);
                            const placement = placements.find(p => p.itemId === selectedItem);
                            
                            if (!item) return null;
                            
                            return (
                                <div>
                                    <h4 className="font-semibold mb-2 flex items-center gap-2">
                                        <Package className="h-4 w-4" />
                                        {item.name}
                                    </h4>
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <span className="text-muted-foreground">Position:</span>
                                            <p className="font-medium">{item.currentLocation.zone}-{item.currentLocation.aisle}-{item.currentLocation.shelf}</p>
                                        </div>
                                        <div>
                                            <span className="text-muted-foreground">Zugänglichkeit:</span>
                                            <p className="font-medium">{item.currentLocation.accessibilityScore}/10</p>
                                        </div>
                                        <div>
                                            <span className="text-muted-foreground">Zugriffe/Tag:</span>
                                            <p className="font-medium">{item.accessFrequency}</p>
                                        </div>
                                        <div>
                                            <span className="text-muted-foreground">Kategorie:</span>
                                            <p className="font-medium">{item.category}</p>
                                        </div>
                                    </div>
                                    
                                    {placement && (
                                        <div className="mt-3 p-2 bg-blue-100 rounded">
                                            <p className="text-sm font-medium text-blue-800 mb-1">
                                                <TrendingUp className="h-4 w-4 inline mr-1" />
                                                Optimierungsempfehlung
                                            </p>
                                            <p className="text-sm text-blue-700">
                                                {placement.reason}
                                            </p>
                                            <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                                                <div>
                                                    <span className="text-blue-600">Neue Position:</span>
                                                    <p className="font-medium">{placement.recommendedLocation.zone}-{placement.recommendedLocation.aisle}-{placement.recommendedLocation.shelf}</p>
                                                </div>
                                                <div>
                                                    <span className="text-blue-600">Zeitersparnis:</span>
                                                    <p className="font-medium">{placement.expectedBenefit.timeSavingsPerDay.toFixed(1)} Min/Tag</p>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            );
                        })()}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}

export default WarehouseLayoutVisualization;