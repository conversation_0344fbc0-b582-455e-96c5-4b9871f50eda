export interface ModuleCard {
  id: string;
  title: string;
  description?: string;
  route: string;
  icon: string;
  color?: string;
  isAccessible?: boolean;
  inDevelopment?: boolean;
}

export interface ModuleSecurityConfig {
  requiresAuthentication: boolean;
  enableInputValidation?: boolean;
  enableAuditLogging?: boolean;
  rateLimiting?: {
    enabled: boolean;
    defaultLimit: number;
    windowMs: number;
  };
  apiKeyValidation?: {
    enabled: boolean;
    requiredServices: string[];
  };
}

export interface ModuleConfig {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  icon: string;
  baseRoute: string;
  requiredRoles: UserRole[];
  isEnabled: boolean;
  pages: ModulePage[];
  security?: ModuleSecurityConfig;
}

export interface ModulePage {
  id: string;
  name: string;
  route: string;
  component: string;
  requiredRoles?: UserRole[];
}

export type UserRole = 'Administrator' | 'Benutzer' | 'Besucher';