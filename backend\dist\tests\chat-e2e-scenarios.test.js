"use strict";
/**
 * End-to-End Scenario Tests for AI Chatbot Database Integration
 *
 * Real-world scenario tests that simulate actual user interactions:
 * - Complete user conversation flows
 * - Complex multi-turn conversations
 * - Real-world query patterns
 * - Integration with actual data patterns
 *
 * Requirements: 1.1, 1.2, 1.3, 2.1, 3.1, 4.1, 5.1
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const chat_routes_1 = __importDefault(require("../routes/chat.routes"));
const openrouter_service_1 = __importDefault(require("../services/openrouter.service"));
// Mock external dependencies
jest.mock('../services/openrouter.service');
jest.mock('@prisma/client');
describe('End-to-End Chat Scenario Tests', () => {
    let app;
    let mockOpenRouterService;
    beforeEach(() => {
        jest.clearAllMocks();
        // Setup Express app
        app = (0, express_1.default)();
        app.use(express_1.default.json());
        app.use('/api/chat', chat_routes_1.default);
        // Setup mocks
        mockOpenRouterService = openrouter_service_1.default;
    });
    describe('Daily Operations Scenarios', () => {
        it('should handle morning system status check scenario', async () => {
            // Simulate a typical morning check by operations team
            const scenarios = [
                {
                    query: 'Guten Morgen! Wie ist der aktuelle Systemstatus?',
                    expectedDataTypes: ['stoerungen'],
                    expectedContext: true
                },
                {
                    query: 'Gibt es kritische Störungen von gestern Nacht?',
                    expectedDataTypes: ['stoerungen'],
                    expectedContext: true
                },
                {
                    query: 'Wie ist die Versand-Performance heute Morgen?',
                    expectedDataTypes: ['dispatch'],
                    expectedContext: true
                }
            ];
            for (const scenario of scenarios) {
                // Mock appropriate response for each scenario
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: `Antwort auf: ${scenario.query}`,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: false,
                    hasAnomalies: false,
                    hasEnrichedContext: scenario.expectedContext,
                    dataTypes: scenario.expectedDataTypes
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: scenario.query
                })
                    .expect(200);
                expect(response.body.hasEnrichedContext).toBe(scenario.expectedContext);
                if (scenario.expectedContext) {
                    expect(response.body.dataTypes).toEqual(expect.arrayContaining(scenario.expectedDataTypes));
                }
            }
        });
        it('should handle incident investigation scenario', async () => {
            // Simulate investigating a specific incident
            const investigationFlow = [
                {
                    query: 'Zeige mir alle kritischen Störungen der letzten 24 Stunden',
                    response: 'Es gibt 2 kritische Störungen: Server-Ausfall um 14:30 und Netzwerk-Problem um 16:45',
                    dataTypes: ['stoerungen']
                },
                {
                    query: 'Gib mir Details zum Server-Ausfall um 14:30',
                    response: 'Server-Ausfall: Betroffen waren 3 Services, MTTR 45 Minuten, Status: Behoben',
                    dataTypes: ['stoerungen']
                },
                {
                    query: 'Hat der Server-Ausfall die Versand-Performance beeinflusst?',
                    response: 'Ja, Service Level fiel von 98% auf 92% zwischen 14:30-15:15',
                    dataTypes: ['stoerungen', 'dispatch']
                }
            ];
            for (const step of investigationFlow) {
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: step.response,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: false,
                    hasAnomalies: false,
                    hasEnrichedContext: true,
                    dataTypes: step.dataTypes
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: step.query
                })
                    .expect(200);
                expect(response.body.response).toBe(step.response);
                expect(response.body.hasEnrichedContext).toBe(true);
                expect(response.body.dataTypes).toEqual(step.dataTypes);
            }
        });
        it('should handle performance analysis scenario', async () => {
            // Simulate weekly performance review
            const performanceQueries = [
                {
                    query: 'Wie war unsere Performance diese Woche im Vergleich zur letzten?',
                    expectedDataTypes: ['dispatch', 'cutting', 'stoerungen'],
                    includeInsights: true
                },
                {
                    query: 'Welche Bereiche zeigen Verbesserungspotential?',
                    expectedDataTypes: ['dispatch', 'cutting'],
                    includeInsights: true,
                    includeAnomalies: true
                },
                {
                    query: 'Gibt es Anomalien in den Maschinen-Daten?',
                    expectedDataTypes: ['cutting'],
                    includeAnomalies: true
                }
            ];
            for (const query of performanceQueries) {
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: `Analyse für: ${query.query}`,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: query.includeInsights || false,
                    hasAnomalies: query.includeAnomalies || false,
                    hasEnrichedContext: true,
                    dataTypes: query.expectedDataTypes
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: query.query,
                    includeInsights: query.includeInsights,
                    includeAnomalies: query.includeAnomalies
                })
                    .expect(200);
                expect(response.body.hasEnrichedContext).toBe(true);
                expect(response.body.hasInsights).toBe(query.includeInsights || false);
                expect(response.body.hasAnomalies).toBe(query.includeAnomalies || false);
                expect(response.body.dataTypes).toEqual(expect.arrayContaining(query.expectedDataTypes));
            }
        });
    });
    describe('Department-Specific Workflows', () => {
        it('should handle Störungen department workflow', async () => {
            const stoerungenWorkflow = [
                'Wie viele offene Störungen haben wir?',
                'Welche Störungen haben die höchste Priorität?',
                'Wie ist unser MTTR diese Woche?',
                'Gibt es wiederkehrende Probleme?',
                'Welche Systeme sind am häufigsten betroffen?'
            ];
            for (const query of stoerungenWorkflow) {
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: `Störungen-Antwort: ${query}`,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: false,
                    hasAnomalies: false,
                    hasEnrichedContext: true,
                    dataTypes: ['stoerungen']
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({ message: query })
                    .expect(200);
                expect(response.body.hasEnrichedContext).toBe(true);
                expect(response.body.dataTypes).toContain('stoerungen');
            }
        });
        it('should handle Dispatch department workflow', async () => {
            const dispatchWorkflow = [
                'Wie ist unser Service Level heute?',
                'Zeige mir die Picking-Performance',
                'Wie viel Tonnage haben wir diese Woche versendet?',
                'Wie sind die QM-Zahlen?',
                'Gibt es Probleme bei den Retouren?'
            ];
            for (const query of dispatchWorkflow) {
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: `Versand-Antwort: ${query}`,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: false,
                    hasAnomalies: false,
                    hasEnrichedContext: true,
                    dataTypes: ['dispatch']
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({ message: query })
                    .expect(200);
                expect(response.body.hasEnrichedContext).toBe(true);
                expect(response.body.dataTypes).toContain('dispatch');
            }
        });
        it('should handle Cutting department workflow', async () => {
            const cuttingWorkflow = [
                'Welche Maschinen laufen am effizientesten?',
                'Wie ist die Schnitt-Performance heute?',
                'Gibt es Probleme mit bestimmten Maschinen?',
                'Wie sind die Lager-Schnitt-Zahlen?',
                'Welche Maschine hat die beste Auslastung?'
            ];
            for (const query of cuttingWorkflow) {
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: `Ablängerei-Antwort: ${query}`,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: false,
                    hasAnomalies: false,
                    hasEnrichedContext: true,
                    dataTypes: ['cutting']
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({ message: query })
                    .expect(200);
                expect(response.body.hasEnrichedContext).toBe(true);
                expect(response.body.dataTypes).toContain('cutting');
            }
        });
    });
    describe('Complex Multi-Domain Scenarios', () => {
        it('should handle cross-department impact analysis', async () => {
            const impactAnalysis = [
                {
                    query: 'Wie wirken sich Störungen auf die Versand-Performance aus?',
                    expectedDataTypes: ['stoerungen', 'dispatch'],
                    includeInsights: true
                },
                {
                    query: 'Beeinflussen Maschinen-Probleme unsere Lieferzeiten?',
                    expectedDataTypes: ['cutting', 'dispatch'],
                    includeInsights: true
                },
                {
                    query: 'Gibt es Korrelationen zwischen System-Ausfällen und Produktions-Effizienz?',
                    expectedDataTypes: ['stoerungen', 'cutting', 'dispatch'],
                    includeInsights: true,
                    includeAnomalies: true
                }
            ];
            for (const analysis of impactAnalysis) {
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: `Korrelations-Analyse: ${analysis.query}`,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: analysis.includeInsights,
                    hasAnomalies: analysis.includeAnomalies || false,
                    hasEnrichedContext: true,
                    dataTypes: analysis.expectedDataTypes
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: analysis.query,
                    includeInsights: analysis.includeInsights,
                    includeAnomalies: analysis.includeAnomalies
                })
                    .expect(200);
                expect(response.body.hasEnrichedContext).toBe(true);
                expect(response.body.dataTypes).toEqual(expect.arrayContaining(analysis.expectedDataTypes));
                expect(response.body.hasInsights).toBe(analysis.includeInsights);
            }
        });
        it('should handle executive dashboard queries', async () => {
            const executiveQueries = [
                'Gib mir eine Zusammenfassung aller KPIs',
                'Wie ist die Gesamtperformance des Unternehmens?',
                'Welche Bereiche brauchen sofortige Aufmerksamkeit?',
                'Zeige mir die wichtigsten Trends der letzten Woche'
            ];
            for (const query of executiveQueries) {
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: `Executive Summary: ${query}`,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: true,
                    hasAnomalies: true,
                    hasEnrichedContext: true,
                    dataTypes: ['stoerungen', 'dispatch', 'cutting']
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: query,
                    includeInsights: true,
                    includeAnomalies: true
                })
                    .expect(200);
                expect(response.body.hasEnrichedContext).toBe(true);
                expect(response.body.dataTypes).toEqual(expect.arrayContaining(['stoerungen', 'dispatch', 'cutting']));
                expect(response.body.hasInsights).toBe(true);
                expect(response.body.hasAnomalies).toBe(true);
            }
        });
    });
    describe('Error Recovery Scenarios', () => {
        it('should handle partial system failures gracefully', async () => {
            // Simulate scenario where some repositories are unavailable
            mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                response: 'Teilweise Daten verfügbar: Störungen OK, Versand nicht erreichbar',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: false,
                hasAnomalies: false,
                hasEnrichedContext: true,
                dataTypes: ['stoerungen'] // Only partial data available
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Vollständiger Systemstatus aller Bereiche'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(true);
            expect(response.body.dataQuality.hasPartialData).toBe(true);
            expect(response.body.dataQuality.hasFullData).toBe(false);
            expect(response.body.dataTypes).toEqual(['stoerungen']);
        });
        it('should handle complete data enrichment failure', async () => {
            // Mock complete failure of data enrichment
            mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                response: 'Entschuldigung, ich kann derzeit nicht auf die Systemdaten zugreifen.',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: false,
                hasAnomalies: false,
                hasEnrichedContext: false,
                dataTypes: []
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Aktuelle Störungen anzeigen'
            })
                .expect(200);
            expect(response.body.dataEnrichmentError).toBe(true);
            expect(response.body.dataEnrichmentUsed).toBe(false);
            expect(response.body.enrichmentDetails.fallbackUsed).toBe(true);
        });
        it('should provide helpful suggestions when no data found', async () => {
            mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                response: 'Keine relevanten Daten gefunden. Versuchen Sie spezifischere Begriffe wie "Störungen", "Versand" oder "Maschinen".',
                timestamp: new Date().toISOString(),
                model: '@preset/lapp',
                hasInsights: false,
                hasAnomalies: false,
                hasEnrichedContext: false,
                dataTypes: []
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'Wie ist das Wetter heute?'
            })
                .expect(200);
            expect(response.body.hasEnrichedContext).toBe(false);
            expect(response.body.dataEnrichmentUsed).toBe(false);
            expect(response.body.response).toContain('Keine relevanten Daten');
        });
    });
    describe('Time-Sensitive Scenarios', () => {
        it('should handle urgent incident response scenario', async () => {
            const urgentScenario = [
                {
                    query: 'URGENT: Kompletter Systemausfall gemeldet!',
                    priority: 'CRITICAL'
                },
                {
                    query: 'Welche Systeme sind betroffen?',
                    priority: 'HIGH'
                },
                {
                    query: 'Wie lange dauert die Wiederherstellung?',
                    priority: 'HIGH'
                },
                {
                    query: 'Welche Services sind noch verfügbar?',
                    priority: 'MEDIUM'
                }
            ];
            for (const step of urgentScenario) {
                const startTime = Date.now();
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: `URGENT RESPONSE: ${step.query}`,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: false,
                    hasAnomalies: false,
                    hasEnrichedContext: true,
                    dataTypes: ['stoerungen']
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: step.query
                })
                    .expect(200);
                const responseTime = Date.now() - startTime;
                // Urgent queries should be handled quickly
                expect(responseTime).toBeLessThan(2000);
                expect(response.body.hasEnrichedContext).toBe(true);
                expect(response.body.dataTypes).toContain('stoerungen');
            }
        });
        it('should handle shift handover scenario', async () => {
            const handoverQueries = [
                'Zusammenfassung der letzten 8 Stunden',
                'Welche Probleme sind noch offen?',
                'Gibt es besondere Aufmerksamkeitspunkte für die nächste Schicht?',
                'Wie sind die aktuellen Performance-Werte?'
            ];
            for (const query of handoverQueries) {
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: `Schichtübergabe: ${query}`,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: true,
                    hasAnomalies: false,
                    hasEnrichedContext: true,
                    dataTypes: ['stoerungen', 'dispatch', 'cutting']
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: query,
                    includeInsights: true
                })
                    .expect(200);
                expect(response.body.hasEnrichedContext).toBe(true);
                expect(response.body.hasInsights).toBe(true);
                expect(response.body.dataTypes.length).toBeGreaterThan(0);
            }
        });
    });
    describe('User Experience Scenarios', () => {
        it('should handle conversational follow-up questions', async () => {
            // Simulate natural conversation flow
            const conversation = [
                {
                    query: 'Wie viele Störungen haben wir heute?',
                    response: 'Heute haben wir 5 Störungen: 2 kritische, 3 mittlere Priorität.'
                },
                {
                    query: 'Erzähl mir mehr über die kritischen',
                    response: 'Die kritischen Störungen betreffen den Hauptserver (seit 10:30) und das Netzwerk (seit 14:15).'
                },
                {
                    query: 'Wann werden sie voraussichtlich behoben?',
                    response: 'Server: ETA 16:00, Netzwerk: ETA 15:30 basierend auf historischen MTTR-Daten.'
                }
            ];
            for (const turn of conversation) {
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: turn.response,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: false,
                    hasAnomalies: false,
                    hasEnrichedContext: true,
                    dataTypes: ['stoerungen']
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: turn.query
                })
                    .expect(200);
                expect(response.body.response).toBe(turn.response);
                expect(response.body.hasEnrichedContext).toBe(true);
            }
        });
        it('should handle mixed language queries', async () => {
            const mixedQueries = [
                'Show me current incidents', // English
                'Zeige mir aktuelle Störungen', // German
                'Was ist der Service Level?', // German
                'Machine efficiency status?' // English
            ];
            for (const query of mixedQueries) {
                mockOpenRouterService.generateResponse.mockResolvedValueOnce({
                    response: `Antwort auf: ${query}`,
                    timestamp: new Date().toISOString(),
                    model: '@preset/lapp',
                    hasInsights: false,
                    hasAnomalies: false,
                    hasEnrichedContext: true,
                    dataTypes: query.includes('incident') || query.includes('Störungen') ? ['stoerungen'] :
                        query.includes('Service') ? ['dispatch'] :
                            query.includes('Machine') ? ['cutting'] : ['stoerungen']
                });
                const response = await (0, supertest_1.default)(app)
                    .post('/api/chat/enhanced')
                    .send({
                    message: query
                })
                    .expect(200);
                expect(response.body.hasEnrichedContext).toBe(true);
                expect(response.body.dataTypes.length).toBeGreaterThan(0);
            }
        });
    });
});
