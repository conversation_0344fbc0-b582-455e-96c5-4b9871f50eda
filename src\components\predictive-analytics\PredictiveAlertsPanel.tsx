/**
 * Predictive Alerts Panel Component
 * 
 * Displays predictive alerts with time-to-impact and probability indicators.
 */

import React, { useState } from 'react';
import { Clock, TrendingUp, AlertTriangle, Eye, X, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import type { PredictiveAlert } from '@/types/predictive-analytics';
import { cn } from '@/lib/utils';

interface PredictiveAlertsPanelProps {
  alerts: PredictiveAlert[];
  loading: boolean;
  onAlertAction: (alertId: string, action: 'acknowledge' | 'dismiss' | 'snooze') => void;
  className?: string;
}

/**
 * Predictive Alerts Panel Component
 */
export function PredictiveAlertsPanel({ 
  alerts, 
  loading, 
  onAlertAction, 
  className 
}: PredictiveAlertsPanelProps) {
  const [selectedAlert, setSelectedAlert] = useState<PredictiveAlert | null>(null);
  const [filterProbability, setFilterProbability] = useState<number>(0);

  // Filter alerts by probability threshold
  const filteredAlerts = alerts.filter(alert => 
    alert.probability >= filterProbability / 100
  );

  // Sort alerts by severity, probability, and time to impact
  const sortedAlerts = [...filteredAlerts].sort((a, b) => {
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
    if (severityDiff !== 0) return severityDiff;
    
    const probabilityDiff = b.probability - a.probability;
    if (probabilityDiff !== 0) return probabilityDiff;
    
    return a.time_to_impact_hours - b.time_to_impact_hours;
  });

  // Get severity color and icon
  const getSeverityDisplay = (severity: PredictiveAlert['severity']) => {
    switch (severity) {
      case 'critical':
        return {
          color: 'bg-red-500 text-white',
          icon: <AlertTriangle className="h-3 w-3" />,
          label: 'Kritisch'
        };
      case 'high':
        return {
          color: 'bg-orange-500 text-white',
          icon: <AlertTriangle className="h-3 w-3" />,
          label: 'Hoch'
        };
      case 'medium':
        return {
          color: 'bg-yellow-500 text-black',
          icon: <Clock className="h-3 w-3" />,
          label: 'Mittel'
        };
      case 'low':
        return {
          color: 'bg-blue-500 text-white',
          icon: <Clock className="h-3 w-3" />,
          label: 'Niedrig'
        };
    }
  };

  // Get probability color
  const getProbabilityColor = (probability: number) => {
    if (probability >= 0.8) return 'text-red-600';
    if (probability >= 0.6) return 'text-orange-600';
    if (probability >= 0.4) return 'text-yellow-600';
    return 'text-blue-600';
  };

  // Get time to impact display
  const getTimeToImpactDisplay = (hours: number) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)} Min`;
    } else if (hours < 24) {
      return `${Math.round(hours)} Std`;
    } else {
      return `${Math.round(hours / 24)} Tage`;
    }
  };

  // Get urgency color based on time to impact
  const getUrgencyColor = (hours: number) => {
    if (hours <= 1) return 'bg-red-100 text-red-800';
    if (hours <= 6) return 'bg-orange-100 text-orange-800';
    if (hours <= 24) return 'bg-yellow-100 text-yellow-800';
    return 'bg-blue-100 text-blue-800';
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle alert action
  const handleAlertAction = (alert: PredictiveAlert, action: 'acknowledge' | 'dismiss' | 'snooze') => {
    onAlertAction(alert.id, action);
    setSelectedAlert(null);
  };

  if (loading) {
    return (
      <Card className={cn("h-96", className)}>
        <CardHeader>
          <Skeleton className="h-6 w-40" />
        </CardHeader>
        <CardContent className="space-y-3">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="flex items-center space-x-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-3 w-2/3" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("h-96", className)} data-testid="predictive-alerts-panel">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">
            Prognose-Alarme ({sortedAlerts.length})
          </CardTitle>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 text-sm">
              <span>Min. Wahrscheinlichkeit:</span>
              <input
                type="range"
                min="0"
                max="100"
                step="10"
                value={filterProbability}
                onChange={(e) => setFilterProbability(Number(e.target.value))}
                className="w-16"
              />
              <span className="text-xs">{filterProbability}%</span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-80">
          {sortedAlerts.length === 0 ? (
            <div className="flex items-center justify-center h-full p-6">
              <div className="text-center">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
                <p className="text-muted-foreground">
                  Keine Prognose-Alarme
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Alle KPIs im normalen Bereich
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-2 p-4">
              {sortedAlerts.map((alert) => {
                const severityDisplay = getSeverityDisplay(alert.severity);
                const probabilityColor = getProbabilityColor(alert.probability);
                const urgencyColor = getUrgencyColor(alert.time_to_impact_hours);
                
                return (
                  <div
                    key={alert.id}
                    className="border rounded-lg p-3 hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge className={severityDisplay.color}>
                            {severityDisplay.icon}
                            <span className="ml-1">{severityDisplay.label}</span>
                          </Badge>
                          <Badge variant="outline" className={urgencyColor}>
                            <Clock className="h-3 w-3 mr-1" />
                            {getTimeToImpactDisplay(alert.time_to_impact_hours)}
                          </Badge>
                        </div>
                        
                        <h4 className="font-medium text-sm mb-1 truncate">
                          {alert.predicted_issue}
                        </h4>
                        
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-xs text-muted-foreground">Wahrscheinlichkeit:</span>
                          <div className="flex items-center gap-1">
                            <Progress 
                              value={alert.probability * 100} 
                              className="w-16 h-2" 
                            />
                            <span className={cn("text-xs font-medium", probabilityColor)}>
                              {Math.round(alert.probability * 100)}%
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>KPI: {alert.kpi_id}</span>
                          <span>Aktuell: {alert.current_value?.toFixed(2) || 'N/A'}</span>
                          <span>Prognose: {alert.predicted_value?.toFixed(2) || 'N/A'}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1 ml-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedAlert(alert)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Prognose-Alarm Details</DialogTitle>
                            </DialogHeader>
                            {selectedAlert && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium">Prognostiziertes Problem</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedAlert.predicted_issue}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Schweregrad</label>
                                    <p className="text-sm text-muted-foreground">
                                      {getSeverityDisplay(selectedAlert.severity).label}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">KPI</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedAlert.kpi_id}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Wahrscheinlichkeit</label>
                                    <p className="text-sm text-muted-foreground">
                                      {Math.round(selectedAlert.probability * 100)}%
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Zeit bis Auswirkung</label>
                                    <p className="text-sm text-muted-foreground">
                                      {getTimeToImpactDisplay(selectedAlert.time_to_impact_hours)}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Alarm-Typ</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedAlert.alert_type === 'threshold_breach' ? 'Schwellwert-Verletzung' :
                                       selectedAlert.alert_type === 'anomaly_prediction' ? 'Anomalie-Prognose' :
                                       'Kapazitätsgrenze'}
                                    </p>
                                  </div>
                                </div>
                                
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium">Aktueller Wert</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedAlert.current_value?.toFixed(2) || 'N/A'}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Prognostizierter Wert</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedAlert.predicted_value?.toFixed(2) || 'N/A'}
                                    </p>
                                  </div>
                                </div>
                                
                                {selectedAlert.recommendations.length > 0 && (
                                  <div>
                                    <label className="text-sm font-medium">Empfehlungen</label>
                                    <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                                      {selectedAlert.recommendations.map((rec, index) => (
                                        <li key={index} className="flex items-start gap-2">
                                          <span className="text-blue-500 mt-1">•</span>
                                          <span>{rec}</span>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                                
                                <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                                  <div>
                                    <label className="font-medium">Generiert am</label>
                                    <p>{formatTimestamp(selectedAlert.generated_at)}</p>
                                  </div>
                                  <div>
                                    <label className="font-medium">Läuft ab am</label>
                                    <p>{formatTimestamp(selectedAlert.expires_at)}</p>
                                  </div>
                                </div>
                                
                                {selectedAlert.is_active && (
                                  <div className="flex gap-2 pt-4 border-t">
                                    <Button
                                      onClick={() => handleAlertAction(selectedAlert, 'acknowledge')}
                                      variant="outline"
                                      size="sm"
                                    >
                                      Bestätigen
                                    </Button>
                                    <Button
                                      onClick={() => handleAlertAction(selectedAlert, 'snooze')}
                                      variant="outline"
                                      size="sm"
                                    >
                                      Schlummern
                                    </Button>
                                    <Button
                                      onClick={() => handleAlertAction(selectedAlert, 'dismiss')}
                                      variant="destructive"
                                      size="sm"
                                    >
                                      Verwerfen
                                    </Button>
                                  </div>
                                )}
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                        
                        {alert.is_active && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAlertAction(alert, 'dismiss')}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

export default PredictiveAlertsPanel;