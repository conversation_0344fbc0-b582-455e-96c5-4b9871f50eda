import React from 'react';
import { ServicegradBentoCard } from './ServicegradBentoCard';
import { BestandBentoCard } from './BestandBentoCard';
import { BestandLogsCard } from './BestandLogsCard';

export function WorkflowGrid() {
  // Bento-Layout: Servicegrad + Bestand nebeneinander, Logs darunter
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ServicegradBentoCard
          process={{
            id: 'servicegrad',
            name: 'Servicegrad Automatisierung',
            description: 'SAP Servicegrad Export mit Excel-Verarbeitung',
            dbTable: 'servicegrad_results',
            status: 'idle',
            tcode: '/n/LSGIT/VS_DLV_CHECK',
            lastRun: undefined,
          } as any}
          onExecute={async () => Promise.resolve()}
          isExecuting={false}
        />
        <BestandBentoCard />
      </div>
      
      {/* Logs Card darunter */}
      <div className="grid grid-cols-1">
        <BestandLogsCard />
      </div>
    </div>
  );
}