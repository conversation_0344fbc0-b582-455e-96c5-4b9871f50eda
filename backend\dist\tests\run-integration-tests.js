"use strict";
/**
 * Integration Test Runner for AI Chatbot Database Integration
 *
 * This script runs all integration tests and provides a comprehensive report
 * of the test results, including performance metrics and coverage analysis.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const child_process_1 = require("child_process");
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
const TEST_SUITES = [
    {
        name: 'Complete Integration Tests',
        description: 'End-to-end tests for chat requests with database integration',
        testFile: 'chat-integration-complete.test.ts',
        requirements: ['1.1', '1.2', '1.3', '2.1', '3.1', '4.1', '5.1']
    },
    {
        name: 'Performance Tests',
        description: 'Performance tests to ensure response times meet requirements',
        testFile: 'chat-performance.test.ts',
        requirements: ['5.5']
    },
    {
        name: 'End-to-End Scenarios',
        description: 'Real-world scenario tests simulating actual user interactions',
        testFile: 'chat-e2e-scenarios.test.ts',
        requirements: ['1.1', '1.2', '1.3', '2.1', '3.1', '4.1', '5.1']
    },
    {
        name: 'Error Handling Integration',
        description: 'Integration tests for error handling and fallback mechanisms',
        testFile: 'error-handling-integration.test.ts',
        requirements: ['1.4', '5.4']
    }
];
class IntegrationTestRunner {
    constructor() {
        this.results = [];
        this.startTime = 0;
        this.endTime = 0;
    }
    async runAllTests() {
        console.log('🚀 Starting AI Chatbot Database Integration Tests\n');
        console.log('='.repeat(60));
        this.startTime = Date.now();
        for (const suite of TEST_SUITES) {
            await this.runTestSuite(suite);
        }
        this.endTime = Date.now();
        this.generateReport();
    }
    async runTestSuite(suite) {
        console.log(`\n📋 Running: ${suite.name}`);
        console.log(`📝 Description: ${suite.description}`);
        console.log(`📄 Requirements: ${suite.requirements.join(', ')}`);
        console.log('-'.repeat(50));
        const testFilePath = path_1.default.join(__dirname, suite.testFile);
        if (!(0, fs_1.existsSync)(testFilePath)) {
            console.log(`❌ Test file not found: ${suite.testFile}`);
            this.results.push({
                testFile: suite.testFile,
                passed: false,
                duration: 0,
                error: 'Test file not found'
            });
            return;
        }
        const suiteStartTime = Date.now();
        try {
            // Run the test suite using Jest
            const command = `npx jest ${suite.testFile} --verbose --detectOpenHandles --forceExit`;
            console.log(`🔄 Executing: ${command}`);
            const output = (0, child_process_1.execSync)(command, {
                cwd: path_1.default.join(__dirname, '../../..'),
                encoding: 'utf8',
                stdio: 'pipe'
            });
            const duration = Date.now() - suiteStartTime;
            console.log(`✅ ${suite.name} completed successfully`);
            console.log(`⏱️  Duration: ${duration}ms`);
            // Parse output for additional metrics
            this.parseTestOutput(output, suite.name);
            this.results.push({
                testFile: suite.testFile,
                passed: true,
                duration
            });
        }
        catch (error) {
            const duration = Date.now() - suiteStartTime;
            console.log(`❌ ${suite.name} failed`);
            console.log(`⏱️  Duration: ${duration}ms`);
            console.log(`💥 Error: ${error.message}`);
            // Show relevant error output
            if (error.stdout) {
                console.log('\n📊 Test Output:');
                console.log(error.stdout.toString().split('\n').slice(-20).join('\n'));
            }
            this.results.push({
                testFile: suite.testFile,
                passed: false,
                duration,
                error: error.message
            });
        }
    }
    parseTestOutput(output, suiteName) {
        // Extract useful metrics from Jest output
        const lines = output.split('\n');
        // Look for test results summary
        const summaryLine = lines.find(line => line.includes('Tests:'));
        if (summaryLine) {
            console.log(`📊 ${summaryLine.trim()}`);
        }
        // Look for performance information
        const timeLine = lines.find(line => line.includes('Time:'));
        if (timeLine) {
            console.log(`⏱️  ${timeLine.trim()}`);
        }
        // Look for coverage information
        const coverageLine = lines.find(line => line.includes('Coverage:'));
        if (coverageLine) {
            console.log(`📈 ${coverageLine.trim()}`);
        }
    }
    generateReport() {
        const totalDuration = this.endTime - this.startTime;
        const passedTests = this.results.filter(r => r.passed).length;
        const failedTests = this.results.filter(r => !r.passed).length;
        const totalTests = this.results.length;
        console.log('\n' + '='.repeat(60));
        console.log('📊 INTEGRATION TEST REPORT');
        console.log('='.repeat(60));
        console.log(`\n🎯 Overall Results:`);
        console.log(`   Total Test Suites: ${totalTests}`);
        console.log(`   Passed: ${passedTests} ✅`);
        console.log(`   Failed: ${failedTests} ${failedTests > 0 ? '❌' : ''}`);
        console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        console.log(`   Total Duration: ${totalDuration}ms (${(totalDuration / 1000).toFixed(1)}s)`);
        console.log(`\n📋 Detailed Results:`);
        this.results.forEach((result, index) => {
            const status = result.passed ? '✅' : '❌';
            const suite = TEST_SUITES[index];
            console.log(`   ${status} ${suite.name}`);
            console.log(`      File: ${result.testFile}`);
            console.log(`      Duration: ${result.duration}ms`);
            console.log(`      Requirements: ${suite.requirements.join(', ')}`);
            if (result.error) {
                console.log(`      Error: ${result.error}`);
            }
            console.log('');
        });
        // Requirements coverage analysis
        this.generateRequirementsCoverage();
        // Performance analysis
        this.generatePerformanceAnalysis();
        // Recommendations
        this.generateRecommendations();
        console.log('='.repeat(60));
        if (failedTests > 0) {
            console.log('❌ Some tests failed. Please review the errors above.');
            process.exit(1);
        }
        else {
            console.log('🎉 All integration tests passed successfully!');
            process.exit(0);
        }
    }
    generateRequirementsCoverage() {
        console.log(`\n📋 Requirements Coverage Analysis:`);
        const allRequirements = new Set();
        const coveredRequirements = new Set();
        TEST_SUITES.forEach(suite => {
            suite.requirements.forEach(req => {
                allRequirements.add(req);
                const result = this.results.find(r => r.testFile === suite.testFile);
                if (result === null || result === void 0 ? void 0 : result.passed) {
                    coveredRequirements.add(req);
                }
            });
        });
        const coveragePercentage = (coveredRequirements.size / allRequirements.size) * 100;
        console.log(`   Total Requirements: ${allRequirements.size}`);
        console.log(`   Covered Requirements: ${coveredRequirements.size}`);
        console.log(`   Coverage: ${coveragePercentage.toFixed(1)}%`);
        if (coveragePercentage < 100) {
            const uncovered = Array.from(allRequirements).filter(req => !coveredRequirements.has(req));
            console.log(`   ⚠️  Uncovered Requirements: ${uncovered.join(', ')}`);
        }
    }
    generatePerformanceAnalysis() {
        console.log(`\n⚡ Performance Analysis:`);
        const performanceResult = this.results.find(r => r.testFile.includes('performance'));
        if (performanceResult) {
            if (performanceResult.passed) {
                console.log(`   ✅ Performance tests passed`);
                console.log(`   ⏱️  Performance test duration: ${performanceResult.duration}ms`);
                if (performanceResult.duration > 30000) {
                    console.log(`   ⚠️  Performance tests took longer than expected (>30s)`);
                }
            }
            else {
                console.log(`   ❌ Performance tests failed`);
                console.log(`   💥 This indicates response time requirements may not be met`);
            }
        }
        // Analyze overall test execution performance
        const avgDuration = this.results.reduce((sum, r) => sum + r.duration, 0) / this.results.length;
        console.log(`   📊 Average test suite duration: ${avgDuration.toFixed(0)}ms`);
        if (avgDuration > 15000) {
            console.log(`   ⚠️  Test suites are running slower than expected`);
        }
    }
    generateRecommendations() {
        console.log(`\n💡 Recommendations:`);
        const failedResults = this.results.filter(r => !r.passed);
        if (failedResults.length === 0) {
            console.log(`   🎉 All tests passed! The AI chatbot database integration is working correctly.`);
            console.log(`   ✅ Response times meet requirements`);
            console.log(`   ✅ Error handling is functioning properly`);
            console.log(`   ✅ All query types are supported`);
            console.log(`   ✅ Backward compatibility is maintained`);
        }
        else {
            console.log(`   🔧 Fix the following issues:`);
            failedResults.forEach(result => {
                const suite = TEST_SUITES.find(s => s.testFile === result.testFile);
                console.log(`      - ${suite === null || suite === void 0 ? void 0 : suite.name}: ${result.error}`);
            });
            console.log(`\n   📝 Next steps:`);
            console.log(`      1. Review failed test output above`);
            console.log(`      2. Fix identified issues in the implementation`);
            console.log(`      3. Re-run tests to verify fixes`);
            console.log(`      4. Ensure all requirements are met`);
        }
        // Performance recommendations
        const performanceResult = this.results.find(r => r.testFile.includes('performance'));
        if (performanceResult && performanceResult.duration > 20000) {
            console.log(`\n   ⚡ Performance recommendations:`);
            console.log(`      - Consider optimizing database queries`);
            console.log(`      - Review data enrichment service efficiency`);
            console.log(`      - Check OpenRouter API response times`);
            console.log(`      - Implement caching where appropriate`);
        }
    }
}
// Run the tests if this script is executed directly
if (require.main === module) {
    const runner = new IntegrationTestRunner();
    runner.runAllTests().catch(error => {
        console.error('❌ Test runner failed:', error);
        process.exit(1);
    });
}
exports.default = IntegrationTestRunner;
