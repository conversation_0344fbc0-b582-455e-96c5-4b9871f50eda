const { PrismaClient } = require('@prisma/client');

async function updateRoleUserMapping() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Aktuelle _RoleToUser Einträge:');
    
    // Zeige aktuelle Einträge
    const currentEntries = await prisma.$queryRaw`SELECT * FROM _RoleToUser ORDER BY A, B`;
    console.table(currentEntries);
    
    console.log('\n🔄 Ändere A=2, B=1 zu A=1, B=2...');
    
    // Prüfe ob der Eintrag A=2, B=1 existiert
    const existingEntry = await prisma.$queryRaw`SELECT * FROM _RoleToUser WHERE A = 2 AND B = 1`;
    
    if (existingEntry.length === 0) {
      console.log('❌ Eintrag A=2, B=1 nicht gefunden!');
      return;
    }
    
    // Prüfe ob A=1, B=2 bereits existiert
    const conflictEntry = await prisma.$queryRaw`SELECT * FROM _RoleToUser WHERE A = 1 AND B = 2`;
    
    if (conflictEntry.length > 0) {
      console.log('⚠️ Eintrag A=1, B=2 existiert bereits! Lösche zuerst A=2, B=1...');
      await prisma.$executeRaw`DELETE FROM _RoleToUser WHERE A = 2 AND B = 1`;
      console.log('✅ Alter Eintrag gelöscht');
    } else {
      // Update den Eintrag
      await prisma.$executeRaw`UPDATE _RoleToUser SET A = 1, B = 2 WHERE A = 2 AND B = 1`;
      console.log('✅ Eintrag erfolgreich geändert von A=2, B=1 zu A=1, B=2');
    }
    
    console.log('\n📊 Neue _RoleToUser Einträge:');
    const newEntries = await prisma.$queryRaw`SELECT * FROM _RoleToUser ORDER BY A, B`;
    console.table(newEntries);
    
    console.log('\n🔍 Benutzer mit Rollen:');
    const usersWithRoles = await prisma.user.findMany({
      include: {
        roles: true
      }
    });
    
    usersWithRoles.forEach(user => {
      console.log(`👤 ${user.username} (ID: ${user.id}): ${user.roles.map(r => `${r.name} (ID: ${r.id})`).join(', ')}`);
    });
    
  } catch (error) {
    console.error('❌ Fehler:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateRoleUserMapping();