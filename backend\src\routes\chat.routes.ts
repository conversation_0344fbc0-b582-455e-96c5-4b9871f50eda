/**
 * Chat-Routen für die direkte Kommunikation mit dem KI-Assistenten
 * 
 * Diese Routen sind unter /api/chat verfügbar und leiten direkt an die KI-Dienste weiter.
 */

import express from 'express';
import { z } from 'zod';
import { createValidationMiddleware } from '../middleware/validation.middleware';
import { rateLimitConfig } from '../middleware/rate-limiting.middleware';
import OpenRouterService from '../services/openrouter.service';
import DataEnrichmentService from '../services/data-enrichment.service';
import performanceMonitor from '../services/performance-monitoring.service';
import { PrismaClient } from '@prisma-sfm-dashboard/client';

const router = express.Router();

// Initialize Prisma client and data enrichment service
const prisma = new PrismaClient();
const dataEnrichmentService = new DataEnrichmentService(prisma);

// Validierungsschema für Chat-Anfragen
const chatRequestSchema = z.object({
  message: z.string().min(1, "Nachricht darf nicht leer sein").max(1000, "Nachricht zu lang"),
  includeInsights: z.boolean().optional().default(false),
  includeAnomalies: z.boolean().optional().default(false)
});

// Einfacher Chat-Endpunkt
router.post('/', rateLimitConfig.general, createValidationMiddleware({ body: chatRequestSchema }), async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { message } = req.body;
    
    console.log(`📝 [CHAT] Anfrage erhalten: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
    
    // Anfrage an OpenRouter-Service weiterleiten
    const llmStartTime = Date.now();
    const response = await OpenRouterService.generateResponse({
      message,
      includeInsights: false,
      includeAnomalies: false
    });
    const llmTime = Date.now() - llmStartTime;
    
    const totalTime = Date.now() - startTime;
    
    // Record performance metrics
    performanceMonitor.recordResponseTime(
      '/api/chat',
      false, // not enriched
      totalTime,
      0, // no enrichment time
      llmTime,
      JSON.stringify(response).length
    );
    
    console.log(`✅ [CHAT] Antwort gesendet für: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}" (${totalTime}ms)`);
    
    res.json(response);
  } catch (error) {
    const totalTime = Date.now() - startTime;
    
    // Record failed response time
    performanceMonitor.recordResponseTime(
      '/api/chat',
      false,
      totalTime,
      0,
      0,
      0
    );
    
    console.error('❌ [CHAT] Fehler:', error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Verarbeitung der Chat-Anfrage ist ein Fehler aufgetreten.'
    });
  }
});

// Erweiterter Chat-Endpunkt mit Insights und Anomalien
router.post('/enhanced', rateLimitConfig.general, createValidationMiddleware({ body: chatRequestSchema }), async (req, res) => {
  const startTime = Date.now();
  
  try {
    const { message, includeInsights, includeAnomalies } = req.body;
    
    console.log(`📝 [CHAT-ENHANCED] Anfrage erhalten: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}" (Insights: ${includeInsights}, Anomalien: ${includeAnomalies})`);
    
    let enrichedContext;
    let dataEnrichmentError = false;
    let enrichmentDetails = {};
    let enrichmentTime = 0;
    
    try {
      // Attempt to enrich context with database information
      console.log(`🔍 [CHAT-ENHANCED] Enriching context for message...`);
      const enrichmentStartTime = Date.now();
      enrichedContext = await dataEnrichmentService.enrichChatContext(message);
      enrichmentTime = Date.now() - enrichmentStartTime;
      
      // Collect enrichment details for response metadata
      enrichmentDetails = {
        requestId: enrichedContext.requestId,
        processingTime: enrichedContext.processingTime,
        fallbackReason: enrichedContext.fallbackReason,
        partialFailure: enrichedContext.partialFailure
      };
      
      if (enrichedContext.hasData) {
        console.log(`✅ [CHAT-ENHANCED] Context enriched with data types: ${enrichedContext.dataTypes.join(', ')} (${enrichedContext.processingTime}ms)`);
        
        if (enrichedContext.partialFailure) {
          console.warn(`⚠️ [CHAT-ENHANCED] Partial failure in data enrichment - some repositories unavailable`);
        }
      } else {
        console.log(`📝 [CHAT-ENHANCED] No relevant database context found (${enrichedContext.fallbackReason || 'no intents detected'})`);
      }
    } catch (enrichmentError) {
      enrichmentTime = Date.now() - startTime;
      console.error('⚠️ [CHAT-ENHANCED] Data enrichment failed, continuing with basic response:', enrichmentError);
      dataEnrichmentError = true;
      enrichmentDetails = {
        error: enrichmentError instanceof Error ? enrichmentError.message : 'Unknown enrichment error',
        fallbackUsed: true
      };
      // Continue without enriched context - graceful fallback
    }
    
    // Anfrage an OpenRouter-Service weiterleiten
    const llmStartTime = Date.now();
    const response = await OpenRouterService.generateResponse({
      message,
      includeInsights,
      includeAnomalies,
      enrichedContext: dataEnrichmentError ? undefined : enrichedContext
    });
    const llmTime = Date.now() - llmStartTime;
    
    const totalTime = Date.now() - startTime;
    
    // Add comprehensive metadata about data enrichment to response
    const enhancedResponse = {
      ...response,
      dataEnrichmentUsed: !dataEnrichmentError && enrichedContext?.hasData,
      dataEnrichmentError: dataEnrichmentError,
      enrichmentDetails,
      detectedIntents: enrichedContext?.detectedIntents?.map(intent => ({
        type: intent.type,
        confidence: intent.confidence,
        keywords: intent.keywords
      })) || [],
      dataQuality: {
        hasFullData: enrichedContext?.hasData && !enrichedContext?.partialFailure,
        hasPartialData: enrichedContext?.hasData && enrichedContext?.partialFailure,
        fallbackUsed: !!enrichedContext?.fallbackReason || dataEnrichmentError,
        availableDataTypes: enrichedContext?.dataTypes || []
      },
      performanceMetrics: {
        totalTime,
        enrichmentTime,
        llmTime,
        cacheUsed: enrichedContext?.requestId ? false : true // Simplified cache detection
      }
    };
    
    // Record performance metrics
    performanceMonitor.recordResponseTime(
      '/api/chat/enhanced',
      true, // enriched
      totalTime,
      enrichmentTime,
      llmTime,
      JSON.stringify(enhancedResponse).length
    );
    
    console.log(`✅ [CHAT-ENHANCED] Antwort gesendet für: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}" (DB-Context: ${enhancedResponse.dataEnrichmentUsed}, ${totalTime}ms)`);
    
    res.json(enhancedResponse);
  } catch (error) {
    const totalTime = Date.now() - startTime;
    
    // Record failed response time
    performanceMonitor.recordResponseTime(
      '/api/chat/enhanced',
      true,
      totalTime,
      0,
      0,
      0
    );
    
    console.error('❌ [CHAT-ENHANCED] Fehler:', error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Verarbeitung der erweiterten Chat-Anfrage ist ein Fehler aufgetreten.'
    });
  }
});

// Cleanup handler for Prisma client
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

export default router;
