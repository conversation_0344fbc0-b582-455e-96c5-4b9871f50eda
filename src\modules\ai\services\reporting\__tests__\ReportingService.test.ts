/**
 * Reporting Service Unit Tests
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { ReportingService, ReportTemplate, ReportGenerationRequest, ReportInsight, ReportRecommendation } from '../ReportingService';
import { AIServiceError } from '../../types';
import { deliveryRepository } from '@/repositories/delivery.repository';
import { productionRepository } from '@/repositories/production.repository';
import { warehouseRepository } from '@/repositories/warehouse.repository';
import { supplierRepository } from '@/repositories/supplier.repository';
import { systemRepository } from '@/repositories/system.repository';

// Mock the repository imports
vi.mock('@/repositories/delivery.repository', () => ({
  deliveryRepository: {
    getAll: vi.fn(),
    invalidateCache: vi.fn()
  }
}));

vi.mock('@/repositories/production.repository', () => ({
  productionRepository: {
    getAll: vi.fn(),
    invalidateCache: vi.fn()
  }
}));

vi.mock('@/repositories/warehouse.repository', () => ({
  warehouseRepository: {
    getAll: vi.fn(),
    invalidateCache: vi.fn()
  }
}));

vi.mock('@/repositories/supplier.repository', () => ({
  supplierRepository: {
    getAll: vi.fn(),
    invalidateCache: vi.fn()
  }
}));

vi.mock('@/repositories/system.repository', () => ({
  systemRepository: {
    getAll: vi.fn(),
    invalidateCache: vi.fn()
  }
}));

describe('ReportingService', () => {
  let reportingService: ReportingService;

  beforeEach(async () => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Create fresh service instance
    reportingService = new ReportingService({
      enableLogging: false
    });
    
    await reportingService.initialize();
  });

  afterEach(async () => {
    await reportingService.destroy();
  });

  describe('Template Management', () => {
    it('should create a new report template', async () => {
      const templateData = {
        name: 'Test Report',
        description: 'Test description',
        type: 'kpi' as const,
        department: 'dispatch' as const,
        sections: [
          {
            id: 'test-section',
            title: 'Test Section',
            type: 'kpi' as const,
            dataSource: 'delivery',
            order: 1
          }
        ],
        format: 'pdf' as const,
        isActive: true
      };

      const template = await reportingService.createTemplate(templateData);

      expect(template).toBeDefined();
      expect(template.id).toBeDefined();
      expect(template.name).toBe(templateData.name);
      expect(template.description).toBe(templateData.description);
      expect(template.type).toBe(templateData.type);
      expect(template.department).toBe(templateData.department);
      expect(template.sections).toHaveLength(1);
      expect(template.format).toBe(templateData.format);
      expect(template.isActive).toBe(true);
      expect(template.createdAt).toBeInstanceOf(Date);
      expect(template.updatedAt).toBeInstanceOf(Date);
    });

    it('should update an existing template', async () => {
      // First create a template
      const template = await reportingService.createTemplate({
        name: 'Original Name',
        description: 'Original description',
        type: 'kpi',
        sections: [],
        format: 'pdf',
        isActive: true
      });

      // Update the template (add small delay to ensure different timestamp)
      await new Promise(resolve => setTimeout(resolve, 1));
      
      const updates = {
        name: 'Updated Name',
        description: 'Updated description',
        isActive: false
      };

      const updatedTemplate = await reportingService.updateTemplate(template.id, updates);

      expect(updatedTemplate.name).toBe(updates.name);
      expect(updatedTemplate.description).toBe(updates.description);
      expect(updatedTemplate.isActive).toBe(false);
      expect(updatedTemplate.updatedAt.getTime()).toBeGreaterThan(template.updatedAt.getTime());
    });

    it('should delete a template', async () => {
      // Create a template
      const template = await reportingService.createTemplate({
        name: 'To Delete',
        description: 'Will be deleted',
        type: 'kpi',
        sections: [],
        format: 'pdf',
        isActive: true
      });

      // Delete the template
      const deleted = await reportingService.deleteTemplate(template.id);
      expect(deleted).toBe(true);

      // Verify it's gone
      const retrieved = await reportingService.getTemplate(template.id);
      expect(retrieved).toBeNull();
    });

    it('should return false when deleting non-existent template', async () => {
      const deleted = await reportingService.deleteTemplate('non-existent-id');
      expect(deleted).toBe(false);
    });

    it('should get all templates', async () => {
      // Create multiple templates
      await reportingService.createTemplate({
        name: 'Template 1',
        description: 'First template',
        type: 'kpi',
        department: 'dispatch',
        sections: [],
        format: 'pdf',
        isActive: true
      });

      await reportingService.createTemplate({
        name: 'Template 2',
        description: 'Second template',
        type: 'performance',
        department: 'cutting',
        sections: [],
        format: 'excel',
        isActive: false
      });

      const templates = await reportingService.getTemplates();
      expect(templates).toHaveLength(3); // 2 created + 1 default
      expect(templates[0].name).toBe('KPI Dashboard Report'); // Default template comes first alphabetically
    });

    it('should filter templates by criteria', async () => {
      await reportingService.createTemplate({
        name: 'Dispatch Report',
        description: 'Dispatch specific',
        type: 'kpi',
        department: 'dispatch',
        sections: [],
        format: 'pdf',
        isActive: true
      });

      await reportingService.createTemplate({
        name: 'Cutting Report',
        description: 'Cutting specific',
        type: 'performance',
        department: 'cutting',
        sections: [],
        format: 'excel',
        isActive: false
      });

      // Filter by department
      const dispatchTemplates = await reportingService.getTemplates({ department: 'dispatch' });
      expect(dispatchTemplates.length).toBeGreaterThanOrEqual(1);
      const dispatchSpecific = dispatchTemplates.find(t => t.name === 'Dispatch Report');
      expect(dispatchSpecific).toBeDefined();

      // Filter by type
      const kpiTemplates = await reportingService.getTemplates({ type: 'kpi' });
      expect(kpiTemplates).toHaveLength(2); // 1 created + 1 default

      // Filter by active status
      const activeTemplates = await reportingService.getTemplates({ isActive: true });
      expect(activeTemplates).toHaveLength(2); // 1 created + 1 default
    });

    it('should get specific template by ID', async () => {
      const template = await reportingService.createTemplate({
        name: 'Specific Template',
        description: 'For ID test',
        type: 'kpi',
        sections: [],
        format: 'pdf',
        isActive: true
      });

      const retrieved = await reportingService.getTemplate(template.id);
      expect(retrieved).toBeDefined();
      expect(retrieved!.name).toBe('Specific Template');
    });

    it('should return null for non-existent template ID', async () => {
      const retrieved = await reportingService.getTemplate('non-existent-id');
      expect(retrieved).toBeNull();
    });
  });

  describe('Report Generation', () => {
    let testTemplate: ReportTemplate;

    beforeEach(async () => {
      testTemplate = await reportingService.createTemplate({
        name: 'Test Generation Template',
        description: 'For testing report generation',
        type: 'kpi',
        department: 'dispatch',
        sections: [
          {
            id: 'delivery-section',
            title: 'Delivery Performance',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'avg',
            order: 1
          },
          {
            id: 'production-section',
            title: 'Production Metrics',
            type: 'chart',
            dataSource: 'production',
            chartType: 'line',
            aggregation: 'sum',
            order: 2
          }
        ],
        format: 'pdf',
        isActive: true
      });

      // Setup mock data
      (deliveryRepository.getAll as Mock).mockResolvedValue([
        { id: '1', value: 95, date: '2024-01-01' },
        { id: '2', value: 87, date: '2024-01-02' },
        { id: '3', value: 92, date: '2024-01-03' }
      ]);

      (productionRepository.getAll as Mock).mockResolvedValue([
        { id: '1', value: 1000, date: '2024-01-01' },
        { id: '2', value: 1200, date: '2024-01-02' },
        { id: '3', value: 1100, date: '2024-01-03' }
      ]);
    });

    it('should generate a report from template', async () => {
      const request: ReportGenerationRequest = {
        templateId: testTemplate.id,
        includeInsights: true,
        includeRecommendations: true
      };

      const report = await reportingService.generateReport(request);

      expect(report).toBeDefined();
      expect(report.id).toBeDefined();
      expect(report.templateId).toBe(testTemplate.id);
      expect(report.title).toContain(testTemplate.name);
      expect(report.generatedAt).toBeInstanceOf(Date);
      expect(report.timeRange).toBeDefined();
      expect(report.sections).toHaveLength(2);
      expect(report.insights).toBeDefined();
      expect(report.recommendations).toBeDefined();
      expect(report.metadata.dataPoints).toBe(6); // 3 + 3 from mock data
      expect(report.metadata.processingTime).toBeGreaterThan(0);
      expect(report.metadata.sources).toContain('delivery');
      expect(report.metadata.sources).toContain('production');
    });

    it('should generate report with custom time range', async () => {
      const customTimeRange = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      const request: ReportGenerationRequest = {
        templateId: testTemplate.id,
        timeRange: customTimeRange
      };

      const report = await reportingService.generateReport(request);

      expect(report.timeRange.start).toEqual(customTimeRange.start);
      expect(report.timeRange.end).toEqual(customTimeRange.end);
    });

    it('should generate report with custom format', async () => {
      const request: ReportGenerationRequest = {
        templateId: testTemplate.id,
        format: 'excel'
      };

      const report = await reportingService.generateReport(request);

      expect(report.format).toBe('excel');
    });

    it('should generate report without insights and recommendations', async () => {
      const request: ReportGenerationRequest = {
        templateId: testTemplate.id,
        includeInsights: false,
        includeRecommendations: false
      };

      const report = await reportingService.generateReport(request);

      expect(report.insights).toHaveLength(0);
      expect(report.recommendations).toHaveLength(0);
    });

    it('should handle unknown data source error', async () => {
      const badTemplate = await reportingService.createTemplate({
        name: 'Bad Template',
        description: 'Has unknown data source',
        type: 'kpi',
        sections: [
          {
            id: 'bad-section',
            title: 'Bad Section',
            type: 'kpi',
            dataSource: 'unknown-source',
            order: 1
          }
        ],
        format: 'pdf',
        isActive: true
      });

      const request: ReportGenerationRequest = {
        templateId: badTemplate.id
      };

      await expect(reportingService.generateReport(request)).rejects.toThrow('Unknown data source: unknown-source');
    });

    it('should handle non-existent template error', async () => {
      const request: ReportGenerationRequest = {
        templateId: 'non-existent-template'
      };

      await expect(reportingService.generateReport(request)).rejects.toThrow('Template not found: non-existent-template');
    });
  });

  describe('Data Aggregation', () => {
    beforeEach(async () => {
      (deliveryRepository.getAll as Mock).mockResolvedValue([
        { id: '1', value: 10 },
        { id: '2', value: 20 },
        { id: '3', value: 30 },
        { id: '4', value: 40 }
      ]);
    });

    it('should aggregate data with sum', async () => {
      const template = await reportingService.createTemplate({
        name: 'Sum Test',
        description: 'Test sum aggregation',
        type: 'kpi',
        sections: [
          {
            id: 'sum-section',
            title: 'Sum Test',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'sum',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({ templateId: template.id });
      const section = report.sections[0];

      expect(section.summary?.total).toBe(100); // 10+20+30+40
    });

    it('should aggregate data with average', async () => {
      const template = await reportingService.createTemplate({
        name: 'Avg Test',
        description: 'Test average aggregation',
        type: 'kpi',
        sections: [
          {
            id: 'avg-section',
            title: 'Average Test',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'avg',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({ templateId: template.id });
      const section = report.sections[0];

      expect(section.summary?.total).toBe(25); // (10+20+30+40)/4
      expect(section.summary?.average).toBe(25);
    });

    it('should aggregate data with count', async () => {
      const template = await reportingService.createTemplate({
        name: 'Count Test',
        description: 'Test count aggregation',
        type: 'kpi',
        sections: [
          {
            id: 'count-section',
            title: 'Count Test',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'count',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({ templateId: template.id });
      const section = report.sections[0];

      expect(section.summary?.total).toBe(4); // 4 items
    });

    it('should detect upward trend', async () => {
      (deliveryRepository.getAll as Mock).mockResolvedValue([
        { id: '1', value: 10 },
        { id: '2', value: 15 },
        { id: '3', value: 25 },
        { id: '4', value: 30 }
      ]);

      const template = await reportingService.createTemplate({
        name: 'Trend Test',
        description: 'Test trend detection',
        type: 'kpi',
        sections: [
          {
            id: 'trend-section',
            title: 'Trend Test',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'avg',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({ templateId: template.id });
      const section = report.sections[0];

      expect(section.summary?.trend).toBe('up');
      expect(section.summary?.change).toBeGreaterThan(0);
    });
  });

  describe('Insight Generation', () => {
    it('should generate trend insights', async () => {
      // Mock data with clear upward trend
      (deliveryRepository.getAll as Mock).mockResolvedValue([
        { id: '1', value: 10 },
        { id: '2', value: 15 },
        { id: '3', value: 30 },
        { id: '4', value: 35 }
      ]);

      const template = await reportingService.createTemplate({
        name: 'Insight Test',
        description: 'Test insight generation',
        type: 'kpi',
        sections: [
          {
            id: 'insight-section',
            title: 'Performance Metrics',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'avg',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({ 
        templateId: template.id,
        includeInsights: true 
      });

      expect(report.insights.length).toBeGreaterThan(0);
      
      const trendInsight = report.insights.find(insight => insight.type === 'trend');
      expect(trendInsight).toBeDefined();
      expect(trendInsight!.title).toContain('Steigender Trend');
      expect(trendInsight!.confidence).toBeGreaterThan(0);
    });

    it('should generate anomaly insights', async () => {
      // Mock data with anomalies (more data points for better detection)
      (deliveryRepository.getAll as Mock).mockResolvedValue([
        { id: '1', value: 10 },
        { id: '2', value: 12 },
        { id: '3', value: 11 },
        { id: '4', value: 9 },
        { id: '5', value: 10 },
        { id: '6', value: 100 }, // Clear anomaly
        { id: '7', value: 11 },
        { id: '8', value: 12 }
      ]);

      const template = await reportingService.createTemplate({
        name: 'Anomaly Test',
        description: 'Test anomaly detection',
        type: 'kpi',
        sections: [
          {
            id: 'anomaly-section',
            title: 'Quality Metrics',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'avg',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({ 
        templateId: template.id,
        includeInsights: true 
      });

      const anomalyInsight = report.insights.find(insight => insight.type === 'anomaly');
      expect(anomalyInsight).toBeDefined();
      expect(anomalyInsight!.title).toContain('Anomalien');
      expect(['medium', 'high']).toContain(anomalyInsight!.severity); // Allow both medium and high severity
    });

    it('should generate correlation insights', async () => {
      // Mock correlated data
      (deliveryRepository.getAll as Mock).mockResolvedValue([
        { id: '1', value: 10 },
        { id: '2', value: 20 },
        { id: '3', value: 30 }
      ]);

      (productionRepository.getAll as Mock).mockResolvedValue([
        { id: '1', value: 100 },
        { id: '2', value: 200 },
        { id: '3', value: 300 }
      ]);

      const template = await reportingService.createTemplate({
        name: 'Correlation Test',
        description: 'Test correlation detection',
        type: 'kpi',
        sections: [
          {
            id: 'delivery-section',
            title: 'Delivery',
            type: 'kpi',
            dataSource: 'delivery',
            order: 1
          },
          {
            id: 'production-section',
            title: 'Production',
            type: 'kpi',
            dataSource: 'production',
            order: 2
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({ 
        templateId: template.id,
        includeInsights: true 
      });

      const correlationInsight = report.insights.find(insight => insight.type === 'correlation');
      expect(correlationInsight).toBeDefined();
      expect(correlationInsight!.title).toContain('Korrelation');
      expect(correlationInsight!.dataPoints).toHaveLength(2);
    });
  });

  describe('Recommendation Generation', () => {
    it('should generate recommendations for declining trends', async () => {
      // Mock data with declining trend
      (deliveryRepository.getAll as Mock).mockResolvedValue([
        { id: '1', value: 100 },
        { id: '2', value: 80 },
        { id: '3', value: 60 },
        { id: '4', value: 40 }
      ]);

      const template = await reportingService.createTemplate({
        name: 'Recommendation Test',
        description: 'Test recommendation generation',
        type: 'kpi',
        department: 'dispatch',
        sections: [
          {
            id: 'declining-section',
            title: 'Performance',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'avg',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({ 
        templateId: template.id,
        includeInsights: true,
        includeRecommendations: true
      });

      expect(report.recommendations.length).toBeGreaterThan(0);
      
      const efficiencyRec = report.recommendations.find(rec => rec.category === 'efficiency');
      expect(efficiencyRec).toBeDefined();
      expect(efficiencyRec!.actionItems.length).toBeGreaterThan(0);
    });

    it('should generate department-specific recommendations', async () => {
      (deliveryRepository.getAll as Mock).mockResolvedValue([
        { id: '1', value: 50 }
      ]);

      const template = await reportingService.createTemplate({
        name: 'Department Test',
        description: 'Test department recommendations',
        type: 'kpi',
        department: 'cutting',
        sections: [
          {
            id: 'cutting-section',
            title: 'Cutting Performance',
            type: 'kpi',
            dataSource: 'delivery',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({ 
        templateId: template.id,
        includeRecommendations: true
      });

      const materialRec = report.recommendations.find(rec => 
        rec.title.includes('Materialverschnitt')
      );
      expect(materialRec).toBeDefined();
      expect(materialRec!.category).toBe('resource');
    });

    it('should prioritize recommendations correctly', async () => {
      (deliveryRepository.getAll as Mock).mockResolvedValue([
        { id: '1', value: 50 }
      ]);

      const template = await reportingService.createTemplate({
        name: 'Priority Test',
        description: 'Test recommendation prioritization',
        type: 'kpi',
        department: 'dispatch',
        sections: [
          {
            id: 'priority-section',
            title: 'Test Section',
            type: 'kpi',
            dataSource: 'delivery',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({ 
        templateId: template.id,
        includeRecommendations: true
      });

      // Recommendations should be sorted by priority (critical > high > medium > low)
      for (let i = 0; i < report.recommendations.length - 1; i++) {
        const current = report.recommendations[i];
        const next = report.recommendations[i + 1];
        
        const priorityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
        expect(priorityOrder[current.priority]).toBeGreaterThanOrEqual(priorityOrder[next.priority]);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle repository errors gracefully', async () => {
      (deliveryRepository.getAll as Mock).mockRejectedValue(new Error('Database connection failed'));

      const template = await reportingService.createTemplate({
        name: 'Error Test',
        description: 'Test error handling',
        type: 'kpi',
        sections: [
          {
            id: 'error-section',
            title: 'Error Section',
            type: 'kpi',
            dataSource: 'delivery',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      await expect(reportingService.generateReport({ templateId: template.id }))
        .rejects.toThrow('PREDICTION_FAILED');
    });

    it('should handle invalid template updates', async () => {
      await expect(reportingService.updateTemplate('non-existent', { name: 'New Name' }))
        .rejects.toThrow('Template not found: non-existent');
    });
  });

  describe('Service Health', () => {
    it('should provide health check information', async () => {
      const health = await reportingService.healthCheck();

      expect(health.isInitialized).toBe(true);
      expect(health.isHealthy).toBe(true);
      expect(health.lastChecked).toBeInstanceOf(Date);
      expect(health.performance).toBeDefined();
      expect(health.performance!.totalRequests).toBeGreaterThanOrEqual(0);
      expect(health.performance!.successRate).toBeGreaterThanOrEqual(0);
      expect(health.performance!.averageResponseTime).toBeGreaterThanOrEqual(0);
    });
  });
});