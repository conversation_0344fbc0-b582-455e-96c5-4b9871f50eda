import React, { memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, PieChart, Pie, Cell, ResponsiveContainer } from 'recharts';
import { CuttingPlan, DrumInventory, CuttingOrder } from '../../services/types';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Clock, Scissors, TrendingUp, AlertCircle } from 'lucide-react';
import { SubtlePatternCard } from '@/components/ui/Card_SubtlePattern';

interface CuttingPlanVisualizationProps {
  plan: CuttingPlan;
  drums: DrumInventory[];
  orders: CuttingOrder[];
}

/**
 * Cutting Plan Visualization Component
 * 
 * Displays comprehensive visualization of cutting optimization results
 * including drum utilization, cutting sequence, and efficiency metrics.
 */
export const CuttingPlanVisualization = memo(function CuttingPlanVisualization({
  plan,
  drums,
  orders
}: CuttingPlanVisualizationProps) {
  
  // Prepare data for drum utilization chart
  const drumUtilizationData = plan.drumAllocations.map(allocation => {
    const drum = drums.find(d => d.id === allocation.drumId);
    const totalLength = drum?.availableLength || drum?.totalLength || 0;
    const usedLength = totalLength - allocation.remainingLength;
    
    return {
      drumId: allocation.drumId.replace('drum-', 'T'),
      utilization: Math.round(allocation.utilization * 100),
      usedLength,
      remainingLength: allocation.remainingLength,
      totalLength,
      cutsCount: allocation.cuts.length
    };
  });

  // Prepare data for waste distribution pie chart
  const wasteData = plan.drumAllocations
    .filter(allocation => allocation.remainingLength > 0)
    .map(allocation => ({
      name: allocation.drumId.replace('drum-', 'Trommel '),
      value: allocation.remainingLength,
      percentage: Math.round((allocation.remainingLength / plan.totalWaste) * 100)
    }));

  // Prepare cutting sequence timeline data
  const sequenceData = plan.cuttingSequence.slice(0, 10).map(step => ({
    step: step.stepNumber,
    drumId: step.drumId.replace('drum-', 'T'),
    cuts: step.cuts.length,
    time: Math.round(step.estimatedTime / 60), // Convert to minutes
    totalLength: step.cuts.reduce((sum, cut) => sum + cut.length, 0)
  }));

  // Chart configuration
  const chartConfig = {
    utilization: {
      label: "Auslastung (%)",
      color: "var(--chart-1)",
    },
    remaining: {
      label: "Verschnitt (m)",
      color: "var(--chart-2)",
    },
    cuts: {
      label: "Anzahl Schnitte",
      color: "var(--chart-3)",
    },
    time: {
      label: "Zeit (min)",
      color: "var(--chart-4)",
    }
  };

  // Colors for pie chart
  const COLORS = ['var(--chart-1)', 'var(--chart-2)', 'var(--chart-3)', 'var(--chart-4)', 'var(--chart-5)'];

  // Calculate summary metrics
  const totalCuts = plan.drumAllocations.reduce((sum, allocation) => sum + allocation.cuts.length, 0);
  const averageUtilization = plan.drumAllocations.reduce((sum, allocation) => sum + allocation.utilization, 0) / plan.drumAllocations.length;
  const estimatedHours = Math.round(plan.estimatedTime / 3600 * 10) / 10;

  return (
    <div className="space-y-6">
      {/* Summary Cards mit SubtlePattern Design */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <SubtlePatternCard
          title="Effizienz"
          value={`${Math.round(plan.efficiency * 100)}%`}
          subtitle="Optimierungsgrad"
          icon={<TrendingUp className="h-5 w-5" />}
          valueClassName="text-green-600"
        />

        <SubtlePatternCard
          title="Schnitte"
          value={totalCuts}
          subtitle="Anzahl Schnitte"
          icon={<Scissors className="h-5 w-5" />}
          valueClassName="text-blue-600"
        />

        <SubtlePatternCard
          title="Verschnitt"
          value={`${parseFloat(plan.totalWaste.toFixed(1))} m`}
          subtitle="Materialverschnitt"
          icon={<AlertCircle className="h-5 w-5" />}
          valueClassName="text-orange-600"
        />

        <SubtlePatternCard
          title="Geschätzte Zeit"
          value={`${estimatedHours}h`}
          subtitle="Bearbeitungszeit"
          icon={<Clock className="h-5 w-5" />}
          valueClassName="text-purple-600"
        />
      </div>

      {/* Drei Charts in einer Reihe mit 3 Spalten */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Drum Utilization Chart */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle>Trommel-Auslastung</CardTitle>
            <CardDescription>
              Auslastung und Verschnitt pro Trommel
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <BarChart data={drumUtilizationData} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis 
                  dataKey="drumId" 
                  className="text-xs font-bold"
                  tick={{ fontSize: 12 }}
                />
                <YAxis 
                  className="text-xs font-bold"
                  tick={{ fill: "#000000" }}
                  label={{ 
                    value: "Auslastung (%)", 
                    angle: -90, 
                    position: "insideLeft",
                    style: { textAnchor: "middle", fontSize: 12 }
                  }}
                />
                <ChartTooltip
                  content={
                    <ChartTooltipContent
                      labelFormatter={(label) => `Trommel: ${label}`}
                      formatter={(value, name) => [
                        `${value}${name === 'utilization' ? '%' : ' m'}`,
                        name === 'utilization' ? 'Auslastung' : 'Verschnitt'
                      ]}
                    />
                  }
                />
                <Bar
                  dataKey="utilization"
                  name="Auslastung"
                  fill={chartConfig.utilization.color}
                  stroke="#000000"
                  strokeWidth={2}
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Waste Distribution Pie Chart */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle>Verschnitt-Verteilung</CardTitle>
            <CardDescription>
              Verteilung des Verschnitts auf die Trommeln
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={wasteData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    stroke="#000000"
                    strokeWidth={2}
                  >
                    {wasteData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <ChartTooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        const data = payload[0].payload;
                        return (
                          <div className="bg-white p-2 border border-gray-300 rounded shadow">
                            <p className="font-medium">{data.name}</p>
                            <p className="text-sm">Verschnitt: {data.value} m ({data.percentage}%)</p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Cutting Sequence Timeline */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle>Schnitt-Sequenz (erste 10 Schritte)</CardTitle>
            <CardDescription>
              Zeitlicher Ablauf der Schneidvorgänge
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <BarChart data={sequenceData} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis 
                  dataKey="step" 
                  className="text-xs font-bold"
                  tick={{ fontSize: 12 }}
                  label={{ value: "Schritt", position: "insideBottom", offset: -5 }}
                />
                <YAxis 
                  className="text-xs font-bold"
                  tick={{ fill: "#000000" }}
                  label={{ 
                    value: "Zeit (min)", 
                    angle: -90, 
                    position: "insideLeft",
                    style: { textAnchor: "middle", fontSize: 12 }
                  }}
                />
                <ChartTooltip
                  content={
                    <ChartTooltipContent
                      labelFormatter={(label) => `Schritt: ${label}`}
                      formatter={(value, name) => [
                        name === 'time' ? `${value} min` : `${value}`,
                        name === 'time' ? 'Zeit' : name === 'cuts' ? 'Schnitte' : 'Trommel'
                      ]}
                    />
                  }
                />
                <Bar
                  dataKey="time"
                  name="Zeit"
                  fill={chartConfig.time.color}
                  stroke="#000000"
                  strokeWidth={2}
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Drum Allocations */}
      <Card className="border-[#ff7a05]">
        <CardHeader>
          <CardTitle>Detaillierte Trommel-Zuordnungen</CardTitle>
          <CardDescription>
            Übersicht aller Schnitte pro Trommel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {plan.drumAllocations.map((allocation, index) => {
              const drum = drums.find(d => d.id === allocation.drumId);
              const totalLength = drum?.availableLength || drum?.totalLength || 0;
              
              return (
                <div key={allocation.drumId} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-center mb-3">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">
                        Trommel {index + 1}
                      </Badge>
                      <span className="font-medium">{drum?.cableType}</span>

                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        Auslastung: {Math.round(allocation.utilization * 100)}%
                      </p>
                      <p className="text-xs text-gray-600">
                        Verschnitt: {allocation.remainingLength} m
                      </p>
                    </div>
                  </div>
                  
                  <Progress 
                    value={allocation.utilization * 100} 
                    className="mb-3"
                  />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {allocation.cuts.map((cut, cutIndex) => {
                      const order = orders.find(o => o.id === cut.orderId);
                      return (
                        <div key={cutIndex} className="text-xs p-2 bg-gray-50 rounded">
                          <p className="font-medium">
                            Schnitt {cutIndex + 1}: {cut.length} m
                          </p>
                          <p className="text-gray-600">
                            Bestellung: {order?.cableType}
                          </p>
                          <p className="text-gray-600">
                            Position: {cut.startPosition}-{cut.endPosition} m
                          </p>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
});