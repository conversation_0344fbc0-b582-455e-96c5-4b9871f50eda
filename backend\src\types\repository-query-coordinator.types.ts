/**
 * Repository Query Coordinator Types
 * 
 * Type definitions for coordinating queries across multiple repositories
 */

export interface QueryIntent {
  type: 'stoerungen' | 'dispatch' | 'cutting' | 'general';
  keywords: string[];
  timeRange?: DateRange;
  specificMetrics?: string[];
  confidence: number;
}

export interface DateRange {
  startDate?: string;
  endDate?: string;
}

export interface QueryResult {
  dataType: string;
  data: any;
  summary: string;
  timestamp: Date;
  success: boolean;
  error?: string;
  queryTime?: number;
  intent?: string;
  fallback?: boolean;
  partialFailure?: boolean;
  retryAttempts?: number;
}

export interface RepositoryQueryCoordinatorConfig {
  maxConcurrentQueries: number;
  queryTimeout: number;
  retryAttempts: number;
  retryDelay: number;
  enableMetrics: boolean;
  enableCaching: boolean;
}

export interface QueryExecutionOptions {
  timeout?: number;
  retries?: number;
  priority?: 'high' | 'normal' | 'low';
  cacheKey?: string;
}

export interface AggregatedQueryResult {
  results: QueryResult[];
  aggregatedData: {
    stoerungen: any;
    dispatch: any;
    cutting: any;
  };
  summary: string;
  totalQueries: number;
  successfulQueries: number;
  failedQueries: number;
  errors: string[];
  timestamp: Date;
  queryMetrics: {
    totalTime: number;
    averageTime: number;
    dataTypes: string[];
  };
}

export interface QueryMetrics {
  totalQueries: number;
  successfulQueries: number;
  failedQueries: number;
  averageQueryTime: number;
  cacheHitRate: number;
  lastExecutionTime: Date;
}