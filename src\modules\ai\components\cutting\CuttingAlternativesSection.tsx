/**
 * CuttingAlternativesSection
 *
 * Zeigt die Alternativen-Übersicht oder eine Hinweiskarte,
 * falls noch keine Alternativen vorhanden sind. Präsentations-Component.
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';
import { ChartErrorBoundary } from '@/components/ErrorBoundary';
import { CuttingAlternativesChart } from './CuttingAlternativesChart';
import type { CuttingAlternative, CuttingPlan } from '../../services/types';

interface CuttingAlternativesSectionProps {
  alternatives: CuttingAlternative[];
  onSelectAlternative: (alternative: CuttingAlternative) => void;
}

export function CuttingAlternativesSection({ alternatives, onSelectAlternative }: CuttingAlternativesSectionProps) {
  const hasAlternatives = Array.isArray(alternatives) && alternatives.length > 0;

  return (
    <div className="space-y-6">
      {hasAlternatives ? (
        <ChartErrorBoundary>
          <CuttingAlternativesChart
            alternatives={alternatives}
            onSelectAlternative={onSelectAlternative}
          />
        </ChartErrorBoundary>
      ) : (
        <Card className="border-[#ff7a05]">
          <CardContent className="flex flex-col items-center justify-center h-64">
            <AlertTriangle className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-600">Führen Sie eine Optimierung durch, um Alternativen zu sehen</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default CuttingAlternativesSection;
