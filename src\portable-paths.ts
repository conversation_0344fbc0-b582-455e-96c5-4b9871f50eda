/**
 * Portable-Pfad-Konfiguration für Electron-App
 * 
 * Diese Datei stellt Funktionen bereit, um die Anwendungspfade für eine
 * portable Nutzung zu konfigurieren. Dadurch werden alle Daten relativ
 * zum Anwendungsverzeichnis gespeichert, anstatt in den Standardverzeichnissen
 * des Betriebssystems.
 */

import { app } from 'electron';
import path from 'path';
import fs from 'fs';

/**
 * Konfiguriert die Anwendungspfade für portable Nutzung
 * 
 * @returns Ein Objekt mit den konfigurierten Pfaden
 */
export function setupPortablePaths() {
  // Basisverzeichnis der Anwendung (Verzeichnis der EXE-Datei)
  const appPath = path.dirname(app.getPath('exe'));
  
  // Benutzerdatenverzeichnis im Anwendungsverzeichnis
  const userDataPath = path.join(appPath, 'userData');
  
  // Datenbankverzeichnis im Anwendungsverzeichnis
  const databasePath = path.join(appPath, 'database');
  
  // <PERSON>elle sicher, dass die Verzeichnisse existieren
  if (!fs.existsSync(userDataPath)) {
    fs.mkdirSync(userDataPath, { recursive: true });
    console.log('Benutzerdatenverzeichnis erstellt:', userDataPath);
  }
  
  if (!fs.existsSync(databasePath)) {
    fs.mkdirSync(databasePath, { recursive: true });
    console.log('Datenbankverzeichnis erstellt:', databasePath);
  }
  
  // Konfiguriere die Anwendungspfade
  app.setPath('userData', userDataPath);
  app.setPath('appData', appPath);
  app.setPath('temp', path.join(appPath, 'temp'));
  app.setPath('logs', path.join(appPath, 'logs'));
  
  // Gib die konfigurierten Pfade zurück
  return {
    appPath,
    userDataPath,
    databasePath
  };
}
