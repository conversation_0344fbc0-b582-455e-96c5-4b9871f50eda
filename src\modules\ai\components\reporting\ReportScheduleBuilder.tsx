/**
 * Report Schedule Builder Component
 * 
 * Form component for configuring report scheduling and automation
 */

import React, { useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar, 
  Clock, 
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';
import type { ReportScheduleFormData } from '@/types/reporting';

interface ReportScheduleBuilderProps {
  schedule?: ReportScheduleFormData;
  onChange: (schedule: ReportScheduleFormData | undefined) => void;
}

/**
 * Report Schedule Builder Component
 */
export const ReportScheduleBuilder: React.FC<ReportScheduleBuilderProps> = ({
  schedule,
  onChange
}) => {
  /**
   * Handle enabling/disabling scheduling
   */
  const handleToggleScheduling = useCallback((enabled: boolean) => {
    if (enabled) {
      onChange({
        frequency: 'monthly',
        time: '09:00',
        timezone: 'Europe/Berlin',
        isActive: true
      });
    } else {
      onChange(undefined);
    }
  }, [onChange]);

  /**
   * Handle field changes
   */
  const handleFieldChange = useCallback((field: keyof ReportScheduleFormData, value: any) => {
    if (!schedule) return;

    onChange({
      ...schedule,
      [field]: value
    });
  }, [schedule, onChange]);

  /**
   * Get frequency display name
   */
  const getFrequencyDisplayName = (frequency: string) => {
    const frequencies: Record<string, string> = {
      'daily': 'Täglich',
      'weekly': 'Wöchentlich',
      'monthly': 'Monatlich',
      'quarterly': 'Vierteljährlich'
    };
    return frequencies[frequency] || frequency;
  };

  /**
   * Get day of week name
   */
  const getDayOfWeekName = (day: number) => {
    const days = ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag'];
    return days[day] || `Tag ${day}`;
  };

  /**
   * Calculate next execution time
   */
  const getNextExecutionTime = () => {
    if (!schedule || !schedule.isActive) return null;

    const now = new Date();
    let nextExecution = new Date();

    switch (schedule.frequency) {
      case 'daily':
        nextExecution.setDate(now.getDate() + 1);
        break;
      case 'weekly':
        const daysUntilNext = (schedule.dayOfWeek || 1) - now.getDay();
        nextExecution.setDate(now.getDate() + (daysUntilNext <= 0 ? daysUntilNext + 7 : daysUntilNext));
        break;
      case 'monthly':
        nextExecution.setMonth(now.getMonth() + 1);
        nextExecution.setDate(schedule.dayOfMonth || 1);
        break;
      case 'quarterly':
        nextExecution.setMonth(now.getMonth() + 3);
        nextExecution.setDate(1);
        break;
    }

    // Set time
    const [hours, minutes] = schedule.time.split(':').map(Number);
    nextExecution.setHours(hours, minutes, 0, 0);

    return nextExecution;
  };

  const nextExecution = getNextExecutionTime();

  return (
    <div className="space-y-6">
      {/* Enable/Disable Scheduling */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Automatische Berichtsgenerierung
          </CardTitle>
          <CardDescription>
            Konfigurieren Sie die automatische Generierung und Verteilung von Berichten.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Switch
              id="enableScheduling"
              checked={!!schedule}
              onCheckedChange={handleToggleScheduling}
            />
            <Label htmlFor="enableScheduling">
              Automatische Generierung aktivieren
            </Label>
          </div>

          {!schedule && (
            <Alert className="mt-4">
              <Info className="h-4 w-4" />
              <AlertDescription>
                Berichte werden nur manuell generiert. Aktivieren Sie die automatische Generierung, 
                um Berichte nach einem Zeitplan zu erstellen und zu versenden.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Schedule Configuration */}
      {schedule && (
        <>
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                Zeitplan konfigurieren
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="frequency">Häufigkeit</Label>
                  <Select 
                    value={schedule.frequency} 
                    onValueChange={(value) => handleFieldChange('frequency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">Täglich</SelectItem>
                      <SelectItem value="weekly">Wöchentlich</SelectItem>
                      <SelectItem value="monthly">Monatlich</SelectItem>
                      <SelectItem value="quarterly">Vierteljährlich</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="time">Uhrzeit</Label>
                  <Input
                    id="time"
                    type="time"
                    value={schedule.time}
                    onChange={(e) => handleFieldChange('time', e.target.value)}
                  />
                </div>
              </div>

              {/* Weekly specific options */}
              {schedule.frequency === 'weekly' && (
                <div className="space-y-2">
                  <Label htmlFor="dayOfWeek">Wochentag</Label>
                  <Select 
                    value={schedule.dayOfWeek?.toString()} 
                    onValueChange={(value) => handleFieldChange('dayOfWeek', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Montag</SelectItem>
                      <SelectItem value="2">Dienstag</SelectItem>
                      <SelectItem value="3">Mittwoch</SelectItem>
                      <SelectItem value="4">Donnerstag</SelectItem>
                      <SelectItem value="5">Freitag</SelectItem>
                      <SelectItem value="6">Samstag</SelectItem>
                      <SelectItem value="0">Sonntag</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Monthly specific options */}
              {schedule.frequency === 'monthly' && (
                <div className="space-y-2">
                  <Label htmlFor="dayOfMonth">Tag des Monats</Label>
                  <Select 
                    value={schedule.dayOfMonth?.toString()} 
                    onValueChange={(value) => handleFieldChange('dayOfMonth', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                        <SelectItem key={day} value={day.toString()}>
                          {day}. Tag
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="timezone">Zeitzone</Label>
                <Select 
                  value={schedule.timezone} 
                  onValueChange={(value) => handleFieldChange('timezone', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Europe/Berlin">Europa/Berlin (MEZ)</SelectItem>
                    <SelectItem value="Europe/London">Europa/London (GMT)</SelectItem>
                    <SelectItem value="America/New_York">Amerika/New York (EST)</SelectItem>
                    <SelectItem value="America/Los_Angeles">Amerika/Los Angeles (PST)</SelectItem>
                    <SelectItem value="Asia/Tokyo">Asien/Tokyo (JST)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={schedule.isActive}
                  onCheckedChange={(checked) => handleFieldChange('isActive', checked)}
                />
                <Label htmlFor="isActive">Zeitplan aktivieren</Label>
              </div>
            </CardContent>
          </Card>

          {/* Schedule Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Zeitplan-Zusammenfassung</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status:</span>
                  <Badge variant={schedule.isActive ? "default" : "secondary"}>
                    {schedule.isActive ? (
                      <>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Aktiv
                      </>
                    ) : (
                      <>
                        <XCircle className="h-3 w-3 mr-1" />
                        Inaktiv
                      </>
                    )}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Häufigkeit:</span>
                  <span className="text-sm">{getFrequencyDisplayName(schedule.frequency)}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Uhrzeit:</span>
                  <span className="text-sm">{schedule.time} Uhr</span>
                </div>

                {schedule.frequency === 'weekly' && schedule.dayOfWeek !== undefined && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Wochentag:</span>
                    <span className="text-sm">{getDayOfWeekName(schedule.dayOfWeek)}</span>
                  </div>
                )}

                {schedule.frequency === 'monthly' && schedule.dayOfMonth && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Tag des Monats:</span>
                    <span className="text-sm">{schedule.dayOfMonth}.</span>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Zeitzone:</span>
                  <span className="text-sm">{schedule.timezone}</span>
                </div>

                {nextExecution && schedule.isActive && (
                  <div className="flex items-center justify-between pt-2 border-t">
                    <span className="text-sm font-medium">Nächste Ausführung:</span>
                    <span className="text-sm font-medium text-blue-600">
                      {nextExecution.toLocaleString('de-DE')}
                    </span>
                  </div>
                )}
              </div>

              {schedule.isActive && (
                <Alert className="mt-4">
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Der Bericht wird automatisch {getFrequencyDisplayName(schedule.frequency).toLowerCase()} 
                    um {schedule.time} Uhr generiert und an die konfigurierten Empfänger versendet.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default ReportScheduleBuilder;