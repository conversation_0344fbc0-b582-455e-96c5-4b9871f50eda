/**
 * Alert Management Panel Component
 * 
 * Displays and manages active alerts with German localization.
 */

import React, { useState } from 'react';
import { AlertTriangle, CheckCircle, Clock, X, Eye, Settings } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import type { Alert } from '@/types/predictive-analytics';
import { cn } from '@/lib/utils';

interface AlertManagementPanelProps {
  alerts: Alert[];
  loading: boolean;
  onAlertAction: (alertId: string, action: 'acknowledge' | 'resolve' | 'dismiss') => void;
  className?: string;
}

/**
 * Alert Management Panel Component
 */
export function AlertManagementPanel({ 
  alerts, 
  loading, 
  onAlertAction, 
  className 
}: AlertManagementPanelProps) {
  const [selectedAlert, setSelectedAlert] = useState<Alert | null>(null);
  const [actionNote, setActionNote] = useState('');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');

  // Filter alerts by severity
  const filteredAlerts = alerts.filter(alert => 
    filterSeverity === 'all' || alert.severity === filterSeverity
  );

  // Sort alerts by severity and timestamp
  const sortedAlerts = [...filteredAlerts].sort((a, b) => {
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
    if (severityDiff !== 0) return severityDiff;
    return new Date(b.triggered_at).getTime() - new Date(a.triggered_at).getTime();
  });

  // Get severity color and icon
  const getSeverityDisplay = (severity: Alert['severity']) => {
    switch (severity) {
      case 'critical':
        return {
          color: 'bg-red-500 text-white',
          icon: <AlertTriangle className="h-3 w-3" />,
          label: 'Kritisch'
        };
      case 'high':
        return {
          color: 'bg-orange-500 text-white',
          icon: <AlertTriangle className="h-3 w-3" />,
          label: 'Hoch'
        };
      case 'medium':
        return {
          color: 'bg-yellow-500 text-black',
          icon: <Clock className="h-3 w-3" />,
          label: 'Mittel'
        };
      case 'low':
        return {
          color: 'bg-blue-500 text-white',
          icon: <Clock className="h-3 w-3" />,
          label: 'Niedrig'
        };
    }
  };

  // Get status display
  const getStatusDisplay = (status: Alert['status']) => {
    switch (status) {
      case 'active':
        return { color: 'bg-red-100 text-red-800', label: 'Aktiv' };
      case 'acknowledged':
        return { color: 'bg-yellow-100 text-yellow-800', label: 'Bestätigt' };
      case 'resolved':
        return { color: 'bg-green-100 text-green-800', label: 'Gelöst' };
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle alert action
  const handleAlertAction = (alert: Alert, action: 'acknowledge' | 'resolve' | 'dismiss') => {
    onAlertAction(alert.id, action);
    setSelectedAlert(null);
    setActionNote('');
  };

  if (loading) {
    return (
      <Card className={cn("h-96", className)}>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent className="space-y-3">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="flex items-center space-x-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-3 w-2/3" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("h-96", className)} data-testid="alert-management-panel">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">
            Aktive Alarme ({sortedAlerts.length})
          </CardTitle>
          <div className="flex items-center gap-2">
            <select
              value={filterSeverity}
              onChange={(e) => setFilterSeverity(e.target.value)}
              className="px-2 py-1 border rounded text-sm"
            >
              <option value="all">Alle</option>
              <option value="critical">Kritisch</option>
              <option value="high">Hoch</option>
              <option value="medium">Mittel</option>
              <option value="low">Niedrig</option>
            </select>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-80">
          {sortedAlerts.length === 0 ? (
            <div className="flex items-center justify-center h-full p-6">
              <div className="text-center">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
                <p className="text-muted-foreground">
                  Keine aktiven Alarme
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-2 p-4">
              {sortedAlerts.map((alert) => {
                const severityDisplay = getSeverityDisplay(alert.severity);
                const statusDisplay = getStatusDisplay(alert.status);
                
                return (
                  <div
                    key={alert.id}
                    className="border rounded-lg p-3 hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge className={severityDisplay.color}>
                            {severityDisplay.icon}
                            <span className="ml-1">{severityDisplay.label}</span>
                          </Badge>
                          <Badge variant="outline" className={statusDisplay.color}>
                            {statusDisplay.label}
                          </Badge>
                        </div>
                        
                        <h4 className="font-medium text-sm mb-1 truncate">
                          {alert.title}
                        </h4>
                        
                        <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                          {alert.message}
                        </p>
                        
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>KPI: {alert.kpi_id}</span>
                          <span>Wert: {alert.current_value?.toFixed(2) || 'N/A'}</span>
                          <span>{formatTimestamp(alert.triggered_at)}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1 ml-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedAlert(alert)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Alarm Details</DialogTitle>
                            </DialogHeader>
                            {selectedAlert && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <label className="text-sm font-medium">Titel</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedAlert.title}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Schweregrad</label>
                                    <p className="text-sm text-muted-foreground">
                                      {getSeverityDisplay(selectedAlert.severity).label}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">KPI</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedAlert.kpi_id}
                                    </p>
                                  </div>
                                  <div>
                                    <label className="text-sm font-medium">Aktueller Wert</label>
                                    <p className="text-sm text-muted-foreground">
                                      {selectedAlert.current_value?.toFixed(2) || 'N/A'}
                                    </p>
                                  </div>
                                </div>
                                
                                <div>
                                  <label className="text-sm font-medium">Nachricht</label>
                                  <p className="text-sm text-muted-foreground mt-1">
                                    {selectedAlert.message}
                                  </p>
                                </div>
                                
                                <div>
                                  <label className="text-sm font-medium">Ausgelöst am</label>
                                  <p className="text-sm text-muted-foreground">
                                    {formatTimestamp(selectedAlert.triggered_at)}
                                  </p>
                                </div>
                                
                                {selectedAlert.status === 'active' && (
                                  <div className="space-y-3">
                                    <div>
                                      <label className="text-sm font-medium">Notiz (optional)</label>
                                      <Textarea
                                        value={actionNote}
                                        onChange={(e) => setActionNote(e.target.value)}
                                        placeholder="Notiz zur Aktion hinzufügen..."
                                        className="mt-1"
                                      />
                                    </div>
                                    
                                    <div className="flex gap-2">
                                      <Button
                                        onClick={() => handleAlertAction(selectedAlert, 'acknowledge')}
                                        variant="outline"
                                        size="sm"
                                      >
                                        Bestätigen
                                      </Button>
                                      <Button
                                        onClick={() => handleAlertAction(selectedAlert, 'resolve')}
                                        variant="default"
                                        size="sm"
                                      >
                                        Lösen
                                      </Button>
                                      <Button
                                        onClick={() => handleAlertAction(selectedAlert, 'dismiss')}
                                        variant="destructive"
                                        size="sm"
                                      >
                                        Verwerfen
                                      </Button>
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                        
                        {alert.status === 'active' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAlertAction(alert, 'acknowledge')}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

export default AlertManagementPanel;