import { IService } from '@/services/base.service';
import { DocumentationSection, Tutorial } from '../../components/documentation/AIDocumentationProvider';

export interface DocumentationSearchOptions {
  category?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  tags?: string[];
  limit?: number;
}

export interface DocumentationMetrics {
  totalSections: number;
  totalTutorials: number;
  completionRate: number;
  popularSections: string[];
  searchQueries: string[];
}

export interface DocumentationValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export class AIDocumentationService implements IService {
  readonly serviceName = 'AIDocumentationService';

  private sections: DocumentationSection[] = [];
  private tutorials: Tutorial[] = [];
  private searchHistory: string[] = [];
  private completionStats: Map<string, number> = new Map();

  async initialize(): Promise<void> {
    await this.loadDocumentationData();
    await this.loadTutorialData();
  }

  async cleanup(): Promise<void> {
    this.sections = [];
    this.tutorials = [];
    this.searchHistory = [];
    this.completionStats.clear();
  }

  // Documentation Management
  async getAllSections(): Promise<DocumentationSection[]> {
    return this.sections;
  }

  async getSectionById(id: string): Promise<DocumentationSection | null> {
    return this.sections.find(section => section.id === id) || null;
  }

  async getSectionsByCategory(category: string): Promise<DocumentationSection[]> {
    return this.sections.filter(section => section.category === category);
  }

  async searchDocumentation(
    query: string,
    options: DocumentationSearchOptions = {}
  ): Promise<DocumentationSection[]> {
    this.searchHistory.push(query);

    let results = this.sections;

    // Filter by category
    if (options.category) {
      results = results.filter(section => section.category === options.category);
    }

    // Filter by difficulty
    if (options.difficulty) {
      results = results.filter(section => section.difficulty === options.difficulty);
    }

    // Filter by tags
    if (options.tags && options.tags.length > 0) {
      results = results.filter(section =>
        options.tags!.some(tag => section.tags.includes(tag))
      );
    }

    // Text search
    const lowercaseQuery = query.toLowerCase();
    results = results.filter(section =>
      section.title.toLowerCase().includes(lowercaseQuery) ||
      section.content.toLowerCase().includes(lowercaseQuery) ||
      section.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );

    // Sort by relevance (simple scoring)
    results.sort((a, b) => {
      const scoreA = this.calculateRelevanceScore(a, query);
      const scoreB = this.calculateRelevanceScore(b, query);
      return scoreB - scoreA;
    });

    // Apply limit
    if (options.limit) {
      results = results.slice(0, options.limit);
    }

    return results;
  }

  // Tutorial Management
  async getAllTutorials(): Promise<Tutorial[]> {
    return this.tutorials;
  }

  async getTutorialById(id: string): Promise<Tutorial | null> {
    return this.tutorials.find(tutorial => tutorial.id === id) || null;
  }

  async getTutorialsByCategory(category: string): Promise<Tutorial[]> {
    return this.tutorials.filter(tutorial => tutorial.category === category);
  }

  async getTutorialsByDifficulty(difficulty: string): Promise<Tutorial[]> {
    return this.tutorials.filter(tutorial => tutorial.difficulty === difficulty);
  }

  async getRecommendedTutorials(
    userLevel: string,
    completedTutorials: string[]
  ): Promise<Tutorial[]> {
    return this.tutorials
      .filter(tutorial => !completedTutorials.includes(tutorial.id))
      .filter(tutorial => {
        // Recommend based on user level
        if (userLevel === 'beginner') {
          return tutorial.difficulty === 'beginner';
        } else if (userLevel === 'intermediate') {
          return ['beginner', 'intermediate'].includes(tutorial.difficulty);
        }
        return true; // Advanced users can see all
      })
      .sort((a, b) => {
        // Sort by difficulty and estimated time
        const difficultyOrder = { beginner: 1, intermediate: 2, advanced: 3 };
        const diffA = difficultyOrder[a.difficulty as keyof typeof difficultyOrder];
        const diffB = difficultyOrder[b.difficulty as keyof typeof difficultyOrder];

        if (diffA !== diffB) {
          return diffA - diffB;
        }

        return a.estimatedTime - b.estimatedTime;
      })
      .slice(0, 5); // Return top 5 recommendations
  }

  // Analytics and Metrics
  async getDocumentationMetrics(): Promise<DocumentationMetrics> {
    const totalCompletions = Array.from(this.completionStats.values())
      .reduce((sum, count) => sum + count, 0);

    const completionRate = this.tutorials.length > 0
      ? totalCompletions / this.tutorials.length
      : 0;

    const popularSections = this.sections
      .sort((a, b) => (this.completionStats.get(b.id) || 0) - (this.completionStats.get(a.id) || 0))
      .slice(0, 5)
      .map(section => section.title);

    const recentSearches = this.searchHistory.slice(-10);

    return {
      totalSections: this.sections.length,
      totalTutorials: this.tutorials.length,
      completionRate,
      popularSections,
      searchQueries: recentSearches
    };
  }

  async trackTutorialCompletion(tutorialId: string): Promise<void> {
    const currentCount = this.completionStats.get(tutorialId) || 0;
    this.completionStats.set(tutorialId, currentCount + 1);
  }

  async trackSectionView(sectionId: string): Promise<void> {
    const currentCount = this.completionStats.get(sectionId) || 0;
    this.completionStats.set(sectionId, currentCount + 1);
  }

  // Documentation Validation
  async validateDocumentation(): Promise<DocumentationValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Check for missing required fields
    this.sections.forEach(section => {
      if (!section.title.trim()) {
        errors.push(`Section ${section.id} is missing a title`);
      }
      if (!section.content.trim()) {
        errors.push(`Section ${section.id} is missing content`);
      }
      if (section.tags.length === 0) {
        warnings.push(`Section ${section.id} has no tags`);
      }
    });

    this.tutorials.forEach(tutorial => {
      if (!tutorial.title.trim()) {
        errors.push(`Tutorial ${tutorial.id} is missing a title`);
      }
      if (tutorial.steps.length === 0) {
        errors.push(`Tutorial ${tutorial.id} has no steps`);
      }
      if (tutorial.estimatedTime <= 0) {
        warnings.push(`Tutorial ${tutorial.id} has invalid estimated time`);
      }
    });

    // Check for broken links or references
    const allIds = new Set([
      ...this.sections.map(s => s.id),
      ...this.tutorials.map(t => t.id)
    ]);

    this.sections.forEach(section => {
      const linkMatches = section.content.match(/\[([^\]]+)\]\(#([^)]+)\)/g);
      if (linkMatches) {
        linkMatches.forEach(match => {
          const id = match.match(/\(#([^)]+)\)/)?.[1];
          if (id && !allIds.has(id)) {
            warnings.push(`Section ${section.id} contains broken link to ${id}`);
          }
        });
      }
    });

    // Suggestions for improvement
    if (this.sections.length < 10) {
      suggestions.push('Consider adding more documentation sections for better coverage');
    }
    if (this.tutorials.length < 5) {
      suggestions.push('Consider adding more tutorials for different user levels');
    }

    const beginnerTutorials = this.tutorials.filter(t => t.difficulty === 'beginner');
    if (beginnerTutorials.length === 0) {
      suggestions.push('Add beginner-level tutorials to help new users');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  // Content Generation Helpers
  async generateContextualHelp(
    currentPage: string,
    userRole: string,
    availableFeatures: string[]
  ): Promise<DocumentationSection[]> {
    const relevantSections = this.sections.filter(section => {
      const pageRelevant = section.tags.includes(currentPage);
      const roleRelevant = section.tags.includes(userRole);
      const featureRelevant = availableFeatures.some(feature =>
        section.tags.includes(feature)
      );

      return pageRelevant || roleRelevant || featureRelevant;
    });

    return relevantSections
      .sort((a, b) => this.calculateContextualRelevance(a, currentPage, userRole) -
        this.calculateContextualRelevance(b, currentPage, userRole))
      .slice(0, 5);
  }

  // Private helper methods
  private calculateRelevanceScore(section: DocumentationSection, query: string): number {
    const lowercaseQuery = query.toLowerCase();
    let score = 0;

    // Title match (highest weight)
    if (section.title.toLowerCase().includes(lowercaseQuery)) {
      score += 10;
    }

    // Content match
    const contentMatches = (section.content.toLowerCase().match(new RegExp(lowercaseQuery, 'g')) || []).length;
    score += contentMatches * 2;

    // Tag match
    const tagMatches = section.tags.filter(tag =>
      tag.toLowerCase().includes(lowercaseQuery)
    ).length;
    score += tagMatches * 5;

    return score;
  }

  private calculateContextualRelevance(
    section: DocumentationSection,
    currentPage: string,
    userRole: string
  ): number {
    let score = 0;

    if (section.tags.includes(currentPage)) score += 10;
    if (section.tags.includes(userRole)) score += 5;

    // Prefer more recent content
    const daysSinceUpdate = (Date.now() - section.lastUpdated.getTime()) / (1000 * 60 * 60 * 24);
    score += Math.max(0, 30 - daysSinceUpdate);

    return score;
  }

  private async loadDocumentationData(): Promise<void> {
    // Load documentation sections from various sources
    this.sections = [
      {
        id: 'ai-overview',
        title: 'AI Module Overview',
        content: 'The AI Module provides intelligent automation and optimization capabilities across all departments...',
        category: 'overview',
        tags: ['ai', 'overview', 'getting-started'],
        lastUpdated: new Date(),
        difficulty: 'beginner'
      },
      {
        id: 'rag-integration',
        title: 'RAG Integration Guide',
        content: 'Retrieval-Augmented Generation (RAG) enhances AI responses with contextual information...',
        category: 'features',
        tags: ['rag', 'ai', 'search', 'context'],
        lastUpdated: new Date(),
        difficulty: 'intermediate'
      },
      {
        id: 'cutting-optimization',
        title: 'Cutting Optimization',
        content: 'The cutting optimization service uses advanced algorithms to minimize waste...',
        category: 'features',
        tags: ['cutting', 'optimization', 'waste-reduction'],
        lastUpdated: new Date(),
        difficulty: 'intermediate'
      },
      {
        id: 'inventory-intelligence',
        title: 'Inventory Intelligence',
        content: 'AI-powered inventory management with predictive analytics and automated recommendations...',
        category: 'features',
        tags: ['inventory', 'prediction', 'analytics'],
        lastUpdated: new Date(),
        difficulty: 'intermediate'
      },
      {
        id: 'api-reference',
        title: 'AI Services API Reference',
        content: 'Complete API documentation for all AI services and their endpoints...',
        category: 'api',
        tags: ['api', 'reference', 'development'],
        lastUpdated: new Date(),
        difficulty: 'advanced'
      }
    ];
  }

  private async loadTutorialData(): Promise<void> {
    // Load tutorial data
    this.tutorials = [
      {
        id: 'getting-started',
        title: 'Getting Started with AI Module',
        description: 'Learn the basics of using the AI module and its core features',
        category: 'basics',
        estimatedTime: 15,
        difficulty: 'beginner',
        prerequisites: [],
        steps: [
          {
            id: 'step-1',
            title: 'Access AI Module',
            content: 'Navigate to the AI module from the main dashboard',
            action: {
              type: 'navigate',
              value: '#/ai'
            }
          },
          {
            id: 'step-2',
            title: 'Explore Dashboard',
            content: 'Familiarize yourself with the AI dashboard layout and available services',
            action: {
              type: 'click',
              target: '.ai-dashboard'
            }
          }
        ]
      },
      {
        id: 'rag-tutorial',
        title: 'Using RAG for Enhanced Responses',
        description: 'Learn how to leverage RAG for more contextual AI responses',
        category: 'features',
        estimatedTime: 25,
        difficulty: 'intermediate',
        prerequisites: ['getting-started'],
        steps: [
          {
            id: 'rag-step-1',
            title: 'Open RAG Service',
            content: 'Navigate to the RAG service interface',
            action: {
              type: 'click',
              target: '[data-testid="rag-service"]'
            }
          },
          {
            id: 'rag-step-2',
            title: 'Submit Query',
            content: 'Enter a query to see RAG-enhanced responses',
            action: {
              type: 'input',
              target: 'input[name="query"]',
              value: 'Show me cutting efficiency trends'
            }
          }
        ]
      }
    ];
  }
}