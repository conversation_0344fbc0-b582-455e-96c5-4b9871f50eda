import React from 'react';
import { FlipCard } from '@/components/ui/flip-card';
import { SystemStatus, SystemStatusMessage } from '@/types/stoerungen.types';
import { cn } from '@/lib/utils';
import { Activity, AlertTriangle, CheckCircle, XCircle, Power, ArrowLeft } from 'lucide-react';

interface SystemStatusFlipCardProps {
  system: SystemStatus;
  className?: string;
  backgroundStyle?: React.CSSProperties;
  onFlip?: (isFlipped: boolean) => void;
  formatLastCheck: (timestamp: string) => string;
  getStatusColor: (status: string) => string;
}

export const SystemStatusFlipCard: React.FC<SystemStatusFlipCardProps> = ({
  system,
  className,
  backgroundStyle,
  onFlip,
  formatLastCheck,
  getStatusColor
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OK':
        return <CheckCircle className="h-6 w-6 text-Green-600" />;
      case 'WARNING':
        return <AlertTriangle className="h-6 w-6 text-Yellow-600" />;
      case 'ERROR':
        return <XCircle className="h-6 w-6 text-Red-600" />;
      case 'OFF':
        return <Power className="h-6 w-6 text-Gray-600" />;
      default:
        return <Activity className="h-6 w-6 text-white" />;
    }
  };

  const getRandomStatusMessage = (messages?: SystemStatusMessage[]): SystemStatusMessage | null => {
    if (!messages || messages.length === 0) return null;
    
    // Return random message from available ones
    const randomIndex = Math.floor(Math.random() * messages.length);
    return messages[randomIndex];
  };

  const statusMessage = getRandomStatusMessage(system.statusMessages);

  // Front side content (original card display)
  const frontContent = (
    <div
      className={cn(
        "relative p-1 rounded-lg h-full",
        getStatusColor(system.status),
        system.status === 'ERROR' ? 'animate-pulse' : ''
      )}
      style={backgroundStyle}
      title={`${system.system_name}: ${system.status} (${formatLastCheck(system.last_check)})`}
    >
      <div className="flex flex-col items-start justify-center text-black min-h-[40px] rounded-lg p-1">
        {getStatusIcon(system.status)}
        <span className="text-sm font-medium mt-0.5 text-left leading-tight">
          {system.system_name}
        </span>
        <span className="text-xs opacity-80 mt-0.5 text-left">
          {formatLastCheck(system.last_check)}
        </span>
      </div>
    </div>
  );

  // Back side content (detailed status information)
  const backContent = (
    <div
      className={cn(
        "relative p-2 rounded-lg h-full flex flex-col justify-between",
        getStatusColor(system.status)
      )}
    >
      {/* Header with back button indicator */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-1">
          {getStatusIcon(system.status)}
          <span className="text-xs font-medium text-black opacity-80">
            Details
          </span>
        </div>
        <ArrowLeft className="h-3 w-3 text-black opacity-60" />
      </div>

      {/* System name */}
      <div className="text-xs font-semibold text-black mb-1 leading-tight">
        {system.system_name}
      </div>

      {/* Status message or fallback */}
      <div className="flex-1 flex flex-col justify-center">
        {statusMessage ? (
          <div className="text-center">
            <div className="text-sm font-bold text-black mb-1">
              {statusMessage.title}
            </div>
            <div className="text-xs text-black opacity-90 leading-tight">
              {statusMessage.description}
            </div>
          </div>
        ) : (
          <div className="text-center">
            <div className="text-sm font-bold text-black mb-1">
              {system.status === 'OK' && 'System Läuft'}
              {system.status === 'WARNING' && 'Achtung'}
              {system.status === 'ERROR' && 'Fehler'}
              {system.status === 'OFF' && 'Offline'}
            </div>
            <div className="text-xs text-black opacity-90">
              {system.status === 'OK' && 'Alle Systeme funktionieren normal'}
              {system.status === 'WARNING' && 'Überwachung erforderlich'}
              {system.status === 'ERROR' && 'Sofortige Aktion erforderlich'}
              {system.status === 'OFF' && 'System ist nicht verfügbar'}
            </div>
          </div>
        )}
      </div>

      {/* Footer with timestamp */}
      <div className="text-xs text-black opacity-70 text-center mt-1">
        {formatLastCheck(system.last_check)}
      </div>
    </div>
  );

  return (
    <FlipCard
      frontContent={frontContent}
      backContent={backContent}
      className={cn(
        "transition-all duration-200 hover:scale-105 min-h-[80px] w-full",
        className
      )}
      width="w-full"
      height="min-h-[80px]"
      onFlip={onFlip}
    />
  );
};