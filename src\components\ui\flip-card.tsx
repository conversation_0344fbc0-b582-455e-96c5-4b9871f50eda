import React, { useState } from 'react';
import { cn } from '@/lib/utils';

interface FlipCardProps {
  children?: React.ReactNode;
  frontContent: React.ReactNode;
  backContent: React.ReactNode;
  className?: string;
  width?: string;
  height?: string;
  disabled?: boolean;
  onFlip?: (isFlipped: boolean) => void;
}

export const FlipCard: React.FC<FlipCardProps> = ({
  frontContent,
  backContent,
  className,
  width = 'w-full',
  height = 'h-full',
  disabled = false,
  onFlip
}) => {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = () => {
    if (disabled) return;
    
    const newFlippedState = !isFlipped;
    setIsFlipped(newFlippedState);
    onFlip?.(newFlippedState);
  };

  return (
    <div 
      className={cn(
        "flip-card-container",
        width,
        height,
        "perspective-1000",
        className
      )}
    >
      <div
        className={cn(
          "flip-card relative transform-style-preserve-3d transition-transform duration-700 ease-in-out cursor-pointer",
          width,
          height,
          isFlipped ? "rotate-y-180" : "",
          disabled && "cursor-not-allowed opacity-50"
        )}
        onClick={handleFlip}
      >
        {/* Front Side */}
        <div
          className={cn(
            "flip-card-front absolute inset-0 backface-hidden",
            width,
            height
          )}
        >
          {frontContent}
        </div>

        {/* Back Side */}
        <div
          className={cn(
            "flip-card-back absolute inset-0 backface-hidden rotate-y-180",
            width,
            height
          )}
          style={{ transform: 'rotateY(180deg) scaleX(-1)' }}
        >
          <div style={{ transform: 'scaleX(-1)' }}>
            {backContent}
          </div>
        </div>
      </div>
    </div>
  );
};

// CSS classes that need to be added to your Tailwind config or CSS file
// Add these to your global CSS:
/*
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}
*/