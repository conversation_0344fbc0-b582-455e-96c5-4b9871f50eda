"use client"

import * as React from "react"
import { Area, AreaChart, CartesianGrid, XAxis } from "recharts"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface TrendData {
  date: string;
  timestamp: string;
  antwortzeit: number;
  erfolgsrate: number;
  cacheTrefferrate: number;
  period: string;
}

interface PerformanceTrendsChartProps {
  timeRange?: '5min' | '1hour' | '24hours' | '7days' | '30days';
  activeMetric?: 'antwortzeit' | 'erfolgsrate' | 'cacheTrefferrate';
}

export function PerformanceTrendsChart({ 
  timeRange: initialTimeRange 
}: PerformanceTrendsChartProps) {
  const [responseTimeData, setResponseTimeData] = React.useState<TrendData[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [activeMetric, setActiveMetric] = React.useState<'antwortzeit' | 'erfolgsrate' | 'cacheTrefferrate'>('antwortzeit');
  const timeRange = initialTimeRange || "7days";


  const chartConfig = {
    antwortzeit: {
      label: "Antwortzeit",
      color: "hsl(var(--chart-1))",
    },
    erfolgsrate: {
      label: "Erfolgsrate", 
      color: "hsl(var(--chart-2))",
    },
    cacheTrefferrate: {
      label: "Cache Trefferrate",
      color: "hsl(var(--chart-3))",
    },
  } satisfies ChartConfig;

  React.useEffect(() => {
    const fetchTrends = async () => {
      setLoading(true);
      try {
        let limit: number;
        
        if (timeRange === '5min') {
          limit = 10; // 10 data points for 5 minutes
        } else if (timeRange === '1hour') {
          limit = 12; // 12 data points for 1 hour (5min intervals)
        } else if (timeRange === '24hours') {
          limit = 24; // 24 data points for 24 hours (1 hour intervals)
        } else if (timeRange === '7days') {
          limit = 7; // 7 data points for 7 days
        } else {
          limit = 30; // 30 data points for 30 days
        }

        // Generate mock trend data based on timeRange
        const now = new Date();
        let intervalMs: number;
        
        if (timeRange === '5min') {
          intervalMs = 30 * 1000; // 30 seconds intervals
        } else if (timeRange === '1hour') {
          intervalMs = 5 * 60 * 1000; // 5 minute intervals
        } else if (timeRange === '24hours') {
          intervalMs = 60 * 60 * 1000; // 1 hour intervals
        } else if (timeRange === '7days') {
          intervalMs = 24 * 60 * 60 * 1000; // 1 day intervals
        } else {
          intervalMs = 24 * 60 * 60 * 1000; // 1 day intervals for 30days
        }

        const mockTrendData = Array.from({ length: limit }, (_, i) => {
          const timestamp = new Date(now.getTime() - (limit - 1 - i) * intervalMs);
          return {
            date: timestamp.toISOString().split('T')[0],
            timestamp: timeRange === '5min' || timeRange === '1hour' ? 
              timestamp.toLocaleTimeString('de-DE', { hour: '2-digit', minute: '2-digit' }) :
              timeRange === '24hours' ?
              timestamp.toLocaleTimeString('de-DE', { hour: '2-digit', minute: '2-digit' }) :
              timestamp.toLocaleDateString('de-DE', { day: '2-digit', month: 'short' }),
            antwortzeit: Math.round(Math.random() * 2000 + 500),
            erfolgsrate: Math.round(Math.random() * 20 + 80),
            cacheTrefferrate: Math.round(Math.random() * 30 + 60),
            period: timeRange
          };
        });

        setResponseTimeData(mockTrendData);
      } catch (error) {
        console.error('Error fetching trends:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTrends();
  }, [timeRange]);

  const getCurrentData = () => {
    return responseTimeData.map(item => ({
      ...item,
      value: item[activeMetric] // Map the selected metric to 'value' for the chart
    }));
  };

  const currentData = getCurrentData();
  // No additional filtering needed since data is already generated for the correct timeRange
  const filteredData = currentData;

  const getMetricInfo = () => {
    switch (activeMetric) {
      case 'antwortzeit':
        return {
          title: 'Antwortzeit Trend',
          description: 'Durchschnittliche Antwortzeit über Zeit',
          unit: 'ms',
          color: '#3b82f6', // Blue
          cssColor: 'rgb(59, 130, 246)',
          isHigherBetter: false
        };
      case 'erfolgsrate':
        return {
          title: 'Erfolgsrate Trend',
          description: 'Erfolgsrate der Anfragen über Zeit',
          unit: '%',
          color: '#10b981', // Green
          cssColor: 'rgb(16, 185, 129)',
          isHigherBetter: true
        };
      case 'cacheTrefferrate':
        return {
          title: 'Cache Trefferrate Trend',
          description: 'Cache-Effizienz über Zeit',
          unit: '%',
          color: '#8b5cf6', // Purple
          cssColor: 'rgb(139, 92, 246)',
          isHigherBetter: true
        };
      default:
        return {
          title: 'Performance Trend',
          description: 'Performance-Metriken über Zeit',
          unit: '',
          color: '#3b82f6',
          cssColor: 'rgb(59, 130, 246)',
          isHigherBetter: true
        };
    }
  };

  const calculateTrend = (data: TrendData[], metric: 'antwortzeit' | 'erfolgsrate' | 'cacheTrefferrate') => {
    if (data.length < 2) return 'stable';

    const firstHalf = data.slice(0, Math.floor(data.length / 2));
    const secondHalf = data.slice(Math.floor(data.length / 2));

    const firstAvg = firstHalf.reduce((sum, item) => sum + item[metric], 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, item) => sum + item[metric], 0) / secondHalf.length;

    const change = ((secondAvg - firstAvg) / firstAvg) * 100;

    if (Math.abs(change) < 5) return 'stable';
    return change > 0 ? 'up' : 'down';
  };

  const metricInfo = getMetricInfo();
  const currentTrend = calculateTrend(responseTimeData, activeMetric);

  const getTrendIcon = (trend: string, isGoodTrend: boolean) => {
    if (trend === 'up') {
      return <TrendingUp className={`h-4 w-4 ${isGoodTrend ? 'text-green-600' : 'text-red-600'}`} />;
    } else if (trend === 'down') {
      return <TrendingDown className={`h-4 w-4 ${isGoodTrend ? 'text-green-600' : 'text-red-600'}`} />;
    } else {
      return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendBadge = (trend: string, isHigherBetter: boolean) => {
    const isGoodTrend = isHigherBetter ? trend === 'up' : trend === 'down';

    return (
      <Badge
        variant={isGoodTrend ? "secondary" : trend === 'stable' ? "outline" : "destructive"}
        className={`flex items-center gap-1 ${isGoodTrend ? "bg-green-100 text-green-800" : ""}`}
      >
        {getTrendIcon(trend, isGoodTrend)}
        {trend === 'up' ? 'Steigend' : trend === 'down' ? 'Fallend' : 'Stabil'}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
          <CardDescription>Lade Trend-Daten...</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader className="flex items-center gap-2 space-y-0 border-b py-5 sm:flex-row">
        <div className="grid flex-1 gap-1">
          <CardTitle className="flex items-center gap-2">
            {metricInfo.title}
            {getTrendBadge(currentTrend, metricInfo.isHigherBetter)}
          </CardTitle>
          <CardDescription>{metricInfo.description}</CardDescription>
          
          {/* Metric Selector */}
          <div className="flex gap-2 mt-4">
            <button
              onClick={() => setActiveMetric('antwortzeit')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${activeMetric === 'antwortzeit'
                ? 'bg-blue-100 text-blue-800 border border-blue-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              Antwortzeit
            </button>
            <button
              onClick={() => setActiveMetric('erfolgsrate')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${activeMetric === 'erfolgsrate'
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              Erfolgsrate
            </button>
            <button
              onClick={() => setActiveMetric('cacheTrefferrate')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${activeMetric === 'cacheTrefferrate'
                ? 'bg-purple-100 text-purple-800 border border-purple-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              Cache Trefferrate
            </button>
          </div>
        </div>

      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[150px] w-full"
        >
          <AreaChart data={filteredData}>
            <defs>
              <linearGradient id="fillValue" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor={metricInfo.cssColor}
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor={metricInfo.cssColor}
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="timestamp"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => value} // Use pre-formatted timestamp
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => value} // Use pre-formatted timestamp
                  formatter={(value) => [`${value}${metricInfo.unit}`, metricInfo.title]}
                  indicator="dot"
                />
              }
            />
            <Area
              dataKey="value"
              type="natural"
              fill="url(#fillValue)"
              stroke={metricInfo.cssColor}
              strokeWidth={2}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}