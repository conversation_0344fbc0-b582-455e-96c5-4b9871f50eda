/**
 * RAGSettingsService - Service for managing RAG configuration settings
 * 
 * Handles CRUD operations for RAG settings stored in the database
 * Updated: RAG Prisma Client integration
 */

import { PrismaClient as RAGPrismaClient } from '@prisma-rag/client';

// RAG Settings Interface matching the frontend component
export interface RAGModuleSettings {
  vectorDatabase: {
    dimensions: number;
    similarityThreshold: number;
    maxResults: number;
    enableCache: boolean;
    cacheTTL: number;
  };
  security: {
    enableRateLimit: boolean;
    rateLimitRequests: number;
    rateLimitWindow: number;
    enableInputValidation: boolean;
    enableAuditLogging: boolean;
  };
  services: {
    embedding: {
      model: string;
      batchSize: number;
      enableCache: boolean;
    };
    rag: {
      contextLength: number;
      enableSourceCitation: boolean;
      confidenceThreshold: number;
    };
  };
}

// Default settings matching frontend defaults
const DEFAULT_RAG_SETTINGS: RAGModuleSettings = {
  vectorDatabase: {
    dimensions: 1536,
    similarityThreshold: 0.7,
    maxResults: 10,
    enableCache: true,
    cacheTTL: 3600000
  },
  security: {
    enableRateLimit: true,
    rateLimitRequests: 100,
    rateLimitWindow: 3600000,
    enableInputValidation: true,
    enableAuditLogging: true
  },
  services: {
    embedding: {
      model: 'text-embedding-3-small',
      batchSize: 100,
      enableCache: true
    },
    rag: {
      contextLength: 8000,
      enableSourceCitation: true,
      confidenceThreshold: 0.8
    }
  }
};

export class RAGSettingsService {
  private ragPrisma: RAGPrismaClient;

  constructor() {
    this.ragPrisma = new RAGPrismaClient();
  }

  /**
   * Get or create default knowledge base
   */
  private async getOrCreateDefaultKnowledgeBase(): Promise<string> {
    try {
      let knowledgeBase = await this.ragPrisma.knowledgeBase.findFirst({
        where: {
          name: 'default'
        }
      });

      if (!knowledgeBase) {
        knowledgeBase = await this.ragPrisma.knowledgeBase.create({
          data: {
            name: 'default',
            description: 'Default knowledge base for RAG settings',
            isActive: true
          }
        });
      }

      return knowledgeBase.id;
    } catch (error) {
      console.error('[RAGSettingsService] Error getting/creating default knowledge base:', error);
      throw error;
    }
  }

  /**
   * Get active RAG settings for a user
   */
  async getActiveSettings(userId?: string): Promise<RAGModuleSettings> {
    try {
      const settings = await this.ragPrisma.rAGSettings.findFirst({
        where: {
          userId: userId || null,
          isActive: true
        },
        orderBy: {
          updatedAt: 'desc'
        }
      });

      if (!settings) {
        return await this.createDefaultSettings(userId);
      }

      return this.parseSettingsFromDatabase(settings);
    } catch (error) {
      console.error('Error getting RAG settings:', error);
      // Return default settings as fallback
      return DEFAULT_RAG_SETTINGS;
    }
  }

  /**
   * Save RAG settings to database
   */
  async saveSettings(
    settings: RAGModuleSettings,
    userId?: string,
    name: string = 'default'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Deactivate existing settings
      await this.ragPrisma.rAGSettings.updateMany({
        where: {
          userId: userId || null,
          isActive: true
        },
        data: {
          isActive: false
        }
      });

      // Get or create default knowledge base
      const knowledgeBaseId = await this.getOrCreateDefaultKnowledgeBase();

      // Create new settings
      await this.ragPrisma.rAGSettings.create({
        data: {
          name,
          userId: userId || null,
          vectorDatabaseConfig: JSON.stringify(settings.vectorDatabase),
          securityConfig: JSON.stringify(settings.security),
          servicesConfig: JSON.stringify(settings.services),
          isActive: true,
          version: '1.0.0',
          knowledgeBaseId
        }
      });

      return { success: true };
    } catch (error) {
      console.error('[RAGSettingsService] Error saving settings:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Get all settings configurations
   */
  async getAllSettings(userId?: string): Promise<Array<{
    id: string;
    name: string;
    description: string | null;
    isActive: boolean;
    version: string;
    createdAt: Date;
    updatedAt: Date;
  }>> {
    try {
      const settings = await this.ragPrisma.rAGSettings.findMany({
        where: {
          userId: userId || null
        },
        orderBy: {
          updatedAt: 'desc'
        },
        select: {
          id: true,
          name: true,
          description: true,
          isActive: true,
          version: true,
          createdAt: true,
          updatedAt: true
        }
      });

      return settings;
    } catch (error) {
      console.error('[RAGSettingsService] Error getting all settings:', error);
      return [];
    }
  }

  /**
   * Delete settings configuration
   */
  async deleteSettings(settingsId: string): Promise<{ success: boolean; error?: string }> {
    try {
      await this.ragPrisma.rAGSettings.delete({
        where: {
          id: settingsId
        }
      });

      return { success: true };
    } catch (error) {
      console.error('[RAGSettingsService] Error deleting settings:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Create default settings in database
   */
  private async createDefaultSettings(userId?: string): Promise<RAGModuleSettings> {
    try {
      // Get or create default knowledge base
      const knowledgeBaseId = await this.getOrCreateDefaultKnowledgeBase();

      await this.ragPrisma.rAGSettings.create({
        data: {
          name: 'Default Settings',
          userId: userId || null,
          vectorDatabaseConfig: JSON.stringify(DEFAULT_RAG_SETTINGS.vectorDatabase),
          securityConfig: JSON.stringify(DEFAULT_RAG_SETTINGS.security),
          servicesConfig: JSON.stringify(DEFAULT_RAG_SETTINGS.services),
          isActive: true,
          version: '1.0.0',
          knowledgeBaseId
        }
      });

      return DEFAULT_RAG_SETTINGS;
    } catch (error) {
      console.error('[RAGSettingsService] Error creating default settings:', error);
      return DEFAULT_RAG_SETTINGS;
    }
  }

  /**
   * Parse settings from database format to application format
   */
  private parseSettingsFromDatabase(dbSettings: any): RAGModuleSettings {
    try {
      return {
        vectorDatabase: JSON.parse(dbSettings.vectorDatabaseConfig),
        security: JSON.parse(dbSettings.securityConfig),
        services: JSON.parse(dbSettings.servicesConfig)
      };
    } catch (error) {
      console.error('[RAGSettingsService] Error parsing settings from database:', error);
      return DEFAULT_RAG_SETTINGS;
    }
  }

  /**
   * Cleanup database connection
   */
  async disconnect(): Promise<void> {
    await this.ragPrisma.$disconnect();
  }
}

export default RAGSettingsService;