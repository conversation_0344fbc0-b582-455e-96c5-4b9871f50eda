/**
 * Report Template Manager Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { ReportTemplateManager } from '../ReportTemplateManager';
import type { ReportTemplate } from '@/types/reporting';

// Mock the UI components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children, className, onClick }: any) => (
    <div data-testid="card" className={className} onClick={onClick}>
      {children}
    </div>
  ),
  CardContent: ({ children }: any) => <div data-testid="card-content">{children}</div>,
  CardDescription: ({ children }: any) => <div data-testid="card-description">{children}</div>,
  CardHeader: ({ children }: any) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children }: any) => <div data-testid="card-title">{children}</div>
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, variant, size, ...props }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      data-variant={variant}
      data-size={size}
      {...props}
    >
      {children}
    </button>
  )
}));

vi.mock('@/components/ui/input', () => ({
  Input: ({ value, onChange, placeholder, className, ...props }: any) => (
    <input 
      value={value} 
      onChange={onChange} 
      placeholder={placeholder}
      className={className}
      {...props} 
    />
  )
}));

vi.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <select value={value} onChange={(e) => onValueChange(e.target.value)}>
      {children}
    </select>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ value, children }: any) => <option value={value}>{children}</option>,
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, variant }: any) => (
    <span data-testid="badge" data-variant={variant}>{children}</span>
  )
}));

vi.mock('@/components/ui/alert', () => ({
  Alert: ({ children, className }: any) => (
    <div data-testid="alert" className={className}>{children}</div>
  ),
  AlertDescription: ({ children }: any) => <div data-testid="alert-description">{children}</div>
}));

describe('ReportTemplateManager', () => {
  const mockOnSelectTemplate = vi.fn();
  const mockOnUpdateTemplate = vi.fn();
  const mockOnDeleteTemplate = vi.fn();
  const mockOnRefresh = vi.fn();

  const mockTemplates: ReportTemplate[] = [
    {
      id: 'template-1',
      name: 'Monthly KPI Report',
      description: 'Monthly performance indicators',
      type: 'kpi',
      department: 'dispatch',
      format: 'pdf',
      sections: [
        {
          id: 'section-1',
          title: 'Delivery Performance',
          type: 'chart',
          dataSource: 'delivery',
          order: 0
        }
      ],
      recipients: ['<EMAIL>'],
      isActive: true,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15'),
      schedule: {
        frequency: 'monthly',
        time: '09:00',
        dayOfMonth: 1,
        timezone: 'Europe/Berlin',
        isActive: true
      }
    },
    {
      id: 'template-2',
      name: 'Weekly Analysis',
      description: 'Weekly operational analysis',
      type: 'analysis',
      department: 'cutting',
      format: 'excel',
      sections: [
        {
          id: 'section-2',
          title: 'Cutting Efficiency',
          type: 'table',
          dataSource: 'production',
          order: 0
        }
      ],
      recipients: [],
      isActive: false,
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-20')
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders template list correctly', () => {
    render(
      <ReportTemplateManager
        templates={mockTemplates}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    expect(screen.getByText('Monthly KPI Report')).toBeInTheDocument();
    expect(screen.getByText('Weekly Analysis')).toBeInTheDocument();
    expect(screen.getByText('Monthly performance indicators')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(
      <ReportTemplateManager
        templates={[]}
        loading={true}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    // Should show loading skeletons
    const cards = screen.getAllByTestId('card');
    expect(cards.length).toBeGreaterThan(0);
  });

  it('shows empty state when no templates', () => {
    render(
      <ReportTemplateManager
        templates={[]}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    expect(screen.getByText('Keine Vorlagen vorhanden')).toBeInTheDocument();
    expect(screen.getByText('Erstellen Sie Ihre erste Berichtsvorlage.')).toBeInTheDocument();
  });

  it('filters templates by search term', () => {
    render(
      <ReportTemplateManager
        templates={mockTemplates}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    const searchInput = screen.getByPlaceholderText('Vorlagen durchsuchen...');
    fireEvent.change(searchInput, { target: { value: 'Monthly' } });

    expect(screen.getByText('Monthly KPI Report')).toBeInTheDocument();
    expect(screen.queryByText('Weekly Analysis')).not.toBeInTheDocument();
  });

  it('filters templates by type', () => {
    render(
      <ReportTemplateManager
        templates={mockTemplates}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    const typeSelect = screen.getByDisplayValue('Alle Typen');
    fireEvent.change(typeSelect, { target: { value: 'kpi' } });

    expect(screen.getByText('Monthly KPI Report')).toBeInTheDocument();
    expect(screen.queryByText('Weekly Analysis')).not.toBeInTheDocument();
  });

  it('filters templates by department', () => {
    render(
      <ReportTemplateManager
        templates={mockTemplates}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    const departmentSelect = screen.getByDisplayValue('Alle Abteilungen');
    fireEvent.change(departmentSelect, { target: { value: 'dispatch' } });

    expect(screen.getByText('Monthly KPI Report')).toBeInTheDocument();
    expect(screen.queryByText('Weekly Analysis')).not.toBeInTheDocument();
  });

  it('filters templates by status', () => {
    render(
      <ReportTemplateManager
        templates={mockTemplates}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    const statusSelect = screen.getByDisplayValue('Alle Status');
    fireEvent.change(statusSelect, { target: { value: 'active' } });

    expect(screen.getByText('Monthly KPI Report')).toBeInTheDocument();
    expect(screen.queryByText('Weekly Analysis')).not.toBeInTheDocument();
  });

  it('selects template when clicked', () => {
    render(
      <ReportTemplateManager
        templates={mockTemplates}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    const templateCard = screen.getByText('Monthly KPI Report').closest('[data-testid="card"]');
    fireEvent.click(templateCard!);

    expect(mockOnSelectTemplate).toHaveBeenCalledWith(mockTemplates[0]);
  });

  it('toggles template active status', async () => {
    render(
      <ReportTemplateManager
        templates={mockTemplates}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    const deactivateButton = screen.getByText('Deaktivieren');
    fireEvent.click(deactivateButton);

    expect(mockOnUpdateTemplate).toHaveBeenCalledWith('template-1', { isActive: false });
  });

  it('deletes template with confirmation', async () => {
    // Mock window.confirm
    const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true);

    render(
      <ReportTemplateManager
        templates={mockTemplates}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    const deleteButtons = screen.getAllByRole('button');
    const deleteButton = deleteButtons.find(button => 
      button.querySelector('svg') // Looking for the Trash2 icon
    );
    
    if (deleteButton) {
      fireEvent.click(deleteButton);
    }

    expect(confirmSpy).toHaveBeenCalledWith(
      'Sind Sie sicher, dass Sie die Vorlage "Monthly KPI Report" löschen möchten?'
    );
    expect(mockOnDeleteTemplate).toHaveBeenCalledWith('template-1');

    confirmSpy.mockRestore();
  });

  it('shows template details when selected', () => {
    render(
      <ReportTemplateManager
        templates={mockTemplates}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    // Select a template first
    const templateCard = screen.getByText('Monthly KPI Report').closest('[data-testid="card"]');
    fireEvent.click(templateCard!);

    // Should show template details
    expect(screen.getByText('Vorlagen-Details: Monthly KPI Report')).toBeInTheDocument();
    expect(screen.getByText('Delivery Performance')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('shows schedule information for scheduled templates', () => {
    render(
      <ReportTemplateManager
        templates={mockTemplates}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    expect(screen.getByText(/Automatische Generierung: Monatlich um 09:00 Uhr/)).toBeInTheDocument();
  });

  it('resets filters when reset button is clicked', () => {
    render(
      <ReportTemplateManager
        templates={mockTemplates}
        loading={false}
        onSelectTemplate={mockOnSelectTemplate}
        onUpdateTemplate={mockOnUpdateTemplate}
        onDeleteTemplate={mockOnDeleteTemplate}
        onRefresh={mockOnRefresh}
      />
    );

    // Apply a filter
    const searchInput = screen.getByPlaceholderText('Vorlagen durchsuchen...');
    fireEvent.change(searchInput, { target: { value: 'NonExistent' } });

    // Should show no results
    expect(screen.getByText('Keine Vorlagen gefunden')).toBeInTheDocument();

    // Reset filters
    const resetButton = screen.getByText('Filter zurücksetzen');
    fireEvent.click(resetButton);

    // Should show all templates again
    expect(screen.getByText('Monthly KPI Report')).toBeInTheDocument();
    expect(screen.getByText('Weekly Analysis')).toBeInTheDocument();
  });
});