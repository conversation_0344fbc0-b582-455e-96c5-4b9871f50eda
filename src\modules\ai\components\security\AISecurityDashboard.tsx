/**
 * AI Security Dashboard Component
 * 
 * Displays security metrics, audit logs, and access control information
 * for AI module administrators.
 */

import React, { useState, useEffect } from 'react';
import { aiSecurityService } from '@/modules/ai/services/security/AISecurityService';
import { apiKeyManager } from '@/modules/ai/services/security/APIKeyManager';
import { AISecurityAuditLog } from '@/modules/ai/types/security';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Key, 
  Activity,
  Users,
  Clock,
  TrendingUp,
  RefreshCw
} from 'lucide-react';
import { withAISecurity } from './AISecurityGuard';

/**
 * Security dashboard for administrators
 */
const AISecurityDashboardComponent: React.FC = () => {
  const [securityStats, setSecurityStats] = useState({
    totalRequests: 0,
    deniedRequests: 0,
    highRiskAttempts: 0,
    topViolations: [] as { violation: string; count: number }[]
  });
  const [auditLogs, setAuditLogs] = useState<AISecurityAuditLog[]>([]);
  const [apiKeyStatus, setApiKeyStatus] = useState({ allConfigured: false, missing: [] as string[] });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    setIsLoading(true);
    try {
      // Load security statistics
      const stats = aiSecurityService.getSecurityStats();
      setSecurityStats(stats);

      // Load audit logs
      const logs = aiSecurityService.getAuditLogs(50);
      setAuditLogs(logs);

      // Check API key status
      const keyStatus = apiKeyManager.checkRequiredKeys();
      setApiKeyStatus(keyStatus);
    } catch (error) {
      console.error('Error loading security data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('de-DE', {
      dateStyle: 'short',
      timeStyle: 'medium'
    }).format(timestamp);
  };

  const getResultBadgeVariant = (result: string) => {
    switch (result) {
      case 'success':
        return 'default';
      case 'denied':
        return 'destructive';
      case 'error':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        <span>Sicherheitsdaten werden geladen...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">KI-Sicherheitsdashboard</h1>
          <p className="text-gray-600">Überwachung und Verwaltung der KI-Modulsicherheit</p>
        </div>
        <Button onClick={loadSecurityData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Aktualisieren
        </Button>
      </div>

      {/* API Key Status Alert */}
      {!apiKeyStatus.allConfigured && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            <div className="font-medium">API-Schlüssel fehlen</div>
            <div className="text-sm mt-1">
              Folgende API-Schlüssel sind nicht konfiguriert: {apiKeyStatus.missing.join(', ')}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Security Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gesamtanfragen</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{securityStats.totalRequests}</div>
            <p className="text-xs text-muted-foreground">
              Alle KI-Anfragen
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Verweigerte Anfragen</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{securityStats.deniedRequests}</div>
            <p className="text-xs text-muted-foreground">
              {securityStats.totalRequests > 0 
                ? `${((securityStats.deniedRequests / securityStats.totalRequests) * 100).toFixed(1)}% der Anfragen`
                : 'Keine Anfragen'
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hochrisiko-Versuche</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{securityStats.highRiskAttempts}</div>
            <p className="text-xs text-muted-foreground">
              Potentiell gefährliche Eingaben
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API-Status</CardTitle>
            {apiKeyStatus.allConfigured ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <XCircle className="h-4 w-4 text-red-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {apiKeyStatus.allConfigured ? (
                <span className="text-green-600">OK</span>
              ) : (
                <span className="text-red-600">Fehler</span>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {apiKeyStatus.allConfigured ? 'Alle Schlüssel konfiguriert' : `${apiKeyStatus.missing.length} fehlen`}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Information Tabs */}
      <Tabs defaultValue="audit-logs" className="space-y-4">
        <TabsList>
          <TabsTrigger value="audit-logs">Audit-Protokoll</TabsTrigger>
          <TabsTrigger value="violations">Sicherheitsverletzungen</TabsTrigger>
          <TabsTrigger value="api-keys">API-Schlüssel</TabsTrigger>
        </TabsList>

        {/* Audit Logs Tab */}
        <TabsContent value="audit-logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Aktuelle Audit-Protokolle</CardTitle>
              <CardDescription>
                Die letzten 50 Sicherheitsereignisse im KI-Modul
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {auditLogs.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    Keine Audit-Protokolle vorhanden
                  </div>
                ) : (
                  auditLogs.map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <Badge variant={getResultBadgeVariant(log.result)}>
                            {log.result === 'success' ? 'Erfolg' : 
                             log.result === 'denied' ? 'Verweigert' : 'Fehler'}
                          </Badge>
                          <span className="font-medium">{log.username}</span>
                          <span className="text-gray-500">•</span>
                          <span className="text-sm text-gray-600">{log.action}</span>
                          <span className="text-gray-500">•</span>
                          <span className="text-sm text-gray-600">{log.feature}</span>
                        </div>
                        {log.reason && (
                          <div className="text-sm text-gray-500 mt-1">{log.reason}</div>
                        )}
                      </div>
                      <div className="text-sm text-gray-500">
                        <Clock className="h-4 w-4 inline mr-1" />
                        {formatTimestamp(log.timestamp)}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Violations Tab */}
        <TabsContent value="violations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Häufigste Sicherheitsverletzungen</CardTitle>
              <CardDescription>
                Übersicht der am häufigsten auftretenden Sicherheitsprobleme
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {securityStats.topViolations.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    Keine Sicherheitsverletzungen aufgezeichnet
                  </div>
                ) : (
                  securityStats.topViolations.map((violation, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                          <span className="text-red-600 font-bold text-sm">{index + 1}</span>
                        </div>
                        <div>
                          <div className="font-medium">{violation.violation}</div>
                        </div>
                      </div>
                      <Badge variant="destructive">{violation.count} Mal</Badge>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* API Keys Tab */}
        <TabsContent value="api-keys" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API-Schlüssel Status</CardTitle>
              <CardDescription>
                Übersicht der konfigurierten API-Schlüssel für KI-Services
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {apiKeyManager.listAPIKeys().map((key) => (
                  <div key={key.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Key className="h-5 w-5 text-gray-500" />
                      <div>
                        <div className="font-medium">{key.service}</div>
                        <div className="text-sm text-gray-500">
                          Erstellt: {formatTimestamp(key.createdAt)}
                          {key.lastUsed && (
                            <span> • Zuletzt verwendet: {formatTimestamp(key.lastUsed)}</span>
                          )}
                        </div>
                      </div>
                    </div>
                    <Badge variant={key.isActive ? 'default' : 'secondary'}>
                      {key.isActive ? 'Aktiv' : 'Inaktiv'}
                    </Badge>
                  </div>
                ))}
                
                {apiKeyManager.listAPIKeys().length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    Keine API-Schlüssel konfiguriert
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Export with security protection - only administrators can access
export const AISecurityDashboard = withAISecurity(
  AISecurityDashboardComponent,
  'ai_configuration'
);