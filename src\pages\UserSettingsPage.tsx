import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User, Bell, Palette, Shield, Save } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import SmoothTab from "@/components/Animation/kokonutui/smooth-tab";
import { userService } from "@/services/user.service";
import { cn } from "@/lib/utils";

// Typdefinitionen
interface TableInfo {
    name: string;
}
  
interface TableData {
    columns: string[];
    rows: any[];
}

export default function UserSettingsPage() {
  // Keine Animation-Varianten mehr, wir nutzen nur CSS-Transitionen
  // Authentication hook
  const { user, isLoading } = useAuth();

  // Aktiver Tab
  const [activeTab, setActiveTab] = useState("profile");
  
  // Profildaten basierend auf dem eingeloggten Benutzer
  const [profile, setProfile] = useState({
    username: "",
    email: "",
    name: "",
    role: "",
    bio: ""
  });

  // Benachrichtigungseinstellungen
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    pushNotifications: false,
    newMessages: true,
    newMentions: true,
    newOrders: true,
    newAlerts: true
  });

  // Erscheinungsbild-Einstellungen
  const [appearance, setAppearance] = useState({
    theme: "system",
    language: "de"
  });


  // Profildaten mit echten Benutzerdaten initialisieren
  useEffect(() => {
    if (user) {
      setProfile({
        username: user.username || "",
        email: user.email || "",
        name: user.name || "",
        role: user.roles?.[0] || "user",
        bio: ""
      });
      loadUserProfile();
    }
  }, [user]);

  // Lade vollständige Profildaten vom Backend
  const loadUserProfile = async () => {
    try {
      const response = await userService.getProfile();
      if (response.success && response.data) {
        setProfile(prev => ({
          ...prev,
          bio: response.data?.bio || ""
        }));
      }
    } catch (error) {
      console.error("Fehler beim Laden des Profils:", error);
    }
  };

  // Handler für Tab-Wechsel
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  // Handler für Profiländerungen
  const handleProfileChange = (field: string, value: string) => {
    setProfile(prev => ({ ...prev, [field]: value }));
  };

  // Handler für Benachrichtigungsänderungen
  const handleNotificationChange = (field: string, value: boolean) => {
    setNotifications(prev => ({ ...prev, [field]: value }));
  };

  // Handler für Erscheinungsbildänderungen
  const handleAppearanceChange = (field: string, value: string) => {
    setAppearance(prev => ({ ...prev, [field]: value }));
  };

  // Handler für Formularübermittlungen
  const handleSubmit = async (formType: string) => {
    try {
      if (formType === "profile") {
        const response = await userService.updateProfile({
          username: profile.username,
          email: profile.email,
          name: profile.name,
          bio: profile.bio
        });
        if (response.success) {
          alert("Profil erfolgreich gespeichert!");
        }
      }
      // Weitere Formulartypen können hier hinzugefügt werden
    } catch (error) {
      console.error(`Fehler beim Speichern von ${formType}:`, error);
      alert(`Fehler beim Speichern der ${formType}-Einstellungen.`);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Lade Benutzereinstellungen...</div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-lg">Sie müssen angemeldet sein, um auf diese Seite zuzugreifen.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-bg min-h-screen p-8">
      <div className="max-w-5xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between w-full mb-8">
            <div className="flex-1">
              <h1 className="text-4xl font-heading tracking-tight text-text flex items-center gap-2">
              <User className="h-8 w-8" />
              Benutzereinstellungen
            </h1>
            <p className="text-lg text-text font-base mt-2 opacity-70">Verwalte deine Konto- und Präferenzeinstellungen</p>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="space-y-6">
          <div className="flex justify-center">
            <SmoothTab
              items={[
                 {
                   id: "profile",
                   title: "Profil",
                   icon: User,
                   color: "bg-blue-500 hover:bg-blue-600",
                 },
                 {
                   id: "notifications",
                   title: "Benachrichtigungen",
                   icon: Bell,
                   color: "bg-green-500 hover:bg-green-600",
                 },
                 {
                   id: "appearance",
                   title: "Erscheinungsbild",
                   icon: Palette,
                   color: "bg-orange-500 hover:bg-orange-600",
                 },
                 {
                   id: "security",
                   title: "Sicherheit",
                   icon: Shield,
                   color: "bg-red-500 hover:bg-red-600",
                 },
              ]}
              defaultTabId="profile"
              onChange={handleTabChange}
              hideCardContent={true}
            />
          </div>

          {/* Tab Content */}
          <div className="space-y-8 w-full">
            {/* Profil Tab */}
            {activeTab === "profile" && (
              <Card className="group border-blue-500/10 hover:border-blue-500/30 overflow-hidden transition-all duration-500 relative bg-blue-50/30 dark:bg-blue-950/10">
                <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>
                <div className="text-blue-500/50 group-hover:text-blue-500 absolute right-1 bottom-3 scale-[6] transition-all duration-700 group-hover:scale-[6.2]">
                  <User className="size-6" />
                </div>
                <div className="from-blue-500 to-blue-500/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
                <CardHeader className="relative z-10">
                  <CardTitle className="flex items-center gap-2">
                    <div className="bg-blue-500/10 text-blue-500 shadow-blue-500/10 group-hover:bg-blue-500/20 group-hover:shadow-blue-500/20 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500">
                      <User className="h-8 w-8" />
                    </div>
                    Profil
                  </CardTitle>
                  <CardDescription>
                    Verwalte deine persönlichen Profildetails.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-8 py-6">
                  <div className="flex items-center space-x-6">
                    <Avatar className="h-16 w-16">
                      <AvatarFallback className="text-lg">
                        {(profile.name || profile.username).charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="text-lg font-medium">{profile.name || profile.username}</h3>
                      <p className="text-sm text-gray-500">{profile.email}</p>
                      <p className="text-sm text-gray-500">Rolle: {profile.role}</p>
                    </div>
                  </div>

                  <Separator />

                  <div className="grid gap-6 w-full">
                    <div className="grid gap-2">
                      <Label htmlFor="username">Benutzername</Label>
                      <Input
                        id="username"
                        value={profile.username}
                        onChange={(e) => handleProfileChange('username', e.target.value)}
                      />
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="email">E-Mail-Adresse</Label>
                      <Input
                        id="email"
                        type="email"
                        value={profile.email}
                        onChange={(e) => handleProfileChange('email', e.target.value)}
                      />
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="name">Vollständiger Name</Label>
                      <Input
                        id="name"
                        value={profile.name}
                        onChange={(e) => handleProfileChange('name', e.target.value)}
                      />
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="bio">Bio</Label>
                      <Textarea
                        id="bio"
                        placeholder="Erzähle etwas über dich..."
                        value={profile.bio}
                        onChange={(e) => handleProfileChange('bio', e.target.value)}
                        rows={3}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={() => handleSubmit('profile')} className="ml-auto">
                    <Save className="h-4 w-4 mr-2" />
                    Profil speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {/* Benachrichtigungen Tab */}
            {activeTab === "notifications" && (
              <Card className="group border-green-500/10 hover:border-green-500/30 overflow-hidden transition-all duration-500 relative bg-green-50/30 dark:bg-green-950/10">
                <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>
                <div className="text-green-500/5 group-hover:text-green-500/10 absolute right-1 bottom-3 scale-[6] transition-all duration-700 group-hover:scale-[6.2]">
                  <Bell className="size-6" />
                </div>
                <div className="from-green-500 to-green-500/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
                <CardHeader className="relative z-10">
                  <CardTitle className="flex items-center gap-2">
                    <div className="bg-green-500/10 text-green-500 shadow-green-500/10 group-hover:bg-green-500/20 group-hover:shadow-green-500/20 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500">
                      <Bell className="h-8 w-8" />
                    </div>
                    Benachrichtigungen
                  </CardTitle>
                  <CardDescription>
                    Verwalte deine Benachrichtigungseinstellungen und Präferenzen.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-8 py-6">
                  <div className="space-y-6 w-full">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="email-notifications">E-Mail-Benachrichtigungen</Label>
                        <p className="text-sm text-gray-500">Erhalte wichtige Updates per E-Mail</p>
                      </div>
                      <Switch
                        id="email-notifications"
                        checked={notifications.emailNotifications}
                        onCheckedChange={(checked) => handleNotificationChange('emailNotifications', checked)}
                      />
                    </div>

                    <Separator />

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="push-notifications">Push-Benachrichtigungen</Label>
                        <p className="text-sm text-gray-500">Erhalte sofortige Benachrichtigungen</p>
                      </div>
                      <Switch
                        id="push-notifications"
                        checked={notifications.pushNotifications}
                        onCheckedChange={(checked) => handleNotificationChange('pushNotifications', checked)}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={() => handleSubmit('notifications')} className="ml-auto">
                    <Save className="h-4 w-4 mr-2" />
                    Einstellungen speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {/* Erscheinungsbild Tab */}
            {activeTab === "appearance" && (
              <Card className="group border-orange-500/10 hover:border-orange-500/30 overflow-hidden transition-all duration-500 relative bg-orange-50/30 dark:bg-orange-950/10">
                <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>
                <div className="text-orange-500/5 group-hover:text-orange-500/10 absolute right-1 bottom-3 scale-[6] transition-all duration-700 group-hover:scale-[6.2]">
                  <Palette className="size-6" />
                </div>
                <div className="from-orange-500 to-orange-500/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
                <CardHeader className="relative z-10">
                  <CardTitle className="flex items-center gap-2">
                    <div className="bg-orange-500/10 text-orange-500 shadow-orange-500/10 group-hover:bg-orange-500/20 group-hover:shadow-orange-500/20 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500">
                      <Palette className="h-5 w-5" />
                    </div>
                    Darstellung
                  </CardTitle>
                  <CardDescription>
                    Passe die Darstellung der Anwendung an.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-8 py-6">
                  <div className="space-y-6 w-full">
                    <div className="grid gap-2">
                      <Label htmlFor="theme">Theme</Label>
                      <Select value={appearance.theme} onValueChange={(value) => handleAppearanceChange('theme', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Hell</SelectItem>
                          <SelectItem value="dark">Dunkel</SelectItem>
                          <SelectItem value="system">System</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="language">Sprache</Label>
                      <Select value={appearance.language} onValueChange={(value) => handleAppearanceChange('language', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="de">Deutsch</SelectItem>
                          <SelectItem value="en">English</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={() => handleSubmit('appearance')} className="ml-auto">
                    <Save className="h-4 w-4 mr-2" />
                    Einstellungen speichern
                  </Button>
                </CardFooter>
              </Card>
            )}

            {/* Sicherheit Tab */}
            {activeTab === "security" && (
              <Card className="group border-red-500/10 hover:border-red-500/30 overflow-hidden transition-all duration-500 relative bg-red-50/30 dark:bg-red-950/10">
                <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>
                <div className="text-red-500/5 group-hover:text-red-500/10 absolute right-1 bottom-3 scale-[6] transition-all duration-700 group-hover:scale-[6.2]">
                  <Shield className="size-6" />
                </div>
                <div className="from-red-500 to-red-500/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
                <CardHeader className="relative z-10">
                  <CardTitle className="flex items-center gap-2">
                    <div className="bg-red-500/10 text-red-500 shadow-red-500/10 group-hover:bg-red-500/20 group-hover:shadow-red-500/20 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500">
                      <Shield className="h-5 w-5" />
                    </div>
                    Sicherheit
                  </CardTitle>
                  <CardDescription>
                    Verwalte deine Sicherheitseinstellungen und Zugangsdaten.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Passwort ändern</h3>

                    <div className="grid gap-6 w-full">
                      <div className="grid gap-2">
                        <Label htmlFor="current-password">Aktuelles Passwort</Label>
                        <Input id="current-password" type="password" />
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="new-password">Neues Passwort</Label>
                        <Input id="new-password" type="password" />
                      </div>

                      <Button variant="outline" className="w-auto">
                        Passwort ändern
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
