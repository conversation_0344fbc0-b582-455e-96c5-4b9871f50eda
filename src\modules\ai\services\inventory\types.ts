/**
 * Inventory Intelligence Service Types
 * 
 * Type definitions for inventory management, ABC analysis, and demand forecasting
 */

// Base Inventory Types
export interface InventoryItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  unitPrice: number;
  supplier?: string;
  location?: string;
  lastUpdated: Date;
  metadata?: any;
}

export interface InventoryMovement {
  id: string;
  itemId: string;
  movementType: 'in' | 'out' | 'adjustment';
  quantity: number;
  date: Date;
  reason?: string;
  reference?: string;
}

export interface ConsumptionData {
  itemId: string;
  date: Date;
  quantity: number;
  value: number;
}

// ABC Analysis Types
export interface ABCClassification {
  classA: InventoryItem[];
  classB: InventoryItem[];
  classC: InventoryItem[];
  reclassifications: ClassificationChange[];
  analysisDate: Date;
  criteria: ABCCriteria;
}

export interface ClassificationChange {
  itemId: string;
  previousClass: 'A' | 'B' | 'C';
  newClass: 'A' | 'B' | 'C';
  reason: string;
  confidence: number;
}

export interface ABCCriteria {
  method: 'value' | 'quantity' | 'frequency' | 'combined';
  classAThreshold: number; // Percentage (e.g., 80 for 80%)
  classBThreshold: number; // Percentage (e.g., 95 for 95%)
  timeWindow: number; // Days to consider for analysis
}

export interface ABCAnalysisResult {
  itemId: string;
  classification: 'A' | 'B' | 'C';
  value: number;
  percentage: number;
  cumulativePercentage: number;
  rank: number;
  confidence: number;
}

// Demand Forecasting Types
export interface DemandForecast {
  itemId: string;
  predictions: ForecastPoint[];
  confidence: number;
  seasonalFactors: SeasonalFactor[];
  trendDirection: 'increasing' | 'decreasing' | 'stable';
  forecastMethod: string;
  accuracy: number;
  generatedAt: Date;
}

export interface ForecastPoint {
  date: Date;
  predictedDemand: number;
  confidenceInterval: {
    lower: number;
    upper: number;
  };
  seasonalAdjustment?: number;
}

export interface SeasonalFactor {
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  factor: number;
  confidence: number;
}

export interface SeasonalityPattern {
  itemId: string;
  hasSeasonality: boolean;
  patterns: SeasonalPattern[];
  strength: number; // 0-1, how strong the seasonal pattern is
  detectedAt: Date;
}

export interface SeasonalPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  cycle: number; // Length of cycle in days
  amplitude: number; // Strength of seasonal variation
  phase: number; // Phase shift
}

// Time Series Analysis Types
export interface TimeSeriesData {
  date: Date;
  value: number;
  trend?: number;
  seasonal?: number;
  residual?: number;
}

export interface TimeSeriesDecomposition {
  trend: TimeSeriesData[];
  seasonal: TimeSeriesData[];
  residual: TimeSeriesData[];
  originalData: TimeSeriesData[];
}

export interface ForecastingModel {
  name: string;
  type: 'simple_moving_average' | 'exponential_smoothing' | 'linear_regression' | 'seasonal_decomposition';
  parameters: any;
  accuracy: ModelAccuracy;
  lastTrained: Date;
}

export interface ModelAccuracy {
  mae: number; // Mean Absolute Error
  mse: number; // Mean Squared Error
  rmse: number; // Root Mean Squared Error
  mape: number; // Mean Absolute Percentage Error
  r2?: number; // R-squared (for regression models)
}

// Reorder Management Types
export interface ReorderRecommendation {
  itemId: string;
  currentStock: number;
  reorderPoint: number;
  recommendedOrderQuantity: number;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  reasoning: string;
  confidence: number;
  estimatedStockoutDate?: Date;
  leadTime: number;
  safetyStock: number;
}

export interface PurchaseRecommendation {
  itemId: string;
  recommendedQuantity: number;
  estimatedCost: number;
  supplier?: string;
  priority: number;
  reasoning: string;
  expectedDelivery?: Date;
  alternatives?: AlternativeRecommendation[];
}

export interface AlternativeRecommendation {
  itemId: string;
  quantity: number;
  cost: number;
  supplier: string;
  pros: string[];
  cons: string[];
}

// Anomaly Detection Types
export interface StockAnomaly {
  id: string;
  itemId: string;
  anomalyType: 'sudden_spike' | 'sudden_drop' | 'unusual_pattern' | 'stockout_risk' | 'overstock';
  severity: 'low' | 'medium' | 'high' | 'critical';
  detectedAt: Date;
  description: string;
  currentValue: number;
  expectedValue: number;
  deviation: number;
  recommendations: string[];
  confidence: number;
}

export interface ConsumptionAnalysis {
  itemId: string;
  averageDailyConsumption: number;
  consumptionTrend: 'increasing' | 'decreasing' | 'stable';
  volatility: number; // Coefficient of variation
  patterns: ConsumptionPattern[];
  outliers: ConsumptionOutlier[];
  analysisDate: Date;
}

export interface ConsumptionPattern {
  type: 'regular' | 'irregular' | 'seasonal' | 'trending';
  description: string;
  strength: number;
  frequency?: number; // For regular patterns
}

export interface ConsumptionOutlier {
  date: Date;
  value: number;
  expectedValue: number;
  deviation: number;
  possibleCause?: string;
}

// Service Configuration Types
export interface InventoryIntelligenceConfig {
  abcAnalysis: {
    enabled: boolean;
    updateFrequency: number; // Hours
    classAThreshold: number;
    classBThreshold: number;
    analysisWindow: number; // Days
  };
  demandForecasting: {
    enabled: boolean;
    forecastHorizon: number; // Days
    updateFrequency: number; // Hours
    minHistoryDays: number;
    models: string[];
  };
  anomalyDetection: {
    enabled: boolean;
    sensitivity: number; // 0-1
    checkFrequency: number; // Hours
    alertThreshold: number;
  };
  reorderOptimization: {
    enabled: boolean;
    safetyStockDays: number;
    leadTimeDays: number;
    serviceLevel: number; // 0-1
  };
}

// Service Status Types
export interface InventoryIntelligenceStatus {
  isInitialized: boolean;
  isHealthy: boolean;
  lastError?: Error;
  lastChecked: Date;
  lastAnalysisRun: Date;
  totalItemsAnalyzed: number;
  classificationsUpToDate: boolean;
  forecastsUpToDate: boolean;
  anomaliesDetected: number;
  reorderRecommendations: number;
  performance: {
    averageResponseTime: number;
    successRate: number;
    totalRequests: number;
    cacheHitRate?: number;
    analysisTime: number;
    forecastAccuracy: number;
    anomalyDetectionRate: number;
  };
  vectorStats?: {
    totalVectors: number;
    lastIndexUpdate: Date;
  };
  errors: string[];
}

// Statistical Types
export interface StatisticalSummary {
  mean: number;
  median: number;
  standardDeviation: number;
  variance: number;
  min: number;
  max: number;
  count: number;
  percentiles: {
    p25: number;
    p75: number;
    p90: number;
    p95: number;
  };
}

export interface CorrelationAnalysis {
  itemPairs: Array<{
    item1Id: string;
    item2Id: string;
    correlation: number;
    significance: number;
  }>;
  strongCorrelations: Array<{
    items: string[];
    correlation: number;
    type: 'positive' | 'negative';
  }>;
}