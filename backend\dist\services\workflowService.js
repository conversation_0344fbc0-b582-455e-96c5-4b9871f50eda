"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowService = void 0;
const child_process_1 = require("child_process");
const path_1 = __importDefault(require("path"));
const client_1 = require("@prisma-sfm-dashboard/client");
class WorkflowService {
    constructor() {
        this.processes = [
            {
                id: 'servicegrad',
                name: 'Servicegrad',
                description: 'SAP Servicegrad Automatisierung mit E-Mail-Versand',
                tcode: '/n/LSGIT/VS_DLV_CHECK',
                exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\SG',
                exportBasename: 'SG',
                dbTable: 'delivery_checks',
                status: 'idle'
            },
            {
                id: 'bestand',
                name: 'Bestand Workflow',
                description: 'Lagerspiegel Export aus verschiedenen Lägern mit Import in die Datenbank',
                tcode: '/nlx03',
                exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST',
                exportBasename: 'BST',
                dbTable: 'lagerspiegel',
                status: 'idle'
            },
            {
                id: 'rueckstandsliste',
                name: 'Rückstandsliste',
                description: 'SAP Rückstandsliste Datenexport',
                tcode: '/n/LSGIT/VS_DLV_CHECK',
                exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\RSL',
                exportBasename: 'RSL',
                dbTable: 'rsl_variant_data',
                status: 'idle'
            },
            {
                id: 'lx03_240',
                name: 'LX03 Lagertyp 240',
                description: 'SAP LX03 Lagertyp 240 Datenexport',
                tcode: '/nlx03',
                exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST',
                exportBasename: 'BST_240',
                dbTable: 'lx03_bin_status',
                status: 'idle'
            },
            {
                id: 'lx03_200',
                name: 'LX03 Lagertyp 200',
                description: 'SAP LX03 Lagertyp 200 Datenexport',
                tcode: '/nlx03',
                exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST',
                exportBasename: 'BST_200',
                dbTable: 'lx03_bin_status',
                status: 'idle'
            },
            {
                id: 'lx03_rest',
                name: 'LX03 Lagertyp Rest',
                description: 'SAP LX03 Lagertyp Rest (241-999) Datenexport',
                tcode: '/nlx03',
                exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST',
                exportBasename: 'BST_REST',
                dbTable: 'lx03_bin_status',
                status: 'idle'
            }
        ];
        // Prisma Client für Datenbankzugriff
        this.prisma = new client_1.PrismaClient();
        this.pythonScriptPath = path_1.default.join(__dirname, '../../scripts/workflows/workflowSAP.py');
    }
    /**
     * Gibt den korrekten Python-Skript-Pfad für einen Workflow zurück
     */
    getPythonScriptPath(processId) {
        switch (processId) {
            case 'servicegrad':
                return path_1.default.join(__dirname, '../../scripts/workflows/Servicegrad/Servicegrad_Workflow.py');
            case 'bestand':
                return path_1.default.join(__dirname, '../../scripts/workflows/Bestand/Bestand-Workflow.py');
            default:
                return this.pythonScriptPath; // Fallback auf das alte Skript
        }
    }
    /**
     * Lädt alle Workflow-Logs aus der Datenbank über Prisma
     */
    async getAllLogs(limit = 100) {
        try {
            const logs = await this.prisma.workflowLog.findMany({
                orderBy: {
                    timestamp: 'desc'
                },
                take: limit
            });
            return logs.map(log => ({
                timestamp: new Date(log.timestamp),
                level: log.level,
                message: log.message,
                workflowId: log.workflowId,
                executionId: log.executionId || undefined,
                details: log.details ? JSON.parse(log.details) : undefined
            }));
        }
        catch (error) {
            console.error('[WorkflowService] Fehler beim Laden aller Logs:', error);
            return [];
        }
    }
    /**
     * Lädt Logs für einen bestimmten Workflow über Prisma
     */
    async getWorkflowLogs(workflowId, limit = 50) {
        try {
            const logs = await this.prisma.workflowLog.findMany({
                where: {
                    workflowId: workflowId
                },
                orderBy: {
                    timestamp: 'desc'
                },
                take: limit
            });
            return logs.map(log => ({
                timestamp: new Date(log.timestamp),
                level: log.level,
                message: log.message,
                workflowId: log.workflowId,
                executionId: log.executionId || undefined,
                details: log.details ? JSON.parse(log.details) : undefined
            }));
        }
        catch (error) {
            console.error(`[WorkflowService] Fehler beim Laden der Logs für ${workflowId}:`, error);
            return [];
        }
    }
    /**
     * Lädt alle verfügbaren Workflow-Prozesse
     */
    async getProcesses() {
        return this.processes;
    }
    /**
     * Führt einen Workflow-Prozess aus
     */
    async executeProcess(processId) {
        try {
            const process = this.processes.find(p => p.id === processId);
            if (!process) {
                throw new Error(`Prozess ${processId} nicht gefunden`);
            }
            // Update process status
            process.status = 'running';
            process.lastRun = new Date();
            const scriptPath = this.getPythonScriptPath(processId);
            console.log(`[WorkflowService] Starte Python-Skript: ${scriptPath}`);
            // Führe das echte Python-Skript aus
            const result = await this.executePythonScript(processId, scriptPath);
            if (result.success) {
                process.status = 'completed';
                process.duration = result.duration;
                process.exportPath = result.exportPath;
                return {
                    success: true,
                    message: `Prozess ${process.name} erfolgreich ausgeführt`,
                    exportPath: result.exportPath
                };
            }
            else {
                process.status = 'error';
                return {
                    success: false,
                    message: result.error || 'Unbekannter Fehler'
                };
            }
        }
        catch (error) {
            const process = this.processes.find(p => p.id === processId);
            if (process) {
                process.status = 'error';
            }
            console.error(`[WorkflowService] Fehler beim Ausführen von Prozess ${processId}:`, error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unbekannter Fehler'
            };
        }
    }
    /**
     * Führt das Python-Skript aus
     */
    async executePythonScript(processId, scriptPath) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            // Prüfe ob Test-Modus für Servicegrad verfügbar ist
            const isServicegradTestMode = processId === 'servicegrad' && process.env.NODE_ENV === 'development';
            let actualScriptPath = scriptPath;
            let pythonArgs = [scriptPath];
            if (isServicegradTestMode) {
                // Versuche zuerst Test-Modus
                const testModePath = path_1.default.join(path_1.default.dirname(scriptPath), 'test_mode.py');
                if (require('fs').existsSync(testModePath)) {
                    console.log(`[WorkflowService] Verwende Test-Modus für ${processId}: ${testModePath}`);
                    actualScriptPath = testModePath;
                    pythonArgs = [testModePath];
                }
            }
            // Führe Python-Skript aus
            const pythonProcess = (0, child_process_1.spawn)('python', pythonArgs, {
                cwd: path_1.default.dirname(actualScriptPath),
                stdio: ['pipe', 'pipe', 'pipe']
            });
            let stdout = '';
            let stderr = '';
            pythonProcess.stdout.on('data', (data) => {
                stdout += data.toString();
                console.log(`[Python] ${data.toString().trim()}`);
            });
            pythonProcess.stderr.on('data', (data) => {
                stderr += data.toString();
                console.error(`[Python Error] ${data.toString().trim()}`);
            });
            pythonProcess.on('close', (code) => {
                const duration = Math.floor((Date.now() - startTime) / 1000);
                if (code === 0) {
                    // Erfolgreiche Ausführung
                    resolve({
                        success: true,
                        duration,
                        exportPath: this.extractExportPathFromOutput(stdout)
                    });
                }
                else {
                    // Fehler bei der Ausführung
                    resolve({
                        success: false,
                        duration,
                        error: `Python-Skript beendet mit Code ${code}: ${stderr}`
                    });
                }
            });
            pythonProcess.on('error', (error) => {
                const duration = Math.floor((Date.now() - startTime) / 1000);
                resolve({
                    success: false,
                    duration,
                    error: `Fehler beim Starten des Python-Skripts: ${error.message}`
                });
            });
        });
    }
    /**
     * Extrahiert den Export-Pfad aus der Python-Ausgabe
     */
    extractExportPathFromOutput(output) {
        const lines = output.split('\n');
        for (const line of lines) {
            if (line.includes('Export gestartet nach:') || line.includes('Datei') && line.includes('.xlsx')) {
                // Versuche Export-Pfad zu extrahieren
                const match = line.match(/([A-Z]:\\[^"]+\.xlsx)/);
                if (match) {
                    return match[1];
                }
            }
        }
        return undefined;
    }
    /**
     * Lädt Workflow-Ausführungen aus der Datenbank
     */
    async getExecutions(workflowId, limit = 50) {
        try {
            // TODO: Implement real database query for workflow executions
            // For now, return empty array until workflow_executions table is created
            const executions = await this.prisma.workflowExecution.findMany({
                where: workflowId ? { workflowId } : undefined,
                orderBy: { startTime: 'desc' },
                take: limit
            });
            // Map Prisma-Daten auf WorkflowExecution-Interface
            return executions.map(execution => ({
                id: execution.id,
                workflowId: execution.workflowId,
                startTime: execution.startTime,
                endTime: execution.endTime,
                duration: execution.durationSeconds, // Map durationSeconds zu duration
                status: execution.status,
                errorMessage: execution.errorMessage,
                recordsProcessed: execution.recordsProcessed,
                dataSize: null // Setze dataSize auf null, da es in der DB nicht vorhanden ist
            }));
        }
        catch (error) {
            console.error('[WorkflowService] Fehler beim Laden der Ausführungen:', error);
            // Return empty array if table doesn't exist yet
            return [];
        }
    }
    /**
     * Lädt Workflow-Statistiken
     */
    async getStats() {
        try {
            const executions = await this.getExecutions(undefined, 100);
            const totalExecutions = executions.length;
            const successfulExecutions = executions.filter(e => e.status === 'completed').length;
            const failedExecutions = executions.filter(e => e.status === 'error').length;
            const completedExecutions = executions.filter(e => e.duration);
            const averageDuration = completedExecutions.length > 0
                ? completedExecutions.reduce((sum, e) => sum + (e.duration || 0), 0) / completedExecutions.length
                : 0;
            const lastExecution = executions.length > 0
                ? executions[0].startTime
                : undefined;
            return {
                totalExecutions,
                successfulExecutions,
                failedExecutions,
                averageDuration,
                lastExecution
            };
        }
        catch (error) {
            console.error('[WorkflowService] Fehler beim Laden der Statistiken:', error);
            return {
                totalExecutions: 0,
                successfulExecutions: 0,
                failedExecutions: 0,
                averageDuration: 0
            };
        }
    }
    /**
     * Lädt die Konfiguration für einen Workflow
     */
    async getWorkflowConfig(workflowId) {
        try {
            // Für jetzt verwenden wir eine einfache JSON-Datei
            // In einer echten Implementierung würde dies aus der Datenbank kommen
            const configPath = path_1.default.join(__dirname, '../../config/workflows', `${workflowId}.json`);
            if (require('fs').existsSync(configPath)) {
                const configData = require('fs').readFileSync(configPath, 'utf8');
                return JSON.parse(configData);
            }
            // Standard-Konfiguration zurückgeben
            return this.getDefaultConfig(workflowId);
        }
        catch (error) {
            console.error(`[WorkflowService] Fehler beim Laden der Konfiguration für ${workflowId}:`, error);
            return this.getDefaultConfig(workflowId);
        }
    }
    /**
     * Aktualisiert die Konfiguration für einen Workflow
     */
    async updateWorkflowConfig(workflowId, config) {
        try {
            const configDir = path_1.default.join(__dirname, '../../config/workflows');
            const configPath = path_1.default.join(configDir, `${workflowId}.json`);
            // Stelle sicher, dass das Verzeichnis existiert
            if (!require('fs').existsSync(configDir)) {
                require('fs').mkdirSync(configDir, { recursive: true });
            }
            // Konfiguration mit Zeitstempel speichern
            const configWithTimestamp = {
                ...config,
                lastModified: new Date().toISOString()
            };
            require('fs').writeFileSync(configPath, JSON.stringify(configWithTimestamp, null, 2));
            console.log(`[WorkflowService] Konfiguration für ${workflowId} gespeichert`);
            return configWithTimestamp;
        }
        catch (error) {
            console.error(`[WorkflowService] Fehler beim Speichern der Konfiguration für ${workflowId}:`, error);
            throw error;
        }
    }
    /**
     * Gibt die Standard-Konfiguration für einen Workflow zurück
     */
    getDefaultConfig(workflowId) {
        const defaultConfigs = {
            servicegrad: {
                id: 'servicegrad',
                name: 'Servicegrad Automatisierung',
                description: 'SAP Servicegrad Export mit automatischer Excel-Verarbeitung',
                databasePath: 'C:\\Users\\<USER>\\OneDrive\\Desktop\\Neuer Ordner\\sfm_dashboard.db',
                exportPath: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\SG',
                emailRecipients: ['<EMAIL>'],
                schedule: {
                    enabled: false,
                    frequency: 'daily',
                    time: '08:00',
                    dayOfWeek: 1,
                    interval: 1
                },
                isActive: true,
                lastModified: new Date().toISOString()
            },
            bestand: {
                id: 'bestand',
                name: 'Bestand Workflow',
                description: 'Lagerspiegel Export aus verschiedenen Lägern mit Import in die Datenbank',
                databasePath: 'sap_datenbank.db',
                exportDir240: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST',
                exportDir200: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST',
                exportDirRest: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST',
                lgnum: '512',
                lgt240: '240',
                lgt200: '200',
                lgtRestLow: '241',
                lgtRestHigh: '999',
                sapExecutablePath: 'C:\\\\Program Files (x86)\\\\SAP\\\\FrontEnd\\\\SapGui\\\\sapshcut.exe',
                sapSystemId: 'PS4',
                sapClient: '009',
                sapLanguage: 'DE',
                emailRecipients: [],
                schedule: {
                    enabled: false,
                    frequency: 'daily',
                    time: '08:00',
                    dayOfWeek: 1,
                    interval: 1
                },
                isActive: true,
                lastModified: new Date().toISOString()
            }
        };
        return defaultConfigs[workflowId] || {
            id: workflowId,
            name: workflowId,
            description: 'Workflow-Konfiguration',
            schedule: {
                enabled: false,
                frequency: 'daily',
                time: '08:00'
            },
            isActive: true,
            lastModified: new Date().toISOString()
        };
    }
}
exports.WorkflowService = WorkflowService;
