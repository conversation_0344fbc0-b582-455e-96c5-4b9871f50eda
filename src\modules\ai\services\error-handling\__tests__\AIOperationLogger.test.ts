/**
 * AI Operation Logger Tests
 * Tests for comprehensive logging and audit trail functionality
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { AIOperationLogger } from '../AIOperationLogger';
import { AIServiceErrorCode, AIServiceErrorSeverity } from '../../types/errors';

describe('AIOperationLogger', () => {
  let logger: AIOperationLogger;

  beforeEach(() => {
    // Reset singleton instance
    (AIOperationLogger as any).instance = undefined;
    logger = AIOperationLogger.getInstance();
    
    // Mock console methods
    vi.spyOn(console, 'info').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
    logger.clearLogs();
  });

  describe('Operation Logging', () => {
    it('should start operation and return operation ID', () => {
      const operationId = logger.startOperation(
        'TestService',
        'testOperation',
        'query',
        { input: 'test' },
        'user123',
        'session456'
      );

      expect(operationId).toMatch(/^ai_op_\d+_[a-z0-9]+$/);
      
      const logs = logger.getOperationLogs();
      expect(logs).toHaveLength(1);
      expect(logs[0].id).toBe(operationId);
      expect(logs[0].service).toBe('TestService');
      expect(logs[0].operation).toBe('testOperation');
      expect(logs[0].operationType).toBe('query');
      expect(logs[0].success).toBe(false); // Not completed yet
      expect(logs[0].userId).toBe('user123');
      expect(logs[0].sessionId).toBe('session456');
    });

    it('should complete operation successfully', () => {
      const operationId = logger.startOperation('TestService', 'testOperation', 'query');
      
      // Simulate some time passing
      vi.advanceTimersByTime(100);
      
      logger.completeOperation(operationId, true, { result: 'success' });

      const logs = logger.getOperationLogs();
      expect(logs[0].success).toBe(true);
      expect(logs[0].endTime).toBeInstanceOf(Date);
      expect(logs[0].duration).toBeGreaterThan(0);
      expect(logs[0].outputData).toEqual({ result: 'success' });
      expect(console.info).toHaveBeenCalledWith(
        expect.stringContaining('TestService.testOperation completed successfully')
      );
    });

    it('should complete operation with error', () => {
      const operationId = logger.startOperation('TestService', 'testOperation', 'query');
      
      const error = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'TestService',
        operation: 'testOperation',
        timestamp: new Date(),
        recoverable: true,
        userMessage: 'User message',
        technicalMessage: 'Technical message'
      } as any;

      logger.completeOperation(operationId, false, undefined, error);

      const logs = logger.getOperationLogs();
      expect(logs[0].success).toBe(false);
      expect(logs[0].error).toBe(error);
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('TestService.testOperation failed')
      );
    });

    it('should handle completion of non-existent operation', () => {
      vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      logger.completeOperation('non-existent-id', true);
      
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('Operation non-existent-id not found')
      );
    });

    it('should sanitize sensitive data in input/output', () => {
      const sensitiveData = {
        username: 'test',
        password: 'secret123',
        apiKey: 'key123',
        token: 'token123',
        normalData: 'visible'
      };

      const operationId = logger.startOperation(
        'TestService',
        'testOperation',
        'query',
        sensitiveData
      );

      const logs = logger.getOperationLogs();
      const inputData = logs[0].inputData;
      
      expect(inputData?.username).toBe('test');
      expect(inputData?.password).toBe('[REDACTED]');
      expect(inputData?.apiKey).toBe('[REDACTED]');
      expect(inputData?.token).toBe('[REDACTED]');
      expect(inputData?.normalData).toBe('visible');
    });
  });

  describe('Error Logging', () => {
    it('should log errors with proper formatting', () => {
      const error = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        severity: AIServiceErrorSeverity.HIGH,
        service: 'TestService',
        operation: 'testOperation',
        timestamp: new Date(),
        recoverable: true,
        userMessage: 'User friendly message',
        technicalMessage: 'Technical error details',
        context: { additional: 'info' },
        originalError: new Error('Original error')
      } as any;

      logger.logError(error);

      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('TestService.testOperation: API_REQUEST_FAILED'),
        expect.objectContaining({
          severity: AIServiceErrorSeverity.HIGH,
          userMessage: 'User friendly message',
          technicalMessage: 'Technical error details'
        })
      );
    });

    it('should log successful recovery', () => {
      const error = {
        code: AIServiceErrorCode.API_TIMEOUT,
        service: 'TestService',
        operation: 'testOperation'
      } as any;

      logger.logSuccessfulRecovery(error, 2);

      expect(console.info).toHaveBeenCalledWith(
        expect.stringContaining('Successfully recovered from API_TIMEOUT after 2 attempts'),
        expect.objectContaining({
          service: 'TestService',
          operation: 'testOperation'
        })
      );
    });

    it('should log failed recovery', () => {
      const error = {
        code: AIServiceErrorCode.API_TIMEOUT,
        service: 'TestService',
        operation: 'testOperation',
        severity: AIServiceErrorSeverity.MEDIUM
      } as any;

      logger.logFailedRecovery(error, { input: 'data' });

      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to recover from API_TIMEOUT'),
        expect.objectContaining({
          service: 'TestService',
          operation: 'testOperation',
          severity: AIServiceErrorSeverity.MEDIUM,
          hadInput: true
        })
      );
    });

    it('should log fallback attempts', () => {
      const error = {
        code: AIServiceErrorCode.OPTIMIZATION_FAILED,
        service: 'TestService',
        operation: 'testOperation'
      } as any;

      logger.logSuccessfulFallback(error, 'BasicFallback');

      expect(console.info).toHaveBeenCalledWith(
        expect.stringContaining("Successfully used fallback strategy 'BasicFallback'")
      );

      logger.logFailedFallbackAttempt(error, 'AdvancedFallback', new Error('Fallback failed'));

      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining("Fallback strategy 'AdvancedFallback' failed")
      );
    });
  });

  describe('Log Retrieval', () => {
    beforeEach(() => {
      // Create test logs
      const op1 = logger.startOperation('Service1', 'operation1', 'query');
      logger.completeOperation(op1, true);
      
      const op2 = logger.startOperation('Service2', 'operation2', 'optimization');
      logger.completeOperation(op2, false, undefined, {
        code: AIServiceErrorCode.OPTIMIZATION_FAILED
      } as any);
      
      const op3 = logger.startOperation('Service1', 'operation3', 'prediction');
      logger.completeOperation(op3, true);
    });

    it('should retrieve all operation logs', () => {
      const logs = logger.getOperationLogs();
      expect(logs).toHaveLength(3);
    });

    it('should filter logs by service', () => {
      const service1Logs = logger.getOperationLogs('Service1');
      expect(service1Logs).toHaveLength(2);
      expect(service1Logs.every(log => log.service === 'Service1')).toBe(true);
    });

    it('should filter logs by operation type', () => {
      const queryLogs = logger.getOperationLogs(undefined, 'query');
      expect(queryLogs).toHaveLength(1);
      expect(queryLogs[0].operationType).toBe('query');
    });

    it('should limit number of returned logs', () => {
      const limitedLogs = logger.getOperationLogs(undefined, undefined, 2);
      expect(limitedLogs).toHaveLength(2);
    });

    it('should retrieve error logs only', () => {
      const errorLogs = logger.getErrorLogs();
      expect(errorLogs).toHaveLength(1);
      expect(errorLogs[0].success).toBe(false);
      expect(errorLogs[0].error).toBeDefined();
    });

    it('should sort logs by timestamp (newest first)', () => {
      const logs = logger.getOperationLogs();
      for (let i = 1; i < logs.length; i++) {
        expect(logs[i-1].startTime.getTime()).toBeGreaterThanOrEqual(
          logs[i].startTime.getTime()
        );
      }
    });
  });

  describe('Performance Metrics', () => {
    beforeEach(() => {
      // Create test operations with known durations
      const op1 = logger.startOperation('TestService', 'fast', 'query');
      logger.completeOperation(op1, true);
      // Manually set duration for testing
      const logs = logger.getOperationLogs();
      logs[0].duration = 100;

      const op2 = logger.startOperation('TestService', 'slow', 'query');
      logger.completeOperation(op2, true);
      logs[1].duration = 300;

      const op3 = logger.startOperation('TestService', 'failed', 'query');
      logger.completeOperation(op3, false);
      logs[2].duration = 200;
    });

    it('should calculate performance metrics for all services', () => {
      const metrics = logger.getPerformanceMetrics();
      
      expect(metrics.totalOperations).toBe(3);
      expect(metrics.averageDuration).toBe(200); // (100 + 300 + 200) / 3
      expect(metrics.successRate).toBe(66.67); // 2/3 * 100, rounded
      expect(metrics.errorRate).toBe(33.33); // 1/3 * 100, rounded
    });

    it('should calculate performance metrics for specific service', () => {
      const metrics = logger.getPerformanceMetrics('TestService');
      
      expect(metrics.totalOperations).toBe(3);
      expect(metrics.successRate).toBe(66.67);
    });

    it('should handle empty logs gracefully', () => {
      logger.clearLogs();
      const metrics = logger.getPerformanceMetrics();
      
      expect(metrics.totalOperations).toBe(0);
      expect(metrics.averageDuration).toBe(0);
      expect(metrics.successRate).toBe(0);
      expect(metrics.errorRate).toBe(0);
    });
  });

  describe('Error Summary', () => {
    it('should provide comprehensive error summary', () => {
      // Create errors to populate counters
      const error1 = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'Service1'
      } as any;

      const error2 = {
        code: AIServiceErrorCode.API_TIMEOUT,
        severity: AIServiceErrorSeverity.HIGH,
        service: 'Service2'
      } as any;

      logger.logError(error1);
      logger.logError(error2);
      logger.logError(error1); // Same error again

      const summary = logger.getErrorSummary();
      
      expect(summary.totalErrors).toBe(3);
      expect(summary.errorsByCode[AIServiceErrorCode.API_REQUEST_FAILED]).toBe(2);
      expect(summary.errorsByCode[AIServiceErrorCode.API_TIMEOUT]).toBe(1);
      expect(summary.errorsBySeverity[AIServiceErrorSeverity.MEDIUM]).toBe(2);
      expect(summary.errorsBySeverity[AIServiceErrorSeverity.HIGH]).toBe(1);
    });
  });

  describe('Log Management', () => {
    it('should clear all logs', () => {
      logger.startOperation('TestService', 'test', 'query');
      expect(logger.getOperationLogs()).toHaveLength(1);
      
      logger.clearLogs();
      expect(logger.getOperationLogs()).toHaveLength(0);
      
      expect(console.info).toHaveBeenCalledWith('[AI Logger] All logs cleared');
    });

    it('should export logs as JSON', () => {
      const operationId = logger.startOperation('TestService', 'test', 'query');
      logger.completeOperation(operationId, true);
      
      const exported = logger.exportLogs('json');
      const parsed = JSON.parse(exported);
      
      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed).toHaveLength(1);
      expect(parsed[0].service).toBe('TestService');
    });

    it('should export logs as CSV', () => {
      const operationId = logger.startOperation('TestService', 'test', 'query');
      logger.completeOperation(operationId, true);
      
      const exported = logger.exportLogs('csv');
      const lines = exported.split('\n');
      
      expect(lines[0]).toContain('ID,Service,Operation'); // Header
      expect(lines[1]).toContain('TestService,test,query'); // Data
    });

    it('should handle empty logs export', () => {
      logger.clearLogs();
      const exported = logger.exportLogs('csv');
      expect(exported).toBe('No logs available');
    });

    it('should prevent memory overflow by limiting log entries', () => {
      // Set a small limit for testing
      (logger as any).maxLogEntries = 5;
      
      // Create more logs than the limit
      for (let i = 0; i < 10; i++) {
        const opId = logger.startOperation('TestService', `operation${i}`, 'query');
        logger.completeOperation(opId, true);
      }
      
      const logs = logger.getOperationLogs();
      expect(logs.length).toBeLessThanOrEqual(5);
    });
  });

  describe('Singleton Pattern', () => {
    it('should return same instance', () => {
      const instance1 = AIOperationLogger.getInstance();
      const instance2 = AIOperationLogger.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });
});