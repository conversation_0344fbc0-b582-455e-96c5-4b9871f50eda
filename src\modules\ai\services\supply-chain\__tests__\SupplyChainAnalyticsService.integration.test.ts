/**
 * Supply Chain Analytics Service Integration Tests
 * 
 * Integration tests for the supply chain analytics service with real repositories
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { SupplyChainAnalyticsService } from '../SupplyChainAnalyticsService';

describe('SupplyChainAnalyticsService Integration Tests', () => {
  let service: SupplyChainAnalyticsService;

  beforeEach(async () => {
    service = new SupplyChainAnalyticsService({
      monitoringInterval: 0, // Disable monitoring for tests
      alertThresholds: {
        riskScore: 7,
        onTimeRate: 0.85,
        qualityRate: 0.95,
        deliveryDelay: 2
      },
      trendAnalysisPeriod: 30,
      performanceWeights: {
        onTimeDelivery: 0.4,
        quality: 0.3,
        cost: 0.2,
        reliability: 0.1
      }
    });

    await service.initialize();
  });

  afterEach(async () => {
    await service.destroy();
  });

  describe('End-to-End Analytics Workflow', () => {
    it('should complete full analytics generation workflow', async () => {
      const analytics = await service.getSupplyChainAnalytics({ days: 30 });

      // Verify complete analytics structure
      expect(analytics).toBeDefined();
      expect(analytics.overallPerformance).toBeDefined();
      expect(analytics.supplierPerformance).toBeInstanceOf(Array);
      expect(analytics.deliveryPerformance).toBeDefined();
      expect(analytics.riskAnalysis).toBeDefined();
      expect(analytics.trends).toBeInstanceOf(Array);

      // Verify overall performance metrics are realistic
      expect(analytics.overallPerformance.onTimeDeliveryRate).toBeGreaterThan(0);
      expect(analytics.overallPerformance.onTimeDeliveryRate).toBeLessThanOrEqual(1);
      expect(analytics.overallPerformance.averageDeliveryTime).toBeGreaterThan(0);
      expect(analytics.overallPerformance.averageDeliveryTime).toBeLessThan(30); // Reasonable range
      expect(analytics.overallPerformance.costEfficiency).toBeGreaterThan(0);
      expect(analytics.overallPerformance.costEfficiency).toBeLessThanOrEqual(1);
      expect(analytics.overallPerformance.supplierReliability).toBeGreaterThan(0);
      expect(analytics.overallPerformance.supplierReliability).toBeLessThanOrEqual(1);
      expect(analytics.overallPerformance.overallRiskScore).toBeGreaterThan(0);
      expect(analytics.overallPerformance.overallRiskScore).toBeLessThanOrEqual(10);

      // Verify supplier performance analytics
      expect(analytics.supplierPerformance.length).toBeGreaterThan(0);
      analytics.supplierPerformance.forEach(supplier => {
        expect(supplier.supplierId).toBeDefined();
        expect(supplier.supplierName).toBeDefined();
        expect(supplier.performanceScore).toBeGreaterThan(0);
        expect(supplier.performanceScore).toBeLessThanOrEqual(10);
        expect(supplier.riskScore).toBeGreaterThan(0);
        expect(supplier.riskScore).toBeLessThanOrEqual(10);
        expect(supplier.deliveryReliability).toBeGreaterThanOrEqual(0);
        expect(supplier.deliveryReliability).toBeLessThanOrEqual(1);
        expect(supplier.qualityScore).toBeGreaterThan(0);
        expect(supplier.qualityScore).toBeLessThanOrEqual(10);
        expect(supplier.costCompetitiveness).toBeGreaterThan(0);
        expect(supplier.costCompetitiveness).toBeLessThanOrEqual(10);
        expect(['improving', 'stable', 'declining']).toContain(supplier.trends.performance);
        expect(['increasing', 'stable', 'decreasing']).toContain(supplier.trends.risk);
      });

      // Verify delivery performance analytics
      expect(analytics.deliveryPerformance.averageDeliveryTime).toBeGreaterThan(0);
      expect(analytics.deliveryPerformance.onTimeRate).toBeGreaterThanOrEqual(0);
      expect(analytics.deliveryPerformance.onTimeRate).toBeLessThanOrEqual(1);
      expect(analytics.deliveryPerformance.delayFrequency).toBeGreaterThanOrEqual(0);
      expect(analytics.deliveryPerformance.delayFrequency).toBeLessThanOrEqual(1);
      expect(analytics.deliveryPerformance.commonDelayReasons).toBeInstanceOf(Array);
      expect(analytics.deliveryPerformance.seasonalPatterns).toBeInstanceOf(Array);

      // Verify risk analysis
      expect(['low', 'medium', 'high', 'critical']).toContain(analytics.riskAnalysis.overallRiskLevel);
      expect(analytics.riskAnalysis.topRiskFactors).toBeInstanceOf(Array);
      expect(analytics.riskAnalysis.riskTrends).toBeInstanceOf(Array);
      expect(analytics.riskAnalysis.mitigationEffectiveness).toBeGreaterThanOrEqual(0);
      expect(analytics.riskAnalysis.mitigationEffectiveness).toBeLessThanOrEqual(1);

      // Verify trends
      expect(analytics.trends.length).toBeGreaterThan(0);
      analytics.trends.forEach(trend => {
        expect(trend.metric).toBeDefined();
        expect(['improving', 'stable', 'declining']).toContain(trend.trend);
        expect(typeof trend.changeRate).toBe('number');
        expect(trend.timeframe).toBeDefined();
        expect(trend.dataPoints).toBeInstanceOf(Array);
        
        trend.dataPoints.forEach(point => {
          expect(point.date).toBeInstanceOf(Date);
          expect(typeof point.value).toBe('number');
          expect(point.context).toBeDefined();
        });
      });
    });

    it('should handle multiple concurrent analytics requests', async () => {
      const requests = [
        service.getSupplyChainAnalytics({ days: 7 }),
        service.getSupplyChainAnalytics({ days: 30 }),
        service.getSupplyChainAnalytics({ days: 90 })
      ];

      const results = await Promise.all(requests);

      expect(results).toHaveLength(3);
      results.forEach(analytics => {
        expect(analytics).toBeDefined();
        expect(analytics.overallPerformance).toBeDefined();
        expect(analytics.supplierPerformance).toBeInstanceOf(Array);
        expect(analytics.deliveryPerformance).toBeDefined();
        expect(analytics.riskAnalysis).toBeDefined();
        expect(analytics.trends).toBeInstanceOf(Array);
      });
    });
  });

  describe('End-to-End Alert Generation Workflow', () => {
    it('should complete full alert generation workflow', async () => {
      const alerts = await service.generateAlerts();

      expect(alerts).toBeInstanceOf(Array);
      
      // Verify alert structure
      alerts.forEach(alert => {
        expect(alert.alertId).toBeDefined();
        expect(['risk_increase', 'performance_decline', 'delivery_delay', 'quality_issue', 'cost_spike']).toContain(alert.type);
        expect(['low', 'medium', 'high', 'critical']).toContain(alert.severity);
        expect(alert.message).toBeDefined();
        expect(alert.details).toBeDefined();
        expect(alert.recommendations).toBeInstanceOf(Array);
        expect(alert.recommendations.length).toBeGreaterThan(0);
        expect(alert.createdAt).toBeInstanceOf(Date);
        expect(alert.acknowledged).toBe(false);
        
        // Verify timestamps are recent
        const timeDiff = Date.now() - alert.createdAt.getTime();
        expect(timeDiff).toBeLessThan(60000); // Less than 1 minute old
      });

      // Verify alerts are cached
      const cachedAlerts = service.getAlerts();
      expect(cachedAlerts.length).toBe(alerts.length);
      expect(cachedAlerts).toEqual(alerts);
    });

    it('should generate different types of alerts', async () => {
      const alerts = await service.generateAlerts();

      // Should have at least some alerts (based on mock data)
      expect(alerts.length).toBeGreaterThan(0);

      // Check for different alert types
      const alertTypes = new Set(alerts.map(a => a.type));
      expect(alertTypes.size).toBeGreaterThan(0);

      // Verify severity distribution
      const severities = new Set(alerts.map(a => a.severity));
      expect(severities.size).toBeGreaterThan(0);
    });

    it('should handle alert acknowledgment workflow', async () => {
      const alerts = await service.generateAlerts();
      expect(alerts.length).toBeGreaterThan(0);

      const alertToAcknowledge = alerts[0];
      expect(alertToAcknowledge.acknowledged).toBe(false);

      // Acknowledge the alert
      const success = service.acknowledgeAlert(alertToAcknowledge.alertId);
      expect(success).toBe(true);

      // Verify alert is acknowledged in cache
      const cachedAlerts = service.getAlerts();
      const acknowledgedAlert = cachedAlerts.find(a => a.alertId === alertToAcknowledge.alertId);
      expect(acknowledgedAlert).toBeDefined();
      expect(acknowledgedAlert?.acknowledged).toBe(true);
    });
  });

  describe('End-to-End Recommendation Generation Workflow', () => {
    it('should complete full recommendation generation workflow', async () => {
      const recommendations = await service.generateRecommendations();

      expect(recommendations).toBeInstanceOf(Array);
      expect(recommendations.length).toBeGreaterThan(0);

      // Verify recommendation structure
      recommendations.forEach(rec => {
        expect(rec.recommendationId).toBeDefined();
        expect(['supplier_diversification', 'inventory_optimization', 'route_optimization', 'risk_mitigation']).toContain(rec.type);
        expect(['low', 'medium', 'high']).toContain(rec.priority);
        expect(rec.title).toBeDefined();
        expect(rec.description).toBeDefined();
        expect(rec.expectedBenefit).toBeDefined();
        expect(rec.implementationSteps).toBeInstanceOf(Array);
        expect(rec.implementationSteps.length).toBeGreaterThan(0);
        expect(rec.estimatedCost).toBeGreaterThanOrEqual(0);
        expect(rec.estimatedSavings).toBeGreaterThanOrEqual(0);
        expect(rec.timeframe).toBeDefined();
        expect(rec.createdAt).toBeInstanceOf(Date);
        
        // Verify timestamps are recent
        const timeDiff = Date.now() - rec.createdAt.getTime();
        expect(timeDiff).toBeLessThan(60000); // Less than 1 minute old
        
        // Verify implementation steps are meaningful
        rec.implementationSteps.forEach(step => {
          expect(step.length).toBeGreaterThan(10); // Reasonable step description length
        });
      });

      // Verify recommendations are cached and sorted by priority
      const cachedRecommendations = service.getRecommendations();
      expect(cachedRecommendations.length).toBe(recommendations.length);
      
      // Should be sorted by priority (high, medium, low)
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      for (let i = 0; i < cachedRecommendations.length - 1; i++) {
        const currentPriority = priorityOrder[cachedRecommendations[i].priority];
        const nextPriority = priorityOrder[cachedRecommendations[i + 1].priority];
        expect(currentPriority).toBeGreaterThanOrEqual(nextPriority);
      }
    });

    it('should generate contextually appropriate recommendations', async () => {
      const recommendations = await service.generateRecommendations();

      // Should have different types of recommendations
      const recommendationTypes = new Set(recommendations.map(r => r.type));
      expect(recommendationTypes.size).toBeGreaterThan(0);

      // Verify ROI calculations are positive for most recommendations
      const positiveROI = recommendations.filter(r => r.estimatedSavings > r.estimatedCost);
      expect(positiveROI.length).toBeGreaterThan(0);

      // Verify high-priority recommendations have higher potential savings
      const highPriorityRecs = recommendations.filter(r => r.priority === 'high');
      const lowPriorityRecs = recommendations.filter(r => r.priority === 'low');
      
      if (highPriorityRecs.length > 0 && lowPriorityRecs.length > 0) {
        const avgHighPrioritySavings = highPriorityRecs.reduce((sum, r) => sum + r.estimatedSavings, 0) / highPriorityRecs.length;
        const avgLowPrioritySavings = lowPriorityRecs.reduce((sum, r) => sum + r.estimatedSavings, 0) / lowPriorityRecs.length;
        
        expect(avgHighPrioritySavings).toBeGreaterThanOrEqual(avgLowPrioritySavings);
      }
    });
  });

  describe('End-to-End Supplier Performance Analysis', () => {
    it('should complete full supplier performance analysis', async () => {
      const supplierAnalytics = await service.getSupplierPerformanceAnalytics({ days: 30 });

      expect(supplierAnalytics).toBeInstanceOf(Array);
      expect(supplierAnalytics.length).toBeGreaterThan(0);

      // Verify suppliers are ranked by performance
      for (let i = 0; i < supplierAnalytics.length - 1; i++) {
        expect(supplierAnalytics[i].performanceScore).toBeGreaterThanOrEqual(supplierAnalytics[i + 1].performanceScore);
      }

      // Verify performance scores are calculated correctly
      supplierAnalytics.forEach(supplier => {
        // Performance score should correlate with individual metrics
        const expectedHighPerformance = 
          supplier.deliveryReliability > 0.9 && 
          supplier.qualityScore > 9 && 
          supplier.costCompetitiveness > 8;
        
        if (expectedHighPerformance) {
          expect(supplier.performanceScore).toBeGreaterThan(7);
          expect(supplier.riskScore).toBeLessThan(5);
        }
        
        // Risk score should be inverse of performance
        expect(supplier.riskScore + supplier.performanceScore).toBeGreaterThan(8);
        expect(supplier.riskScore + supplier.performanceScore).toBeLessThan(20);
      });
    });

    it('should analyze supplier trends correctly', async () => {
      const supplierAnalytics = await service.getSupplierPerformanceAnalytics({ days: 90 });

      supplierAnalytics.forEach(supplier => {
        // Trends should be consistent
        if (supplier.trends.performance === 'improving') {
          expect(supplier.trends.risk).toBe('decreasing');
        } else if (supplier.trends.performance === 'declining') {
          expect(supplier.trends.risk).toBe('increasing');
        }
        
        // Performance and risk trends should be logical
        expect(['improving', 'stable', 'declining']).toContain(supplier.trends.performance);
        expect(['increasing', 'stable', 'decreasing']).toContain(supplier.trends.risk);
      });
    });
  });

  describe('End-to-End Delivery Performance Analysis', () => {
    it('should complete full delivery performance analysis', async () => {
      const deliveryAnalytics = await service.getDeliveryPerformanceAnalytics({ days: 30 });

      expect(deliveryAnalytics).toBeDefined();
      expect(deliveryAnalytics.averageDeliveryTime).toBeGreaterThan(0);
      expect(deliveryAnalytics.onTimeRate).toBeGreaterThanOrEqual(0);
      expect(deliveryAnalytics.onTimeRate).toBeLessThanOrEqual(1);
      expect(deliveryAnalytics.delayFrequency).toBeGreaterThanOrEqual(0);
      expect(deliveryAnalytics.delayFrequency).toBeLessThanOrEqual(1);

      // On-time rate and delay frequency should be complementary
      expect(deliveryAnalytics.onTimeRate + deliveryAnalytics.delayFrequency).toBeCloseTo(1, 1);

      // Verify delay reasons
      expect(deliveryAnalytics.commonDelayReasons).toBeInstanceOf(Array);
      deliveryAnalytics.commonDelayReasons.forEach(reason => {
        expect(reason.reason).toBeDefined();
        expect(reason.frequency).toBeGreaterThan(0);
        expect(reason.averageDelay).toBeGreaterThan(0);
        expect(['low', 'medium', 'high']).toContain(reason.impact);
      });

      // Verify seasonal patterns
      expect(deliveryAnalytics.seasonalPatterns).toBeInstanceOf(Array);
      deliveryAnalytics.seasonalPatterns.forEach(pattern => {
        expect(pattern.season).toBeDefined();
        expect(pattern.deliveryTimeFactor).toBeGreaterThan(0);
        expect(pattern.riskFactor).toBeGreaterThan(0);
        expect(pattern.description).toBeDefined();
      });
    });
  });

  describe('End-to-End Risk Analysis', () => {
    it('should complete full risk analysis', async () => {
      const riskAnalytics = await service.getRiskAnalytics({ days: 30 });

      expect(riskAnalytics).toBeDefined();
      expect(['low', 'medium', 'high', 'critical']).toContain(riskAnalytics.overallRiskLevel);
      expect(riskAnalytics.mitigationEffectiveness).toBeGreaterThanOrEqual(0);
      expect(riskAnalytics.mitigationEffectiveness).toBeLessThanOrEqual(1);

      // Verify top risk factors
      expect(riskAnalytics.topRiskFactors).toBeInstanceOf(Array);
      riskAnalytics.topRiskFactors.forEach(factor => {
        expect(factor.type).toBeDefined();
        expect(['low', 'medium', 'high']).toContain(factor.severity);
        expect(factor.probability).toBeGreaterThanOrEqual(0);
        expect(factor.probability).toBeLessThanOrEqual(1);
        expect(factor.description).toBeDefined();
        expect(factor.impact).toBeGreaterThanOrEqual(0);
      });

      // Risk factors should be sorted by impact
      for (let i = 0; i < riskAnalytics.topRiskFactors.length - 1; i++) {
        expect(riskAnalytics.topRiskFactors[i].impact).toBeGreaterThanOrEqual(riskAnalytics.topRiskFactors[i + 1].impact);
      }

      // Verify risk trends
      expect(riskAnalytics.riskTrends).toBeInstanceOf(Array);
      riskAnalytics.riskTrends.forEach(trend => {
        expect(trend.riskType).toBeDefined();
        expect(['increasing', 'stable', 'decreasing']).toContain(trend.trend);
        expect(typeof trend.changeRate).toBe('number');
        expect(trend.timeframe).toBeDefined();
      });
    });
  });

  describe('End-to-End Trend Analysis', () => {
    it('should complete full trend analysis', async () => {
      const trends = await service.getTrendAnalysis({ days: 30 });

      expect(trends).toBeInstanceOf(Array);
      expect(trends.length).toBeGreaterThan(0);

      trends.forEach(trend => {
        expect(trend.metric).toBeDefined();
        expect(['improving', 'stable', 'declining']).toContain(trend.trend);
        expect(typeof trend.changeRate).toBe('number');
        expect(trend.timeframe).toBeDefined();
        expect(trend.dataPoints).toBeInstanceOf(Array);
        expect(trend.dataPoints.length).toBeGreaterThan(0);

        // Verify data points are chronologically ordered
        for (let i = 0; i < trend.dataPoints.length - 1; i++) {
          expect(trend.dataPoints[i].date.getTime()).toBeLessThanOrEqual(trend.dataPoints[i + 1].date.getTime());
        }

        // Verify trend direction matches change rate
        if (trend.changeRate > 5) {
          expect(trend.trend).toBe('improving');
        } else if (trend.changeRate < -5) {
          expect(trend.trend).toBe('declining');
        }
      });
    });

    it('should analyze different metrics', async () => {
      const trends = await service.getTrendAnalysis({ days: 60 });

      const metrics = new Set(trends.map(t => t.metric));
      expect(metrics.size).toBeGreaterThan(1);

      // Should include key metrics
      const expectedMetrics = ['Pünktlichkeitsrate', 'Durchschnittliche Lieferzeit', 'Lieferkosten'];
      const foundMetrics = trends.map(t => t.metric);
      
      expectedMetrics.forEach(expectedMetric => {
        expect(foundMetrics).toContain(expectedMetric);
      });
    });
  });

  describe('Service Performance and Reliability', () => {
    it('should maintain performance under load', async () => {
      const operations = [
        () => service.getSupplyChainAnalytics({ days: 30 }),
        () => service.generateAlerts(),
        () => service.generateRecommendations(),
        () => service.getSupplierPerformanceAnalytics({ days: 30 }),
        () => service.getDeliveryPerformanceAnalytics({ days: 30 }),
        () => service.getRiskAnalytics({ days: 30 }),
        () => service.getTrendAnalysis({ days: 30 })
      ];

      const startTime = Date.now();
      const results = await Promise.all(operations.map(op => op()));
      const duration = Date.now() - startTime;

      // All operations should complete
      expect(results).toHaveLength(operations.length);
      results.forEach(result => {
        expect(result).toBeDefined();
      });

      // Should complete within reasonable time
      expect(duration).toBeLessThan(15000); // 15 seconds for all operations
    });

    it('should handle service restart gracefully', async () => {
      // Generate initial data
      const initialAnalytics = await service.getSupplyChainAnalytics({ days: 30 });
      const initialAlerts = await service.generateAlerts();
      
      expect(initialAnalytics).toBeDefined();
      expect(initialAlerts.length).toBeGreaterThan(0);

      // Restart service
      await service.destroy();
      await service.initialize();

      // Generate data after restart
      const restartAnalytics = await service.getSupplyChainAnalytics({ days: 30 });
      const restartAlerts = await service.generateAlerts();

      expect(restartAnalytics).toBeDefined();
      expect(restartAlerts).toBeInstanceOf(Array);

      // Data should be consistent (structure-wise)
      expect(restartAnalytics.overallPerformance).toBeDefined();
      expect(restartAnalytics.supplierPerformance).toBeInstanceOf(Array);
    });

    it('should provide consistent health status', () => {
      const health1 = service.getHealthStatus();
      const health2 = service.getHealthStatus();

      expect(health1.status).toBe(health2.status);
      expect(health1.details.configuration).toEqual(health2.details.configuration);
    });
  });

  describe('Data Integration and Consistency', () => {
    it('should maintain data consistency across operations', async () => {
      const analytics = await service.getSupplyChainAnalytics({ days: 30 });
      const supplierAnalytics = await service.getSupplierPerformanceAnalytics({ days: 30 });
      const alerts = await service.generateAlerts();

      // Supplier count should be consistent
      expect(analytics.supplierPerformance.length).toBe(supplierAnalytics.length);

      // Supplier IDs should match
      const analyticsSupplierIds = new Set(analytics.supplierPerformance.map(s => s.supplierId));
      const supplierAnalyticsIds = new Set(supplierAnalytics.map(s => s.supplierId));
      expect(analyticsSupplierIds).toEqual(supplierAnalyticsIds);

      // Alerts should reference existing suppliers
      const supplierAlerts = alerts.filter(a => a.supplierId);
      supplierAlerts.forEach(alert => {
        expect(analyticsSupplierIds.has(alert.supplierId!)).toBe(true);
      });
    });

    it('should handle cross-service data dependencies', async () => {
      const analytics = await service.getSupplyChainAnalytics({ days: 30 });
      const recommendations = await service.generateRecommendations();

      // Recommendations should be based on analytics
      expect(recommendations.length).toBeGreaterThan(0);

      // High-risk scenarios should generate more recommendations
      const highRiskSuppliers = analytics.supplierPerformance.filter(s => s.riskScore > 7);
      const diversificationRecs = recommendations.filter(r => r.type === 'supplier_diversification');

      if (highRiskSuppliers.length > 0) {
        expect(diversificationRecs.length).toBeGreaterThan(0);
      }

      // Poor delivery performance should generate inventory recommendations
      if (analytics.deliveryPerformance.onTimeRate < 0.85) {
        const inventoryRecs = recommendations.filter(r => r.type === 'inventory_optimization');
        expect(inventoryRecs.length).toBeGreaterThan(0);
      }
    });
  });
});