const http = require('http');

async function testBackendConnection() {
  console.log('🔍 Testing backend connection...');
  
  // Test 1: Direct connection to backend
  console.log('\n📡 Test 1: Direct connection to localhost:3001');
  
  const testHealth = () => {
    return new Promise((resolve, reject) => {
      const req = http.get('http://localhost:3001/api/health', {
        timeout: 5000,
        headers: {
          'User-Agent': 'Debug-Script',
          'Accept': 'application/json'
        }
      }, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          console.log(`✅ Status: ${res.statusCode}`);
          console.log(`✅ Response: ${data}`);
          resolve({ status: res.statusCode, data });
        });
      });

      req.on('error', (error) => {
        console.error(`❌ Connection error: ${error.message}`);
        reject(error);
      });

      req.on('timeout', () => {
        console.error('❌ Request timeout (5s)');
        req.destroy();
        reject(new Error('Timeout'));
      });
    });
  };

  try {
    await testHealth();
    console.log('\n✅ Backend connection successful!');
    
    // Test 2: RAG endpoint
    console.log('\n📡 Test 2: RAG endpoint test');
    
    const testRAG = () => {
      return new Promise((resolve, reject) => {
        const req = http.get('http://localhost:3001/api/rag/test', {
          timeout: 5000,
          headers: {
            'User-Agent': 'Debug-Script',
            'Accept': 'application/json'
          }
        }, (res) => {
          let data = '';
          
          res.on('data', (chunk) => {
            data += chunk;
          });
          
          res.on('end', () => {
            console.log(`✅ RAG Status: ${res.statusCode}`);
            console.log(`✅ RAG Response: ${data}`);
            resolve({ status: res.statusCode, data });
          });
        });

        req.on('error', (error) => {
          console.error(`❌ RAG Connection error: ${error.message}`);
          reject(error);
        });

        req.on('timeout', () => {
          console.error('❌ RAG Request timeout (5s)');
          req.destroy();
          reject(new Error('RAG Timeout'));
        });
      });
    };

    await testRAG();
    console.log('\n✅ RAG endpoint successful!');
    
  } catch (error) {
    console.error('\n❌ Backend connection failed:', error.message);
    console.log('\n💡 Troubleshooting tips:');
    console.log('1. Make sure the backend is running: npm run start:dev');
    console.log('2. Check if port 3001 is available');
    console.log('3. Verify backend dependencies are installed: cd backend && npm install');
    console.log('4. Check backend logs for errors');
  }
}

// Run the test
testBackendConnection().catch(console.error);