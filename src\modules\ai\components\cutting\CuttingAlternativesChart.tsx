import React, { memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer } from 'recharts';
import { CuttingAlternative } from '../../services/types';
import { TrendingUp, Clock, AlertCircle, CheckCircle, Star } from 'lucide-react';

interface CuttingAlternativesChartProps {
  alternatives: CuttingAlternative[];
  onSelectAlternative: (alternative: CuttingAlternative) => void;
}

/**
 * Cutting Alternatives Chart Component
 * 
 * Displays and compares different cutting optimization alternatives
 * with performance metrics and selection functionality.
 */
export const CuttingAlternativesChart = memo(function CuttingAlternativesChart({
  alternatives,
  onSelectAlternative
}: CuttingAlternativesChartProps) {

  // Prepare data for comparison chart
  const comparisonData = alternatives.slice(0, 6).map((alt, index) => ({
    id: alt.id,
    name: `Alt ${index + 1}`,
    score: Math.round(alt.score * 100) / 100,
    efficiency: Math.round(alt.plan.efficiency * 100),
    waste: Math.round(alt.plan.totalWaste),
    time: Math.round(alt.plan.estimatedTime / 3600 * 10) / 10, // Hours
    cuts: alt.plan.drumAllocations.reduce((sum, allocation) => sum + allocation.cuts.length, 0)
  }));

  // Prepare radar chart data for top 3 alternatives
  const radarData = alternatives.slice(0, 3).map((alt, index) => {
    const efficiency = alt.plan.efficiency * 100;
    const wasteScore = Math.max(0, 100 - (alt.plan.totalWaste / 10)); // Inverse waste score
    const timeScore = Math.max(0, 100 - (alt.plan.estimatedTime / 3600)); // Inverse time score
    const utilizationScore = alt.plan.drumAllocations.reduce((sum, allocation) =>
      sum + allocation.utilization, 0) / alt.plan.drumAllocations.length * 100;

    return {
      alternative: `Alternative ${index + 1}`,
      Effizienz: Math.round(efficiency),
      'Verschnitt-Score': Math.round(wasteScore),
      'Zeit-Score': Math.round(timeScore),
      'Auslastung': Math.round(utilizationScore),
      'Gesamt-Score': Math.round(alt.score)
    };
  });

  // Chart configuration
  const chartConfig = {
    score: {
      label: "Score",
      color: "var(--chart-1)",
    },
    efficiency: {
      label: "Effizienz (%)",
      color: "var(--chart-2)",
    },
    waste: {
      label: "Verschnitt (m)",
      color: "var(--chart-3)",
    },
    time: {
      label: "Zeit (h)",
      color: "var(--chart-4)",
    }
  };

  // Get best alternative
  const bestAlternative = alternatives.reduce((best, current) =>
    current.score > best.score ? current : best, alternatives[0]);

  return (
    <div className="space-y-6">
      {/* Alternatives Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {alternatives.slice(0, 6).map((alternative, index) => {
          const isBest = alternative.id === bestAlternative.id;
          const totalCuts = alternative.plan.drumAllocations.reduce((sum, allocation) =>
            sum + allocation.cuts.length, 0);
          const estimatedHours = Math.round(alternative.plan.estimatedTime / 3600 * 10) / 10;

          return (
            <Card
              key={alternative.id}
              className={`border-2 ${isBest ? 'border-green-500 bg-green-50' : 'border-[#ff7a05]'} 
                         hover:shadow-lg transition-shadow cursor-pointer`}
              onClick={() => onSelectAlternative(alternative)}
            >
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">
                    Alternative {index + 1}
                    {isBest && <Star className="inline ml-2 h-4 w-4 text-yellow-500" />}
                  </CardTitle>
                  <Badge variant={isBest ? "default" : "outline"}>
                    Score: {Math.round(alternative.score * 100) / 100}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Key Metrics */}
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="flex items-center space-x-1">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <span>{Math.round(alternative.plan.efficiency * 100)}% Effizienz</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <AlertCircle className="h-4 w-4 text-orange-600" />
                    <span>{parseFloat(alternative.plan.totalWaste.toFixed(1))} m Verschnitt</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <span>{estimatedHours}h Zeit</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="h-4 w-4 text-purple-600" />
                    <span>{totalCuts} Schnitte</span>
                  </div>
                </div>

                {/* Advantages */}
                <div>
                  <p className="text-xs font-medium text-green-700 mb-1">Vorteile:</p>
                  <ul className="text-xs text-green-600 space-y-1">
                    {alternative.advantages.slice(0, 2).map((advantage, i) => (
                      <li key={i}>• {advantage}</li>
                    ))}
                  </ul>
                </div>

                {/* Disadvantages */}
                <div>
                  <p className="text-xs font-medium text-red-700 mb-1">Nachteile:</p>
                  <ul className="text-xs text-red-600 space-y-1">
                    {alternative.disadvantages.slice(0, 2).map((disadvantage, i) => (
                      <li key={i}>• {disadvantage}</li>
                    ))}
                  </ul>
                </div>

                <Button
                  onClick={(e) => {
                    e.stopPropagation();
                    onSelectAlternative(alternative);
                  }}
                  variant={isBest ? "default" : "outline"}
                  size="sm"
                  className="w-full"
                >
                  {isBest ? 'Beste Wahl' : 'Auswählen'}
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Comparison Bar Chart */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle>Alternativen-Vergleich</CardTitle>
            <CardDescription>
              Vergleich der wichtigsten Kennzahlen
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <BarChart data={comparisonData} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis
                  dataKey="name"
                  className="text-xs font-bold"
                  tick={{ fontSize: 12 }}
                />
                <YAxis
                  className="text-xs font-bold"
                  tick={{ fill: "#000000" }}
                  label={{
                    value: "Score",
                    angle: -90,
                    position: "insideLeft",
                    style: { textAnchor: "middle", fontSize: 12 }
                  }}
                />
                <ChartTooltip
                  content={
                    <ChartTooltipContent
                      labelFormatter={(label) => `${label}`}
                      formatter={(value, name) => [
                        `${value}${name === 'efficiency' ? '%' : name === 'time' ? 'h' : name === 'waste' ? ' m' : ''}`,
                        name === 'score' ? 'Score' :
                          name === 'efficiency' ? 'Effizienz' :
                            name === 'waste' ? 'Verschnitt' :
                              name === 'time' ? 'Zeit' : 'Schnitte'
                      ]}
                    />
                  }
                />
                <Bar
                  dataKey="score"
                  name="Score"
                  fill={chartConfig.score.color}
                  stroke="#000000"
                  strokeWidth={2}
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Radar Chart for Top 3 */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle>Performance-Radar (Top 3)</CardTitle>
            <CardDescription>
              Mehrdimensionaler Vergleich der besten Alternativen
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart
                  data={[
                    { subject: 'Effizienz', ...radarData.reduce((acc, data, index) => ({ ...acc, [`alt${index + 1}`]: data.Effizienz }), {}) },
                    { subject: 'Verschnitt-Score', ...radarData.reduce((acc, data, index) => ({ ...acc, [`alt${index + 1}`]: data['Verschnitt-Score'] }), {}) },
                    { subject: 'Zeit-Score', ...radarData.reduce((acc, data, index) => ({ ...acc, [`alt${index + 1}`]: data['Zeit-Score'] }), {}) },
                    { subject: 'Auslastung', ...radarData.reduce((acc, data, index) => ({ ...acc, [`alt${index + 1}`]: data.Auslastung }), {}) },
                    { subject: 'Gesamt-Score', ...radarData.reduce((acc, data, index) => ({ ...acc, [`alt${index + 1}`]: data['Gesamt-Score'] }), {}) }
                  ]}
                >
                  <PolarGrid />
                  <PolarAngleAxis
                    dataKey="subject"
                    tick={{ fontSize: 10 }}
                  />
                  <PolarRadiusAxis
                    angle={90}
                    domain={[0, 100]}
                    tick={{ fontSize: 8 }}
                  />
                  {radarData.map((data, index) => (
                    <Radar
                      key={index}
                      name={data.alternative}
                      dataKey={`alt${index + 1}`}
                      stroke={`var(--chart-${index + 1})`}
                      fill={`var(--chart-${index + 1})`}
                      fillOpacity={0.1}
                      strokeWidth={2}
                    />
                  ))}
                  <ChartTooltip
                    content={({ active, payload, label }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="bg-white p-2 border border-gray-300 rounded shadow">
                            <p className="font-medium">{label}</p>
                            {payload.map((entry, index) => (
                              <p key={index} className="text-sm">
                                {radarData[index]?.alternative}: {entry.value}
                              </p>
                            ))}
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                </RadarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Comparison Table */}
      <Card className="border-[#ff7a05]">
        <CardHeader>
          <CardTitle>Detaillierter Vergleich</CardTitle>
          <CardDescription>
            Vollständige Übersicht aller Alternativen
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Alternative</th>
                  <th className="text-right p-2">Score</th>
                  <th className="text-right p-2">Effizienz</th>
                  <th className="text-right p-2">Verschnitt</th>
                  <th className="text-right p-2">Zeit</th>
                  <th className="text-right p-2">Schnitte</th>
                  <th className="text-center p-2">Aktion</th>
                </tr>
              </thead>
              <tbody>
                {comparisonData.map((data, index) => {
                  const alternative = alternatives[index];
                  const isBest = alternative.id === bestAlternative.id;

                  return (
                    <tr
                      key={data.id}
                      className={`border-b hover:bg-gray-50 ${isBest ? 'bg-green-50' : ''}`}
                    >
                      <td className="p-2 font-medium">
                        {data.name}
                        {isBest && <Star className="inline ml-1 h-3 w-3 text-yellow-500" />}
                      </td>
                      <td className="p-2 text-right font-medium">{data.score}</td>
                      <td className="p-2 text-right">{data.efficiency}%</td>
                      <td className="p-2 text-right">{data.waste} m</td>
                      <td className="p-2 text-right">{data.time}h</td>
                      <td className="p-2 text-right">{data.cuts}</td>
                      <td className="p-2 text-center">
                        <Button
                          onClick={() => onSelectAlternative(alternative)}
                          size="sm"
                          variant={isBest ? "default" : "outline"}
                        >
                          Wählen
                        </Button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
});