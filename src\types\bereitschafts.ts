import { Eskalationslevel, StoerungStatus, Stoerung } from './stoerungen.types';

export interface BereitschaftsPerson {
  id: number;
  name: string;
  telefon: string;
  email: string;
  abteilung: string;
  aktiv: boolean;
  reihenfolge: number;
  createdAt: string;
  updatedAt: string;
  bereitschaftsWochen?: BereitschaftsWoche[];
  bereitschaftsAusnahmen?: BereitschaftsAusnahme[];
}

export interface BereitschaftsWoche {
  id: number;
  personId: number;
  person: BereitschaftsPerson;
  wochenStart: string;
  wochenEnde: string;
  von: string;
  bis: string;
  aktiv: boolean;
  notiz?: string;
  createdAt: string;
  updatedAt: string;
  ausnahmen?: BereitschaftsAusnahme[];
  hatAusnahme?: boolean;
}

export interface BereitschaftsAusnahme {
  id: number;
  personId: number;
  person: BereitschaftsPerson;
  von: string;
  bis: string;
  grund: string;
  ersatzPersonId?: number;
  aktiv: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface BereitschaftsKonfiguration {
  id: number;
  wechselTag: number; // 1 = Montag, 7 = Sonntag
  wechselUhrzeit: string; // Format: "HH:MM"
  rotationAktiv: boolean;
  benachrichtigungTage: number;
  emailBenachrichtigung: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface BereitschaftsStatistiken {
  zeitraum: {
    von: string;
    bis: string;
  };
  gesamtBereitschaften: number;
  personenStatistiken: {
    person: BereitschaftsPerson;
    anzahlBereitschaften: number;
    gesamtStunden: number;
    durchschnittStundenProWoche: number;
  }[];
}

// --- Eskalationsmatrix Typen ---

export interface EskalationsBedingung {
  severity?: Stoerung['severity'][];
  affected_system?: string[];
  category?: string[];
  zeit_nach_erstellung_minuten?: number;
}

export interface EskalationsAktion {
  neuer_status?: StoerungStatus;
  neues_eskalationslevel?: Eskalationslevel;
  benachrichtige_personId?: number[];
  benachrichtige_email?: string[];
}

export interface EskalationsRegel {
  id: number;
  name: string;
  beschreibung?: string;
  aktiv: boolean;
  prioritaet: number;
  bedingungen: EskalationsBedingung;
  aktion: EskalationsAktion;
  createdAt: string;
  updatedAt: string;
}

// --- Form-Interfaces für API-Requests ---

export interface CreateBereitschaftsPersonRequest {
  name: string;
  telefon: string;
  email: string;
  abteilung: string;
  reihenfolge?: number;
}

export interface UpdateBereitschaftsPersonRequest {
  name?: string;
  telefon?: string;
  email?: string;
  abteilung?: string;
  aktiv?: boolean;
  reihenfolge?: number;
}

export interface CreateBereitschaftsWocheRequest {
  personId: number;
  wochenStart: string;
  wochenEnde: string;
  von: string;
  bis: string;
  notiz?: string;
}

export interface UpdateBereitschaftsWocheRequest {
  personId?: number;
  wochenStart?: string;
  wochenEnde?: string;
  von?: string;
  bis?: string;
  aktiv?: boolean;
  notiz?: string;
}

export interface CreateBereitschaftsAusnahmeRequest {
  personId: number;
  von: string;
  bis: string;
  grund: string;
  ersatzPersonId?: number;
}

export interface UpdateBereitschaftsAusnahmeRequest {
  personId?: number;
  von?: string;
  bis?: string;
  grund?: string;
  ersatzPersonId?: number;
  aktiv?: boolean;
}

export interface UpdateBereitschaftsKonfigurationRequest {
  wechselTag?: number;
  wechselUhrzeit?: string;
  rotationAktiv?: boolean;
  benachrichtigungTage?: number;
  emailBenachrichtigung?: boolean;
}

export interface GenerateWochenplanRequest {
  startDate: string;
  anzahlWochen: number;
}

export interface UpdatePersonenReihenfolgeRequest {
  personenIds: number[];
}

export interface CreateEskalationsRegelRequest {
    name: string;
    beschreibung?: string;
    aktiv: boolean;
    prioritaet: number;
    bedingungen: EskalationsBedingung;
    aktion: EskalationsAktion;
}

export interface UpdateEskalationsRegelRequest extends Partial<CreateEskalationsRegelRequest> {}


// Utility Types
export type WochenTag = 1 | 2 | 3 | 4 | 5 | 6 | 7;

export const WOCHEN_TAGE: Record<WochenTag, string> = {
  1: 'Montag',
  2: 'Dienstag',
  3: 'Mittwoch',
  4: 'Donnerstag',
  5: 'Freitag',
  6: 'Samstag',
  7: 'Sonntag'
};

export const ABTEILUNGEN = [
  'LSI',
  'Versand',
  'Ablängerei',
  'Wareneingang',
  'IT',
  'Qualität',
  'Produktion'
] as const;

export type Abteilung = typeof ABTEILUNGEN[number];