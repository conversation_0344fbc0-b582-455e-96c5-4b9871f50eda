/**
 * Reporting Types
 * 
 * Type definitions for the automated reporting system
 */

/**
 * Report template configuration
 */
export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: 'kpi' | 'performance' | 'analysis' | 'custom';
  department?: 'dispatch' | 'cutting' | 'incoming-goods' | 'all';
  sections: ReportSection[];
  format: 'pdf' | 'excel' | 'html' | 'json';
  schedule?: ReportSchedule;
  recipients?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Report section configuration
 */
export interface ReportSection {
  id: string;
  title: string;
  type: 'chart' | 'table' | 'kpi' | 'text' | 'insights';
  dataSource: string;
  query?: string;
  chartType?: 'line' | 'bar' | 'pie' | 'area' | 'scatter';
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max';
  timeRange?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  filters?: Record<string, any>;
  order: number;
}

/**
 * Report scheduling configuration
 */
export interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  time: string; // HH:MM format
  dayOfWeek?: number; // 0-6 for weekly
  dayOfMonth?: number; // 1-31 for monthly
  timezone: string;
  isActive: boolean;
}

/**
 * Generated report data
 */
export interface GeneratedReport {
  id: string;
  templateId: string;
  title: string;
  generatedAt: Date;
  timeRange: {
    start: Date;
    end: Date;
  };
  sections: ReportSectionData[];
  insights: ReportInsight[];
  recommendations: ReportRecommendation[];
  metadata: {
    dataPoints: number;
    processingTime: number;
    sources: string[];
  };
  format: 'pdf' | 'excel' | 'html' | 'json';
  filePath?: string;
  size?: number;
}

/**
 * Report section data
 */
export interface ReportSectionData {
  sectionId: string;
  title: string;
  type: string;
  data: any[];
  chartConfig?: any;
  summary?: {
    total?: number;
    average?: number;
    trend?: 'up' | 'down' | 'stable';
    change?: number;
  };
}

/**
 * AI-generated insights
 */
export interface ReportInsight {
  id: string;
  type: 'trend' | 'anomaly' | 'pattern' | 'correlation';
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  confidence: number;
  dataPoints: string[];
  visualizationSuggestion?: string;
}

/**
 * AI-generated recommendations
 */
export interface ReportRecommendation {
  id: string;
  category: 'efficiency' | 'cost' | 'quality' | 'process' | 'resource';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  expectedBenefit: string;
  actionItems: string[];
  kpiImpact: string[];
}

/**
 * Report generation request
 */
export interface ReportGenerationRequest {
  templateId: string;
  timeRange?: {
    start: Date;
    end: Date;
  };
  format?: 'pdf' | 'excel' | 'html' | 'json';
  includeInsights?: boolean;
  includeRecommendations?: boolean;
  customFilters?: Record<string, any>;
}

/**
 * Report distribution configuration
 */
export interface ReportDistribution {
  id: string;
  reportId: string;
  method: 'email' | 'dashboard' | 'file' | 'api';
  recipients: string[];
  subject?: string;
  message?: string;
  scheduledAt?: Date;
  status: 'pending' | 'sent' | 'failed';
  sentAt?: Date;
  error?: string;
}

/**
 * Report builder form data
 */
export interface ReportBuilderFormData {
  name: string;
  description: string;
  type: 'kpi' | 'performance' | 'analysis' | 'custom';
  department: 'dispatch' | 'cutting' | 'incoming-goods' | 'all';
  format: 'pdf' | 'excel' | 'html' | 'json';
  sections: ReportSectionFormData[];
  schedule?: ReportScheduleFormData;
  recipients: string[];
  isActive: boolean;
}

/**
 * Report section form data
 */
export interface ReportSectionFormData {
  title: string;
  type: 'chart' | 'table' | 'kpi' | 'text' | 'insights';
  dataSource: string;
  chartType?: 'line' | 'bar' | 'pie' | 'area' | 'scatter';
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max';
  timeRange?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  filters?: Record<string, any>;
}

/**
 * Report schedule form data
 */
export interface ReportScheduleFormData {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  time: string;
  dayOfWeek?: number;
  dayOfMonth?: number;
  timezone: string;
  isActive: boolean;
}

/**
 * Report export options
 */
export interface ReportExportOptions {
  format: 'pdf' | 'excel' | 'html' | 'json';
  includeCharts: boolean;
  includeInsights: boolean;
  includeRecommendations: boolean;
  customFileName?: string;
}

/**
 * Report preview data
 */
export interface ReportPreview {
  templateId: string;
  previewData: ReportSectionData[];
  estimatedSize: number;
  estimatedGenerationTime: number;
  dataSourcesUsed: string[];
}