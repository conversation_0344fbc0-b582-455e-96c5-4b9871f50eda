/**
 * AIQualitySection
 *
 * Wrapper um die bestehende `AIQualityAnalysis`-Komponente.
 * Hält die Page schlank und kapselt reine Darstellung.
 */

import React from 'react';
import { AIQualityAnalysis } from './AIQualityAnalysis';

interface AIQualitySectionProps {
  averageQualityScore: number;
  improvementAreas: string[];
  successPatterns: string[];
  recommendations: string[];
}

export function AIQualitySection({
  averageQualityScore,
  improvementAreas,
  successPatterns,
  recommendations,
}: AIQualitySectionProps) {
  return (
    <div className="space-y-6">
      <AIQualityAnalysis
        analysisData={{
          averageQualityScore,
          improvementAreas,
          successPatterns,
          recommendations,
        }}
      />
    </div>
  );
}

export default AIQualitySection;
