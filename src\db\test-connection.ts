import { db } from './index';
import { ragDb } from './rag-db';
import { stoerungen } from './schema';
import { knowledgeBases } from './rag-schema';
import { eq, count } from 'drizzle-orm';

/**
 * Test der SFM Dashboard Drizzle-Verbindung
 */
export async function testSfmConnection(): Promise<void> {
  console.log('🔍 Testing SFM Dashboard Drizzle connection...');
  
  try {
    // Test einer einfachen COUNT-Abfrage
    const result = await db.select({ count: count() }).from(stoerungen);
    console.log(`✅ SFM Dashboard connected successfully. Störungen count: ${result[0].count}`);
    
    // Test einer einfachen SELECT-Abfrage mit LIMIT
    const recentStoerungen = await db
      .select({ id: stoerungen.id, title: stoerungen.title })
      .from(stoerungen)
      .limit(5);
    
    console.log(`✅ Sample query successful. Recent störungen: ${recentStoerungen.length} records`);
    
  } catch (error) {
    console.error('❌ SFM Dashboard connection failed:', error);
    throw error;
  }
}

/**
 * Test der RAG Knowledge Base Drizzle-Verbindung
 */
export async function testRagConnection(): Promise<void> {
  console.log('🔍 Testing RAG Knowledge Base Drizzle connection...');
  
  try {
    // Test einer einfachen COUNT-Abfrage
    const result = await ragDb.select({ count: count() }).from(knowledgeBases);
    console.log(`✅ RAG DB connected successfully. Knowledge bases count: ${result[0].count}`);
    
    // Test einer einfachen SELECT-Abfrage mit LIMIT
    const activeKnowledgeBases = await ragDb
      .select({ id: knowledgeBases.id, name: knowledgeBases.name })
      .from(knowledgeBases)
      .where(eq(knowledgeBases.isActive, 1))
      .limit(5);
    
    console.log(`✅ Sample query successful. Active knowledge bases: ${activeKnowledgeBases.length} records`);
    
  } catch (error) {
    console.error('❌ RAG DB connection failed:', error);
    throw error;
  }
}

/**
 * Führt alle Verbindungstests aus
 */
export async function testAllConnections(): Promise<void> {
  console.log('🚀 Starting Drizzle connection tests...\n');
  
  try {
    await testSfmConnection();
    console.log('');
    await testRagConnection();
    console.log('\n✅ All database connections working correctly!');
  } catch (error) {
    console.error('\n❌ Database connection tests failed:', error);
    process.exit(1);
  }
}
