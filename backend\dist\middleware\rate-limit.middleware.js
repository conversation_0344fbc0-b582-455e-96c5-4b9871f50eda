"use strict";
/**
 * Rate Limiting Middleware
 *
 * Implementiert Rate-Limiting für verschiedene API-Endpunkte,
 * um Missbrauch zu verhindern und die Server-Performance zu schützen.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.rateLimitConfig = exports.authRateLimit = exports.uploadRateLimit = exports.generalRateLimit = exports.rateLimitMiddleware = void 0;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
/**
 * Standard Rate-Limiting-Konfiguration
 */
const createRateLimit = (windowMs, max, message) => {
    return (0, express_rate_limit_1.default)({
        windowMs, // Zeitfenster in Millisekunden
        max, // Maximale Anza<PERSON> von Anfragen pro Zeitfenster
        message: {
            success: false,
            error: message,
            code: 'RATE_LIMIT_EXCEEDED'
        },
        standardHeaders: true, // Fügt `RateLimit-*` Headers hinzu
        legacyHeaders: false, // Deaktiviert `X-RateLimit-*` Headers
        handler: (req, res) => {
            console.warn(`[RATE-LIMIT] Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
            res.status(429).json({
                success: false,
                error: message,
                code: 'RATE_LIMIT_EXCEEDED',
                retryAfter: Math.ceil(windowMs / 1000) // Sekunden bis zum nächsten Versuch
            });
        }
    });
};
/**
 * Rate-Limiting für E-Mail-Endpunkte
 * Sehr restriktiv, da E-Mail-Versand ressourcenintensiv ist
 */
exports.rateLimitMiddleware = createRateLimit(15 * 60 * 1000, // 15 Minuten
5, // Maximal 5 E-Mails pro 15 Minuten
'Zu viele E-Mail-Anfragen. Bitte versuchen Sie es später erneut.');
/**
 * Rate-Limiting für allgemeine API-Endpunkte
 */
exports.generalRateLimit = createRateLimit(15 * 60 * 1000, // 15 Minuten
100, // Maximal 100 Anfragen pro 15 Minuten
'Zu viele API-Anfragen. Bitte versuchen Sie es später erneut.');
/**
 * Rate-Limiting für Upload-Endpunkte
 */
exports.uploadRateLimit = createRateLimit(60 * 1000, // 1 Minute
10, // Maximal 10 Uploads pro Minute
'Zu viele Upload-Anfragen. Bitte versuchen Sie es später erneut.');
/**
 * Rate-Limiting für Authentifizierungs-Endpunkte
 */
exports.authRateLimit = createRateLimit(15 * 60 * 1000, // 15 Minuten
5, // Maximal 5 Login-Versuche pro 15 Minuten
'Zu viele Login-Versuche. Bitte versuchen Sie es später erneut.');
/**
 * Konfigurationsobjekt für verschiedene Rate-Limits
 */
exports.rateLimitConfig = {
    general: exports.generalRateLimit,
    dataEndpoint: exports.generalRateLimit,
    writeOperation: exports.generalRateLimit,
    healthCheck: exports.generalRateLimit,
    metricsEndpoint: exports.generalRateLimit,
    unauthenticated: exports.generalRateLimit,
    emailSend: exports.rateLimitMiddleware, // E-Mail-spezifisches Rate-Limiting
    upload: exports.uploadRateLimit,
    auth: exports.authRateLimit,
    adaptive: (req, res, next) => {
        // Adaptive Rate-Limiting basierend auf Request-Typ
        if (req.path.includes('/email/')) {
            return (0, exports.rateLimitMiddleware)(req, res, next);
        }
        return (0, exports.generalRateLimit)(req, res, next);
    }
};
/**
 * Exportiere Standard-Rate-Limit für E-Mails
 */
exports.default = exports.rateLimitMiddleware;
