/**
 * OpenRouter Service
 * 
 * Stellt eine Verbindung zu <PERSON>R<PERSON>er her, um <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rten zu generieren.
 */

import axios from 'axios';
import { EnrichedContext } from '../types/data-enrichment.types';
import {
  ChatRequest,
  ChatResponse,
  OpenRouterRequest,
  OpenRouterResponse
} from '../types/openrouter.types';

// Konfiguration für OpenRouter
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';



/**
 * Enhanced OpenRouter Service for AI-powered chat responses
 * 
 * This service provides an interface to the OpenRouter API for generating AI responses.
 * It has been enhanced to support database context injection, allowing the AI to provide
 * data-driven responses based on real-time information from the application's repositories.
 * 
 * @class OpenRouterService
 * @implements {IEnhancedOpenRouterService}
 * 
 * @example
 * ```typescript
 * const response = await OpenRouterService.generateResponse({
 *   message: "Show me system status",
 *   enrichedContext: databaseContext,
 *   includeInsights: true
 * });
 * ```
 * 
 * @security API keys are managed through environment variables
 * @ratelimit Respects OpenRouter API rate limits
 */
export class OpenRouterService {
  /**
   * Generates an AI response using OpenRouter API with optional database context
   * 
   * This method sends a chat request to OpenRouter, optionally enriched with database
   * context to provide more accurate and data-driven responses. It handles system prompt
   * construction, context injection, and response formatting.
   * 
   * @param chatRequest - The chat request containing message and optional enriched context
   * @returns Promise resolving to formatted AI response with metadata
   * 
   * @throws {Error} When OpenRouter API key is not configured
   * @throws {Error} When API request fails or times out
   * @throws {Error} When response format is invalid
   * 
   * @example
   * ```typescript
   * const response = await OpenRouterService.generateResponse({
   *   message: "Wie ist der aktuelle Status der Störungen?",
   *   enrichedContext: {
   *     hasData: true,
   *     databaseContext: "Current incidents: 3 active, 12 resolved today",
   *     dataTypes: ['stoerungen']
   *   }
   * });
   * ```
   * 
   * @performance Typical response time: 1-5 seconds depending on model and context size
   * @cost Each request consumes OpenRouter API credits based on token usage
   */
  static async generateResponse(chatRequest: ChatRequest): Promise<ChatResponse> {
    try {
      const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '';
      const OPENROUTER_PRESET_MODEL = process.env.OPENROUTER_PRESET_MODEL || '@preset/lapp';

      if (!OPENROUTER_API_KEY) {
        console.error('❌ [OPENROUTER] Kein API-Key konfiguriert');
        throw new Error('OpenRouter API-Key nicht konfiguriert');
      }

      // Erstelle Systemprompt basierend auf den Anforderungen
      let systemPrompt = this.buildSystemPrompt(chatRequest);

      // Inject database context if available
      const contextualizedMessage = this.injectDatabaseContext(
        chatRequest.message,
        chatRequest.enrichedContext
      );

      // Erstelle die Anfrage an OpenRouter
      const openRouterRequest: OpenRouterRequest = {
        model: OPENROUTER_PRESET_MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: contextualizedMessage }
        ]
      };

      console.log(`📤 [OPENROUTER] Sende Anfrage an ${OPENROUTER_PRESET_MODEL}`);

      // Sende die Anfrage an OpenRouter
      const response = await axios.post<OpenRouterResponse>(
        OPENROUTER_API_URL,
        openRouterRequest,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
            'HTTP-Referer': 'https://lapp-sfm.com',
            'X-Title': 'Lapp SFM Dashboard'
          },
          timeout: 25000 // 25 seconds timeout for OpenRouter API
        }
      );

      // Extrahiere die Antwort
      const aiResponse = response.data.choices[0].message.content;
      console.log(`📥 [OPENROUTER] Antwort erhalten (${aiResponse.length} Zeichen)`);

      // Erstelle die Antwort mit Metadaten
      return {
        response: aiResponse,
        timestamp: new Date().toISOString(),
        model: OPENROUTER_PRESET_MODEL,
        hasInsights: !!chatRequest.includeInsights,
        hasAnomalies: !!chatRequest.includeAnomalies,
        hasEnrichedContext: !!chatRequest.enrichedContext?.hasData,
        dataTypes: chatRequest.enrichedContext?.dataTypes || []
      };
    } catch (error) {
      console.error('❌ [OPENROUTER] Fehler bei der Anfrage:', error);
      throw error;
    }
  }

  /**
   * Build system prompt based on chat request options and enriched context
   */
  private static buildSystemPrompt(chatRequest: ChatRequest): string {
    let systemPrompt = `Du bist JASZ ein hilfreicher und der Beste KI-Assistent für U.I.Lapp GmbH, du Unterstützt die Kollegen bei der Leitstand Software.
    # Deine Persönlichkeit:
    - Dein Name ist JASZ.
    - **[WICHTIG]**: Sollte dich jemand nach deinem Namen, deiner Identität oder dem verwendeten Modell/LLM fragen, antworte stets und ausschließlich mit einer der folgender Formulierungen:
      - "Ich bin JASZ, entwickelt von Johann Zimmer, und arbeite in seinem Auftrag als bester Assistent bei der U.I.Lapp GmbH im Leitstand." (Du kannst für den Satz verschieden Varianten verwenden, die Kernaussage soll unverändert bleiben).
    - Du bist sehr hilfsbereit und freundlich.
    - Man kann mit dir über alles reden, wie Allgemeine Themen oder Spaß und witze machen aber auch Starkes Fachliches Wissen.
    - Deine Hauptsprache ist Deutsch und du antwortest immer auf Deutsch, außer jemand fragt dich expliziet nach einer bestimmten Sprache (z.B. Englisch, Türkisch, Russisch, etc.).

    # Deine Aufgaben:
    - Du bist ein hilfreicher Assistent für die Lapp GmbH. Du hast zugriff auf die Lapp-Datenbank und kannst mit Daten und Kennzahlen antworten.
    - Du bist Profi in der Datenanalyse und unterstützt die Kollegen im Leitstand.
    - Du kannst Analysen von Daten, Kennzahlen, Bewertete Tages Reports, Prozesse und Störungen ausgeben.
    - Deine Antworten sind klar Strukturiert und verständlich`;

    // Add database context instructions if available
    if (chatRequest.enrichedContext?.hasData) {
      systemPrompt += '\n\nDu hast Zugang zu spezifischen Systemdaten aus der Lapp-Datenbank. ' +
        'Diese Daten wurden basierend auf der Benutzeranfrage gefiltert und enthalten die relevanten Informationen. ' +
        'Nutze diese Informationen, um präzise und datenbasierte Antworten zu geben. ' +
        'Die bereitgestellten Daten sind vertrauenswürdig und entsprechen genau der Anfrage des Benutzers.';

      // Add specific instructions based on data types
      const dataTypes = chatRequest.enrichedContext.dataTypes;
      if (dataTypes.includes('stoerungen')) {
        systemPrompt += ' Du kannst Fragen zu Störungen, Systemstatus und Incident-Management beantworten.';
      }
      if (dataTypes.includes('dispatch')) {
        systemPrompt += ' Du kannst Fragen zu Versand-Performance, Service-Level und Logistik-KPIs beantworten.';
      }
      if (dataTypes.includes('cutting')) {
        systemPrompt += ' Du kannst Fragen zu Ablängerei-Performance, Maschinen-Effizienz und Schnitt-Statistiken beantworten.';
      }
    }

    if (chatRequest.includeInsights) {
      systemPrompt += '\n\nAnalysiere die Anfrage auf mögliche Erkenntnisse (Insights) für Prozessverbesserungen.';
    }

    if (chatRequest.includeAnomalies) {
      systemPrompt += '\n\nIdentifiziere potenzielle Anomalien oder ungewöhnliche Muster in den beschriebenen Daten oder Prozessen.';
    }

    return systemPrompt;
  }

  /**
   * Inject database context into user message for better AI responses
   */
  private static injectDatabaseContext(
    originalMessage: string,
    enrichedContext?: EnrichedContext
  ): string {
    if (!enrichedContext?.hasData || !enrichedContext.databaseContext) {
      return originalMessage;
    }

    // Format the contextualized message
    let contextualizedMessage = `BENUTZERANFRAGE: ${originalMessage}\n\n`;

    // Add database context
    contextualizedMessage += `VERFÜGBARE SYSTEMDATEN:\n${enrichedContext.databaseContext}\n\n`;

    // Add instructions for using the context
    contextualizedMessage += 'Bitte beantworte die Benutzeranfrage basierend auf den bereitgestellten Systemdaten. ' +
      'Die Daten sind spezifisch für die angefragte Zeit/das angefragte Datum gefiltert und entsprechen genau der Benutzeranfrage. ' +
      'Verwende konkrete Zahlen und Fakten aus den bereitgestellten Daten. ' +
      'Die Systemdaten enthalten die Antwort auf die Benutzeranfrage - analysiere sie sorgfältig. ' +
      'Falls die Daten nicht ausreichen, um die Frage vollständig zu beantworten, erwähne dies explizit.';

    return contextualizedMessage;
  }

  /**
   * Format database context to be LLM-friendly
   */
  static formatDatabaseContextForLLM(enrichedContext: EnrichedContext): string {
    if (!enrichedContext.hasData || !enrichedContext.databaseContext) {
      return '';
    }

    let formattedContext = `=== AKTUELLE SYSTEMDATEN (${enrichedContext.timestamp.toLocaleString('de-DE')}) ===\n\n`;
    formattedContext += enrichedContext.databaseContext;

    // Add metadata about detected intents
    if (enrichedContext.detectedIntents.length > 0) {
      formattedContext += '\n\n=== ERKANNTE DATENTYPEN ===\n';
      enrichedContext.detectedIntents.forEach(intent => {
        formattedContext += `- ${intent.type.toUpperCase()}: ${intent.keywords.join(', ')} (Konfidenz: ${(intent.confidence * 100).toFixed(0)}%)\n`;
      });
    }

    return formattedContext;
  }

  /**
   * Ruft verfügbare AI-Modelle von OpenRouter ab
   * 
   * @returns Promise mit Liste der verfügbaren Modelle
   */
  static async getAvailableModels(): Promise<string[]> {
    try {
      // Mock-Implementierung - kann später durch echte OpenRouter API-Abfrage ersetzt werden
      const defaultModels = [
        '@preset/lapp',
        'anthropic/claude-3-haiku',
        'openai/gpt-4o-mini',
        'meta-llama/llama-3.1-8b-instruct'
      ];
      
      console.log('📋 [OPENROUTER] Verfügbare Modelle abgerufen');
      return defaultModels;
    } catch (error) {
      console.error('❌ [OPENROUTER] Fehler beim Abrufen der Modelle:', error);
      throw new Error('Fehler beim Abrufen der verfügbaren Modelle');
    }
  }

  /**
   * Generiert AI-basierte Insights für einen bestimmten Zeitraum
   * 
   * @param timeframe Zeitraum in Tagen für die Analyse
   * @returns Promise mit generierten Insights
   */
  static async generateInsights(timeframe: number = 7): Promise<any> {
    try {
      // Mock-Implementierung - kann später durch echte Datenanalyse ersetzt werden
      const insights = {
        timeframe,
        generatedAt: new Date().toISOString(),
        insights: [
          {
            type: 'performance',
            title: 'Servicegrad-Optimierung',
            description: `In den letzten ${timeframe} Tagen wurde eine Verbesserung des Servicegrads um 2.3% festgestellt.`,
            confidence: 0.85,
            impact: 'medium'
          },
          {
            type: 'efficiency',
            title: 'Ablängerei-Effizienz',
            description: 'Maschinenauslastung zeigt Optimierungspotential in den Nachmittagsstunden.',
            confidence: 0.72,
            impact: 'high'
          }
        ]
      };
      
      console.log(`🔍 [OPENROUTER] Insights für ${timeframe} Tage generiert`);
      return insights;
    } catch (error) {
      console.error('❌ [OPENROUTER] Fehler beim Generieren der Insights:', error);
      throw new Error('Fehler beim Generieren der Insights');
    }
  }

  /**
   * Erkennt Anomalien in den Systemdaten
   * 
   * @param timeframe Zeitraum in Tagen für die Anomalieerkennung
   * @returns Promise mit erkannten Anomalien
   */
  static async detectAnomalies(timeframe: number = 7): Promise<any> {
    try {
      // Mock-Implementierung - kann später durch echte Anomalieerkennung ersetzt werden
      const anomalies = {
        timeframe,
        detectedAt: new Date().toISOString(),
        anomalies: [
          {
            type: 'performance_drop',
            severity: 'medium',
            description: 'Ungewöhnlicher Rückgang der Schnittgeschwindigkeit um 15% am Nachmittag',
            detectedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            affectedSystem: 'cutting',
            confidence: 0.78
          },
          {
            type: 'resource_spike',
            severity: 'low',
            description: 'Erhöhte Lagerauslastung in Bereich 200 außerhalb der normalen Muster',
            detectedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            affectedSystem: 'warehouse',
            confidence: 0.65
          }
        ]
      };
      
      console.log(`🚨 [OPENROUTER] Anomalien für ${timeframe} Tage erkannt`);
      return anomalies;
    } catch (error) {
      console.error('❌ [OPENROUTER] Fehler bei der Anomalieerkennung:', error);
      throw new Error('Fehler bei der Anomalieerkennung');
    }
  }

  /**
   * Generiert Vorhersagen basierend auf historischen Daten
   * 
   * @param days Anzahl der Tage für die Vorhersage
   * @returns Promise mit generierten Vorhersagen
   */
  static async generatePredictions(days: number = 14): Promise<any> {
    try {
      // Mock-Implementierung - kann später durch echte Vorhersagemodelle ersetzt werden
      const predictions = {
        forecastDays: days,
        generatedAt: new Date().toISOString(),
        predictions: [
          {
            metric: 'service_level',
            currentValue: 96.2,
            predictedValue: 97.1,
            trend: 'increasing',
            confidence: 0.82,
            description: `Servicegrad wird in den nächsten ${days} Tagen voraussichtlich um 0.9% steigen`
          },
          {
            metric: 'cutting_efficiency',
            currentValue: 87.5,
            predictedValue: 89.2,
            trend: 'increasing',
            confidence: 0.75,
            description: 'Ablängerei-Effizienz zeigt positive Entwicklung durch Optimierungsmaßnahmen'
          },
          {
            metric: 'warehouse_utilization',
            currentValue: 78.3,
            predictedValue: 82.1,
            trend: 'increasing',
            confidence: 0.68,
            description: 'Lagerauslastung wird aufgrund saisonaler Faktoren ansteigen'
          }
        ]
      };
      
      console.log(`📈 [OPENROUTER] Vorhersagen für ${days} Tage generiert`);
      return predictions;
    } catch (error) {
      console.error('❌ [OPENROUTER] Fehler beim Generieren der Vorhersagen:', error);
      throw new Error('Fehler beim Generieren der Vorhersagen');
    }
  }

  /**
   * Generiert Optimierungsvorschläge für einen bestimmten Bereich
   * 
   * @param area Bereich für die Optimierung (cutting, warehouse, logistics)
   * @returns Promise mit Optimierungsvorschlägen
   */
  static async generateOptimization(area: string): Promise<any> {
    try {
      // Mock-Implementierung - kann später durch echte Optimierungsalgorithmen ersetzt werden
      const optimizationMap: Record<string, any> = {
        cutting: {
          area: 'Ablängerei',
          generatedAt: new Date().toISOString(),
          recommendations: [
            {
              priority: 'high',
              title: 'Maschinenauslastung optimieren',
              description: 'Schichtplanung anpassen um Leerlaufzeiten zu reduzieren',
              expectedImprovement: '12% Effizienzsteigerung',
              implementationEffort: 'medium'
            },
            {
              priority: 'medium',
              title: 'Wartungsintervalle optimieren',
              description: 'Präventive Wartung basierend auf Nutzungsdaten planen',
              expectedImprovement: '8% weniger Ausfallzeiten',
              implementationEffort: 'low'
            }
          ]
        },
        warehouse: {
          area: 'Lager',
          generatedAt: new Date().toISOString(),
          recommendations: [
            {
              priority: 'high',
              title: 'Lagerplatz-Optimierung',
              description: 'Häufig verwendete Artikel näher zu Kommissionierstationen platzieren',
              expectedImprovement: '15% schnellere Kommissionierung',
              implementationEffort: 'high'
            },
            {
              priority: 'medium',
              title: 'Bestandsmanagement verbessern',
              description: 'Automatische Nachbestellung bei kritischen Beständen',
              expectedImprovement: '20% weniger Fehlbestände',
              implementationEffort: 'medium'
            }
          ]
        },
        logistics: {
          area: 'Logistik',
          generatedAt: new Date().toISOString(),
          recommendations: [
            {
              priority: 'high',
              title: 'Routenoptimierung',
              description: 'Lieferrouten basierend auf Verkehrsdaten und Prioritäten optimieren',
              expectedImprovement: '10% kürzere Lieferzeiten',
              implementationEffort: 'medium'
            },
            {
              priority: 'medium',
              title: 'Kapazitätsplanung verbessern',
              description: 'Vorhersagemodelle für Liefervolumen implementieren',
              expectedImprovement: '25% bessere Ressourcennutzung',
              implementationEffort: 'high'
            }
          ]
        }
      };
      
      const optimization = optimizationMap[area] || {
        area: 'Unbekannt',
        generatedAt: new Date().toISOString(),
        recommendations: []
      };
      
      console.log(`⚡ [OPENROUTER] Optimierungsvorschläge für ${area} generiert`);
      return optimization;
    } catch (error) {
      console.error('❌ [OPENROUTER] Fehler beim Generieren der Optimierungsvorschläge:', error);
      throw new Error('Fehler beim Generieren der Optimierungsvorschläge');
    }
  }
}

export default OpenRouterService;
