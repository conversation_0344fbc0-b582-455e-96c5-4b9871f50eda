#!/usr/bin/env tsx

import { PrismaClient } from '../node_modules/@prisma-sfm-dashboard/client';

const prisma = new PrismaClient();

// Standard-Statusmeldungen pro Kategorie und Status
const standardStatusMessages = [
  // Terminals
  {
    category: 'Terminals',
    status: 'OK',
    messages: [
      { title: 'System läuft optimal', description: 'Das Terminal funktioniert einwandfrei und ist voll einsatzbereit.', priority: 1 },
      { title: 'Alle Funktionen verfügbar', description: 'Barcode-Scanner, Touchscreen und Netzwerk funktionieren normal.', priority: 2 },
      { title: 'Letzte Wartung erfolgreich', description: 'Die letzte planmäßige Wartung wurde erfolgreich abgeschlossen.', priority: 3 }
    ]
  },
  {
    category: 'Terminals',
    status: 'WARNING',
    messages: [
      { title: 'Touchscreen reagiert langsam', description: 'Der Touchscreen zeigt verzögerte Reaktionszeiten. Neustart empfohlen.', priority: 1 },
      { title: 'Software-Update verfügbar', description: 'Ein neues Software-Update ist verfügbar und sollte installiert werden.', priority: 2 },
      { title: 'Barcode-Scanner Probleme', description: 'Der Barcode-Scanner zeigt gelegentliche Lesefehler.', priority: 3 }
    ]
  },
  {
    category: 'Terminals',
    status: 'ERROR',
    messages: [
      { title: 'Terminal nicht erreichbar', description: 'Das Terminal antwortet nicht auf Netzwerk-Anfragen. Verbindung unterbrochen.', priority: 1 },
      { title: 'Hardware-Fehler erkannt', description: 'Ein kritischer Hardware-Fehler wurde erkannt. Sofortige Wartung erforderlich.', priority: 2 },
      { title: 'System abgestürzt', description: 'Das Terminal-System ist abgestürzt und muss neu gestartet werden.', priority: 3 }
    ]
  },
  {
    category: 'Terminals',
    status: 'OFF',
    messages: [
      { title: 'Planmäßige Wartung', description: 'Das Terminal ist für geplante Wartungsarbeiten außer Betrieb.', priority: 1 },
      { title: 'Energiesparmodus', description: 'Das Terminal befindet sich im Energiesparmodus außerhalb der Betriebszeiten.', priority: 2 },
      { title: 'Manuell deaktiviert', description: 'Das Terminal wurde manuell durch den Administrator deaktiviert.', priority: 3 }
    ]
  },

  // Maschinen
  {
    category: 'Maschinen',
    status: 'OK',
    messages: [
      { title: 'Betrieb läuft stabil', description: 'Die Maschine arbeitet innerhalb aller Normparameter und produziert planmäßig.', priority: 1 },
      { title: 'Optimale Leistung', description: 'Alle Sensoren melden Normalwerte. Produktionsgeschwindigkeit im Sollbereich.', priority: 2 },
      { title: 'Wartung nicht fällig', description: 'Die nächste planmäßige Wartung ist noch nicht fällig.', priority: 3 }
    ]
  },
  {
    category: 'Maschinen',
    status: 'WARNING',
    messages: [
      { title: 'Erhöhte Betriebstemperatur', description: 'Die Betriebstemperatur liegt über dem optimalen Bereich. Überwachung verstärkt.', priority: 1 },
      { title: 'Wartung in 24h fällig', description: 'Die nächste planmäßige Wartung ist in 24 Stunden fällig.', priority: 2 },
      { title: 'Leichte Vibrationen erkannt', description: 'Ungewöhnliche Vibrationen wurden gemessen. Prüfung empfohlen.', priority: 3 }
    ]
  },
  {
    category: 'Maschinen',
    status: 'ERROR',
    messages: [
      { title: 'Maschinenausfall', description: 'Die Maschine ist ausgefallen und muss repariert werden. Produktion gestoppt.', priority: 1 },
      { title: 'Kritischer Sensorfehler', description: 'Ein kritischer Sensor meldet fehlerhafte Werte. Sicherheitsstopp aktiviert.', priority: 2 },
      { title: 'Notaus betätigt', description: 'Der Notaus-Schalter wurde betätigt. Maschine ist gesperrt.', priority: 3 }
    ]
  },
  {
    category: 'Maschinen',
    status: 'OFF',
    messages: [
      { title: 'Schichtende', description: 'Die Maschine ist nach Schichtende planmäßig ausgeschaltet.', priority: 1 },
      { title: 'Wartungsmodus', description: 'Die Maschine befindet sich im Wartungsmodus für Instandhaltungsarbeiten.', priority: 2 },
      { title: 'Produktionspause', description: 'Die Maschine ist aufgrund einer Produktionspause gestoppt.', priority: 3 }
    ]
  },

  // Fördertechnik
  {
    category: 'Fördertechnik',
    status: 'OK',
    messages: [
      { title: 'Reibungsloser Transport', description: 'Das Förderband transportiert Material ohne Störungen mit optimaler Geschwindigkeit.', priority: 1 },
      { title: 'Alle Sensoren aktiv', description: 'Positionssensoren und Geschwindigkeitsmessung funktionieren einwandfrei.', priority: 2 },
      { title: 'Kein Stau erkannt', description: 'Der Materialfluss läuft ohne Blockierungen oder Rückstau.', priority: 3 }
    ]
  },
  {
    category: 'Fördertechnik',
    status: 'WARNING',
    messages: [
      { title: 'Bandgeschwindigkeit reduziert', description: 'Die Fördergeschwindigkeit wurde aufgrund von Überlastung reduziert.', priority: 1 },
      { title: 'Stau in Sektor C', description: 'Ein leichter Stau wurde in Sektor C erkannt. Überwachung aktiv.', priority: 2 },
      { title: 'Sensor verschmutzt', description: 'Ein Fördertechnik-Sensor ist verschmutzt und sollte gereinigt werden.', priority: 3 }
    ]
  },
  {
    category: 'Fördertechnik',
    status: 'ERROR',
    messages: [
      { title: 'Förderband gestoppt', description: 'Das Förderband ist aufgrund eines kritischen Fehlers gestoppt.', priority: 1 },
      { title: 'Motorschaden', description: 'Der Fördermotor zeigt Anzeichen eines Schadens. Sofortige Reparatur nötig.', priority: 2 },
      { title: 'Blockierung erkannt', description: 'Eine Blockierung im Förderweg wurde erkannt. Manueller Eingriff erforderlich.', priority: 3 }
    ]
  },

  // Datenbanken
  {
    category: 'Datenbanken',
    status: 'OK',
    messages: [
      { title: 'Datenbank verfügbar', description: 'Die Datenbank ist vollständig verfügbar und antwortet schnell auf Anfragen.', priority: 1 },
      { title: 'Backup erfolgreich', description: 'Das letzte automatische Backup wurde erfolgreich erstellt.', priority: 2 },
      { title: 'Performance optimal', description: 'CPU-Auslastung und Antwortzeiten liegen im grünen Bereich.', priority: 3 }
    ]
  },
  {
    category: 'Datenbanken',
    status: 'WARNING',
    messages: [
      { title: 'Hohe CPU-Auslastung', description: 'Die Datenbank zeigt eine hohe CPU-Auslastung. Performance-Überwachung aktiv.', priority: 1 },
      { title: 'Speicherplatz wird knapp', description: 'Der verfügbare Speicherplatz auf dem Datenbankserver wird knapp.', priority: 2 },
      { title: 'Lange Antwortzeiten', description: 'Die Datenbank-Antwortzeiten sind überdurchschnittlich hoch.', priority: 3 }
    ]
  },
  {
    category: 'Datenbanken',
    status: 'ERROR',
    messages: [
      { title: 'Datenbankverbindung fehlgeschlagen', description: 'Die Verbindung zur Datenbank ist fehlgeschlagen. Dienst nicht verfügbar.', priority: 1 },
      { title: 'Backup fehlgeschlagen', description: 'Das automatische Backup ist fehlgeschlagen. Datenintegrität gefährdet.', priority: 2 },
      { title: 'Korrupte Daten erkannt', description: 'Korrupte Datensätze wurden erkannt. Datenwiederherstellung erforderlich.', priority: 3 }
    ]
  },

  // Netzwerk
  {
    category: 'Netzwerk',
    status: 'OK',
    messages: [
      { title: 'Netzwerk stabil', description: 'Alle Netzwerkverbindungen sind stabil und die Latenz liegt im Normalbereich.', priority: 1 },
      { title: 'Volle Bandbreite', description: 'Die Netzwerkauslastung ist gering und die volle Bandbreite steht zur Verfügung.', priority: 2 },
      { title: 'Alle Geräte online', description: 'Alle Netzwerkgeräte sind online und funktionsbereit.', priority: 3 }
    ]
  },
  {
    category: 'Netzwerk',
    status: 'WARNING',
    messages: [
      { title: 'Hohe Netzwerklast', description: 'Das Netzwerk zeigt eine hohe Auslastung. Überwachung verstärkt.', priority: 1 },
      { title: 'Schwaches WLAN-Signal', description: 'Das WLAN-Signal ist in einigen Bereichen schwach.', priority: 2 },
      { title: 'Gelegentliche Verbindungsabbrüche', description: 'Gelegentliche kurze Verbindungsabbrüche wurden registriert.', priority: 3 }
    ]
  },
  {
    category: 'Netzwerk',
    status: 'ERROR',
    messages: [
      { title: 'Netzwerk nicht erreichbar', description: 'Das Netzwerk ist komplett nicht erreichbar. Verbindung unterbrochen.', priority: 1 },
      { title: 'Switch ausgefallen', description: 'Ein kritischer Netzwerk-Switch ist ausgefallen. Infrastruktur beeinträchtigt.', priority: 2 },
      { title: 'Firewall blockiert Verkehr', description: 'Die Firewall blockiert kritischen Netzwerkverkehr.', priority: 3 }
    ]
  },

  // SAP
  {
    category: 'SAP',
    status: 'OK',
    messages: [
      { title: 'SAP System verfügbar', description: 'Das SAP-System ist vollständig verfügbar und alle Module funktionieren normal.', priority: 1 },
      { title: 'Schnelle Antwortzeiten', description: 'Die SAP-Transaktionen werden mit optimaler Geschwindigkeit ausgeführt.', priority: 2 },
      { title: 'Alle Interfaces aktiv', description: 'Alle RFC-Verbindungen und Schnittstellen funktionieren einwandfrei.', priority: 3 }
    ]
  },
  {
    category: 'SAP',
    status: 'WARNING',
    messages: [
      { title: 'Hohe Systemlast', description: 'Das SAP-System zeigt eine hohe Systemlast. Performance beobachten.', priority: 1 },
      { title: 'Interface-Verzögerungen', description: 'Die SAP-Interfaces zeigen längere Antwortzeiten als üblich.', priority: 2 },
      { title: 'Dialog-Instanzen ausgelastet', description: 'Die verfügbaren Dialog-Instanzen sind nahezu ausgelastet.', priority: 3 }
    ]
  },
  {
    category: 'SAP',
    status: 'ERROR',
    messages: [
      { title: 'SAP-System nicht verfügbar', description: 'Das SAP-System ist nicht verfügbar. Alle Transaktionen blockiert.', priority: 1 },
      { title: 'Datenbank-Connection fehlt', description: 'Die Verbindung zur SAP-Datenbank ist unterbrochen.', priority: 2 },
      { title: 'RFC-Verbindung fehlgeschlagen', description: 'Die RFC-Verbindung zu externen Systemen ist fehlgeschlagen.', priority: 3 }
    ]
  },

  // ITM
  {
    category: 'ITM',
    status: 'OK',
    messages: [
      { title: 'Materialfluss optimal', description: 'Das ITM-System steuert den Materialfluss effizient und ohne Unterbrechungen.', priority: 1 },
      { title: 'Alle Puffer verfügbar', description: 'Die Pufferbereiche sind optimal ausgelastet und bereit für weiteres Material.', priority: 2 },
      { title: 'SPS-Kommunikation stabil', description: 'Die Kommunikation mit allen SPS-Systemen läuft fehlerfrei.', priority: 3 }
    ]
  },
  {
    category: 'ITM',
    status: 'WARNING',
    messages: [
      { title: 'Materialfluss verlangsamt', description: 'Der Materialfluss läuft langsamer als geplant. Überwachung aktiv.', priority: 1 },
      { title: 'Pufferbereich fast voll', description: 'Ein Pufferbereich erreicht seine Kapazitätsgrenze.', priority: 2 },
      { title: 'Kommunikationsfehler mit PLC', description: 'Gelegentliche Kommunikationsfehler mit der SPS wurden erkannt.', priority: 3 }
    ]
  },
  {
    category: 'ITM',
    status: 'ERROR',
    messages: [
      { title: 'ITM-System ausgefallen', description: 'Das ITM-System ist ausgefallen. Materialfluss gestoppt.', priority: 1 },
      { title: 'Kritischer Steuerungsfehler', description: 'Ein kritischer Fehler in der Materialflusssteuerung wurde erkannt.', priority: 2 },
      { title: 'Verbindung zu Hardware verloren', description: 'Die Verbindung zu kritischen Hardware-Komponenten ist verloren.', priority: 3 }
    ]
  },

  // Läger
  {
    category: 'Läger',
    status: 'OK',
    messages: [
      { title: 'Lager voll einsatzbereit', description: 'Alle Regalbediengeräte funktionieren normal und das Lager ist voll zugänglich.', priority: 1 },
      { title: 'Optimale Kapazitätsnutzung', description: 'Die Lagerkapazität ist optimal genutzt ohne Überbelastung.', priority: 2 },
      { title: 'Alle Lagerplätze verfügbar', description: 'Keine gesperrten Lagerplätze, alle Bereiche sind zugänglich.', priority: 3 }
    ]
  },
  {
    category: 'Läger',
    status: 'WARNING',
    messages: [
      { title: 'Lagerkapazität zu 85% ausgelastet', description: 'Die Lagerkapazität erreicht 85% Auslastung. Platz wird knapp.', priority: 1 },
      { title: 'RBG-Geschwindigkeit reduziert', description: 'Die Regalbediengeräte fahren mit reduzierter Geschwindigkeit.', priority: 2 },
      { title: 'Einzelne Lagerplätze gesperrt', description: 'Einige Lagerplätze sind aufgrund von Wartung gesperrt.', priority: 3 }
    ]
  },
  {
    category: 'Läger',
    status: 'ERROR',
    messages: [
      { title: 'RBG ausgefallen', description: 'Ein Regalbediengerät ist ausgefallen. Lagerbereich nicht erreichbar.', priority: 1 },
      { title: 'Lagerplatz-System fehlerh.', description: 'Das Lagerplatz-Verwaltungssystem zeigt kritische Fehler.', priority: 2 },
      { title: 'Mechanischer Schaden', description: 'Ein mechanischer Schaden am Lagersystem wurde erkannt.', priority: 3 }
    ]
  },

  // Flurförderzeuge
  {
    category: 'Flurförderzeuge',
    status: 'OK',
    messages: [
      { title: 'Fahrzeugflotte einsatzbereit', description: 'Alle Flurförderzeuge sind betriebsbereit und voll geladen.', priority: 1 },
      { title: 'Navigation funktioniert', description: 'GPS und Navigationssystem arbeiten präzise und zuverlässig.', priority: 2 },
      { title: 'Keine Wartung fällig', description: 'Alle Fahrzeuge haben ihre Wartungsintervalle eingehalten.', priority: 3 }
    ]
  },
  {
    category: 'Flurförderzeuge',
    status: 'WARNING',
    messages: [
      { title: 'Akku schwach', description: 'Der Akku eines Flurförderzeugs ist schwach und muss geladen werden.', priority: 1 },
      { title: 'GPS-Signal schwach', description: 'Das GPS-Signal für die Navigation ist in einigen Bereichen schwach.', priority: 2 },
      { title: 'Wartungsintervall erreicht', description: 'Ein Fahrzeug hat sein Wartungsintervall erreicht.', priority: 3 }
    ]
  },
  {
    category: 'Flurförderzeuge',
    status: 'ERROR',
    messages: [
      { title: 'Fahrzeug ausgefallen', description: 'Ein Flurförderzeug ist ausgefallen und nicht einsatzbereit.', priority: 1 },
      { title: 'Kollision erkannt', description: 'Eine Kollision oder Blockierung wurde erkannt. Fahrzeug gestoppt.', priority: 2 },
      { title: 'Navigationssystem fehlerh.', description: 'Das Navigationssystem zeigt kritische Fehler.', priority: 3 }
    ]
  },

  // Anlagen
  {
    category: 'Anlagen',
    status: 'OK',
    messages: [
      { title: 'Anlage läuft optimal', description: 'Die Anlage arbeitet mit optimaler Effizienz und allen Sicherheitsstandards.', priority: 1 },
      { title: 'Temperatur im Normbereich', description: 'Alle Betriebstemperaturen liegen innerhalb der spezifizierten Parameter.', priority: 2 },
      { title: 'Sicherheitssysteme aktiv', description: 'Alle Sicherheitssysteme sind aktiv und funktionsbereit.', priority: 3 }
    ]
  },
  {
    category: 'Anlagen',
    status: 'WARNING',
    messages: [
      { title: 'Betriebstemperatur erhöht', description: 'Die Betriebstemperatur der Anlage ist erhöht. Überwachung aktiv.', priority: 1 },
      { title: 'Filter muss gewechselt werden', description: 'Der Anlagenfilter hat seine Lebensdauer erreicht und muss gewechselt werden.', priority: 2 },
      { title: 'Vibration außerhalb Norm', description: 'Die gemessenen Vibrationen liegen außerhalb der Norm.', priority: 3 }
    ]
  },
  {
    category: 'Anlagen',
    status: 'ERROR',
    messages: [
      { title: 'Anlagenstopp', description: 'Die Anlage ist aufgrund eines kritischen Fehlers gestoppt.', priority: 1 },
      { title: 'Sicherheitssystem ausgelöst', description: 'Das Sicherheitssystem hat einen Notfall erkannt und die Anlage gestoppt.', priority: 2 },
      { title: 'Hydraulikfehler', description: 'Ein kritischer Fehler im Hydrauliksystem wurde erkannt.', priority: 3 }
    ]
  }
];

async function seedSystemStatusMessages() {
  console.log('🌱 Starting SystemStatusMessage seeding...');
  
  try {
    // Zuerst alle bestehenden Nachrichten löschen
    await prisma.systemStatusMessage.deleteMany();
    console.log('✅ Cleared existing SystemStatusMessage records');

    let totalInserted = 0;

    // Alle Standard-Statusmeldungen einfügen
    for (const categoryData of standardStatusMessages) {
      for (const message of categoryData.messages) {
        await prisma.systemStatusMessage.create({
          data: {
            category: categoryData.category,
            status: categoryData.status,
            title: message.title,
            description: message.description,
            priority: message.priority,
            is_active: 1
          }
        });
        totalInserted++;
      }
      console.log(`✅ Added ${categoryData.messages.length} messages for ${categoryData.category} (${categoryData.status})`);
    }

    console.log(`🎉 Successfully seeded ${totalInserted} SystemStatusMessage records!`);
    
  } catch (error) {
    console.error('❌ Error seeding SystemStatusMessage:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Führe das Seeding aus, falls das Skript direkt aufgerufen wird
if (require.main === module) {
  seedSystemStatusMessages()
    .then(() => {
      console.log('✅ SystemStatusMessage seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ SystemStatusMessage seeding failed:', error);
      process.exit(1);
    });
}

export { seedSystemStatusMessages };