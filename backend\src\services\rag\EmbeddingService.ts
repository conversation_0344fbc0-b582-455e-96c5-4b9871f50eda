/**
 * EmbeddingService - Text to Vector Embedding Generation
 * 
 * Handles text embedding generation using OpenAI's embedding models
 * with caching, batch processing, and error handling
 */

import {
  EmbeddingRequest,
  EmbeddingResponse,
  BatchEmbeddingRequest,
  BatchEmbeddingResponse,
  RAGError,
  RAGErrorCode
} from '../../types/rag.types';

interface OpenAIEmbeddingResponse {
  data: Array<{
    embedding: number[];
    index: number;
  }>;
  model: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

export class EmbeddingService {
  private readonly apiKey: string;
  private readonly openAIBaseUrl: string = 'https://api.openai.com/v1';
  private readonly ollamaBaseUrl: string;
  private readonly defaultModel: string;
  private readonly provider: 'openai' | 'ollama';
  private readonly maxTokensPerRequest: number = 8000;
  private readonly maxBatchSize: number = 100;

  // Simple in-memory cache for embeddings
  private embeddingCache = new Map<string, EmbeddingResponse>();
  private readonly cacheMaxSize = 1000;

  constructor(options?: {
    apiKey?: string;
    ollamaUrl?: string;
    provider?: 'openai' | 'ollama';
    defaultModel?: string;
  }) {
    this.apiKey = options?.apiKey || process.env.OPENAI_API_KEY || '';
    this.ollamaBaseUrl = options?.ollamaUrl || process.env.OLLAMA_BASE_URL || 'http://localhost:11434';
    this.provider = options?.provider || (process.env.RAG_EMBEDDING_PROVIDER as any) || 'ollama'; // Default to Ollama
    
    // Set default model based on provider and environment
    if (this.provider === 'ollama') {
      this.defaultModel = options?.defaultModel || process.env.RAG_EMBEDDING_MODEL || 'mxbai-embed-large:latest';
    } else {
      this.defaultModel = options?.defaultModel || process.env.RAG_EMBEDDING_MODEL || 'text-embedding-3-small';
    }
    
    console.log(`Embedding service initialized with provider: ${this.provider}, model: ${this.defaultModel}`);
  }

  /**
   * Determine the best available provider
   * Now defaults to Ollama as per project configuration
   */
  private determineProvider(): 'openai' | 'ollama' {
    // Default to Ollama as specified in .env.example
    // Only use OpenAI if explicitly configured
    if (process.env.RAG_EMBEDDING_PROVIDER === 'openai' && this.apiKey) {
      return 'openai';
    }
    
    // Default to Ollama
    return 'ollama';
  }

  /**
   * Generate embedding for a single text
   */
  async generateEmbedding(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const { text, model = this.defaultModel } = request;

    // Check cache first
    const cacheKey = this.getCacheKey(text, model);
    const cached = this.embeddingCache.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // Validate input
      if (!text || text.trim().length === 0) {
        throw new Error('Text cannot be empty');
      }

      // Estimate token count (rough approximation: 1 token ≈ 4 characters)
      const estimatedTokens = Math.ceil(text.length / 4);
      if (estimatedTokens > this.maxTokensPerRequest) {
        throw new Error(`Text too long: estimated ${estimatedTokens} tokens, max ${this.maxTokensPerRequest}`);
      }

      let embedding: Float32Array;
      let actualModel: string;
      let tokenCount: number;

      if (this.provider === 'openai' && this.apiKey) {
        // OpenAI API call
        const response = await this.callOpenAIEmbedding([text], model);
        embedding = new Float32Array(response.data[0].embedding);
        actualModel = response.model;
        tokenCount = response.usage.prompt_tokens;
      } else if (this.provider === 'ollama') {
        // Ollama API call
        try {
          const response = await this.callOllamaEmbedding(text, model);
          embedding = new Float32Array(response.embedding);
          actualModel = model;
          tokenCount = estimatedTokens; // Ollama doesn't return token count
        } catch (error) {
          throw new Error(`Ollama embedding failed: ${(error as Error).message}`);
        }
      } else {
        throw new Error('No valid embedding provider configured. Please configure OpenAI API key or Ollama.');
      }

      const result: EmbeddingResponse = {
        embedding,
        model: actualModel,
        dimensions: embedding.length,
        tokenCount
      };

      // Cache the result
      this.cacheEmbedding(cacheKey, result);

      return result;

    } catch (error) {
      console.error('Error generating embedding:', error);
      throw {
        code: RAGErrorCode.EMBEDDING_GENERATION_FAILED,
        message: 'Failed to generate text embedding',
        details: { text: text.substring(0, 100) + '...', model, error: (error as Error).message },
        timestamp: new Date()
      } as RAGError;
    }
  }

  /**
   * Generate embeddings for multiple texts in batch
   */
  async generateBatchEmbeddings(request: BatchEmbeddingRequest): Promise<BatchEmbeddingResponse> {
    const { texts, model = this.defaultModel, batchSize = this.maxBatchSize } = request;
    const startTime = Date.now();

    const results: EmbeddingResponse[] = [];
    let failedCount = 0;

    // Process in smaller batches to avoid API limits
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      
      try {
        const batchResults = await this.processBatch(batch, model);
        results.push(...batchResults);
      } catch (error) {
        console.error(`Error processing batch ${i / batchSize + 1}:`, error);
        failedCount += batch.length;
        
        // Try individual processing for failed batch
        for (const text of batch) {
          try {
            const individual = await this.generateEmbedding({ text, model });
            results.push(individual);
            failedCount--; // Reduce failed count for successful individual processing
          } catch (individualError) {
            console.error('Individual embedding also failed:', individualError);
          }
        }
      }

      // Add delay between batches to respect rate limits
      if (i + batchSize < texts.length) {
        await this.delay(100);
      }
    }

    const processingTimeMs = Date.now() - startTime;

    return {
      embeddings: results,
      totalProcessed: results.length,
      failedCount,
      processingTimeMs
    };
  }

  /**
   * Process a single batch of texts
   */
  private async processBatch(texts: string[], model: string): Promise<EmbeddingResponse[]> {
    // Check cache for all texts in batch
    const results: EmbeddingResponse[] = [];
    const uncachedTexts: string[] = [];
    const uncachedIndices: number[] = [];

    texts.forEach((text, index) => {
      const cacheKey = this.getCacheKey(text, model);
      const cached = this.embeddingCache.get(cacheKey);
      if (cached) {
        results[index] = cached;
      } else {
        uncachedTexts.push(text);
        uncachedIndices.push(index);
      }
    });

    // Process uncached texts
    if (uncachedTexts.length > 0) {
      if (this.provider === 'openai' && this.apiKey) {
        // OpenAI batch API call
        const response = await this.callOpenAIEmbedding(uncachedTexts, model);
        
        response.data.forEach((embeddingData, i) => {
          const text = uncachedTexts[i];
          const embedding = new Float32Array(embeddingData.embedding);
          const result: EmbeddingResponse = {
            embedding,
            model: response.model,
            dimensions: embedding.length,
            tokenCount: Math.ceil(response.usage.prompt_tokens / uncachedTexts.length)
          };
          results[uncachedIndices[i]] = result;
          this.cacheEmbedding(this.getCacheKey(text, model), result);
        });
      } else if (this.provider === 'ollama') {
        // Ollama individual calls (no batch support)
        for (let i = 0; i < uncachedTexts.length; i++) {
          const text = uncachedTexts[i];
          try {
            const response = await this.callOllamaEmbedding(text, model);
            const embedding = new Float32Array(response.embedding);
            const result: EmbeddingResponse = {
              embedding,
              model,
              dimensions: embedding.length,
              tokenCount: Math.ceil(text.length / 4)
            };
            results[uncachedIndices[i]] = result;
            this.cacheEmbedding(this.getCacheKey(text, model), result);
          } catch (error) {
            throw new Error(`Ollama embedding failed for text ${i}: ${(error as Error).message}`);
          }
        }
      } else {
        throw new Error('No valid embedding provider configured. Please configure OpenAI API key or Ollama.');
      }
    }

    return results;
  }

  /**
   * Call OpenAI Embedding API
   */
  private async callOpenAIEmbedding(texts: string[], model: string): Promise<OpenAIEmbeddingResponse> {
    const response = await fetch(`${this.openAIBaseUrl}/embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        input: texts,
        model,
        encoding_format: 'float'
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
    }

    return await response.json();
  }

  /**
   * Call Ollama Embedding API
   */
  private async callOllamaEmbedding(text: string, model: string): Promise<{ embedding: number[] }> {
    const response = await fetch(`${this.ollamaBaseUrl}/api/embeddings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        prompt: text
      })
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      throw new Error(`Ollama API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return await response.json();
  }

  /**
   * Check if Ollama is available
   */
  async isOllamaAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.ollamaBaseUrl}/api/tags`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if specific model is available in Ollama
   */
  async isModelAvailable(model: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.ollamaBaseUrl}/api/tags`);
      if (!response.ok) return false;
      
      const data = await response.json();
      return data.models?.some((m: any) => m.name === model) || false;
    } catch (error) {
      return false;
    }
  }

  /**
     * Generate cache key for embedding
     */
  private getCacheKey(text: string, model: string): string {
    // Use first 100 chars + hash of full text for cache key
    const textPreview = text.substring(0, 100);
    const textHash = this.simpleHash(text);
    return `${model}:${textHash}:${textPreview}`;
  }

  /**
   * Simple hash function for text
   */
  private simpleHash(text: string): string {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Cache embedding result
   */
  private cacheEmbedding(key: string, result: EmbeddingResponse): void {
    // Implement simple LRU by removing oldest entries when cache is full
    if (this.embeddingCache.size >= this.cacheMaxSize) {
      const firstKey = this.embeddingCache.keys().next().value;
      if (firstKey) {
        this.embeddingCache.delete(firstKey);
      }
    }
    
    this.embeddingCache.set(key, result);
  }

  /**
   * Clear embedding cache
   */
  clearCache(): void {
    this.embeddingCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
  } {
    // This is a simplified version - in production you'd track hits/misses
    return {
      size: this.embeddingCache.size,
      maxSize: this.cacheMaxSize,
      hitRate: 0 // Would need to track this properly
    };
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate embedding dimensions
   */
  validateEmbedding(embedding: Float32Array, expectedDimensions: number = 1536): boolean {
    return embedding.length === expectedDimensions && 
           embedding.every(val => !isNaN(val) && isFinite(val));
  }

  /**
   * Get supported models
   */
  getSupportedModels(): string[] {
    if (this.provider === 'openai') {
      return [
        'text-embedding-3-small',
        'text-embedding-3-large',
        'text-embedding-ada-002'
      ];
    } else if (this.provider === 'ollama') {
      return [
        'mxbai-embed-large:latest',
        'nomic-embed-text:latest',
        'all-minilm:latest'
      ];
    } else {
      return ['mock-embedding'];
    }
  }

  /**
   * Get current provider and model info
   */
  getProviderInfo(): {
    provider: string;
    model: string;
    baseUrl: string;
    available: boolean;
  } {
    return {
      provider: this.provider,
      model: this.defaultModel,
      baseUrl: this.provider === 'ollama' ? this.ollamaBaseUrl : this.openAIBaseUrl,
      available: this.provider === 'openai' ? !!this.apiKey : true // Ollama availability checked at runtime
    };
  }
}

export default EmbeddingService;