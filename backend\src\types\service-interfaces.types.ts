/**
 * Service Interface Definitions
 * 
 * Comprehensive TypeScript interfaces for all AI chatbot database integration services.
 * These interfaces define the contracts for service components and ensure type safety
 * across the entire data enrichment system.
 * 
 * @fileoverview Service interfaces for AI chatbot database integration
 * @version 1.0.0
 * <AUTHOR> Lapp Dashboard Team
 */

import { 
  QueryIntent, 
  EnrichedContext, 
  QueryResult, 
  DataEnrichmentConfig,
  DateRange 
} from './data-enrichment.types';

/**
 * Interface for the main Data Enrichment Service
 * 
 * This service analyzes user queries and enriches them with relevant database context
 * for AI chatbot responses. It coordinates intent recognition, data retrieval, and
 * context building.
 */
export interface IDataEnrichmentService {
  /**
   * Enriches a chat message with relevant database context
   * 
   * @param message - The user's chat message to analyze and enrich
   * @param options - Optional configuration for enrichment behavior
   * @returns Promise resolving to enriched context with database information
   * @throws {Error} When intent recognition fails or database queries timeout
   * 
   * @example
   * ```typescript
   * const enrichedContext = await dataEnrichmentService.enrichChatContext(
   *   "Wie viele Störungen hatten wir diese Woche?",
   *   { includeTimestamp: true }
   * );
   * ```
   */
  enrichChatContext(message: string, options?: EnrichmentOptions): Promise<EnrichedContext>;

  /**
   * Parses user intent from a message
   * 
   * @param message - The message to analyze for intent
   * @returns Array of detected query intents with confidence scores
   * 
   * @example
   * ```typescript
   * const intents = dataEnrichmentService.parseIntent("Show me dispatch performance");
   * // Returns: [{ type: 'dispatch', keywords: ['dispatch', 'performance'], confidence: 0.9 }]
   * ```
   */
  parseIntent(message: string): QueryIntent[];

  /**
   * Formats raw data for LLM consumption
   * 
   * @param data - Raw data from repositories
   * @param dataType - Type of data being formatted ('stoerungen', 'dispatch', 'cutting')
   * @param options - Formatting options
   * @returns Human-readable formatted string for AI context
   */
  formatDataForLLM(data: any[], dataType: string, options?: DataFormatterOptions): string;

  /**
   * Updates the service configuration
   * 
   * @param config - New configuration settings
   */
  updateConfig(config: Partial<DataEnrichmentConfig>): void;

  /**
   * Gets current service health and statistics
   * 
   * @returns Service health information and performance metrics
   */
  getServiceHealth(): Promise<ServiceHealthInfo>;
}

/**
 * Interface for Repository Query Coordinator
 * 
 * Coordinates queries across multiple repositories based on detected intent.
 * Handles parallel execution, error recovery, and data aggregation.
 */
export interface IRepositoryQueryCoordinator {
  /**
   * Executes queries across multiple repositories based on intents
   * 
   * @param intents - Array of query intents to execute
   * @param options - Query execution options
   * @returns Promise resolving to array of query results
   * 
   * @example
   * ```typescript
   * const results = await coordinator.executeQueries([
   *   { type: 'stoerungen', keywords: ['incidents'], confidence: 0.9 }
   * ]);
   * ```
   */
  executeQueries(intents: QueryIntent[], options?: QueryExecutionOptions): Promise<QueryResult[]>;

  /**
   * Retrieves Störungen (incidents) data based on intent
   * 
   * @param intent - Query intent for Störungen data
   * @returns Promise resolving to Störungen data
   */
  getStoerungenData(intent: QueryIntent): Promise<QueryResult>;

  /**
   * Retrieves Dispatch data based on intent
   * 
   * @param intent - Query intent for Dispatch data
   * @returns Promise resolving to Dispatch data
   */
  getDispatchData(intent: QueryIntent): Promise<QueryResult>;

  /**
   * Retrieves Cutting data based on intent
   * 
   * @param intent - Query intent for Cutting data
   * @returns Promise resolving to Cutting data
   */
  getCuttingData(intent: QueryIntent): Promise<QueryResult>;

  /**
   * Validates query intent before execution
   * 
   * @param intent - Intent to validate
   * @returns True if intent is valid and can be executed
   */
  validateIntent(intent: QueryIntent): boolean;

  /**
   * Gets coordinator performance metrics
   * 
   * @returns Performance statistics for query coordination
   */
  getPerformanceMetrics(): Promise<CoordinatorMetrics>;
}

/**
 * Interface for Data Formatting Service
 * 
 * Provides human-readable data formatting templates and context building
 * logic to combine multiple data sources for AI consumption.
 */
export interface IDataFormattingService {
  /**
   * Formats Störungen data for human readability
   * 
   * @param data - Raw Störungen data
   * @param options - Formatting options
   * @returns Formatted string for AI context
   */
  formatStoerungenData(data: any, options?: DataFormatterOptions): string;

  /**
   * Formats Dispatch data for human readability
   * 
   * @param data - Raw Dispatch data
   * @param options - Formatting options
   * @returns Formatted string for AI context
   */
  formatDispatchData(data: any, options?: DataFormatterOptions): string;

  /**
   * Formats Cutting data for human readability
   * 
   * @param data - Raw Cutting data
   * @param options - Formatting options
   * @returns Formatted string for AI context
   */
  formatCuttingData(data: any, options?: DataFormatterOptions): string;

  /**
   * Builds comprehensive context from multiple data sources
   * 
   * @param results - Array of query results from different sources
   * @param options - Context building options
   * @returns Combined context string for AI consumption
   */
  buildContext(results: QueryResult[], options?: ContextBuildingOptions): string;

  /**
   * Generates summary for complex multi-source queries
   * 
   * @param results - Query results to summarize
   * @param options - Summary generation options
   * @returns Executive summary of the data
   */
  generateSummary(results: QueryResult[], options?: SummaryOptions): string;

  /**
   * Formats time series data with trends
   * 
   * @param data - Time series data points
   * @param dataType - Type of time series data
   * @param options - Formatting options
   * @returns Formatted time series with trend analysis
   */
  formatTimeSeriesData(data: any[], dataType: string, options?: TimeSeriesOptions): string;
}

/**
 * Interface for Enhanced OpenRouter Service
 * 
 * Extends the existing OpenRouter service to handle enriched context
 * and provide better AI responses with database information.
 */
export interface IEnhancedOpenRouterService {
  /**
   * Generates AI response with enriched database context
   * 
   * @param request - Chat request with optional enriched context
   * @returns Promise resolving to AI response with metadata
   * 
   * @example
   * ```typescript
   * const response = await openRouterService.generateEnrichedResponse({
   *   message: "Show me system status",
   *   enrichedContext: contextData
   * });
   * ```
   */
  generateEnrichedResponse(request: EnrichedChatRequest): Promise<EnrichedChatResponse>;

  /**
   * Builds system prompt with database context
   * 
   * @param basePrompt - Base system prompt
   * @param context - Enriched context to inject
   * @returns Enhanced system prompt with database information
   */
  buildEnrichedPrompt(basePrompt: string, context: EnrichedContext): string;

  /**
   * Injects database context into user message
   * 
   * @param message - Original user message
   * @param context - Database context to inject
   * @returns Message with injected context
   */
  injectDatabaseContext(message: string, context?: EnrichedContext): string;

  /**
   * Validates enriched context before processing
   * 
   * @param context - Context to validate
   * @returns True if context is valid and can be used
   */
  validateEnrichedContext(context: EnrichedContext): boolean;
}

/**
 * Supporting interfaces for service options and configurations
 */

export interface EnrichmentOptions {
  includeTimestamp?: boolean;
  includeFreshness?: boolean;
  maxQueryTime?: number;
  fallbackOnError?: boolean;
  cacheResults?: boolean;
}

export interface QueryExecutionOptions {
  parallel?: boolean;
  timeout?: number;
  retryCount?: number;
  fallbackOnPartialFailure?: boolean;
}

export interface DataFormatterOptions {
  maxItems?: number;
  includeTimestamp?: boolean;
  format?: 'summary' | 'detailed' | 'minimal';
  language?: 'de' | 'en';
}

export interface ContextBuildingOptions {
  includeTimestamp?: boolean;
  includeFreshness?: boolean;
  includeTrends?: boolean;
  includeDetails?: boolean;
  format?: 'summary' | 'detailed' | 'minimal';
  maxLength?: number;
  language?: 'de' | 'en';
}

export interface SummaryOptions {
  maxLength?: number;
  includeRecommendations?: boolean;
  includeKeyMetrics?: boolean;
  language?: 'de' | 'en';
}

export interface TimeSeriesOptions {
  includeTrends?: boolean;
  includeComparisons?: boolean;
  timeFormat?: 'relative' | 'absolute';
  language?: 'de' | 'en';
}

export interface EnrichedChatRequest {
  message: string;
  enrichedContext?: EnrichedContext;
  includeInsights?: boolean;
  includeAnomalies?: boolean;
  responseFormat?: 'standard' | 'detailed' | 'summary';
}

export interface EnrichedChatResponse {
  response: string;
  timestamp: string;
  model: string;
  hasEnrichedContext: boolean;
  dataTypes: string[];
  processingTime: number;
  contextQuality: number; // 0-1 score
  fallbackUsed: boolean;
  metadata: ResponseMetadata;
}

export interface ResponseMetadata {
  intentConfidence: number;
  dataFreshness: string;
  queriesExecuted: number;
  cacheHits: number;
  errors: string[];
  warnings: string[];
}

export interface ServiceHealthInfo {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  totalRequests: number;
  successRate: number;
  averageResponseTime: number;
  cacheHitRate: number;
  lastError?: string;
  repositoryStatus: {
    stoerungen: 'available' | 'unavailable' | 'degraded';
    dispatch: 'available' | 'unavailable' | 'degraded';
    cutting: 'available' | 'unavailable' | 'degraded';
  };
}

export interface CoordinatorMetrics {
  totalQueries: number;
  successfulQueries: number;
  failedQueries: number;
  averageQueryTime: number;
  parallelExecutions: number;
  cacheUtilization: number;
  repositoryPerformance: {
    [key: string]: {
      averageTime: number;
      successRate: number;
      lastError?: string;
    };
  };
}