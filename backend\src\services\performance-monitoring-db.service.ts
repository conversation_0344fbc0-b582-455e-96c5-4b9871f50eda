/**
 * Database-Persistent Performance Monitoring Service
 * 
 * Erweiterte Version des Performance Monitoring Service, die Metriken
 * persistent in der Datenbank speichert für langfristige Analyse und Reporting.
 */

import { PrismaClient } from '@prisma-sfm-dashboard/client';
import { EventEmitter } from 'events';
import {
  PerformanceMetric,
  QueryPerformanceMetric,
  IntentRecognitionMetric,
  EnrichmentPerformanceMetric,
  ResponseTimeMetric,
  PerformanceStats
} from './performance-monitoring.service';

export interface DatabasePerformanceConfig {
  enableInMemoryCache: boolean; // Keep recent metrics in memory for fast access
  inMemoryCacheSize: number;    // Number of recent metrics to keep in memory
  batchSize: number;            // Number of metrics to batch before database write
  flushInterval: number;        // Interval to flush batched metrics (ms)
  retentionDays: number;        // Days to keep metrics in database
  enableAggregation: boolean;   // Enable daily/hourly aggregation
}

const DEFAULT_DB_CONFIG: DatabasePerformanceConfig = {
  enableInMemoryCache: true,
  inMemoryCacheSize: 1000,
  batchSize: 50,
  flushInterval: 30 * 1000, // 30 seconds
  retentionDays: 90, // 3 months
  enableAggregation: true
};

export class DatabasePerformanceMonitoringService extends EventEmitter {
  private prisma: PrismaClient;
  private config: DatabasePerformanceConfig;
  private inMemoryMetrics: PerformanceMetric[] = [];
  private pendingMetrics: PerformanceMetric[] = [];
  private flushTimer?: NodeJS.Timeout;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(prisma: PrismaClient, config: Partial<DatabasePerformanceConfig> = {}) {
    super();
    this.prisma = prisma;
    this.config = { ...DEFAULT_DB_CONFIG, ...config };
    
    this.startFlushTimer();
    this.startCleanupTimer();
  }

  /**
   * Record a query performance metric with database persistence
   */
  async recordQueryPerformance(
    queryType: 'stoerungen' | 'dispatch' | 'cutting' | 'combined',
    duration: number,
    success: boolean,
    details: {
      dataSize?: number;
      cacheHit?: boolean;
      retryCount?: number;
      errorType?: string;
    } = {}
  ): Promise<void> {
    const metricId = this.generateId();
    
    // Create the metric for in-memory cache
    const metric: QueryPerformanceMetric = {
      id: metricId,
      timestamp: new Date(),
      type: 'query',
      duration,
      success,
      details: {
        queryType,
        dataSize: details.dataSize || 0,
        cacheHit: details.cacheHit || false,
        retryCount: details.retryCount || 0,
        errorType: details.errorType
      }
    };

    // Add to in-memory cache if enabled
    if (this.config.enableInMemoryCache) {
      this.addToInMemoryCache(metric);
    }

    // Add to pending batch for database write
    this.pendingMetrics.push(metric);

    // Prepare database record
    const dbRecord = {
      metricId,
      queryType,
      duration,
      success,
      dataSize: details.dataSize || 0,
      cacheHit: details.cacheHit || false,
      retryCount: details.retryCount || 0,
      errorType: details.errorType,
      timestamp: new Date()
    };

    // Immediate write for critical metrics or batch write
    if (!success || duration > 5000) {
      // Write immediately for failures or very slow queries
      await this.writeQueryMetricToDatabase(dbRecord);
    } else {
      // Add to batch for later write
      this.scheduleFlushIfNeeded();
    }

    this.emit('queryPerformance', metric);
  }

  /**
   * Record intent recognition performance with database persistence
   */
  async recordIntentRecognition(
    message: string,
    detectedIntents: string[],
    confidence: number,
    duration: number,
    keywords: string[] = [],
    accuracy?: number
  ): Promise<void> {
    const metricId = this.generateId();
    const messageHash = this.hashString(message);

    const metric: IntentRecognitionMetric = {
      id: metricId,
      timestamp: new Date(),
      type: 'intent',
      duration,
      success: detectedIntents.length > 0,
      details: {
        message: message.substring(0, 200),
        detectedIntents,
        confidence,
        accuracy,
        keywords
      }
    };

    if (this.config.enableInMemoryCache) {
      this.addToInMemoryCache(metric);
    }

    this.pendingMetrics.push(metric);

    // Prepare database record
    const dbRecord = {
      metricId,
      messageHash,
      detectedIntents: JSON.stringify(detectedIntents),
      confidence,
      accuracy,
      keywords: JSON.stringify(keywords),
      duration,
      timestamp: new Date()
    };

    // Write to database (batch or immediate)
    this.scheduleFlushIfNeeded();

    this.emit('intentRecognition', metric);
  }

  /**
   * Record data enrichment performance with database persistence
   */
  async recordEnrichmentPerformance(
    requestId: string,
    duration: number,
    success: boolean,
    details: {
      intentCount: number;
      queryCount: number;
      successfulQueries: number;
      fallbackUsed: boolean;
      dataTypes: string[];
    }
  ): Promise<void> {
    const metricId = this.generateId();

    const metric: EnrichmentPerformanceMetric = {
      id: metricId,
      timestamp: new Date(),
      type: 'enrichment',
      duration,
      success,
      details: {
        requestId,
        ...details
      }
    };

    if (this.config.enableInMemoryCache) {
      this.addToInMemoryCache(metric);
    }

    this.pendingMetrics.push(metric);

    // Prepare database record
    const dbRecord = {
      metricId,
      requestId,
      intentCount: details.intentCount,
      queryCount: details.queryCount,
      successfulQueries: details.successfulQueries,
      fallbackUsed: details.fallbackUsed,
      dataTypes: JSON.stringify(details.dataTypes),
      duration,
      success,
      timestamp: new Date()
    };

    this.scheduleFlushIfNeeded();
    this.emit('enrichmentPerformance', metric);
  }

  /**
   * Record overall response time performance with database persistence
   */
  async recordResponseTime(
    endpoint: string,
    enriched: boolean,
    totalTime: number,
    enrichmentTime: number,
    llmTime: number,
    dataSize: number = 0
  ): Promise<void> {
    const metricId = this.generateId();
    const success = totalTime < (enriched ? 3000 : 2000);

    const metric: ResponseTimeMetric = {
      id: metricId,
      timestamp: new Date(),
      type: 'response',
      duration: totalTime,
      success,
      details: {
        endpoint,
        enriched,
        totalTime,
        enrichmentTime,
        llmTime,
        dataSize
      }
    };

    if (this.config.enableInMemoryCache) {
      this.addToInMemoryCache(metric);
    }

    this.pendingMetrics.push(metric);

    // Prepare database record
    const dbRecord = {
      metricId,
      endpoint,
      enriched,
      totalTime,
      enrichmentTime,
      llmTime,
      dataSize,
      success,
      timestamp: new Date()
    };

    // Write immediately for slow responses
    if (totalTime > (enriched ? 3000 : 2000)) {
      await this.writeResponseMetricToDatabase(dbRecord);
    } else {
      this.scheduleFlushIfNeeded();
    }

    this.emit('responseTime', metric);
  }

  /**
   * Get performance statistics from database with optional time range
   */
  async getPerformanceStats(timeRange?: { start: Date; end: Date }): Promise<PerformanceStats> {
    const whereClause = timeRange ? {
      timestamp: {
        gte: timeRange.start,
        lte: timeRange.end
      }
    } : {};

    // Get response time metrics
    const responseMetrics = await this.prisma.responseTimeMetric.findMany({
      where: whereClause,
      select: {
        totalTime: true,
        success: true,
        enriched: true,
        dataSize: true
      }
    });

    // Get query performance metrics
    const queryMetrics = await this.prisma.queryPerformanceMetric.findMany({
      where: whereClause,
      select: {
        queryType: true,
        duration: true,
        success: true,
        cacheHit: true
      }
    });

    // Get intent recognition metrics
    const intentMetrics = await this.prisma.intentRecognitionMetric.findMany({
      where: whereClause,
      select: {
        accuracy: true,
        confidence: true
      }
    });

    // Calculate statistics
    const totalRequests = responseMetrics.length;
    const averageResponseTime = totalRequests > 0 
      ? responseMetrics.reduce((sum, m) => sum + m.totalTime, 0) / totalRequests 
      : 0;
    
    const successRate = totalRequests > 0 
      ? responseMetrics.filter(m => m.success).length / totalRequests 
      : 0;

    const cacheHitRate = queryMetrics.length > 0
      ? queryMetrics.filter(m => m.cacheHit).length / queryMetrics.length
      : 0;

    const intentAccuracy = intentMetrics.length > 0
      ? intentMetrics
          .filter(m => m.accuracy !== null)
          .reduce((sum, m) => sum + (m.accuracy || 0), 0) / 
        intentMetrics.filter(m => m.accuracy !== null).length
      : 0;

    // Calculate query performance by type
    const queryPerformance = {
      stoerungen: this.calculateQueryTypeStats(queryMetrics, 'stoerungen'),
      dispatch: this.calculateQueryTypeStats(queryMetrics, 'dispatch'),
      cutting: this.calculateQueryTypeStats(queryMetrics, 'cutting')
    };

    // Calculate time range stats (simplified for database version)
    const now = new Date();
    const timeRanges = {
      last5min: await this.getBasicPerformanceStats({
        start: new Date(now.getTime() - 5 * 60 * 1000),
        end: now
      }),
      last1hour: await this.getBasicPerformanceStats({
        start: new Date(now.getTime() - 60 * 60 * 1000),
        end: now
      }),
      last24hours: await this.getBasicPerformanceStats({
        start: new Date(now.getTime() - 24 * 60 * 60 * 1000),
        end: now
      })
    };

    return {
      totalRequests,
      averageResponseTime,
      successRate,
      cacheHitRate,
      intentAccuracy,
      queryPerformance,
      timeRanges
    };
  }

  /**
   * Get performance alerts from database
   */
  async getPerformanceAlerts(): Promise<Array<{
    type: 'warning' | 'error';
    message: string;
    metric: string;
    value: number;
    threshold: number;
    timestamp: Date;
  }>> {
    const alerts = await this.prisma.performanceAlert.findMany({
      where: {
        resolved: false
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 50
    });

    return alerts.map(alert => ({
      type: alert.type as 'warning' | 'error',
      message: alert.message,
      metric: alert.metric,
      value: alert.value,
      threshold: alert.threshold,
      timestamp: alert.createdAt
    }));
  }

  /**
   * Create performance alert in database
   */
  async createPerformanceAlert(
    type: 'warning' | 'error',
    message: string,
    metric: string,
    value: number,
    threshold: number
  ): Promise<void> {
    await this.prisma.performanceAlert.create({
      data: {
        type,
        message,
        metric,
        value,
        threshold
      }
    });
  }

  /**
   * Get historical performance trends
   */
  async getPerformanceTrends(
    metric: 'response_time' | 'success_rate' | 'cache_hit_rate',
    period: 'hour' | 'day' | 'week',
    limit: number = 24
  ): Promise<Array<{ timestamp: Date; value: number }>> {
    // This would implement aggregated queries for trends
    // For now, return a simplified version
    const timeInterval = period === 'hour' ? 60 * 60 * 1000 : 
                        period === 'day' ? 24 * 60 * 60 * 1000 : 
                        7 * 24 * 60 * 60 * 1000;

    const endTime = new Date();
    const startTime = new Date(endTime.getTime() - (limit * timeInterval));

    // Simplified trend calculation - in production, use aggregated tables
    const responseMetrics = await this.prisma.responseTimeMetric.findMany({
      where: {
        timestamp: {
          gte: startTime,
          lte: endTime
        }
      },
      select: {
        timestamp: true,
        totalTime: true,
        success: true
      },
      orderBy: {
        timestamp: 'asc'
      }
    });

    // Group by time intervals and calculate averages
    const trends: Array<{ timestamp: Date; value: number }> = [];
    
    for (let i = 0; i < limit; i++) {
      const intervalStart = new Date(startTime.getTime() + (i * timeInterval));
      const intervalEnd = new Date(intervalStart.getTime() + timeInterval);
      
      const intervalMetrics = responseMetrics.filter(m => 
        m.timestamp >= intervalStart && m.timestamp < intervalEnd
      );

      if (intervalMetrics.length > 0) {
        let value = 0;
        switch (metric) {
          case 'response_time':
            value = intervalMetrics.reduce((sum, m) => sum + m.totalTime, 0) / intervalMetrics.length;
            break;
          case 'success_rate':
            value = intervalMetrics.filter(m => m.success).length / intervalMetrics.length;
            break;
          case 'cache_hit_rate':
            // Would need to join with query metrics
            value = 0.5; // Placeholder
            break;
        }
        
        trends.push({
          timestamp: intervalStart,
          value
        });
      }
    }

    return trends;
  }

  /**
   * Export metrics from database
   */
  async exportMetrics(
    format: 'json' | 'csv' = 'json',
    timeRange?: { start: Date; end: Date },
    metricTypes?: string[]
  ): Promise<string> {
    const whereClause = timeRange ? {
      timestamp: {
        gte: timeRange.start,
        lte: timeRange.end
      }
    } : {};

    // Get all metric types if not specified
    const types = metricTypes || ['query', 'intent', 'enrichment', 'response'];
    const allMetrics: any[] = [];

    // Fetch different metric types
    if (types.includes('query')) {
      const queryMetrics = await this.prisma.queryPerformanceMetric.findMany({
        where: whereClause
      });
      allMetrics.push(...queryMetrics.map(m => ({ ...m, type: 'query' })));
    }

    if (types.includes('response')) {
      const responseMetrics = await this.prisma.responseTimeMetric.findMany({
        where: whereClause
      });
      allMetrics.push(...responseMetrics.map(m => ({ ...m, type: 'response' })));
    }

    // Sort by timestamp
    allMetrics.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    if (format === 'csv') {
      const headers = ['timestamp', 'type', 'duration', 'success', 'details'];
      const rows = allMetrics.map(m => [
        m.timestamp.toISOString(),
        m.type,
        m.duration?.toString() || m.totalTime?.toString() || '0',
        m.success.toString(),
        JSON.stringify(m)
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    return JSON.stringify(allMetrics, null, 2);
  }

  /**
   * Clean up old metrics based on retention policy
   */
  async cleanupOldMetrics(): Promise<{ deleted: number }> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays);

    const deletePromises = [
      this.prisma.queryPerformanceMetric.deleteMany({
        where: { timestamp: { lt: cutoffDate } }
      }),
      this.prisma.responseTimeMetric.deleteMany({
        where: { timestamp: { lt: cutoffDate } }
      }),
      this.prisma.intentRecognitionMetric.deleteMany({
        where: { timestamp: { lt: cutoffDate } }
      }),
      this.prisma.enrichmentPerformanceMetric.deleteMany({
        where: { timestamp: { lt: cutoffDate } }
      })
    ];

    const results = await Promise.all(deletePromises);
    const totalDeleted = results.reduce((sum, result) => sum + result.count, 0);

    console.log(`🧹 [PERF-DB] Cleaned up ${totalDeleted} old performance metrics`);
    return { deleted: totalDeleted };
  }

  // Private methods

  private addToInMemoryCache(metric: PerformanceMetric): void {
    this.inMemoryMetrics.push(metric);
    
    if (this.inMemoryMetrics.length > this.config.inMemoryCacheSize) {
      this.inMemoryMetrics = this.inMemoryMetrics.slice(-this.config.inMemoryCacheSize);
    }
  }

  private scheduleFlushIfNeeded(): void {
    if (this.pendingMetrics.length >= this.config.batchSize) {
      this.flushPendingMetrics();
    }
  }

  private async flushPendingMetrics(): Promise<void> {
    if (this.pendingMetrics.length === 0) return;

    const metricsToFlush = [...this.pendingMetrics];
    this.pendingMetrics = [];

    try {
      // Group metrics by type and batch insert
      const queryMetrics = metricsToFlush.filter(m => m.type === 'query');
      const responseMetrics = metricsToFlush.filter(m => m.type === 'response');
      const intentMetrics = metricsToFlush.filter(m => m.type === 'intent');
      const enrichmentMetrics = metricsToFlush.filter(m => m.type === 'enrichment');

      const promises: Promise<any>[] = [];

      if (queryMetrics.length > 0) {
        promises.push(this.batchInsertQueryMetrics(queryMetrics));
      }

      if (responseMetrics.length > 0) {
        promises.push(this.batchInsertResponseMetrics(responseMetrics));
      }

      if (intentMetrics.length > 0) {
        promises.push(this.batchInsertIntentMetrics(intentMetrics));
      }

      if (enrichmentMetrics.length > 0) {
        promises.push(this.batchInsertEnrichmentMetrics(enrichmentMetrics));
      }

      await Promise.all(promises);
      
      console.log(`📊 [PERF-DB] Flushed ${metricsToFlush.length} performance metrics to database`);
    } catch (error) {
      console.error('❌ [PERF-DB] Error flushing metrics to database:', error);
      // Re-add failed metrics to pending queue
      this.pendingMetrics.unshift(...metricsToFlush);
    }
  }

  private async batchInsertQueryMetrics(metrics: PerformanceMetric[]): Promise<void> {
    const data = metrics.map(m => ({
      metricId: m.id,
      queryType: (m as QueryPerformanceMetric).details.queryType,
      duration: m.duration,
      success: m.success,
      dataSize: (m as QueryPerformanceMetric).details.dataSize,
      cacheHit: (m as QueryPerformanceMetric).details.cacheHit,
      retryCount: (m as QueryPerformanceMetric).details.retryCount,
      errorType: (m as QueryPerformanceMetric).details.errorType,
      timestamp: m.timestamp
    }));

    await this.prisma.queryPerformanceMetric.createMany({ data });
  }

  private async batchInsertResponseMetrics(metrics: PerformanceMetric[]): Promise<void> {
    const data = metrics.map(m => ({
      metricId: m.id,
      endpoint: (m as ResponseTimeMetric).details.endpoint,
      enriched: (m as ResponseTimeMetric).details.enriched,
      totalTime: (m as ResponseTimeMetric).details.totalTime,
      enrichmentTime: (m as ResponseTimeMetric).details.enrichmentTime,
      llmTime: (m as ResponseTimeMetric).details.llmTime,
      dataSize: (m as ResponseTimeMetric).details.dataSize,
      success: m.success,
      timestamp: m.timestamp
    }));

    await this.prisma.responseTimeMetric.createMany({ data });
  }

  private async batchInsertIntentMetrics(metrics: PerformanceMetric[]): Promise<void> {
    const data = metrics.map(m => ({
      metricId: m.id,
      messageHash: this.hashString((m as IntentRecognitionMetric).details.message),
      detectedIntents: JSON.stringify((m as IntentRecognitionMetric).details.detectedIntents),
      confidence: (m as IntentRecognitionMetric).details.confidence,
      accuracy: (m as IntentRecognitionMetric).details.accuracy,
      keywords: JSON.stringify((m as IntentRecognitionMetric).details.keywords),
      duration: m.duration,
      timestamp: m.timestamp
    }));

    await this.prisma.intentRecognitionMetric.createMany({ data });
  }

  private async batchInsertEnrichmentMetrics(metrics: PerformanceMetric[]): Promise<void> {
    const data = metrics.map(m => ({
      metricId: m.id,
      requestId: (m as EnrichmentPerformanceMetric).details.requestId,
      intentCount: (m as EnrichmentPerformanceMetric).details.intentCount,
      queryCount: (m as EnrichmentPerformanceMetric).details.queryCount,
      successfulQueries: (m as EnrichmentPerformanceMetric).details.successfulQueries,
      fallbackUsed: (m as EnrichmentPerformanceMetric).details.fallbackUsed,
      dataTypes: JSON.stringify((m as EnrichmentPerformanceMetric).details.dataTypes),
      duration: m.duration,
      success: m.success,
      timestamp: m.timestamp
    }));

    await this.prisma.enrichmentPerformanceMetric.createMany({ data });
  }

  private async writeQueryMetricToDatabase(data: any): Promise<void> {
    await this.prisma.queryPerformanceMetric.create({ data });
  }

  private async writeResponseMetricToDatabase(data: any): Promise<void> {
    await this.prisma.responseTimeMetric.create({ data });
  }

  private calculateQueryTypeStats(
    queryMetrics: any[], 
    queryType: string
  ): { avg: number; count: number; successRate: number } {
    const typeMetrics = queryMetrics.filter(m => m.queryType === queryType);
    
    if (typeMetrics.length === 0) {
      return { avg: 0, count: 0, successRate: 0 };
    }

    const avg = typeMetrics.reduce((sum, m) => sum + m.duration, 0) / typeMetrics.length;
    const successRate = typeMetrics.filter(m => m.success).length / typeMetrics.length;

    return { avg, count: typeMetrics.length, successRate };
  }

  private async getBasicPerformanceStats(timeRange: { start: Date; end: Date }): Promise<any> {
    // Simplified version for nested time ranges
    const responseMetrics = await this.prisma.responseTimeMetric.count({
      where: {
        timestamp: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      }
    });

    return {
      totalRequests: responseMetrics,
      averageResponseTime: 0, // Would need aggregation
      successRate: 0,
      cacheHitRate: 0,
      intentAccuracy: 0,
      queryPerformance: {
        stoerungen: { avg: 0, count: 0, successRate: 0 },
        dispatch: { avg: 0, count: 0, successRate: 0 },
        cutting: { avg: 0, count: 0, successRate: 0 }
      },
      timeRanges: {} as any
    };
  }

  private generateId(): string {
    return `perf_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushPendingMetrics();
    }, this.config.flushInterval);
  }

  private startCleanupTimer(): void {
    // Run cleanup daily
    this.cleanupTimer = setInterval(() => {
      this.cleanupOldMetrics();
    }, 24 * 60 * 60 * 1000);
  }

  /**
   * Destroy the service and cleanup resources
   */
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    // Flush any remaining metrics
    this.flushPendingMetrics();
    
    this.removeAllListeners();
  }
}

export default DatabasePerformanceMonitoringService;