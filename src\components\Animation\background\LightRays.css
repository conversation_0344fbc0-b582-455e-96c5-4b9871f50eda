/*
  LightRays.css
  ----------------------------------------------------------------------------
  Die<PERSON> CSS-Datei gehö<PERSON> zur React-Komponente `LightRays.tsx`.
  Sie stellt sicher, dass der Canvas/WebGL-Container immer die verfügbare
  Fläche ausfüllt und sauber im Layout sitzt.

  Hinweise:
  - Wir halten die Styles minimal und selbsterklärend.
  - Viele Kommentare helfen zukünftigen Änderungen.
  - Wenn die Komponente Klicks nicht abfangen soll, kann man optional
    `pointer-events: none;` aktivieren (siehe Kommentar unten).
*/

/*
  Der Container wird von `LightRays.tsx` gerendert und erhält diese Klasse.
  Er füllt die gesamte Breite und Höhe seines Elternelements aus.
*/
.light-rays-container {
  width: 100%;
  height: 100%;
  display: block; /* Verhindert Inline-Gap und sorgt für erwartetes Box-Verhalten */
  position: relative; /* Basis-Positionierung; Canvas wird darin platziert */
  /* pointer-events: none; */ /* Aktivieren, wenn Interaktionen durchklicken sollen */
}
