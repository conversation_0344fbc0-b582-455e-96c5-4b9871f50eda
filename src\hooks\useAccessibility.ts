import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Accessibility preferences interface
 */
export interface AccessibilityPreferences {
    prefersReducedMotion: boolean;
    prefersHighContrast: boolean;
    prefersReducedTransparency: boolean;
    screenReaderActive: boolean;
    keyboardNavigationActive: boolean;
}

/**
 * Focus management options
 */
export interface FocusManagementOptions {
    trapFocus?: boolean;
    restoreFocus?: boolean;
    skipLinks?: boolean;
    announceChanges?: boolean;
}

/**
 * Screen reader announcement types
 */
export type AnnouncementType = 'polite' | 'assertive' | 'off';

/**
 * Custom hook for comprehensive accessibility support
 * Implements requirements 4.1, 4.2, 4.3, 4.4, 6.4, 6.5
 */
export function useAccessibility(options: FocusManagementOptions = {}) {
    const [accessibilityPreferences, setAccessibilityPreferences] = useState<AccessibilityPreferences>(() => ({
        prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        prefersHighContrast: window.matchMedia('(prefers-contrast: high)').matches,
        prefersReducedTransparency: window.matchMedia('(prefers-reduced-transparency: reduce)').matches,
        screenReaderActive: false,
        keyboardNavigationActive: false,
    }));

    const announcementRef = useRef<HTMLDivElement>(null);
    const focusHistoryRef = useRef<HTMLElement[]>([]);
    const keyboardNavigationRef = useRef(false);

    /**
     * Detect screen reader usage
     */
    const detectScreenReader = useCallback(() => {
        // Check for common screen reader indicators
        const hasScreenReader = !!(
            window.navigator.userAgent.match(/NVDA|JAWS|VoiceOver|TalkBack|Dragon/i) ||
            window.speechSynthesis ||
            document.querySelector('[aria-live]') ||
            // Check if user is navigating with keyboard extensively
            keyboardNavigationRef.current
        );

        setAccessibilityPreferences(prev => ({
            ...prev,
            screenReaderActive: hasScreenReader,
        }));

        return hasScreenReader;
    }, []);

    /**
     * Handle keyboard navigation detection
     */
    const handleKeyboardNavigation = useCallback((event: KeyboardEvent) => {
        // Detect keyboard navigation patterns
        if (event.key === 'Tab' || event.key === 'ArrowUp' || event.key === 'ArrowDown' ||
            event.key === 'ArrowLeft' || event.key === 'ArrowRight' || event.key === 'Enter' ||
            event.key === 'Space') {
            keyboardNavigationRef.current = true;

            setAccessibilityPreferences(prev => ({
                ...prev,
                keyboardNavigationActive: true,
            }));

            // Store focus history for restoration
            if (document.activeElement && document.activeElement !== document.body) {
                focusHistoryRef.current.push(document.activeElement as HTMLElement);
                // Keep only last 10 focus elements
                if (focusHistoryRef.current.length > 10) {
                    focusHistoryRef.current.shift();
                }
            }
        }
    }, []);

    /**
     * Handle mouse usage (indicates non-keyboard navigation)
     */
    const handleMouseUsage = useCallback(() => {
        // Reset keyboard navigation flag on mouse usage
        keyboardNavigationRef.current = false;

        setAccessibilityPreferences(prev => ({
            ...prev,
            keyboardNavigationActive: false,
        }));
    }, []);

    /**
     * Set up media query listeners and event handlers
     */
    useEffect(() => {
        // Media query listeners
        const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
        const reducedTransparencyQuery = window.matchMedia('(prefers-reduced-transparency: reduce)');

        const handleReducedMotionChange = (e: MediaQueryListEvent) => {
            setAccessibilityPreferences(prev => ({
                ...prev,
                prefersReducedMotion: e.matches,
            }));
        };

        const handleHighContrastChange = (e: MediaQueryListEvent) => {
            setAccessibilityPreferences(prev => ({
                ...prev,
                prefersHighContrast: e.matches,
            }));
        };

        const handleReducedTransparencyChange = (e: MediaQueryListEvent) => {
            setAccessibilityPreferences(prev => ({
                ...prev,
                prefersReducedTransparency: e.matches,
            }));
        };

        // Add event listeners
        reducedMotionQuery.addEventListener('change', handleReducedMotionChange);
        highContrastQuery.addEventListener('change', handleHighContrastChange);
        reducedTransparencyQuery.addEventListener('change', handleReducedTransparencyChange);

        document.addEventListener('keydown', handleKeyboardNavigation);
        document.addEventListener('mousedown', handleMouseUsage);

        // Initial screen reader detection
        detectScreenReader();

        // Cleanup
        return () => {
            reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
            highContrastQuery.removeEventListener('change', handleHighContrastChange);
            reducedTransparencyQuery.removeEventListener('change', handleReducedTransparencyChange);

            document.removeEventListener('keydown', handleKeyboardNavigation);
            document.removeEventListener('mousedown', handleMouseUsage);
        };
    }, [handleKeyboardNavigation, handleMouseUsage, detectScreenReader]);

    /**
     * Announce message to screen readers
     */
    const announce = useCallback((message: string, type: AnnouncementType = 'polite') => {
        if (!announcementRef.current) return;

        // Clear previous announcement
        announcementRef.current.textContent = '';

        // Set aria-live attribute
        announcementRef.current.setAttribute('aria-live', type);

        // Add announcement after a brief delay to ensure screen readers pick it up
        setTimeout(() => {
            if (announcementRef.current) {
                announcementRef.current.textContent = message;
            }
        }, 100);

        // Clear announcement after 5 seconds
        setTimeout(() => {
            if (announcementRef.current) {
                announcementRef.current.textContent = '';
            }
        }, 5000);
    }, []);

    /**
     * Focus management utilities
     */
    const focusManagement = {
        /**
         * Focus first focusable element in container
         */
        focusFirst: (container: HTMLElement) => {
            const focusableElements = container.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            const firstElement = focusableElements[0] as HTMLElement;
            if (firstElement) {
                firstElement.focus();
            }
        },

        /**
         * Focus last focusable element in container
         */
        focusLast: (container: HTMLElement) => {
            const focusableElements = container.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
            if (lastElement) {
                lastElement.focus();
            }
        },

        /**
         * Trap focus within container
         */
        trapFocus: (container: HTMLElement) => {
            const focusableElements = container.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            const firstElement = focusableElements[0] as HTMLElement;
            const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

            const handleTabKey = (e: KeyboardEvent) => {
                if (e.key !== 'Tab') return;

                if (e.shiftKey) {
                    // Shift + Tab
                    if (document.activeElement === firstElement) {
                        e.preventDefault();
                        lastElement.focus();
                    }
                } else {
                    // Tab
                    if (document.activeElement === lastElement) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            };

            container.addEventListener('keydown', handleTabKey);

            // Return cleanup function
            return () => {
                container.removeEventListener('keydown', handleTabKey);
            };
        },

        /**
         * Restore focus to previous element
         */
        restoreFocus: () => {
            const lastFocusedElement = focusHistoryRef.current.pop();
            if (lastFocusedElement && document.contains(lastFocusedElement)) {
                lastFocusedElement.focus();
            }
        },
    };

    /**
     * Generate accessible IDs
     */
    const generateId = useCallback((prefix: string = 'accessible') => {
        return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
    }, []);



    /**
     * Get ARIA attributes for form elements
     */
    const getFormAriaAttributes = useCallback((
        label: string,
        description?: string,
        error?: string,
        required: boolean = false
    ) => {
        const attributes: Record<string, string> = {
            'aria-label': label,
            'aria-required': required.toString(),
        };

        const describedBy: string[] = [];

        if (description) {
            const descId = generateId('desc');
            attributes['aria-describedby'] = descId;
            describedBy.push(descId);
        }

        if (error) {
            const errorId = generateId('error');
            attributes['aria-describedby'] = [...describedBy, errorId].join(' ');
            attributes['aria-invalid'] = 'true';
        }

        return attributes;
    }, [generateId]);

    /**
     * Get announcement element ref for screen readers
     */
    const getAnnouncementRef = useCallback(() => {
        return announcementRef;
    }, []);

    return {
        accessibilityPreferences,
        announce,
        focusManagement,
        generateId,
        getFormAriaAttributes,
        getAnnouncementRef,
        detectScreenReader,
        // Convenience getters
        prefersReducedMotion: accessibilityPreferences.prefersReducedMotion,
        prefersHighContrast: accessibilityPreferences.prefersHighContrast,
        prefersReducedTransparency: accessibilityPreferences.prefersReducedTransparency,
        screenReaderActive: accessibilityPreferences.screenReaderActive,
        keyboardNavigationActive: accessibilityPreferences.keyboardNavigationActive,
    };
}

export default useAccessibility;