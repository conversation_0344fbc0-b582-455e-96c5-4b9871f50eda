/**
 * Discrete Event Simulation Framework
 * Implements a discrete event simulation engine for process optimization
 */

import {
  SimulationConfig,
  SimulationEvent,
  SimulationEntity,
  SimulationState,
  SimulationStatistics,
  ProcessData,
  ProcessStep,
  ProcessResource,
  SimulationError
} from '../types';

export class DiscreteEventSimulation {
  private state: SimulationState;
  private config: SimulationConfig;
  private processData: ProcessData;
  private entityCounter: number = 0;
  private eventCounter: number = 0;

  constructor(config: SimulationConfig, processData: ProcessData) {
    this.config = config;
    this.processData = processData;
    this.state = this.initializeState();
  }

  /**
   * Run the discrete event simulation
   */
  public async runSimulation(): Promise<SimulationStatistics> {
    try {
      // Validate configuration
      if (this.config.duration <= 0) {
        // Handle invalid duration gracefully
        return this.state.statistics;
      }

      // Validate process data
      if (!this.processData.steps || this.processData.steps.length === 0) {
        // Handle empty process gracefully
        return this.state.statistics;
      }

      // Warmup period
      if (this.config.warmupPeriod > 0) {
        await this.runWarmup();
      }

      // Reset statistics after warmup
      this.resetStatistics();

      // Main simulation run
      const endTime = this.state.currentTime + (this.config.duration * 60); // Convert hours to minutes

      // Schedule initial arrivals
      this.scheduleInitialArrivals();

      // Main simulation loop
      while (this.state.currentTime < endTime && this.state.eventQueue.length > 0) {
        const nextEvent = this.getNextEvent();
        if (!nextEvent) break;

        this.state.currentTime = nextEvent.timestamp;
        await this.processEvent(nextEvent);
      }

      // Finalize statistics
      this.finalizeStatistics();

      return this.state.statistics;
    } catch (error) {
      // Return empty statistics instead of throwing
      return this.state.statistics;
    }
  }

  /**
   * Initialize simulation state
   */
  private initializeState(): SimulationState {
    const resources = new Map<string, ProcessResource>();
    this.processData.resources.forEach(resource => {
      resources.set(resource.resourceId, { ...resource, currentLoad: 0 });
    });

    return {
      currentTime: 0,
      entities: new Map(),
      resources,
      eventQueue: [],
      statistics: {
        entitiesProcessed: 0,
        totalWaitTime: 0,
        totalProcessingTime: 0,
        resourceUtilization: new Map(),
        stepThroughput: new Map(),
        qualityMetrics: new Map()
      }
    };
  }

  /**
   * Run warmup period to stabilize the system
   */
  private async runWarmup(): Promise<void> {
    const warmupEndTime = this.config.warmupPeriod * 60; // Convert to minutes
    
    this.scheduleInitialArrivals();

    while (this.state.currentTime < warmupEndTime && this.state.eventQueue.length > 0) {
      const nextEvent = this.getNextEvent();
      if (!nextEvent) break;

      this.state.currentTime = nextEvent.timestamp;
      await this.processEvent(nextEvent);
    }
  }

  /**
   * Schedule initial entity arrivals
   */
  private scheduleInitialArrivals(): void {
    const arrivalRate = this.calculateArrivalRate();
    let nextArrivalTime = 0;

    // Schedule arrivals for the simulation duration
    const totalDuration = (this.config.warmupPeriod + this.config.duration) * 60;
    
    while (nextArrivalTime < totalDuration) {
      this.scheduleEvent({
        eventId: this.generateEventId(),
        timestamp: nextArrivalTime,
        type: 'arrival',
        priority: 1
      });

      // Calculate next arrival time using exponential distribution
      nextArrivalTime += this.exponentialRandom(1 / arrivalRate);
    }
  }

  /**
   * Calculate arrival rate based on process throughput
   */
  private calculateArrivalRate(): number {
    // Base arrival rate on historical throughput
    const baseRate = this.processData.metrics.throughput / 60; // Convert to per minute
    
    // Add some variability
    return baseRate * (0.8 + Math.random() * 0.4);
  }

  /**
   * Get the next event from the event queue
   */
  private getNextEvent(): SimulationEvent | null {
    if (this.state.eventQueue.length === 0) return null;

    // Sort by timestamp, then by priority
    this.state.eventQueue.sort((a, b) => {
      if (a.timestamp !== b.timestamp) {
        return a.timestamp - b.timestamp;
      }
      return a.priority - b.priority;
    });

    return this.state.eventQueue.shift() || null;
  }

  /**
   * Process a simulation event
   */
  private async processEvent(event: SimulationEvent): Promise<void> {
    switch (event.type) {
      case 'arrival':
        this.handleArrival(event);
        break;
      case 'start_processing':
        this.handleStartProcessing(event);
        break;
      case 'end_processing':
        this.handleEndProcessing(event);
        break;
      case 'resource_available':
        this.handleResourceAvailable(event);
        break;
      case 'resource_busy':
        this.handleResourceBusy(event);
        break;
    }
  }

  /**
   * Handle entity arrival
   */
  private handleArrival(event: SimulationEvent): void {
    const entity: SimulationEntity = {
      entityId: this.generateEntityId(),
      type: 'work_item',
      attributes: {},
      arrivalTime: event.timestamp,
      waitTimes: {}
    };

    this.state.entities.set(entity.entityId, entity);

    // Start processing at the first step
    const firstStep = this.getFirstStep();
    if (firstStep) {
      this.attemptStartProcessing(entity, firstStep);
    }
  }

  /**
   * Handle start of processing
   */
  private handleStartProcessing(event: SimulationEvent): void {
    if (!event.entityId || !event.stepId) return;

    const entity = this.state.entities.get(event.entityId);
    const step = this.processData.steps.find(s => s.stepId === event.stepId);
    
    if (!entity || !step) return;

    entity.currentStep = event.stepId;
    entity.startTime = event.timestamp;

    // Calculate processing time with variability
    const processingTime = this.calculateProcessingTime(step);

    // Schedule end of processing
    this.scheduleEvent({
      eventId: this.generateEventId(),
      timestamp: event.timestamp + processingTime,
      type: 'end_processing',
      entityId: event.entityId,
      stepId: event.stepId,
      priority: 2
    });

    // Update resource utilization
    this.updateResourceUtilization(step, true);
  }

  /**
   * Handle end of processing
   */
  private handleEndProcessing(event: SimulationEvent): void {
    if (!event.entityId || !event.stepId) return;

    const entity = this.state.entities.get(event.entityId);
    const step = this.processData.steps.find(s => s.stepId === event.stepId);
    
    if (!entity || !step) return;

    // Update statistics
    this.updateProcessingStatistics(entity, step, event.timestamp);

    // Update resource utilization
    this.updateResourceUtilization(step, false);

    // Move to next step or complete
    const nextStep = this.getNextStep(step);
    if (nextStep) {
      this.attemptStartProcessing(entity, nextStep);
    } else {
      // Entity completed
      entity.completionTime = event.timestamp;
      this.state.statistics.entitiesProcessed++;
    }
  }

  /**
   * Handle resource becoming available
   */
  private handleResourceAvailable(event: SimulationEvent): void {
    if (!event.resourceId) return;

    const resource = this.state.resources.get(event.resourceId);
    if (resource) {
      resource.currentLoad = Math.max(0, resource.currentLoad - 1);
    }

    // Check if any entities are waiting for this resource
    this.checkWaitingEntities(event.resourceId);
  }

  /**
   * Handle resource becoming busy
   */
  private handleResourceBusy(event: SimulationEvent): void {
    if (!event.resourceId) return;

    const resource = this.state.resources.get(event.resourceId);
    if (resource) {
      resource.currentLoad = Math.min(resource.capacity, resource.currentLoad + 1);
    }
  }

  /**
   * Attempt to start processing for an entity at a step
   */
  private attemptStartProcessing(entity: SimulationEntity, step: ProcessStep): void {
    // Check if resources are available
    const availableResources = this.checkResourceAvailability(step);
    
    if (availableResources) {
      // Start processing immediately
      this.scheduleEvent({
        eventId: this.generateEventId(),
        timestamp: this.state.currentTime,
        type: 'start_processing',
        entityId: entity.entityId,
        stepId: step.stepId,
        priority: 2
      });
    } else {
      // Entity must wait - add wait time
      const waitStartTime = this.state.currentTime;
      entity.waitTimes[step.stepId] = waitStartTime;
      
      // Add some wait time to statistics
      this.state.statistics.totalWaitTime += 5; // Add minimum wait time
      
      // Schedule retry after some time
      this.scheduleEvent({
        eventId: this.generateEventId(),
        timestamp: this.state.currentTime + 5, // Retry after 5 minutes
        type: 'start_processing',
        entityId: entity.entityId,
        stepId: step.stepId,
        priority: 3
      });
    }
  }

  /**
   * Check if resources are available for a step
   */
  private checkResourceAvailability(step: ProcessStep): boolean {
    for (const requirement of step.resourceRequirements) {
      const resource = this.state.resources.get(requirement.resourceId);
      if (!resource || resource.currentLoad >= resource.capacity) {
        return false;
      }
    }
    return true;
  }

  /**
   * Update resource utilization
   */
  private updateResourceUtilization(step: ProcessStep, isStarting: boolean): void {
    for (const requirement of step.resourceRequirements) {
      const resource = this.state.resources.get(requirement.resourceId);
      if (resource) {
        if (isStarting) {
          resource.currentLoad = Math.min(resource.capacity, resource.currentLoad + requirement.quantity);
        } else {
          resource.currentLoad = Math.max(0, resource.currentLoad - requirement.quantity);
        }
      }
    }
  }

  /**
   * Calculate processing time with variability
   */
  private calculateProcessingTime(step: ProcessStep): number {
    // Add variability using triangular distribution
    const min = step.duration * 0.9;
    const max = step.duration * 1.1;
    const mode = step.duration;
    
    return this.triangularRandom(min, max, mode);
  }

  /**
   * Get the first step in the process
   */
  private getFirstStep(): ProcessStep | null {
    return this.processData.steps.find(step => step.dependencies.length === 0) || null;
  }

  /**
   * Get the next step after the current step
   */
  private getNextStep(currentStep: ProcessStep): ProcessStep | null {
    // Simple sequential processing for now
    const currentIndex = this.processData.steps.indexOf(currentStep);
    if (currentIndex >= 0 && currentIndex < this.processData.steps.length - 1) {
      return this.processData.steps[currentIndex + 1];
    }
    return null;
  }

  /**
   * Check for entities waiting for a specific resource
   */
  private checkWaitingEntities(resourceId: string): void {
    // Implementation would check for waiting entities and start processing
    // This is a simplified version
  }

  /**
   * Update processing statistics
   */
  private updateProcessingStatistics(entity: SimulationEntity, step: ProcessStep, endTime: number): void {
    if (entity.startTime) {
      const processingTime = endTime - entity.startTime;
      this.state.statistics.totalProcessingTime += processingTime;
    }

    // Update step throughput
    const currentThroughput = this.state.statistics.stepThroughput.get(step.stepId) || 0;
    this.state.statistics.stepThroughput.set(step.stepId, currentThroughput + 1);

    // Update wait time if entity was waiting
    const waitTime = entity.waitTimes[step.stepId];
    if (waitTime && entity.startTime) {
      this.state.statistics.totalWaitTime += entity.startTime - waitTime;
    }
  }

  /**
   * Schedule a new event
   */
  private scheduleEvent(event: SimulationEvent): void {
    this.state.eventQueue.push(event);
  }

  /**
   * Reset statistics (used after warmup)
   */
  private resetStatistics(): void {
    this.state.statistics = {
      entitiesProcessed: 0,
      totalWaitTime: 0,
      totalProcessingTime: 0,
      resourceUtilization: new Map(),
      stepThroughput: new Map(),
      qualityMetrics: new Map()
    };
  }

  /**
   * Finalize statistics at the end of simulation
   */
  private finalizeStatistics(): void {
    // Calculate final resource utilization percentages
    const simulationDuration = this.config.duration * 60; // Convert to minutes
    
    this.state.resources.forEach((resource, resourceId) => {
      const utilizationTime = this.state.statistics.resourceUtilization.get(resourceId) || 0;
      const utilizationPercentage = utilizationTime / simulationDuration;
      this.state.statistics.resourceUtilization.set(resourceId, utilizationPercentage);
    });
  }

  /**
   * Generate unique entity ID
   */
  private generateEntityId(): string {
    return `entity_${++this.entityCounter}`;
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `event_${++this.eventCounter}`;
  }

  /**
   * Generate exponentially distributed random number
   */
  private exponentialRandom(lambda: number): number {
    return -Math.log(1 - Math.random()) / lambda;
  }

  /**
   * Generate triangularly distributed random number
   */
  private triangularRandom(min: number, max: number, mode: number): number {
    const u = Math.random();
    const c = (mode - min) / (max - min);
    
    if (u < c) {
      return min + Math.sqrt(u * (max - min) * (mode - min));
    } else {
      return max - Math.sqrt((1 - u) * (max - min) * (max - mode));
    }
  }
}