import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Terminal, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { workflowsClient } from '@/helpers/ipc/workflows.ipc';

type WorkflowStatus = 'idle' | 'running' | 'success' | 'error';

interface BestandLogsCardProps {
  className?: string;
}

export function BestandLogsCard({ className }: BestandLogsCardProps) {
  const [status, setStatus] = useState<WorkflowStatus>('idle');
  const [executionProgress, setExecutionProgress] = useState(0);
  const [logLines, setLogLines] = useState<string[]>([]);

  // Status Poll
  useEffect(() => {
    let mounted = true;
    const poll = async () => {
      try {
        const s = await workflowsClient.status('bestand');
        if (!mounted) return;
        const mapped: WorkflowStatus =
          s.status === 'running' ? 'running' : s.status === 'success' ? 'success' : s.status === 'error' ? 'error' : 'idle';
        setStatus(mapped);
        
        // Fake progress for running status
        if (s.status === 'running') {
          setExecutionProgress((prev) => {
            if (prev >= 90) return 90;
            return prev + Math.random() * 5;
          });
        } else if (s.status === 'success') {
          setExecutionProgress(100);
        } else if (s.status === 'idle') {
          setExecutionProgress(0);
        }
      } catch {
        // ignore
      }
    };
    poll();
    const i = setInterval(poll, 2000);
    return () => {
      mounted = false;
      clearInterval(i);
    };
  }, []);

  // Subscribe to logs when running
  useEffect(() => {
    if (status === 'running') {
      const unsub = workflowsClient.subscribeLogs('bestand', (line) => {
        setLogLines((prev) => [...prev, line]);
      });
      return unsub;
    }
  }, [status]);

  const variants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: 'spring' as const, damping: 25 },
    },
  };

  return (
    <motion.div
      variants={variants}
      className={cn(
        'group border-gray-300/60 bg-white hover:border-gray-400/80 relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border-2 px-6 pt-6 pb-6 shadow-lg transition-all duration-500',
        className,
      )}
    >
      {/* Background Pattern */}
      <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#6b728060_1px,transparent_1px),linear-gradient(to_bottom,#6b728060_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>

      {/* Large Background Icon */}
      <div className="text-gray-200/50 group-hover:text-gray-300/70 absolute right-1 bottom-3 scale-[4] transition-all duration-700 group-hover:scale-[4.2]">
        <Terminal className="h-8 w-8" />
      </div>

      <div className="relative z-10 flex h-full flex-col">
        {/* Header */}
        <div className="flex items-center gap-3 mb-4">
          <div className="bg-gray-100/70 text-gray-600 shadow-gray-200/50 group-hover:bg-gray-200/80 group-hover:shadow-gray-300/60 flex h-10 w-10 items-center justify-center rounded-full shadow-lg transition-all duration-500">
            <Terminal className="h-5 w-5" />
          </div>
          <div>
            <h3 className="text-lg font-semibold tracking-tight">Workflow Logs</h3>
            <p className="text-muted-foreground text-sm">Live-Ausgabe des Bestand Workflows</p>
          </div>
        </div>

        {/* Progress */}
        {(status === 'running' || executionProgress > 0) && (
          <div className="mb-4">
            <div className="flex justify-between text-xs mb-2">
              <span className="text-muted-foreground">Fortschritt</span>
              <span className="text-blue-600 font-medium">{Math.round(executionProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300" 
                style={{ width: `${executionProgress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Live Logs */}
        <div className="flex-1 min-h-0">
          {logLines.length > 0 ? (
            <div className="h-full overflow-auto rounded-lg bg-gray-900 p-4 text-xs text-green-400 border font-mono">
              {logLines.slice(-100).map((line, idx) => (
                <div key={idx} className="whitespace-pre-wrap leading-relaxed">
                  {line}
                </div>
              ))}
            </div>
          ) : (
            <div className="h-full flex items-center justify-center rounded-lg bg-gray-50 border-2 border-dashed border-gray-300">
              <div className="text-center text-gray-500">
                <Terminal className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Keine Logs verfügbar</p>
                <p className="text-xs">Starte den Workflow um Logs zu sehen</p>
              </div>
            </div>
          )}
        </div>

        {/* Status Indicator */}
        {status === 'running' && (
          <div className="mt-4 flex items-center gap-2 text-sm text-blue-600">
            <Clock className="h-4 w-4 animate-spin" />
            <span>Workflow läuft...</span>
          </div>
        )}
      </div>

      {/* Bottom Gradient */}
      <div className="from-gray-400 to-gray-300 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
    </motion.div>
  );
}