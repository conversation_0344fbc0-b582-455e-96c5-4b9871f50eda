import { AIPerformanceMonitor } from '../services/performance/AIPerformanceMonitor';
import { AICacheService } from '../services/caching/AICacheService';

// Global instances
let performanceMonitor: AIPerformanceMonitor;
let cacheService: AICacheService;

export function setPerformanceMonitor(monitor: AIPerformanceMonitor): void {
  performanceMonitor = monitor;
}

export function setCacheService(cache: AICacheService): void {
  cacheService = cache;
}

/**
 * Decorator for monitoring method performance
 */
export function MonitorPerformance(operationName?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const actualOperationName = operationName || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      if (!performanceMonitor) {
        return originalMethod.apply(this, args);
      }

      const operationId = `${actualOperationName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      performanceMonitor.startOperation(operationId, actualOperationName, {
        className: target.constructor.name,
        methodName: propertyKey,
        args: args.length
      });

      try {
        const result = await originalMethod.apply(this, args);
        performanceMonitor.endOperation(operationId, true);
        return result;
      } catch (error) {
        performanceMonitor.endOperation(operationId, false, error instanceof Error ? error.message : 'Unknown error');
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Decorator for caching method results
 */
export function Cacheable(options?: {
  ttl?: number;
  keyGenerator?: (...args: any[]) => string;
  tags?: string[];
}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      if (!cacheService) {
        return originalMethod.apply(this, args);
      }

      const keyGenerator = options?.keyGenerator || ((...args) => 
        `${target.constructor.name}.${propertyKey}:${JSON.stringify(args)}`
      );
      
      const cacheKey = keyGenerator(...args);
      const taggedKey = options?.tags 
        ? `${cacheKey}:${options.tags.map(tag => `tag:${tag}`).join(':')}`
        : cacheKey;

      // Try to get from cache
      const cached = await cacheService.get(taggedKey);
      if (cached !== null) {
        return cached;
      }

      // Execute method and cache result
      const result = await originalMethod.apply(this, args);
      await cacheService.set(taggedKey, result, options?.ttl);
      
      return result;
    };

    return descriptor;
  };
}

/**
 * Decorator for memoizing expensive operations
 */
export function Memoize(options?: {
  ttl?: number;
  maxSize?: number;
  keyGenerator?: (...args: any[]) => string;
}) {
  const memoCache = new Map<string, { value: any; timestamp: number; ttl: number }>();
  const maxSize = options?.maxSize || 100;
  const defaultTtl = options?.ttl || 300000; // 5 minutes

  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const keyGenerator = options?.keyGenerator || ((...args) => 
        `${target.constructor.name}.${propertyKey}:${JSON.stringify(args)}`
      );
      
      const key = keyGenerator(...args);
      const now = Date.now();

      // Check cache
      const cached = memoCache.get(key);
      if (cached && (now - cached.timestamp) < cached.ttl) {
        return cached.value;
      }

      // Execute method
      const result = await originalMethod.apply(this, args);

      // Cache result
      memoCache.set(key, {
        value: result,
        timestamp: now,
        ttl: defaultTtl
      });

      // Cleanup old entries if cache is too large
      if (memoCache.size > maxSize) {
        const entries = Array.from(memoCache.entries());
        entries.sort(([, a], [, b]) => a.timestamp - b.timestamp);
        
        // Remove oldest 20% of entries
        const toRemove = Math.floor(entries.length * 0.2);
        for (let i = 0; i < toRemove; i++) {
          memoCache.delete(entries[i][0]);
        }
      }

      return result;
    };

    return descriptor;
  };
}

/**
 * Decorator for rate limiting method calls
 */
export function RateLimit(options: {
  maxCalls: number;
  windowMs: number;
  keyGenerator?: (...args: any[]) => string;
}) {
  const rateLimitCache = new Map<string, { calls: number; windowStart: number }>();

  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const keyGenerator = options.keyGenerator || (() => 
        `${target.constructor.name}.${propertyKey}`
      );
      
      const key = keyGenerator(...args);
      const now = Date.now();

      let rateLimitData = rateLimitCache.get(key);
      
      // Reset window if expired
      if (!rateLimitData || (now - rateLimitData.windowStart) > options.windowMs) {
        rateLimitData = { calls: 0, windowStart: now };
        rateLimitCache.set(key, rateLimitData);
      }

      // Check rate limit
      if (rateLimitData.calls >= options.maxCalls) {
        const resetTime = rateLimitData.windowStart + options.windowMs;
        const waitTime = resetTime - now;
        throw new Error(`Rate limit exceeded. Try again in ${waitTime}ms`);
      }

      // Increment call count
      rateLimitData.calls++;

      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}

/**
 * Decorator for automatic retry with exponential backoff
 */
export function Retry(options?: {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  retryCondition?: (error: any) => boolean;
}) {
  const maxAttempts = options?.maxAttempts || 3;
  const baseDelay = options?.baseDelay || 1000;
  const maxDelay = options?.maxDelay || 10000;
  const retryCondition = options?.retryCondition || (() => true);

  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      let lastError: any;

      for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
          return await originalMethod.apply(this, args);
        } catch (error) {
          lastError = error;

          if (attempt === maxAttempts || !retryCondition(error)) {
            throw error;
          }

          // Calculate delay with exponential backoff
          const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      throw lastError;
    };

    return descriptor;
  };
}

/**
 * Decorator for circuit breaker pattern
 */
export function CircuitBreaker(options?: {
  failureThreshold?: number;
  resetTimeout?: number;
  monitoringPeriod?: number;
}) {
  const failureThreshold = options?.failureThreshold || 5;
  const resetTimeout = options?.resetTimeout || 60000; // 1 minute
  const monitoringPeriod = options?.monitoringPeriod || 10000; // 10 seconds

  let state: 'closed' | 'open' | 'half-open' = 'closed';
  let failures = 0;
  let lastFailureTime = 0;
  let nextAttemptTime = 0;

  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const now = Date.now();

      // Check if circuit should be reset
      if (state === 'open' && now >= nextAttemptTime) {
        state = 'half-open';
      }

      // Reject if circuit is open
      if (state === 'open') {
        throw new Error(`Circuit breaker is open. Next attempt in ${nextAttemptTime - now}ms`);
      }

      try {
        const result = await originalMethod.apply(this, args);
        
        // Reset on success
        if (state === 'half-open') {
          state = 'closed';
          failures = 0;
        }

        return result;
      } catch (error) {
        failures++;
        lastFailureTime = now;

        // Open circuit if threshold exceeded
        if (failures >= failureThreshold) {
          state = 'open';
          nextAttemptTime = now + resetTimeout;
        }

        throw error;
      }
    };

    return descriptor;
  };
}