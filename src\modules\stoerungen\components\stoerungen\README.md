# Störung Form Modal

The `StoerungsForm` component has been converted to work as a modal dialog instead of an inline form.

## Components

### StoerungsForm
The core form component that handles störung creation. Now designed to work within a modal container.

### StoerungsFormModal
A wrapper component that integrates `StoerungsForm` with the dialog system.

## Usage

### Method 1: Using the utility function (Recommended)
```tsx
import { useDialog } from '@/components/ui/dialog';
import { openStoerungsForm } from '@/modules/stoerungen/utils';

function MyComponent() {
  const { openDialog } = useDialog();

  const handleRefresh = () => {
    // Your refresh logic here
  };

  const handleOpenForm = () => {
    openStoerungsForm(openDialog, handleRefresh);
  };

  return (
    <Button onClick={handleOpenForm}>
      Störung Melden
    </Button>
  );
}
```

### Method 2: Direct usage
```tsx
import { useDialog } from '@/components/ui/dialog';
import { StoerungsFormModal } from '@/modules/stoerungen/components/stoerungen/StoerungsFormModal';

function MyComponent() {
  const { openDialog } = useDialog();

  const handleRefresh = () => {
    // Your refresh logic here
  };

  const handleOpenForm = () => {
    openDialog(
      <StoerungsFormModal onSubmit={handleRefresh} />
    );
  };

  return (
    <Button onClick={handleOpenForm}>
      Störung Melden
    </Button>
  );
}
```

## Features

- **Modal Dialog**: Form opens in a centered modal overlay
- **Responsive**: Works on all screen sizes
- **Auto-close**: Modal closes automatically after successful submission
- **Error Handling**: Displays validation and submission errors
- **German Interface**: All text in German as per project requirements

## Requirements

- The component must be used within a `DialogProvider` context
- The `useDialog` hook must be available in the calling component
- All form validation and submission logic remains the same as the original inline form