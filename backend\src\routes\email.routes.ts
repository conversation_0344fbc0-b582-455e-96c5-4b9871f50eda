/**
 * E-Mail API Routes
 * 
 * API-Endpunkte für E-Mail-Versand mit Anhängen.
 */

import { Router } from 'express';
import { rateLimitConfig } from '../middleware/rate-limit.middleware';
import { EmailController } from '../controllers/email.controller';

const router = Router();
const emailController = new EmailController();

/**
 * @route POST /api/email/send
 * @desc Sendet eine E-Mail mit optionalen Anhängen
 * @access Private (Authentication required)
 */
router.post('/send', rateLimitConfig.emailSend, emailController.sendEmail.bind(emailController));

/**
 * @route POST /api/email/send-stoerung
 * @desc Sendet eine Störungs-E-Mail mit Anhängen
 * @access Private (Authentication required)
 */
router.post('/send-stoerung', rateLimitConfig.emailSend, emailController.sendStoerungEmail.bind(emailController));

/**
 * @route GET /api/email/config
 * @desc Überprüft die E-Mail-Konfiguration
 * @access Private (Authentication required)
 */
router.get('/config', rateLimitConfig.healthCheck, emailController.checkConfig.bind(emailController));

export default router;