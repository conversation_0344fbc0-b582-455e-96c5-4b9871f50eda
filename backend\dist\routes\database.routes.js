"use strict";
/**
 * Datenbank-API-Routen
 *
 * Diese Routen stellen die Datenbankfunktionen als REST-API zur Verfügung.
 * Das Frontend kann diese API verwenden, um auf die Datenbank zuzugreifen.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const database_service_1 = require("../services/database.service");
const validation_middleware_1 = require("../middleware/validation.middleware");
const rate_limiting_middleware_1 = require("../middleware/rate-limiting.middleware");
const repository_factory_1 = require("../repositories/repository.factory");
const router = express_1.default.Router();
// Debug-Ausgabe beim <PERSON> der Routen
console.log('[ROUTES] Lade Database-Routen...');
// DatabaseService instanziieren mit Error-Handling
let databaseService;
try {
    databaseService = new database_service_1.DatabaseService();
    console.log('[ROUTES] DatabaseService erfolgreich initialisiert');
}
catch (error) {
    console.error('[ROUTES] Fehler beim Initialisieren des DatabaseService:', error);
    throw error;
}
// WICHTIG: Router-Debugging
router.use((req, res, next) => {
    console.log(`[ROUTES] Router erreicht: ${req.method} ${req.originalUrl} ${req.path}`);
    next();
});
// Basis-Middleware für alle Database-Routen
router.use(validation_middleware_1.sanitizationMiddleware); // Eingabe-Sanitization
router.use(rate_limiting_middleware_1.rateLimitConfig.dataEndpoint); // Rate-Limiting für Daten-Endpunkte
/**
 * Hilfsfunktion zum Verarbeiten von Anfragen und Fehlerbehandlung
 */
async function handleRequest(req, res, operation) {
    try {
        console.log(`[ROUTES] Verarbeite Anfrage: ${req.method} ${req.originalUrl}`);
        const result = await operation();
        const response = {
            success: true,
            data: result
        };
        console.log(`[ROUTES] Erfolgreiche Antwort für ${req.originalUrl}`);
        res.json(response);
    }
    catch (error) {
        console.error(`[ROUTES] API-Fehler für ${req.originalUrl}:`, error);
        const response = {
            success: false,
            error: error instanceof Error ? error.message : 'Unbekannter Fehler'
        };
        res.status(500).json(response);
    }
}
// Test-Route (vereinfacht)
router.get('/test', (req, res) => {
    console.log('[ROUTES] Test-Route aufgerufen!');
    res.json({
        message: 'Database-Routen funktionieren!',
        timestamp: new Date().toISOString(),
        path: req.path,
        originalUrl: req.originalUrl
    });
});
// Testroute für Ablaengerei-Daten (direkt über Repository)
router.get('/test-ablaengerei', async (req, res) => {
    console.log('[ROUTES] Test-Ablaengerei-Route aufgerufen!');
    try {
        // Zugriff über das Repository statt direkt auf die Datenbank
        const cuttingRepo = (0, repository_factory_1.getCuttingRepository)();
        const ablaengereiData = await cuttingRepo.getAblaengereiData();
        res.json({
            success: true,
            message: 'Ablaengerei-Tabelle Statistik',
            count: ablaengereiData.length,
            firstFew: ablaengereiData.slice(0, 3),
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('[ROUTES] Fehler beim Zugriff auf Ablaengerei-Tabelle:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unbekannter Fehler',
            timestamp: new Date().toISOString()
        });
    }
});
// Service Level Daten
router.get('/service-level', async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => (0, repository_factory_1.getDispatchRepository)().getServiceLevelData(dateRange));
});
// Tägliche Leistungsdaten
router.get('/daily-performance', async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => (0, repository_factory_1.getDispatchRepository)().getDailyPerformanceData(dateRange));
});
// Kommissionierungsdaten
router.get('/picking', async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => (0, repository_factory_1.getDispatchRepository)().getPickingData(dateRange));
});
// Retourendaten
router.get('/returns', async (req, res) => {
    await handleRequest(req, res, () => (0, repository_factory_1.getDispatchRepository)().getReturnsData());
});
// Lieferpositionsdaten
router.get('/delivery-positions', async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => (0, repository_factory_1.getDispatchRepository)().getDeliveryPositionsData(dateRange));
});
// Tagesleistungsdaten
router.get('/tagesleistung', async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => (0, repository_factory_1.getDispatchRepository)().getTagesleistungData(dateRange));
});
// Importiert Ablaengerei-CSV-Daten (mit strengerem Rate-Limiting)
router.post('/import-ablaengerei-csv', rate_limiting_middleware_1.rateLimitConfig.writeOperation, validation_middleware_1.validators.csvImport, async (req, res) => {
    await handleRequest(req, res, () => databaseService.importAblaengereiCsvData());
});
// Ablaengerei-Daten
router.get('/ablaengerei', async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => (0, repository_factory_1.getCuttingRepository)().getAblaengereiData(dateRange));
});
// WE-Daten (Wareneingang)
router.get('/we', async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => (0, repository_factory_1.getCuttingRepository)().getWEData(dateRange));
});
// Lagerauslastung200-Daten
router.get('/lagerauslastung200', validation_middleware_1.validators.dateRange, async (req, res) => {
    // Datumsparameter aus der validierten Anfrage extrahieren
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    // Parameter an das Warehouse Repository übergeben
    await handleRequest(req, res, () => (0, repository_factory_1.getWarehouseRepository)().getLagerauslastung200Data(dateRange));
});
// Lagerauslastung240-Daten
router.get('/lagerauslastung240', validation_middleware_1.validators.dateRange, async (req, res) => {
    // Datumsparameter aus der validierten Anfrage extrahieren
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    // Parameter an das Warehouse Repository übergeben
    await handleRequest(req, res, () => (0, repository_factory_1.getWarehouseRepository)().getLagerauslastung240Data(dateRange));
});
// Schnitte-Daten
router.get('/schnitte-data', async (req, res) => {
    await handleRequest(req, res, () => (0, repository_factory_1.getCuttingRepository)().getSchnitteData());
});
// Maschinen-Effizienz-Daten
router.get('/maschinen-efficiency', validation_middleware_1.validators.maschinenEfficiency, async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => (0, repository_factory_1.getCuttingRepository)().getMaschinenEfficiency(dateRange));
});
// Cutting-Chart-Daten
router.get('/cutting-chart-data', async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => (0, repository_factory_1.getCuttingRepository)().getCuttingChartData(dateRange));
});
// Lager-Cuts-Chart-Daten
router.get('/lager-cuts-chart-data', async (req, res) => {
    console.log('🔍 [DEBUG] Lager-Cuts-Chart-Daten-Endpunkt aufgerufen!');
    const { startDate, endDate } = req.query;
    console.log(`🔍 [DEBUG] Datumsbereich für Lager-Cuts: ${startDate || 'nicht angegeben'} bis ${endDate || 'nicht angegeben'}`);
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    try {
        console.log('🔍 [DEBUG] Rufe getCuttingRepository().getLagerCutsChartData auf...');
        const result = await (0, repository_factory_1.getCuttingRepository)().getLagerCutsChartData(dateRange);
        console.log(`🔍 [DEBUG] Lager-Cuts-Chart-Daten erhalten: ${result ? JSON.stringify(result).substring(0, 100) + '...' : 'keine Daten'}`);
        const response = {
            success: true,
            data: result
        };
        console.log(`✅ [DEBUG] Sende Lager-Cuts-Chart-Daten-Antwort: ${JSON.stringify(response).substring(0, 100)}...`);
        res.json(response);
    }
    catch (error) {
        console.error(`❌ [DEBUG] Fehler beim Abrufen der Lager-Cuts-Chart-Daten:`, error);
        const response = {
            success: false,
            error: error instanceof Error ? error.message : 'Unbekannter Fehler'
        };
        res.status(500).json(response);
    }
});
// ATrL-Daten
router.get('/atrl-data', validation_middleware_1.validators.dateRange, async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => (0, repository_factory_1.getWarehouseRepository)().getAtrlData(dateRange));
});
// ARiL-Daten
router.get('/aril-data', validation_middleware_1.validators.dateRange, async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => (0, repository_factory_1.getWarehouseRepository)().getArilData(dateRange));
});
// System FTS Verfügbarkeitsdaten
router.get('/system-fts-data', validation_middleware_1.validators.dateRange, async (req, res) => {
    const { startDate, endDate } = req.query;
    const dateRange = startDate && endDate ? { startDate, endDate } : undefined;
    await handleRequest(req, res, () => databaseService.getSystemFTSData(dateRange));
});
// Materialdaten aus der Datenbank abrufen
router.get('/materialdaten', async (req, res) => {
    console.log('[ROUTES] Materialdaten-Route aufgerufen!');
    await handleRequest(req, res, () => databaseService.getMaterialdaten());
});
// Trommeldaten aus der Datenbank abrufen
router.get('/trommeldaten', async (req, res) => {
    console.log('[ROUTES] Trommeldaten-Route aufgerufen!');
    await handleRequest(req, res, () => databaseService.getTrommeldaten());
});
exports.default = router;
