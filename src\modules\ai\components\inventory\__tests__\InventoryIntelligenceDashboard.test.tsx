import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import InventoryIntelligenceDashboard from '../InventoryIntelligenceDashboard';
import { InventoryIntelligenceService } from '../../../services/inventory/InventoryIntelligenceService';

// Mock the inventory intelligence service
vi.mock('../../../services/inventory/InventoryIntelligenceService');

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}));

// Mock child components
vi.mock('../ABCAnalysisChart', () => ({
  ABCAnalysisChart: ({ classification, onItemSelect }: any) => (
    <div data-testid="abc-analysis-chart" onClick={() => onItemSelect?.('test-item')}>
      ABC Analysis Chart - {classification?.classA?.length || 0} Class A items
    </div>
  )
}));

vi.mock('../DemandForecastChart', () => ({
  DemandForecastChart: ({ forecast }: any) => (
    <div data-testid="demand-forecast-chart">
      Demand Forecast Chart - {forecast?.itemId || 'No item selected'}
    </div>
  )
}));

vi.mock('../InventoryRecommendationPanel', () => ({
  InventoryRecommendationPanel: ({ reorderRecommendations, onApproveRecommendation }: any) => (
    <div data-testid="recommendation-panel">
      Recommendation Panel - {reorderRecommendations?.length || 0} recommendations
      <button onClick={() => onApproveRecommendation?.('test-item', 'reorder')}>
        Approve Test
      </button>
    </div>
  )
}));

// Mock ErrorBoundary
vi.mock('@/components/ErrorBoundary', () => ({
  ChartErrorBoundary: ({ children }: any) => <div data-testid="error-boundary">{children}</div>
}));

describe('InventoryIntelligenceDashboard', () => {
  const mockService = {
    initialize: vi.fn(),
    performABCAnalysis: vi.fn(),
    detectStockAnomalies: vi.fn(),
    calculateOptimalReorderPoint: vi.fn(),
    forecastDemand: vi.fn(),
    detectSeasonality: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup mock service
    (InventoryIntelligenceService as any).mockImplementation(() => mockService);
    
    // Setup default mock responses
    mockService.initialize.mockResolvedValue(undefined);
    mockService.performABCAnalysis.mockResolvedValue({
      classA: [{ id: 'item-1', name: 'High Value Item', category: 'A', currentStock: 100, unitPrice: 50, lastUpdated: new Date() }],
      classB: [{ id: 'item-2', name: 'Medium Value Item', category: 'B', currentStock: 200, unitPrice: 25, lastUpdated: new Date() }],
      classC: [{ id: 'item-3', name: 'Low Value Item', category: 'C', currentStock: 500, unitPrice: 5, lastUpdated: new Date() }],
      reclassifications: [],
      analysisDate: new Date(),
      criteria: { method: 'value', classAThreshold: 80, classBThreshold: 95, timeWindow: 90 }
    });
    mockService.detectStockAnomalies.mockResolvedValue([]);
    mockService.calculateOptimalReorderPoint.mockResolvedValue({
      itemId: 'test-item',
      currentStock: 100,
      reorderPoint: 150,
      recommendedOrderQuantity: 200,
      urgency: 'medium',
      reasoning: 'Test reasoning',
      confidence: 0.8,
      leadTime: 7,
      safetyStock: 25
    });
    mockService.forecastDemand.mockResolvedValue({
      itemId: 'test-item',
      predictions: [
        {
          date: new Date('2024-01-01'),
          predictedDemand: 50,
          confidenceInterval: { lower: 40, upper: 60 }
        }
      ],
      confidence: 0.85,
      seasonalFactors: [],
      trendDirection: 'stable',
      forecastMethod: 'exponential_smoothing',
      accuracy: 0.9,
      generatedAt: new Date()
    });
    mockService.detectSeasonality.mockResolvedValue({
      itemId: 'test-item',
      hasSeasonality: false,
      patterns: [],
      strength: 0,
      detectedAt: new Date()
    });
  });

  it('renders dashboard with header and navigation', async () => {
    render(<InventoryIntelligenceDashboard />);

    // Check header
    expect(screen.getByText('INVENTORY INTELLIGENCE')).toBeInTheDocument();
    
    // Check action buttons
    expect(screen.getByText('Aktualisieren')).toBeInTheDocument();
    expect(screen.getByText('PDF Export')).toBeInTheDocument();
    expect(screen.getByText('Excel Export')).toBeInTheDocument();

    // Wait for initialization
    await waitFor(() => {
      expect(mockService.initialize).toHaveBeenCalled();
    });
  });

  it('displays configuration panel with settings', () => {
    render(<InventoryIntelligenceDashboard />);

    // Check configuration panel
    expect(screen.getByText('Analyse-Einstellungen')).toBeInTheDocument();
    expect(screen.getByText('Analysezeitraum (Tage)')).toBeInTheDocument();
    expect(screen.getByText('Prognosehorizont (Tage)')).toBeInTheDocument();
    expect(screen.getByText('Artikel für Detailanalyse')).toBeInTheDocument();
  });

  it('shows tabs for different analysis views', () => {
    render(<InventoryIntelligenceDashboard />);

    // Check tab navigation
    expect(screen.getByText('ABC-Analyse')).toBeInTheDocument();
    expect(screen.getByText('Bedarfsprognose')).toBeInTheDocument();
    expect(screen.getByText('Empfehlungen')).toBeInTheDocument();
    expect(screen.getByText('Übersicht')).toBeInTheDocument();
  });

  it('initializes service and loads data on mount', async () => {
    render(<InventoryIntelligenceDashboard />);

    await waitFor(() => {
      expect(mockService.initialize).toHaveBeenCalled();
      expect(mockService.performABCAnalysis).toHaveBeenCalled();
      expect(mockService.detectStockAnomalies).toHaveBeenCalled();
    });
  });

  it('handles refresh analysis button click', async () => {
    render(<InventoryIntelligenceDashboard />);

    // Wait for initial load
    await waitFor(() => {
      expect(mockService.initialize).toHaveBeenCalled();
    });

    // Click refresh button
    const refreshButton = screen.getByText('Aktualisieren');
    fireEvent.click(refreshButton);

    await waitFor(() => {
      expect(mockService.performABCAnalysis).toHaveBeenCalledTimes(2);
    });
  });

  it('displays ABC analysis chart when data is loaded', async () => {
    render(<InventoryIntelligenceDashboard />);

    await waitFor(() => {
      expect(screen.getByTestId('abc-analysis-chart')).toBeInTheDocument();
      expect(screen.getByText('ABC Analysis Chart - 1 Class A items')).toBeInTheDocument();
    });
  });

  it('shows demand forecast when item is selected', async () => {
    render(<InventoryIntelligenceDashboard />);

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByTestId('abc-analysis-chart')).toBeInTheDocument();
    });

    // Switch to demand forecast tab
    const forecastTab = screen.getByText('Bedarfsprognose');
    fireEvent.click(forecastTab);

    // Select an item
    const itemSelect = screen.getByDisplayValue('');
    fireEvent.change(itemSelect, { target: { value: 'NYY-J-3x1.5' } });

    await waitFor(() => {
      expect(mockService.forecastDemand).toHaveBeenCalledWith('NYY-J-3x1.5', 30);
      expect(mockService.detectSeasonality).toHaveBeenCalledWith('NYY-J-3x1.5');
    });
  });

  it('displays recommendations panel', async () => {
    render(<InventoryIntelligenceDashboard />);

    // Switch to recommendations tab
    const recommendationsTab = screen.getByText('Empfehlungen');
    fireEvent.click(recommendationsTab);

    await waitFor(() => {
      expect(screen.getByTestId('recommendation-panel')).toBeInTheDocument();
    });
  });

  it('handles recommendation approval', async () => {
    render(<InventoryIntelligenceDashboard />);

    // Switch to recommendations tab
    const recommendationsTab = screen.getByText('Empfehlungen');
    fireEvent.click(recommendationsTab);

    await waitFor(() => {
      expect(screen.getByTestId('recommendation-panel')).toBeInTheDocument();
    });

    // Click approve button
    const approveButton = screen.getByText('Approve Test');
    fireEvent.click(approveButton);

    // Should handle approval (in real implementation would update state)
    expect(approveButton).toBeInTheDocument();
  });

  it('shows overview tab with summary metrics', async () => {
    render(<InventoryIntelligenceDashboard />);

    // Switch to overview tab
    const overviewTab = screen.getByText('Übersicht');
    fireEvent.click(overviewTab);

    await waitFor(() => {
      expect(screen.getByText('Analysierte Artikel')).toBeInTheDocument();
      expect(screen.getByText('Klasse A Artikel')).toBeInTheDocument();
      expect(screen.getByText('Aktive Anomalien')).toBeInTheDocument();
      expect(screen.getByText('Empfehlungen')).toBeInTheDocument();
    });

    // Check quick actions
    expect(screen.getByText('Schnellaktionen')).toBeInTheDocument();
    expect(screen.getByText('Analyse aktualisieren')).toBeInTheDocument();
    expect(screen.getByText('Bericht exportieren')).toBeInTheDocument();
    expect(screen.getByText('Einstellungen')).toBeInTheDocument();
  });

  it('handles export functionality', async () => {
    // Mock URL.createObjectURL and related functions
    const mockCreateObjectURL = vi.fn(() => 'mock-url');
    const mockRevokeObjectURL = vi.fn();
    global.URL.createObjectURL = mockCreateObjectURL;
    global.URL.revokeObjectURL = mockRevokeObjectURL;

    // Mock document.createElement and appendChild
    const mockAnchor = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    const mockCreateElement = vi.fn(() => mockAnchor);
    const mockAppendChild = vi.fn();
    const mockRemoveChild = vi.fn();
    
    Object.defineProperty(document, 'createElement', { value: mockCreateElement });
    Object.defineProperty(document.body, 'appendChild', { value: mockAppendChild });
    Object.defineProperty(document.body, 'removeChild', { value: mockRemoveChild });

    render(<InventoryIntelligenceDashboard />);

    // Wait for initial load
    await waitFor(() => {
      expect(mockService.initialize).toHaveBeenCalled();
    });

    // Click PDF export button
    const pdfExportButton = screen.getByText('PDF Export');
    fireEvent.click(pdfExportButton);

    await waitFor(() => {
      expect(mockCreateObjectURL).toHaveBeenCalled();
      expect(mockAnchor.click).toHaveBeenCalled();
      expect(mockRevokeObjectURL).toHaveBeenCalled();
    });
  });

  it('handles configuration changes', async () => {
    render(<InventoryIntelligenceDashboard />);

    // Change analysis timeframe
    const timeframeSelect = screen.getByDisplayValue('90');
    fireEvent.change(timeframeSelect, { target: { value: '180' } });

    // Change forecast horizon
    const horizonSelect = screen.getByDisplayValue('30');
    fireEvent.change(horizonSelect, { target: { value: '60' } });

    // Configuration should be updated (would trigger re-analysis in real implementation)
    expect(timeframeSelect).toHaveValue('180');
    expect(horizonSelect).toHaveValue('60');
  });

  it('handles service initialization errors gracefully', async () => {
    mockService.initialize.mockRejectedValue(new Error('Service initialization failed'));

    render(<InventoryIntelligenceDashboard />);

    await waitFor(() => {
      expect(mockService.initialize).toHaveBeenCalled();
    });

    // Should still render the dashboard structure
    expect(screen.getByText('INVENTORY INTELLIGENCE')).toBeInTheDocument();
  });

  it('shows loading state during analysis', async () => {
    // Make the service calls take longer
    mockService.performABCAnalysis.mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    );

    render(<InventoryIntelligenceDashboard />);

    // Should show loading state
    expect(screen.getByText('Analysiere...')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('Aktualisieren')).toBeInTheDocument();
    }, { timeout: 200 });
  });

  it('displays empty state when no item is selected for forecast', () => {
    render(<InventoryIntelligenceDashboard />);

    // Switch to demand forecast tab
    const forecastTab = screen.getByText('Bedarfsprognose');
    fireEvent.click(forecastTab);

    // Should show empty state
    expect(screen.getByText('Wählen Sie einen Artikel aus, um die Bedarfsprognose zu sehen')).toBeInTheDocument();
  });

  it('handles item selection from ABC chart', async () => {
    render(<InventoryIntelligenceDashboard />);

    await waitFor(() => {
      expect(screen.getByTestId('abc-analysis-chart')).toBeInTheDocument();
    });

    // Click on ABC chart (which triggers item selection)
    const abcChart = screen.getByTestId('abc-analysis-chart');
    fireEvent.click(abcChart);

    await waitFor(() => {
      expect(mockService.forecastDemand).toHaveBeenCalledWith('test-item', 30);
    });
  });
});