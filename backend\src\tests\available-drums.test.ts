/**
 * Tests für Available Drums API
 * 
 * Testet die Datumskonvertierung und API-Funktionalität
 */

import request from 'supertest';
import { describe, it, expect } from '@jest/globals';

// Hilfsfunktion zur Datumskonvertierung (kopiert aus der Route für Tests)
function convertDateForDatabase(dateString: string): string {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear().toString().slice(-2);
  
  return `${day}.${month}.${year}`;
}

describe('Available Drums API', () => {
  describe('Datumskonvertierung', () => {
    it('sollte YYYY-MM-DD zu DD.MM.YY konvertieren', () => {
      expect(convertDateForDatabase('2025-07-31')).toBe('31.07.25');
      expect(convertDateForDatabase('2025-01-01')).toBe('01.01.25');
      expect(convertDateForDatabase('2025-12-25')).toBe('25.12.25');
    });

    it('sollte mit verschiedenen Jahren funktionieren', () => {
      expect(convertDateForDatabase('2024-06-15')).toBe('15.06.24');
      expect(convertDateForDatabase('2026-03-08')).toBe('08.03.26');
    });
  });

  describe('POST /api/available-drums/filter', () => {
    it('sollte gültige Anfragen akzeptieren', async () => {
      const response = await request('http://localhost:3001')
        .post('/api/available-drums/filter')
        .send({
          aufnahmeDatum: '2025-07-31',
          material: '',
          minGesamtbestand: 0
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.count).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(response.body.drums)).toBe(true);
    });

    it('sollte ungültige Datumsformate ablehnen', async () => {
      const response = await request('http://localhost:3001')
        .post('/api/available-drums/filter')
        .send({
          aufnahmeDatum: '31.07.25', // Falsches Format
          material: '',
          minGesamtbestand: 0
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Ungültige Eingabedaten');
    });

    it('sollte mit Material-Filter funktionieren', async () => {
      const response = await request('http://localhost:3001')
        .post('/api/available-drums/filter')
        .send({
          aufnahmeDatum: '2025-07-31',
          material: '00100004',
          minGesamtbestand: 0
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });
});