"use strict";
/**
 * Performance Monitoring Routes
 *
 * Provides endpoints for accessing performance metrics and optimization data
 * for the AI chatbot database integration.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const performance_monitoring_service_1 = __importDefault(require("../services/performance-monitoring.service"));
const data_cache_service_1 = __importDefault(require("../services/data-cache.service"));
const router = (0, express_1.Router)();
/**
 * Get comprehensive performance statistics
 */
router.get('/stats', async (req, res) => {
    try {
        const timeRange = req.query.timeRange;
        let dateRange;
        if (timeRange) {
            const now = new Date();
            switch (timeRange) {
                case '5min':
                    dateRange = {
                        start: new Date(now.getTime() - 5 * 60 * 1000),
                        end: now
                    };
                    break;
                case '1hour':
                    dateRange = {
                        start: new Date(now.getTime() - 60 * 60 * 1000),
                        end: now
                    };
                    break;
                case '24hours':
                    dateRange = {
                        start: new Date(now.getTime() - 24 * 60 * 60 * 1000),
                        end: now
                    };
                    break;
            }
        }
        const stats = performance_monitoring_service_1.default.getPerformanceStats(dateRange);
        res.json({
            success: true,
            data: stats,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error getting performance stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve performance statistics'
        });
    }
});
/**
 * Get query performance by type
 */
router.get('/query/:type', async (req, res) => {
    try {
        const queryType = req.params.type;
        if (!['stoerungen', 'dispatch', 'cutting'].includes(queryType)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid query type. Must be one of: stoerungen, dispatch, cutting'
            });
        }
        const performance = performance_monitoring_service_1.default.getQueryPerformanceByType(queryType);
        res.json({
            success: true,
            data: {
                queryType,
                ...performance
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error(`Error getting ${req.params.type} performance:`, error);
        res.status(500).json({
            success: false,
            error: `Failed to retrieve ${req.params.type} performance metrics`
        });
    }
});
/**
 * Get intent recognition accuracy metrics
 */
router.get('/intent-accuracy', async (req, res) => {
    try {
        const accuracyMetrics = performance_monitoring_service_1.default.getIntentAccuracyMetrics();
        res.json({
            success: true,
            data: accuracyMetrics,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error getting intent accuracy metrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve intent accuracy metrics'
        });
    }
});
/**
 * Get performance alerts
 */
router.get('/alerts', async (req, res) => {
    try {
        const alerts = performance_monitoring_service_1.default.getPerformanceAlerts();
        res.json({
            success: true,
            data: {
                alerts,
                count: alerts.length,
                hasErrors: alerts.some(a => a.type === 'error'),
                hasWarnings: alerts.some(a => a.type === 'warning')
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error getting performance alerts:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve performance alerts'
        });
    }
});
/**
 * Get cache statistics
 */
router.get('/cache', async (req, res) => {
    try {
        const cacheStats = data_cache_service_1.default.getStats();
        const frequentPatterns = data_cache_service_1.default.getFrequentPatterns();
        res.json({
            success: true,
            data: {
                stats: cacheStats,
                frequentPatterns,
                recommendations: generateCacheRecommendations(cacheStats, frequentPatterns)
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error getting cache statistics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve cache statistics'
        });
    }
});
/**
 * Optimize cache
 */
router.post('/cache/optimize', async (req, res) => {
    try {
        const optimization = data_cache_service_1.default.optimizeCache();
        res.json({
            success: true,
            data: optimization,
            message: `Cache optimized: ${optimization.evicted} entries removed, ${optimization.optimizations.length} optimizations applied`,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error optimizing cache:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to optimize cache'
        });
    }
});
/**
 * Clear cache
 */
router.delete('/cache', async (req, res) => {
    try {
        const tags = req.query.tags;
        const dataType = req.query.dataType;
        let clearedCount = 0;
        if (tags) {
            const tagArray = tags.split(',').map(t => t.trim());
            clearedCount = data_cache_service_1.default.invalidateByTags(tagArray);
        }
        else if (dataType && ['stoerungen', 'dispatch', 'cutting'].includes(dataType)) {
            clearedCount = data_cache_service_1.default.invalidateByDataType(dataType);
        }
        else {
            data_cache_service_1.default.clear();
            clearedCount = -1; // Indicates full clear
        }
        res.json({
            success: true,
            data: {
                clearedCount,
                action: clearedCount === -1 ? 'full_clear' : 'selective_clear'
            },
            message: clearedCount === -1
                ? 'Cache completely cleared'
                : `${clearedCount} cache entries cleared`,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error clearing cache:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to clear cache'
        });
    }
});
/**
 * Export performance metrics
 */
router.get('/export', async (req, res) => {
    try {
        const format = req.query.format || 'json';
        if (!['json', 'csv'].includes(format)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid format. Must be json or csv'
            });
        }
        const exportData = performance_monitoring_service_1.default.exportMetrics(format);
        if (format === 'csv') {
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', `attachment; filename=performance-metrics-${Date.now()}.csv`);
            res.send(exportData);
        }
        else {
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Content-Disposition', `attachment; filename=performance-metrics-${Date.now()}.json`);
            res.send(exportData);
        }
    }
    catch (error) {
        console.error('Error exporting performance metrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to export performance metrics'
        });
    }
});
/**
 * Reset performance metrics (for testing/debugging)
 */
router.post('/reset', async (req, res) => {
    try {
        performance_monitoring_service_1.default.clearMetrics();
        res.json({
            success: true,
            message: 'Performance metrics reset successfully',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Error resetting performance metrics:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to reset performance metrics'
        });
    }
});
/**
 * Get system health summary
 */
router.get('/health', async (req, res) => {
    try {
        const stats = performance_monitoring_service_1.default.getPerformanceStats();
        const alerts = performance_monitoring_service_1.default.getPerformanceAlerts();
        const cacheStats = data_cache_service_1.default.getStats();
        const health = {
            status: determineHealthStatus(stats, alerts),
            performance: {
                averageResponseTime: stats.averageResponseTime,
                successRate: stats.successRate,
                totalRequests: stats.totalRequests
            },
            cache: {
                hitRate: cacheStats.hitRate,
                totalEntries: cacheStats.totalEntries,
                totalSize: Math.round(cacheStats.totalSize / 1024 / 1024 * 100) / 100 // MB
            },
            alerts: {
                total: alerts.length,
                errors: alerts.filter(a => a.type === 'error').length,
                warnings: alerts.filter(a => a.type === 'warning').length
            },
            timestamp: new Date().toISOString()
        };
        res.json({
            success: true,
            data: health
        });
    }
    catch (error) {
        console.error('Error getting system health:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to retrieve system health'
        });
    }
});
// Helper functions
function generateCacheRecommendations(stats, patterns) {
    const recommendations = [];
    if (stats.hitRate < 0.3) {
        recommendations.push('Cache hit rate is low. Consider increasing cache TTL or reviewing query patterns.');
    }
    if (stats.totalSize > 80 * 1024 * 1024) { // 80MB
        recommendations.push('Cache size is approaching limits. Consider implementing more aggressive eviction policies.');
    }
    if (patterns.length > 0) {
        const lowEfficiencyPatterns = patterns.filter(p => p.cacheEffectiveness < 0.5);
        if (lowEfficiencyPatterns.length > 0) {
            recommendations.push(`${lowEfficiencyPatterns.length} query patterns have low cache effectiveness. Review caching strategy for these patterns.`);
        }
    }
    if (stats.evictionCount > stats.totalEntries * 0.5) {
        recommendations.push('High eviction rate detected. Consider increasing cache size or adjusting TTL values.');
    }
    return recommendations;
}
function determineHealthStatus(stats, alerts) {
    const errorAlerts = alerts.filter(a => a.type === 'error');
    const warningAlerts = alerts.filter(a => a.type === 'warning');
    if (errorAlerts.length > 0 || stats.successRate < 0.8) {
        return 'critical';
    }
    if (warningAlerts.length > 0 || stats.averageResponseTime > 2500) {
        return 'warning';
    }
    return 'healthy';
}
exports.default = router;
