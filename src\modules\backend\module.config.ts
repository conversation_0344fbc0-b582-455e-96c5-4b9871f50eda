import { ModuleConfig } from '@/types/module';

export const backendModuleConfig: ModuleConfig = {
  id: 'backend',
  name: 'backend',
  displayName: 'Backend & Automatisierung',
  description: 'Systemverwaltung, Workflows und Automatisierung',
  icon: 'Settings',
  baseRoute: '/modules/backend',
  requiredRoles: ['Administrator'],
  isEnabled: true,
  pages: [
    {
      id: 'system',
      name: 'System',
      route: '/modules/backend/system',
      component: 'SystemPage'
    },
    {
      id: 'workflows',
      name: 'Workflows',
      route: '/modules/backend/workflows',
      component: 'WorkflowPage'
    },
    {
      id: 'settings',
      name: 'Einstellungen',
      route: '/modules/backend/settings',
      component: 'SettingsPage'
    }
  ]
};