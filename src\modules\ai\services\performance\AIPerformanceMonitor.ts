// Performance monitoring interfaces and types

export interface PerformanceMetric {
    operationName: string;
    startTime: number;
    endTime: number;
    duration: number;
    memoryUsage: number;
    cpuUsage?: number;
    success: boolean;
    errorMessage?: string;
    metadata?: Record<string, any>;
}

export interface PerformanceStats {
    operationName: string;
    totalCalls: number;
    successfulCalls: number;
    failedCalls: number;
    averageDuration: number;
    minDuration: number;
    maxDuration: number;
    p95Duration: number;
    p99Duration: number;
    averageMemoryUsage: number;
    totalMemoryUsage: number;
    errorRate: number;
    throughput: number; // operations per second
}

export interface ResourceUsage {
    timestamp: number;
    memoryUsage: number;
    cpuUsage: number;
    activeOperations: number;
    cacheHitRate: number;
    queueLength: number;
}

export interface PerformanceAlert {
    id: string;
    type: 'high_latency' | 'high_error_rate' | 'memory_leak' | 'cpu_spike';
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    operationName: string;
    threshold: number;
    currentValue: number;
    timestamp: number;
    recommendations: string[];
}

/**
 * Comprehensive performance monitoring for AI services
 */
export class AIPerformanceMonitor {
    private metrics: PerformanceMetric[] = [];
    private resourceHistory: ResourceUsage[] = [];
    private activeOperations = new Map<string, { startTime: number; metadata?: any }>();
    private alerts: PerformanceAlert[] = [];

    private thresholds = {
        highLatency: 5000, // 5 seconds
        highErrorRate: 0.1, // 10%
        memoryLeakThreshold: 100 * 1024 * 1024, // 100MB increase
        cpuSpikeThreshold: 80 // 80% CPU usage
    };

    private maxMetricsHistory = 10000;
    private maxResourceHistory = 1000;

    constructor() {
        // Start resource monitoring
        setInterval(() => this.collectResourceMetrics(), 30000); // Every 30 seconds

        // Cleanup old metrics
        setInterval(() => this.cleanupOldMetrics(), 300000); // Every 5 minutes
    }

    /**
     * Start monitoring an operation
     */
    startOperation(operationId: string, operationName: string, metadata?: any): void {
        this.activeOperations.set(operationId, {
            startTime: Date.now(),
            metadata: { operationName, ...metadata }
        });
    }

    /**
     * End monitoring an operation
     */
    endOperation(operationId: string, success: boolean, errorMessage?: string): PerformanceMetric | null {
        const operation = this.activeOperations.get(operationId);
        if (!operation) {
            return null;
        }

        const endTime = Date.now();
        const duration = endTime - operation.startTime;
        const memoryUsage = this.getCurrentMemoryUsage();

        const metric: PerformanceMetric = {
            operationName: operation.metadata?.operationName || 'unknown',
            startTime: operation.startTime,
            endTime,
            duration,
            memoryUsage,
            success,
            errorMessage,
            metadata: operation.metadata
        };

        this.metrics.push(metric);
        this.activeOperations.delete(operationId);

        // Check for performance alerts
        this.checkPerformanceAlerts(metric);

        return metric;
    }

    /**
     * Record a completed operation
     */
    recordOperation(
        operationName: string,
        duration: number,
        success: boolean,
        memoryUsage?: number,
        errorMessage?: string,
        metadata?: any
    ): void {
        const now = Date.now();
        const metric: PerformanceMetric = {
            operationName,
            startTime: now - duration,
            endTime: now,
            duration,
            memoryUsage: memoryUsage || this.getCurrentMemoryUsage(),
            success,
            errorMessage,
            metadata
        };

        this.metrics.push(metric);
        this.checkPerformanceAlerts(metric);
    }

    /**
     * Get performance statistics for an operation
     */
    getOperationStats(operationName: string, timeWindow?: number): PerformanceStats {
        const cutoff = timeWindow ? Date.now() - timeWindow : 0;
        const operationMetrics = this.metrics.filter(
            m => m.operationName === operationName && m.endTime > cutoff
        );

        if (operationMetrics.length === 0) {
            return {
                operationName,
                totalCalls: 0,
                successfulCalls: 0,
                failedCalls: 0,
                averageDuration: 0,
                minDuration: 0,
                maxDuration: 0,
                p95Duration: 0,
                p99Duration: 0,
                averageMemoryUsage: 0,
                totalMemoryUsage: 0,
                errorRate: 0,
                throughput: 0
            };
        }

        const durations = operationMetrics.map(m => m.duration).sort((a, b) => a - b);
        const successfulCalls = operationMetrics.filter(m => m.success).length;
        const totalDuration = durations.reduce((sum, d) => sum + d, 0);
        const totalMemory = operationMetrics.reduce((sum, m) => sum + m.memoryUsage, 0);

        const timeSpan = Math.max(
            operationMetrics[operationMetrics.length - 1].endTime - operationMetrics[0].startTime,
            1000
        );

        return {
            operationName,
            totalCalls: operationMetrics.length,
            successfulCalls,
            failedCalls: operationMetrics.length - successfulCalls,
            averageDuration: totalDuration / operationMetrics.length,
            minDuration: durations[0],
            maxDuration: durations[durations.length - 1],
            p95Duration: durations[Math.floor(durations.length * 0.95)],
            p99Duration: durations[Math.floor(durations.length * 0.99)],
            averageMemoryUsage: totalMemory / operationMetrics.length,
            totalMemoryUsage: totalMemory,
            errorRate: (operationMetrics.length - successfulCalls) / operationMetrics.length,
            throughput: (operationMetrics.length / timeSpan) * 1000 // ops per second
        };
    }

    /**
     * Get all operation statistics
     */
    getAllOperationStats(timeWindow?: number): PerformanceStats[] {
        const operationNames = [...new Set(this.metrics.map(m => m.operationName))];
        return operationNames.map(name => this.getOperationStats(name, timeWindow));
    }

    /**
     * Get current resource usage
     */
    getCurrentResourceUsage(): ResourceUsage {
        return {
            timestamp: Date.now(),
            memoryUsage: this.getCurrentMemoryUsage(),
            cpuUsage: this.getCurrentCpuUsage(),
            activeOperations: this.activeOperations.size,
            cacheHitRate: 0, // Will be updated by cache service
            queueLength: 0 // Will be updated by queue service
        };
    }

    /**
     * Get resource usage history
     */
    getResourceHistory(timeWindow?: number): ResourceUsage[] {
        const cutoff = timeWindow ? Date.now() - timeWindow : 0;
        return this.resourceHistory.filter(r => r.timestamp > cutoff);
    }

    /**
     * Get performance alerts
     */
    getAlerts(severity?: PerformanceAlert['severity']): PerformanceAlert[] {
        const alerts = severity
            ? this.alerts.filter(a => a.severity === severity)
            : this.alerts;

        return alerts.sort((a, b) => b.timestamp - a.timestamp);
    }

    /**
     * Clear alerts
     */
    clearAlerts(alertIds?: string[]): void {
        if (alertIds) {
            this.alerts = this.alerts.filter(a => !alertIds.includes(a.id));
        } else {
            this.alerts = [];
        }
    }

    /**
     * Get performance recommendations
     */
    getOptimizationRecommendations(): string[] {
        const recommendations: string[] = [];
        const stats = this.getAllOperationStats(3600000); // Last hour

        for (const stat of stats) {
            if (stat.errorRate > 0.05) {
                recommendations.push(
                    `Operation "${stat.operationName}" has high error rate (${(stat.errorRate * 100).toFixed(1)}%). Consider implementing retry logic or improving error handling.`
                );
            }

            if (stat.averageDuration > 3000) {
                recommendations.push(
                    `Operation "${stat.operationName}" has high average latency (${stat.averageDuration}ms). Consider caching results or optimizing the algorithm.`
                );
            }

            if (stat.p95Duration > stat.averageDuration * 3) {
                recommendations.push(
                    `Operation "${stat.operationName}" has inconsistent performance. P95 latency is ${stat.p95Duration}ms vs average ${stat.averageDuration}ms.`
                );
            }

            if (stat.averageMemoryUsage > 50 * 1024 * 1024) { // 50MB
                recommendations.push(
                    `Operation "${stat.operationName}" uses high memory (${(stat.averageMemoryUsage / 1024 / 1024).toFixed(1)}MB). Consider optimizing data structures or implementing streaming.`
                );
            }
        }

        return recommendations;
    }

    private collectResourceMetrics(): void {
        const usage = this.getCurrentResourceUsage();
        this.resourceHistory.push(usage);

        // Keep only recent history
        if (this.resourceHistory.length > this.maxResourceHistory) {
            this.resourceHistory = this.resourceHistory.slice(-this.maxResourceHistory);
        }

        // Check for resource-based alerts
        this.checkResourceAlerts(usage);
    }

    private checkPerformanceAlerts(metric: PerformanceMetric): void {
        // High latency alert
        if (metric.duration > this.thresholds.highLatency) {
            this.createAlert({
                type: 'high_latency',
                severity: metric.duration > this.thresholds.highLatency * 2 ? 'critical' : 'high',
                message: `Operation "${metric.operationName}" took ${metric.duration}ms to complete`,
                operationName: metric.operationName,
                threshold: this.thresholds.highLatency,
                currentValue: metric.duration,
                recommendations: [
                    'Consider implementing caching for this operation',
                    'Review algorithm efficiency',
                    'Check for database query optimization opportunities'
                ]
            });
        }

        // Check error rate for recent operations
        const recentMetrics = this.metrics
            .filter(m => m.operationName === metric.operationName && m.endTime > Date.now() - 300000) // Last 5 minutes
            .slice(-20); // Last 20 operations

        if (recentMetrics.length >= 10) {
            const errorRate = recentMetrics.filter(m => !m.success).length / recentMetrics.length;
            if (errorRate > this.thresholds.highErrorRate) {
                this.createAlert({
                    type: 'high_error_rate',
                    severity: errorRate > 0.5 ? 'critical' : 'high',
                    message: `Operation "${metric.operationName}" has high error rate: ${(errorRate * 100).toFixed(1)}%`,
                    operationName: metric.operationName,
                    threshold: this.thresholds.highErrorRate,
                    currentValue: errorRate,
                    recommendations: [
                        'Review error logs for common failure patterns',
                        'Implement retry logic with exponential backoff',
                        'Add input validation and error handling'
                    ]
                });
            }
        }
    }

    private checkResourceAlerts(usage: ResourceUsage): void {
        // Memory leak detection
        if (this.resourceHistory.length >= 10) {
            const oldUsage = this.resourceHistory[this.resourceHistory.length - 10];
            const memoryIncrease = usage.memoryUsage - oldUsage.memoryUsage;

            if (memoryIncrease > this.thresholds.memoryLeakThreshold) {
                this.createAlert({
                    type: 'memory_leak',
                    severity: 'high',
                    message: `Memory usage increased by ${(memoryIncrease / 1024 / 1024).toFixed(1)}MB in the last 5 minutes`,
                    operationName: 'system',
                    threshold: this.thresholds.memoryLeakThreshold,
                    currentValue: memoryIncrease,
                    recommendations: [
                        'Check for memory leaks in recent operations',
                        'Review cache size and eviction policies',
                        'Monitor object lifecycle and garbage collection'
                    ]
                });
            }
        }

        // CPU spike detection
        if (usage.cpuUsage > this.thresholds.cpuSpikeThreshold) {
            this.createAlert({
                type: 'cpu_spike',
                severity: usage.cpuUsage > 95 ? 'critical' : 'medium',
                message: `High CPU usage detected: ${usage.cpuUsage.toFixed(1)}%`,
                operationName: 'system',
                threshold: this.thresholds.cpuSpikeThreshold,
                currentValue: usage.cpuUsage,
                recommendations: [
                    'Review currently running operations',
                    'Consider implementing operation queuing',
                    'Check for infinite loops or inefficient algorithms'
                ]
            });
        }
    }

    private createAlert(alertData: Omit<PerformanceAlert, 'id' | 'timestamp'>): void {
        const alert: PerformanceAlert = {
            ...alertData,
            id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: Date.now()
        };

        this.alerts.push(alert);

        // Keep only recent alerts
        if (this.alerts.length > 100) {
            this.alerts = this.alerts.slice(-100);
        }
    }

    private getCurrentMemoryUsage(): number {
        if (typeof process !== 'undefined' && process.memoryUsage) {
            return process.memoryUsage().heapUsed;
        }
        return 0;
    }

    private getCurrentCpuUsage(): number {
        // Simplified CPU usage - in real implementation, use proper CPU monitoring
        return Math.random() * 20 + 10; // Simulate 10-30% usage
    }

    private cleanupOldMetrics(): void {
        const cutoff = Date.now() - 3600000; // Keep last hour
        this.metrics = this.metrics.filter(m => m.endTime > cutoff);

        if (this.metrics.length > this.maxMetricsHistory) {
            this.metrics = this.metrics.slice(-this.maxMetricsHistory);
        }

        // Clean up old alerts
        const alertCutoff = Date.now() - 86400000; // Keep last 24 hours
        this.alerts = this.alerts.filter(a => a.timestamp > alertCutoff);
    }
}