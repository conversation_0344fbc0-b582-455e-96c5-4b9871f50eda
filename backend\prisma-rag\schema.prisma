// RAG Knowledge Database Schema
// Separate schema for RAG functionality according to Prisma best practices
// Updated: Correct output path for separate database client

generator client {
  provider = "prisma-client-js"
  output = "../node_modules/@prisma-rag/client"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL_RAG")
}

// Knowledge Base Model - Container for different knowledge domains
model KnowledgeBase {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  documents   Document[]
  settings    RAGSettings[]
  
  @@map("knowledge_bases")
}

// Document Model - Individual documents in the knowledge base
model Document {
  id              String   @id @default(cuid())
  title           String
  content         String
  source          String?
  metadata        String?  // JSON string for additional metadata
  embedding       String?  // Vector embedding as JSON string
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relations
  knowledgeBaseId String
  knowledgeBase   KnowledgeBase @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  
  @@map("documents")
}

// RAG Settings Model - Configuration for RAG functionality
model RAGSettings {
  id                    String   @id @default(cuid())
  name                  String
  description           String?
  isActive              Boolean  @default(true)
  
  // Configuration as JSON strings
  vectorDatabaseConfig  String   // JSON: dimensions, similarityThreshold, maxResults, etc.
  securityConfig        String   // JSON: rateLimit, validation, audit settings
  servicesConfig        String   // JSON: embedding and RAG service settings
  
  version               String   @default("1.0.0")
  userId                String?  // Optional user association
  
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  
  // Relations
  knowledgeBaseId       String
  knowledgeBase         KnowledgeBase @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  
  @@map("rag_settings")
}