/**
 * Predictive Analytics Dashboard
 * 
 * Main dashboard for predictive analytics featuring KPI forecasts,
 * alert management, and capacity planning visualizations.
 */

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { AlertTriangle, TrendingUp, TrendingDown, Activity, Bell, Settings } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { predictiveAnalyticsService } from '@/services/predictive-analytics.service';
import { KPIForecastChart } from '@/components/charts/KPIForecastChart';
import { AlertManagementPanel } from '@/components/predictive-analytics/AlertManagementPanel';
import { CapacityPlanningChart } from '@/components/charts/CapacityPlanningChart';
import { PredictiveAlertsPanel } from '@/components/predictive-analytics/PredictiveAlertsPanel';
import { PerformancePatternsChart } from '@/components/charts/PerformancePatternsChart';
import type { 
  KPIMonitoringResult, 
  KPIForecast, 
  Alert as AlertType,
  PredictiveAlert,
  CapacityForecast 
} from '@/types/predictive-analytics';

/**
 * Predictive Analytics Dashboard Component
 */
export function PredictiveAnalyticsDashboard() {
  const [selectedDepartment, setSelectedDepartment] = useState<string>('dispatch');
  const [selectedKPIs, setSelectedKPIs] = useState<string[]>(['service_level', 'picking_efficiency']);
  const [alertsVisible, setAlertsVisible] = useState(true);

  // Query for KPI monitoring data
  const { 
    data: kpiData, 
    isLoading: kpiLoading, 
    error: kpiError 
  } = useQuery({
    queryKey: ['kpi-monitoring', selectedKPIs],
    queryFn: () => predictiveAnalyticsService.monitorKPIs(selectedKPIs),
    refetchInterval: 30000, // Refresh every 30 seconds
    enabled: selectedKPIs.length > 0
  });

  // Query for KPI forecasts
  const { 
    data: forecastData, 
    isLoading: forecastLoading 
  } = useQuery({
    queryKey: ['kpi-forecasts', selectedKPIs],
    queryFn: async () => {
      const forecasts = await Promise.all(
        selectedKPIs.map(kpiId => 
          predictiveAnalyticsService.predictKPITrends(kpiId, 24)
        )
      );
      return forecasts;
    },
    enabled: selectedKPIs.length > 0
  });

  // Query for active alerts
  const { 
    data: alerts, 
    isLoading: alertsLoading 
  } = useQuery({
    queryKey: ['active-alerts'],
    queryFn: () => predictiveAnalyticsService.evaluateAlertConditions(),
    refetchInterval: 60000 // Refresh every minute
  });

  // Query for predictive alerts
  const { 
    data: predictiveAlerts, 
    isLoading: predictiveAlertsLoading 
  } = useQuery({
    queryKey: ['predictive-alerts'],
    queryFn: () => predictiveAnalyticsService.generatePredictiveAlerts(),
    refetchInterval: 300000 // Refresh every 5 minutes
  });

  // Query for capacity forecast
  const { 
    data: capacityForecast, 
    isLoading: capacityLoading 
  } = useQuery({
    queryKey: ['capacity-forecast', selectedDepartment],
    queryFn: () => predictiveAnalyticsService.forecastCapacityNeeds(selectedDepartment),
    enabled: !!selectedDepartment
  });

  // Query for performance patterns
  const { 
    data: performancePatterns, 
    isLoading: patternsLoading 
  } = useQuery({
    queryKey: ['performance-patterns', selectedDepartment],
    queryFn: () => predictiveAnalyticsService.analyzePerformancePatterns(selectedDepartment),
    enabled: !!selectedDepartment
  });

  // Calculate summary statistics
  const criticalAlertsCount = alerts?.filter(alert => alert.severity === 'critical').length || 0;
  const highPriorityPredictiveAlerts = predictiveAlerts?.filter(alert => alert.severity === 'high' || alert.severity === 'critical').length || 0;
  const totalKPIsMonitored = kpiData?.length || 0;
  const kpisWithIssues = kpiData?.filter(kpi => kpi.status !== 'normal').length || 0;

  return (
    <div className="min-h-screen bg-background p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Predictive Analytics</h1>
          <p className="text-muted-foreground mt-1">
            KPI-Prognosen, Anomalieerkennung und Kapazitätsplanung
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAlertsVisible(!alertsVisible)}
            className="flex items-center gap-2"
          >
            <Bell className="h-4 w-4" />
            Benachrichtigungen
            {(criticalAlertsCount + highPriorityPredictiveAlerts) > 0 && (
              <Badge variant="destructive" className="ml-1">
                {criticalAlertsCount + highPriorityPredictiveAlerts}
              </Badge>
            )}
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Einstellungen
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
        <Card className="bg-neo-blue text-white">
          <CardHeader className="border-b-3 border-black pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Überwachte KPIs
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-xl font-bold">{totalKPIsMonitored}</div>
            <p className="text-xs opacity-90">
              {kpisWithIssues} mit Problemen
            </p>
          </CardContent>
        </Card>

        <Card className="bg-neo-red text-white">
          <CardHeader className="border-b-3 border-black pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              Kritische Alarme
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-xl font-bold">{criticalAlertsCount}</div>
            <p className="text-xs opacity-90">
              Sofortige Aufmerksamkeit erforderlich
            </p>
          </CardContent>
        </Card>

        <Card className="bg-neo-yellow text-black">
          <CardHeader className="border-b-3 border-black pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Prognose-Alarme
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-xl font-bold">{highPriorityPredictiveAlerts}</div>
            <p className="text-xs opacity-90">
              Hohe Priorität
            </p>
          </CardContent>
        </Card>

        <Card className="bg-neo-green text-white">
          <CardHeader className="border-b-3 border-black pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <TrendingDown className="h-4 w-4" />
              Kapazitätsauslastung
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="text-xl font-bold">
              {capacityForecast ? `${Math.round(capacityForecast.current_utilization * 100)}%` : '--'}
            </div>
            <p className="text-xs opacity-90">
              Aktuelle Auslastung
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Alert Banner */}
      {alertsVisible && (criticalAlertsCount > 0 || highPriorityPredictiveAlerts > 0) && (
        <Alert className="border-red-500 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Achtung:</strong> {criticalAlertsCount} kritische Alarme und {highPriorityPredictiveAlerts} hochpriorisierte Prognose-Alarme erfordern Ihre Aufmerksamkeit.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="forecasts" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="forecasts">KPI-Prognosen</TabsTrigger>
          <TabsTrigger value="alerts">Alarm-Management</TabsTrigger>
          <TabsTrigger value="capacity">Kapazitätsplanung</TabsTrigger>
          <TabsTrigger value="patterns">Performance-Muster</TabsTrigger>
        </TabsList>

        {/* KPI Forecasts Tab */}
        <TabsContent value="forecasts" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">KPI-Prognosen</h2>
            <div className="flex items-center gap-2">
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-1 border rounded-md text-sm"
              >
                <option value="dispatch">Versand</option>
                <option value="cutting">Ablängerei</option>
                <option value="incoming-goods">Wareneingang</option>
              </select>
            </div>
          </div>

          {forecastLoading ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {[1, 2].map(i => (
                <Card key={i}>
                  <CardHeader>
                    <Skeleton className="h-4 w-32" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-64 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : forecastData && forecastData.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
              {forecastData.map((forecast) => (
                <KPIForecastChart
                  key={forecast.kpi_id}
                  forecast={forecast}
                  className="h-64"
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-48">
                <p className="text-muted-foreground">
                  Keine Prognosedaten verfügbar. Wählen Sie KPIs zur Überwachung aus.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Alert Management Tab */}
        <TabsContent value="alerts" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
            <AlertManagementPanel
              alerts={alerts || []}
              loading={alertsLoading}
              onAlertAction={(alertId, action) => {
                console.log(`Alert ${alertId} action: ${action}`);
                // Handle alert actions (acknowledge, resolve, etc.)
              }}
            />
            <PredictiveAlertsPanel
              alerts={predictiveAlerts || []}
              loading={predictiveAlertsLoading}
              onAlertAction={(alertId, action) => {
                console.log(`Predictive alert ${alertId} action: ${action}`);
                // Handle predictive alert actions
              }}
            />
          </div>
        </TabsContent>

        {/* Capacity Planning Tab */}
        <TabsContent value="capacity" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Kapazitätsplanung</h2>
            <div className="flex items-center gap-2">
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-1 border rounded-md text-sm"
              >
                <option value="dispatch">Versand</option>
                <option value="cutting">Ablängerei</option>
                <option value="incoming-goods">Wareneingang</option>
              </select>
            </div>
          </div>

          {capacityLoading ? (
            <Card>
              <CardHeader>
                <Skeleton className="h-4 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-80 w-full" />
              </CardContent>
            </Card>
          ) : capacityForecast ? (
            <CapacityPlanningChart
              forecast={capacityForecast}
              className="h-80"
            />
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-64">
                <p className="text-muted-foreground">
                  Keine Kapazitätsdaten für {selectedDepartment} verfügbar.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Performance Patterns Tab */}
        <TabsContent value="patterns" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Performance-Muster</h2>
            <div className="flex items-center gap-2">
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-1 border rounded-md text-sm"
              >
                <option value="dispatch">Versand</option>
                <option value="cutting">Ablängerei</option>
                <option value="incoming-goods">Wareneingang</option>
              </select>
            </div>
          </div>

          {patternsLoading ? (
            <Card>
              <CardHeader>
                <Skeleton className="h-4 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-80 w-full" />
              </CardContent>
            </Card>
          ) : performancePatterns && performancePatterns.length > 0 ? (
            <PerformancePatternsChart
              patterns={performancePatterns}
              className="h-80"
            />
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-64">
                <p className="text-muted-foreground">
                  Keine Performance-Muster für {selectedDepartment} erkannt.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default PredictiveAnalyticsDashboard;