{"dispatchArea": "Dispatch-Bereich", "serviceLevel": "Servicelevel", "serviceLevelDescription": "<PERSON><PERSON><PERSON> Servicelevel (Servicegrad) als Liniendiagramm über einen Zeitraum.", "dailyPerformance": "Tägliche Performance", "dailyPerformanceDescription": "Zeigt die tägliche Performance als Balkendiagramm.", "picking": "Picking", "pickingDescription": "Zeigt die Kommissionierungsdaten als Balkendiagramm.", "returns": "Retouren", "returnsDescription": "Zeigt die Retourendaten als Kreisdiagramm.", "deliveryPositions": "Lieferpositionen", "deliveryPositionsDescription": "Zeigt die Lieferpositionsdaten als Balkendiagramm.", "date": "Datum", "servicegradPercentage": "Servicegrad (%)", "minimum": "Minimum", "average": "Durchschnitt", "maximum": "Maximum", "updated": "<PERSON>ktual<PERSON><PERSON>", "loading": "Wird geladen", "noData": "<PERSON><PERSON> vorhanden", "deliveredLup": "Ausgelieferte LuP", "outstanding": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ratio": "Verhältnis", "total": "Gesamt", "charts": "Diagramme", "statistics": "Statistiken", "kpiDashboard": "KPI-Dashboard", "kpiDashboardDescription": "Übersicht über die wichtigsten Leistungskennzahlen", "productivityMetrics": "Produktivität", "productivityMetricsDescription": "Kennzahlen zur Messung der Produktivität", "logisticsEfficiency": "Logistik", "logisticsEfficiencyDescription": "Kennzahlen zur Messung der Logistik-Effizienz", "inventoryCapacity": "Lagerbestand", "inventoryCapacityDescription": "Kennzahlen zum Lagerbestand und zur Kapazitätsauslastung", "qualityManagement": "Qualität", "qualityManagementDescription": "Kennzahlen zum Qualitätsmanagement", "correlationAnalysis": "Korrelationsanalyse", "correlationAnalysisDescription": "Analyse der Zusammenhänge zwischen verschiedenen Kennzahlen", "tonnagePerHour": "Tonnagen pro Stunde", "tonnagePerHourDescription": "Produzierte Tonnagen pro Mitarbeiterstunde", "positionsPerHour": "Positionen pro Stunde", "positionsPerHourDescription": "Ausgelieferte Positionen pro Mitarbeiterstunde", "comparedToPreviousDay": "Im Vergleich zum Vortag", "directLoadingRatio": "Direktverladung", "directLoadingRatioDescription": "Anteil der Direktverladung am Gesamtumschlag", "avgWeightPerColli": "Gewicht pro Colli", "avgWeightPerColliDescription": "Durchschnittliches Gewicht pro Colli", "elephantRatio": "Elefanten", "elephantRatioDescription": "Anteil der Elefanten am Gesamtumschlag", "atrlArilRatio": "ATRL/ARIL", "atrlArilRatioDescription": "Verhältnis zwischen ATRL und ARIL", "arilFillLevel": "Füllgrad ARIL", "arilFillLevelDescription": "Aktueller Füllgrad des ARIL", "criticalLevelIndicator": "Status", "criticalLevelDescription": "Warnung bei kritischem Füllgrad (>85%)", "basedOnCurrentFillLevel": "Basierend auf aktuellem Füllgrad", "critical": "<PERSON><PERSON><PERSON>", "normal": "Normal", "qmAcceptanceRate": "Akzeptanzrate", "qmAcceptanceRateDescription": "Anteil der angenommenen QM-Fälle", "qmProcessingRate": "Bearbeitungsrate", "qmProcessingRateDescription": "Anteil der bearbeiteten QM-Fälle", "serviceLevelBacklogCorrelation": "Servicegrad vs. Rückstände", "serviceLevelBacklogCorrelationDesc": "Korrelation zwischen Servicegrad und Rückständen", "productivityQualityCorrelation": "Produktivität vs. Qualität", "productivityQualityCorrelationDesc": "Korrelation zwischen Produktivität und Qualität", "fillLevelServiceCorrelation": "Füllgrad vs. Servicegrad", "fillLevelServiceCorrelationDesc": "Korrelation zwischen Füllgrad ARIL und Servicegrad", "strong": "<PERSON><PERSON>", "moderate": "<PERSON><PERSON><PERSON>", "weak": "<PERSON><PERSON><PERSON>", "negativeCorrelation": "negative Korrelation", "positiveCorrelation": "positive Korrelation", "performanceIndex": "Leistungsindex", "performanceIndexDescription": "Gewichteter Gesamtindex der Leistung", "targetAchievement": "Zielerreichung", "targetAchievementDescription": "Durchschnittliche Erreichung der Zielwerte", "overallStatus": "Status", "overallStatusDescription": "Ampelsystem für den Gesamtstatus", "basedOnWeightedAverage": "Gewichteter Durchschnitt", "comparedToTargets": "Vergleich zu Zielwerten", "basedOnTargetAchievement": "Basierend auf Zielerreichung", "green": "<PERSON><PERSON><PERSON><PERSON>", "yellow": "<PERSON><PERSON><PERSON>", "red": "Rot", "overview": "Übersicht", "shopfloorOverview": "Shopfloor Management Übersicht", "performanceOverview": "Leistungsübersicht", "performanceOverviewDescription": "Wichtige KPIs aller Abteilungen auf einen Blick", "lastUpdated": "Letzte Aktualisierung", "showDashboard": "Dashboard anzeigen", "onTrack": "Im Plan", "attention": "Achtung", "criticalStatus": "<PERSON><PERSON><PERSON>", "predictiveAnalytics": "Predictive Analytics", "predictiveAnalyticsDescription": "KPI-Prognosen, Anomalieerkennung und Kapazitätsplanung", "kpiForecasts": "KPI-Prognosen", "alertManagement": "Alarm-Management", "capacityPlanning": "Kapazitätsplanung", "performancePatterns": "Performance-Muster", "monitoredKpis": "Überwachte KPIs", "criticalAlerts": "Kritische Alarme", "forecastAlerts": "Prognose-Alarme", "capacityUtilization": "Kapazitätsauslastung", "immediateAttentionRequired": "Sofortige Aufmerksamkeit erforderlich", "highPriority": "Hohe Priorität", "currentUtilization": "Aktuelle Auslastung", "withIssues": "mit Problemen", "notifications": "Benachrichtigungen", "settings": "Einstellungen", "criticalAlertsAndForecastAlerts": "kritische Alarme und hochpriorisierte Prognose-Alarme erfordern Ihre Aufmerksamkeit", "noForecastDataAvailable": "<PERSON><PERSON> Prognosedaten verfügbar. Wählen Sie KPIs zur Überwachung aus.", "noCapacityDataAvailable": "<PERSON><PERSON>ätsdaten verfügbar", "noPerformancePatternsDetected": "<PERSON>ine <PERSON>-<PERSON><PERSON>", "allKpisInNormalRange": "Alle KPIs im normalen Bereich", "confidence": "Konfidenz", "current": "Aktuell", "forecast": "Prognose", "model": "<PERSON><PERSON>", "horizon": "Horizont", "trend": "Trend", "generated": "<PERSON><PERSON><PERSON>", "rising": "Steigend", "falling": "Fallend", "stable": "Stabil", "lowConfidence": "<PERSON><PERSON><PERSON><PERSON>z - Prognose mit Vorsicht interpretieren", "activeAlerts": "Aktive Alarme", "noActiveAlerts": "Keine aktiven Alarme", "high": "Hoch", "medium": "<PERSON><PERSON><PERSON>", "low": "<PERSON><PERSON><PERSON>", "active": "Aktiv", "acknowledged": "Bestätigt", "resolved": "<PERSON><PERSON><PERSON><PERSON>", "acknowledge": "Bestätigen", "resolve": "<PERSON><PERSON><PERSON>", "dismiss": "Verwerfen", "alertDetails": "Alarm Details", "title": "Titel", "severity": "Schweregrad", "kpi": "KPI", "currentValue": "Aktueller Wert", "message": "Nachricht", "triggeredAt": "Ausgelöst am", "noteOptional": "Notiz (optional)", "addActionNote": "Notiz zur Aktion hinzufügen...", "predictiveAlertsPanel": "Prognose-Alarme", "noPredictiveAlerts": "<PERSON><PERSON>Al<PERSON>e", "minProbability": "<PERSON><PERSON>", "probability": "Wahrscheinlichkeit", "predictedIssue": "Prognostiziertes Problem", "timeToImpact": "Zeit bis Auswirkung", "alertType": "Alarm-Typ", "predictedValue": "Prognostizierter Wert", "recommendations": "Empfehlungen", "generatedAt": "Generiert am", "expiresAt": "Läuft ab am", "snooze": "Sc<PERSON><PERSON>mer<PERSON>", "thresholdBreach": "Schwellwert-Verletzung", "anomalyPrediction": "Anomalie-Prognose", "capacityLimit": "Kapazitätsgrenze", "capacityGaps": "Kapazitätslücken", "noCapacityGapsForecasted": "<PERSON><PERSON>ätslücken prognostiziert", "capacityGap": "Kapazitätslücke", "timeframe": "Zeitraum", "gap": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON><PERSON>", "available": "Verfügbar", "units": "Einheiten", "noRecommendationsAvailable": "<PERSON><PERSON> Empfehlungen verfügbar", "increaseCapacity": "Kapazität erhöhen", "redistributeLoad": "Last umverteilen", "scheduleAdjustment": "Zeitplan anpassen", "cost": "<PERSON><PERSON>", "benefit": "<PERSON><PERSON><PERSON>", "implementation": "Umsetzung", "days": "Tage", "currentCapacity": "Aktuelle Kapazität", "capacity": "Kapazität", "detectedPatterns": "<PERSON><PERSON><PERSON><PERSON>", "averageStrength": "Durchschnittliche Stärke", "patternTypes": "Muster-<PERSON>n", "frequencyDistribution": "Frequenz-Verteilung", "patternStrengthDistribution": "Muster-Stärke Verteilung", "noPatternsFound": "<PERSON><PERSON> gefunden", "adjustFilters": "Passen Sie die Filter an", "pattern": "Muster", "seasonal": "Saisonal", "cyclical": "<PERSON><PERSON><PERSON><PERSON>", "irregular": "Unregelm<PERSON><PERSON><PERSON>", "strength": "Stärke", "frequency": "<PERSON><PERSON><PERSON><PERSON>", "hourly": "<PERSON><PERSON><PERSON><PERSON>", "daily": "Tä<PERSON><PERSON>", "weekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "affectedKpis": "Betroffene KPIs", "detectedAt": "Erkannt am", "patternId": "Muster-ID", "more": "weitere", "allTypes": "Alle Typen", "allFrequencies": "Alle Frequenzen", "dispatch": "<PERSON>ers<PERSON>", "cutting": "Ablängerei", "incomingGoods": "Wareneingang", "personnel": "Personal", "equipment": "Ausrüstung", "storage": "Lager", "processing": "Verarbeitung"}