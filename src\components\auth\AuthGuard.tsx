import React, { useEffect, useRef } from 'react';
import { useNavigate, useLocation } from '@tanstack/react-router';
import { useAuthContext } from '@/contexts/AuthContext';

interface AuthGuardProps {
  children: React.ReactNode;
}

/**
 * AuthGuard Component
 * 
 * Behandelt die initiale Authentifizierungsprüfung beim App-Start.
 * Leitet Benutzer basierend auf ihrem Authentifizierungsstatus weiter:
 * - Nicht authentifiziert: Weiterleitung zu /login
 * - Authentifiziert auf Login-Seite: Weiterleitung zu /dashboard
 * - Authentifiziert auf anderen Seiten: Normale Anzeige
 * 
 * Requirements: 1.1, 1.3, 2.2
 * - WHEN die Anwendung gestartet wird AND kein gültiger JWT-Token im localStorage vorhanden ist 
 *   THEN soll die Anwendung automatisch zur Login-Seite (/login) weiterleiten
 * - WHEN ein Benutzer die Login-Seite aufruft AND bereits authentifiziert ist 
 *   THEN soll er zum Dashboard (/) weitergeleitet werden
 * - WHEN ein authentifizierter Benutzer die Anwendung neu startet AND ein gültiger Token vorhanden ist 
 *   THEN soll er direkt zum Dashboard weitergeleitet werden
 */
export const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthContext();
  const navigate = useNavigate();
  const location = useLocation();
  const normalizedStartRef = useRef(false);

  useEffect(() => {
    // Warte bis der Authentifizierungsstatus geladen ist
    if (isLoading) return;

    const currentPath = location.pathname;
    const isOnLoginPage = currentPath === '/login';
    const isOnRegisterPage = currentPath === '/register';
    const isOnPublicPage = isOnLoginPage || isOnRegisterPage;

    console.log('🛡️ AuthGuard - Authentifizierungsstatus:', {
      isAuthenticated,
      currentPath,
      isOnPublicPage
    });

    // 1) Initiale Normalisierung der Startseite: einmalig deterministisch weiterleiten
    if (!normalizedStartRef.current) {
      normalizedStartRef.current = true;
      if (isAuthenticated) {
        // Immer auf die User-Landing-Page starten
        navigate({ to: '/' });
        return;
      } else {
        // Nicht authentifiziert -> zur Login-Seite, sofern nicht schon dort
        if (!isOnPublicPage) {
          navigate({ to: '/login' });
          return;
        }
      }
    }

    // 2) Reguläres Guard-Verhalten
    if (isAuthenticated) {
      // Benutzer ist authentifiziert
      if (isOnLoginPage || isOnRegisterPage) {
        // Weiterleitung von Login/Register-Seite zur Startseite
        console.log('✅ Authentifiziert auf öffentlicher Seite - Weiterleitung zur Startseite');
        navigate({ to: '/' });
      }
      // Sonst: Benutzer bleibt auf der aktuellen geschützten Seite
    } else {
      // Benutzer ist nicht authentifiziert
      if (!isOnPublicPage) {
        // Weiterleitung zu Login-Seite wenn auf geschützter Seite
        console.log('🔒 Nicht authentifiziert auf geschützter Seite - Weiterleitung zur Login-Seite');
        navigate({ to: '/login' });
      }
      // Sonst: Benutzer bleibt auf Login/Register-Seite
    }
  }, [isAuthenticated, isLoading, navigate, location.pathname]);

  // Zeige Loading-Spinner während der initialen Authentifizierungsprüfung
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="bg-white p-8 rounded-lg shadow-lg border-4 border-blue-600">
          <div className="flex items-center space-x-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-4 border-blue-600"></div>
            <div>
              <h2 className="text-xl font-bold text-gray-800">JOZI1 Lapp Dashboard</h2>
              <p className="text-gray-600">Authentifizierung wird geprüft...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Rendere die Anwendung nach der Authentifizierungsprüfung
  return <>{children}</>;
};