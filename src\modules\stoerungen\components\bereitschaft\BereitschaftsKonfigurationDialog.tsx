import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Settings, Users, Calendar, BarChart3, X, ShieldAlert } from 'lucide-react';
import SmoothTab from "@/components/Animation/kokonutui/smooth-tab";
// Fach-Views
import { BereitschaftsPersonenVerwaltung } from './BereitschaftsPersonenVerwaltung';
import { BereitschaftsWochenplanung } from './BereitschaftsWochenplanung';
import { BereitschaftsKonfigurationComponent } from './BereitschaftsKonfiguration';
import { BereitschaftsEskalationsmatrix } from './BereitschaftsEskalationsmatrix';

interface BereitschaftsKonfigurationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfigurationChange?: () => void;
}

export const BereitschaftsKonfigurationDialog: React.FC<BereitschaftsKonfigurationDialogProps> = ({
  open,
  onOpenChange,
  onConfigurationChange
}) => {
  const [activeTab, setActiveTab] = useState<'personen' | 'planung' | 'konfiguration' | 'eskalation' | 'statistiken'>('personen');

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50"
        onClick={() => onOpenChange(false)}
      />
      
      {/* Dialog Content */}
      <div className="relative bg-white rounded-lg shadow-lg max-w-5xl min-h-[950px] w-full mx-4 overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between py-2 px-6 border-b bg-gray-100">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Settings className="h-5 w-5 text-blue-600" />
            Bereitschaftsplan-Verwaltung
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Smooth Tabs mit Störungsmanagement-Bereichen */}
        <div className="flex-1 flex flex-col overflow-hidden p-6 space-y-6">
          <div className="flex justify-center w-full">
            <div className="w-full max-w-3xl">
              <SmoothTab
                items={[
                  {
                    id: 'personen',
                    title: "Personen",
                    icon: Users,
                    color: "bg-green-500 hover:bg-green-600",
                  },
                  {
                    id: 'planung',
                    title: "Planung",
                    icon: Calendar,
                    color: "bg-orange-500 hover:bg-orange-600",
                  },
                  {
                    id: 'konfiguration',
                    title: "Konfiguration",
                    icon: Settings,
                    color: "bg-blue-500 hover:bg-blue-600",
                  },
                  {
                    id: 'eskalation',
                    title: "Eskalation",
                    icon: ShieldAlert,
                    color: "bg-purple-500 hover:bg-purple-600",
                  },
                  {
                    id: 'statistiken',
                    title: "Statistiken",
                    icon: BarChart3,
                    color: "bg-purple-500 hover:bg-purple-600",
                  },
                ]}
                defaultTabId={activeTab}
                onChange={(tabId) => setActiveTab(tabId as any)}
              />
            </div>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-y-auto pr-1">
            {activeTab === 'personen' && (
              <div className="max-h-[900px]">
                <BereitschaftsPersonenVerwaltung onPersonenChange={onConfigurationChange} />
              </div>
            )}

            {activeTab === 'planung' && (
              <div className="max-h-[780px]">
                <BereitschaftsWochenplanung onPlanungChange={onConfigurationChange} />
              </div>
            )}

            {activeTab === 'konfiguration' && (
              <div className="max-h-[780px]">
                <BereitschaftsKonfigurationComponent onConfigurationChange={onConfigurationChange} />
              </div>
            )}

            {activeTab === 'eskalation' && (
              <div className="min-h-[500px]">
                <BereitschaftsEskalationsmatrix />
              </div>
            )}

            {activeTab === 'statistiken' && (
              <div className="min-h-[500px] p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Bereitschafts-Statistiken</h3>
                <p className="text-gray-600 mb-4">
                  Analysen folgen. Nutzen Sie vorerst Personen, Planung und Einstellungen.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};