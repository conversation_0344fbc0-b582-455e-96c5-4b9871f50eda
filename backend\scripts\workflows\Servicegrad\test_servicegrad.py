#!/usr/bin/env python3
"""
Test-Skript für Servicegrad Workflow Integration
"""

import sys
import os
from pathlib import Path

# Füge das aktuelle Verzeichnis zum Python-Pfad hinzu
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from workflow_logger import WorkflowLogger

def test_workflow_logger():
    """Testet den WorkflowLogger für Servicegrad"""
    print("=== TESTE SERVICEGRAD WORKFLOW LOGGER ===")
    
    try:
        # Erstelle Logger
        logger = WorkflowLogger("servicegrad")
        print("✅ WorkflowLogger erfolgreich erstellt")
        
        # Teste verschiedene Log-Methoden
        logger.info("Test Info-Nachricht")
        logger.warning("Test Warning-Nachricht")
        logger.debug("Test Debug-Nachricht")
        
        # Teste Prozess-Logging
        logger.log_process_start("Test Servicegrad Prozess")
        logger.log_sap_action("Test SAP-Aktion", "wnd[0]", True)
        logger.log_file_operation("Test Datei-Operation", "test.xlsx", True)
        logger.log_process_complete("Test Servicegrad Prozess", "test_export.xlsx")
        
        print("✅ Alle Log-Methoden erfolgreich getestet")
        
        # Teste Log-Abfrage
        logs = WorkflowLogger.get_logs_for_workflow("servicegrad", limit=10)
        print(f"✅ {len(logs)} Logs für Servicegrad gefunden")
        
        return True
        
    except Exception as e:
        print(f"❌ Fehler beim Testen des WorkflowLoggers: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """Testet die Datenbankverbindung"""
    print("\n=== TESTE DATENBANKVERBINDUNG ===")
    
    try:
        import sqlite3
        from pathlib import Path
        
        # Bestimme DB-Pfad
        backend_dir = Path(__file__).parent.parent.parent
        db_path = str(backend_dir / "database" / "sfm_dashboard.db")
        
        print(f"DB-Pfad: {db_path}")
        
        # Teste Verbindung
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Prüfe ob workflow_logs Tabelle existiert
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='workflow_logs'
        """)
        
        result = cursor.fetchone()
        if result:
            print("✅ workflow_logs Tabelle existiert")
            
            # Zähle Einträge
            cursor.execute("SELECT COUNT(*) FROM workflow_logs WHERE workflow_id = 'servicegrad'")
            count = cursor.fetchone()[0]
            print(f"✅ {count} Servicegrad-Logs in der Datenbank")
        else:
            print("⚠️ workflow_logs Tabelle existiert nicht (wird beim ersten Lauf erstellt)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Fehler bei Datenbankverbindung: {e}")
        return False

def main():
    """Hauptfunktion für Tests"""
    print("SERVICEGRAD WORKFLOW INTEGRATION TEST")
    print("=" * 50)
    
    success = True
    
    # Test 1: WorkflowLogger
    if not test_workflow_logger():
        success = False
    
    # Test 2: Datenbankverbindung
    if not test_database_connection():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ ALLE TESTS ERFOLGREICH")
    else:
        print("❌ EINIGE TESTS FEHLGESCHLAGEN")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)