import { defineConfig } from "vite";
import path from "path";

// https://vitejs.dev/config
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    }
  },
  define: {
    'MAIN_WINDOW_VITE_DEV_SERVER_URL': JSON.stringify(process.env.MAIN_WINDOW_VITE_DEV_SERVER_URL),
    'MAIN_WINDOW_VITE_NAME': JSON.stringify(process.env.MAIN_WINDOW_VITE_NAME),
  },
  build: {
    outDir: "dist/main",
    lib: {
      entry: "src/main/electron-main.ts",
      formats: ["cjs"],
      fileName: () => "electron-main.js",
    },
    rollupOptions: {
      external: ["electron", "better-sqlite3", "child_process", "fs", "path", "os", "crypto", "util", "express", "cors", "@prisma/client", "bcryptjs", "jsonwebtoken"],
    },
    sourcemap: true,
    emptyOutDir: false
  },
});
