import React, { useEffect, useState, memo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from "recharts";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { TrendingUp, TrendingDown } from "lucide-react";
import apiService from "@/services/api.service";

/**
 * ReturnsChart Komponente
 * 
 * Zeigt die Retouren-Kennzahlen als Donut-Chart mit Datumsfilter an:
 * - QM offen
 * - QM abgelehnt
 * - QM angenommen
 * 
 * @param data Optional: A<PERSON><PERSON> von <PERSON>pu<PERSON> mit den verschiedenen Kennzahlen
 */

// Definition des Datentyps für den DateRange
interface DateRange {
  from?: Date;
  to?: Date;
}

// Definition des Datentyps für die Retouren-Daten
interface ReturnDataItem {
  name: string;
  value: number;
  fill: string;
}

// Definition des Datentyps für Datenbankzeilen
interface DatabaseRow {
  name: string;
  value: number;
  fill: string;
  date?: string; // Optionales Datumsfeld, falls vorhanden
}

interface ReturnsChartProps {
  // Optional, da die Daten jetzt auch direkt geladen werden können
  data?: ReturnDataItem[];
  dateRange?: DateRange;
}

// Footer-Komponente für die ReturnsChart
export function ReturnsChartFooter() {
  return (
    <div className="w-full">
      <div className="grid gap-2">
        <div className="flex items-center gap-2 leading-none font-medium">
          Q-Meldungen aus der Datenbank
        </div>
        <div className="text-muted-foreground flex items-center gap-2 leading-none">
          Letzte Aktualisierung: {new Date().toLocaleDateString()}
        </div>
      </div>
    </div>
  );
}

export const ReturnsChart = memo(function ReturnsChart({ data: propData, dateRange }: ReturnsChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<ReturnDataItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [trend, setTrend] = useState<number>(0); // Positiv = Aufwärtstrend, Negativ = Abwärtstrend

  // Konfiguration für das Diagramm
  const chartConfig = {
    qm_offen: {
      label: t("qmOpen"),
      color: "var(--chart-1)",
    },
    qm_abgelehnt: {
      label: t("qmRejected"),
      color: "var(--chart-2)",
    },
    qm_angenommen: {
      label: t("qmAccepted"),
      color: "var(--chart-3)",
    },
  };



  // Lade Daten aus der Datenbank, wenn keine Props übergeben wurden oder wenn sich der Datumsbereich ändert
  useEffect(() => {
    // Erzwinge ein Neuladen der Daten bei jeder Datumsbereichsänderung
    setLoading(true);
    setError(null);

    if (propData) {
      // Verwende die übergebenen Daten, wenn vorhanden
      setChartData(propData);
      setLoading(false);
      return;
    }

    // Lade Daten aus der Datenbank
    const loadData = async () => {
      try {
        const result = await apiService.getReturnsData();

        if (result && Array.isArray(result)) {

          // Filtere die Daten nach dem ausgewählten Datumsbereich
          let filteredData: ReturnDataItem[] = [];

          if (dateRange?.from || dateRange?.to) {
            // Erstelle Kopien der Datumsobjekte mit korrekten Uhrzeiten
            const fromDate = dateRange?.from ? new Date(dateRange.from) : null;
            const toDate = dateRange?.to ? new Date(dateRange.to) : null;

            if (fromDate) fromDate.setHours(0, 0, 0, 0);
            if (toDate) toDate.setHours(23, 59, 59, 999);

            // Filtere die Daten nach dem Datumsbereich
            const filteredRows = result.filter((row: unknown) => {
              const dbRow = row as DatabaseRow;
              if (!dbRow.date) return false;

              try {
                const rowDateStr = String(dbRow.date).trim();
                const rowDate = new Date(rowDateStr);

                // Überprüfe, ob das Datum gültig ist
                if (isNaN(rowDate.getTime())) {
                  return false;
                }

                // Prüfe, ob das Datum im ausgewählten Bereich liegt
                const isAfterFrom = !fromDate || rowDate >= fromDate;
                const isBeforeTo = !toDate || rowDate <= toDate;

                return isAfterFrom && isBeforeTo;
              } catch (err) {
                return false;
              }
            });

            // Gruppiere die gefilterten Daten nach Name
            const groupedData: Record<string, ReturnDataItem> = {};

            filteredRows.forEach((row: unknown) => {
              const dbRow = row as DatabaseRow;
              if (!groupedData[dbRow.name]) {
                groupedData[dbRow.name] = {
                  name: dbRow.name,
                  value: 0,
                  fill: dbRow.fill
                };
              }

              groupedData[dbRow.name].value += Number(dbRow.value) || 0;
            });

            // Konvertiere das Objekt zurück in ein Array
            filteredData = Object.values(groupedData);
          } else {
            // Wenn kein Datumsbereich ausgewählt ist oder die Daten kein Datumsfeld haben,
            // verwende alle verfügbaren Daten

            // Gruppiere die Daten nach Name
            const groupedData: Record<string, ReturnDataItem> = {};

            result.forEach((row: unknown) => {
              const dbRow = row as DatabaseRow;
              if (!groupedData[dbRow.name]) {
                groupedData[dbRow.name] = {
                  name: dbRow.name,
                  value: 0,
                  fill: dbRow.fill
                };
              }

              groupedData[dbRow.name].value += Number(dbRow.value) || 0;
            });

            filteredData = Object.values(groupedData);
          }

          setChartData(filteredData);

          // Berechne den Trend (hier nur als Beispiel)
          setTrend(0);
        } else {
          setError('Keine gültigen Daten aus der Datenbank erhalten.');
          setChartData([]);
          setTrend(0);
        }
      } catch (err) {
        setError('Fehler beim Laden der Daten aus der Datenbank.');
        setChartData([]);
        setTrend(0);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [propData, dateRange, t]);

  // Berechne Gesamtwert für den Footer
  const totalValue = chartData.length > 0 ? chartData.reduce((sum: number, item: ReturnDataItem) => sum + item.value, 0) : 0;

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">{t("loading")}...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="text-red-500 text-4xl mb-2">⚠️</div>
        <p className="font-bold text-red-500">{error}</p>
      </div>
    );
  }

  // Zeige Hinweis an, wenn keine Daten vorhanden sind
  if (chartData.length === 0) {
    // Wenn keine Daten vorhanden sind, verwenden wir Testdaten für die Anzeige

    // Testdaten für die Entwicklung
    const testData: ReturnDataItem[] = [
      { name: 'QM offen', value: 15, fill: 'var(--chart-1)' },
      { name: 'QM abgelehnt', value: 8, fill: 'var(--chart-2)' },
      { name: 'QM angenommen', value: 27, fill: 'var(--chart-3)' }
    ];

    // Verwende Testdaten für die Anzeige
    setTimeout(() => {
      setChartData(testData);
      setLoading(false);
    }, 500);

    // Zeige Ladeanimation während der Testdaten geladen werden
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">Lade Testdaten...</p>
      </div>
    );
  }

  return (
    <Card className="text-black w-full h-104 border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <CardTitle>QM</CardTitle>
        <CardDescription>
          Q-Meldungen - Status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="w-full h-60">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              innerRadius={40}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              stroke="#000"
              strokeWidth={2}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  indicator="dot"
                  labelClassName="font-bold"
                  formatter={(value, name) => {
                    // Finde den passenden Namen basierend auf dem Wert
                    const item = chartData.find(item => item.value === value);
                    return [value, item ? item.name : name];
                  }}
                  labelFormatter={() => {
                    // Zeige den ausgewählten Datumsbereich im Tooltip
                    if (dateRange?.from && dateRange?.to) {
                      const fromDate = dateRange.from.toLocaleDateString();
                      const toDate = dateRange.to.toLocaleDateString();
                      return `Zeitraum: ${fromDate} - ${toDate}`;
                    } else {
                      return `Datum: ${new Date().toLocaleDateString()}`;
                    }
                  }}
                />
              }
            />
            <ChartLegend content={() => {
              // Erstelle ein korrektes payload-Format für die Legende
              const series = [
                { dataKey: 'qm_offen', name: t("qmOpen"), color: "var(--chart-1)", value: 'qm_offen', type: 'rect' as const },
                { dataKey: 'qm_abgelehnt', name: t("qmRejected"), color: "var(--chart-2)", value: 'qm_abgelehnt', type: 'rect' as const },
                { dataKey: 'qm_angenommen', name: t("qmAccepted"), color: "var(--chart-3)", value: 'qm_angenommen', type: 'rect' as const },
              ];

              return (
                <ChartLegendContent
                  payload={series}
                  className="p-2 rounded-md"
                />
              );
            }} />
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              {trend > 0 ? (
                <>
                  Trend: +{trend}% <TrendingUp className="h-4 w-4" />
                </>
              ) : trend < 0 ? (
                <>
                  Trend: {trend}% <TrendingDown className="h-4 w-4" />
                </>
              ) : (
                "Trend: Stabil"
              )}
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Gesamt Retouren: {totalValue}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});
