import { useEffect, useState, useLayoutEffect } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "@tanstack/react-router";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Truck, Scissors, PackageCheck, Clock, Home, Info } from "lucide-react";
import dashboardIcon from "@/assets/iconDashboard.png";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import apiService from "@/services/api.service";
import { AskJaszButton } from "@/modules/ai/components";
import { createPageContext } from "@/contexts/AskJaszContext";

// Interface für echte KPI-Daten aus der Datenbank
interface DepartmentKPIs {
  dispatch: {
    serviceLevel: number;
    targetServiceLevel: number;
    pickingPerformance: number; // Summe von ATRL + ARIL (keine Prozent)
    deliveryPerformance: number;
    qualityRate: number;
    producedTonnage: number; // Aus TagesleistungData
    directLoading: number; // Aus TagesleistungData
    umschlag: number; // Aus TagesleistungData
    kgPerColli: number; // Aus TagesleistungData
    elefanten: number; // Aus TagesleistungData
    lastUpdated: string;
  };
  ablaengerei: {
    schnitte: number; // Summe von pickCut + cutRR + cutTR + cutTT
    schnitteTT: number; // cutTT
    schnitteTR: number; // cutTR
    schnitteRR: number; // cutRR
    schnitteCut2Pick: number; // pickCut
    qualityRate: number; // Gleiche Berechnung wie bei Dispatch
    lastUpdated: string;
  };
  wareneingang: {
    weAtrlPos: number; // weAtrl - Automatische WE-Positionen
    weManuellePos: number; // weManl - Manuelle WE-Positionen
    gesamtWE: number; // Summe von weAtrl + weManl
    automatisierungsgrad: number; // Prozentsatz automatischer WE
    qualityRate: number; // Gleiche Berechnung wie bei Dispatch
    lastUpdated: string;
  };
}



/**
 * Dashboard-Seite des Shopfloor-Management-Dashboards
 * 
 * Zeigt eine Übersicht über alle verfügbaren Abteilungen mit echten KPI-Daten
 * aus der Datenbank für den gestrigen Tag.
 * Das Design folgt dem Neobrutalism-Stil mit kräftigen Farben und starken Konturen.
 */
export default function DashboardPage() {
  const { t } = useTranslation();
  const [departmentKPIs, setDepartmentKPIs] = useState<DepartmentKPIs | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [actualDataDate, setActualDataDate] = useState<string>('');

  // Funktion zur Rückgabe des gestrigen Datums für die KPI-Anzeige
  const getTargetDate = (): string => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const day = String(yesterday.getDate()).padStart(2, '0');
    const month = String(yesterday.getMonth() + 1).padStart(2, '0');
    const year = yesterday.getFullYear();
    return `${year}-${month}-${day}`; // Format: YYYY-MM-DD (passend zur Datenbank)
  };

  // Funktion zum Laden aller KPI-Daten für den gestrigen Tag
  const loadKPIData = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      const targetDate = getTargetDate();


      // Parallel alle Datenquellen laden über die API
      const [
        serviceLevelResponse,
        pickingResponse,
        deliveryResponse,
        qmResponse,
        tagesleistungResponse,
        ablaengereiResponse,
        weResponse
      ] = await Promise.all([
        apiService.getServiceLevelData(),
        apiService.getPickingData(),
        apiService.getDeliveryPositionsData(),
        apiService.getReturnsData(),
        apiService.getTagesleistungData(),
        apiService.getAblaengereiData(),
        apiService.getWEData()
      ]);

      // Verwende die API-Responses direkt, da sie bereits die Datenarrays enthalten
      const serviceLevelResult = serviceLevelResponse || [];
      const pickingResult = pickingResponse || [];
      const deliveryResult = deliveryResponse || [];
      const qmResult = qmResponse || [];
      const tagesleistungResult = tagesleistungResponse || [];
      const ablaengereiResult = ablaengereiResponse || [];
      const weResult = weResponse || [];



      // Filtere Daten für das Zieldatum und berechne KPIs
      const kpis = calculateKPIs(
        targetDate,
        serviceLevelResult,
        pickingResult,
        deliveryResult,
        qmResult,
        tagesleistungResult,
        ablaengereiResult,
        weResult
      );

      setDepartmentKPIs(kpis);
    } catch (err) {
      console.error('Fehler beim Laden der KPI-Daten:', err);
      setError('Fehler beim Laden der Dashboard-Daten');
    } finally {
      setLoading(false);
    }
  };

  // Funktion zur Berechnung der KPIs aus den Rohdaten
  const calculateKPIs = (
    targetDate: string,
    serviceLevelData: any[],
    pickingData: any[],
    deliveryData: any[],
    qmData: any[],
    tagesleistungData: any[],
    ablaengereiData: any[],
    weData: any[]
  ): DepartmentKPIs => {
    const lastUpdated = new Date().toLocaleTimeString('de-DE');

    // Hilfsfunktion: Verwende neueste verfügbare Daten als Fallback
    const getLatestOrTargetData = (dataArray: any[], targetDate: string, dateField: string = 'datum') => {
      // Versuche zuerst das Zieldatum zu finden
      let item = dataArray?.find(item => item[dateField] === targetDate);

      // Falls nicht gefunden, verwende den neuesten verfügbaren Datensatz
      if (!item && dataArray && dataArray.length > 0) {
        item = dataArray[0]; // Daten sind bereits nach Datum sortiert (neueste zuerst)

      }

      return item;
    };

    // Servicegrad - verwende neueste verfügbare Daten
    const serviceLevelItem = getLatestOrTargetData(serviceLevelData, targetDate);
    let serviceLevel = serviceLevelItem?.servicegrad || 0;
    // Konvertiere Dezimalwerte zu Prozent
    if (serviceLevel > 0 && serviceLevel <= 1) {
      serviceLevel = serviceLevel * 100;
    }

    // Picking-Performance - verwende neueste verfügbare Daten
    const pickingItem = getLatestOrTargetData(pickingData, targetDate, 'date');
    const pickingPerformance = pickingItem
      ? pickingItem.atrl + pickingItem.aril
      : 0;

    // Delivery-Performance - verwende neueste verfügbare Daten
    const deliveryItem = getLatestOrTargetData(deliveryData, targetDate, 'date');
    const deliveryPerformance = deliveryItem && (deliveryItem.ausgeliefert_lup + deliveryItem.rueckstaendig) > 0
      ? (deliveryItem.ausgeliefert_lup / (deliveryItem.ausgeliefert_lup + deliveryItem.rueckstaendig)) * 100
      : 0;

    // Qualitätsrate - verwende neueste verfügbare Daten
    const qmLatestData = qmData && qmData.length > 0 ? qmData : [];
    const totalQM = qmLatestData?.reduce((sum, item) => sum + item.value, 0) || 0;
    const acceptedQM = qmLatestData?.find(item => item.name.toLowerCase().includes('angenommen'))?.value || 0;
    const qualityRate = totalQM > 0 ? (acceptedQM / totalQM) * 100 : 85; // Fallback-Wert

    // Tagesleistung - verwende neueste verfügbare Daten
    const tagesleistungItem = getLatestOrTargetData(tagesleistungData, targetDate, 'date');
    const producedTonnage = tagesleistungItem?.produzierte_tonnagen || 0;
    const kgPerColli = tagesleistungItem?.kg_pro_colli || 0;
    const umschlag = tagesleistungItem?.umschlag || 0;
    const directLoading = tagesleistungItem?.direktverladung_kiaa || 0;
    const elefanten = tagesleistungItem?.elefanten || 0;

    // Ablängerei-Daten - verwende neueste verfügbare Daten
    const ablaengereiItem = getLatestOrTargetData(ablaengereiData, targetDate);

    const cutTT = ablaengereiItem?.cutTT || 0;
    const cutTR = ablaengereiItem?.cutTR || 0;
    const cutRR = ablaengereiItem?.cutRR || 0;
    const pickCut = ablaengereiItem?.pickCut || 0;
    const schnitte = cutTT + cutTR + cutRR + pickCut; // Summe aller Schnitte



    // Qualitätsrate für Ablängerei (gleiche Berechnung wie bei Dispatch)
    const ablaengereiQualityRate = qualityRate;

    // Wareneingang-Daten - verwende neueste verfügbare Daten
    // getWEData() liefert Objekte der Form:
    // { datum: "YYYY-MM-DD", weAtrl: number, weManl: number }
    const weItem = getLatestOrTargetData(weData, targetDate, 'datum');
    const weAtrl = Number(weItem?.weAtrl ?? 0);
    const weManl = Number(weItem?.weManl ?? 0);
    const gesamtWE = weAtrl + weManl;
    const automatisierungsgrad = gesamtWE > 0 ? (weAtrl / gesamtWE) * 100 : 0;



    // Qualitätsrate für Wareneingang (gleiche Berechnung wie bei Dispatch)
    const wareneingangQualityRate = qualityRate;

    // Setze das tatsächlich verwendete Datum für die Anzeige
    const actualDate = weItem?.datum || ablaengereiItem?.datum || serviceLevelItem?.datum || targetDate;
    setActualDataDate(actualDate);

    return {
      dispatch: {
        serviceLevel,
        targetServiceLevel: 98.5,
        pickingPerformance,
        deliveryPerformance,
        qualityRate,
        producedTonnage,
        directLoading,
        umschlag,
        kgPerColli,
        elefanten,
        lastUpdated
      },
      ablaengerei: {
        schnitte,
        schnitteTT: cutTT,
        schnitteTR: cutTR,
        schnitteRR: cutRR,
        schnitteCut2Pick: pickCut,
        qualityRate: ablaengereiQualityRate,
        lastUpdated
      },
      wareneingang: {
        weAtrlPos: weAtrl,
        weManuellePos: weManl,
        gesamtWE,
        automatisierungsgrad,
        qualityRate: wareneingangQualityRate,
        lastUpdated
      }
    };
  };

  // Lade KPI-Daten beim Component Mount
  useEffect(() => {
    loadKPIData();
  }, []);

  // Loading State
  if (loading) {
    return (
      <div className="w-full bg-bg min-h-screen p-8 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mb-4"></div>
          <p className="font-heading text-xl">Dashboard wird geladen...</p>
        </div>
      </div>
    );
  }

  // Error State
  if (error || !departmentKPIs) {
    return (
      <div className="w-full bg-bg min-h-screen p-8 flex items-center justify-center">
        <div className="flex flex-col items-center text-red-500">
          <div className="text-4xl mb-4">⚠️</div>
          <p className="font-heading text-xl">{error || 'Fehler beim Laden der Daten'}</p>
          <Button
            onClick={loadKPIData}
            className="mt-4 bg-black text-white hover:bg-gray-800"
          >
            Erneut versuchen
          </Button>
        </div>
      </div>
    );
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: 'spring' as const, damping: 25 },
    },
  };

  // Farbzuordnung für Karten-Akzente und Border
  const colorMap = {
    blue: {
      border: "border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200",
      accentFrom: "from-blue-500",
      accentTo: "to-blue-500/30",
    },
    green: {
      border: "border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200",
      accentFrom: "from-green-500",
      accentTo: "to-green-500/30",
    },
    amber: {
      border: "border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200",
      accentFrom: "from-amber-500",
      accentTo: "to-amber-500/30",
    },
    red: {
      border: "border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200",
      accentFrom: "from-red-500",
      accentTo: "to-red-500/30",
    },
    violet: {
      border: "border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200",
      accentFrom: "from-violet-500",
      accentTo: "to-violet-500/30",
    },
    cyan: {
      border: "border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200",
      accentFrom: "from-cyan-500",
      accentTo: "to-cyan-500/30",
    },
  } as const;

  const DepartmentCard = ({
    children,
    className,
    icon,
    title,
    color = "blue",
    bgColor = "bg-blue-200"
  }: {
    children: React.ReactNode;
    className?: string;
    icon: React.ReactNode;
    title: string;
    color?: keyof typeof colorMap | string;
    bgColor?: string;
  }) => {
    const palette = (typeof color === "string" && color in colorMap
      ? colorMap[color as keyof typeof colorMap]
      : colorMap.blue);

    return (
      <motion.div
        variants={itemVariants}
        initial={{ scale: 1, y: 0 }}
        whileHover={{
          scale: 1.02,
          y: -4,
          transition: { duration: 0.15, ease: "easeOut" }
        }}
        animate={{ scale: 1, y: 0 }}
        transition={{
          duration: 0.15,
          ease: "easeOut"
        }}
        className={cn(
          'group relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border-1 shadow-md transition-all duration-150 hover:shadow-lg',
          palette.border,
          bgColor,
          className,
        )}
      >
        <div className="absolute top-0 -right-1/2 z-0 size-full cursor-pointer bg-[linear-gradient(to_right,#3d16165e_1px,transparent_1px),linear-gradient(to_bottom,#3d16165e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>

        <div className={cn(
          "text-black/5 group-hover:text-black/15 absolute right-2 bottom-4 scale-[4] transition-all duration-200 group-hover:scale-[4.3]"
        )}>
          {icon}
        </div>

        <div className="relative z-10 flex h-full flex-col justify-between">
          <div className="border-b-3 border-black p-4">
            <div className="flex items-center justify-between text-black">
              <span className="text-xl font-heading">{title}</span>
              <motion.div
                className="flex h-10 w-10 items-center justify-center"
                initial={{ scale: 1, rotate: 0 }}
                whileHover={{
                  scale: 1.1,
                  rotate: 5,
                  transition: { duration: 0.15, ease: "easeOut" }
                }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{
                  duration: 0.15,
                  ease: "easeOut"
                }}
              >
                {icon}
              </motion.div>
            </div>
          </div>

          <div className="flex-grow p-6">
            {children}
          </div>
        </div>

        <div
          className={cn(
            "absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r transition-all duration-150 group-hover:h-2",
            // Akzentfarbe abhängig von Card-Farbe
            palette.accentFrom,
            palette.accentTo,
            // Der Blur sorgt für Glow, bleibt aber Card-spezifisch
            "blur-2xl group-hover:blur-sm"
          )}
        />
      </motion.div>
    );
  };

  // Tooltip/Icon Helper
  // Tooltip nur beim Icon-Hover anzeigen, nicht bei Hover der gesamten Card
  // InfoTip: breiteres Popup mit intelligenter Positionierung, damit es nicht über die Card-Kante hinausragt.
  const InfoTip = ({ text, className = "" }: { text: string; className?: string }) => (
    <span className={cn("relative inline-flex items-center", className)}>
      <Info className="h-3 w-3 text-black/60 hover:text-black cursor-pointer peer" />
      {/* Popup wird innerhalb der Card gehalten: wir nutzen ein separates Portal-Container-Overlay innerhalb der Card-Box */}
      <span
        className="pointer-events-none absolute top-full z-50 mt-2 whitespace-pre-line rounded-md border border-black/20 bg-white px-4 py-3 text-sm text-black opacity-0 shadow-lg transition-opacity duration-150 w-[280px] max-w-[min(80vw,200px)]
                   peer-hover:opacity-100 left-1/2 -translate-x-1/2"
      >
        {text}
      </span>
    </span>
  );

  // Hilfsfunktion: dynamische Positionierung je nach Platz (oben/unten, links/rechts)
  // Wir setzen den Tooltip standardmäßig unten-mittig. Zusätzlich begrenzen wir die Breite und verhindern, dass er über die Card-Kante hinausragt, indem:
  // - w-[280px] + max-w-[calc(100vw-4rem)] (kleiner auf sehr kleinen Screens)
  // - left-1/2 -translate-x-1/2 und zusätzlich translate-x-Korrekturen mit CSS clamp via style für Feintuning
  // - pointer-events-none und hoher z-index
  // Außerdem erlauben wir overflow-visible in den Kartenelementen, damit der Tooltip nicht abgeschnitten wird.
  return (
    <div className="w-full bg-bg min-h-screen p-8">
      {/* Header mit korrektem Neobrutalism Styling */}
      <div className="flex items-center justify-between w-full mb-8">
        <div className="flex-1">
          <div className="flex items-center gap-4">
            <img
              src={dashboardIcon}
              alt="Dashboard"
              className="h-8 w-8 mr-2 object-contain"
            />
            <h1 className="text-4xl font-heading tracking-tight text-text">{t("DASHBOARD")}</h1>
            <div className="-mt-7">
            <AskJaszButton
              context={createPageContext(
                "Dashboard Hauptseite",
                [
                  "Übersicht über alle Abteilungen",
                  "KPI-Anzeige für Versand, Ablängerei und Wareneingang",
                  "Echtzeit-Daten und Leistungsmetriken",
                  "Navigation zu Detailansichten"
                ],
                "Tagesübersicht mit aktuellen Kennzahlen"
              )}
              position="inline"
              size="md"
              variant="default"
              tooltip="Hilfe zur Dashboard-Hauptseite erhalten"
            />
          </div>
          </div>
          <p className="text-lg text-text font-base mt-2 opacity-70">{t("Shopfloormanagement")}</p>
        </div>
      </div>

      {/* Willkommens-Card */}
      <Card className="mb-8 border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
        <CardContent className="p-2">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-heading text-black mb-2">
                Tagesübersicht ({actualDataDate || getTargetDate()})
                {actualDataDate && actualDataDate !== getTargetDate() && (
                  <span className="text-sm font-base opacity-70 ml-2">
                    (Neueste verfügbare Daten)
                  </span>
                )}
              </h2>
            </div>
            <div className="text-sm text-black font-base flex items-center gap-2 opacity-70">
              <Clock className="h-4 w-4" />
              Letzte Aktualisierung: {new Date().toLocaleTimeString('de-DE')}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* KPI-Cards für Versand, Ablängerei und Wareneingang mit echten Daten */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
        variants={{
          hidden: {},
          visible: {
            transition: {
              staggerChildren: 0.15,
              delayChildren: 0.1,
            },
          },
        }}
        initial="hidden"
        animate="visible"
      >

        {/* Dispatch KPI-Card mit allen verfügbaren Daten */}
        <DepartmentCard
          icon={<Truck className="h-8 w-8" />}
          title="VERSAND"
          color="blue"
          bgColor="bg-blue-200"
        >
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="font-base text-black inline-flex items-center gap-2">
                  Servicegrad
                  <InfoTip text={"Anteil termingerecht ausgelieferter Positionen in % (Soll: 98,5%)."} />
                </span>
                <span className="font-heading text-lg text-black">
                  {Number.isFinite(departmentKPIs.dispatch.serviceLevel) ? departmentKPIs.dispatch.serviceLevel.toFixed(1) : '0.0'}%
                </span>
              </div>
              <Progress value={Number.isFinite(departmentKPIs.dispatch.serviceLevel) ? departmentKPIs.dispatch.serviceLevel : 0} className="h-3" />
              <div className="text-xs text-black font-base mt-1 opacity-70">
                Ziel: {Number.isFinite(departmentKPIs.dispatch.targetServiceLevel) ? departmentKPIs.dispatch.targetServiceLevel : 0}%
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <div className="text-sm font-base text-black inline-flex items-center gap-2">
                  Picking-Leistung (ATRL+ARIL)
                  <InfoTip text={"Summe aller gepickten Positionen in den Versand aus dem Automatischen Trommellager + Automatischen Ringlager."} />
                </div>
                <div className="text-2xl font-heading text-black">
                  {Number.isFinite(departmentKPIs.dispatch.pickingPerformance) ? departmentKPIs.dispatch.pickingPerformance.toFixed(0) : '0'}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-base text-black inline-flex items-center gap-2">
                  Lieferleistung
                  <InfoTip text={"Anteil ausgelieferte zu Rückständig Lieferungen in %."} />
                </div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.dispatch.deliveryPerformance.toFixed(1)}%</div>
              </div>
            </div>

            <div className="pt-2 border-t border-black/20 space-y-2">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black inline-flex items-center gap-2">
                    Qualitätsrate
                    <InfoTip text={"Anteil akzeptierter Q-Meldungen gegenüber alle Q-Meldungen in %."} />
                  </span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.qualityRate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black inline-flex items-center gap-2">
                    Produzierte Tonnagen
                    <InfoTip text={"Gesamtgewicht der produzierten Tonnagen pro Tag."} />
                  </span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.producedTonnage.toFixed(1)}t</span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black inline-flex items-center gap-2">
                    Direktverladung
                    <InfoTip text={"Anzahl Direktverladung + KIAA Sendungen."} />
                  </span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.directLoading.toFixed(0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black inline-flex items-center gap-2">
                    Umschlag
                    <InfoTip text={"Anzahl umgeschlagener Colli/Einheiten."} />
                  </span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.umschlag.toFixed(0)}</span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black inline-flex items-center gap-2">
                    kg/Colli
                    <InfoTip text={"Durchschnittliches Gewicht pro Colli in kg."} />
                  </span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.kgPerColli.toFixed(1)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black inline-flex items-center gap-2">
                    Elefanten
                    <InfoTip text={"Visualisierte Menge (1 Elefant = 5000 kg)."} />
                  </span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.elefanten.toFixed(0)}</span>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="mt-6 pt-4 border-t-3 border-black bg-bg/50 -mx-6 -mb-6 px-6 py-4">
              <div className="flex justify-between w-full items-center">
                <div className="text-sm text-black font-base opacity-70">
                  Aktualisiert {departmentKPIs.dispatch.lastUpdated}
                </div>
                <Button variant="sfm" size="sm" asChild>
                  <Link to="/modules/dashboard/dispatch">Details</Link>
                </Button>
              </div>
            </div>
          </div>
        </DepartmentCard>

        {/* Ablängerei KPI-Card mit allen verfügbaren Daten */}
        <DepartmentCard
          icon={<Scissors className="h-8 w-8" />}
          title="ABLÄNGEREI"
          color="orange"
          bgColor="bg-orange-200"
        >
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="font-base text-black inline-flex items-center gap-2">
                  Schnitte (Gesamt)
                  <InfoTip text={"Summe aller Schnitte (Trommel + Ring + Cut2Pick)."} />
                </span>
                <span className="font-heading text-lg text-black">{departmentKPIs.ablaengerei.schnitte.toFixed(0)}</span>
              </div>
              <Progress value={Math.min((departmentKPIs.ablaengerei.schnitte / 1000) * 100, 100)} className="h-3" />
              <div className="text-xs text-black font-base mt-1 opacity-70">
                Schnittarten:
              </div>
            </div>

            <div className="grid grid-cols-4 gap-2">
              <div className="space-y-1 text-center">
                <div className="text-sm font-base text-black inline-flex items-center gap-2 justify-center">
                  Schnitte TT
                  <InfoTip text={"Anzahl Trommel Trommel Schnitte."} />
                </div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.ablaengerei.schnitteTT.toFixed(0)}</div>
              </div>
              <div className="space-y-1 text-center">
                <div className="text-sm font-base text-black inline-flex items-center gap-2 justify-center">
                  Schnitte TR
                  <InfoTip text={"Anzahl Trommel Ring Schnitte."} />
                </div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.ablaengerei.schnitteTR.toFixed(0)}</div>
              </div>
              <div className="space-y-1 text-center">
                <div className="text-sm font-base text-black inline-flex items-center gap-2 justify-center">
                  Schnitte RR
                  <InfoTip text={"Anzahl Ring Ring Schnitte."} />
                </div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.ablaengerei.schnitteRR.toFixed(0)}</div>
              </div>
              <div className="space-y-1 text-center">
                <div className="text-sm font-base text-black inline-flex items-center gap-2 justify-center">
                  Cut2Pick
                  <InfoTip text={"Anzahl Cut2Pick Schnitte."} />
                </div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.ablaengerei.schnitteCut2Pick.toFixed(0)}</div>
              </div>
            </div>

            <div className="pt-2 border-t border-black/20 space-y-2">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black inline-flex items-center gap-2">
                    Qualitätsrate
                    <InfoTip text={"Anteil akzeptierter Q-Meldungen gegenüber alle Q-Meldungen in %."} />
                  </span>
                  <span className="font-heading text-black">{departmentKPIs.ablaengerei.qualityRate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black opacity-0">Platzhalter</span>
                  <span className="font-heading text-black opacity-0">-</span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black opacity-0">Platzhalter</span>
                  <span className="font-heading text-black opacity-0">-</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black opacity-0">Platzhalter</span>
                  <span className="font-heading text-black opacity-0">-</span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black opacity-0">Platzhalter</span>
                  <span className="font-heading text-black opacity-0">-</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black opacity-0">Platzhalter</span>
                  <span className="font-heading text-black opacity-0">-</span>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="mt-6 pt-4 border-t-3 border-black bg-bg/50 -mx-6 -mb-6 px-6 py-4">
              <div className="flex justify-between w-full items-center">
                <div className="text-sm text-black font-base opacity-70">
                  Aktualisiert {departmentKPIs.ablaengerei.lastUpdated}
                </div>
                <Button variant="sfm" size="sm" asChild>
                  <Link to="/modules/dashboard/cutting">Details</Link>
                </Button>
              </div>
            </div>
          </div>
        </DepartmentCard>

        {/* Wareneingang KPI-Card mit allen verfügbaren Daten */}
        <DepartmentCard
          icon={<PackageCheck className="h-8 w-8" />}
          title="WARENEINGANG"
          color="green"
          bgColor="bg-green-200"
        >
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="font-base text-black inline-flex items-center gap-2">
                  Gesamt WE-Positionen
                  <InfoTip text={"Summe aller WE Positionen."} />
                </span>
                <span className="font-heading text-lg text-black">{departmentKPIs.wareneingang.gesamtWE.toFixed(0)}</span>
              </div>
              <Progress value={Math.min((departmentKPIs.wareneingang.gesamtWE / 500) * 100, 100)} className="h-3" />
              <div className="text-xs text-black font-base mt-1 opacity-70">
                Lageraufteilung:
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <div className="text-sm font-base text-black inline-flex items-center gap-2">
                  WE ATrL
                  <InfoTip text={"Anzahl Automatische Wareneingangspositionen."} />
                </div>
                <div className="text-2xl font-heading text-black">
                  {Number.isFinite(departmentKPIs.wareneingang.weAtrlPos) ? departmentKPIs.wareneingang.weAtrlPos.toFixed(0) : '0'}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-base text-black inline-flex items-center gap-2">
                  WE ManL
                  <InfoTip text={"Anzahl Manuelle Wareneingangspositionen."} />
                </div>
                <div className="text-2xl font-heading text-black">
                  {Number.isFinite(departmentKPIs.wareneingang.weManuellePos) ? departmentKPIs.wareneingang.weManuellePos.toFixed(0) : '0'}
                </div>
              </div>
            </div>

            <div className="pt-2 border-t border-black/20 space-y-2">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black inline-flex items-center gap-2">
                    Qualitätsrate
                    <InfoTip text={"Anteil akzeptierter Q-Meldungen gegenüber alle Q-Meldungen in %."} />
                  </span>
                  <span className="font-heading text-black">{departmentKPIs.wareneingang.qualityRate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black inline-flex items-center gap-2">
                    Automatisierung
                    <InfoTip text={"Anteil WE Automatisches Trommellager gegenüber WE Manuelles Trommellager in %."} />
                  </span>
                  <span className="font-heading text-black">{departmentKPIs.wareneingang.automatisierungsgrad.toFixed(1)}%</span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black opacity-0">Platzhalter</span>
                  <span className="font-heading text-black opacity-0">-</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black opacity-0">Platzhalter</span>
                  <span className="font-heading text-black opacity-0">-</span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black opacity-0">Platzhalter</span>
                  <span className="font-heading text-black opacity-0">-</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black opacity-0">Platzhalter</span>
                  <span className="font-heading text-black opacity-0">-</span>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="mt-6 pt-4 border-t-3 border-black bg-bg/50 -mx-6 -mb-6 px-6 py-4">
              <div className="flex justify-between w-full items-center">
                <div className="text-sm text-black font-base opacity-70">
                  Aktualisiert {departmentKPIs.wareneingang.lastUpdated}
                </div>
                <Button variant="sfm" size="sm" asChild>
                  <Link to="/modules/dashboard/incoming-goods">Details</Link>
                </Button>
              </div>
            </div>
          </div>
        </DepartmentCard>
      </motion.div>
    </div>
  );
}
