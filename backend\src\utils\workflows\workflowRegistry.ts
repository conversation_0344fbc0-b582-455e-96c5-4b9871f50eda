import path from 'path';

export type WorkflowId = 'bestand' | 'servicegrad' | 'rueckstandsliste' | 'lx03_240' | 'lx03_200' | 'lx03_rest';

export type WorkflowDefinition = {
  id: WorkflowId;
  name: string;
  scriptPath: string;
  env?: Record<string, string>;
  logDir?: string;
};

export const workflowRegistry: Record<WorkflowId, WorkflowDefinition> = {
  bestand: {
    id: 'bestand',
    name: 'Bestand',
    scriptPath: path.join(process.cwd(), 'backend', 'scripts', 'workflows', 'Bestand', 'Bestand-Workflow.py'),
    env: {},
    logDir: path.join(process.cwd(), 'backend', 'workflows', 'logs'),
  },
  servicegrad: {
    id: 'servicegrad',
    name: 'Servicegrad',
    scriptPath: path.join(process.cwd(), 'backend', 'scripts', 'workflows', 'Servicegrad', 'Servicegrad_Workflow.py'),
    env: {},
    logDir: path.join(process.cwd(), 'backend', 'workflows', 'logs'),
  },
  rueckstandsliste: {
    id: 'rueckstandsliste',
    name: 'Rückstandsliste',
    scriptPath: path.join(process.cwd(), 'backend', 'scripts', 'workflows', 'Rueckstandsliste', 'Rueckstandsliste_Workflow.py'),
    env: {},
    logDir: path.join(process.cwd(), 'backend', 'workflows', 'logs'),
  },
  lx03_240: {
    id: 'lx03_240',
    name: 'LX03 Lagertyp 240',
    scriptPath: path.join(process.cwd(), 'backend', 'scripts', 'workflows', 'LX03', 'LX03_240_Workflow.py'),
    env: {},
    logDir: path.join(process.cwd(), 'backend', 'workflows', 'logs'),
  },
  lx03_200: {
    id: 'lx03_200',
    name: 'LX03 Lagertyp 200',
    scriptPath: path.join(process.cwd(), 'backend', 'scripts', 'workflows', 'LX03', 'LX03_200_Workflow.py'),
    env: {},
    logDir: path.join(process.cwd(), 'backend', 'workflows', 'logs'),
  },
  lx03_rest: {
    id: 'lx03_rest',
    name: 'LX03 Lagertyp Rest',
    scriptPath: path.join(process.cwd(), 'backend', 'scripts', 'workflows', 'LX03', 'LX03_Rest_Workflow.py'),
    env: {},
    logDir: path.join(process.cwd(), 'backend', 'workflows', 'logs'),
  },
};

export function getWorkflowDef(id: WorkflowId): WorkflowDefinition | undefined {
  return workflowRegistry[id];
}

export function listWorkflows() {
  return Object.values(workflowRegistry).map(w => ({ id: w.id, name: w.name }));
}