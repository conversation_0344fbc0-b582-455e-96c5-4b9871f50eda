/**
 * AI Error Handling Module Exports
 * Centralized exports for all error handling functionality
 */

// Core error handling services
export { AIErrorHandler } from './AIErrorHandler';
export { AIOperationLogger } from './AIOperationLogger';

// Error types and interfaces
export type {
  AIServiceError,
  AIOperationLog,
  ErrorRecoveryStrategy,
  FallbackStrategy,
  ErrorHandlingConfig
} from '../../types/errors';

export {
  AIServiceErrorCode,
  AIServiceErrorSeverity
} from '../../types/errors';

// Error message utilities
export {
  AI_ERROR_MESSAGES,
  getErrorMessage,
  formatErrorForUser,
  formatErrorForTechnical
} from '../../utils/errorMessages';

// Error handling decorators
export {
  withErrorHandling,
  withRetry,
  withCache,
  withValidation,
  withPerformanceMonitoring,
  withAIServiceProtection
} from '../../decorators/errorHandling';

// React components
export { AIErrorBoundary, withAIErrorBoundary } from '../../components/error-handling/AIErrorBoundary';
export { AIErrorDisplay, AIErrorInline } from '../../components/error-handling/AIErrorDisplay';

// React hooks
export {
  useAIErrorHandler,
  useAIError,
  useAIErrorWithConfig
} from '../../hooks/useAIErrorHandler';

// Import the classes for use in utility functions
import { AIErrorHandler } from './AIErrorHandler';
import { AIOperationLogger } from './AIOperationLogger';
import { AIServiceErrorCode, AIServiceError } from '../../types/errors';

// Utility functions for common error handling patterns
export const createAIErrorHandler = (serviceName: string) => {
  const handler = AIErrorHandler.getInstance();
  const logger = AIOperationLogger.getInstance();

  return {
    createError: (code: AIServiceErrorCode, operation: string, originalError?: Error, context?: any) =>
      handler.createError(code, serviceName, operation, originalError, context),

    handleError: <T>(error: AIServiceError, input?: any) =>
      handler.handleError<T>(error, input, serviceName),

    startOperation: (operation: string, type: 'query' | 'optimization' | 'prediction' | 'analysis' | 'configuration', input?: any) =>
      logger.startOperation(serviceName, operation, type, input),

    completeOperation: (operationId: string, success: boolean, output?: any, error?: AIServiceError) =>
      logger.completeOperation(operationId, success, output, error),

    getServiceMetrics: () => logger.getPerformanceMetrics(serviceName),

    getServiceLogs: (limit?: number) => logger.getOperationLogs(serviceName, undefined, limit)
  };
};

// Pre-configured error handlers for common AI services
export const createRAGErrorHandler = () => createAIErrorHandler('RAGService');
export const createCuttingErrorHandler = () => createAIErrorHandler('CuttingOptimizer');
export const createInventoryErrorHandler = () => createAIErrorHandler('InventoryIntelligence');
export const createProcessErrorHandler = () => createAIErrorHandler('ProcessOptimizer');
export const createPredictiveErrorHandler = () => createAIErrorHandler('PredictiveAnalytics');
export const createReportingErrorHandler = () => createAIErrorHandler('ReportingService');
export const createSupplyChainErrorHandler = () => createAIErrorHandler('SupplyChainAnalytics');
export const createWarehouseErrorHandler = () => createAIErrorHandler('WarehouseOptimizer');