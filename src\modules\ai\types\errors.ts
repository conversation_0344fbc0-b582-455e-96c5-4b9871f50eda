/**
 * AI Module Error Types and Interfaces
 * Comprehensive error handling for all AI services
 */

export enum AIServiceErrorCode {
  // General AI Service Errors
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  INITIALIZATION_FAILED = 'INIT<PERSON>LIZATION_FAILED',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  
  // OpenRouter API Errors
  API_KEY_INVALID = 'API_KEY_INVALID',
  API_RATE_LIMIT_EXCEEDED = 'API_RATE_LIMIT_EXCEEDED',
  API_REQUEST_FAILED = 'API_REQUEST_FAILED',
  API_TIMEOUT = 'API_TIMEOUT',
  MODEL_UNAVAILABLE = 'M<PERSON>EL_UNAVAILABLE',
  
  // Vector Database Errors
  VECTOR_STORAGE_FAILED = 'VECTOR_STORAGE_FAILED',
  VECTOR_SEARCH_FAILED = 'VECTOR_SEARCH_FAILED',
  EMBEDDING_GENERATION_FAILED = 'EMBEDDING_GENERATION_FAILED',
  INDEX_CREATION_FAILED = 'INDEX_CREATION_FAILED',
  
  // RAG Service Errors
  DOCUMENT_INDEXING_FAILED = 'DOCUMENT_INDEXING_FAILED',
  CONTEXT_RETRIEVAL_FAILED = 'CONTEXT_RETRIEVAL_FAILED',
  KNOWLEDGE_BASE_ERROR = 'KNOWLEDGE_BASE_ERROR',
  
  // Optimization Service Errors
  OPTIMIZATION_FAILED = 'OPTIMIZATION_FAILED',
  INSUFFICIENT_DATA = 'INSUFFICIENT_DATA',
  INVALID_PARAMETERS = 'INVALID_PARAMETERS',
  CALCULATION_ERROR = 'CALCULATION_ERROR',
  
  // Prediction Service Errors
  PREDICTION_FAILED = 'PREDICTION_FAILED',
  MODEL_TRAINING_FAILED = 'MODEL_TRAINING_FAILED',
  FORECAST_GENERATION_FAILED = 'FORECAST_GENERATION_FAILED',
  ANOMALY_DETECTION_FAILED = 'ANOMALY_DETECTION_FAILED',
  
  // Data Access Errors
  DATABASE_CONNECTION_FAILED = 'DATABASE_CONNECTION_FAILED',
  DATA_VALIDATION_FAILED = 'DATA_VALIDATION_FAILED',
  REPOSITORY_ERROR = 'REPOSITORY_ERROR',
  
  // Cache Errors
  CACHE_READ_FAILED = 'CACHE_READ_FAILED',
  CACHE_WRITE_FAILED = 'CACHE_WRITE_FAILED',
  CACHE_INVALIDATION_FAILED = 'CACHE_INVALIDATION_FAILED'
}

export enum AIServiceErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface AIServiceError extends Error {
  code: AIServiceErrorCode;
  severity: AIServiceErrorSeverity;
  service: string;
  operation: string;
  timestamp: Date;
  context?: Record<string, any>;
  originalError?: Error;
  recoverable: boolean;
  userMessage: string;
  technicalMessage: string;
}

export interface ErrorRecoveryStrategy {
  canRecover: (error: AIServiceError) => boolean;
  recover: (error: AIServiceError, context?: any) => Promise<any>;
  maxRetries: number;
  retryDelay: number;
}

export interface AIOperationLog {
  id: string;
  service: string;
  operation: string;
  operationType: 'query' | 'optimization' | 'prediction' | 'analysis' | 'configuration';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  success: boolean;
  inputData?: Record<string, any>;
  outputData?: Record<string, any>;
  error?: AIServiceError;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface ErrorHandlingConfig {
  enableLogging: boolean;
  enableRetries: boolean;
  maxRetries: number;
  retryDelay: number;
  enableFallbacks: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  enableUserNotifications: boolean;
  enableTechnicalLogs: boolean;
}

export interface FallbackStrategy<T = any> {
  name: string;
  canHandle: (error: AIServiceError) => boolean;
  execute: (originalInput: any, error: AIServiceError) => Promise<T>;
  priority: number;
}