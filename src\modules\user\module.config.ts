import { ModuleConfig } from '@/types/module';

/**
 * Konfiguration für das User-Modul
 * 
 * Enthält alle Einstellungen und Konfigurationen für Benutzereinstellungen
 */
export const userModuleConfig: ModuleConfig = {
  id: 'user',
  name: 'user',
  displayName: '<PERSON><PERSON><PERSON>',
  description: 'Benutzereinstellungen und Benutzerprofil',
  icon: 'User',
  baseRoute: '/user-settings',
  requiredRoles: ['<PERSON><PERSON><PERSON>', 'Administrator'],
  isEnabled: true,
  pages: [
    { id: 'settings', name: 'Einstellungen', route: '/user-settings', component: 'UserSettingsPage' }
  ]
};
