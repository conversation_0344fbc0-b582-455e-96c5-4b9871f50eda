/**
 * Supply Chain Optimization Types
 * 
 * Type definitions for supply chain optimization, delivery prediction,
 * supplier risk assessment, and logistics optimization.
 */

// Base Types
export interface Supplier {
  id: string;
  name: string;
  location: string;
  category: string;
  contactInfo: ContactInfo;
  performanceMetrics: SupplierPerformanceMetrics;
}

export interface ContactInfo {
  email: string;
  phone: string;
  address: string;
  contactPerson: string;
}

export interface SupplierPerformanceMetrics {
  onTimeDeliveryRate: number;
  qualityRate: number;
  responseTime: number; // hours
  financialStabilityScore: number; // 1-10
  reliabilityScore: number; // 1-10
  costCompetitiveness: number; // 1-10
}

// Delivery Prediction Types
export interface DeliveryPredictionRequest {
  supplierId: string;
  productType?: string;
  quantity?: number;
  urgency?: 'low' | 'medium' | 'high';
  destination?: string;
  timeRange?: { days: number };
}

export interface DeliveryTimePrediction {
  supplierId: string;
  predictedDeliveryTime: number; // days
  confidenceLevel: number; // percentage
  confidenceIntervals: {
    lower95: number;
    upper95: number;
    lower68: number;
    upper68: number;
  };
  delayRisks: RiskFactor[];
  factors: string[];
  lastUpdated: Date;
}

// Risk Assessment Types
export interface SupplierEvaluationRequest {
  supplierId: string;
  productCategories?: string[];
  assessmentDate?: Date;
  timeRange?: { days: number };
}

export interface SupplierRiskAssessment {
  supplierId: string;
  overallRiskScore: number; // 1-10 scale
  riskCategory: 'low' | 'medium' | 'high' | 'critical';
  riskFactors: RiskFactor[];
  recommendations: string[];
  alternativeSuppliers: string[];
  assessmentDate: Date;
  validUntil: Date;
}

export interface RiskFactor {
  type: 'delivery_performance' | 'quality' | 'financial_stability' | 'geographic' | 'weather' | 'supplier_reliability' | 'capacity' | 'insufficient_data';
  severity: 'low' | 'medium' | 'high';
  probability: number; // 0-1
  description: string;
  impact: number; // estimated impact in days or cost
}

// Logistics Optimization Types
export interface LogisticsOptimizationRequest {
  requestId: string;
  deliveries: DeliveryRequest[];
  constraints: LogisticsConstraints;
  optimizationGoals: OptimizationGoal[];
}

export interface DeliveryRequest {
  deliveryId: string;
  destination: string;
  items: DeliveryItem[];
  priority: 'low' | 'medium' | 'high';
  timeWindow?: TimeWindow;
  specialRequirements?: string[];
}

export interface DeliveryItem {
  itemId: string;
  quantity: number;
  weight: number;
  dimensions: Dimensions;
  fragile?: boolean;
  temperatureControlled?: boolean;
}

export interface Dimensions {
  length: number;
  width: number;
  height: number;
  volume: number;
}

export interface TimeWindow {
  earliest: Date;
  latest: Date;
  preferred?: Date;
}

export interface LogisticsConstraints {
  maxVehicleCapacity: number;
  maxRouteDistance: number;
  maxRouteTime: number; // minutes
  availableVehicles: number;
  driverWorkingHours: number;
  fuelCostPerKm: number;
  laborCostPerHour: number;
}

export interface OptimizationGoal {
  type: 'minimize_cost' | 'minimize_time' | 'minimize_distance' | 'maximize_efficiency';
  weight: number; // relative importance
}

export interface LogisticsOptimization {
  requestId: string;
  currentRoutes: DeliveryRoute[];
  optimizedRoutes: OptimizedRoute[];
  benefits: OptimizationBenefits;
  contingencyPlans: ContingencyPlan[];
  implementationSteps: string[];
  optimizationDate: Date;
}

export interface DeliveryRoute {
  routeId: string;
  deliveries: DeliveryRequest[];
  totalDistance: number; // km
  estimatedTime: number; // minutes
  cost: number; // euros
}

export interface OptimizedRoute extends DeliveryRoute {
  optimizationMethod: string;
  confidence: number; // 0-1
  alternativeRoutes?: AlternativeRoute[];
}

export interface AlternativeRoute {
  routeId: string;
  description: string;
  totalDistance: number;
  estimatedTime: number;
  efficiency: number;
  tradeoffs: string[];
}

export interface OptimizationBenefits {
  distanceReduction: number; // percentage
  timeReduction: number; // percentage
  costSavings: number; // percentage
}

export interface ContingencyPlan {
  planId: string;
  trigger: string;
  alternativeRoute: OptimizedRoute;
  additionalCost: number; // euros
  additionalTime: number; // minutes
}

// Supply Chain Disruption Types
export interface DisruptionAnalysisRequest {
  disruptionId: string;
  disruptionType: 'weather' | 'supplier_failure' | 'transportation' | 'demand_spike' | 'quality_issue' | 'geopolitical' | 'pandemic' | 'natural_disaster';
  affectedRegion?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  estimatedDuration?: number; // days
  description?: string;
}

export interface SupplyChainDisruption {
  disruptionId: string;
  disruptionType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  impactAssessment: DisruptionImpact;
  affectedSuppliers: string[];
  affectedRoutes: string[];
  mitigationStrategies: MitigationStrategy[];
  recoveryTimeline: RecoveryTimeline;
  financialImpact: FinancialImpact;
  analysisDate: Date;
}

export interface DisruptionImpact {
  affectedSuppliers: number;
  affectedRoutes: number;
  estimatedDuration: number; // days
  businessImpact: 'low' | 'medium' | 'high' | 'critical';
}

export interface MitigationStrategy {
  strategyId: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  estimatedEffectiveness: number; // 0-1
  implementationTime: number; // hours
  cost: number; // euros
}

export interface RecoveryTimeline {
  phases: RecoveryPhase[];
}

export interface RecoveryPhase {
  phase: string;
  duration: number; // hours
  description: string;
  milestones?: string[];
}

export interface FinancialImpact {
  estimatedCost: number; // euros
  potentialRevenueLoss: number; // euros
  mitigationCost: number; // euros
}

// Analytics and Reporting Types
export interface SupplyChainAnalytics {
  overallPerformance: PerformanceMetrics;
  supplierPerformance: SupplierPerformanceAnalytics[];
  deliveryPerformance: DeliveryPerformanceAnalytics;
  riskAnalysis: RiskAnalytics;
  trends: TrendAnalysis[];
}

export interface PerformanceMetrics {
  onTimeDeliveryRate: number;
  averageDeliveryTime: number;
  costEfficiency: number;
  supplierReliability: number;
  overallRiskScore: number;
}

export interface SupplierPerformanceAnalytics {
  supplierId: string;
  supplierName: string;
  performanceScore: number;
  riskScore: number;
  deliveryReliability: number;
  qualityScore: number;
  costCompetitiveness: number;
  trends: {
    performance: 'improving' | 'stable' | 'declining';
    risk: 'increasing' | 'stable' | 'decreasing';
  };
}

export interface DeliveryPerformanceAnalytics {
  averageDeliveryTime: number;
  onTimeRate: number;
  delayFrequency: number;
  commonDelayReasons: DelayReason[];
  seasonalPatterns: SeasonalPattern[];
}

export interface DelayReason {
  reason: string;
  frequency: number;
  averageDelay: number; // days
  impact: 'low' | 'medium' | 'high';
}

export interface SeasonalPattern {
  season: string;
  deliveryTimeFactor: number; // multiplier
  riskFactor: number; // multiplier
  description: string;
}

export interface RiskAnalytics {
  overallRiskLevel: 'low' | 'medium' | 'high' | 'critical';
  topRiskFactors: RiskFactor[];
  riskTrends: RiskTrend[];
  mitigationEffectiveness: number;
}

export interface RiskTrend {
  riskType: string;
  trend: 'increasing' | 'stable' | 'decreasing';
  changeRate: number; // percentage
  timeframe: string;
}

export interface TrendAnalysis {
  metric: string;
  trend: 'improving' | 'stable' | 'declining';
  changeRate: number; // percentage
  timeframe: string;
  dataPoints: TrendDataPoint[];
}

export interface TrendDataPoint {
  date: Date;
  value: number;
  context?: string;
}

// Configuration Types
export interface SupplyChainOptimizationConfig {
  predictionHorizon: number; // days
  riskThresholds: {
    low: number;
    medium: number;
    high: number;
  };
  optimizationParameters: {
    maxIterations: number;
    convergenceThreshold: number;
    timeLimit: number; // seconds
  };
  alertSettings: {
    riskThreshold: number;
    deliveryDelayThreshold: number; // days
    qualityThreshold: number;
  };
}

// Result Types
export interface SupplyChainOptimizationResult {
  optimizationId: string;
  requestType: 'delivery_prediction' | 'risk_assessment' | 'logistics_optimization' | 'disruption_analysis';
  results: any; // Specific result type based on requestType
  confidence: number;
  recommendations: string[];
  nextSteps: string[];
  validUntil: Date;
  createdAt: Date;
}

// Alert and Recommendation Types
export interface SupplyChainAlert {
  alertId: string;
  message: string;
  details: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  supplierName?: string;
  recommendations: string[];
  acknowledged: boolean;
  createdAt: Date;
}

export interface SupplyChainRecommendation {
  recommendationId: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  expectedBenefit: string;
  timeframe: string;
  estimatedSavings: number;
  estimatedCost: number;
  implementationSteps: string[];
  createdAt: Date;
}

// Export all types
export type {
  // Main service types
  DeliveryTimePrediction,
  SupplierRiskAssessment,
  LogisticsOptimization,
  SupplyChainDisruption,
  
  // Request types
  DeliveryPredictionRequest,
  SupplierEvaluationRequest,
  LogisticsOptimizationRequest,
  DisruptionAnalysisRequest,
  
  // Analytics types
  SupplyChainAnalytics,
  SupplyChainOptimizationResult,
  SupplyChainAlert,
  SupplyChainRecommendation
};