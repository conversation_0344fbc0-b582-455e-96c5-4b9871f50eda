/**
 * Performance Monitor Component
 * 
 * Monitors application performance metrics and provides insights
 * for optimization opportunities.
 */

import { useEffect, useState } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage?: {
    used: number;
    total: number;
    limit: number;
  };
  bundleLoadTimes: Record<string, number>;
}

interface PerformanceMonitorProps {
  enabled?: boolean;
  showOverlay?: boolean;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = process.env.NODE_ENV === 'development',
  showOverlay = false,
  onMetricsUpdate
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    bundleLoadTimes: {}
  });

  useEffect(() => {
    if (!enabled) return;

    const measurePerformance = () => {
      // Measure page load time
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const loadTime = navigation.loadEventEnd - navigation.fetchStart;

      // Measure memory usage (if available)
      let memoryUsage;
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        memoryUsage = {
          used: Math.round(memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
        };
      }

      // Measure bundle load times
      const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const bundleLoadTimes: Record<string, number> = {};
      
      resourceEntries
        .filter(entry => entry.name.includes('.js') || entry.name.includes('.css'))
        .forEach(entry => {
          const fileName = entry.name.split('/').pop() || entry.name;
          bundleLoadTimes[fileName] = entry.responseEnd - entry.requestStart;
        });

      const newMetrics: PerformanceMetrics = {
        loadTime,
        renderTime: performance.now(), // Time since navigation start
        memoryUsage,
        bundleLoadTimes
      };

      setMetrics(newMetrics);
      onMetricsUpdate?.(newMetrics);
    };

    // Initial measurement
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
    }

    // Periodic measurements
    const interval = setInterval(measurePerformance, 30000); // Every 30 seconds

    return () => {
      window.removeEventListener('load', measurePerformance);
      clearInterval(interval);
    };
  }, [enabled, onMetricsUpdate]);

  // Performance overlay for development
  if (!enabled || !showOverlay) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs font-mono z-50 max-w-sm">
      <div className="font-bold mb-2">Performance Metrics</div>
      
      <div className="space-y-1">
        <div>Load Time: {metrics.loadTime.toFixed(0)}ms</div>
        <div>Render Time: {metrics.renderTime.toFixed(0)}ms</div>
        
        {metrics.memoryUsage && (
          <div>
            Memory: {metrics.memoryUsage.used}MB / {metrics.memoryUsage.total}MB
            <div className="text-xs text-gray-300">
              Limit: {metrics.memoryUsage.limit}MB
            </div>
          </div>
        )}
        
        {Object.keys(metrics.bundleLoadTimes).length > 0 && (
          <div>
            <div className="font-semibold mt-2">Bundle Load Times:</div>
            {Object.entries(metrics.bundleLoadTimes)
              .slice(0, 5) // Show top 5
              .map(([bundle, time]) => (
                <div key={bundle} className="text-xs">
                  {bundle}: {time.toFixed(0)}ms
                </div>
              ))}
          </div>
        )}
      </div>
      
      <div className="mt-2 pt-2 border-t border-gray-600">
        <div className="text-xs text-gray-300">
          Updated: {new Date().toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

/**
 * Performance Hook for component-level monitoring
 */
export const usePerformanceMonitor = (componentName: string) => {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] ${componentName} render time: ${renderTime.toFixed(2)}ms`);
      }
    };
  });
};

/**
 * Bundle Size Reporter
 */
export const reportBundleSize = () => {
  if (process.env.NODE_ENV !== 'development') return;
  
  const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
  const jsFiles = resourceEntries.filter(entry => entry.name.includes('.js'));
  const cssFiles = resourceEntries.filter(entry => entry.name.includes('.css'));
  
  console.group('Bundle Size Report');
  
  console.log('JavaScript Bundles:');
  jsFiles.forEach(file => {
    const fileName = file.name.split('/').pop();
    const size = file.transferSize ? `${(file.transferSize / 1024).toFixed(1)}KB` : 'Unknown';
    const loadTime = `${(file.responseEnd - file.requestStart).toFixed(0)}ms`;
    console.log(`  ${fileName}: ${size} (${loadTime})`);
  });
  
  console.log('CSS Files:');
  cssFiles.forEach(file => {
    const fileName = file.name.split('/').pop();
    const size = file.transferSize ? `${(file.transferSize / 1024).toFixed(1)}KB` : 'Unknown';
    const loadTime = `${(file.responseEnd - file.requestStart).toFixed(0)}ms`;
    console.log(`  ${fileName}: ${size} (${loadTime})`);
  });
  
  const totalJS = jsFiles.reduce((sum, file) => sum + (file.transferSize || 0), 0);
  const totalCSS = cssFiles.reduce((sum, file) => sum + (file.transferSize || 0), 0);
  
  console.log(`Total JS: ${(totalJS / 1024).toFixed(1)}KB`);
  console.log(`Total CSS: ${(totalCSS / 1024).toFixed(1)}KB`);
  console.log(`Total Assets: ${((totalJS + totalCSS) / 1024).toFixed(1)}KB`);
  
  console.groupEnd();
};

export default PerformanceMonitor;