# Knowledge Base Management System

## Overview

The Knowledge Base Management System provides comprehensive document ingestion, preprocessing, indexing, and retrieval capabilities for the RAG (Retrieval-Augmented Generation) system. It manages the complete lifecycle of documents from ingestion to vector indexing.

## Features

### Document Ingestion and Preprocessing
- **Multi-format Support**: Handles TXT, Markdown, JSON, and CSV documents
- **Content Preprocessing**: Cleans and optimizes content for better chunking and indexing
- **Validation**: Comprehensive document validation with configurable constraints
- **Duplicate Detection**: Content hashing to prevent duplicate document ingestion

### Document Chunking
- **Intelligent Chunking**: Creates optimal chunks with configurable size and overlap
- **Sentence Boundary Detection**: Breaks chunks at natural sentence boundaries when possible
- **Metadata Preservation**: Maintains document metadata throughout the chunking process
- **Token Estimation**: Estimates token count for each chunk for better resource management

### Vector Indexing
- **Automatic Indexing**: Optional automatic vector indexing during document ingestion
- **Batch Processing**: Efficient batch processing for multiple documents
- **Embedding Integration**: Seamless integration with the EmbeddingService
- **Vector Storage**: Stores vectors with comprehensive metadata in the VectorDatabaseService

### Document Management
- **CRUD Operations**: Complete Create, Read, Update, Delete operations for documents
- **Metadata Updates**: Update document metadata without reprocessing content
- **Content Updates**: Update document content with automatic reprocessing and reindexing
- **Bulk Operations**: Support for bulk document operations

### Health Monitoring and Statistics
- **Health Checks**: Comprehensive health monitoring for the knowledge base
- **Statistics**: Detailed statistics on documents, chunks, and vectors
- **Performance Metrics**: Processing time and efficiency metrics
- **Error Tracking**: Comprehensive error tracking and reporting

## Architecture

### Service Dependencies
- **VectorDatabaseService**: For vector storage and similarity search
- **EmbeddingService**: For generating embeddings from text content
- **SQLite Database**: For document and chunk metadata storage

### Database Schema

#### knowledge_documents
- Document metadata and content storage
- Processing status tracking
- Content hashing for duplicate detection

#### document_chunks
- Individual document chunks with position information
- Token count estimation
- Embedding ID references

#### processing_queue
- Asynchronous processing queue management
- Priority-based processing
- Error tracking and retry logic

## Configuration

```typescript
interface KnowledgeBaseConfig {
  chunkSize?: number;           // Default: 1000
  chunkOverlap?: number;        // Default: 200
  maxDocumentSize?: number;     // Default: 10MB
  supportedFormats?: string[];  // Default: ['txt', 'md', 'json', 'csv']
  autoIndex?: boolean;          // Default: true
  preprocessingEnabled?: boolean; // Default: true
}
```

## Usage Examples

### Basic Document Ingestion
```typescript
const knowledgeService = new KnowledgeBaseService(vectorService, embeddingService);
await knowledgeService.initialize();

const document = {
  title: 'Operations Manual',
  content: 'This is the operations manual content...',
  documentType: 'txt',
  metadata: { department: 'dispatch', category: 'manual' }
};

const result = await knowledgeService.ingestDocument(document);
console.log(`Processed: ${result.chunksCreated} chunks, ${result.vectorsIndexed} vectors`);
```

### Document Management
```typescript
// Retrieve document
const document = await knowledgeService.getDocument(documentId);

// Update document metadata
await knowledgeService.updateDocument(documentId, {
  metadata: { department: 'cutting', updated: true }
});

// Update document content (triggers reprocessing)
await knowledgeService.updateDocument(documentId, {
  content: 'Updated content...'
});

// Delete document
await knowledgeService.deleteDocument(documentId);
```

### Health Monitoring
```typescript
// Get knowledge base statistics
const stats = await knowledgeService.getKnowledgeBaseStats();
console.log(`Documents: ${stats.totalDocuments}, Chunks: ${stats.totalChunks}`);

// Get health status
const health = await knowledgeService.getHealthStatus();
console.log(`Healthy: ${health.isHealthy}, Errors: ${health.errors.length}`);
```

### Batch Operations
```typescript
// List documents with filtering
const documents = await knowledgeService.listDocuments({
  department: 'dispatch',
  documentType: 'txt',
  limit: 50
});

// Reindex all documents
const reindexResult = await knowledgeService.reindexAllDocuments();
console.log(`Reindexed: ${reindexResult.processed}, Errors: ${reindexResult.errors}`);
```

## Content Preprocessing

### Markdown Processing
- Removes markdown syntax while preserving content structure
- Strips headers, bold/italic formatting, code blocks, and links
- Maintains readability for better chunking

### JSON Processing
- Pretty-formats JSON for better readability
- Handles invalid JSON gracefully
- Preserves structure for semantic understanding

### CSV Processing
- Converts tabular data to readable text format
- Creates key-value pairs from headers and data
- Improves semantic understanding of structured data

## Error Handling

### Validation Errors
- Empty title or content
- Unsupported document types
- Document size limits
- Invalid configuration parameters

### Processing Errors
- Embedding service failures (graceful degradation)
- Vector service failures (continues without indexing)
- Database connection issues
- Concurrent processing conflicts

### Recovery Mechanisms
- Automatic retry for transient failures
- Fallback processing modes
- Error logging and reporting
- Health check integration

## Performance Considerations

### Chunking Strategy
- Configurable chunk size and overlap
- Sentence boundary detection for natural breaks
- Token count estimation for resource planning
- Parallel chunk processing

### Database Optimization
- Proper indexing for fast queries
- Transaction management for consistency
- Connection pooling for efficiency
- WAL mode for better concurrency

### Memory Management
- Streaming processing for large documents
- Batch operations for efficiency
- Resource cleanup and garbage collection
- Memory usage monitoring

## Integration with RAG System

The Knowledge Base Service integrates seamlessly with the RAG system:

1. **Document Ingestion**: Documents are processed and indexed automatically
2. **Vector Search**: Indexed vectors are searchable via the VectorDatabaseService
3. **Context Retrieval**: RAG queries retrieve relevant document chunks
4. **Source Attribution**: Original document information is preserved for citations

## Testing

### Unit Tests
- Document validation logic
- Content preprocessing functions
- Chunking algorithms
- Configuration validation

### Integration Tests
- End-to-end document processing
- RAG system integration
- Multi-department knowledge base
- Performance and scalability testing

### Error Handling Tests
- Service failure scenarios
- Concurrent processing
- Data consistency validation
- Recovery mechanism testing

## Monitoring and Maintenance

### Health Checks
- Service initialization status
- Database connectivity
- Dependent service health
- Processing queue status

### Statistics Tracking
- Document processing metrics
- Chunk and vector counts
- Processing time analysis
- Error rate monitoring

### Maintenance Operations
- Index rebuilding
- Database optimization
- Cleanup operations
- Performance tuning

## Future Enhancements

### Planned Features
- Advanced document format support (PDF, DOCX)
- Semantic chunking based on content structure
- Automatic document classification
- Version control for document updates
- Advanced search and filtering capabilities

### Performance Improvements
- Distributed processing for large document sets
- Advanced caching strategies
- Incremental indexing updates
- Real-time processing capabilities