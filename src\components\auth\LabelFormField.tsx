import React, { forwardRef, useId } from 'react';
import { useMotionPreference } from '@/hooks/useMotionPreference';
import '@/styles/label-auth.css';

/**
 * Label form field props interface
 */
export interface LabelFormFieldProps {
  label: string;
  type?: 'text' | 'email' | 'password' | 'tel' | 'url';
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  error?: string | null;
  required?: boolean;
  disabled?: boolean;
  autoComplete?: string;
  'aria-describedby'?: string;
  className?: string;
}

/**
 * LabelFormField component for label-styled form inputs
 * Implements requirements 2.1, 2.2, 2.3, 2.4, 4.3, 6.4
 */
export const LabelFormField = forwardRef<HTMLInputElement, LabelFormFieldProps>(
  (
    {
      label,
      type = 'text',
      placeholder,
      value,
      onChange,
      onBlur,
      error = null,
      required = false,
      disabled = false,
      autoComplete,
      'aria-describedby': ariaDescribedBy,
      className = '',
      ...props
    },
    ref
  ) => {
    const { prefersReducedMotion } = useMotionPreference();
    const fieldId = useId();
    const errorId = error ? `${fieldId}-error` : undefined;

    /**
     * Handle input change events
     */
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value);
    };

    /**
     * Build aria-describedby attribute
     */
    const buildAriaDescribedBy = () => {
      const ids = [ariaDescribedBy, errorId].filter(Boolean);
      return ids.length > 0 ? ids.join(' ') : undefined;
    };

    /**
     * Get input classes based on state
     */
    const getInputClasses = () => {
      const baseClasses = ['label-input'];
      
      if (error) {
        baseClasses.push('border-red-500');
      }
      
      if (disabled) {
        baseClasses.push('opacity-50', 'cursor-not-allowed');
      }
      
      if (prefersReducedMotion) {
        baseClasses.push('transition-none');
      }

      return baseClasses.join(' ');
    };

    return (
      <div className={`label-form-field ${className}`}>
        {/* Field Label */}
        <label
          htmlFor={fieldId}
          className="label-form-label"
          style={{
            display: 'block',
            marginBottom: '0.25rem',
          }}
        >
          {label}
          {required && (
            <span 
              className="text-red-500 ml-1" 
              aria-label="required field"
              title="Pflichtfeld"
            >
              *
            </span>
          )}
        </label>

        {/* Input Field */}
        <input
          ref={ref}
          id={fieldId}
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          onBlur={onBlur}
          required={required}
          disabled={disabled}
          autoComplete={autoComplete}
          className={getInputClasses()}
          aria-describedby={buildAriaDescribedBy()}
          aria-invalid={error ? 'true' : 'false'}
          {...props}
        />

        {/* Error Message */}
        {error && (
          <div
            id={errorId}
            className="label-error"
            role="alert"
            aria-live="polite"
            style={{
              marginTop: '0.25rem',
              fontSize: '0.8rem',
            }}
          >
            {error}
          </div>
        )}
      </div>
    );
  }
);

LabelFormField.displayName = 'LabelFormField';

/**
 * Label form button props interface
 */
export interface LabelFormButtonProps {
  children: React.ReactNode;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  className?: string;
  'aria-label'?: string;
}

/**
 * LabelFormButton component for label-styled form buttons
 */
export const LabelFormButton = forwardRef<HTMLButtonElement, LabelFormButtonProps>(
  (
    {
      children,
      type = 'button',
      variant = 'primary',
      disabled = false,
      loading = false,
      onClick,
      className = '',
      'aria-label': ariaLabel,
      ...props
    },
    ref
  ) => {
    const { prefersReducedMotion } = useMotionPreference();

    /**
     * Get button classes based on variant and state
     */
    const getButtonClasses = () => {
      const baseClasses = ['label-button'];
      
      if (variant === 'primary') {
        baseClasses.push('label-button-primary');
      }
      
      if (disabled || loading) {
        baseClasses.push('opacity-50', 'cursor-not-allowed');
      }
      
      if (prefersReducedMotion) {
        baseClasses.push('transition-none');
      }

      return baseClasses.join(' ');
    };

    /**
     * Handle button click
     */
    const handleClick = () => {
      if (!disabled && !loading && onClick) {
        onClick();
      }
    };

    return (
      <button
        ref={ref}
        type={type}
        className={`${getButtonClasses()} ${className}`}
        disabled={disabled || loading}
        onClick={handleClick}
        aria-label={ariaLabel}
        aria-busy={loading}
        {...props}
      >
        {loading ? (
          <span className="flex items-center justify-center gap-2">
            <span
              style={{
                width: '16px',
                height: '16px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderTop: '2px solid currentColor',
                borderRadius: '50%',
                animation: prefersReducedMotion ? 'none' : 'spin 1s linear infinite',
              }}
              aria-hidden="true"
            />
            Lädt...
          </span>
        ) : (
          children
        )}
      </button>
    );
  }
);

LabelFormButton.displayName = 'LabelFormButton';

export default LabelFormField;