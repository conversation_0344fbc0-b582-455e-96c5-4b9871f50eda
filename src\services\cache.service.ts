/**
 * In-Memory Cache Service
 * 
 * Implementiert einen effizienten Cache für API-Responses mit
 * automatischer Invalidierung und Memory-Management.
 */

/**
 * Cache-Eintrag mit Metadaten
 */
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time To Live in Millisekunden
  accessCount: number;
  lastAccessed: number;
}

/**
 * Cache-Konfiguration
 */
interface CacheConfig {
  defaultTTL: number; // Standard TTL in Millisekunden
  maxEntries: number; // Maximale Anzahl von Cache-Einträgen
  cleanupInterval: number; // Intervall für Cleanup in Millisekunden
  enableLogging: boolean; // Cache-Logging aktivieren
}

/**
 * Cache-Statistiken
 */
interface CacheStats {
  hits: number;
  misses: number;
  totalRequests: number;
  hitRate: number;
  memoryUsage: number;
  entryCount: number;
}

/**
 * In-Memory Cache Service Klasse
 */
export class CacheService {
  private cache = new Map<string, CacheEntry<any>>();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    totalRequests: 0,
    hitRate: 0,
    memoryUsage: 0,
    entryCount: 0
  };
  
  private cleanupTimer: NodeJS.Timeout | null = null;
  private config: CacheConfig;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5 Minuten
      maxEntries: 1000,
      cleanupInterval: 60 * 1000, // 1 Minute
      enableLogging: false,
      ...config
    };

    this.startCleanupTimer();
    this.log('Cache Service initialisiert');
  }

  /**
   * Daten aus dem Cache abrufen
   * @param key Cache-Schlüssel
   * @returns Cached data oder undefined
   */
  get<T>(key: string): T | undefined {
    this.stats.totalRequests++;
    
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      this.log('Cache MISS', key);
      return undefined;
    }

    // Prüfe TTL
    const now = Date.now();
    if (now > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      this.updateHitRate();
      this.log('Cache EXPIRED', key);
      return undefined;
    }

    // Update Access-Metadaten
    entry.accessCount++;
    entry.lastAccessed = now;
    
    this.stats.hits++;
    this.updateHitRate();
    this.log('Cache HIT', key);
    
    return entry.data;
  }

  /**
   * Daten in den Cache einfügen
   * @param key Cache-Schlüssel
   * @param data Zu cachende Daten
   * @param ttl Time To Live (optional, verwendet defaultTTL wenn nicht angegeben)
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const actualTTL = ttl || this.config.defaultTTL;

    // Prüfe Cache-Größe und führe eviction durch wenn nötig
    if (this.cache.size >= this.config.maxEntries) {
      this.evictLeastUsed();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      ttl: actualTTL,
      accessCount: 1,
      lastAccessed: now
    };

    this.cache.set(key, entry);
    this.updateStats();
    this.log('Cache SET', key, `TTL: ${actualTTL}ms`);
  }

  /**
   * Cache-Eintrag löschen
   * @param key Cache-Schlüssel
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.updateStats();
      this.log('Cache DELETE', key);
    }
    return deleted;
  }

  /**
   * Cache-Einträge mit Prefix löschen
   * @param prefix Schlüssel-Prefix
   */
  deleteByPrefix(prefix: string): number {
    let deletedCount = 0;
    
    for (const key of Array.from(this.cache.keys())) {
      if (key.startsWith(prefix)) {
        this.cache.delete(key);
        deletedCount++;
      }
    }
    
    if (deletedCount > 0) {
      this.updateStats();
      this.log('Cache DELETE BY PREFIX', prefix, `${deletedCount} entries deleted`);
    }
    
    return deletedCount;
  }

  /**
   * Gesamten Cache leeren
   */
  clear(): void {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      totalRequests: 0,
      hitRate: 0,
      memoryUsage: 0,
      entryCount: 0
    };
    this.log('Cache CLEAR', 'All entries cleared');
  }

  /**
   * Cache-Statistiken abrufen
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Cache-Einträge nach TTL aufräumen
   */
  cleanup(): number {
    const now = Date.now();
    let expiredCount = 0;

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      this.updateStats();
      this.log('Cache CLEANUP', `${expiredCount} expired entries removed`);
    }

    return expiredCount;
  }

  /**
   * Least Recently Used Eviction
   */
  private evictLeastUsed(): void {
    if (this.cache.size === 0) return;

    let oldestKey = '';
    let oldestAccess = Date.now();

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (entry.lastAccessed < oldestAccess) {
        oldestAccess = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.log('Cache EVICTION', oldestKey, 'LRU evicted');
    }
  }

  /**
   * Hit-Rate aktualisieren
   */
  private updateHitRate(): void {
    this.stats.hitRate = this.stats.totalRequests > 0 
      ? (this.stats.hits / this.stats.totalRequests) * 100 
      : 0;
  }

  /**
   * Cache-Statistiken aktualisieren
   */
  private updateStats(): void {
    this.stats.entryCount = this.cache.size;
    this.stats.memoryUsage = this.estimateMemoryUsage();
    this.updateHitRate();
  }

  /**
   * Geschätzte Memory-Nutzung berechnen
   */
  private estimateMemoryUsage(): number {
    let totalSize = 0;
    
    for (const [key, entry] of Array.from(this.cache.entries())) {
      // Grobe Schätzung: Key + JSON-Serialized Data + Metadaten
      totalSize += key.length * 2; // UTF-16
      totalSize += JSON.stringify(entry.data).length * 2;
      totalSize += 64; // Metadaten overhead
    }
    
    return totalSize;
  }

  /**
   * Cleanup-Timer starten
   */
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Cache Service beenden
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    
    this.clear();
    this.log('Cache Service destroyed');
  }

  /**
   * Cache-Logging
   */
  private log(action: string, key?: string, details?: string): void {
    if (!this.config.enableLogging) return;
    
    const logMessage = [
      `[CACHE] ${action}`,
      key && `Key: ${key}`,
      details && `Details: ${details}`,
      `(${this.stats.entryCount} entries, ${this.stats.hitRate.toFixed(1)}% hit rate)`
    ].filter(Boolean).join(' | ');
    
    console.log(logMessage);
  }
}

/**
 * Cache-Schlüssel-Generator
 */
export class CacheKeyGenerator {
  /**
   * Generiert einen Cache-Schlüssel für API-Endpunkte
   * @param endpoint API-Endpunkt
   * @param params Query-Parameter
   * @returns Cache-Schlüssel
   */
  static forApiEndpoint(endpoint: string, params?: Record<string, any>): string {
    const baseKey = `api:${endpoint}`;
    
    if (!params || Object.keys(params).length === 0) {
      return baseKey;
    }

    // Parameter sortieren für konsistente Schlüssel
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');

    return `${baseKey}:${sortedParams}`;
  }

  /**
   * Generiert einen Cache-Schlüssel für Datentypen
   * @param dataType Datentyp (z.B. 'service-level', 'picking')
   * @param filters Filter-Parameter
   * @returns Cache-Schlüssel
   */
  static forDataType(dataType: string, filters?: Record<string, any>): string {
    return this.forApiEndpoint(`data:${dataType}`, filters);
  }

  /**
   * Generiert einen Prefix für Cache-Invalidierung
   * @param category Cache-Kategorie
   * @returns Cache-Prefix
   */
  static prefixFor(category: 'api' | 'data' | 'user'): string {
    return `${category}:`;
  }
}

// Singleton Cache Instance
let cacheInstance: CacheService | null = null;

/**
 * Globale Cache-Instanz abrufen oder erstellen
 * @param config Cache-Konfiguration (nur beim ersten Aufruf verwendet)
 * @returns Cache-Service Instanz
 */
export function getCache(config?: Partial<CacheConfig>): CacheService {
  if (!cacheInstance) {
    cacheInstance = new CacheService(config);
  }
  return cacheInstance;
}

/**
 * Cache-Service zerstören (für Testing oder Cleanup)
 */
export function destroyCache(): void {
  if (cacheInstance) {
    cacheInstance.destroy();
    cacheInstance = null;
  }
}