// Zentraler Helper für Asset-URLs, 100% Electron-/Vite-kompatibel
// Nutzt Vite's Transform von new URL(..., import.meta.url) für Build-time URL-Generierung
// und funktioniert zuverlässig unter file:// im Electron-Renderer.

export function assetUrl(fileName: string): string {
  // Pfad relativ zu dieser Datei (src/utils) -> src/assets
  return new URL(`../assets/${fileName}`, import.meta.url).href;
}
