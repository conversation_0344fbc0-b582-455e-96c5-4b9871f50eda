import * as React from "react"
import {
  IconChartBar,
  IconDashboard,
  IconDatabase,
  IconFolder,
  IconInnerShadowTop,
  IconListDetails,
  IconSettings,
  IconRobot,
} from "@tabler/icons-react"
import { useAuthContext } from "@/contexts/AuthContext"

import { NavMain } from "@/components/nav-main"
import { NavSecondary } from "@/components/nav-secondary"
import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

const data = {
  navMain: [
    {
      title: "Dashboard",
      url: "/",
      icon: IconDashboard,
    },
    {
      title: "Versand",
      url: "/dispatch",
      icon: IconListDetails,
    },
    {
      title: "Ablängerei",
      url: "/cutting",
      icon: IconChartBar,
    },
    {
      title: "Wareneingang",
      url: "/incoming-goods",
      icon: IconFolder,
    },
    {
      title: "KI-Assistent",
      url: "/modules/ai",
      icon: IconRobot,
    },
    {
      title: "Wissensdatenbank",
      url: "/rag",
      icon: IconDatabase,
    },
    {
      title: "System",
      url: "/system",
      icon: IconDatabase,
    },
  ],
  navSecondary: [
    {
      title: "Einstellungen",
      url: "/settings",
      icon: IconSettings,
    },
  ],
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, isAuthenticated } = useAuthContext()

  // Filter navigation items based on user role
  const getFilteredNavItems = () => {
    if (!isAuthenticated || !user) return data.navMain.filter(item => item.title === "Dashboard");
    
    const userRole = Array.isArray(user.roles) ? user.roles[0] : user.roles;
    
    // All users can see Dashboard, Versand, Ablängerei, Wareneingang
    let filteredItems = data.navMain.filter(item => 
      ['Dashboard', 'Versand', 'Ablängerei', 'Wareneingang'].includes(item.title)
    );
    
    // Add AI Assistant and Knowledge Base for all authenticated users
    if (userRole) {
      filteredItems.push(data.navMain.find(item => item.title === 'KI-Assistent')!);
      filteredItems.push(data.navMain.find(item => item.title === 'Wissensdatenbank')!);
    }
    
    // Add System for Benutzer and Administrator
    if (userRole === 'Benutzer' || userRole === 'Administrator') {
      filteredItems.push(data.navMain.find(item => item.title === 'System')!);
    }
    
    return filteredItems;
  };

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="/">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">JOZI1 Lapp Dashboard</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={getFilteredNavItems()} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
