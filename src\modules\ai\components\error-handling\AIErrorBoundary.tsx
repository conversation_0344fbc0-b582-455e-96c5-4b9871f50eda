/**
 * AI Error Boundary Component
 * Catches and handles React errors in AI components
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>riangle, Refresh<PERSON>w, Bug } from 'lucide-react';
import { AIErrorHandler } from '../../services/error-handling/AIErrorHandler';
import { AIServiceErrorCode } from '../../types/errors';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  serviceName?: string;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

export class AIErrorBoundary extends Component<Props, State> {
  private errorHandler: AIErrorHandler;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
    this.errorHandler = AIErrorHandler.getInstance();
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });

    // Create structured AI error
    const aiError = this.errorHandler.createError(
      AIServiceErrorCode.SERVICE_UNAVAILABLE,
      this.props.serviceName || 'AIComponent',
      'render',
      error,
      {
        componentStack: errorInfo.componentStack,
        errorBoundary: true
      }
    );

    // Log the error
    console.error('[AI Error Boundary] Component error caught:', {
      error: aiError,
      errorInfo,
      props: this.props
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  private handleReportError = () => {
    const { error, errorInfo, errorId } = this.state;

    // Create error report
    const errorReport = {
      errorId,
      timestamp: new Date().toISOString(),
      error: {
        message: error?.message,
        stack: error?.stack,
        name: error?.name
      },
      errorInfo: {
        componentStack: errorInfo?.componentStack
      },
      serviceName: this.props.serviceName,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Log error report
    console.error('[AI Error Report]', errorReport);

    // In a real application, you would send this to your error reporting service
    alert('Fehlerbericht wurde erstellt. Fehler-ID: ' + errorId);
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-700">
              <AlertTriangle className="h-5 w-5" />
              KI-Service Fehler
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Ein unerwarteter Fehler ist aufgetreten</AlertTitle>
              <AlertDescription>
                Der KI-Service konnte nicht geladen werden. Dies kann durch eine
                temporäre Störung oder ein Konfigurationsproblem verursacht werden.
              </AlertDescription>
            </Alert>

            <div className="text-sm text-gray-600 space-y-2">
              <p><strong>Fehler-ID:</strong> {this.state.errorId}</p>
              <p><strong>Service:</strong> {this.props.serviceName || 'Unbekannt'}</p>
              <p><strong>Zeit:</strong> {new Date().toLocaleString('de-DE')}</p>
            </div>

            {/* Vite verwendet import.meta.env anstelle von process.env */}
            {import.meta.env.DEV && this.state.error && (
              <details className="text-xs bg-gray-100 p-2 rounded">
                <summary className="cursor-pointer font-medium">
                  Technische Details (nur in Entwicklung)
                </summary>
                <pre className="mt-2 whitespace-pre-wrap">
                  {this.state.error.message}
                  {'\n\n'}
                  {this.state.error.stack}
                  {this.state.errorInfo?.componentStack && (
                    '\n\nComponent Stack:\n' + this.state.errorInfo.componentStack
                  )}
                </pre>
              </details>
            )}

            <div className="flex gap-2">
              <Button
                onClick={this.handleRetry}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Erneut versuchen
              </Button>

              <Button
                onClick={this.handleReportError}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Bug className="h-4 w-4" />
                Fehler melden
              </Button>
            </div>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component for wrapping components with error boundary
 */
export function withAIErrorBoundary<P = {}>(
  WrappedComponent: React.ComponentType<P>,
  serviceName?: string,
  fallback?: ReactNode
) {
  const WithErrorBoundary = (props: P) => (
    <AIErrorBoundary serviceName={serviceName} fallback={fallback}>
      <WrappedComponent {...props} />
    </AIErrorBoundary>
  );

  WithErrorBoundary.displayName = `withAIErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithErrorBoundary;
}