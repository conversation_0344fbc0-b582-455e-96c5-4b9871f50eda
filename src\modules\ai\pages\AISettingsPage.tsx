/**
 * AI Settings Page
 * 
 * Page component for managing AI module settings and configuration
 */

import React from 'react';
import { useNavigate } from '@tanstack/react-router';
import { ArrowLeft } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { AISettingsManager, AIModuleSettings } from '../components/settings/AISettingsManager';

const AISettingsPage: React.FC = () => {
  const navigate = useNavigate();

  const handleSettingsChange = (settings: AIModuleSettings) => {
    // In a real implementation, this would save settings to backend/storage
    console.log('AI Settings updated:', settings);
    
    // Show success notification
    // toast.success('Einstellungen erfolgreich gespeichert');
  };

  const handleBack = () => {
    navigate({ to: '/modules/ai' });
  };

  return (
    <div className="w-full bg-bg min-h-screen p-8">
      <div>
        {/* Back Navigation */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={handleBack}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-4 w-4" />
            Zurück zum KI-Dashboard
          </Button>
        </div>

        {/* Settings Manager */}
        <AISettingsManager
          onSettingsChange={handleSettingsChange}
          initialSettings={{
            // Load initial settings from storage/backend
            // This would typically come from a settings service
          }}
        />
      </div>
    </div>
  );
};

export default AISettingsPage;