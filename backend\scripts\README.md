# Backend Scripts

This directory contains utility scripts for database management and setup.

## Category Population Script

### Purpose
Populates the RAG database `categories` table with predefined categories for the JOZI1 Lapp Dashboard application.

### Categories Created
- **dispatch** (Versand) - Versand- und Lieferoperationen
- **cutting** (Ablängerei) - Schneid- und Ablängoperationen  
- **incoming-goods** (Wareneingang) - Wareneingang und Annahme
- **system** (System) - Systemdokumentation und -konfiguration
- **quality** (Qualität) - Qualitätssicherung und -kontrolle
- **maintenance** (Wartung) - Wartung und Instandhaltung
- **app** (App Leitfaden) - Anwendungsleitfäden und Benutzerhandbücher
- **procedures** (Verfahren) - Betriebsverfahren und Arbeitsanweisungen
- **kpi** (KPI & Metriken) - Leistungskennzahlen und Metriken

### Usage

#### Using npm script (recommended):
```bash
cd backend
npm run rag:populate-categories
```

#### Direct execution:
```bash
cd backend
ts-node scripts/populate-categories.ts
# or
node scripts/populate-categories.js
```

### Features
- **Idempotent**: Safe to run multiple times
- **Update existing**: Updates existing categories with new descriptions
- **Create missing**: Creates categories that don't exist
- **Detailed logging**: Shows progress and summary
- **Error handling**: Continues processing even if individual categories fail

### Output
The script provides detailed console output including:
- Number of existing categories found
- Progress for each category (created/updated)
- Final summary with counts
- List of all categories in the database

### Requirements
- RAG database must be initialized (`npm run rag:init`)
- Prisma RAG client must be generated
- Node.js environment with TypeScript support

### Error Handling
- Individual category failures don't stop the entire process
- Detailed error messages for troubleshooting
- Graceful database connection cleanup