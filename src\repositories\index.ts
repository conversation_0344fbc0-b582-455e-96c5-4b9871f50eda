/**
 * Repository Index
 * 
 * Zentrale Exportdatei für alle Repositories.
 * Stellt eine einheitliche Schnittstelle zur Verfügung.
 */

// Base Repository
export * from './base.repository';

// Domain-spezifische Repositories
export * from './warehouse.repository';
export * from './production.repository';
export * from './system.repository';

// Repository-Instanzen für direkten Import
import { WarehouseRepository } from './warehouse.repository';
import { ProductionRepository } from './production.repository';
import { SystemRepository } from './system.repository';

/**
 * Repository-Manager für alle Datenabstraktionen
 * Singleton-Pattern für konsistente Repository-Instanzen
 */
export class RepositoryManager {
  private static instance: RepositoryManager;
  
  public readonly warehouse: WarehouseRepository;
  public readonly production: ProductionRepository;
  public readonly system: SystemRepository;
  
  private constructor() {
    this.warehouse = new WarehouseRepository();
    this.production = new ProductionRepository();
    this.system = new SystemRepository();
    
    console.log('RepositoryManager initialisiert mit allen Domain-Repositories');
  }
  
  /**
   * Singleton-Instanz abrufen
   */
  public static getInstance(): RepositoryManager {
    if (!RepositoryManager.instance) {
      RepositoryManager.instance = new RepositoryManager();
    }
    return RepositoryManager.instance;
  }
  
  /**
   * Cache für alle Repositories invalidieren
   */
  public invalidateAllCache(): void {
    this.warehouse.invalidateAllCache();
    this.production.invalidateAllCache();
    this.system.invalidateAllCache();
    console.log('Cache für alle Repositories invalidiert');
  }
  
  /**
   * Gesamte Dashboard-Daten für alle Domains abrufen
   */
  public async getDashboardData(filter?: { startDate?: string; endDate?: string }): Promise<{
    warehouse: any;
    production: any;
    system: any;
    timestamp: string;
  }> {
    const [warehouseStats, productionStats, systemDashboard] = await Promise.all([
      this.warehouse.getOverallStats(filter),
      this.production.getOverallProductionStats(),
      this.system.getSystemDashboard(filter)
    ]);
    
    return {
      warehouse: warehouseStats,
      production: productionStats,
      system: systemDashboard,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * Repository-Health-Check durchführen
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    repositories: Record<string, boolean>;
    errors: string[];
  }> {
    const results = {
      status: 'healthy' as 'healthy' | 'degraded' | 'unhealthy',
      repositories: {} as Record<string, boolean>,
      errors: [] as string[]
    };
    
    // Test alle Repository-Domains
    const tests = [
      { name: 'warehouse.we', test: () => this.warehouse.we.getAll() },
      { name: 'production.schnitte', test: () => this.production.schnitte.getAll() },
      { name: 'system.serviceLevel', test: () => this.system.serviceLevel.getAll() }
    ];
    
    for (const { name, test } of tests) {
      try {
        await Promise.race([
          test(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Timeout')), 5000)
          )
        ]);
        results.repositories[name] = true;
      } catch (error) {
        results.repositories[name] = false;
        results.errors.push(`${name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
    
    const failedCount = Object.values(results.repositories).filter(Boolean).length;
    const totalCount = Object.keys(results.repositories).length;
    
    if (failedCount === 0) {
      results.status = 'unhealthy';
    } else if (failedCount < totalCount) {
      results.status = 'degraded';
    }
    
    return results;
  }
}

/**
 * Globale Repository-Instanz für einfachen Zugriff
 */
export const repositories = RepositoryManager.getInstance();

/**
 * Convenience-Exports für häufig verwendete Repositories
 */
export const warehouseRepo = repositories.warehouse;
export const productionRepo = repositories.production;
export const systemRepo = repositories.system;

/**
 * Repository-Utility-Funktionen
 */
export const repositoryUtils = {
  /**
   * Cache-Management für alle Repositories
   */
  cache: {
    invalidateAll: () => repositories.invalidateAllCache(),
    invalidateWarehouse: () => repositories.warehouse.invalidateAllCache(),
    invalidateProduction: () => repositories.production.invalidateAllCache(),
    invalidateSystem: () => repositories.system.invalidateAllCache()
  },
  
  /**
   * Batch-Operationen für mehrere Repositories
   */
  batch: {
    getDashboard: (filter?: { startDate?: string; endDate?: string }) => 
      repositories.getDashboardData(filter),
    healthCheck: () => repositories.healthCheck()
  }
};