import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface AskJaszContextData {
  type: 'page' | 'component' | 'kpi' | 'chart' | 'form' | 'workflow';
  title: string;
  description: string;
  functionality?: string[];
  currentData?: Record<string, any>;
  additionalContext?: string;
}

export interface AskJaszPromptTemplate {
  id: string;
  name: string;
  template: string;
  category: 'explanation' | 'analysis' | 'functionality' | 'help';
}

interface AskJaszContextValue {
  currentContext: AskJaszContextData | null;
  setContext: (context: AskJaszContextData | null) => void;
  openChatWithPrompt: (prompt: string, context?: AskJaszContextData) => void;
  isAvailable: boolean;
}

const AskJaszContext = createContext<AskJaszContextValue | undefined>(undefined);

export const useAskJasz = () => {
  const context = useContext(AskJaszContext);
  if (context === undefined) {
    throw new Error('useAskJasz must be used within an AskJaszProvider');
  }
  return context;
};

interface AskJaszProviderProps {
  children: ReactNode;
  onOpenChat?: (prompt: string, context?: AskJaszContextData) => void;
}

export const AskJaszProvider: React.FC<AskJaszProviderProps> = ({ 
  children, 
  onOpenChat 
}) => {
  const [currentContext, setCurrentContext] = useState<AskJaszContextData | null>(null);

  const setContext = (context: AskJaszContextData | null) => {
    setCurrentContext(context);
  };

  const openChatWithPrompt = (prompt: string, context?: AskJaszContextData) => {
    if (onOpenChat) {
      onOpenChat(prompt, context || currentContext || undefined);
    }
  };

  const value: AskJaszContextValue = {
    currentContext,
    setContext,
    openChatWithPrompt,
    isAvailable: !!onOpenChat
  };

  return (
    <AskJaszContext.Provider value={value}>
      {children}
    </AskJaszContext.Provider>
  );
};

// Predefined prompt templates
export const PROMPT_TEMPLATES: Record<string, AskJaszPromptTemplate> = {
  PAGE_EXPLANATION: {
    id: 'page_explanation',
    name: 'Seiten-Erklärung',
    template: 'Erkläre mir diese {title} Seite. Was kann ich hier alles machen? {functionality}',
    category: 'explanation'
  },
  KPI_EXPLANATION: {
    id: 'kpi_explanation', 
    name: 'KPI-Erklärung',
    template: 'Erkläre mir diese KPI "{title}" mit dem aktuellen Wert {currentValue}. Was bedeutet dieser Wert und wie wird er berechnet?',
    category: 'explanation'
  },
  CHART_ANALYSIS: {
    id: 'chart_analysis',
    name: 'Diagramm-Analyse',
    template: 'Analysiere mir dieses {title} Diagramm. Was zeigen die aktuellen Daten und welche Trends sind erkennbar? {additionalContext}',
    category: 'analysis'
  },
  WORKFLOW_EXPLANATION: {
    id: 'workflow_explanation',
    name: 'Workflow-Erklärung', 
    template: 'Erkläre mir diesen {title} Workflow Schritt für Schritt. Wie funktioniert der Prozess und was sind die wichtigsten Punkte?',
    category: 'explanation'
  },
  FORM_HELP: {
    id: 'form_help',
    name: 'Formular-Hilfe',
    template: 'Hilf mir beim Ausfüllen dieses {title} Formulars. Welche Felder sind wichtig und was muss ich beachten?',
    category: 'help'
  },
  MONITORING_HELP: {
    id: 'monitoring_help', 
    name: 'Monitoring-Hilfe',
    template: 'Erkläre mir diese {title} Monitoring-Ansicht. Wie interpretiere ich die Statusanzeigen und wann sollte ich handeln?',
    category: 'explanation'
  },
  TROUBLESHOOTING: {
    id: 'troubleshooting',
    name: 'Fehlerbehebung',
    template: 'Ich sehe hier ein Problem bei {title}. Wie kann ich das analysieren und welche Schritte zur Fehlerbehebung gibt es?',
    category: 'help'
  },
  DATA_ANALYSIS: {
    id: 'data_analysis',
    name: 'Datenanalyse',
    template: 'Analysiere die aktuellen Daten für {title}. Was bedeuten die Werte und welche Handlungsempfehlungen gibt es?',
    category: 'analysis'
  },
  DASHBOARD_OVERVIEW: {
    id: 'dashboard_overview',
    name: 'Dashboard-Übersicht',
    template: 'Erkläre mir das {title} Dashboard. Was sind die wichtigsten KPIs und wie interpretiere ich die Visualisierungen?',
    category: 'explanation'
  },
  KPI_CALCULATION: {
    id: 'kpi_calculation',
    name: 'KPI-Berechnung',
    template: 'Wie wird die KPI "{title}" mit dem Wert {currentValue} berechnet? Was sind die Eingangsdaten und Formeln?',
    category: 'explanation'
  },
  MACHINE_EFFICIENCY: {
    id: 'machine_efficiency',
    name: 'Maschineneffizienz',
    template: 'Erkläre mir die Maschineneffizienz-Metriken für {title}. Wie wird die Effizienz berechnet und was bedeuten die Werte?',
    category: 'explanation'
  },
  CSR_ANALYSIS: {
    id: 'csr_analysis',
    name: 'CSR-Analyse',
    template: 'Analysiere die Customer Service Rate Daten für {title}. Welche Trends sind erkennbar und was bedeuten sie für die Supply Chain?',
    category: 'analysis'
  },
  TREND_INTERPRETATION: {
    id: 'trend_interpretation',
    name: 'Trend-Interpretation',
    template: 'Interpretiere die Trends im {title}. Was zeigen die Daten über die Entwicklung und welche Maßnahmen sollten ergriffen werden?',
    category: 'analysis'
  }
};

// Utility function to generate context-aware prompts
export const generatePrompt = (
  template: AskJaszPromptTemplate,
  context: AskJaszContextData
): string => {
  let prompt = template.template;
  
  // Replace placeholders with context data
  prompt = prompt.replace('{title}', context.title);
  prompt = prompt.replace('{description}', context.description);
  
  if (context.functionality) {
    prompt = prompt.replace('{functionality}', 
      `Verfügbare Funktionen: ${context.functionality.join(', ')}`);
  }
  
  if (context.currentData?.value !== undefined) {
    prompt = prompt.replace('{currentValue}', String(context.currentData.value));
  }
  
  if (context.additionalContext) {
    prompt = prompt.replace('{additionalContext}', context.additionalContext);
  }
  
  // Clean up any remaining placeholders
  prompt = prompt.replace(/\{[^}]+\}/g, '');
  
  return prompt.trim();
};

// Context builders for common use cases
export const createPageContext = (
  title: string,
  functionality: string[],
  additionalContext?: string
): AskJaszContextData => ({
  type: 'page',
  title,
  description: `Die ${title} Seite der Leitstand Anwendung`,
  functionality,
  additionalContext
});

export const createKpiContext = (
  title: string,
  currentValue: any,
  description?: string
): AskJaszContextData => ({
  type: 'kpi',
  title,
  description: description || `KPI-Metrik für ${title}`,
  currentData: { value: currentValue }
});

export const createChartContext = (
  title: string,
  chartType: string,
  dataContext?: string
): AskJaszContextData => ({
  type: 'chart',
  title,
  description: `${chartType} Diagramm für ${title}`,
  additionalContext: dataContext
});

export const createWorkflowContext = (
  title: string,
  status?: string,
  steps?: string[]
): AskJaszContextData => ({
  type: 'workflow',
  title,
  description: `Workflow für ${title}`,
  functionality: steps,
  currentData: status ? { status } : undefined
});