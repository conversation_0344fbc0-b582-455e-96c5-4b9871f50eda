import React from 'react';
import { Area, Area<PERSON>hart, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { Badge } from '@/components/ui/badge';
import { TrendingUp } from 'lucide-react';

interface QueryPerformance {
  avg: number;
  count: number;
  successRate: number;
}

interface PerformanceStats {
  totalRequests: number;
  averageResponseTime: number;
  successRate: number;
  cacheHitRate: number;
  intentAccuracy: number;
  queryPerformance: {
    stoerungen: QueryPerformance;
    dispatch: QueryPerformance;
    cutting: QueryPerformance;
  };
}

interface PerformanceStatsChartProps {
  stats: PerformanceStats | null;
}

export function PerformanceStatsChart({ stats }: PerformanceStatsChartProps) {
  const chartConfig = {
    responseTime: {
      label: "Antwortzeit",
      color: "hsl(var(--chart-1))",
    },
    successRate: {
      label: "Erfolgsrate",
      color: "hsl(var(--chart-2))",
    },
    count: {
      label: "Anzahl Queries",
      color: "hsl(var(--chart-3))",
    },
  } satisfies ChartConfig;

  if (!stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Query Performance</CardTitle>
          <CardDescription>Keine Daten verfügbar</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">Keine Performance-Daten verfügbar</div>
        </CardContent>
      </Card>
    );
  }

  // Transform data for the chart
  const chartData = Object.entries(stats.queryPerformance).map(([source, metrics]) => ({
    source: source === 'stoerungen' ? 'Störungen' :
      source === 'dispatch' ? 'Versand' :
        source === 'cutting' ? 'Ablängerei' : source,
    responseTime: Math.round(metrics.avg),
    successRate: Math.round(metrics.successRate * 100),
    count: metrics.count,
    rawSource: source
  }));

  const getPerformanceStatus = (successRate: number, responseTime: number) => {
    if (successRate >= 95 && responseTime < 1000) return 'excellent';
    if (successRate >= 90 && responseTime < 2000) return 'good';
    if (successRate >= 80 && responseTime < 3000) return 'fair';
    return 'poor';
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'excellent':
        return <Badge className="bg-green-100 text-green-800">Ausgezeichnet</Badge>;
      case 'good':
        return <Badge className="bg-blue-100 text-blue-800">Gut</Badge>;
      case 'fair':
        return <Badge variant="outline">Akzeptabel</Badge>;
      case 'poor':
        return <Badge variant="destructive">Verbesserung nötig</Badge>;
      default:
        return <Badge variant="outline">Unbekannt</Badge>;
    }
  };

  return (
    <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <CardTitle>Query Performance nach Datenquelle</CardTitle>
        <CardDescription>
          Antwortzeiten und Erfolgsraten der verschiedenen Datenquellen
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Performance Chart */}
        <div className="mb-6">
          <ChartContainer config={chartConfig}>
            <AreaChart
              accessibilityLayer
              data={chartData}
              margin={{
                left: 12,
                right: 12,
              }}
            >
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="source"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                tick={{ fontSize: 12 }}
              />
              <YAxis
                tickLine={false}
                axisLine={false}
                tick={{ fontSize: 12 }}
              />
              <ChartTooltip
                cursor={false}
                content={<ChartTooltipContent
                  formatter={(value, name) => [
                    name === 'responseTime' ? `${value}ms` : `${value}%`,
                    name === 'responseTime' ? 'Antwortzeit' : 'Erfolgsrate'
                  ]}
                />}
              />
              <defs>
                <linearGradient id="fillResponseTime" x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor="var(--color-responseTime)"
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor="var(--color-responseTime)"
                    stopOpacity={0.1}
                  />
                </linearGradient>
                <linearGradient id="fillSuccessRate" x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor="var(--color-successRate)"
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor="var(--color-successRate)"
                    stopOpacity={0.1}
                  />
                </linearGradient>
              </defs>
              <Area
                dataKey="successRate"
                type="natural"
                fill="url(#fillSuccessRate)"
                fillOpacity={0.4}
                stroke="var(--color-successRate)"
                strokeWidth={2}
                stackId="a"
              />
              <Area
                dataKey="responseTime"
                type="natural"
                fill="url(#fillResponseTime)"
                fillOpacity={0.4}
                stroke="var(--color-responseTime)"
                strokeWidth={2}
                stackId="b"
              />
            </AreaChart>
          </ChartContainer>
        </div>

        {/* Performance Summary Table */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Performance Übersicht</h4>
          <div className="space-y-3">
            {chartData.map((item) => {
              const status = getPerformanceStatus(item.successRate, item.responseTime);
              return (
                <div key={item.rawSource} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="font-medium">{item.source}</div>
                    {getStatusBadge(status)}
                  </div>
                  <div className="flex items-center gap-6 text-sm text-muted-foreground">
                    <div className="text-center">
                      <div className="font-medium text-foreground">{item.count}</div>
                      <div>Queries</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-foreground">{item.responseTime}ms</div>
                      <div>Antwortzeit</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-foreground">{item.successRate}%</div>
                      <div>Erfolgsrate</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Overall Performance Insights */}
        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <h4 className="text-sm font-medium mb-2">Performance Insights</h4>
          <div className="space-y-2 text-sm text-muted-foreground">
            {chartData.length > 0 && (
              <>
                <div>
                  • Schnellste Datenquelle: {chartData.reduce((prev, current) =>
                    prev.responseTime < current.responseTime ? prev : current
                  ).source} ({chartData.reduce((prev, current) =>
                    prev.responseTime < current.responseTime ? prev : current
                  ).responseTime}ms)
                </div>
                <div>
                  • Zuverlässigste Datenquelle: {chartData.reduce((prev, current) =>
                    prev.successRate > current.successRate ? prev : current
                  ).source} ({chartData.reduce((prev, current) =>
                    prev.successRate > current.successRate ? prev : current
                  ).successRate}%)
                </div>
                <div>
                  • Gesamt Queries: {chartData.reduce((sum, item) => sum + item.count, 0)}
                </div>
              </>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Performance-Übersicht nach Datenquelle <TrendingUp className="h-4 w-4" />
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              {chartData.length > 0 && (
                <>
                  Schnellste: {chartData.reduce((prev, current) =>
                    prev.responseTime < current.responseTime ? prev : current
                  ).source} |
                  Zuverlässigste: {chartData.reduce((prev, current) =>
                    prev.successRate > current.successRate ? prev : current
                  ).source}
                </>
              )}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}