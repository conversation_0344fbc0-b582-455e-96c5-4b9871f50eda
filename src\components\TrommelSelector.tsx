"use client"

import { useState, useCallback, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CheckCircle, Search, Loader2 } from "lucide-react"
import TrommelrechnungService, { TrommelData } from "@/modules/ai/services/cutting/TrommelrechnungService"

// Props Interface für die TrommelSelector-Komponente
interface TrommelSelectorProps {
  verfügbareTrommeln?: TrommelData[];
  selectedTrommel?: string;
  onTrommelChange?: (value: string) => void;
  trommelSuchOpen?: boolean;
  onTrommelSuchOpenChange?: (open: boolean) => void;
  trommelSuchWert?: string;
  onTrommelSuchWertChange?: (value: string) => void;
  onTrommelSelect?: (trommel: TrommelData) => void;
  disabled?: boolean;
  selectedCableType?: string; // Ausgewählter Kabeltyp für bessere Fehlermeldungen
}

export default function TrommelSelector({ 
  verfügbareTrommeln = [],
  selectedTrommel = "",
  onTrommelChange,
  trommelSuchOpen = false,
  onTrommelSuchOpenChange,
  trommelSuchWert = "",
  onTrommelSuchWertChange,
  onTrommelSelect,
  disabled = false,
  selectedCableType = ""
}: TrommelSelectorProps) {
  // State für dynamische Suche
  const [searchResults, setSearchResults] = useState<TrommelData[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchTotal, setSearchTotal] = useState(0);
  
  // Debounced Search - Suche erst nach 300ms ohne weitere Eingabe
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  
  // Dynamische Suche für Trommeln implementieren
  const performSearch = useCallback(async (searchTerm: string) => {
    if (searchTerm.length < 2) {
      // Bei weniger als 2 Zeichen keine Suche durchführen
      setSearchResults([]);
      setSearchTotal(0);
      return;
    }
    
    setIsSearching(true);
    try {
      // Lade alle verfügbaren Trommeln und filtere lokal
      const alleTrommeln = await TrommelrechnungService.getVerfügbareTrommeln();
      const gefilterteTrommeln = alleTrommeln.filter((trommel: TrommelData) => 
        trommel.Trommelname.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trommel.Außendurchmesser.toString().includes(searchTerm)
      );
      
      setSearchResults(gefilterteTrommeln.slice(0, 100)); // Limitiere auf 100 Ergebnisse
       setSearchTotal(gefilterteTrommeln.length);
    } catch (error) {
      console.error('Fehler bei der Trommelsuche:', error);
      setSearchResults([]);
      setSearchTotal(0);
    } finally {
      setIsSearching(false);
    }
  }, []);
  
  // Debounced Search Handler
  const handleSearchChange = useCallback((value: string) => {
    onTrommelSuchWertChange?.(value);
    
    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    // Set new timeout for search
    const newTimeout = setTimeout(() => {
      performSearch(value);
    }, 300); // 300ms Debounce
    
    setSearchTimeout(newTimeout);
  }, [searchTimeout, onTrommelSuchWertChange, performSearch]);
  
  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);
  
  // Bestimme welche Trommeln angezeigt werden sollen
  const trommelnToShow = trommelSuchWert.length >= 2 ? searchResults : verfügbareTrommeln.slice(0, 100);
  
  // Verwende die dynamischen Suchergebnisse oder die ersten 100 Trommeln
  const displayedTrommeln = trommelnToShow;
  const hasMoreResults = trommelSuchWert.length >= 2 ? searchTotal > 100 : verfügbareTrommeln.length > 100;
  const totalResults = trommelSuchWert.length >= 2 ? searchTotal : verfügbareTrommeln.length;

  return (
    <div className="w-full">
      <Popover open={trommelSuchOpen} onOpenChange={onTrommelSuchOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={trommelSuchOpen}
            className="w-full justify-between text-left"
            disabled={disabled}
          >
            <span className="truncate flex-1 mr-2">
              {selectedTrommel && verfügbareTrommeln.length > 0
                ? (() => {
                    const trommel = verfügbareTrommeln.find((t) => t.Trommelname === selectedTrommel);
                    return trommel ? `${trommel.Trommelname} (Ø${trommel.Außendurchmesser}mm)` : "Trommel auswählen...";
                  })()
                : "Trommel auswählen..."}
            </span>
            <Search className="h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput 
              placeholder="Nach Trommelname oder Durchmesser suchen... (min. 2 Zeichen)"
              value={trommelSuchWert}
              onValueChange={handleSearchChange}
            />
            <CommandEmpty>
              {selectedCableType ? 
                `Keine Trommeln für Kabeltyp "${selectedCableType}" verfügbar.` : 
                "Keine Trommel gefunden."
              }
            </CommandEmpty>
            <CommandList>
              <CommandGroup>
                {isSearching && (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span className="text-sm text-muted-foreground">Suche läuft...</span>
                  </div>
                )}
                {!isSearching && trommelnToShow.map((trommel) => (
                  <CommandItem
                    key={trommel.Trommelname}
                    value={trommel.Trommelname}
                    onSelect={(currentValue) => {
                      onTrommelChange?.(currentValue === selectedTrommel ? "" : currentValue);
                      const selectedTrom = trommelnToShow.find(t => t.Trommelname === currentValue);
                      if (selectedTrom && onTrommelSelect) {
                        onTrommelSelect(selectedTrom);
                      }
                      onTrommelSuchOpenChange?.(false);
                      onTrommelSuchWertChange?.("");
                    }}
                  >
                    <CheckCircle
                      className={`mr-2 h-4 w-4 ${
                        trommel.Trommelname === selectedTrommel ? "opacity-100" : "opacity-0"
                      }`}
                    />
                    <div className="flex flex-col">
                      <span className="font-medium">{trommel.Trommelname}</span>
                      <span className="text-sm text-gray-500">Ø {trommel.Außendurchmesser}mm, Wickelbreite: {trommel.Wickelbreite_mm}mm</span>
                    </div>
                  </CommandItem>
                ))}
                {/* Zeige Hinweis für dynamische Suche oder lokale Filterung */}
                {!isSearching && trommelSuchWert.length >= 2 && searchTotal > 100 && (
                  <div className="px-2 py-1 text-xs text-gray-500 border-t">
                    {searchTotal} Ergebnisse gefunden. Nur die ersten 100 werden angezeigt.
                  </div>
                )}
                {!isSearching && trommelSuchWert.length < 2 && verfügbareTrommeln.length > 100 && (
                  <div className="px-2 py-1 text-xs text-gray-500 border-t">
                    Nur die ersten 100 Trommeln werden angezeigt. Verwenden Sie die Suche für spezifischere Ergebnisse.
                  </div>
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}