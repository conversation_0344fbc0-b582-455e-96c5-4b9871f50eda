import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AIDocumentationService } from '../AIDocumentationService';

describe('AIDocumentationService', () => {
  let service: AIDocumentationService;

  beforeEach(async () => {
    service = new AIDocumentationService();
    await service.initialize();
  });

  afterEach(async () => {
    await service.cleanup();
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      const newService = new AIDocumentationService();
      await expect(newService.initialize()).resolves.not.toThrow();
    });

    it('should load documentation sections', async () => {
      const sections = await service.getAllSections();
      expect(sections).toBeDefined();
      expect(Array.isArray(sections)).toBe(true);
      expect(sections.length).toBeGreaterThan(0);
    });

    it('should load tutorials', async () => {
      const tutorials = await service.getAllTutorials();
      expect(tutorials).toBeDefined();
      expect(Array.isArray(tutorials)).toBe(true);
      expect(tutorials.length).toBeGreaterThan(0);
    });
  });

  describe('documentation management', () => {
    it('should get section by id', async () => {
      const sections = await service.getAllSections();
      if (sections.length > 0) {
        const firstSection = sections[0];
        const foundSection = await service.getSectionById(firstSection.id);
        expect(foundSection).toEqual(firstSection);
      }
    });

    it('should return null for non-existent section', async () => {
      const section = await service.getSectionById('non-existent-id');
      expect(section).toBeNull();
    });

    it('should get sections by category', async () => {
      const overviewSections = await service.getSectionsByCategory('overview');
      expect(Array.isArray(overviewSections)).toBe(true);
      overviewSections.forEach(section => {
        expect(section.category).toBe('overview');
      });
    });

    it('should search documentation', async () => {
      const results = await service.searchDocumentation('AI');
      expect(Array.isArray(results)).toBe(true);
      
      // Results should contain sections with 'AI' in title, content, or tags
      results.forEach(section => {
        const hasAI = 
          section.title.toLowerCase().includes('ai') ||
          section.content.toLowerCase().includes('ai') ||
          section.tags.some(tag => tag.toLowerCase().includes('ai'));
        expect(hasAI).toBe(true);
      });
    });

    it('should search with options', async () => {
      const results = await service.searchDocumentation('optimization', {
        category: 'features',
        difficulty: 'intermediate',
        limit: 2
      });
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeLessThanOrEqual(2);
      
      results.forEach(section => {
        expect(section.category).toBe('features');
        expect(section.difficulty).toBe('intermediate');
      });
    });
  });

  describe('tutorial management', () => {
    it('should get tutorial by id', async () => {
      const tutorials = await service.getAllTutorials();
      if (tutorials.length > 0) {
        const firstTutorial = tutorials[0];
        const foundTutorial = await service.getTutorialById(firstTutorial.id);
        expect(foundTutorial).toEqual(firstTutorial);
      }
    });

    it('should return null for non-existent tutorial', async () => {
      const tutorial = await service.getTutorialById('non-existent-id');
      expect(tutorial).toBeNull();
    });

    it('should get tutorials by category', async () => {
      const basicsTutorials = await service.getTutorialsByCategory('basics');
      expect(Array.isArray(basicsTutorials)).toBe(true);
      basicsTutorials.forEach(tutorial => {
        expect(tutorial.category).toBe('basics');
      });
    });

    it('should get tutorials by difficulty', async () => {
      const beginnerTutorials = await service.getTutorialsByDifficulty('beginner');
      expect(Array.isArray(beginnerTutorials)).toBe(true);
      beginnerTutorials.forEach(tutorial => {
        expect(tutorial.difficulty).toBe('beginner');
      });
    });

    it('should get recommended tutorials for beginners', async () => {
      const recommendations = await service.getRecommendedTutorials('beginner', []);
      expect(Array.isArray(recommendations)).toBe(true);
      expect(recommendations.length).toBeLessThanOrEqual(5);
      
      recommendations.forEach(tutorial => {
        expect(tutorial.difficulty).toBe('beginner');
      });
    });

    it('should exclude completed tutorials from recommendations', async () => {
      const allTutorials = await service.getAllTutorials();
      if (allTutorials.length > 0) {
        const completedIds = [allTutorials[0].id];
        const recommendations = await service.getRecommendedTutorials('beginner', completedIds);
        
        recommendations.forEach(tutorial => {
          expect(completedIds).not.toContain(tutorial.id);
        });
      }
    });
  });

  describe('analytics and metrics', () => {
    it('should get documentation metrics', async () => {
      const metrics = await service.getDocumentationMetrics();
      
      expect(metrics).toBeDefined();
      expect(typeof metrics.totalSections).toBe('number');
      expect(typeof metrics.totalTutorials).toBe('number');
      expect(typeof metrics.completionRate).toBe('number');
      expect(Array.isArray(metrics.popularSections)).toBe(true);
      expect(Array.isArray(metrics.searchQueries)).toBe(true);
    });

    it('should track tutorial completion', async () => {
      const tutorialId = 'test-tutorial';
      await service.trackTutorialCompletion(tutorialId);
      
      // Verify tracking by checking metrics
      const metrics = await service.getDocumentationMetrics();
      expect(metrics.completionRate).toBeGreaterThanOrEqual(0);
    });

    it('should track section views', async () => {
      const sectionId = 'test-section';
      await service.trackSectionView(sectionId);
      
      // This should not throw and should update internal tracking
      expect(true).toBe(true);
    });
  });

  describe('documentation validation', () => {
    it('should validate documentation', async () => {
      const validation = await service.validateDocumentation();
      
      expect(validation).toBeDefined();
      expect(typeof validation.isValid).toBe('boolean');
      expect(Array.isArray(validation.errors)).toBe(true);
      expect(Array.isArray(validation.warnings)).toBe(true);
      expect(Array.isArray(validation.suggestions)).toBe(true);
    });

    it('should identify missing required fields', async () => {
      // This test would need to inject invalid data to properly test
      const validation = await service.validateDocumentation();
      
      // With the current mock data, validation should pass
      expect(validation.isValid).toBe(true);
      expect(validation.errors.length).toBe(0);
    });
  });

  describe('contextual help', () => {
    it('should generate contextual help', async () => {
      const contextualHelp = await service.generateContextualHelp(
        'ai-dashboard',
        'administrator',
        ['rag', 'cutting']
      );
      
      expect(Array.isArray(contextualHelp)).toBe(true);
      expect(contextualHelp.length).toBeLessThanOrEqual(5);
    });

    it('should filter contextual help by relevance', async () => {
      const contextualHelp = await service.generateContextualHelp(
        'cutting-page',
        'user',
        ['cutting']
      );
      
      // Results should be relevant to cutting
      contextualHelp.forEach(section => {
        const isRelevant = 
          section.tags.includes('cutting-page') ||
          section.tags.includes('user') ||
          section.tags.includes('cutting');
        expect(isRelevant).toBe(true);
      });
    });
  });

  describe('cleanup', () => {
    it('should cleanup successfully', async () => {
      await expect(service.cleanup()).resolves.not.toThrow();
      
      // After cleanup, data should be cleared
      const sections = await service.getAllSections();
      const tutorials = await service.getAllTutorials();
      
      expect(sections).toEqual([]);
      expect(tutorials).toEqual([]);
    });
  });

  describe('search functionality', () => {
    it('should handle empty search queries', async () => {
      const results = await service.searchDocumentation('');
      expect(Array.isArray(results)).toBe(true);
    });

    it('should handle special characters in search', async () => {
      const results = await service.searchDocumentation('AI-Module & Optimization');
      expect(Array.isArray(results)).toBe(true);
    });

    it('should be case insensitive', async () => {
      const lowerResults = await service.searchDocumentation('ai');
      const upperResults = await service.searchDocumentation('AI');
      const mixedResults = await service.searchDocumentation('Ai');
      
      expect(lowerResults).toEqual(upperResults);
      expect(upperResults).toEqual(mixedResults);
    });

    it('should track search history', async () => {
      await service.searchDocumentation('first search');
      await service.searchDocumentation('second search');
      
      const metrics = await service.getDocumentationMetrics();
      expect(metrics.searchQueries).toContain('first search');
      expect(metrics.searchQueries).toContain('second search');
    });
  });
});