/**
 * Test-Skript für Upload-Funktionalität
 * Testet ob statische Dateien korrekt bereitgestellt werden
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

// Test 1: Überprüfe ob der Server läuft
function testServerHealth() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:3001/api/health', (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        console.log('✅ Server Health Check:', res.statusCode === 200 ? 'OK' : 'FEHLER');
        if (res.statusCode === 200) {
          console.log('   Server Response:', JSON.parse(data).message);
        }
        resolve(res.statusCode === 200);
      });
    });
    req.on('error', (err) => {
      console.log('❌ Server Health Check Fehler:', err.message);
      reject(err);
    });
  });
}

// Test 2: Erstelle eine Test-Datei im uploads/stoerungen Ordner
function createTestFile() {
  const uploadsDir = path.join(__dirname, 'backend', 'uploads', 'stoerungen');
  
  // Erstelle Ordner falls nicht vorhanden
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log('📁 Upload-Ordner erstellt:', uploadsDir);
  }
  
  // Erstelle Test-Datei
  const testFilePath = path.join(uploadsDir, 'test-image.txt');
  fs.writeFileSync(testFilePath, 'Dies ist eine Test-Datei für Upload-Funktionalität\nZeitpunkt: ' + new Date().toISOString());
  console.log('📄 Test-Datei erstellt:', testFilePath);
  
  return 'test-image.txt';
}

// Test 3: Überprüfe ob statische Datei erreichbar ist
function testStaticFileAccess(filename) {
  return new Promise((resolve, reject) => {
    const url = `http://localhost:3001/api/uploads/stoerungen/${filename}`;
    console.log('🔍 Teste statische Datei:', url);
    
    const req = http.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ Statische Datei erreichbar!');
          console.log('   Content-Type:', res.headers['content-type']);
          console.log('   Datei-Inhalt:', data.substring(0, 100) + '...');
          resolve(true);
        } else {
          console.log('❌ Statische Datei nicht erreichbar. Status:', res.statusCode);
          resolve(false);
        }
      });
    });
    req.on('error', (err) => {
      console.log('❌ Fehler beim Zugriff auf statische Datei:', err.message);
      reject(err);
    });
  });
}

// Haupttest-Funktion
async function runTests() {
  console.log('🧪 Starte Upload-Funktionalitäts-Tests...');
  console.log('=' .repeat(50));
  
  try {
    // Test 1: Server Health
    await testServerHealth();
    
    // Test 2: Test-Datei erstellen
    const testFileName = createTestFile();
    
    // Kurz warten
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test 3: Statische Datei testen
    const staticFileWorking = await testStaticFileAccess(testFileName);
    
    console.log('=' .repeat(50));
    console.log('📊 Test-Ergebnisse:');
    console.log('   Server läuft:', '✅');
    console.log('   Statische Dateien:', staticFileWorking ? '✅' : '❌');
    
    if (staticFileWorking) {
      console.log('\n🎉 Alle Tests erfolgreich! Upload-Funktionalität sollte funktionieren.');
    } else {
      console.log('\n⚠️  Statische Dateien funktionieren nicht korrekt.');
    }
    
  } catch (error) {
    console.error('❌ Test-Fehler:', error.message);
  }
}

// Tests ausführen
runTests();