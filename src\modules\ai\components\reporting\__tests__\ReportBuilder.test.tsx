/**
 * Report Builder Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { ReportBuilder } from '../ReportBuilder';
import type { ReportTemplate } from '@/types/reporting';

// Mock the UI components
vi.mock('@/components/ui/card', () => ({
  Card: ({ children }: { children: React.ReactNode }) => <div data-testid="card">{children}</div>,
  CardContent: ({ children }: { children: React.ReactNode }) => <div data-testid="card-content">{children}</div>,
  CardDescription: ({ children }: { children: React.ReactNode }) => <div data-testid="card-description">{children}</div>,
  CardHeader: ({ children }: { children: React.ReactNode }) => <div data-testid="card-header">{children}</div>,
  CardTitle: ({ children }: { children: React.ReactNode }) => <div data-testid="card-title">{children}</div>
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} {...props}>
      {children}
    </button>
  )
}));

vi.mock('@/components/ui/input', () => ({
  Input: ({ value, onChange, ...props }: any) => (
    <input value={value} onChange={onChange} {...props} />
  )
}));

vi.mock('@/components/ui/textarea', () => ({
  Textarea: ({ value, onChange, ...props }: any) => (
    <textarea value={value} onChange={onChange} {...props} />
  )
}));

vi.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <select value={value} onChange={(e) => onValueChange(e.target.value)}>
      {children}
    </select>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ value, children }: any) => <option value={value}>{children}</option>,
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: () => <span>Select Value</span>
}));

vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div data-testid="dialog-content">{children}</div>,
  DialogDescription: ({ children }: any) => <div data-testid="dialog-description">{children}</div>,
  DialogHeader: ({ children }: any) => <div data-testid="dialog-header">{children}</div>,
  DialogTitle: ({ children }: any) => <div data-testid="dialog-title">{children}</div>
}));

vi.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, value, onValueChange }: any) => (
    <div data-testid="tabs" data-value={value}>
      {children}
    </div>
  ),
  TabsContent: ({ children, value }: any) => (
    <div data-testid={`tab-content-${value}`}>{children}</div>
  ),
  TabsList: ({ children }: any) => <div data-testid="tabs-list">{children}</div>,
  TabsTrigger: ({ children, value }: any) => (
    <button data-testid={`tab-trigger-${value}`}>{children}</button>
  )
}));

vi.mock('@/components/ui/switch', () => ({
  Switch: ({ checked, onCheckedChange, ...props }: any) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={(e) => onCheckedChange(e.target.checked)}
      {...props}
    />
  )
}));

vi.mock('@/components/ui/label', () => ({
  Label: ({ children, ...props }: any) => <label {...props}>{children}</label>
}));

vi.mock('@/components/ui/alert', () => ({
  Alert: ({ children }: any) => <div data-testid="alert">{children}</div>,
  AlertDescription: ({ children }: any) => <div data-testid="alert-description">{children}</div>
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children }: any) => <span data-testid="badge">{children}</span>
}));

// Mock the sub-components
vi.mock('../ReportSectionBuilder', () => ({
  ReportSectionBuilder: ({ section, onChange }: any) => (
    <div data-testid="report-section-builder">
      <input
        data-testid="section-title"
        value={section.title}
        onChange={(e) => onChange({ ...section, title: e.target.value })}
      />
    </div>
  )
}));

vi.mock('../ReportScheduleBuilder', () => ({
  ReportScheduleBuilder: ({ schedule, onChange }: any) => (
    <div data-testid="report-schedule-builder">
      <input
        type="checkbox"
        data-testid="schedule-enabled"
        checked={!!schedule}
        onChange={(e) => onChange(e.target.checked ? { frequency: 'daily', time: '09:00', timezone: 'Europe/Berlin', isActive: true } : undefined)}
      />
    </div>
  )
}));

describe('ReportBuilder', () => {
  const mockOnSave = vi.fn();
  const mockOnCancel = vi.fn();
  const mockReportingService = null;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the report builder dialog', () => {
    render(
      <ReportBuilder
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        reportingService={mockReportingService}
      />
    );

    expect(screen.getByTestId('dialog')).toBeInTheDocument();
    expect(screen.getByText('Neue Vorlage erstellen')).toBeInTheDocument();
  });

  it('renders with existing template data', () => {
    const mockTemplate: ReportTemplate = {
      id: 'test-template',
      name: 'Test Template',
      description: 'Test Description',
      type: 'kpi',
      department: 'dispatch',
      format: 'pdf',
      sections: [{
        id: 'section-1',
        title: 'Test Section',
        type: 'chart',
        dataSource: 'delivery',
        order: 0
      }],
      recipients: ['<EMAIL>'],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    render(
      <ReportBuilder
        template={mockTemplate}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        reportingService={mockReportingService}
      />
    );

    expect(screen.getByDisplayValue('Test Template')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test Description')).toBeInTheDocument();
  });

  it('handles form field changes', async () => {
    render(
      <ReportBuilder
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        reportingService={mockReportingService}
      />
    );

    const nameInput = screen.getByPlaceholderText('z.B. Monatlicher KPI-Bericht');
    fireEvent.change(nameInput, { target: { value: 'New Template Name' } });

    expect(nameInput).toHaveValue('New Template Name');
  });

  it('adds new sections', async () => {
    render(
      <ReportBuilder
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        reportingService={mockReportingService}
      />
    );

    // Switch to sections tab
    const sectionsTab = screen.getByTestId('tab-trigger-sections');
    fireEvent.click(sectionsTab);

    // Add a section
    const addSectionButton = screen.getByText('Abschnitt hinzufügen');
    fireEvent.click(addSectionButton);

    expect(screen.getByTestId('report-section-builder')).toBeInTheDocument();
  });

  it('validates required fields before saving', async () => {
    render(
      <ReportBuilder
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        reportingService={mockReportingService}
      />
    );

    const saveButton = screen.getByText('Speichern');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText('Name ist erforderlich')).toBeInTheDocument();
    });

    expect(mockOnSave).not.toHaveBeenCalled();
  });

  it('calls onSave with correct data when form is valid', async () => {
    render(
      <ReportBuilder
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        reportingService={mockReportingService}
      />
    );

    // Fill required fields
    const nameInput = screen.getByPlaceholderText('z.B. Monatlicher KPI-Bericht');
    fireEvent.change(nameInput, { target: { value: 'Test Template' } });

    const descriptionInput = screen.getByPlaceholderText('Beschreiben Sie den Zweck und Inhalt dieses Berichts...');
    fireEvent.change(descriptionInput, { target: { value: 'Test Description' } });

    // Add a section
    const sectionsTab = screen.getByTestId('tab-trigger-sections');
    fireEvent.click(sectionsTab);

    const addSectionButton = screen.getByText('Abschnitt hinzufügen');
    fireEvent.click(addSectionButton);

    // Save the template
    const saveButton = screen.getByText('Speichern');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Test Template',
          description: 'Test Description',
          type: 'kpi',
          department: 'all',
          format: 'pdf',
          sections: expect.arrayContaining([
            expect.objectContaining({
              title: 'Abschnitt 1',
              type: 'chart',
              dataSource: 'delivery'
            })
          ]),
          isActive: true
        })
      );
    });
  });

  it('calls onCancel when cancel button is clicked', () => {
    render(
      <ReportBuilder
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        reportingService={mockReportingService}
      />
    );

    const cancelButton = screen.getByText('Abbrechen');
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('manages recipients correctly', async () => {
    render(
      <ReportBuilder
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        reportingService={mockReportingService}
      />
    );

    // Switch to distribution tab
    const distributionTab = screen.getByTestId('tab-trigger-distribution');
    fireEvent.click(distributionTab);

    // Add a recipient
    const emailInput = screen.getByPlaceholderText('E-Mail-Adresse eingeben...');
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    const addButton = screen.getByText('Hinzufügen');
    fireEvent.click(addButton);

    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });
});