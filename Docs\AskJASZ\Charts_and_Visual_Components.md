# Charts und Visual Components - Dokumentation

## Übersicht
Die Charts und Visual Components bilden das visuelle Herzstück der Analytics-Bereiche im Leitstand. Sie transformieren komplexe Daten in verständliche, interaktive Visualisierungen und enthalten integrierte Ask JASZ Buttons für kontextspezifische Erklärungen und Analysen.

## Implementierte Chart-Komponenten

### 1. MTTR Trend Chart (MTTRTrendChart)

#### Zweck und Funktionalität
- **Hauptziel**: Visualisierung der zeitlichen Entwicklung der Mean Time To Repair (MTTR)
- **Chart-Typ**: Liniendiagramm mit Zeitachse
- **Datenquelle**: Historische Störungsdaten mit Zeitstempel-basierten Aggregationen
- **Zeitraum**: Konfigurierbar von Stunden bis Jahren

#### Technische Implementation
- **Library**: Recharts für React-basierte Visualisierung
- **Responsive Design**: Automatische Anpassung an Container-Größe
- **Animation**: Smooth-Transitions bei Datenänderungen
- **Performance**: Optimiert für große Datensätze durch Daten-Sampling

#### Interaktive Features
- **Hover-Tooltips**: Detaillierte Informationen bei Mouse-over
- **Zoom-Funktionalität**: Zeitbereich-spezifische Detailansicht
- **Data-Point Selection**: Klick auf Datenpunkte für Drill-down
- **Legend-Toggling**: Ein-/Ausblenden verschiedener Datenreihen

#### Ask JASZ Integration
- **Kontext**: MTTR-Trend-Analyse mit spezifischem Zeitraum
- **Template**: `CHART_ANALYSIS` für Trend-Interpretation
- **Tooltip**: "Lass dir diesen MTTR-Trend erklären und erfahre, was die Entwicklung bedeutet"

### 2. Störungskategorien Chart (StoerungsCategoryChart)

#### Zweck und Funktionalität
- **Hauptziel**: Visualisierung der Störungsverteilung nach Kategorien/Systemen
- **Chart-Typ**: Donut/Pie Chart oder Balkendiagramm (konfigurierbar)
- **Datenquelle**: Störungsdaten gruppiert nach Kategorie/System
- **Zeitraum**: Selektierbar über Date-Picker

#### Kategorie-Klassifikation
- **Hardware**: Server, Storage, Netzwerk-Hardware
- **Software**: Anwendungen, Betriebssysteme, Middleware
- **Netzwerk**: Verbindungsprobleme, Bandbreite, Latenz
- **Stromversorgung**: USV-Ausfälle, Netzspannungsprobleme
- **Umwelt**: Klimatechnik, Gebäudetechnik
- **Prozess**: Workflow-Probleme, Benutzer-Fehler

#### Visuelle Gestaltung
- **Farbkodierung**: Eindeutige Farben pro Kategorie
- **Prozentanzeigen**: Relativer Anteil jeder Kategorie
- **Absolute Zahlen**: Zusätzliche Anzeige der exakten Werte
- **Animation**: Smooth Loading und Transition-Effekte

#### Ask JASZ Integration
- **Kontext**: Störungsverteilung mit Top-Kategorien
- **Template**: `CHART_ANALYSIS` für Kategorie-Analyse
- **Tooltip**: "Analysiere die Störungsverteilung und finde Optimierungspotentiale"

### 3. Störungsstatistiken Chart (StoerungsStatsChart)

#### Zweck und Funktionalität
- **Hauptziel**: Kombinierte Darstellung verschiedener Störungsmetriken
- **Chart-Typ**: Kombiniertes Balken- und Liniendiagramm
- **Metriken**: Anzahl, MTTR, MTTA, Schweregrad-Verteilung
- **Vergleichsmodus**: Zeitraum-Vergleiche (Monat vs. Vormonat, etc.)

#### Dargestellte Metriken
- **Primary Axis**: Störungsanzahlen (Balken)
- **Secondary Axis**: Durchschnittszeiten (Linien)
- **Schweregrad-Overlay**: Farbkodierte Aufschlüsselung
- **Trend-Indikatoren**: Pfeile für Verbesserung/Verschlechterung

#### Interaktive Analyse-Features
- **Multi-Metric Selection**: Ein-/Ausblenden verschiedener Metriken
- **Time-Range Comparison**: Side-by-side Vergleich verschiedener Perioden
- **Drill-down Navigation**: Detail-Ansicht spezifischer Metriken
- **Export Functions**: Datenexport für weitere Analysen

#### Ask JASZ Integration
- **Kontext**: Multi-Metric Störungsanalyse
- **Template**: `CHART_ANALYSIS` für komplexe Korrelationsanalyse
- **Tooltip**: "Verstehe die Zusammenhänge zwischen verschiedenen Störungsmetriken"

### 4. System Status Heatmap (SystemStatusHeatmap)

#### Zweck und Funktionalität
- **Hauptziel**: Echtzeit-Visualisierung des Systemzustands
- **Darstellung**: Grid-basierte Heatmap mit Farbkodierung
- **Update-Frequenz**: Echtzeit (WebSocket) oder periodisch (30s-5min)
- **Responsiveness**: Adaptive Grid-Größe je nach Display

#### Farbkodierung und Status
- **🟢 Grün (Operational)**: Alle Parameter im Normalbereich
- **🟡 Gelb (Warning)**: Warnungen, Aufmerksamkeit erforderlich
- **🔴 Rot (Critical)**: Kritische Probleme, sofortige Aktion nötig
- **⚫ Grau (Offline)**: System nicht erreichbar oder keine Daten
- **🔵 Blau (Maintenance)**: Geplante Wartungsarbeiten

#### System-Kategorien
- **Production Systems**: Kritische Geschäftsanwendungen
- **Infrastructure**: Server, Storage, Netzwerk
- **Databases**: DBMS und Data-Warehouses
- **Integration**: APIs, Message Queues, ESB
- **Security**: Firewall, IDS/IPS, SIEM
- **Monitoring**: Überwachungssysteme selbst

#### Interaktive Features
- **Hover-Details**: Detailinformationen zum Systemstatus
- **Click-Navigation**: Sprung zu System-spezifischen Dashboards
- **Filter-Options**: Nach Kategorie, Standort, Kritikalität
- **Auto-Refresh**: Konfigurierbare Aktualisierungsintervalle

#### Ask JASZ Integration
- **Kontext**: System-Status mit Fokus auf kritische Bereiche
- **Template**: `SYSTEM_STATUS_ANALYSIS` (spezifisch für Heatmap)
- **Tooltip**: "Analysiere den aktuellen Systemzustand und erkenne Muster"

## Erweiterte Chart-Komponenten

### 5. Workflow Performance Chart (WorkflowPerformanceChart)

#### Funktionalität
- **Multi-System-Übersicht**: Performance verschiedener Workflow-Systeme
- **Real-time Metrics**: Live-Durchsatz und Latenz-Metriken
- **Comparative Analysis**: Vergleich verschiedener Workflow-Kategorien
- **Bottleneck Identification**: Visuelle Hervorhebung von Engpässen

#### Ask JASZ Integration
- **Kontext**: Workflow-Performance mit System-Vergleich
- **Fokus**: Optimierungsempfehlungen und Bottleneck-Analyse

### 6. Performance Analytics Chart (PerformanceAnalyticsChart)

#### Funktionalität
- **Comprehensive Analytics**: Umfassende Performance-Analyse
- **Predictive Elements**: Trend-Prognosen und Forecasting
- **Capacity Planning**: Visueller Kapazitäts-Bedarf
- **SLA Compliance**: Überwachung Service-Level-Agreements

#### Ask JASZ Integration
- **Kontext**: Tiefgehende Performance-Analyse
- **Fokus**: Strategische Empfehlungen und Langzeit-Optimierung

## Design-Prinzipien und Standards

### Visuelle Konsistenz
- **Farbpalette**: Einheitliche Farben für alle Charts
- **Typographie**: Konsistente Schriftarten und -größen
- **Spacing**: Standardisierte Abstände und Margins
- **Icon-System**: Einheitliche Icons für ähnliche Funktionen

### Accessibility (Barrierefreiheit)
- **Color-blind Friendly**: Farben mit ausreichendem Kontrast
- **Screen Reader**: Alternative Text für alle visuellen Elemente
- **Keyboard Navigation**: Vollständige Tastatur-Bedienbarkeit
- **High Contrast Mode**: Unterstützung für Hochkontrast-Modi

### Responsive Design
- **Mobile-first**: Optimierung für kleinste Bildschirme zuerst
- **Breakpoint-basiert**: Anpassung an verschiedene Bildschirmgrößen
- **Touch-friendly**: Ausreichend große Touch-Targets
- **Adaptive Layout**: Intelligent anpassende Layouts

### Performance-Optimierung
- **Lazy Loading**: Bedarfsgerechte Laden von Chart-Komponenten
- **Data Virtualization**: Effiziente Handhabung großer Datensätze
- **Caching**: Zwischenspeicherung berechneter Visualisierungen
- **Debouncing**: Vermeidung redundanter Berechnungen

## Interaktivitäts-Features

### Standard-Interaktionen
- **Pan & Zoom**: Navigation in Zeit- und Werte-Achsen
- **Selection**: Auswahl von Datenbereichen für Detail-Analyse
- **Filtering**: Ein-/Ausblenden von Datenreihen
- **Highlighting**: Hervorhebung verwandter Datenpunkte

### Erweiterte Interaktionen
- **Brushing & Linking**: Verknüpfte Selektion über mehrere Charts
- **Drill-down**: Navigation von Aggregaten zu Details
- **Cross-filtering**: Filter-Synchronisation zwischen Charts
- **Real-time Updates**: Live-Aktualisierung bei Datenänderungen

### Kontextuelle Menüs
- **Right-click Options**: Zusätzliche Funktionen per Rechtsklick
- **Export Options**: Verschiedene Export-Formate
- **Share Functions**: Teilen von Chart-Zuständen
- **Bookmark**: Speichern interessanter Ansichten

## Ask JASZ Integration Details

### Chart-spezifische Kontexte
Jede Chart-Komponente erstellt automatisch einen detaillierten Kontext:
```typescript
const createChartContext = (
  chartType: string,
  dataPoints: any[],
  timeRange: string,
  insights: string[]
) => ({
  type: 'chart',
  title: `${chartType} Chart`,
  data: dataPoints,
  timeRange,
  insights,
  patterns: detectPatterns(dataPoints)
});
```

### Template-System für Charts
Verschiedene Templates je nach Chart-Typ:
- **TREND_ANALYSIS**: Für zeitbasierte Entwicklungen
- **DISTRIBUTION_ANALYSIS**: Für Kategorien-Verteilungen
- **CORRELATION_ANALYSIS**: Für Zusammenhänge zwischen Metriken
- **PERFORMANCE_ANALYSIS**: Für Performance-spezifische Analysen

### Intelligente Insights
- **Automatic Pattern Recognition**: Erkennung von Trends, Ausreißern, Mustern
- **Statistical Analysis**: Automatische statistische Auswertungen
- **Comparative Insights**: Vergleiche mit historischen Daten
- **Predictive Elements**: Vorhersagen basierend auf aktuellen Trends

## Datenmanagement

### Datenquellen-Integration
- **Real-time APIs**: Live-Datenstreams für Echtzeit-Charts
- **Database Queries**: Optimierte Abfragen für historische Analysen  
- **Cached Data**: Zwischenspeicherung für bessere Performance
- **External APIs**: Integration externer Datenquellen

### Datenqualität
- **Validation**: Umfassende Datenvalidierung vor Visualisierung
- **Cleansing**: Bereinigung inkonsistenter oder fehlerhafter Daten
- **Interpolation**: Intelligente Behandlung fehlender Datenpunkte
- **Outlier Detection**: Erkennung und Behandlung von Ausreißern

### Aggregation und Processing
- **Time-based Aggregation**: Verdichtung nach Zeitintervallen
- **Statistical Aggregation**: Durchschnitt, Median, Perzentile
- **Custom Calculations**: Geschäftsspezifische Berechnungen
- **Real-time Processing**: Stream-Processing für Live-Daten

## Export und Sharing

### Export-Formate
- **PNG/SVG**: Hochqualitative Bilder für Präsentationen
- **PDF**: Mehrseitige Reports mit mehreren Charts
- **Excel/CSV**: Rohdaten für weitere Analysen
- **Interactive HTML**: Standalone interaktive Charts

### Sharing-Funktionen
- **Link Sharing**: Direkte Links zu Chart-Konfigurationen
- **Embed Codes**: Integration in externe Websites
- **Team Collaboration**: Geteilte Chart-Sammlungen
- **Version Control**: Versionierung von Chart-Konfigurationen

### Automatisierte Reports
- **Scheduled Reports**: Zeitgesteuerte Chart-Reports
- **Threshold-based Reports**: Event-getriggerte Berichte
- **Custom Templates**: Benutzer-definierte Report-Vorlagen
- **Distribution Lists**: Automatische Verteilung an Teams

## Best Practices für Chart-Design

### Datenvisualisierung
1. **Clear Message**: Jede Chart sollte eine klare Botschaft vermitteln
2. **Appropriate Chart Type**: Passender Chart-Typ für Datenart
3. **Minimal Ink**: Reduzierung auf wesentliche Elemente
4. **Consistent Scale**: Einheitliche Skalierung für Vergleichbarkeit

### Benutzerführung
1. **Progressive Disclosure**: Schrittweise Detailierung
2. **Contextual Help**: Situative Hilfestellung
3. **Error Handling**: Graceful Degradation bei Problemen
4. **Loading States**: Benutzerfreundliche Ladezustände

### Performance
1. **Efficient Rendering**: Optimierte Render-Zyklen
2. **Memory Management**: Vermeidung von Memory Leaks
3. **Batch Updates**: Gruppierte Datenaktualisierungen
4. **Lazy Computation**: Bedarfsgerechte Berechnungen

## Troubleshooting

### Häufige Probleme
- **Rendering Issues**: Chart wird nicht angezeigt
- **Performance Problems**: Langsame Chart-Aktualisierung
- **Data Issues**: Fehlende oder inkorrekte Daten
- **Responsive Problems**: Charts passen nicht auf mobile Geräte

### Debugging-Strategien
- **Console Logging**: Detailliertes Logging für Fehlerdiagnose
- **Data Inspection**: Validierung eingehender Daten
- **Performance Profiling**: Identifikation von Performance-Bottlenecks
- **Error Boundaries**: Graceful Error Handling in React

### Support-Tools
- **Chart Inspector**: Tool zur Chart-Konfiguration-Analyse
- **Data Validator**: Überprüfung der Datenqualität
- **Performance Monitor**: Überwachung der Chart-Performance
- **Error Reporter**: Automatische Fehlerberichterstattung