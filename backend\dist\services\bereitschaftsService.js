"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bereitschaftsService = exports.BereitschaftsService = void 0;
const bereitschaftsRepository_1 = require("../repositories/bereitschaftsRepository");
const date_fns_1 = require("date-fns");
class BereitschaftsService {
    // Personen-Management
    async getAllPersonen() {
        try {
            return await bereitschaftsRepository_1.bereitschaftsRepository.getAllPersonen();
        }
        catch (error) {
            console.error('Fehler beim Laden der Bereitschaftspersonen:', error);
            throw new Error('Bereitschaftspersonen konnten nicht geladen werden');
        }
    }
    async getPersonById(id) {
        try {
            const person = await bereitschaftsRepository_1.bereitschaftsRepository.getPersonById(id);
            if (!person) {
                throw new Error('Person nicht gefunden');
            }
            return person;
        }
        catch (error) {
            console.error(`<PERSON>hler beim <PERSON> der <PERSON> ${id}:`, error);
            throw error;
        }
    }
    async createPerson(data) {
        try {
            // Validierung
            if (!data.name || !data.telefon || !data.email) {
                throw new Error('Name, Telefon und E-Mail sind Pflichtfelder');
            }
            if (!this.isValidEmail(data.email)) {
                throw new Error('Ungültige E-Mail-Adresse');
            }
            if (!this.isValidPhone(data.telefon)) {
                throw new Error('Ungültige Telefonnummer');
            }
            return await bereitschaftsRepository_1.bereitschaftsRepository.createPerson(data);
        }
        catch (error) {
            console.error('Fehler beim Erstellen der Person:', error);
            throw error;
        }
    }
    async updatePerson(id, data) {
        try {
            // Validierung falls E-Mail oder Telefon geändert wird
            if (data.email && !this.isValidEmail(data.email)) {
                throw new Error('Ungültige E-Mail-Adresse');
            }
            if (data.telefon && !this.isValidPhone(data.telefon)) {
                throw new Error('Ungültige Telefonnummer');
            }
            return await bereitschaftsRepository_1.bereitschaftsRepository.updatePerson(id, data);
        }
        catch (error) {
            console.error(`Fehler beim Aktualisieren der Person ${id}:`, error);
            throw error;
        }
    }
    async deletePerson(id) {
        try {
            // Prüfe ob Person aktuelle Bereitschaften hat
            const aktuelleBereitschaft = await bereitschaftsRepository_1.bereitschaftsRepository.getAktuelleBereitschaft();
            if (aktuelleBereitschaft && aktuelleBereitschaft.personId === id) {
                throw new Error('Person kann nicht gelöscht werden, da sie aktuell Bereitschaft hat');
            }
            return await bereitschaftsRepository_1.bereitschaftsRepository.deletePerson(id);
        }
        catch (error) {
            console.error(`Fehler beim Löschen der Person ${id}:`, error);
            throw error;
        }
    }
    async updatePersonenReihenfolge(personenIds) {
        try {
            return await bereitschaftsRepository_1.bereitschaftsRepository.updatePersonenReihenfolge(personenIds);
        }
        catch (error) {
            console.error('Fehler beim Aktualisieren der Reihenfolge:', error);
            throw new Error('Reihenfolge konnte nicht aktualisiert werden');
        }
    }
    // Wochenplan-Management
    async getWochenplan(startDate, anzahlWochen = 8) {
        try {
            const start = startDate || (0, date_fns_1.addWeeks)((0, date_fns_1.startOfWeek)(new Date(), { weekStartsOn: 1 }), -2);
            return await bereitschaftsRepository_1.bereitschaftsRepository.getWochenPlan(start, anzahlWochen);
        }
        catch (error) {
            console.error('Fehler beim Laden des Wochenplans:', error);
            throw new Error('Wochenplan konnte nicht geladen werden');
        }
    }
    async getAktuelleBereitschaft() {
        try {
            return await bereitschaftsRepository_1.bereitschaftsRepository.getAktuelleBereitschaft();
        }
        catch (error) {
            console.error('Fehler beim Laden der aktuellen Bereitschaft:', error);
            throw new Error('Aktuelle Bereitschaft konnte nicht geladen werden');
        }
    }
    async generiereWochenplan(startDate, anzahlWochen) {
        try {
            if (anzahlWochen <= 0 || anzahlWochen > 52) {
                throw new Error('Anzahl Wochen muss zwischen 1 und 52 liegen');
            }
            const personen = await bereitschaftsRepository_1.bereitschaftsRepository.getAllPersonen();
            if (personen.length === 0) {
                throw new Error('Keine aktiven Bereitschaftspersonen vorhanden');
            }
            return await bereitschaftsRepository_1.bereitschaftsRepository.generiereWochenplan(startDate, anzahlWochen);
        }
        catch (error) {
            console.error('Fehler beim Generieren des Wochenplans:', error);
            throw error;
        }
    }
    async updateWoche(id, data) {
        try {
            return await bereitschaftsRepository_1.bereitschaftsRepository.updateWoche(id, data);
        }
        catch (error) {
            console.error(`Fehler beim Aktualisieren der Woche ${id}:`, error);
            throw new Error('Woche konnte nicht aktualisiert werden');
        }
    }
    async deleteWoche(id) {
        try {
            return await bereitschaftsRepository_1.bereitschaftsRepository.deleteWoche(id);
        }
        catch (error) {
            console.error(`Fehler beim Löschen der Woche ${id}:`, error);
            throw new Error('Woche konnte nicht gelöscht werden');
        }
    }
    // Ausnahmen-Management
    async getAllAusnahmen() {
        try {
            return await bereitschaftsRepository_1.bereitschaftsRepository.getAllAusnahmen();
        }
        catch (error) {
            console.error('Fehler beim Laden der Ausnahmen:', error);
            throw new Error('Ausnahmen konnten nicht geladen werden');
        }
    }
    async createAusnahme(data) {
        try {
            // Validierung
            if (data.von >= data.bis) {
                throw new Error('Start-Datum muss vor End-Datum liegen');
            }
            if (!data.grund.trim()) {
                throw new Error('Grund ist ein Pflichtfeld');
            }
            // Prüfe auf Überschneidungen mit bestehenden Ausnahmen
            const bestehendeBereitschaften = await bereitschaftsRepository_1.bereitschaftsRepository.getPersonenInZeitraum(data.von, data.bis);
            const ueberschneidungen = bestehendeBereitschaften.filter(b => b.personId === data.personId);
            if (ueberschneidungen.length > 0) {
                console.warn('Ausnahme überschneidet sich mit bestehenden Bereitschaften:', ueberschneidungen);
            }
            return await bereitschaftsRepository_1.bereitschaftsRepository.createAusnahme(data);
        }
        catch (error) {
            console.error('Fehler beim Erstellen der Ausnahme:', error);
            throw error;
        }
    }
    async updateAusnahme(id, data) {
        try {
            if (data.von && data.bis && data.von >= data.bis) {
                throw new Error('Start-Datum muss vor End-Datum liegen');
            }
            return await bereitschaftsRepository_1.bereitschaftsRepository.updateAusnahme(id, data);
        }
        catch (error) {
            console.error(`Fehler beim Aktualisieren der Ausnahme ${id}:`, error);
            throw error;
        }
    }
    async deleteAusnahme(id) {
        try {
            return await bereitschaftsRepository_1.bereitschaftsRepository.deleteAusnahme(id);
        }
        catch (error) {
            console.error(`Fehler beim Löschen der Ausnahme ${id}:`, error);
            throw new Error('Ausnahme konnte nicht gelöscht werden');
        }
    }
    // Konfiguration-Management
    async getKonfiguration() {
        try {
            return await bereitschaftsRepository_1.bereitschaftsRepository.getKonfiguration();
        }
        catch (error) {
            console.error('Fehler beim Laden der Konfiguration:', error);
            throw new Error('Konfiguration konnte nicht geladen werden');
        }
    }
    async updateKonfiguration(data) {
        try {
            // Validierung
            if (data.wechselTag && (data.wechselTag < 1 || data.wechselTag > 7)) {
                throw new Error('Wechseltag muss zwischen 1 (Montag) und 7 (Sonntag) liegen');
            }
            if (data.wechselUhrzeit && !this.isValidTime(data.wechselUhrzeit)) {
                throw new Error('Ungültige Uhrzeit (Format: HH:MM)');
            }
            if (data.benachrichtigungTage && (data.benachrichtigungTage < 0 || data.benachrichtigungTage > 14)) {
                throw new Error('Benachrichtigungstage müssen zwischen 0 und 14 liegen');
            }
            return await bereitschaftsRepository_1.bereitschaftsRepository.updateKonfiguration(data);
        }
        catch (error) {
            console.error('Fehler beim Aktualisieren der Konfiguration:', error);
            throw error;
        }
    }
    // Bereitschaftsplan mit Ausnahmen berechnen
    async getBereitschaftsplanMitAusnahmen(startDate, anzahlWochen) {
        try {
            const wochenplan = await this.getWochenplan(startDate, anzahlWochen);
            const ausnahmen = await bereitschaftsRepository_1.bereitschaftsRepository.getAllAusnahmen();
            // Wende Ausnahmen auf den Wochenplan an
            const planMitAusnahmen = wochenplan.map(woche => {
                const relevanteAusnahmen = ausnahmen.filter(ausnahme => ausnahme.personId === woche.personId &&
                    ausnahme.von <= woche.bis &&
                    ausnahme.bis >= woche.von);
                return {
                    ...woche,
                    ausnahmen: relevanteAusnahmen,
                    hatAusnahme: relevanteAusnahmen.length > 0
                };
            });
            return planMitAusnahmen;
        }
        catch (error) {
            console.error('Fehler beim Berechnen des Bereitschaftsplans mit Ausnahmen:', error);
            throw new Error('Bereitschaftsplan mit Ausnahmen konnte nicht berechnet werden');
        }
    }
    // Validierungshilfsmethoden
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    isValidPhone(phone) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        return phoneRegex.test(phone);
    }
    isValidTime(time) {
        const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
        return timeRegex.test(time);
    }
    // Statistiken und Reports
    async getBereitschaftsStatistiken(startDate, endDate) {
        try {
            const bereitschaften = await bereitschaftsRepository_1.bereitschaftsRepository.getPersonenInZeitraum(startDate, endDate);
            const personen = await bereitschaftsRepository_1.bereitschaftsRepository.getAllPersonen();
            const statistiken = personen.map(person => {
                const personBereitschaften = bereitschaften.filter(b => b.personId === person.id);
                const gesamtStunden = personBereitschaften.reduce((sum, b) => {
                    const stunden = (b.bis.getTime() - b.von.getTime()) / (1000 * 60 * 60);
                    return sum + stunden;
                }, 0);
                return {
                    person,
                    anzahlBereitschaften: personBereitschaften.length,
                    gesamtStunden: Math.round(gesamtStunden),
                    durchschnittStundenProWoche: personBereitschaften.length > 0 ? Math.round(gesamtStunden / personBereitschaften.length) : 0
                };
            });
            return {
                zeitraum: { von: startDate, bis: endDate },
                gesamtBereitschaften: bereitschaften.length,
                personenStatistiken: statistiken
            };
        }
        catch (error) {
            console.error('Fehler beim Berechnen der Bereitschaftsstatistiken:', error);
            throw new Error('Bereitschaftsstatistiken konnten nicht berechnet werden');
        }
    }
}
exports.BereitschaftsService = BereitschaftsService;
exports.bereitschaftsService = new BereitschaftsService();
