/**
 * KPI Repository Integration Service
 * 
 * Integrates predictive analytics with existing repository layer
 * to provide real-time KPI data access and monitoring.
 */

import { BaseService, IService, ServiceConfig } from './base.service';
import { SystemRepository } from '@/repositories/system.repository';
import { ProductionRepository } from '@/repositories/production.repository';
import { WarehouseRepository } from '@/repositories/warehouse.repository';
import { KPI, KPIDataPoint } from '@/types/predictive-analytics';

/**
 * KPI Repository Integration Service
 */
export class KPIRepositoryIntegrationService extends BaseService implements IService {
  readonly serviceName = 'KPIRepositoryIntegrationService';
  
  private systemRepo: SystemRepository;
  private productionRepo: ProductionRepository;
  private warehouseRepo: WarehouseRepository;
  private kpiDefinitions: Map<string, KPI> = new Map();

  constructor(serviceConfig?: ServiceConfig) {
    super(serviceConfig);
    
    this.systemRepo = new SystemRepository();
    this.productionRepo = new ProductionRepository();
    this.warehouseRepo = new WarehouseRepository();
  }

  /**
   * Initialize the service and load KPI definitions
   */
  protected async onInitialize(): Promise<void> {
    this.log('Initializing KPI Repository Integration Service');
    
    // Load KPI definitions
    await this.loadKPIDefinitions();
    
    this.log('KPI Repository Integration Service initialized successfully');
  }

  /**
   * Health check for the service
   */
  protected async onHealthCheck(): Promise<void> {
    // Check if repositories are accessible
    try {
      await this.systemRepo.serviceLevel.getAll();
      await this.productionRepo.schnitte.getAll();
    } catch (error) {
      throw new Error(`Repository health check failed: ${error}`);
    }
  }

  /**
   * Cleanup service resources
   */
  protected async onDestroy(): Promise<void> {
    this.kpiDefinitions.clear();
    this.log('KPI Repository Integration Service destroyed');
  }

  /**
   * Get KPI definition by ID
   */
  async getKPIDefinition(kpiId: string): Promise<KPI | null> {
    this.ensureInitialized();
    return this.kpiDefinitions.get(kpiId) || null;
  }

  /**
   * Get all available KPI definitions
   */
  async getAllKPIDefinitions(): Promise<KPI[]> {
    this.ensureInitialized();
    return Array.from(this.kpiDefinitions.values());
  }

  /**
   * Get KPI definitions for a specific department
   */
  async getKPIDefinitionsByDepartment(department: 'dispatch' | 'cutting' | 'incoming-goods'): Promise<KPI[]> {
    this.ensureInitialized();
    return Array.from(this.kpiDefinitions.values())
      .filter(kpi => kpi.department === department || kpi.department === 'all');
  }

  /**
   * Get historical KPI data
   */
  async getKPIHistoricalData(kpiId: string, hours: number): Promise<KPIDataPoint[]> {
    this.ensureInitialized();
    
    const kpi = this.kpiDefinitions.get(kpiId);
    if (!kpi) {
      throw new Error(`KPI definition not found: ${kpiId}`);
    }

    try {
      switch (kpi.category) {
        case 'performance':
          return await this.getPerformanceKPIData(kpiId, hours);
        case 'quality':
          return await this.getQualityKPIData(kpiId, hours);
        case 'efficiency':
          return await this.getEfficiencyKPIData(kpiId, hours);
        case 'cost':
          return await this.getCostKPIData(kpiId, hours);
        case 'time':
          return await this.getTimeKPIData(kpiId, hours);
        default:
          throw new Error(`Unsupported KPI category: ${kpi.category}`);
      }
    } catch (error) {
      this.log(`Error getting historical data for KPI ${kpiId}`, error);
      throw error;
    }
  }

  /**
   * Get latest KPI data point
   */
  async getLatestKPIData(kpiId: string): Promise<KPIDataPoint | null> {
    this.ensureInitialized();
    
    try {
      const historicalData = await this.getKPIHistoricalData(kpiId, 1);
      return historicalData.length > 0 ? historicalData[historicalData.length - 1] : null;
    } catch (error) {
      this.log(`Error getting latest data for KPI ${kpiId}`, error);
      return null;
    }
  }

  /**
   * Get KPIs for a department
   */
  async getDepartmentKPIs(departmentId: string): Promise<KPI[]> {
    this.ensureInitialized();
    
    const departmentMap: Record<string, 'dispatch' | 'cutting' | 'incoming-goods'> = {
      'dispatch': 'dispatch',
      'cutting': 'cutting',
      'incoming-goods': 'incoming-goods',
      'versand': 'dispatch',
      'ablaengerei': 'cutting',
      'wareneingang': 'incoming-goods'
    };
    
    const department = departmentMap[departmentId.toLowerCase()];
    if (!department) {
      throw new Error(`Unknown department: ${departmentId}`);
    }
    
    return await this.getKPIDefinitionsByDepartment(department);
  }

  /**
   * Load KPI definitions from repository data
   */
  private async loadKPIDefinitions(): Promise<void> {
    const now = new Date().toISOString();
    
    // System/Dispatch KPIs
    this.kpiDefinitions.set('dispatch_service_level', {
      id: 'dispatch_service_level',
      name: 'Service Level',
      description: 'Percentage of orders delivered on time',
      unit: '%',
      department: 'dispatch',
      category: 'performance',
      target_value: 95,
      warning_threshold: 5,
      critical_threshold: 10,
      is_active: true,
      created_at: now,
      updated_at: now
    });

    this.kpiDefinitions.set('dispatch_picking_efficiency', {
      id: 'dispatch_picking_efficiency',
      name: 'Picking Efficiency',
      description: 'Number of picks per hour',
      unit: 'picks/hour',
      department: 'dispatch',
      category: 'efficiency',
      target_value: 100,
      warning_threshold: 20,
      critical_threshold: 40,
      is_active: true,
      created_at: now,
      updated_at: now
    });

    this.kpiDefinitions.set('dispatch_delivery_positions', {
      id: 'dispatch_delivery_positions',
      name: 'Delivery Positions',
      description: 'Number of delivery positions processed',
      unit: 'positions',
      department: 'dispatch',
      category: 'performance',
      target_value: 1000,
      warning_threshold: 200,
      critical_threshold: 400,
      is_active: true,
      created_at: now,
      updated_at: now
    });

    // Cutting/Production KPIs
    this.kpiDefinitions.set('cutting_machine_efficiency', {
      id: 'cutting_machine_efficiency',
      name: 'Machine Efficiency',
      description: 'Average efficiency of cutting machines',
      unit: '%',
      department: 'cutting',
      category: 'efficiency',
      target_value: 85,
      warning_threshold: 15,
      critical_threshold: 25,
      is_active: true,
      created_at: now,
      updated_at: now
    });

    this.kpiDefinitions.set('cutting_daily_output', {
      id: 'cutting_daily_output',
      name: 'Daily Cutting Output',
      description: 'Total cuts produced per day',
      unit: 'cuts',
      department: 'cutting',
      category: 'performance',
      target_value: 5000,
      warning_threshold: 1000,
      critical_threshold: 2000,
      is_active: true,
      created_at: now,
      updated_at: now
    });

    this.kpiDefinitions.set('cutting_waste_percentage', {
      id: 'cutting_waste_percentage',
      name: 'Material Waste',
      description: 'Percentage of material wasted in cutting process',
      unit: '%',
      department: 'cutting',
      category: 'cost',
      target_value: 5,
      warning_threshold: 3,
      critical_threshold: 5,
      is_active: true,
      created_at: now,
      updated_at: now
    });

    // Warehouse/Incoming Goods KPIs
    this.kpiDefinitions.set('warehouse_utilization', {
      id: 'warehouse_utilization',
      name: 'Warehouse Utilization',
      description: 'Percentage of warehouse capacity utilized',
      unit: '%',
      department: 'incoming-goods',
      category: 'efficiency',
      target_value: 80,
      warning_threshold: 10,
      critical_threshold: 20,
      is_active: true,
      created_at: now,
      updated_at: now
    });

    this.kpiDefinitions.set('incoming_processing_time', {
      id: 'incoming_processing_time',
      name: 'Processing Time',
      description: 'Average time to process incoming goods',
      unit: 'hours',
      department: 'incoming-goods',
      category: 'time',
      target_value: 4,
      warning_threshold: 2,
      critical_threshold: 4,
      is_active: true,
      created_at: now,
      updated_at: now
    });

    this.log(`Loaded ${this.kpiDefinitions.size} KPI definitions`);
  }

  /**
   * Get performance KPI data
   */
  private async getPerformanceKPIData(kpiId: string, hours: number): Promise<KPIDataPoint[]> {
    const data: KPIDataPoint[] = [];
    const now = new Date();

    switch (kpiId) {
      case 'dispatch_service_level':
        const serviceLevelData = await this.systemRepo.serviceLevel.getAll();
        return serviceLevelData.slice(-hours).map(item => ({
          timestamp: new Date(item.datum || now).toISOString(),
          value: (item as any).servicegrad || 0,
          metadata: { kpi_id: kpiId, source: 'service_level_repository' }
        }));

      case 'dispatch_delivery_positions':
        const deliveryData = await this.systemRepo.deliveryPositions.getAll();
        return deliveryData.slice(-hours).map(item => ({
          timestamp: new Date(item.datum || now).toISOString(),
          value: item.positionen || 0,
          metadata: { kpi_id: kpiId, source: 'delivery_positions_repository' }
        }));

      case 'cutting_daily_output':
        const cuttingData = await this.productionRepo.schnitte.getDailySums();
        return cuttingData.slice(-hours).map(item => ({
          timestamp: new Date(item.date || now).toISOString(),
          value: item.grandTotal || 0,
          metadata: { kpi_id: kpiId, source: 'cutting_repository' }
        }));

      default:
        // Generate mock data for unknown performance KPIs
        for (let i = hours; i >= 0; i--) {
          const timestamp = new Date(now.getTime() - i * 3600000);
          data.push({
            timestamp: timestamp.toISOString(),
            value: 80 + Math.random() * 40, // 80-120 range
            metadata: { kpi_id: kpiId, source: 'mock_performance' }
          });
        }
        return data;
    }
  }

  /**
   * Get efficiency KPI data
   */
  private async getEfficiencyKPIData(kpiId: string, hours: number): Promise<KPIDataPoint[]> {
    const data: KPIDataPoint[] = [];
    const now = new Date();

    switch (kpiId) {
      case 'dispatch_picking_efficiency':
        const pickingData = await this.systemRepo.picking.getAll();
        return pickingData.slice(-hours).map(item => ({
          timestamp: new Date(item.datum || now).toISOString(),
          value: item.picks || 0,
          metadata: { kpi_id: kpiId, source: 'picking_repository' }
        }));

      case 'cutting_machine_efficiency':
        const machineData = await this.productionRepo.maschinenEfficiency.getAll();
        return machineData.slice(-hours).map(item => ({
          timestamp: new Date(item.datum || now).toISOString(),
          value: item.efficiency || 0,
          metadata: { kpi_id: kpiId, source: 'machine_efficiency_repository' }
        }));

      default:
        // Generate mock efficiency data
        for (let i = hours; i >= 0; i--) {
          const timestamp = new Date(now.getTime() - i * 3600000);
          data.push({
            timestamp: timestamp.toISOString(),
            value: 70 + Math.random() * 30, // 70-100% efficiency
            metadata: { kpi_id: kpiId, source: 'mock_efficiency' }
          });
        }
        return data;
    }
  }

  /**
   * Get quality KPI data
   */
  private async getQualityKPIData(kpiId: string, hours: number): Promise<KPIDataPoint[]> {
    const data: KPIDataPoint[] = [];
    const now = new Date();

    // Generate mock quality data
    for (let i = hours; i >= 0; i--) {
      const timestamp = new Date(now.getTime() - i * 3600000);
      data.push({
        timestamp: timestamp.toISOString(),
        value: 95 + Math.random() * 5, // 95-100% quality
        metadata: { kpi_id: kpiId, source: 'mock_quality' }
      });
    }
    return data;
  }

  /**
   * Get cost KPI data
   */
  private async getCostKPIData(kpiId: string, hours: number): Promise<KPIDataPoint[]> {
    const data: KPIDataPoint[] = [];
    const now = new Date();

    switch (kpiId) {
      case 'cutting_waste_percentage':
        // Calculate waste from cutting data
        const warehousePerf = await this.productionRepo.ablaengerei.getWarehousePerformance();
        const wastePercentage = 100 - (warehousePerf.totalCuts > 0 ? 
          (warehousePerf.warehouse220.cuts + warehousePerf.warehouse240.cuts + warehousePerf.warehouse200.cuts) / warehousePerf.totalCuts * 100 : 0);
        
        for (let i = hours; i >= 0; i--) {
          const timestamp = new Date(now.getTime() - i * 3600000);
          data.push({
            timestamp: timestamp.toISOString(),
            value: Math.max(0, wastePercentage + (Math.random() - 0.5) * 2), // Add some variation
            metadata: { kpi_id: kpiId, source: 'calculated_waste' }
          });
        }
        return data;

      default:
        // Generate mock cost data
        for (let i = hours; i >= 0; i--) {
          const timestamp = new Date(now.getTime() - i * 3600000);
          data.push({
            timestamp: timestamp.toISOString(),
            value: 1000 + Math.random() * 500, // Cost in euros
            metadata: { kpi_id: kpiId, source: 'mock_cost' }
          });
        }
        return data;
    }
  }

  /**
   * Get time KPI data
   */
  private async getTimeKPIData(kpiId: string, hours: number): Promise<KPIDataPoint[]> {
    const data: KPIDataPoint[] = [];
    const now = new Date();

    // Generate mock time data
    for (let i = hours; i >= 0; i--) {
      const timestamp = new Date(now.getTime() - i * 3600000);
      data.push({
        timestamp: timestamp.toISOString(),
        value: 2 + Math.random() * 4, // 2-6 hours processing time
        metadata: { kpi_id: kpiId, source: 'mock_time' }
      });
    }
    return data;
  }
}

// Export singleton instance
export const kpiRepositoryIntegrationService = new KPIRepositoryIntegrationService();
export default kpiRepositoryIntegrationService;