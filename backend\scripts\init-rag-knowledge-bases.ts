/**
 * Initialize RAG Knowledge Bases
 * 
 * Creates default knowledge bases for the RAG system
 */

import { PrismaClient } from '@prisma/rag-client';

const prisma = new PrismaClient();

async function initializeKnowledgeBases() {
  console.log('🚀 Initializing RAG Knowledge Bases...');

  const knowledgeBases = [
    {
      id: 1,
      name: 'Standard Wissensdatenbank',
      description: 'Allgemeine Dokumente und Verfahren',
      category: 'business'
    },
    {
      id: 2,
      name: 'Technische Dokumentation',
      description: 'Technische Handbücher und Spezifikationen',
      category: 'technical'
    },
    {
      id: 3,
      name: 'Verfahren und Prozesse',
      description: 'Arbeitsanweisungen und Prozessbeschreibungen',
      category: 'procedures'
    },
    {
      id: 4,
      name: 'KPI und Metriken',
      description: 'Kennzahlen und Leistungsindikatoren',
      category: 'kpi'
    }
  ];

  for (const kb of knowledgeBases) {
    try {
      const existing = await prisma.knowledgeBase.findUnique({
        where: { id: kb.id }
      });

      if (!existing) {
        await prisma.knowledgeBase.create({
          data: kb
        });
        console.log(`✅ Created knowledge base: ${kb.name}`);
      } else {
        console.log(`⏭️  Knowledge base already exists: ${kb.name}`);
      }
    } catch (error) {
      console.error(`❌ Error creating knowledge base ${kb.name}:`, error);
    }
  }

  console.log('✅ Knowledge bases initialization completed');
}

async function main() {
  try {
    await initializeKnowledgeBases();
  } catch (error) {
    console.error('❌ Error initializing knowledge bases:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

export { initializeKnowledgeBases };