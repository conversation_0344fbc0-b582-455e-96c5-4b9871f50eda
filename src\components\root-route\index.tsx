import React from 'react';
import { Outlet, useLocation } from '@tanstack/react-router';
import { DialogProvider } from '@/components/ui/dialog';
import Toaster from '@/components/ui/toaster';
import DragWindowRegion from '@/components/DragWindowRegion';

/**
 * Root-Komponente, die das gemeinsame Layout für alle Routen bereitstellt
 */
export function RootRouteComponent() {
  const location = useLocation();
  
  // Hide DragWindowRegion on UserLandingPage and login/auth pages
  const hideDragRegion = location.pathname === '/' || 
                        location.pathname === '/login' || 
                        location.pathname === '/register' ||
                        location.pathname.startsWith('/auth');

  return (
    <div className="flex flex-col h-screen">
      {!hideDragRegion && <DragWindowRegion />}
      <div className="flex-1 flex overflow-hidden">
        <DialogProvider>
          <main className="flex-1 overflow-auto">
            <Outlet />
          </main>
          <Toaster />
        </DialogProvider>
      </div>
    </div>
  );
}
