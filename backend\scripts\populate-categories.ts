import { PrismaClient } from '@prisma/rag-client';

const prisma = new PrismaClient();

interface CategoryData {
  id: string;
  name: string;
  description: string;
}

const categories: CategoryData[] = [
  {
    id: 'dispatch',
    name: 'Versand',
    description: 'Versand- und Lieferoperationen'
  },
  {
    id: 'cutting',
    name: '<PERSON><PERSON><PERSON><PERSON>nger<PERSON>',
    description: 'Schneid- und Ablängoperationen'
  },
  {
    id: 'incoming-goods',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Wareneingang und Annahme'
  },
  {
    id: 'system',
    name: 'System',
    description: 'Systemdokumentation und -konfiguration'
  },
  {
    id: 'quality',
    name: 'Qualität',
    description: 'Qualitätssicherung und -kontrolle'
  },
  {
    id: 'maintenance',
    name: 'Wartung',
    description: 'Wartung und Instandhaltung'
  },
  {
    id: 'app',
    name: 'App Leitfaden',
    description: 'Anwendungsleitfäden und Benutzerhandbücher'
  },
  {
    id: 'procedures',
    name: 'Verfahren',
    description: 'Betriebsverfahren und Arbeitsanweisungen'
  },
  {
    id: 'kpi',
    name: 'KPI & Metriken',
    description: 'Leistungskennzahlen und Metriken'
  }
];

export async function populateCategories(): Promise<void> {
  try {
    console.log('🚀 Starting category population...');
    
    // Check if categories already exist
    const existingCategories = await prisma.category.findMany();
    console.log(`📊 Found ${existingCategories.length} existing categories`);
    
    let created = 0;
    let updated = 0;
    let skipped = 0;
    
    for (const category of categories) {
      try {
        const existing = await prisma.category.findUnique({
          where: { id: category.id }
        });
        
        if (existing) {
          // Update existing category
          await prisma.category.update({
            where: { id: category.id },
            data: {
              name: category.name,
              description: category.description
            }
          });
          updated++;
          console.log(`✅ Updated category: ${category.name} (${category.id})`);
        } else {
          // Create new category
          await prisma.category.create({
            data: category
          });
          created++;
          console.log(`🆕 Created category: ${category.name} (${category.id})`);
        }
      } catch (error) {
        console.error(`❌ Error processing category ${category.id}:`, (error as Error).message);
        skipped++;
      }
    }
    
    console.log('\n📈 Summary:');
    console.log(`   Created: ${created}`);
    console.log(`   Updated: ${updated}`);
    console.log(`   Skipped: ${skipped}`);
    console.log(`   Total: ${categories.length}`);
    
    // Display final categories
    const finalCategories = await prisma.category.findMany({
      orderBy: { name: 'asc' }
    });
    
    console.log('\n📋 Final categories in database:');
    finalCategories.forEach(cat => {
      console.log(`   ${cat.name} (${cat.id}) - ${cat.description || 'No description'}`);
    });
    
    console.log('\n✅ Category population completed successfully!');
    
  } catch (error) {
    console.error('❌ Error populating categories:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script if called directly
if (require.main === module) {
  populateCategories()
    .then(() => {
      console.log('🎉 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}