import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Zap,
  Database,
  TrendingUp,
  RefreshCw,
  HardDrive,
  Clock,
  Target
} from 'lucide-react';
import { motion } from 'framer-motion';

interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  averageAccessTime: number;
}

interface CacheRecommendation {
  type: 'info' | 'warning' | 'success';
  message: string;
}

export function CacheAnalyticsChart() {
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);
  const [recommendations, setRecommendations] = useState<CacheRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const chartConfig = {
    hits: {
      label: "Cache Hits",
      color: "oklch(0.685 0.169 237.323)",
    },
    misses: {
      label: "Cache Misses",
      color: "oklch(0.66 0.17 301)",
    },
  } satisfies ChartConfig;

  const fetchCacheData = async () => {
    try {
      const response = await fetch('/api/performance/cache');
      if (!response.ok) throw new Error('Failed to fetch cache data');

      const data = await response.json();
      setCacheStats(data.data.stats);
      setRecommendations(data.data.recommendations || []);
    } catch (error) {
      console.error('Error fetching cache data:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveSnapshot = async () => {
    setSaving(true);
    try {
      // Cache snapshot functionality not available in basic performance routes
      // Just refresh the data instead
      await fetchCacheData();
    } catch (error) {
      console.error('Error saving cache snapshot:', error);
    } finally {
      setSaving(false);
    }
  };

  useEffect(() => {
    fetchCacheData();

    // Auto-refresh every 60 seconds
    const interval = setInterval(fetchCacheData, 60000);
    return () => clearInterval(interval);
  }, []);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getCacheEfficiencyStatus = (hitRate: number) => {
    if (hitRate >= 0.8) return { status: 'excellent', color: 'text-green-600', label: 'Ausgezeichnet' };
    if (hitRate >= 0.6) return { status: 'good', color: 'text-blue-600', label: 'Gut' };
    if (hitRate >= 0.4) return { status: 'fair', color: 'text-yellow-600', label: 'Akzeptabel' };
    return { status: 'poor', color: 'text-red-600', label: 'Verbesserung nötig' };
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cache Analytics</CardTitle>
          <CardDescription>Lade Cache-Statistiken...</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </CardContent>
      </Card>
    );
  }

  if (!cacheStats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cache Analytics</CardTitle>
          <CardDescription>Keine Cache-Daten verfügbar</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">Cache-Statistiken konnten nicht geladen werden</div>
        </CardContent>
      </Card>
    );
  }

  const pieData = [
    {
      name: 'Cache Hits',
      value: Math.round(cacheStats.hitRate * 100),
      fill: 'var(--color-hits)',
    },
    {
      name: 'Cache Misses',
      value: Math.round(cacheStats.missRate * 100),
      fill: 'var(--color-misses)',
    },
  ];

  const efficiency = getCacheEfficiencyStatus(cacheStats.hitRate);

  return (
    <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-4 transition-all duration-300 hover:border-slate-200 h-[320px]">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Cache Analytics
              <Badge
                className={`${efficiency.color} bg-opacity-10`}
                variant="outline"
              >
                {efficiency.label}
              </Badge>
            </CardTitle>
            <CardDescription>
              Cache-Performance und Effizienz-Metriken
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchCacheData}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={saveSnapshot}
              disabled={saving}
            >
              {saving ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Database className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {/* Compact Cache Hit/Miss Chart and Stats */}
        <div className="flex items-center gap-4 mb-4">
          {/* Compact Pie Chart */}
          <div className="h-[100px] w-[100px] flex-shrink-0">
            <ChartContainer config={chartConfig} className="h-full w-full">
              <PieChart>
                <Pie
                  data={[
                    {
                      name: 'Cache Hits',
                      value: Math.round(cacheStats.hitRate * 100),
                      fill: 'var(--color-hits)',
                    },
                    {
                      name: 'Cache Misses',
                      value: Math.round(cacheStats.missRate * 100),
                      fill: 'var(--color-misses)',
                    },
                  ]}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={40}
                  innerRadius={20}
                  stroke="none"
                />
                <ChartTooltip
                  content={<ChartTooltipContent formatter={(value) => [`${value}%`, '']} />}
                />
              </PieChart>
            </ChartContainer>
          </div>

          {/* Key Metrics */}
          <div className="flex-1 grid grid-cols-2 gap-3">
            <div className="text-center p-2 border rounded bg-white">
              <div className="text-sm text-muted-foreground">Hit Rate</div>
              <div className={`text-lg font-bold ${efficiency.color}`}>
                {formatPercentage(cacheStats.hitRate)}
              </div>
            </div>
            <div className="text-center p-2 border rounded bg-white">
              <div className="text-sm text-muted-foreground">Einträge</div>
              <div className="text-lg font-bold">{cacheStats.totalEntries.toLocaleString()}</div>
            </div>
            <div className="text-center p-2 border rounded bg-white">
              <div className="text-sm text-muted-foreground">Größe</div>
              <div className="text-lg font-bold">{formatBytes(cacheStats.totalSize)}</div>
            </div>
            <div className="text-center p-2 border rounded bg-white">
              <div className="text-sm text-muted-foreground">Ø Zeit</div>
              <div className="text-lg font-bold">{Math.round(cacheStats.averageAccessTime)}ms</div>
            </div>
          </div>
        </div>

        {/* Status Summary */}
        <div className="p-3 bg-muted/50 rounded">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className={`font-bold text-base ${efficiency.color}`}>
                {efficiency.label}
              </div>
              <div className="text-sm text-muted-foreground">Status</div>
            </div>
            <div>
              <div className="font-bold text-base">
                {cacheStats.evictionCount < cacheStats.totalEntries * 0.1 ? 'Niedrig' :
                  cacheStats.evictionCount < cacheStats.totalEntries * 0.3 ? 'Mittel' : 'Hoch'}
              </div>
              <div className="text-sm text-muted-foreground">Evictions</div>
            </div>
            <div>
              <div className="font-bold text-base">
                {cacheStats.averageAccessTime < 10 ? 'Schnell' :
                  cacheStats.averageAccessTime < 50 ? 'Normal' : 'Langsam'}
              </div>
              <div className="text-sm text-muted-foreground">Geschwindigkeit</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}