"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WarehouseController = void 0;
class WarehouseController {
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
     * Holt die Lagerauslastungsdaten für das Lager 200
     */
    async getLagerauslastung200(req, res) {
        try {
            const { startDate, endDate } = req.query;
            // Baue die WHERE-Bedingung auf
            const where = {};
            if (startDate || endDate) {
                where.aufnahmeDatum = {};
                // Konvertiere das Datum in das Format 'DD.MM.YY' für die Abfrage
                if (startDate) {
                    const date = new Date(startDate);
                    const day = String(date.getDate()).padStart(2, '0');
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const year = String(date.getFullYear()).slice(-2);
                    where.aufnahmeDatum.gte = `${day}.${month}.${year}`;
                }
                if (endDate) {
                    const date = new Date(endDate);
                    const day = String(date.getDate()).padStart(2, '0');
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const year = String(date.getFullYear()).slice(-2);
                    where.aufnahmeDatum.lte = `${day}.${month}.${year}`;
                }
            }
            // Hole die Rohdaten
            const rawData = await this.prisma.bestand200.findMany({
                where,
                select: {
                    aufnahmeDatum: true,
                    auslastungA: true,
                    auslastungB: true,
                    auslastungC: true,
                },
                orderBy: {
                    aufnahmeDatum: 'asc',
                },
            });
            // Gruppiere nach Datum und berechne Durchschnitte
            const groupedByDate = rawData.reduce((acc, item) => {
                if (!item.aufnahmeDatum)
                    return acc;
                // Konvertiere das Datum von 'DD.MM.YY' zu 'YYYY-MM-DD'
                const [day, month, year] = item.aufnahmeDatum.split('.').map(Number);
                const date = new Date(2000 + year, month - 1, day);
                const dateStr = date.toISOString().split('T')[0];
                if (!acc[dateStr]) {
                    acc[dateStr] = [0, 0, 0, 0]; // [sumA, sumB, sumC, count]
                }
                // Konvertiere alle Werte zu Zahlen, um Typenkonflikte zu vermeiden
                const a = Number(item.auslastungA) || 0;
                const b = Number(item.auslastungB) || 0;
                const c = Number(item.auslastungC) || 0;
                acc[dateStr][0] += a;
                acc[dateStr][1] += b;
                acc[dateStr][2] += c;
                acc[dateStr][3] += 1;
                return acc;
            }, {});
            // Transformiere in das gewünschte Format
            const result = Object.entries(groupedByDate).map(([date, values]) => {
                const [sumA, sumB, sumC, count] = values;
                const avgA = count > 0 ? sumA / count : 0;
                const avgB = count > 0 ? sumB / count : 0;
                const avgC = count > 0 ? sumC / count : 0;
                return {
                    date,
                    auslastungA: parseFloat(avgA.toFixed(2)),
                    auslastungB: parseFloat(avgB.toFixed(2)),
                    auslastungC: parseFloat(avgC.toFixed(2)),
                    gesamt: parseFloat(((avgA + avgB + avgC) / 3).toFixed(2))
                };
            });
            res.json(result);
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Lagerauslastung 200:', error);
            res.status(500).json({ error: 'Interner Serverfehler' });
        }
    }
    /**
     * Holt die Lagerauslastungsdaten für das Lager 240
     */
    async getLagerauslastung240(req, res) {
        try {
            const { startDate, endDate } = req.query;
            // Baue die WHERE-Bedingung auf
            const where = {};
            if (startDate || endDate) {
                where.aufnahmeDatum = {};
                // Konvertiere das Datum in das Format 'DD.MM.YY' für die Abfrage
                if (startDate) {
                    const date = new Date(startDate);
                    const day = String(date.getDate()).padStart(2, '0');
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const year = String(date.getFullYear()).slice(-2);
                    where.aufnahmeDatum.gte = `${day}.${month}.${year}`;
                }
                if (endDate) {
                    const date = new Date(endDate);
                    const day = String(date.getDate()).padStart(2, '0');
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const year = String(date.getFullYear()).slice(-2);
                    where.aufnahmeDatum.lte = `${day}.${month}.${year}`;
                }
            }
            // Hole die Rohdaten (vorerst aus bestand200, da wir keine bestand240 Tabelle haben)
            const rawData = await this.prisma.bestand200.findMany({
                where,
                select: {
                    aufnahmeDatum: true,
                    auslastungA: true,
                    auslastungB: true,
                    auslastungC: true,
                },
                orderBy: {
                    aufnahmeDatum: 'asc',
                },
            });
            // Gruppiere nach Datum und berechne Durchschnitte
            const groupedByDate = rawData.reduce((acc, item) => {
                if (!item.aufnahmeDatum)
                    return acc;
                // Konvertiere das Datum von 'DD.MM.YY' zu 'YYYY-MM-DD'
                const [day, month, year] = item.aufnahmeDatum.split('.').map(Number);
                const date = new Date(2000 + year, month - 1, day);
                const dateStr = date.toISOString().split('T')[0];
                if (!acc[dateStr]) {
                    acc[dateStr] = [0, 0, 0, 0]; // [sumA, sumB, sumC, count]
                }
                // Konvertiere alle Werte zu Zahlen und multipliziere mit 0.8 für Lager 240
                const a = (Number(item.auslastungA) || 0) * 0.8;
                const b = (Number(item.auslastungB) || 0) * 0.8;
                const c = (Number(item.auslastungC) || 0) * 0.8;
                acc[dateStr][0] += a;
                acc[dateStr][1] += b;
                acc[dateStr][2] += c;
                acc[dateStr][3] += 1;
                return acc;
            }, {});
            // Transformiere in das gewünschte Format
            const result = Object.entries(groupedByDate).map(([date, values]) => {
                const [sumA, sumB, sumC, count] = values;
                const avgA = count > 0 ? sumA / count : 0;
                const avgB = count > 0 ? sumB / count : 0;
                const avgC = count > 0 ? sumC / count : 0;
                return {
                    date,
                    auslastungA: parseFloat(avgA.toFixed(2)),
                    auslastungB: parseFloat(avgB.toFixed(2)),
                    auslastungC: parseFloat(avgC.toFixed(2)),
                    gesamt: parseFloat(((avgA + avgB + avgC) / 3).toFixed(2))
                };
            });
            res.json(result);
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Lagerauslastung 240:', error);
            res.status(500).json({ error: 'Interner Serverfehler' });
        }
    }
}
exports.WarehouseController = WarehouseController;
