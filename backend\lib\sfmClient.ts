/**
 * SFM Dashboard Prisma Client
 * Separate Client für die SFM-Dashboard-Datenbank gemäß PrismaRegeln.md
 * 
 * Dieser Client wird für alle SFM-Dashboard-bezogenen Datenbankoperationen verwendet.
 * Er nutzt das separate Schema aus prisma-sfm-dashboard/schema.prisma
 */

import { PrismaClient } from '@prisma-sfm-dashboard/client';

// SFM Dashboard-spezifischer Prisma Client
const sfmClient = new PrismaClient({
  datasources: {
    db: {
      url: process.env.SFM_DATABASE_URL || 'file:../../../data/sfm-dashboard.db'
    }
  },
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error']
});

// Graceful shutdown handler
process.on('beforeExit', async () => {
  await sfmClient.$disconnect();
});

process.on('SIGINT', async () => {
  await sfmClient.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await sfmClient.$disconnect();
  process.exit(0);
});

export { sfmClient };
export default sfmClient;