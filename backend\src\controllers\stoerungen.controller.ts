import { Request, Response } from 'express';
import { StoerungenRepository } from '../repositories/stoerungen.repository';
import { PrismaClient } from '@prisma-sfm-dashboard/client';
import { z } from 'zod';
import { getBackendCache } from '../services/cache.service';
import { EmailService } from '../services/email.service';
import multer from 'multer';
import * as path from 'path';
import * as fs from 'fs';

const prisma = new PrismaClient();
const stoerungenRepo = StoerungenRepository.getInstance(prisma);
const emailService = new EmailService();

// Configure multer for image uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'stoerungen');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'stoerung-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const imageUpload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit for images
  },
  fileFilter: (req: any, file: any, cb: any) => {
    // Allow only image files
    const allowedTypes = [
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/gif',
      'image/webp'
    ];
    
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const fileExtension = path.extname(file.originalname).toLowerCase();
    
    if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Nur Bilddateien sind erlaubt (JPG, PNG, GIF, WebP)'), false);
    }
  }
});

const createStoerungSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  status: z.enum(['NEW', 'IN_PROGRESS', 'RESOLVED']).default('NEW'),
  category: z.string().optional(),
  affected_system: z.string().optional(),
  location: z.string().optional(),
  reported_by: z.string().optional(),
  assigned_to: z.string().optional(),
  tags: z.array(z.string()).optional(),
<<<<<<< HEAD
  resolution_steps: z.string().optional(),
  root_cause: z.string().optional(),
  lessons_learned: z.string().optional(),
  send_protocol: z.boolean().optional().default(false), // Neues Feld für E-Mail-Versand
=======
>>>>>>> parent of 99336204 (feat: add scrollbar styling and improve UI components)
});

const updateStoerungSchema = createStoerungSchema.partial();

const addCommentSchema = z.object({
  stoerung_id: z.number(),
  user_id: z.string().optional(),
  comment: z.string().min(1, 'Comment is required'),
});

const updateSystemStatusSchema = z.object({
  system_name: z.string().min(1, 'System name is required'),
  status: z.enum(['OK', 'WARNING', 'ERROR']),
  metadata: z.any().optional(),
});

export class StoerungenController {
  async getStoerungen(req: Request, res: Response) {
    try {
      const { status, severity, category, affected_system, limit, offset } = req.query;

      const options = {
        status: status as string,
        severity: severity as string,
        category: category as string,
        affected_system: affected_system as string,
        limit: limit ? parseInt(limit as string) : undefined,
        offset: offset ? parseInt(offset as string) : undefined,
      };

      // Remove undefined values
      Object.keys(options).forEach(key => {
        if (options[key as keyof typeof options] === undefined) {
          delete options[key as keyof typeof options];
        }
      });

      const stoerungen = await stoerungenRepo.getStoerungen(options);
      res.json({
        success: true,
        data: stoerungen
      });
    } catch (error) {
      console.error('Error fetching störungen:', error);
      res.status(500).json({ success: false, error: 'Failed to fetch störungen' });
    }
  }

  async getStoerungById(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid ID' });
      }

      const stoerung = await stoerungenRepo.getStoerungById(id);
      if (!stoerung) {
        return res.status(404).json({ error: 'Störung not found' });
      }

      res.json({
        success: true,
        data: stoerung
      });
    } catch (error) {
      console.error('Error fetching störung:', error);
      res.status(500).json({ success: false, error: 'Failed to fetch störung' });
    }
  }

  async createStoerung(req: Request, res: Response) {
    try {
      const validatedData = createStoerungSchema.parse(req.body);
      
      // send_protocol aus den Daten entfernen, da es nicht in der DB gespeichert wird
      const { send_protocol, ...stoerungDataForDB } = validatedData;
      const stoerung = await stoerungenRepo.createStoerung(stoerungDataForDB);
      
      // Automatischer E-Mail-Versand, wenn send_protocol aktiviert ist
      if (send_protocol && validatedData.assigned_to) {
        try {
          console.log('Sende Störungs-E-Mail automatisch...');
          
          // E-Mail-Service-Konfiguration überprüfen
          const isConfigured = await emailService.verifyConnection();
          if (isConfigured) {
            // Störungsdaten für E-Mail-Template vorbereiten
            const stoerungData = {
              title: validatedData.title,
              description: validatedData.description || '',
              severity: validatedData.severity,
              affected_system: validatedData.affected_system || 'Nicht angegeben',
              location: validatedData.location,
              reported_by: validatedData.reported_by
            };
            
            // E-Mail an zugewiesene Person senden
            const emailSuccess = await emailService.sendStoerungEmail(
              validatedData.assigned_to, // E-Mail-Adresse der zugewiesenen Person
              stoerungData
            );
            
            if (emailSuccess) {
              console.log('Störungs-E-Mail erfolgreich versendet an:', validatedData.assigned_to);
            } else {
              console.warn('Störungs-E-Mail konnte nicht versendet werden');
            }
          } else {
            console.warn('E-Mail-Service ist nicht konfiguriert - E-Mail wird nicht versendet');
          }
        } catch (emailError) {
          console.error('Fehler beim automatischen E-Mail-Versand:', emailError);
          // E-Mail-Fehler sollen die Störungserstellung nicht blockieren
        }
      }
      
      res.status(201).json({
        success: true,
        data: stoerung,
        emailSent: send_protocol && validatedData.assigned_to ? true : false
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Validation error', details: error.errors });
      }
      console.error('Error creating störung:', error);
      res.status(500).json({ success: false, error: 'Failed to create störung' });
    }
  }

  async updateStoerung(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid ID' });
      }

      const validatedData = updateStoerungSchema.parse(req.body);
      
      // Calculate MTTR if resolving
      let updateData: any = { ...validatedData };
      if (validatedData.status === 'RESOLVED') {
        const existing = await stoerungenRepo.getStoerungById(id);
        if (existing && !existing.resolved_at) {
          const mttr = Math.round((Date.now() - new Date(existing.created_at).getTime()) / (1000 * 60));
          updateData.mttr_minutes = mttr;
        }
      }

      const stoerung = await stoerungenRepo.updateStoerung(id, updateData);
      res.json({
        success: true,
        data: stoerung
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Validation error', details: error.errors });
      }
      console.error('Error updating störung:', error);
      res.status(500).json({ success: false, error: 'Failed to update störung' });
    }
  }

  async deleteStoerung(req: Request, res: Response) {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: 'Invalid ID' });
      }

      const success = await stoerungenRepo.deleteStoerung(id);
      if (!success) {
        return res.status(404).json({ error: 'Störung not found' });
      }

      res.status(204).send();
    } catch (error) {
      console.error('Error deleting störung:', error);
      res.status(500).json({ success: false, error: 'Failed to delete störung' });
    }
  }

  async addComment(req: Request, res: Response) {
    try {
      const validatedData = addCommentSchema.parse(req.body);
      const comment = await stoerungenRepo.addComment(validatedData);
      
      res.status(201).json({
        success: true,
        data: comment
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Validation error', details: error.errors });
      }
      console.error('Error adding comment:', error);
      res.status(500).json({ success: false, error: 'Failed to add comment' });
    }
  }

  async getStoerungsStats(req: Request, res: Response) {
    try {
      const stats = await stoerungenRepo.getStoerungsStats();
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Error fetching störungen stats:', error);
      res.status(500).json({ success: false, error: 'Failed to fetch störungen stats' });
    }
  }

  async uploadImage(req: Request, res: Response) {
    try {
      const stoerungId = parseInt(req.params.id);
      if (isNaN(stoerungId)) {
        return res.status(400).json({ error: 'Invalid Störung ID' });
      }

      // Check if störung exists
      const stoerung = await stoerungenRepo.getStoerungById(stoerungId);
      if (!stoerung) {
        return res.status(404).json({ error: 'Störung not found' });
      }

      if (!req.file) {
        return res.status(400).json({ error: 'No image file provided' });
      }

      // Save attachment info to database
      const attachment = await prisma.stoerungsAttachment.create({
        data: {
          stoerung_id: stoerungId,
          filename: req.file.originalname,
          stored_name: req.file.filename,
          file_path: req.file.path,
          file_size: req.file.size,
          mime_type: req.file.mimetype,
          file_type: 'image',
          uploaded_by: req.body.uploaded_by || 'system'
        }
      });

      res.status(201).json({
        success: true,
        data: {
          id: attachment.id,
          filename: attachment.filename,
          file_size: attachment.file_size,
          mime_type: attachment.mime_type,
          created_at: attachment.created_at
        }
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      // Clean up uploaded file if database save failed
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      res.status(500).json({ success: false, error: 'Failed to upload image' });
    }
  }

  async getAttachments(req: Request, res: Response) {
    try {
      const stoerungId = parseInt(req.params.id);
      if (isNaN(stoerungId)) {
        return res.status(400).json({ error: 'Invalid Störung ID' });
      }

      const attachments = await prisma.stoerungsAttachment.findMany({
        where: { stoerung_id: stoerungId },
        orderBy: { created_at: 'desc' },
        select: {
          id: true,
          filename: true,
          file_size: true,
          mime_type: true,
          file_type: true,
          uploaded_by: true,
          created_at: true
        }
      });

      res.json({
        success: true,
        data: attachments
      });
    } catch (error) {
      console.error('Error fetching attachments:', error);
      res.status(500).json({ success: false, error: 'Failed to fetch attachments' });
    }
  }

  async deleteAttachment(req: Request, res: Response) {
    try {
      const attachmentId = parseInt(req.params.attachmentId);
      if (isNaN(attachmentId)) {
        return res.status(400).json({ error: 'Invalid attachment ID' });
      }

      const attachment = await prisma.stoerungsAttachment.findUnique({
        where: { id: attachmentId }
      });

      if (!attachment) {
        return res.status(404).json({ error: 'Attachment not found' });
      }

      // Delete file from filesystem
      if (fs.existsSync(attachment.file_path)) {
        fs.unlinkSync(attachment.file_path);
      }

      // Delete from database
      await prisma.stoerungsAttachment.delete({
        where: { id: attachmentId }
      });

      res.status(204).send();
    } catch (error) {
      console.error('Error deleting attachment:', error);
      res.status(500).json({ success: false, error: 'Failed to delete attachment' });
    }
  }

  async getAttachmentFile(req: Request, res: Response) {
    try {
      const attachmentId = parseInt(req.params.attachmentId);
      if (isNaN(attachmentId)) {
        return res.status(400).json({ error: 'Invalid attachment ID' });
      }

      const attachment = await prisma.stoerungsAttachment.findUnique({
        where: { id: attachmentId }
      });

      if (!attachment) {
        return res.status(404).json({ error: 'Attachment not found' });
      }

      // Check if file exists
      if (!fs.existsSync(attachment.file_path)) {
        return res.status(404).json({ error: 'File not found on disk' });
      }

      // Set appropriate headers
      res.setHeader('Content-Type', attachment.mime_type || 'application/octet-stream');
      res.setHeader('Content-Disposition', `inline; filename="${attachment.filename}"`);
      
      // Stream the file
      const fileStream = fs.createReadStream(attachment.file_path);
      fileStream.pipe(res);
    } catch (error) {
      console.error('Error serving attachment file:', error);
      res.status(500).json({ success: false, error: 'Failed to serve attachment file' });
    }
  }

  async getActiveStoerungen(req: Request, res: Response) {
    try {
      const active = await stoerungenRepo.getStoerungen({
        status: 'NEW,IN_PROGRESS',
      });
      res.json({
        success: true,
        data: active
      });
    } catch (error) {
      console.error('Error fetching active störungen:', error);
      res.status(500).json({ success: false, error: 'Failed to fetch active störungen' });
    }
  }

  async getSystemStatus(req: Request, res: Response) {
    try {
      const statuses = await stoerungenRepo.getSystemStatus();
      res.json({
        success: true,
        data: statuses
      });
    } catch (error) {
      console.error('Error fetching system status:', error);
      res.status(500).json({ success: false, error: 'Failed to fetch system status' });
    }
  }

  async updateSystemStatus(req: Request, res: Response) {
    try {
      const validatedData = updateSystemStatusSchema.parse(req.body);
      const status = await stoerungenRepo.updateSystemStatus(validatedData);
      
      res.json({
        success: true,
        data: status
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: 'Validation error', details: error.errors });
      }
      console.error('Error updating system status:', error);
      res.status(500).json({ success: false, error: 'Failed to update system status' });
    }
  }
}

// Export the upload middleware
export const imageUploadMiddleware = imageUpload.single('image');