import { spawn, ChildProcessWithoutNullStreams } from 'child_process';
import fs from 'fs';
import path from 'path';

export type RunnerEvents = {
  onStdout?: (line: string) => void;
  onStderr?: (line: string) => void;
  onClose?: (code: number | null) => void;
};

export function runPythonWorkflow(scriptPath: string, env?: Record<string, string>, events?: RunnerEvents) {
  const pythonCmd = process.env.PYTHON_PATH || 'python';

  const proc: ChildProcessWithoutNullStreams = spawn(pythonCmd, [scriptPath], {
    cwd: process.cwd(),
    env: { ...process.env, ...(env ?? {}) },
    shell: process.platform === 'win32',
    windowsHide: true,
  });

  proc.stdout.on('data', (d) => {
    const line = d.toString();
    events?.onStdout?.(line);
  });

  proc.stderr.on('data', (d) => {
    const line = d.toString();
    events?.onStderr?.(line);
  });

  proc.on('close', (code) => {
    events?.onClose?.(code);
  });

  return proc;
}

// Einfacher Tail-Reader für Logdateien
export function tailFile(filePath: string, onLine: (line: string) => void) {
  let position = 0;
  const interval = setInterval(() => {
    try {
      const stat = fs.statSync(filePath);
      const size = stat.size;
      if (size > position) {
        const fd = fs.openSync(filePath, 'r');
        const buf = Buffer.alloc(size - position);
        fs.readSync(fd, buf, 0, size - position, position);
        fs.closeSync(fd);
        const chunk = buf.toString('utf-8');
        position = size;
        chunk.split(/\r?\n/).filter(Boolean).forEach(line => onLine(line));
      }
    } catch {
      // ignore
    }
  }, 1000);

  return () => clearInterval(interval);
}