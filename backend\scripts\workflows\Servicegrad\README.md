# Servicegrad Workflow Integration

## Overview
The Servicegrad workflow is a comprehensive SAP automation process that:

1. **SAP Data Export**: Connects to SAP using transaction `/n/LSGIT/VS_DLV_CHECK`
2. **Excel Processing**: Processes exported data and calculates service level metrics
3. **Email Reporting**: Sends automated reports with charts and tables
4. **Database Storage**: Stores results in the SQLite database for dashboard visualization

## Files Structure

```
backend/scripts/workflows/Servicegrad/
├── Servicegrad_Workflow.py     # Main workflow script
├── workflow_logger.py          # Logging utility
└── README.md                   # This documentation
```

## Integration Points

### Frontend Components
- `src/modules/backend/components/workflows/ServicegradWorkflowCard.tsx` - Specialized UI card
- `src/modules/backend/components/workflows/WorkflowGrid.tsx` - Main workflow grid
- `src/modules/backend/pages/WorkflowPage.tsx` - Main workflow page

### Backend Services
- `backend/src/services/workflowService.ts` - Workflow execution service
- `backend/src/routes/workflow.routes.ts` - API endpoints
- `backend/src/controllers/workflowController.ts` - Request handlers

### Database Integration
The workflow stores results in the `dispatch_data` table:
```sql
CREATE TABLE dispatch_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    datum DATE,
    servicegrad REAL
);
```

## Configuration

### SAP Connection
```python
SAP_EXECUTABLE_PATH = r"C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe"
SAP_SYSTEM_ID = "PS4"
SAP_CLIENT = "009"
SAP_LANGUAGE = "DE"
```

### File Paths
```python
EXPORT_DIR = r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\SG"
MAIN_EXCEL_PATH = r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\Workflows\Servicegrad_BluePrint.xlsx"
TARGET_EXCEL_PATH = r"\\adsgroup\Group\UIL-CL-Zentral\04 Statistiken & Auswertungen\01 Statistiken\Servicegrad LO\Geschaeftsjahr 2425\Servicegrad LSS Bot\Servicegrad Kennzahlen.xlsx"
```

### Email Configuration
```python
EMAIL_RECIPIENT = "<EMAIL>"
SMTP_SERVER = "smtp.gmail.com"
SMTP_USERNAME = "<EMAIL>"
SMTP_PASSWORD = "Power-Automate7"
```

## Workflow Steps

1. **SAP Export**
   - Connects to SAP GUI
   - Executes transaction with calculated date (Monday uses Friday, other days use previous day)
   - Exports data to Excel file

2. **Data Processing**
   - Copies SAP data to working Excel file
   - Calculates service level metrics for each warehouse
   - Creates summary tables with German formatting

3. **Report Generation**
   - Transfers data to target Excel file
   - Applies formatting and styling
   - Creates charts and visualizations

4. **Email Distribution**
   - Generates HTML email with embedded tables and charts
   - Sends via Outlook (primary) or SMTP (fallback)
   - Includes Excel attachment

5. **Database Storage**
   - Extracts service level for warehouse 512
   - Stores in SQLite database for dashboard display
   - Updates existing records or creates new ones

6. **Cleanup**
   - Resets working files for next execution
   - Closes Excel and SAP processes

## API Endpoints

### Execute Workflow
```http
POST /api/workflows/execute
Content-Type: application/json

{
  "processId": "servicegrad"
}
```

### Get Workflow Status
```http
GET /api/workflows/processes
```

### Get Execution Logs
```http
GET /api/workflows/logs?workflowId=servicegrad
```

## Error Handling

The workflow includes comprehensive error handling:
- SAP connection failures
- Excel processing errors
- Email delivery issues
- Database connection problems

All errors are logged to both console and log files with timestamps.

## Monitoring

The workflow can be monitored through:
- Dashboard UI in the Workflows section
- Log files with timestamps
- Database records for execution history
- Email notifications for failures

## Dependencies

### Python Packages
- `xlwings` - Excel automation
- `pandas` - Data processing
- `win32com.client` - Outlook integration
- `smtplib` - Email sending
- `sqlite3` - Database operations

### System Requirements
- Windows OS with SAP GUI installed
- Microsoft Excel
- Microsoft Outlook (optional, SMTP fallback available)
- Python 3.x with required packages

## Troubleshooting

### Common Issues

1. **SAP Connection Failed**
   - Verify SAP GUI is installed
   - Check SAP system credentials
   - Ensure network connectivity

2. **Excel Processing Errors**
   - Verify Excel file paths exist
   - Check file permissions
   - Ensure Excel is not open by other processes

3. **Email Delivery Failed**
   - Check Outlook configuration
   - Verify SMTP credentials
   - Test network connectivity

4. **Database Errors**
   - Verify database file exists
   - Check file permissions
   - Ensure SQLite is accessible

### Debug Mode
Run the script with debug logging:
```python
python Servicegrad_Workflow.py --debug
```

### Manual Execution
For testing purposes, the workflow can be executed manually:
```python
python Servicegrad_Workflow.py
```

## Maintenance

### Regular Tasks
- Monitor log files for errors
- Verify email delivery
- Check database integrity
- Update SAP credentials as needed

### Updates
When updating the workflow:
1. Test in development environment
2. Backup existing configuration
3. Update production files
4. Monitor first execution
5. Verify all integration points

## Support

For issues or questions regarding the Servicegrad workflow integration:
1. Check log files for error details
2. Verify all configuration settings
3. Test individual components
4. Contact system administrator if needed