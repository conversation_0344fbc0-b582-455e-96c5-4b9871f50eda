/**
 * Base Repository Interface
 * 
 * Definiert die grundlegenden CRUD-Operationen und Caching-Verhalten
 * für alle Repository-Implementierungen.
 */

export interface BaseRepository<T, TCreateInput = Partial<T>, TUpdateInput = Partial<T>> {
  /**
   * Einzelnen Datensatz anhand der ID abrufen
   */
  findById(id: string | number): Promise<T | null>;

  /**
   * Alle Datensätze abrufen (mit optionaler Paginierung)
   */
  findAll(options?: FindAllOptions): Promise<T[]>;

  /**
   * Datensätze anhand von Kriterien suchen
   */
  findWhere(criteria: Partial<T>): Promise<T[]>;

  /**
   * Einzelnen Datensatz anhand von Kriterien suchen
   */
  findOneWhere(criteria: Partial<T>): Promise<T | null>;

  /**
   * Neuen Datensatz erstellen
   */
  create(data: TCreateInput): Promise<T>;

  /**
   * Datensatz aktualisieren
   */
  update(id: string | number, data: TUpdateInput): Promise<T | null>;

  /**
   * Datensatz löschen
   */
  delete(id: string | number): Promise<boolean>;

  /**
   * Anzahl der Datensätze ermitteln
   */
  count(criteria?: Partial<T>): Promise<number>;

  /**
   * Repository-Cache invalidieren
   */
  invalidateCache(key?: string): Promise<void>;

  /**
   * Repository-Statistiken abrufen
   */
  getStats(): Promise<RepositoryStats>;
}

/**
 * Options für findAll-Operationen
 */
export interface FindAllOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  include?: string[];
  where?: any;
}

/**
 * Repository-Statistiken
 */
export interface RepositoryStats {
  totalQueries: number;
  cacheHits: number;
  cacheMisses: number;
  hitRate: number;
  avgQueryTime: number;
  lastAccessed: Date;
}

/**
 * Cache-Konfiguration für Repositories
 */
export interface RepositoryCacheConfig {
  enabled: boolean;
  ttl: number; // Time To Live in Millisekunden
  maxEntries: number;
  keyPrefix: string;
}