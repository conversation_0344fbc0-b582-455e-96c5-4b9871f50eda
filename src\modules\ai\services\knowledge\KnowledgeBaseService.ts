/**
 * Knowledge Base Management Service
 * 
 * Manages document ingestion, preprocessing, indexing, and retrieval for the RAG system.
 * Provides comprehensive knowledge base operations with metadata support and health monitoring.
 */

import { AIBaseService, AIServiceConfig, AIServiceStatus } from '../base/AIBaseService';
import { VectorDatabaseService } from '../vector/VectorDatabaseService';
import { EmbeddingService } from '../embedding/EmbeddingService';
import {
  Document,
  DocumentChunk,
  VectorMetadata,
  AIServiceError
} from '../types';

/**
 * Knowledge Base Service Configuration
 */
export interface KnowledgeBaseConfig extends AIServiceConfig {
  chunkSize?: number;
  chunkOverlap?: number;
  maxDocumentSize?: number;
  supportedFormats?: string[];
  autoIndex?: boolean;
  preprocessingEnabled?: boolean;
}

/**
 * Document processing statistics
 */
export interface DocumentProcessingStats {
  totalDocuments: number;
  totalChunks: number;
  totalVectors: number;
  averageChunksPerDocument: number;
  processingTime: number;
  lastProcessed: Date;
}

/**
 * Knowledge base health metrics
 */
export interface KnowledgeBaseHealth {
  isHealthy: boolean;
  totalDocuments: number;
  totalChunks: number;
  indexedVectors: number;
  lastIndexUpdate: Date;
  storageSize: number;
  averageDocumentSize: number;
  processingQueue: number;
  errors: string[];
}

/**
 * Document ingestion result
 */
export interface IngestionResult {
  documentId: string;
  success: boolean;
  chunksCreated: number;
  vectorsIndexed: number;
  processingTime: number;
  errors: string[];
}

/**
 * Knowledge Base Management Service
 */
export class KnowledgeBaseService extends AIBaseService {
  readonly serviceName = 'KnowledgeBaseService';

  private vectorService: VectorDatabaseService;
  private embeddingService: EmbeddingService;
  private knowledgeConfig: KnowledgeBaseConfig;
  private db: any; // SQLite database connection
  private processingQueue: Map<string, Promise<IngestionResult>> = new Map();

  constructor(
    vectorService: VectorDatabaseService,
    embeddingService: EmbeddingService,
    config: KnowledgeBaseConfig = {}
  ) {
    super(config);
    this.vectorService = vectorService;
    this.embeddingService = embeddingService;
    this.knowledgeConfig = {
      chunkSize: 1000,
      chunkOverlap: 200,
      maxDocumentSize: 10 * 1024 * 1024, // 10MB
      supportedFormats: ['txt', 'md', 'json', 'csv'],
      autoIndex: true,
      preprocessingEnabled: true,
      ...config
    };
  }

  /**
   * Initialize the knowledge base service
   */
  protected async onInitialize(): Promise<void> {
    try {
      // Initialize database connection
      const Database = require('better-sqlite3');
      this.db = new Database(process.env.KNOWLEDGE_DB_PATH || './database/knowledge.db');

      // Enable optimizations
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('synchronous = NORMAL');
      this.db.pragma('cache_size = 1000000');

      // Create knowledge base tables
      await this.createKnowledgeTables();

      // Initialize dependent services
      try {
        await this.vectorService.initialize();
      } catch (error) {
        this.log('Vector service initialization failed', error);
        // Continue - service might already be initialized
      }

      try {
        await this.embeddingService.initialize();
      } catch (error) {
        this.log('Embedding service initialization failed', error);
        // Continue - service might already be initialized
      }

      this.log('Knowledge base service initialized successfully');
    } catch (error) {
      this.log('Failed to initialize knowledge base service', error);
      throw error;
    }
  }

  /**
   * Create knowledge base tables
   */
  private async createKnowledgeTables(): Promise<void> {
    try {
      // Knowledge base documents table
      const createDocumentsTable = `
        CREATE TABLE IF NOT EXISTS knowledge_documents (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          content TEXT NOT NULL,
          document_type TEXT NOT NULL,
          source_url TEXT,
          file_path TEXT,
          file_size INTEGER,
          content_hash TEXT,
          department TEXT,
          metadata JSON,
          processing_status TEXT DEFAULT 'pending',
          processing_error TEXT,
          indexed_at DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;
      this.db.exec(createDocumentsTable);

      // Document chunks table
      const createChunksTable = `
        CREATE TABLE IF NOT EXISTS document_chunks (
          id TEXT PRIMARY KEY,
          document_id TEXT NOT NULL,
          content TEXT NOT NULL,
          chunk_index INTEGER NOT NULL,
          start_position INTEGER,
          end_position INTEGER,
          token_count INTEGER,
          embedding_id TEXT,
          metadata JSON,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (document_id) REFERENCES knowledge_documents(id) ON DELETE CASCADE
        )
      `;
      this.db.exec(createChunksTable);

      // Processing queue table
      const createQueueTable = `
        CREATE TABLE IF NOT EXISTS processing_queue (
          id TEXT PRIMARY KEY,
          document_id TEXT NOT NULL,
          operation_type TEXT NOT NULL,
          priority INTEGER DEFAULT 0,
          status TEXT DEFAULT 'pending',
          error_message TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          started_at DATETIME,
          completed_at DATETIME,
          FOREIGN KEY (document_id) REFERENCES knowledge_documents(id) ON DELETE CASCADE
        )
      `;
      this.db.exec(createQueueTable);

      // Create indices for better performance
      const createIndices = `
        CREATE INDEX IF NOT EXISTS idx_documents_type ON knowledge_documents(document_type);
        CREATE INDEX IF NOT EXISTS idx_documents_department ON knowledge_documents(department);
        CREATE INDEX IF NOT EXISTS idx_documents_status ON knowledge_documents(processing_status);
        CREATE INDEX IF NOT EXISTS idx_documents_created ON knowledge_documents(created_at);
        CREATE INDEX IF NOT EXISTS idx_chunks_document ON document_chunks(document_id);
        CREATE INDEX IF NOT EXISTS idx_chunks_index ON document_chunks(chunk_index);
        CREATE INDEX IF NOT EXISTS idx_queue_status ON processing_queue(status);
        CREATE INDEX IF NOT EXISTS idx_queue_priority ON processing_queue(priority DESC);
      `;
      this.db.exec(createIndices);

      this.log('Knowledge base tables created successfully');
    } catch (error) {
      this.log('Failed to create knowledge base tables', error);
      throw error;
    }
  }

  /**
   * Ingest a document into the knowledge base
   */
  async ingestDocument(document: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>): Promise<IngestionResult> {
    const documentId = this.generateDocumentId(document.title, document.content);

    return this.handleAIError(
      async () => {
        // Check if already processing
        if (this.processingQueue.has(documentId)) {
          return await this.processingQueue.get(documentId)!;
        }

        // Start processing
        const processingPromise = this.processDocument(documentId, document);
        this.processingQueue.set(documentId, processingPromise);

        try {
          const result = await processingPromise;
          return result;
        } finally {
          this.processingQueue.delete(documentId);
        }
      },
      async () => {
        return {
          documentId,
          success: false,
          chunksCreated: 0,
          vectorsIndexed: 0,
          processingTime: 0,
          errors: ['Document ingestion failed']
        };
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Process a document through the ingestion pipeline
   */
  private async processDocument(
    documentId: string,
    document: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<IngestionResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let chunksCreated = 0;
    let vectorsIndexed = 0;

    try {
      // Validate document
      this.validateDocument(document);

      // Store document in database
      await this.storeDocument(documentId, document);

      // Preprocess content if enabled
      let processedContent = document.content;
      if (this.knowledgeConfig.preprocessingEnabled) {
        processedContent = await this.preprocessContent(document.content, document.documentType);
      }

      // Create chunks
      const chunks = await this.createDocumentChunks(documentId, processedContent);
      chunksCreated = chunks.length;

      // Store chunks
      await this.storeDocumentChunks(chunks);

      // Create embeddings and index vectors if auto-indexing is enabled
      if (this.knowledgeConfig.autoIndex) {
        vectorsIndexed = await this.indexDocumentChunks(documentId, chunks);
      }

      // Update document status
      await this.updateDocumentStatus(documentId, 'completed', null);

      const processingTime = Date.now() - startTime;
      this.log(`Document processed successfully: ${documentId} (${processingTime}ms)`);

      return {
        documentId,
        success: true,
        chunksCreated,
        vectorsIndexed,
        processingTime,
        errors
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(errorMessage);

      // Update document status with error
      await this.updateDocumentStatus(documentId, 'failed', errorMessage);

      this.log(`Document processing failed: ${documentId}`, error);

      return {
        documentId,
        success: false,
        chunksCreated,
        vectorsIndexed,
        processingTime: Date.now() - startTime,
        errors
      };
    }
  }

  /**
   * Validate document before processing
   */
  private validateDocument(document: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>): void {
    if (!document.title || document.title.trim().length === 0) {
      throw new Error('Document title is required');
    }

    if (!document.content || document.content.trim().length === 0) {
      throw new Error('Document content is required');
    }

    if (document.content.length > (this.knowledgeConfig.maxDocumentSize || 10485760)) {
      throw new Error('Document size exceeds maximum allowed size');
    }

    if (!document.documentType || document.documentType.trim().length === 0) {
      throw new Error('Document type is required');
    }

    const supportedFormats = this.knowledgeConfig.supportedFormats || [];
    if (supportedFormats.length > 0 && !supportedFormats.includes(document.documentType.toLowerCase())) {
      throw new Error(`Unsupported document type: ${document.documentType}`);
    }
  }

  /**
   * Store document in database
   */
  private async storeDocument(
    documentId: string,
    document: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<void> {
    const contentHash = this.generateContentHash(document.content);

    const insertDocument = this.db.prepare(`
      INSERT OR REPLACE INTO knowledge_documents 
      (id, title, content, document_type, source_url, content_hash, department, metadata, processing_status, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'processing', CURRENT_TIMESTAMP)
    `);

    insertDocument.run(
      documentId,
      document.title,
      document.content,
      document.documentType,
      document.sourceUrl || null,
      contentHash,
      document.metadata?.department || null,
      JSON.stringify(document.metadata || {})
    );
  }

  /**
   * Preprocess document content
   */
  private async preprocessContent(content: string, documentType: string): Promise<string> {
    let processed = content;

    // Remove excessive whitespace
    processed = processed.replace(/\s+/g, ' ').trim();

    // Document type specific preprocessing
    switch (documentType.toLowerCase()) {
      case 'md':
      case 'markdown':
        // Remove markdown syntax but keep structure
        processed = processed
          .replace(/^#{1,6}\s+/gm, '') // Remove headers
          .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
          .replace(/\*(.*?)\*/g, '$1') // Remove italic
          .replace(/`(.*?)`/g, '$1') // Remove inline code
          .replace(/```[\s\S]*?```/g, '') // Remove code blocks
          .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1'); // Remove links, keep text
        break;

      case 'json':
        // Pretty format JSON for better chunking
        try {
          const parsed = JSON.parse(processed);
          processed = JSON.stringify(parsed, null, 2);
        } catch {
          // Keep original if not valid JSON
        }
        break;

      case 'csv':
        // Convert CSV to more readable format
        const lines = processed.split('\n');
        if (lines.length > 1) {
          const headers = lines[0].split(',');
          processed = lines.slice(1).map(line => {
            const values = line.split(',');
            return headers.map((header, index) => `${header}: ${values[index] || ''}`).join(', ');
          }).join('\n');
        }
        break;
    }

    return processed;
  }

  /**
   * Create document chunks
   */
  private async createDocumentChunks(documentId: string, content: string): Promise<DocumentChunk[]> {
    const chunks: DocumentChunk[] = [];
    const chunkSize = this.knowledgeConfig.chunkSize || 1000;
    const overlap = this.knowledgeConfig.chunkOverlap || 200;

    let startPosition = 0;
    let chunkIndex = 0;

    while (startPosition < content.length) {
      const endPosition = Math.min(startPosition + chunkSize, content.length);
      let chunkContent = content.substring(startPosition, endPosition);

      // Try to break at sentence boundaries if not at the end
      if (endPosition < content.length) {
        const lastSentenceEnd = Math.max(
          chunkContent.lastIndexOf('.'),
          chunkContent.lastIndexOf('!'),
          chunkContent.lastIndexOf('?')
        );

        if (lastSentenceEnd > chunkSize * 0.7) { // Only if we have a reasonable chunk
          chunkContent = chunkContent.substring(0, lastSentenceEnd + 1);
        }
      }

      const chunkId = `${documentId}_chunk_${chunkIndex}`;
      const chunk: DocumentChunk = {
        id: chunkId,
        documentId,
        content: chunkContent.trim(),
        chunkIndex,
        metadata: {
          startPosition,
          endPosition: startPosition + chunkContent.length,
          tokenCount: this.estimateTokenCount(chunkContent)
        },
        createdAt: new Date()
      };

      chunks.push(chunk);
      chunkIndex++;

      // Move start position with overlap
      startPosition += chunkContent.length - overlap;
      if (startPosition >= content.length) break;
    }

    return chunks;
  }

  /**
   * Store document chunks in database
   */
  private async storeDocumentChunks(chunks: DocumentChunk[]): Promise<void> {
    const insertChunk = this.db.prepare(`
      INSERT OR REPLACE INTO document_chunks 
      (id, document_id, content, chunk_index, start_position, end_position, token_count, metadata)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const transaction = this.db.transaction(() => {
      for (const chunk of chunks) {
        insertChunk.run(
          chunk.id,
          chunk.documentId,
          chunk.content,
          chunk.chunkIndex,
          chunk.metadata?.startPosition || null,
          chunk.metadata?.endPosition || null,
          chunk.metadata?.tokenCount || null,
          JSON.stringify(chunk.metadata || {})
        );
      }
    });

    transaction();
  }

  /**
   * Index document chunks as vectors
   */
  private async indexDocumentChunks(documentId: string, chunks: DocumentChunk[]): Promise<number> {
    let indexedCount = 0;

    for (const chunk of chunks) {
      try {
        // Create embedding for chunk
        const embedding = await this.embeddingService.createEmbedding(chunk.content);

        // Create vector metadata
        const vectorMetadata: VectorMetadata = {
          content: chunk.content,
          documentId: chunk.documentId,
          chunkIndex: chunk.chunkIndex,
          title: `Chunk ${chunk.chunkIndex}`,
          source: documentId,
          timestamp: new Date().toISOString(),
          ...chunk.metadata
        };

        // Store vector
        await this.vectorService.insertVector(chunk.id, embedding, vectorMetadata);

        // Update chunk with embedding ID
        await this.updateChunkEmbeddingId(chunk.id, chunk.id);

        indexedCount++;
      } catch (error) {
        this.log(`Failed to index chunk ${chunk.id}`, error);
        // Continue with other chunks
      }
    }

    return indexedCount;
  }

  /**
   * Update document processing status
   */
  private async updateDocumentStatus(documentId: string, status: string, error: string | null): Promise<void> {
    const updateStatus = this.db.prepare(`
      UPDATE knowledge_documents 
      SET processing_status = ?, processing_error = ?, 
          indexed_at = CASE WHEN ? = 'completed' THEN CURRENT_TIMESTAMP ELSE indexed_at END,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    updateStatus.run(status, error, status, documentId);
  }

  /**
   * Update chunk embedding ID
   */
  private async updateChunkEmbeddingId(chunkId: string, embeddingId: string): Promise<void> {
    const updateChunk = this.db.prepare(`
      UPDATE document_chunks 
      SET embedding_id = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    updateChunk.run(embeddingId, chunkId);
  }
  /**
    * Update an existing document
    */
  async updateDocument(documentId: string, updates: Partial<Document>): Promise<IngestionResult> {
    return this.handleAIError(
      async () => {
        // Check if document exists
        const existingDoc = await this.getDocument(documentId);
        if (!existingDoc) {
          throw new Error(`Document not found: ${documentId}`);
        }

        // If content is being updated, reprocess the document
        if (updates.content && updates.content !== existingDoc.content) {
          // Delete existing chunks and vectors
          await this.deleteDocumentChunks(documentId);

          // Create updated document object
          const updatedDocument = {
            ...existingDoc,
            ...updates,
            updatedAt: new Date()
          };

          // Reprocess the document
          return await this.processDocument(documentId, updatedDocument);
        } else {
          // Just update metadata
          await this.updateDocumentMetadata(documentId, updates);

          return {
            documentId,
            success: true,
            chunksCreated: 0,
            vectorsIndexed: 0,
            processingTime: 0,
            errors: []
          };
        }
      },
      async () => {
        return {
          documentId,
          success: false,
          chunksCreated: 0,
          vectorsIndexed: 0,
          processingTime: 0,
          errors: ['Document update failed']
        };
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Delete a document and all associated data
   */
  async deleteDocument(documentId: string): Promise<boolean> {
    return this.handleAIError(
      async () => {
        // Delete document chunks and vectors
        await this.deleteDocumentChunks(documentId);

        // Delete document record
        const deleteDocument = this.db.prepare('DELETE FROM knowledge_documents WHERE id = ?');
        const result = deleteDocument.run(documentId);

        this.log(`Document deleted: ${documentId}`);
        return result.changes > 0;
      },
      async () => {
        this.log(`Failed to delete document: ${documentId}`);
        return false;
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Delete document chunks and associated vectors
   */
  private async deleteDocumentChunks(documentId: string): Promise<void> {
    // Get all chunks for the document
    const getChunks = this.db.prepare('SELECT id FROM document_chunks WHERE document_id = ?');
    const chunks = getChunks.all(documentId);

    // Delete vectors for each chunk
    for (const chunk of chunks) {
      try {
        await this.vectorService.deleteVector(chunk.id);
      } catch (error) {
        this.log(`Failed to delete vector for chunk ${chunk.id}`, error);
      }
    }

    // Delete chunk records
    const deleteChunks = this.db.prepare('DELETE FROM document_chunks WHERE document_id = ?');
    deleteChunks.run(documentId);
  }

  /**
   * Get a document by ID
   */
  async getDocument(documentId: string): Promise<Document | null> {
    return this.handleAIError(
      async () => {
        const getDocument = this.db.prepare(`
          SELECT id, title, content, document_type, source_url, metadata, created_at, updated_at
          FROM knowledge_documents 
          WHERE id = ?
        `);

        const row = getDocument.get(documentId);
        if (!row) return null;

        return {
          id: row.id,
          title: row.title,
          content: row.content,
          documentType: row.document_type,
          sourceUrl: row.source_url,
          metadata: JSON.parse(row.metadata || '{}'),
          createdAt: new Date(row.created_at),
          updatedAt: new Date(row.updated_at)
        };
      },
      async () => null,
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * List documents with filtering and pagination
   */
  async listDocuments(options: {
    department?: string;
    documentType?: string;
    status?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<Document[]> {
    return this.handleAIError(
      async () => {
        let query = `
          SELECT id, title, content, document_type, source_url, metadata, created_at, updated_at
          FROM knowledge_documents 
          WHERE 1=1
        `;
        const params: any[] = [];

        if (options.department) {
          query += ' AND department = ?';
          params.push(options.department);
        }

        if (options.documentType) {
          query += ' AND document_type = ?';
          params.push(options.documentType);
        }

        if (options.status) {
          query += ' AND processing_status = ?';
          params.push(options.status);
        }

        query += ' ORDER BY updated_at DESC';

        if (options.limit) {
          query += ' LIMIT ?';
          params.push(options.limit);

          if (options.offset) {
            query += ' OFFSET ?';
            params.push(options.offset);
          }
        }

        const rows = this.db.prepare(query).all(...params);

        return rows.map((row: any) => ({
          id: row.id,
          title: row.title,
          content: row.content,
          documentType: row.document_type,
          sourceUrl: row.source_url,
          metadata: JSON.parse(row.metadata || '{}'),
          createdAt: new Date(row.created_at),
          updatedAt: new Date(row.updated_at)
        }));
      },
      async () => [],
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Get document chunks
   */
  async getDocumentChunks(documentId: string): Promise<DocumentChunk[]> {
    return this.handleAIError(
      async () => {
        const getChunks = this.db.prepare(`
          SELECT id, document_id, content, chunk_index, metadata, created_at
          FROM document_chunks 
          WHERE document_id = ?
          ORDER BY chunk_index
        `);

        const rows = getChunks.all(documentId);

        return rows.map((row: any) => ({
          id: row.id,
          documentId: row.document_id,
          content: row.content,
          chunkIndex: row.chunk_index,
          metadata: JSON.parse(row.metadata || '{}'),
          createdAt: new Date(row.created_at)
        }));
      },
      async () => [],
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Reindex all documents
   */
  async reindexAllDocuments(): Promise<{ processed: number; errors: number }> {
    return this.handleAIError(
      async () => {
        const documents = await this.listDocuments({ status: 'completed' });
        let processed = 0;
        let errors = 0;

        for (const document of documents) {
          try {
            // Get document chunks
            const chunks = await this.getDocumentChunks(document.id);

            // Reindex chunks
            await this.indexDocumentChunks(document.id, chunks);
            processed++;
          } catch (error) {
            this.log(`Failed to reindex document ${document.id}`, error);
            errors++;
          }
        }

        this.log(`Reindexing completed: ${processed} processed, ${errors} errors`);
        return { processed, errors };
      },
      async () => ({ processed: 0, errors: 1 }),
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Get knowledge base statistics
   */
  async getKnowledgeBaseStats(): Promise<DocumentProcessingStats> {
    return this.handleAIError(
      async () => {
        const statsQuery = `
          SELECT 
            COUNT(*) as total_documents,
            (SELECT COUNT(*) FROM document_chunks) as total_chunks,
            (SELECT COUNT(*) FROM document_chunks WHERE embedding_id IS NOT NULL) as total_vectors,
            MAX(updated_at) as last_processed
          FROM knowledge_documents
        `;

        const stats = this.db.prepare(statsQuery).get();

        const avgChunksQuery = `
          SELECT AVG(chunk_count) as avg_chunks
          FROM (
            SELECT COUNT(*) as chunk_count 
            FROM document_chunks 
            GROUP BY document_id
          )
        `;

        const avgStats = this.db.prepare(avgChunksQuery).get();

        return {
          totalDocuments: stats.total_documents || 0,
          totalChunks: stats.total_chunks || 0,
          totalVectors: stats.total_vectors || 0,
          averageChunksPerDocument: avgStats.avg_chunks || 0,
          processingTime: 0, // This would need to be tracked separately
          lastProcessed: stats.last_processed ? new Date(stats.last_processed) : new Date()
        };
      },
      async () => ({
        totalDocuments: 0,
        totalChunks: 0,
        totalVectors: 0,
        averageChunksPerDocument: 0,
        processingTime: 0,
        lastProcessed: new Date()
      }),
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Get knowledge base health status
   */
  async getHealthStatus(): Promise<KnowledgeBaseHealth> {
    return this.handleAIError(
      async () => {
        const stats = await this.getKnowledgeBaseStats();
        const vectorStats = await this.vectorService.getIndexStats();

        // Check for processing errors
        const errorQuery = `
          SELECT processing_error 
          FROM knowledge_documents 
          WHERE processing_status = 'failed' AND processing_error IS NOT NULL
          LIMIT 10
        `;
        const errorRows = this.db.prepare(errorQuery).all();
        const errors = errorRows.map((row: any) => row.processing_error);

        // Get storage size
        const sizeQuery = `SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()`;
        const sizeResult = this.db.prepare(sizeQuery).get();

        // Calculate average document size
        const avgSizeQuery = `SELECT AVG(LENGTH(content)) as avg_size FROM knowledge_documents`;
        const avgSizeResult = this.db.prepare(avgSizeQuery).get();

        const isHealthy = stats.totalDocuments > 0 &&
          stats.totalVectors > 0 &&
          errors.length === 0 &&
          this.processingQueue.size < 10; // Reasonable queue size

        return {
          isHealthy,
          totalDocuments: stats.totalDocuments,
          totalChunks: stats.totalChunks,
          indexedVectors: stats.totalVectors,
          lastIndexUpdate: vectorStats.lastUpdated,
          storageSize: sizeResult.size || 0,
          averageDocumentSize: avgSizeResult.avg_size || 0,
          processingQueue: this.processingQueue.size,
          errors
        };
      },
      async () => ({
        isHealthy: false,
        totalDocuments: 0,
        totalChunks: 0,
        indexedVectors: 0,
        lastIndexUpdate: new Date(),
        storageSize: 0,
        averageDocumentSize: 0,
        processingQueue: 0,
        errors: ['Health check failed']
      }),
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Update document metadata only
   */
  private async updateDocumentMetadata(documentId: string, updates: Partial<Document>): Promise<void> {
    const updateFields: string[] = [];
    const params: any[] = [];

    if (updates.title !== undefined) {
      updateFields.push('title = ?');
      params.push(updates.title);
    }

    if (updates.sourceUrl !== undefined) {
      updateFields.push('source_url = ?');
      params.push(updates.sourceUrl);
    }

    if (updates.metadata !== undefined) {
      updateFields.push('metadata = ?');
      params.push(JSON.stringify(updates.metadata));
    }

    if (updateFields.length > 0) {
      updateFields.push('updated_at = CURRENT_TIMESTAMP');
      params.push(documentId);

      const updateQuery = `
        UPDATE knowledge_documents 
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `;

      this.db.prepare(updateQuery).run(...params);
    }
  }

  /**
   * Generate document ID from title and content
   */
  private generateDocumentId(title: string, content: string): string {
    const hash = require('crypto')
      .createHash('sha256')
      .update(title + content.substring(0, 1000))
      .digest('hex');
    return `doc_${hash.substring(0, 16)}`;
  }

  /**
   * Generate content hash for duplicate detection
   */
  private generateContentHash(content: string): string {
    return require('crypto')
      .createHash('sha256')
      .update(content)
      .digest('hex');
  }

  /**
   * Estimate token count for content
   */
  private estimateTokenCount(content: string): number {
    // Rough estimation: ~4 characters per token
    return Math.ceil(content.length / 4);
  }

  /**
   * Enhanced health check for knowledge base
   */
  protected async onHealthCheck(): Promise<void> {
    if (!this.db) {
      throw new Error('Database connection not available');
    }

    // Test database connectivity
    try {
      this.db.prepare('SELECT 1').get();
    } catch (error) {
      throw new Error('Database connectivity test failed');
    }

    // Test dependent services
    try {
      const vectorStatus = await this.vectorService.healthCheck();
      if (!vectorStatus.isHealthy) {
        throw new Error('Vector service is not healthy');
      }
    } catch (error) {
      throw new Error(`Vector service health check failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    try {
      const embeddingStatus = await this.embeddingService.healthCheck();
      if (!embeddingStatus.isHealthy) {
        throw new Error('Embedding service is not healthy');
      }
    } catch (error) {
      throw new Error(`Embedding service health check failed: ${error instanceof Error ? error.message : String(error)}`);
    }

    // Check table existence
    const tables = ['knowledge_documents', 'document_chunks', 'processing_queue'];
    for (const table of tables) {
      const tableExists = this.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name=?
      `).get(table);

      if (!tableExists) {
        throw new Error(`Required table not found: ${table}`);
      }
    }
  }

  /**
   * Cleanup database resources
   */
  protected async onDestroy(): Promise<void> {
    // Clear processing queue
    this.processingQueue.clear();

    if (this.db) {
      this.db.close();
      this.log('Knowledge base database connection closed');
    }
  }
}