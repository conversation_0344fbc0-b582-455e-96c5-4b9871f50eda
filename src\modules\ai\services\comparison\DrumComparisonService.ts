/**
 * Drum Comparison Service
 * 
 * Service für den Vergleich zwischen KI-vorgeschlagenen Trommeln
 * und tatsächlich manuell ausgewählten Trommeln.
 * Bewertet die Qualität der KI-Auswahl und identifiziert Verbesserungspotentiale.
 */

import { 
  ManualDrumSelection, 
  DrumComparisonResult, 
  DrumMatch, 
  DrumDifference,
  CuttingPlan,
  DeliveryItem
} from '../types';
import { AvailableDrum } from '../../types/cutting';
import { InventoryService } from '../inventory/InventoryService';
import { CuttingOptimizerService } from '../cutting/CuttingOptimizerService';

export interface ComparisonMetrics {
  accuracyScore: number; // Genauigkeit der KI-Vorhersage (0-100)
  efficiencyDifference: number; // Effizienzunterschied in %
  wasteDifference: number; // Verschnittunterschied in %
  costImpact: number; // Geschätzte Kostenauswirkung
  learningPoints: string[]; // Erkenntnisse für KI-Verbesserung
}

export interface ComparisonOptions {
  includeAlternatives?: boolean; // Alternative Trommelvorschläge berücksichtigen
  toleranceThreshold?: number; // Toleranzschwelle für "ähnliche" Matches
  calculateCostImpact?: boolean; // Kostenauswirkung berechnen
  generateLearningPoints?: boolean; // Lernpunkte für KI-Training generieren
}

export class DrumComparisonService {
  private inventoryService: InventoryService;
  private cuttingOptimizer: CuttingOptimizerService;

  constructor(
    inventoryService: InventoryService,
    cuttingOptimizer: CuttingOptimizerService
  ) {
    this.inventoryService = inventoryService;
    this.cuttingOptimizer = cuttingOptimizer;
  }

  /**
   * Hauptmethode für den Vergleich zwischen KI-Vorschlägen und manueller Auswahl
   */
  async compareSelections(
    deliveryId: string,
    deliveryItems: DeliveryItem[],
    aiSuggestions: AvailableDrum[],
    manualSelections: ManualDrumSelection[],
    selectionDate: Date,
    options: ComparisonOptions = {}
  ): Promise<DrumComparisonResult> {
    try {
      // 1. Matches und Unterschiede identifizieren
      const matches = await this.findMatches(aiSuggestions, manualSelections, options);
      const differences = await this.findDifferences(aiSuggestions, manualSelections, matches);

      // 2. Effizienz- und Verschnittanalyse
      const aiPlan = await this.calculatePlanMetrics(deliveryItems, aiSuggestions);
      const manualPlan = await this.calculatePlanMetrics(deliveryItems, manualSelections);

      // 3. Qualitätsbewertung berechnen
      const qualityScore = this.calculateQualityScore(matches, differences, aiPlan, manualPlan);

      // 4. Vergleichsergebnis zusammenstellen
      const result: DrumComparisonResult = {
        deliveryId,
        comparisonDate: new Date(),
        aiSuggestions,
        manualSelections,
        matches,
        differences,
        qualityScore,
        efficiency: {
          aiEfficiency: aiPlan.efficiency,
          manualEfficiency: manualPlan.efficiency,
          difference: manualPlan.efficiency - aiPlan.efficiency
        },
        waste: {
          aiWaste: aiPlan.totalWaste,
          manualWaste: manualPlan.totalWaste,
          difference: manualPlan.totalWaste - aiPlan.totalWaste
        }
      };

      return result;
    } catch (error) {
      console.error('Error comparing drum selections:', error);
      throw error;
    }
  }

  /**
   * Finde Übereinstimmungen zwischen KI-Vorschlägen und manueller Auswahl
   */
  private async findMatches(
    aiSuggestions: AvailableDrum[],
    manualSelections: ManualDrumSelection[],
    options: ComparisonOptions
  ): Promise<DrumMatch[]> {
    const matches: DrumMatch[] = [];
    const toleranceThreshold = options.toleranceThreshold || 0.8;

    for (const aiDrum of aiSuggestions) {
      for (const manualDrum of manualSelections) {
        const matchResult = this.evaluateMatch(aiDrum, manualDrum, toleranceThreshold);
        
        if (matchResult.confidence > 0) {
          matches.push({
            aiDrumId: aiDrum.id,
            manualChargeNumber: manualDrum.chargeNumber,
            matchType: matchResult.type,
            confidence: matchResult.confidence
          });
        }
      }
    }

    // Sortiere Matches nach Vertrauenswert (höchste zuerst)
    return matches.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Bewerte die Übereinstimmung zwischen einer KI-Trommel und einer manuellen Auswahl
   */
  private evaluateMatch(
    aiDrum: AvailableDrum,
    manualDrum: ManualDrumSelection,
    toleranceThreshold: number
  ): { type: DrumMatch['matchType']; confidence: number } {
    let confidence = 0;
    let matchType: DrumMatch['matchType'] = 'alternative';

    // Exakte Übereinstimmung der Charge-Nummer (falls verfügbar)
    if (aiDrum.id === manualDrum.chargeNumber) {
      return { type: 'exact', confidence: 1.0 };
    }

    // Material-Übereinstimmung
    const materialMatch = aiDrum.material === manualDrum.material;
    if (materialMatch) {
      confidence += 0.4;
    }

    // Größen-/Typ-Übereinstimmung (falls verfügbar)
    const sizeMatch = aiDrum.diameter && manualDrum.lagereinheitentyp;
    if (sizeMatch) {
      confidence += 0.3;
    }

    // Längen-Kompatibilität
    const lengthCompatible = this.isLengthCompatible(aiDrum, manualDrum);
    if (lengthCompatible) {
      confidence += 0.3;
    }

    // Bestimme Match-Typ basierend auf Vertrauenswert
    if (confidence >= toleranceThreshold) {
      matchType = 'similar';
    } else if (confidence >= 0.5) {
      matchType = 'alternative';
    } else {
      confidence = 0; // Kein ausreichender Match
    }

    return { type: matchType, confidence };
  }

  /**
   * Prüfe Längen-Kompatibilität zwischen Trommeln
   */
  private isLengthCompatible(
    aiDrum: AvailableDrum,
    manualDrum: ManualDrumSelection
  ): boolean {
    const aiLength = aiDrum.availableLength || aiDrum.remainingLength || 0;
    const manualLength = manualDrum.gesamtbestand;

    if (aiLength === 0 || manualLength === 0) {
      return false;
    }

    // Toleranz von ±10% für Längenkompatibilität
    const tolerance = 0.1;
    const difference = Math.abs(aiLength - manualLength) / Math.max(aiLength, manualLength);
    
    return difference <= tolerance;
  }

  /**
   * Finde Unterschiede zwischen KI-Vorschlägen und manueller Auswahl
   */
  private async findDifferences(
    aiSuggestions: AvailableDrum[],
    manualSelections: ManualDrumSelection[],
    matches: DrumMatch[]
  ): Promise<DrumDifference[]> {
    const differences: DrumDifference[] = [];
    const matchedAiDrums = new Set(matches.map(m => m.aiDrumId));
    const matchedManualDrums = new Set(matches.map(m => m.manualChargeNumber));

    // KI-Vorschläge ohne entsprechende manuelle Auswahl
    for (const aiDrum of aiSuggestions) {
      if (!matchedAiDrums.has(aiDrum.id)) {
        differences.push({
          type: 'missing_manual_selection',
          description: `KI schlug Trommel ${aiDrum.id} vor, wurde aber nicht manuell ausgewählt`,
          impact: this.assessImpact(aiDrum),
          aiDrum
        });
      }
    }

    // Manuelle Auswahlen ohne entsprechenden KI-Vorschlag
    for (const manualDrum of manualSelections) {
      if (!matchedManualDrums.has(manualDrum.chargeNumber)) {
        differences.push({
          type: 'missing_ai_suggestion',
          description: `Trommel ${manualDrum.chargeNumber} wurde manuell ausgewählt, aber nicht von der KI vorgeschlagen`,
          impact: this.assessManualImpact(manualDrum),
          manualDrum
        });
      }
    }

    return differences;
  }

  /**
   * Bewerte die Auswirkung einer fehlenden manuellen Auswahl
   */
  private assessImpact(aiDrum: AvailableDrum): DrumDifference['impact'] {
    const length = aiDrum.availableLength || aiDrum.remainingLength || 0;
    
    if (length > 1000) return 'high'; // Große Trommel
    if (length > 500) return 'medium'; // Mittlere Trommel
    return 'low'; // Kleine Trommel
  }

  /**
   * Bewerte die Auswirkung einer fehlenden KI-Vorhersage
   */
  private assessManualImpact(manualDrum: ManualDrumSelection): DrumDifference['impact'] {
    if (manualDrum.gesamtbestand > 1000) return 'high';
    if (manualDrum.gesamtbestand > 500) return 'medium';
    return 'low';
  }

  /**
   * Berechne Metriken für einen Schneidplan
   */
  private async calculatePlanMetrics(
    deliveryItems: DeliveryItem[],
    drums: (AvailableDrum | ManualDrumSelection)[]
  ): Promise<{ efficiency: number; totalWaste: number }> {
    try {
      // Konvertiere zu einheitlichem Format für Berechnung
      const availableDrums: AvailableDrum[] = drums.map(drum => {
        if ('chargeNumber' in drum) {
          // ManualDrumSelection zu AvailableDrum konvertieren
          return {
            id: drum.chargeNumber,
            material: drum.material,
            availableLength: drum.gesamtbestand,
            remainingLength: drum.gesamtbestand
          };
        }
        return drum as AvailableDrum;
      });

      // Vereinfachte Effizienz- und Verschnittberechnung
      const totalRequired = deliveryItems.reduce((sum, item) => 
        sum + (item.requiredLength * item.quantity), 0
      );
      
      const totalAvailable = availableDrums.reduce((sum, drum) => 
        sum + (drum.availableLength || drum.remainingLength || 0), 0
      );

      const efficiency = totalAvailable > 0 ? (totalRequired / totalAvailable) * 100 : 0;
      const totalWaste = Math.max(0, totalAvailable - totalRequired);

      return { efficiency, totalWaste };
    } catch (error) {
      console.error('Error calculating plan metrics:', error);
      return { efficiency: 0, totalWaste: 0 };
    }
  }

  /**
   * Berechne Qualitätsbewertung der KI-Auswahl
   */
  private calculateQualityScore(
    matches: DrumMatch[],
    differences: DrumDifference[],
    aiPlan: { efficiency: number; totalWaste: number },
    manualPlan: { efficiency: number; totalWaste: number }
  ): number {
    let score = 0;

    // Basis-Score basierend auf Matches (40% der Gesamtbewertung)
    const matchScore = matches.reduce((sum, match) => {
      switch (match.matchType) {
        case 'exact': return sum + (40 * match.confidence);
        case 'similar': return sum + (30 * match.confidence);
        case 'alternative': return sum + (20 * match.confidence);
        default: return sum;
      }
    }, 0);
    score += Math.min(40, matchScore);

    // Effizienz-Score (30% der Gesamtbewertung)
    const efficiencyDiff = aiPlan.efficiency - manualPlan.efficiency;
    const efficiencyScore = Math.max(0, 30 + efficiencyDiff); // Bonus für bessere KI-Effizienz
    score += Math.min(30, Math.max(0, efficiencyScore));

    // Verschnitt-Score (20% der Gesamtbewertung)
    const wasteDiff = manualPlan.totalWaste - aiPlan.totalWaste;
    const wasteScore = Math.max(0, 20 + (wasteDiff / 100)); // Bonus für weniger KI-Verschnitt
    score += Math.min(20, Math.max(0, wasteScore));

    // Penalty für große Unterschiede (10% der Gesamtbewertung)
    const highImpactDifferences = differences.filter(d => d.impact === 'high').length;
    const penaltyScore = Math.max(0, 10 - (highImpactDifferences * 2));
    score += penaltyScore;

    return Math.round(Math.min(100, Math.max(0, score)));
  }

  /**
   * Generiere detaillierte Metriken für weitere Analyse
   */
  async generateDetailedMetrics(
    comparisonResult: DrumComparisonResult,
    options: ComparisonOptions = {}
  ): Promise<ComparisonMetrics> {
    const accuracyScore = comparisonResult.qualityScore;
    const efficiencyDifference = comparisonResult.efficiency.difference;
    const wasteDifference = comparisonResult.waste.difference;

    // Geschätzte Kostenauswirkung (falls gewünscht)
    let costImpact = 0;
    if (options.calculateCostImpact) {
      costImpact = this.estimateCostImpact(efficiencyDifference, wasteDifference);
    }

    // Lernpunkte für KI-Verbesserung
    let learningPoints: string[] = [];
    if (options.generateLearningPoints) {
      learningPoints = this.generateLearningPoints(comparisonResult);
    }

    return {
      accuracyScore,
      efficiencyDifference,
      wasteDifference,
      costImpact,
      learningPoints
    };
  }

  /**
   * Schätze Kostenauswirkung der Unterschiede
   */
  private estimateCostImpact(
    efficiencyDifference: number,
    wasteDifference: number
  ): number {
    // Vereinfachte Kostenschätzung (€ pro Meter Verschnitt)
    const wasteCoastPerMeter = 2.5; // Beispielwert
    const efficiencyCostFactor = 0.1; // Beispielwert

    return (wasteDifference * wasteCoastPerMeter) + 
           (efficiencyDifference * efficiencyCostFactor);
  }

  /**
   * Generiere Lernpunkte für KI-Verbesserung
   */
  private generateLearningPoints(result: DrumComparisonResult): string[] {
    const points: string[] = [];

    // Analyse der Unterschiede
    const missingAiSuggestions = result.differences.filter((d: DrumDifference) => 
      d.type === 'missing_ai_suggestion'
    );
    
    if (missingAiSuggestions.length > 0) {
      points.push(`KI übersah ${missingAiSuggestions.length} manuell ausgewählte Trommeln`);
    }

    // Effizienz-Analyse
    if (result.efficiency.difference > 5) {
      points.push('Manuelle Auswahl war effizienter - KI-Algorithmus überprüfen');
    } else if (result.efficiency.difference < -5) {
      points.push('KI-Auswahl war effizienter - Schulung der Mitarbeiter empfohlen');
    }

    // Verschnitt-Analyse
    if (result.waste.difference > 50) {
      points.push('Manuelle Auswahl führte zu mehr Verschnitt');
    } else if (result.waste.difference < -50) {
      points.push('KI-Auswahl führte zu mehr Verschnitt - Optimierung erforderlich');
    }

    return points;
  }
}

// Factory-Funktion für Service-Erstellung
export function createDrumComparisonService(
  inventoryService: InventoryService,
  cuttingOptimizer: CuttingOptimizerService
): DrumComparisonService {
  return new DrumComparisonService(inventoryService, cuttingOptimizer);
}