/**
 * Statistical Forecasting Models
 * 
 * Implements basic statistical forecasting models for inventory demand prediction
 */

import { ConsumptionData, TimeSeriesData, ModelAccuracy } from '../types';

export class StatisticalForecaster {
  /**
   * Simple Moving Average forecasting
   */
  static simpleMovingAverage(
    data: number[],
    window: number,
    horizon: number = 1
  ): { predictions: number[]; accuracy: ModelAccuracy } {
    if (data.length < window) {
      throw new Error(`Insufficient data for moving average (need at least ${window} points)`);
    }

    const predictions: number[] = [];
    const errors: number[] = [];

    // Calculate predictions for existing data (for accuracy measurement)
    for (let i = window; i < data.length; i++) {
      const windowData = data.slice(i - window, i);
      const prediction = windowData.reduce((sum, val) => sum + val, 0) / window;
      const actual = data[i];
      
      errors.push(Math.abs(actual - prediction));
    }

    // Generate future predictions
    for (let i = 0; i < horizon; i++) {
      const startIndex = Math.max(0, data.length - window + i);
      const endIndex = data.length + i;
      const windowData = [...data.slice(startIndex), ...predictions].slice(-window);
      const prediction = windowData.reduce((sum, val) => sum + val, 0) / window;
      predictions.push(Math.max(0, prediction));
    }

    const accuracy = this.calculateAccuracy(
      data.slice(window),
      data.slice(window - 1, -1).map((_, i) => {
        const windowData = data.slice(i, i + window);
        return windowData.reduce((sum, val) => sum + val, 0) / window;
      })
    );

    return { predictions, accuracy };
  }

  /**
   * Exponential Smoothing forecasting
   */
  static exponentialSmoothing(
    data: number[],
    alpha: number = 0.3,
    horizon: number = 1
  ): { predictions: number[]; accuracy: ModelAccuracy } {
    if (data.length < 2) {
      throw new Error('Insufficient data for exponential smoothing (need at least 2 points)');
    }

    const smoothed: number[] = [data[0]];
    const errors: number[] = [];

    // Calculate smoothed values and errors
    for (let i = 1; i < data.length; i++) {
      const prediction = smoothed[i - 1];
      const actual = data[i];
      const smoothedValue = alpha * actual + (1 - alpha) * prediction;
      
      smoothed.push(smoothedValue);
      errors.push(Math.abs(actual - prediction));
    }

    // Generate future predictions
    const predictions: number[] = [];
    let lastSmoothed = smoothed[smoothed.length - 1];

    for (let i = 0; i < horizon; i++) {
      predictions.push(Math.max(0, lastSmoothed));
      // For simple exponential smoothing, future predictions remain constant
    }

    const accuracy = this.calculateAccuracy(
      data.slice(1),
      smoothed.slice(0, -1)
    );

    return { predictions, accuracy };
  }

  /**
   * Double Exponential Smoothing (Holt's method) for trending data
   */
  static doubleExponentialSmoothing(
    data: number[],
    alpha: number = 0.3,
    beta: number = 0.1,
    horizon: number = 1
  ): { predictions: number[]; accuracy: ModelAccuracy } {
    if (data.length < 3) {
      throw new Error('Insufficient data for double exponential smoothing (need at least 3 points)');
    }

    // Initialize level and trend
    let level = data[0];
    let trend = data[1] - data[0];
    
    const predictions: number[] = [];
    const errors: number[] = [];

    // Process historical data
    for (let i = 1; i < data.length; i++) {
      const prediction = level + trend;
      const actual = data[i];
      
      errors.push(Math.abs(actual - prediction));
      
      // Update level and trend
      const prevLevel = level;
      level = alpha * actual + (1 - alpha) * (level + trend);
      trend = beta * (level - prevLevel) + (1 - beta) * trend;
    }

    // Generate future predictions
    for (let i = 1; i <= horizon; i++) {
      const prediction = level + trend * i;
      predictions.push(Math.max(0, prediction));
    }

    const accuracy = this.calculateAccuracy(
      data.slice(1),
      data.slice(0, -1).map((_, i) => {
        // Recalculate predictions for accuracy (simplified)
        return Math.max(0, data[i] + (i > 0 ? data[i] - data[i - 1] : 0));
      })
    );

    return { predictions, accuracy };
  }

  /**
   * Linear Regression forecasting
   */
  static linearRegression(
    data: number[],
    horizon: number = 1
  ): { predictions: number[]; accuracy: ModelAccuracy; slope: number; intercept: number } {
    if (data.length < 2) {
      throw new Error('Insufficient data for linear regression (need at least 2 points)');
    }

    const n = data.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = data;

    // Calculate means
    const xMean = x.reduce((sum, val) => sum + val, 0) / n;
    const yMean = y.reduce((sum, val) => sum + val, 0) / n;

    // Calculate slope and intercept
    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < n; i++) {
      numerator += (x[i] - xMean) * (y[i] - yMean);
      denominator += Math.pow(x[i] - xMean, 2);
    }

    const slope = denominator !== 0 ? numerator / denominator : 0;
    const intercept = yMean - slope * xMean;

    // Generate predictions
    const predictions: number[] = [];
    for (let i = 1; i <= horizon; i++) {
      const prediction = intercept + slope * (n + i - 1);
      predictions.push(Math.max(0, prediction));
    }

    // Calculate fitted values for accuracy
    const fittedValues = x.map(xi => intercept + slope * xi);
    const accuracy = this.calculateAccuracy(y, fittedValues);

    return { predictions, accuracy, slope, intercept };
  }

  /**
   * Seasonal Naive forecasting (repeat last season)
   */
  static seasonalNaive(
    data: number[],
    seasonLength: number,
    horizon: number = 1
  ): { predictions: number[]; accuracy: ModelAccuracy } {
    if (data.length < seasonLength) {
      throw new Error(`Insufficient data for seasonal naive (need at least ${seasonLength} points)`);
    }

    const predictions: number[] = [];
    const errors: number[] = [];

    // Calculate errors for existing data
    for (let i = seasonLength; i < data.length; i++) {
      const seasonalPrediction = data[i - seasonLength];
      const actual = data[i];
      errors.push(Math.abs(actual - seasonalPrediction));
    }

    // Generate future predictions
    for (let i = 0; i < horizon; i++) {
      const seasonalIndex = (data.length + i) % seasonLength;
      const lookbackIndex = data.length - seasonLength + seasonalIndex;
      const prediction = data[lookbackIndex] || 0;
      predictions.push(Math.max(0, prediction));
    }

    // Calculate accuracy using seasonal predictions
    const actualValues = data.slice(seasonLength);
    const predictedValues = actualValues.map((_, i) => {
      const seasonalIndex = (seasonLength + i) % seasonLength;
      return data[seasonLength - seasonLength + seasonalIndex] || 0;
    });
    
    const accuracy = this.calculateAccuracy(actualValues, predictedValues);

    return { predictions, accuracy };
  }

  /**
   * Weighted Moving Average forecasting
   */
  static weightedMovingAverage(
    data: number[],
    weights: number[],
    horizon: number = 1
  ): { predictions: number[]; accuracy: ModelAccuracy } {
    const window = weights.length;
    
    if (data.length < window) {
      throw new Error(`Insufficient data for weighted moving average (need at least ${window} points)`);
    }

    // Normalize weights
    const weightSum = weights.reduce((sum, w) => sum + w, 0);
    const normalizedWeights = weights.map(w => w / weightSum);

    const predictions: number[] = [];
    const errors: number[] = [];

    // Calculate predictions for existing data
    for (let i = window; i < data.length; i++) {
      const windowData = data.slice(i - window, i);
      const prediction = windowData.reduce((sum, val, idx) => sum + val * normalizedWeights[idx], 0);
      const actual = data[i];
      
      errors.push(Math.abs(actual - prediction));
    }

    // Generate future predictions
    for (let i = 0; i < horizon; i++) {
      const startIndex = Math.max(0, data.length - window + i);
      const windowData = [...data.slice(startIndex), ...predictions].slice(-window);
      const prediction = windowData.reduce((sum, val, idx) => sum + val * normalizedWeights[idx], 0);
      predictions.push(Math.max(0, prediction));
    }

    const accuracy = this.calculateAccuracy(
      data.slice(window),
      data.slice(0, data.length - window).map((_, i) => {
        const windowData = data.slice(i, i + window);
        return windowData.reduce((sum, val, idx) => sum + val * normalizedWeights[idx], 0);
      })
    );

    return { predictions, accuracy };
  }

  /**
   * Calculate accuracy metrics
   */
  private static calculateAccuracy(actual: number[], predicted: number[]): ModelAccuracy {
    if (actual.length !== predicted.length || actual.length === 0) {
      return { mae: Infinity, mse: Infinity, rmse: Infinity, mape: Infinity };
    }

    const n = actual.length;
    let mae = 0;
    let mse = 0;
    let mape = 0;
    let ssRes = 0;
    let ssTot = 0;

    const actualMean = actual.reduce((sum, val) => sum + val, 0) / n;

    for (let i = 0; i < n; i++) {
      const error = actual[i] - predicted[i];
      const absError = Math.abs(error);
      
      mae += absError;
      mse += error * error;
      
      if (actual[i] !== 0) {
        mape += Math.abs(error / actual[i]);
      }
      
      ssRes += error * error;
      ssTot += Math.pow(actual[i] - actualMean, 2);
    }

    mae /= n;
    mse /= n;
    mape = (mape / n) * 100;
    const rmse = Math.sqrt(mse);
    const r2 = ssTot !== 0 ? 1 - (ssRes / ssTot) : 0;

    return { mae, mse, rmse, mape, r2 };
  }

  /**
   * Cross-validation for model selection
   */
  static crossValidate(
    data: number[],
    modelFunction: (trainData: number[]) => number[],
    folds: number = 5
  ): ModelAccuracy {
    if (data.length < folds) {
      throw new Error('Insufficient data for cross-validation');
    }

    const foldSize = Math.floor(data.length / folds);
    const accuracies: ModelAccuracy[] = [];

    for (let i = 0; i < folds; i++) {
      const testStart = i * foldSize;
      const testEnd = i === folds - 1 ? data.length : (i + 1) * foldSize;
      
      const trainData = [...data.slice(0, testStart), ...data.slice(testEnd)];
      const testData = data.slice(testStart, testEnd);
      
      try {
        const predictions = modelFunction(trainData);
        const accuracy = this.calculateAccuracy(testData, predictions.slice(0, testData.length));
        accuracies.push(accuracy);
      } catch (error) {
        // Skip this fold if model fails
        continue;
      }
    }

    if (accuracies.length === 0) {
      return { mae: Infinity, mse: Infinity, rmse: Infinity, mape: Infinity };
    }

    // Average the accuracies
    return {
      mae: accuracies.reduce((sum, acc) => sum + acc.mae, 0) / accuracies.length,
      mse: accuracies.reduce((sum, acc) => sum + acc.mse, 0) / accuracies.length,
      rmse: accuracies.reduce((sum, acc) => sum + acc.rmse, 0) / accuracies.length,
      mape: accuracies.reduce((sum, acc) => sum + acc.mape, 0) / accuracies.length,
      r2: accuracies.reduce((sum, acc) => sum + (acc.r2 || 0), 0) / accuracies.length
    };
  }

  /**
   * Automatic model selection based on cross-validation
   */
  static selectBestModel(
    data: number[],
    horizon: number = 1
  ): { modelName: string; predictions: number[]; accuracy: ModelAccuracy } {
    const models = [
      {
        name: 'Simple Moving Average (7)',
        fn: () => this.simpleMovingAverage(data, Math.min(7, Math.floor(data.length / 2)), horizon)
      },
      {
        name: 'Simple Moving Average (14)',
        fn: () => this.simpleMovingAverage(data, Math.min(14, Math.floor(data.length / 2)), horizon)
      },
      {
        name: 'Exponential Smoothing',
        fn: () => this.exponentialSmoothing(data, 0.3, horizon)
      },
      {
        name: 'Double Exponential Smoothing',
        fn: () => this.doubleExponentialSmoothing(data, 0.3, 0.1, horizon)
      },
      {
        name: 'Linear Regression',
        fn: () => this.linearRegression(data, horizon)
      }
    ];

    let bestModel = models[0];
    let bestAccuracy = Infinity;

    for (const model of models) {
      try {
        const result = model.fn();
        const mape = result.accuracy.mape;
        
        if (mape < bestAccuracy && !isNaN(mape) && isFinite(mape)) {
          bestAccuracy = mape;
          bestModel = model;
        }
      } catch (error) {
        // Skip models that fail
        continue;
      }
    }

    const result = bestModel.fn();
    return {
      modelName: bestModel.name,
      predictions: result.predictions,
      accuracy: result.accuracy
    };
  }
}