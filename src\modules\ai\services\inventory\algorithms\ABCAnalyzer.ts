/**
 * ABC Analysis Algorithm
 * 
 * Implements ABC analysis for inventory classification based on value, quantity, or frequency
 */

import { 
  InventoryItem, 
  ConsumptionData, 
  ABCClassification, 
  ABCCriteria, 
  ABCAnalysisResult,
  ClassificationChange,
  StatisticalSummary
} from '../types';

export class ABCAnalyzer {
  /**
   * Perform ABC analysis on inventory items
   */
  static performAnalysis(
    items: InventoryItem[],
    consumptionData: ConsumptionData[],
    criteria: ABCCriteria
  ): ABCClassification {
    // Calculate analysis values for each item
    const analysisResults = this.calculateAnalysisValues(items, consumptionData, criteria);
    
    // Sort by value descending
    analysisResults.sort((a, b) => b.value - a.value);
    
    // Calculate cumulative percentages
    const totalValue = analysisResults.reduce((sum, item) => sum + item.value, 0);
    let cumulativeValue = 0;
    
    analysisResults.forEach((result, index) => {
      cumulativeValue += result.value;
      result.percentage = (result.value / totalValue) * 100;
      result.cumulativePercentage = (cumulativeValue / totalValue) * 100;
      result.rank = index + 1;
    });
    
    // Classify items
    const classifiedItems = this.classifyItems(analysisResults, criteria);
    
    return {
      classA: classifiedItems.classA,
      classB: classifiedItems.classB,
      classC: classifiedItems.classC,
      reclassifications: [], // Will be populated when comparing with previous analysis
      analysisDate: new Date(),
      criteria
    };
  }

  /**
   * Calculate analysis values based on criteria
   */
  private static calculateAnalysisValues(
    items: InventoryItem[],
    consumptionData: ConsumptionData[],
    criteria: ABCCriteria
  ): ABCAnalysisResult[] {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - criteria.timeWindow);
    
    // Filter consumption data to time window
    const relevantConsumption = consumptionData.filter(
      data => data.date >= cutoffDate
    );
    
    // Group consumption by item
    const consumptionByItem = new Map<string, ConsumptionData[]>();
    relevantConsumption.forEach(data => {
      if (!consumptionByItem.has(data.itemId)) {
        consumptionByItem.set(data.itemId, []);
      }
      consumptionByItem.get(data.itemId)!.push(data);
    });
    
    return items.map(item => {
      const itemConsumption = consumptionByItem.get(item.id) || [];
      let value = 0;
      
      switch (criteria.method) {
        case 'value':
          value = itemConsumption.reduce((sum, data) => sum + data.value, 0);
          break;
        case 'quantity':
          value = itemConsumption.reduce((sum, data) => sum + data.quantity, 0);
          break;
        case 'frequency':
          value = itemConsumption.length;
          break;
        case 'combined':
          const totalValue = itemConsumption.reduce((sum, data) => sum + data.value, 0);
          const totalQuantity = itemConsumption.reduce((sum, data) => sum + data.quantity, 0);
          const frequency = itemConsumption.length;
          // Weighted combination: 50% value, 30% quantity, 20% frequency
          value = (totalValue * 0.5) + (totalQuantity * item.unitPrice * 0.3) + (frequency * item.unitPrice * 0.2);
          break;
      }
      
      return {
        itemId: item.id,
        classification: 'C' as 'A' | 'B' | 'C', // Will be updated in classification step
        value,
        percentage: 0, // Will be calculated
        cumulativePercentage: 0, // Will be calculated
        rank: 0, // Will be calculated
        confidence: this.calculateConfidence(itemConsumption, criteria.timeWindow)
      };
    });
  }

  /**
   * Classify items into A, B, C categories
   */
  private static classifyItems(
    analysisResults: ABCAnalysisResult[],
    criteria: ABCCriteria
  ): { classA: InventoryItem[]; classB: InventoryItem[]; classC: InventoryItem[] } {
    const classA: InventoryItem[] = [];
    const classB: InventoryItem[] = [];
    const classC: InventoryItem[] = [];
    
    // If no results, return empty classifications
    if (analysisResults.length === 0) {
      return { classA, classB, classC };
    }
    
    // Ensure at least one item in each class if we have enough items
    const totalItems = analysisResults.length;
    
    analysisResults.forEach((result, index) => {
      if (result.cumulativePercentage <= criteria.classAThreshold) {
        result.classification = 'A';
        classA.push(this.createMinimalInventoryItem(result.itemId));
      } else if (result.cumulativePercentage <= criteria.classBThreshold) {
        result.classification = 'B';
        classB.push(this.createMinimalInventoryItem(result.itemId));
      } else {
        result.classification = 'C';
        classC.push(this.createMinimalInventoryItem(result.itemId));
      }
    });
    
    // Ensure we have at least one item in class C if we have any items
    if (totalItems > 0 && classA.length === 0 && classB.length === 0 && classC.length === 0) {
      // This shouldn't happen, but as a fallback, put all items in class C
      analysisResults.forEach(result => {
        result.classification = 'C';
        classC.push(this.createMinimalInventoryItem(result.itemId));
      });
    }
    
    return { classA, classB, classC };
  }

  /**
   * Calculate confidence score for classification
   */
  private static calculateConfidence(consumption: ConsumptionData[], timeWindow: number): number {
    if (consumption.length === 0) return 0;
    
    // Base confidence on data availability and consistency
    const dataAvailability = Math.min(consumption.length / (timeWindow / 7), 1); // Assume weekly data points
    
    // Calculate coefficient of variation for consistency
    const values = consumption.map(d => d.value);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const cv = Math.sqrt(variance) / mean;
    
    // Lower CV means more consistent, higher confidence
    const consistencyScore = Math.max(0, 1 - cv);
    
    return (dataAvailability * 0.6) + (consistencyScore * 0.4);
  }

  /**
   * Compare with previous analysis to identify reclassifications
   */
  static identifyReclassifications(
    currentAnalysis: ABCClassification,
    previousAnalysis: ABCClassification
  ): ClassificationChange[] {
    const changes: ClassificationChange[] = [];
    
    // Create maps for quick lookup
    const currentClassMap = new Map<string, 'A' | 'B' | 'C'>();
    const previousClassMap = new Map<string, 'A' | 'B' | 'C'>();
    
    // Populate current classifications
    currentAnalysis.classA.forEach(item => currentClassMap.set(item.id, 'A'));
    currentAnalysis.classB.forEach(item => currentClassMap.set(item.id, 'B'));
    currentAnalysis.classC.forEach(item => currentClassMap.set(item.id, 'C'));
    
    // Populate previous classifications
    previousAnalysis.classA.forEach(item => previousClassMap.set(item.id, 'A'));
    previousAnalysis.classB.forEach(item => previousClassMap.set(item.id, 'B'));
    previousAnalysis.classC.forEach(item => previousClassMap.set(item.id, 'C'));
    
    // Find changes
    currentClassMap.forEach((currentClass, itemId) => {
      const previousClass = previousClassMap.get(itemId);
      if (previousClass && previousClass !== currentClass) {
        changes.push({
          itemId,
          previousClass,
          newClass: currentClass,
          reason: this.getReclassificationReason(previousClass, currentClass),
          confidence: 0.8 // Default confidence, could be calculated based on data quality
        });
      }
    });
    
    return changes;
  }

  /**
   * Get reason for reclassification
   */
  private static getReclassificationReason(
    previousClass: 'A' | 'B' | 'C',
    newClass: 'A' | 'B' | 'C'
  ): string {
    if (previousClass === 'C' && newClass === 'A') {
      return 'Significant increase in consumption value/frequency';
    } else if (previousClass === 'A' && newClass === 'C') {
      return 'Significant decrease in consumption value/frequency';
    } else if (previousClass === 'A' && newClass === 'B') {
      return 'Moderate decrease in consumption value/frequency';
    } else if (previousClass === 'B' && newClass === 'A') {
      return 'Moderate increase in consumption value/frequency';
    } else if (previousClass === 'B' && newClass === 'C') {
      return 'Moderate decrease in consumption value/frequency';
    } else if (previousClass === 'C' && newClass === 'B') {
      return 'Moderate increase in consumption value/frequency';
    }
    return 'Classification change based on updated consumption patterns';
  }

  /**
   * Calculate statistical summary for analysis values
   */
  static calculateStatistics(analysisResults: ABCAnalysisResult[]): StatisticalSummary {
    const values = analysisResults.map(r => r.value).sort((a, b) => a - b);
    const count = values.length;
    
    if (count === 0) {
      return {
        mean: 0, median: 0, standardDeviation: 0, variance: 0,
        min: 0, max: 0, count: 0,
        percentiles: { p25: 0, p75: 0, p90: 0, p95: 0 }
      };
    }
    
    const mean = values.reduce((sum, val) => sum + val, 0) / count;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / count;
    const standardDeviation = Math.sqrt(variance);
    
    return {
      mean,
      median: this.getPercentile(values, 50),
      standardDeviation,
      variance,
      min: values[0],
      max: values[count - 1],
      count,
      percentiles: {
        p25: this.getPercentile(values, 25),
        p75: this.getPercentile(values, 75),
        p90: this.getPercentile(values, 90),
        p95: this.getPercentile(values, 95)
      }
    };
  }

  /**
   * Calculate percentile from sorted array
   */
  private static getPercentile(sortedValues: number[], percentile: number): number {
    const index = (percentile / 100) * (sortedValues.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    
    if (lower === upper) {
      return sortedValues[lower];
    }
    
    const weight = index - lower;
    return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
  }

  /**
   * Create minimal inventory item for classification results
   * In a real implementation, this would fetch the full item from the repository
   */
  private static createMinimalInventoryItem(itemId: string): InventoryItem {
    return {
      id: itemId,
      name: `Item ${itemId}`,
      category: 'Unknown',
      currentStock: 0,
      unitPrice: 0,
      lastUpdated: new Date()
    };
  }
}