# Design Document

## Overview

This design extends the existing AI chatbot system by adding intelligent database query capabilities. The solution integrates with the current OpenRouter service and repository layer to provide real-time data insights. The architecture maintains the existing chat flow while adding a data enrichment layer that analyzes user intent and augments AI responses with relevant database information.

## Architecture

### High-Level Flow

```mermaid
sequenceDiagram
    participant User
    participant Cha<PERSON><PERSON><PERSON> as ChatBot UI
    participant ChatRoutes as Chat Routes
    participant OpenRouterService as OpenRouter Service
    participant DataService as Data Enrichment Service
    participant Repositories as Data Repositories
    participant OpenRouter as OpenRouter API

    User->>ChatBot: Ask question about data
    ChatBot->>ChatRoutes: POST /api/chat/enhanced
    ChatRoutes->>DataService: Analyze intent & enrich context
    DataService->>DataService: Parse intent keywords
    DataService->>Repositories: Query relevant data
    Repositories-->>DataService: Return formatted data
    DataService-->>ChatRoutes: Return enriched context
    ChatRoutes->>OpenRouterService: Generate response with context
    OpenRouterService->>OpenRouter: Send enriched prompt
    OpenRouter-->>OpenRouterService: AI response
    OpenRouterService-->>ChatRoutes: Formatted response
    ChatRoutes-->>ChatBot: JSON response
    ChatBot-->>User: Display answer
```

### Component Integration

The design leverages existing components:
- **Frontend**: No changes to ChatBot.tsx - uses existing enhanced mode
- **Backend**: Extends existing chat routes and OpenRouter service
- **Data Layer**: Utilizes existing repositories (StoerungenRepository, DispatchRepository, CuttingRepository)

## Components and Interfaces

### 1. Data Enrichment Service

**Purpose**: Analyzes user queries and enriches them with relevant database context.

**Interface**:
```typescript
interface DataEnrichmentService {
  enrichChatContext(message: string): Promise<EnrichedContext>;
  parseIntent(message: string): QueryIntent;
  formatDataForLLM(data: any[], dataType: string): string;
}

interface EnrichedContext {
  originalMessage: string;
  detectedIntents: QueryIntent[];
  databaseContext: string;
  hasData: boolean;
  dataTypes: string[];
}

interface QueryIntent {
  type: 'stoerungen' | 'dispatch' | 'cutting' | 'general';
  keywords: string[];
  timeRange?: DateRange;
  specificMetrics?: string[];
}
```

### 2. Repository Query Coordinator

**Purpose**: Coordinates queries across multiple repositories based on detected intent.

**Interface**:
```typescript
interface RepositoryQueryCoordinator {
  executeQueries(intents: QueryIntent[]): Promise<QueryResult[]>;
  getStoerungenData(intent: QueryIntent): Promise<any>;
  getDispatchData(intent: QueryIntent): Promise<any>;
  getCuttingData(intent: QueryIntent): Promise<any>;
}

interface QueryResult {
  dataType: string;
  data: any;
  summary: string;
  timestamp: Date;
}
```

### 3. Enhanced OpenRouter Service

**Purpose**: Extends existing service to handle enriched context.

**Modifications**:
```typescript
interface ChatRequest {
  message: string;
  includeInsights?: boolean;
  includeAnomalies?: boolean;
  enrichedContext?: EnrichedContext; // New field
}
```

## Data Models

### Intent Recognition Patterns

```typescript
const INTENT_PATTERNS = {
  stoerungen: [
    'störungen', 'incidents', 'problems', 'issues', 'fehler', 'system status',
    'mttr', 'resolution', 'severity', 'critical', 'resolved'
  ],
  dispatch: [
    'versand', 'dispatch', 'service level', 'tonnage', 'delivery', 'picking',
    'atrl', 'aril', 'returns', 'qm', 'performance', 'logistics'
  ],
  cutting: [
    'ablängerei', 'cutting', 'schnitt', 'machine', 'efficiency', 'cuts',
    'lager', 'warehouse', 'production'
  ],
  timeRange: [
    'heute', 'today', 'gestern', 'yesterday', 'woche', 'week', 'monat', 'month',
    'letzte', 'last', 'diese', 'this', 'aktuell', 'current'
  ]
};
```

### Data Formatting Templates

```typescript
interface DataFormatter {
  formatStoerungenData(data: StoerungsStats): string;
  formatDispatchData(data: PerformanceMetrics): string;
  formatCuttingData(data: CuttingPerformanceOverview): string;
  formatTimeSeriesData(data: any[], type: string): string;
}
```

## Error Handling

### Intent Recognition Fallbacks
- **Unknown Intent**: Default to querying all repositories with basic filters
- **No Data Found**: Provide helpful suggestions for alternative queries
- **Repository Errors**: Graceful degradation with partial data or error messages
- **Timeout Handling**: Set reasonable timeouts for database queries (5 seconds max)

### Data Quality Checks
- **Empty Results**: Inform user when no data matches their query
- **Stale Data**: Include timestamps and data freshness indicators
- **Partial Failures**: Continue with available data if some repositories fail

## Testing Strategy

### Unit Tests
- **Intent Recognition**: Test keyword detection and time range parsing
- **Data Formatting**: Verify proper formatting of different data types
- **Repository Integration**: Mock repository calls and test data retrieval
- **Error Scenarios**: Test handling of various failure conditions

### Integration Tests
- **End-to-End Flow**: Test complete chat flow with database integration
- **Repository Coordination**: Test multi-repository queries
- **OpenRouter Integration**: Test enriched context handling
- **Performance**: Measure response times with database queries

### Test Data Scenarios
- **Störungen Queries**: "How many incidents this week?", "Show critical issues"
- **Dispatch Queries**: "What's our service level today?", "Show picking performance"
- **Cutting Queries**: "Which machines are most efficient?", "Show cutting statistics"
- **Mixed Queries**: "Overall performance summary", "System health status"
- **Time-based Queries**: "Last month's statistics", "Today's performance"

### Performance Considerations
- **Caching Strategy**: Leverage existing repository caching (5-15 minute TTL)
- **Query Optimization**: Use existing optimized repository methods
- **Concurrent Queries**: Execute multiple repository queries in parallel
- **Response Time Target**: Complete database enrichment within 2 seconds
- **Fallback Strategy**: Provide basic AI response if data enrichment fails

### Security Considerations
- **Data Access**: Use existing repository security patterns
- **Query Validation**: Sanitize and validate all user inputs
- **Rate Limiting**: Leverage existing rate limiting for chat endpoints
- **Data Exposure**: Only expose aggregated statistics, not sensitive raw data