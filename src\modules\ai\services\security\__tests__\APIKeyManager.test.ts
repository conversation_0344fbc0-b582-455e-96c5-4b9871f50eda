/**
 * API Key Manager Tests
 * 
 * Tests for secure API key storage, validation, and management.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { APIKeyManager } from '../APIKeyManager';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock crypto.subtle for testing
Object.defineProperty(global, 'crypto', {
  value: {
    subtle: {
      digest: vi.fn().mockResolvedValue(new ArrayBuffer(32))
    }
  }
});

describe('APIKeyManager', () => {
  let apiKeyManager: APIKeyManager;

  beforeEach(() => {
    apiKeyManager = new APIKeyManager();
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('validateAPIKey', () => {
    it('should validate correct OpenRouter API key format', () => {
      const validKey = 'sk-or-v1-' + 'a'.repeat(64);
      const result = apiKeyManager.validateAPIKey(validKey, 'openrouter');

      expect(result.isValid).toBe(true);
      expect(result.service).toBe('OpenRouter API');
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid OpenRouter API key format', () => {
      const invalidKey = 'invalid-key-format';
      const result = apiKeyManager.validateAPIKey(invalidKey, 'openrouter');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('API-Schlüssel entspricht nicht dem erwarteten Format für OpenRouter API');
    });

    it('should reject empty API key', () => {
      const result = apiKeyManager.validateAPIKey('', 'openrouter');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('API-Schlüssel darf nicht leer sein');
    });

    it('should reject unknown service', () => {
      const result = apiKeyManager.validateAPIKey('some-key', 'unknown-service');

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Unbekannter Service: unknown-service');
    });
  });

  describe('storeAPIKey', () => {
    it('should store valid API key successfully', async () => {
      const validKey = 'sk-or-v1-' + 'a'.repeat(64);
      
      // Mock successful hash generation
      const mockHashArray = new Uint8Array(32).fill(1);
      vi.mocked(crypto.subtle.digest).mockResolvedValue(mockHashArray.buffer);

      const result = await apiKeyManager.storeAPIKey('openrouter', validKey);

      expect(result.success).toBe(true);
      expect(result.error).toBeUndefined();
      expect(localStorageMock.setItem).toHaveBeenCalled();
    });

    it('should reject invalid API key format', async () => {
      const invalidKey = 'invalid-key';
      
      const result = await apiKeyManager.storeAPIKey('openrouter', invalidKey);

      expect(result.success).toBe(false);
      expect(result.error).toContain('entspricht nicht dem erwarteten Format');
      expect(localStorageMock.setItem).not.toHaveBeenCalled();
    });

    it('should deactivate old keys when storing new one', async () => {
      const validKey = 'sk-or-v1-' + 'a'.repeat(64);
      
      // Mock existing stored keys
      const existingKeys = [
        {
          id: 'openrouter_123',
          service: 'openrouter',
          keyHash: 'old-hash',
          isActive: true,
          createdAt: new Date(),
          environment: 'both' as const
        }
      ];
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(existingKeys));
      
      // Mock successful hash generation
      const mockHashArray = new Uint8Array(32).fill(1);
      vi.mocked(crypto.subtle.digest).mockResolvedValue(mockHashArray.buffer);

      await apiKeyManager.storeAPIKey('openrouter', validKey);

      // Verify that setItem was called with updated keys
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'ai_api_keys',
        expect.stringContaining('"isActive":false') // Old key should be deactivated
      );
    });
  });

  describe('listAPIKeys', () => {
    it('should return list of stored keys without revealing hashes', () => {
      const storedKeys = [
        {
          id: 'openrouter_123',
          service: 'openrouter',
          keyHash: 'secret-hash',
          isActive: true,
          createdAt: new Date().toISOString(),
          environment: 'both'
        }
      ];
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(storedKeys));

      const keys = apiKeyManager.listAPIKeys();

      expect(keys).toHaveLength(1);
      expect(keys[0]).not.toHaveProperty('keyHash');
      expect(keys[0].service).toBe('openrouter');
      expect(keys[0].isActive).toBe(true);
    });

    it('should return empty array when no keys stored', () => {
      localStorageMock.getItem.mockReturnValue(null);

      const keys = apiKeyManager.listAPIKeys();

      expect(keys).toHaveLength(0);
    });

    it('should handle corrupted storage gracefully', () => {
      localStorageMock.getItem.mockReturnValue('invalid-json');
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const keys = apiKeyManager.listAPIKeys();

      expect(keys).toHaveLength(0);
      expect(consoleSpy).toHaveBeenCalledWith('Error reading stored keys:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('removeAPIKey', () => {
    it('should remove API key successfully', async () => {
      const storedKeys = [
        {
          id: 'openrouter_123',
          service: 'openrouter',
          keyHash: 'hash1',
          isActive: true,
          createdAt: new Date().toISOString(),
          environment: 'both'
        },
        {
          id: 'openrouter_456',
          service: 'openrouter',
          keyHash: 'hash2',
          isActive: false,
          createdAt: new Date().toISOString(),
          environment: 'both'
        }
      ];
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(storedKeys));

      const result = await apiKeyManager.removeAPIKey('openrouter_123');

      expect(result).toBe(true);
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'ai_api_keys',
        expect.not.stringContaining('openrouter_123')
      );
    });

    it('should handle removal of non-existent key', async () => {
      localStorageMock.getItem.mockReturnValue('[]');

      const result = await apiKeyManager.removeAPIKey('non-existent');

      expect(result).toBe(true); // Should not fail
    });
  });

  describe('checkRequiredKeys', () => {
    it('should report missing required keys', () => {
      localStorageMock.getItem.mockReturnValue('[]');
      
      // Mock environment variable not being set
      const originalEnv = process.env.OPENROUTER_API_KEY;
      delete process.env.OPENROUTER_API_KEY;

      const result = apiKeyManager.checkRequiredKeys();

      expect(result.allConfigured).toBe(false);
      expect(result.missing).toContain('OpenRouter API');

      // Restore environment
      if (originalEnv) {
        process.env.OPENROUTER_API_KEY = originalEnv;
      }
    });

    it('should report all configured when keys are present', () => {
      // Mock environment variable being set
      const originalEnv = process.env.OPENROUTER_API_KEY;
      process.env.OPENROUTER_API_KEY = 'sk-or-v1-' + 'a'.repeat(64);

      const result = apiKeyManager.checkRequiredKeys();

      expect(result.allConfigured).toBe(true);
      expect(result.missing).toHaveLength(0);

      // Restore environment
      if (originalEnv) {
        process.env.OPENROUTER_API_KEY = originalEnv;
      } else {
        delete process.env.OPENROUTER_API_KEY;
      }
    });

    it('should check stored keys when environment variables not set', () => {
      const storedKeys = [
        {
          id: 'openrouter_123',
          service: 'openrouter',
          keyHash: 'hash',
          isActive: true,
          createdAt: new Date().toISOString(),
          environment: 'both'
        }
      ];
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(storedKeys));
      
      // Mock environment variable not being set
      const originalEnv = process.env.OPENROUTER_API_KEY;
      delete process.env.OPENROUTER_API_KEY;

      const result = apiKeyManager.checkRequiredKeys();

      expect(result.allConfigured).toBe(true);
      expect(result.missing).toHaveLength(0);

      // Restore environment
      if (originalEnv) {
        process.env.OPENROUTER_API_KEY = originalEnv;
      }
    });
  });

  describe('updateLastUsed', () => {
    it('should update last used timestamp for active key', async () => {
      const storedKeys = [
        {
          id: 'openrouter_123',
          service: 'openrouter',
          keyHash: 'hash',
          isActive: true,
          createdAt: new Date().toISOString(),
          environment: 'both'
        }
      ];
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(storedKeys));

      await apiKeyManager.updateLastUsed('openrouter');

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'ai_api_keys',
        expect.stringContaining('"lastUsed"')
      );
    });

    it('should handle update for non-existent service gracefully', async () => {
      localStorageMock.getItem.mockReturnValue('[]');

      await expect(apiKeyManager.updateLastUsed('non-existent')).resolves.not.toThrow();
    });
  });
});