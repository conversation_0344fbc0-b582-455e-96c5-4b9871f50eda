import Database from 'better-sqlite3';

class DatabaseManager {
  private static instance: DatabaseManager;
  private db: Database.Database | null = null;

  // Privater Konstruktor für Singleton-Pattern
  private constructor() {}

  /**
   * Gibt die Singleton-Instanz des DatabaseManagers zurück
   */
  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  /**
   * Stellt eine Verbindung zur SQLite-Datenbank her
   * @param dbPath Pfad zur SQLite-Datenbankdatei
   */
  public static async connect(dbPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const instance = DatabaseManager.getInstance();
        
        // Bestehende Verbindung schließen, falls vorhanden
        if (instance.db) {
          DatabaseManager.close();
        }

        // Neue Verbindung herstellen
        instance.db = new Database(dbPath, {
          verbose: console.log, // Log SQL-Abfragen für Debugging
        });

        // Fremdschlüssel-Unterstützung aktivieren
        instance.db.pragma('foreign_keys = ON');
        
        console.log('Datenbankverbindung erfolgreich hergestellt');
        resolve();
      } catch (error) {
        console.error('Fehler beim Herstellen der Datenbankverbindung:', error);
        reject(error);
      }
    });
  }

  /**
   * Gibt eine Liste aller Tabellen in der Datenbank zurück
   */
  public static async getTables(): Promise<{name: string}[]> {
    const instance = DatabaseManager.getInstance();
    this.ensureConnection();

    try {
      const stmt = instance.db!.prepare(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name != 'android_metadata'"
      );
      return stmt.all() as {name: string}[];
    } catch (error) {
      console.error('Fehler beim Abrufen der Tabellen:', error);
      throw error;
    }
  }

  /**
   * Liest Daten aus einer Tabelle aus
   * @param tableName Name der Tabelle
   * @param limit Maximale Anzahl der zurückzugebenden Zeilen (optional)
   */
  public static async getTableData(
    tableName: string, 
    limit: number = 100
  ): Promise<{ columns: string[]; rows: any[] }> {
    const instance = DatabaseManager.getInstance();
    this.ensureConnection();

    try {
      // Spaltennamen abrufen
      const columnsStmt = instance.db!.prepare(`PRAGMA table_info(${tableName})`);
      const columns = (columnsStmt.all() as any[]).map(col => col.name);

      // Daten abrufen
      const dataStmt = instance.db!.prepare(
        `SELECT * FROM ${tableName} LIMIT ?`
      );
      const rows = dataStmt.all(limit);

      return {
        columns,
        rows: Array.isArray(rows) ? rows : [rows]
      };
    } catch (error) {
      console.error(`Fehler beim Lesen der Tabelle ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Führt eine SQL-Abfrage aus
   * @param sql SQL-Abfrage
   * @param params Parameter für die Abfrage
   */
  public static async query(sql: string, params: any[] = []): Promise<any> {
    const instance = DatabaseManager.getInstance();
    this.ensureConnection();

    try {
      const stmt = instance.db!.prepare(sql);
      return stmt.all(...params);
    } catch (error) {
      console.error('Fehler bei der SQL-Abfrage:', { sql, error });
      throw error;
    }
  }

  /**
   * Schließt die Datenbankverbindung
   */
  public static close(): void {
    const instance = DatabaseManager.getInstance();
    
    if (instance.db) {
      try {
        instance.db.close();
        console.log('Datenbankverbindung geschlossen');
      } catch (error) {
        console.error('Fehler beim Schließen der Datenbankverbindung:', error);
      } finally {
        instance.db = null;
      }
    }
  }

  /**
   * Überprüft, ob eine Datenbankverbindung besteht
   */
  private static ensureConnection(): void {
    const instance = DatabaseManager.getInstance();
    
    if (!instance.db) {
      throw new Error('Keine aktive Datenbankverbindung. Bitte verbinden Sie sich zuerst mit der Datenbank.');
    }
  }
}

export default DatabaseManager;
