/**
 * Comprehensive Error Handling Test
 * Tests the complete error handling system functionality
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

describe('Comprehensive Error Handling System', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should have all required error handling components', async () => {
    // Test that all main components can be imported
    const errorTypes = await import('../../types/errors');
    const errorMessages = await import('../../utils/errorMessages');
    const errorHandler = await import('../AIErrorHandler');
    const operationLogger = await import('../AIOperationLogger');

    expect(errorTypes.AIServiceErrorCode).toBeDefined();
    expect(errorTypes.AIServiceErrorSeverity).toBeDefined();
    expect(errorMessages.formatErrorForUser).toBeDefined();
    expect(errorMessages.formatErrorForTechnical).toBeDefined();
    expect(errorHandler.AIErrorHandler).toBeDefined();
    expect(operationLogger.AIOperationLogger).toBeDefined();
  });

  it('should create and handle errors end-to-end', async () => {
    const { AIErrorHandler } = await import('../AIErrorHandler');
    const { AIServiceErrorCode } = await import('../../types/errors');
    
    const handler = AIErrorHandler.getInstance();
    
    // Create an error
    const error = handler.createError(
      AIServiceErrorCode.API_REQUEST_FAILED,
      'TestService',
      'testOperation',
      new Error('Test error')
    );

    expect(error.code).toBe(AIServiceErrorCode.API_REQUEST_FAILED);
    expect(error.service).toBe('TestService');
    expect(error.operation).toBe('testOperation');
    expect(error.userMessage).toBeDefined();
    expect(error.technicalMessage).toBeDefined();
  });

  it('should log operations correctly', async () => {
    const { AIOperationLogger } = await import('../AIOperationLogger');
    
    const logger = AIOperationLogger.getInstance();
    
    // Start an operation
    const operationId = logger.startOperation(
      'TestService',
      'testOperation',
      'query',
      { input: 'test' }
    );

    expect(operationId).toBeDefined();
    expect(typeof operationId).toBe('string');

    // Complete the operation
    logger.completeOperation(operationId, true, { result: 'success' });

    // Verify logs
    const logs = logger.getOperationLogs();
    expect(logs.length).toBeGreaterThan(0);
    expect(logs[0].id).toBe(operationId);
    expect(logs[0].success).toBe(true);
  });

  it('should format error messages correctly', async () => {
    const { formatErrorForUser, formatErrorForTechnical } = await import('../../utils/errorMessages');
    const { AIServiceErrorCode } = await import('../../types/errors');

    const error = {
      code: AIServiceErrorCode.API_REQUEST_FAILED,
      userMessage: 'User message',
      technicalMessage: 'Technical message'
    };

    const userMessage = formatErrorForUser(error, 'Test Context');
    const technicalMessage = formatErrorForTechnical(error);

    expect(userMessage).toContain('Test Context');
    expect(userMessage).toContain('Die KI-Anfrage konnte nicht verarbeitet werden');
    expect(technicalMessage).toContain('OpenRouter API request failed');
  });

  it('should provide error summary and metrics', async () => {
    const { AIOperationLogger } = await import('../AIOperationLogger');
    const { AIErrorHandler } = await import('../AIErrorHandler');
    const { AIServiceErrorCode } = await import('../../types/errors');
    
    const logger = AIOperationLogger.getInstance();
    const handler = AIErrorHandler.getInstance();

    // Clear any existing logs
    logger.clearLogs();

    // Create and log some errors
    const error1 = handler.createError(
      AIServiceErrorCode.API_REQUEST_FAILED,
      'Service1',
      'operation1'
    );
    
    const error2 = handler.createError(
      AIServiceErrorCode.API_TIMEOUT,
      'Service2',
      'operation2'
    );

    logger.logError(error1);
    logger.logError(error2);

    // Get summary
    const summary = handler.getErrorSummary();
    expect(summary.totalErrors).toBe(2);
    expect(summary.errorsByCode[AIServiceErrorCode.API_REQUEST_FAILED]).toBe(1);
    expect(summary.errorsByCode[AIServiceErrorCode.API_TIMEOUT]).toBe(1);
  });

  it('should export logs in different formats', async () => {
    const { AIOperationLogger } = await import('../AIOperationLogger');
    
    const logger = AIOperationLogger.getInstance();
    logger.clearLogs();

    // Create a test operation
    const opId = logger.startOperation('TestService', 'testOp', 'query');
    logger.completeOperation(opId, true, { result: 'test' });

    // Test JSON export
    const jsonExport = logger.exportLogs('json');
    expect(() => JSON.parse(jsonExport)).not.toThrow();

    // Test CSV export
    const csvExport = logger.exportLogs('csv');
    expect(csvExport).toContain('ID,Service,Operation');
    expect(csvExport).toContain('TestService,testOp');
  });
});