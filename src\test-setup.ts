import '@testing-library/jest-dom';

// Mock process.env for tests
Object.defineProperty(process, 'env', {
  value: {
    NODE_ENV: 'test',
    OPENROUTER_API_KEY: 'test-api-key',
    VECTOR_DB_PATH: ':memory:',
  },
  writable: true,
});

// Mock better-sqlite3 for tests
vi.mock('better-sqlite3', () => {
  const mockDb = {
    prepare: vi.fn(),
    exec: vi.fn(),
    pragma: vi.fn(),
    loadExtension: vi.fn(),
    close: vi.fn(),
    transaction: vi.fn((fn) => fn),
  };

  const mockStatement = {
    run: vi.fn(() => ({ changes: 1 })),
    get: vi.fn(() => ({ total: 0 })),
    all: vi.fn(() => []),
  };

  mockDb.prepare.mockReturnValue(mockStatement);

  return {
    default: vi.fn(() => mockDb),
  };
});

// Mock axios for tests
vi.mock('axios', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));