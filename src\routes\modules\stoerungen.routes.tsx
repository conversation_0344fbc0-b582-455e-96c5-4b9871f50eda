import { createRoute } from "@tanstack/react-router";
import { lazy } from "react";
import { ModuleGuard } from '@/components/auth';
import { stoerungenModuleConfig } from '@/modules/stoerungen/module.config';

// Import the Root-Route from __root.tsx
import { RootRoute as rootRoute } from '@/routes/__root';

// Lazy load Störungen pages
const StoerungenPage = lazy(() => import("@/modules/stoerungen/pages/StoerungenPage"));

/**
 * Störungen Module Routes
 * 
 * Definiert alle Routen für das Störungen-Modul mit rollenbasierter Zugriffskontrolle.
 * Alle Routen sind unter /modules/stoerungen/* organisiert.
 */

// Störungen Module Base Route
export const StoerungenModuleRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/modules/stoerungen",
  component: () => (
    <ModuleGuard 
      moduleId={stoerungenModuleConfig.id}
      requiredRoles={stoerungenModuleConfig.requiredRoles}
    >
      <StoerungenPage />
    </ModuleGuard>
  ),
});

// Future: Monitoring Page Route (when implemented)
// export const StoerungenMonitoringRoute = createRoute({
//   getParentRoute: () => rootRoute,
//   path: "/modules/stoerungen/monitoring",
//   component: () => (
//     <ModuleGuard 
//       moduleId={stoerungenModuleConfig.id}
//       requiredRoles={stoerungenModuleConfig.requiredRoles}
//     >
//       <MonitoringPage />
//     </ModuleGuard>
//   ),
// });

// Export all Störungen routes
export const stoerungenRoutes = [
  StoerungenModuleRoute,
  // StoerungenMonitoringRoute, // Uncomment when MonitoringPage is implemented
];