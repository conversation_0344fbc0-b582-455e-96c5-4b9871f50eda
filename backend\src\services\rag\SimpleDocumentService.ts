/**
 * SimpleDocumentService - Basic document management for SQLite RAG
 */

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
import * as crypto from 'crypto';
import { CreateDocumentRequest } from '../../types/rag.types';

export class SimpleDocumentService {
  private db: Database.Database;

  constructor(dbPath?: string) {
    const ragDbPath = dbPath || path.join(process.cwd(), 'database', 'rag_knowledge.db');
    this.db = new Database(ragDbPath);
  }

  /**
   * Store document in database
   */
  async storeDocument(request: CreateDocumentRequest): Promise<string> {
    const id = uuidv4();
    const now = new Date().toISOString();
    const contentHash = this.generateContentHash(request.content);

    // Prepare metadata
    const metadata = {
      ...request.metadata,
      uploadedAt: now,
      contentHash,
      knowledgeBaseId: request.knowledgeBaseId || 1
    };

    const stmt = this.db.prepare(`
      INSERT INTO documents (id, knowledge_base_id, title, content, source, source_path, content_type, language, metadata, hash, isActive, created_at, updated_at, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id,
      request.knowledgeBaseId || 1,
      request.title,
      request.content,
      request.source || 'upload',
      request.source || '',
      request.contentType || 'text/plain',
      request.language || 'de',
      JSON.stringify(metadata),
      contentHash,
      1, // isActive
      now,
      now,
      'indexed'
    );

    // Handle category relationship if provided
    if (request.metadata?.category) {
      try {
        const categoryStmt = this.db.prepare(`
          INSERT OR IGNORE INTO document_categories (document_id, category_id)
          VALUES (?, ?)
        `);
        categoryStmt.run(id, request.metadata.category);
      } catch (error) {
        console.warn('Failed to associate document with category:', error);
      }
    }

    return id;
  }

  /**
   * Store chunk in database
   */
  async storeChunk(documentId: string, content: string, index: number): Promise<string> {
    const chunkId = uuidv4();
    const now = new Date().toISOString();
    const tokenCount = Math.ceil(content.length / 4); // Rough estimate

    const stmt = this.db.prepare(`
      INSERT INTO chunks (id, document_id, content, chunk_index, token_count, start_position, end_position, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      chunkId,
      documentId,
      content,
      index,
      tokenCount,
      0, // start_position - would need proper calculation
      content.length, // end_position
      now
    );

    return chunkId;
  }

  /**
   * Simple document chunking
   */
  chunkDocument(content: string, maxChunkSize: number = 500): string[] {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const chunks: string[] = [];
    let currentChunk = '';

    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());
        currentChunk = sentence.trim();
      } else {
        currentChunk += (currentChunk.length > 0 ? '. ' : '') + sentence.trim();
      }
    }

    if (currentChunk.length > 0) {
      chunks.push(currentChunk.trim());
    }

    return chunks.length > 0 ? chunks : [content]; // Fallback to full content
  }

  /**
   * Get all documents
   */
  async getAllDocuments() {
    const stmt = this.db.prepare(`
      SELECT 
        d.id,
        d.title,
        d.source as filename,
        d.content_type,
        d.language,
        d.created_at,
        d.updated_at,
        LENGTH(d.content) as fileSize,
        COUNT(DISTINCT c.id) as chunksCount,
        COUNT(DISTINCT e.id) as embeddingsCount,
        COALESCE(cat.id, 'system') as category,
        cat.description
      FROM documents d
      LEFT JOIN chunks c ON d.id = c.document_id
      LEFT JOIN embeddings e ON c.id = e.chunk_id
      LEFT JOIN document_categories dc ON d.id = dc.document_id
      LEFT JOIN categories cat ON dc.category_id = cat.id
      WHERE d.status = 'indexed'
      GROUP BY d.id, cat.id
      ORDER BY d.created_at DESC
    `);

    const rows = stmt.all();
    
    return rows.map((row: any) => ({
      id: row.id,
      title: row.title,
      category: row.category || 'system',
      description: row.description,
      language: row.language,
      filename: row.filename,
      fileSize: row.fileSize,
      chunksCount: row.chunksCount,
      embeddingsCount: row.embeddingsCount,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));
  }

  /**
   * Get chunks for a document
   */
  async getDocumentChunks(documentId: string): Promise<Array<{id: string, content: string, chunk_index: number}>> {
    const stmt = this.db.prepare(`
      SELECT id, content, chunk_index
      FROM chunks
      WHERE document_id = ?
      ORDER BY chunk_index
    `);

    return stmt.all(documentId) as Array<{id: string, content: string, chunk_index: number}>;
  }

  /**
   * Delete document chunks
   */
  async deleteDocumentChunks(documentId: string): Promise<number> {
    const stmt = this.db.prepare(`
      DELETE FROM chunks WHERE document_id = ?
    `);

    const result = stmt.run(documentId);
    return result.changes;
  }

  /**
   * Delete document
   */
  async deleteDocument(documentId: string): Promise<boolean> {
    const stmt = this.db.prepare(`
      DELETE FROM documents WHERE id = ?
    `);

    const result = stmt.run(documentId);
    return result.changes > 0;
  }

  /**
   * Generate content hash for deduplication
   */
  private generateContentHash(content: string): string {
    return crypto.createHash('sha256').update(content.trim()).digest('hex');
  }

  /**
   * Cleanup resources
   */
  async disconnect(): Promise<void> {
    if (this.db) {
      this.db.close();
    }
  }
}

export default SimpleDocumentService;