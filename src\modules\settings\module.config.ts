import { ModuleConfig } from '@/types/module';

/**
 * Konfiguration für das Settings-Modul
 * 
 * Enthält alle Einstellungen und Konfigurationen für die Anwendung
 */
export const settingsModuleConfig: ModuleConfig = {
  id: 'settings',
  name: 'settings',
  displayName: 'App-Settings',
  description: 'Anwendungseinstellungen und Konfiguration',
  icon: 'Settings',
  baseRoute: '/modules/settings',
  requiredRoles: ['Besucher', 'Benutzer', 'Administrator'],
  isEnabled: true,
  pages: [
    { id: 'general', name: 'Allgemein', route: '/modules/settings', component: 'SettingsPage' }
  ]
};
