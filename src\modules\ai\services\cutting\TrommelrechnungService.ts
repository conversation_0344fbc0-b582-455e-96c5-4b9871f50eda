/**
 * Trommelrechnungs-Service
 * 
 * Implementiert die mathematischen Formeln zur Berechnung der maximalen Kabellänge auf Trommeln
 * basierend auf der Dokumentation in Trommelrechnung.md
 */

// Importiere den neuen Database API Service anstelle der JSON-Dateien
import DatabaseApiService, { MaterialdatenFromDB, TrommeldatenFromDB } from '../../../../services/api/database-api.service';

// Typen für Trommel- und Kabeldaten
export interface TrommelData {
  Trommelname: string;
  Außendurchmesser: number;
  Kerndurchmesser: number;
  Freiraum_mm: number;
  Wickelbreite_mm: number;
  Max_Tragkraft_Kg: number;
}

export interface KabelMaterialData {
  MATNR: string;
  Kabeldurchmesser: number;
  Zuschlag_Kabedurchmesser: number;
  Biegefaktor: number;
  Ringauslieferung: string | null;
  Kleinster_erlauber_Freiraum: number;
  Bruttogewicht_pro_m: number;
  // Zusätzliche Felder aus der Datenbank
  Materialkurztext?: string;
}

export interface TrommelrechnungInput {
  // Kabeldaten
  kabeldurchmesser_mm: number;
  prozentualer_zuschlag: number;
  bruttogewicht_pro_meter_kg: number;
  gewünschte_kabellänge_m?: number;
  
  // Trommeldaten
  außendurchmesser_mm: number;
  kerndurchmesser_mm: number;
  freiraum_mm: number;
  führungsbogenhöhe_mm: number;
  innenbreite_mm: number;
  max_tragkraft_kg: number;
}

export interface TrommelrechnungResult {
  maximale_kabellänge_m: number;
  ist_geeignet: boolean;
  empfehlung: string;
  berechnungsdetails: {
    kdmmz: number; // Kabeldurchmesser mit Zuschlag
    hochlagenzahl: number;
    querlagenzahl: number;
    mittlerer_trommelumfang: number;
    tatsächlicher_freiraum: number;
    gewicht_gesamt_kg: number;
    tragkraft_ausreichend: boolean;
  };
  warnungen: string[];
}

export interface TrommelEmpfehlung {
  trommelname: string;
  maximale_kabellänge_m: number;
  ist_optimal: boolean;
  grund: string;
}

export class TrommelrechnungService {
  private static readonly WILDWICKELFAKTOR = 3.05; // 0.97 * Pi
  private static readonly LAGENFAKTOR = 0.93; // Berücksichtigt, dass Kabellagen nicht genau übereinander liegen
  private static readonly MAX_HOCHLAGEN_ÜBER_FÜHRUNGSBOGEN = 2;
  
  /**
   * Berechnet die maximale Kabellänge für eine gegebene Trommel-Kabel-Kombination
   */
  static berechneMaximaleKabellänge(input: TrommelrechnungInput): TrommelrechnungResult {
    const warnungen: string[] = [];
    
    // 1. Kabeldurchmesser inklusive Zuschlag berechnen
    const kdmmz = input.kabeldurchmesser_mm * (1 + input.prozentualer_zuschlag);
    
    // 2. Hochlagenzahl (HL) berechnen
    let hochlagenzahl = Math.floor(
      (input.außendurchmesser_mm - input.kerndurchmesser_mm - (2 * input.freiraum_mm)) / (2 * kdmmz)
    );
    
    // 3. Tatsächlichen Trommelfreiraum berechnen
    let tatsächlicher_freiraum = ((input.außendurchmesser_mm - input.kerndurchmesser_mm) / 2) - hochlagenzahl * kdmmz;
    
    // 4. Freiraum-Überprüfung und Anpassung der Hochlagenzahl
    while (tatsächlicher_freiraum < input.freiraum_mm && hochlagenzahl > 0) {
      hochlagenzahl -= 1;
      tatsächlicher_freiraum = ((input.außendurchmesser_mm - input.kerndurchmesser_mm) / 2) - hochlagenzahl * kdmmz;
    }
    
    // 5. Führungsbogen-Überprüfung (falls Führungsbogenhöhe > 0)
    if (input.führungsbogenhöhe_mm > 0) {
      while (hochlagenzahl > 0 && 
             (hochlagenzahl * kdmmz * this.LAGENFAKTOR) >= (input.führungsbogenhöhe_mm + 2 * kdmmz)) {
        hochlagenzahl -= 1;
        warnungen.push('Hochlagenzahl wurde wegen Führungsbogen reduziert');
      }
    }
    
    // 6. Querlagenzahl (QL) berechnen
    const querlagenzahl = Math.floor(input.innenbreite_mm / kdmmz);
    
    // 7. Mittleren Trommelumfang (MU) berechnen
    const mittlerer_trommelumfang = Math.round(
      this.WILDWICKELFAKTOR * (input.kerndurchmesser_mm + hochlagenzahl * kdmmz)
    );
    
    // 8. Maximale Kabellänge berechnen (in Meter)
    const maximale_kabellänge_m = (hochlagenzahl * querlagenzahl * mittlerer_trommelumfang) / 1000;
    
    // 9. Gewicht berechnen
    const gewicht_gesamt_kg = maximale_kabellänge_m * input.bruttogewicht_pro_meter_kg;
    
    // 10. Tragkraft prüfen
    const tragkraft_ausreichend = gewicht_gesamt_kg <= input.max_tragkraft_kg;
    
    if (!tragkraft_ausreichend) {
      warnungen.push(`Gewicht (${gewicht_gesamt_kg.toFixed(2)} kg) überschreitet Tragkraft (${input.max_tragkraft_kg} kg)`);
    }
    
    // 11. Geeignetheit prüfen
    const ist_geeignet = hochlagenzahl > 0 && querlagenzahl > 0 && tragkraft_ausreichend;
    
    // 12. Empfehlung generieren
    let empfehlung = '';
    if (!ist_geeignet) {
      if (hochlagenzahl <= 0 || querlagenzahl <= 0) {
        empfehlung = 'Trommel ist zu klein für dieses Kabel. Wählen Sie eine größere Trommel.';
      } else if (!tragkraft_ausreichend) {
        empfehlung = 'Tragkraft der Trommel ist unzureichend. Wählen Sie eine stabilere Trommel.';
      }
    } else {
      empfehlung = `Trommel ist geeignet. Maximale Kabellänge: ${maximale_kabellänge_m.toFixed(2)} m`;
    }
    
    return {
      maximale_kabellänge_m: Number(maximale_kabellänge_m.toFixed(2)),
      ist_geeignet,
      empfehlung,
      berechnungsdetails: {
        kdmmz: Number(kdmmz.toFixed(2)),
        hochlagenzahl,
        querlagenzahl,
        mittlerer_trommelumfang,
        tatsächlicher_freiraum: Number(tatsächlicher_freiraum.toFixed(2)),
        gewicht_gesamt_kg: Number(gewicht_gesamt_kg.toFixed(2)),
        tragkraft_ausreichend
      },
      warnungen
    };
  }
  
  /**
   * Prüft ob Ring verwendet werden kann (Kabellänge <= 250m und Gewicht <= 30kg)
   */
  static prüfeRingVerwendung(kabellänge_m: number, gewicht_kg: number): boolean {
    return kabellänge_m <= 250 && gewicht_kg <= 30;
  }
  
  /**
   * Findet die beste Trommel für gegebene Kabelparameter
   */
  static async findeOptimaleTrommel(
    kabeldurchmesser_mm: number,
    prozentualer_zuschlag: number,
    bruttogewicht_pro_meter_kg: number,
    gewünschte_kabellänge_m: number,
    führungsbogenhöhe_mm: number = 0
  ): Promise<TrommelEmpfehlung | null> {
    const gewicht_gesamt = gewünschte_kabellänge_m * bruttogewicht_pro_meter_kg;
    
    // Prüfe zuerst Ring
    if (this.prüfeRingVerwendung(gewünschte_kabellänge_m, gewicht_gesamt)) {
      return {
        trommelname: 'Ring',
        maximale_kabellänge_m: 250,
        ist_optimal: true,
        grund: 'Ring ist optimal für kleine Längen und geringes Gewicht'
      };
    }
    
    // Durchsuche verfügbare Trommeln
    const trommeln = await this.getVerfügbareTrommeln();
    const geeigneteTrammeln: Array<TrommelEmpfehlung & { außendurchmesser: number }> = [];
    
    for (const trommel of trommeln) {
      const input: TrommelrechnungInput = {
        kabeldurchmesser_mm,
        prozentualer_zuschlag,
        bruttogewicht_pro_meter_kg,
        gewünschte_kabellänge_m,
        außendurchmesser_mm: trommel.Außendurchmesser,
        kerndurchmesser_mm: trommel.Kerndurchmesser,
        freiraum_mm: trommel.Freiraum_mm,
        führungsbogenhöhe_mm,
        innenbreite_mm: trommel.Wickelbreite_mm,
        max_tragkraft_kg: trommel.Max_Tragkraft_Kg
      };
      
      const result = this.berechneMaximaleKabellänge(input);
      
      if (result.ist_geeignet && result.maximale_kabellänge_m >= gewünschte_kabellänge_m) {
        geeigneteTrammeln.push({
          trommelname: trommel.Trommelname,
          maximale_kabellänge_m: result.maximale_kabellänge_m,
          ist_optimal: false,
          grund: `Kann ${gewünschte_kabellänge_m}m aufnehmen (max: ${result.maximale_kabellänge_m}m)`,
          außendurchmesser: trommel.Außendurchmesser
        });
      }
    }
    
    if (geeigneteTrammeln.length === 0) {
      return null;
    }
    
    // Wähle die kleinste geeignete Trommel (nach Außendurchmesser)
    geeigneteTrammeln.sort((a, b) => a.außendurchmesser - b.außendurchmesser);
    const beste = geeigneteTrammeln[0];
    beste.ist_optimal = true;
    beste.grund = `Kleinste geeignete Trommel für ${gewünschte_kabellänge_m}m`;
    
    return {
      trommelname: beste.trommelname,
      maximale_kabellänge_m: beste.maximale_kabellänge_m,
      ist_optimal: beste.ist_optimal,
      grund: beste.grund
    };
  }
  
  /**
   * Lädt verfügbare Kabelmaterialien aus der Datenbank
   * Performance-optimiert: Lädt nur die ersten 100 Materialien für bessere Performance
   */
  static async getVerfügbareKabelmaterialien(): Promise<KabelMaterialData[]> {
    try {
      // Verwende die neue Suchfunktion mit Limit für bessere Performance
      const { data: materialdatenFromDB } = await DatabaseApiService.searchMaterialdaten('', 100, 0);
      
      // Transformiere Datenbank-Format zu Service-Format
      return materialdatenFromDB.map(material => ({
        MATNR: material.MATNR,
        Kabeldurchmesser: material.Kabeldurchmesser,
        Zuschlag_Kabedurchmesser: material.ZuschlagKabeldurchmesser,
        Biegefaktor: material.Biegefaktor,
        Ringauslieferung: material.Ringauslieferung || null,
        Kleinster_erlauber_Freiraum: material.KleinsterErlauberFreiraum,
        Bruttogewicht_pro_m: material.Bruttogewicht,
        Materialkurztext: material.Materialkurztext
      }));
    } catch (error) {
      console.error('Fehler beim Laden der Kabelmaterialien:', error);
      return [];
    }
  }

  /**
   * Sucht Kabelmaterialien basierend auf Suchbegriff
   * @param searchTerm Suchbegriff für MATNR oder Materialkurztext
   * @param limit Maximale Anzahl der Ergebnisse (Standard: 100)
   * @returns Promise mit Array von gefilterten Kabelmaterialien
   */
  static async searchKabelmaterialien(
    searchTerm: string = '',
    limit: number = 100
  ): Promise<{ materials: KabelMaterialData[], total: number }> {
    try {
      const { data: materialdatenFromDB, total } = await DatabaseApiService.searchMaterialdaten(searchTerm, limit, 0);
      
      // Transformiere Datenbank-Format zu Service-Format
      const materials = materialdatenFromDB.map(material => ({
        MATNR: material.MATNR,
        Kabeldurchmesser: material.Kabeldurchmesser,
        Zuschlag_Kabedurchmesser: material.ZuschlagKabeldurchmesser,
        Biegefaktor: material.Biegefaktor,
        Ringauslieferung: material.Ringauslieferung || null,
        Kleinster_erlauber_Freiraum: material.KleinsterErlauberFreiraum,
        Bruttogewicht_pro_m: material.Bruttogewicht,
        Materialkurztext: material.Materialkurztext
      }));
      
      return { materials, total };
    } catch (error) {
      console.error('Fehler beim Suchen der Kabelmaterialien:', error);
      return { materials: [], total: 0 };
    }
  }
  
  /**
   * Lädt verfügbare Trommeln aus der Datenbank
   */
  static async getVerfügbareTrommeln(): Promise<TrommelData[]> {
    try {
      const trommeldatenFromDB = await DatabaseApiService.getTrommeldaten();
      
      // Transformiere Datenbank-Format zu Service-Format
      return trommeldatenFromDB.map(trommel => ({
        Trommelname: trommel.Trommelname,
        Außendurchmesser: trommel.Außendurchmesser,
        Kerndurchmesser: trommel.Kerndurchmesser,
        Freiraum_mm: trommel.Freiraum_mm,
        Wickelbreite_mm: trommel.Wickelbreite_mm,
        Max_Tragkraft_Kg: trommel.MaxTragkraft_Kg
      }));
    } catch (error) {
      console.error('Fehler beim Laden der Trommeldaten:', error);
      return [];
    }
  }
  
  /**
   * Findet Kabelmaterial nach MATNR
   */
  static async findKabelmaterial(matnr: string): Promise<KabelMaterialData | null> {
    try {
      const materialFromDB = await DatabaseApiService.findKabelmaterial(matnr);
      
      if (!materialFromDB) {
        return null;
      }
      
      // Transformiere Datenbank-Format zu Service-Format
      return {
        MATNR: materialFromDB.MATNR,
        Kabeldurchmesser: materialFromDB.Kabeldurchmesser,
        Zuschlag_Kabedurchmesser: materialFromDB.ZuschlagKabeldurchmesser,
        Biegefaktor: materialFromDB.Biegefaktor,
        Ringauslieferung: materialFromDB.Ringauslieferung || null,
        Kleinster_erlauber_Freiraum: materialFromDB.KleinsterErlauberFreiraum,
        Bruttogewicht_pro_m: materialFromDB.Bruttogewicht,
        Materialkurztext: materialFromDB.Materialkurztext
      };
    } catch (error) {
      console.error('Fehler beim Suchen des Kabelmaterials:', error);
      return null;
    }
  }
  
  /**
   * Findet Trommel nach Name
   */
  static async findTrommel(trommelname: string): Promise<TrommelData | null> {
    try {
      const trommelFromDB = await DatabaseApiService.findTrommel(trommelname);
      
      if (!trommelFromDB) {
        return null;
      }
      
      // Transformiere Datenbank-Format zu Service-Format
      return {
        Trommelname: trommelFromDB.Trommelname,
        Außendurchmesser: trommelFromDB.Außendurchmesser,
        Kerndurchmesser: trommelFromDB.Kerndurchmesser,
        Freiraum_mm: trommelFromDB.Freiraum_mm,
        Wickelbreite_mm: trommelFromDB.Wickelbreite_mm,
        Max_Tragkraft_Kg: trommelFromDB.MaxTragkraft_Kg
      };
    } catch (error) {
      console.error('Fehler beim Suchen der Trommel:', error);
      return null;
    }
  }
}

export default TrommelrechnungService;