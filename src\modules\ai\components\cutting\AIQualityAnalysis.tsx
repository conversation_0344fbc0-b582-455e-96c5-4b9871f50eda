import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  TrendingUp,
  TrendingDown,
  Target,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Lightbulb
} from 'lucide-react';

interface AIQualityAnalysisProps {
  analysisData: {
    averageQualityScore: number;
    improvementAreas: string[];
    successPatterns: string[];
    recommendations: string[];
  };
  className?: string;
}

/**
 * Komponente zur Anzeige der KI-Qualitätsanalyse
 * Zeigt durchschnittliche Qualitätsbewertung, Verbesserungsbereiche, 
 * Erfolgsmuster und Empfehlungen für die KI-Optimierung an
 */
export const AIQualityAnalysis: React.FC<AIQualityAnalysisProps> = ({
  analysisData,
  className = ''
}) => {
  const {
    averageQualityScore,
    improvementAreas,
    successPatterns,
    recommendations
  } = analysisData;

  // Bestimme die Farbe und das Icon basierend auf dem durchschnittlichen Qualitätsscore
  const getScoreColor = (score: number): string => {
    if (score >= 85) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 85) return <CheckCircle className="h-6 w-6 text-green-600" />;
    if (score >= 70) return <AlertTriangle className="h-6 w-6 text-yellow-600" />;
    return <TrendingDown className="h-6 w-6 text-red-600" />;
  };

  const getScoreDescription = (score: number): string => {
    if (score >= 85) return 'Ausgezeichnet';
    if (score >= 70) return 'Gut';
    if (score >= 50) return 'Verbesserungsbedürftig';
    return 'Kritisch';
  };

  const getProgressColor = (score: number): string => {
    if (score >= 85) return 'bg-green-500';
    if (score >= 70) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Gesamtbewertung */}
      <Card className="border-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            KI-Qualitätsanalyse
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Hauptscore */}
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center gap-2">
                {getScoreIcon(averageQualityScore)}
                <span className={`text-4xl font-bold ${getScoreColor(averageQualityScore)}`}>
                  {averageQualityScore.toFixed(1)}%
                </span>
              </div>
              <div className="text-lg font-medium text-gray-700">
                {getScoreDescription(averageQualityScore)}
              </div>
              <Progress
                value={averageQualityScore}
                className="h-3 w-full max-w-md mx-auto"
              />
            </div>

            {/* Score-Bereiche */}
            <div className="grid grid-cols-3 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="text-sm text-gray-500">Kritisch</div>
                <div className="text-lg font-bold text-red-600">&lt; 50%</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-gray-500">Gut</div>
                <div className="text-lg font-bold text-yellow-600">50-85%</div>
              </div>
              <div className="text-center">
                <div className="text-sm text-gray-500">Ausgezeichnet</div>
                <div className="text-lg font-bold text-green-600">≥ 85%</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Erfolgsmuster */}
      {successPatterns.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Erfolgsmuster
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {successPatterns.map((pattern, index) => (
                <div key={index} className="flex items-start gap-2 p-3 bg-green-50 rounded-lg">
                  <TrendingUp className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-green-800">{pattern}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Verbesserungsbereiche */}
      {improvementAreas.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-orange-600" />
              Hauptverbesserungsbereiche
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {improvementAreas.map((area, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Badge variant="outline" className="border-orange-300 text-orange-700">
                    {index + 1}
                  </Badge>
                  <span className="text-sm">{area}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empfehlungen */}
      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-blue-600" />
              Empfehlungen zur Optimierung
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recommendations.map((recommendation, index) => {
                // Bestimme die Priorität basierend auf Schlüsselwörtern
                const isHighPriority = recommendation.toLowerCase().includes('kritisch') ||
                  recommendation.toLowerCase().includes('sofort') ||
                  recommendation.toLowerCase().includes('dringend');
                const isMediumPriority = recommendation.toLowerCase().includes('optimierung') ||
                  recommendation.toLowerCase().includes('verbesserung');

                let alertClass = 'border-blue-200 bg-blue-50';
                let textClass = 'text-blue-800';
                let priorityBadge = 'Niedrig';
                let badgeClass = 'bg-blue-100 text-blue-800';

                if (isHighPriority) {
                  alertClass = 'border-red-200 bg-red-50';
                  textClass = 'text-red-800';
                  priorityBadge = 'Hoch';
                  badgeClass = 'bg-red-100 text-red-800';
                } else if (isMediumPriority) {
                  alertClass = 'border-yellow-200 bg-yellow-50';
                  textClass = 'text-yellow-800';
                  priorityBadge = 'Mittel';
                  badgeClass = 'bg-yellow-100 text-yellow-800';
                }

                return (
                  <Alert key={index} className={alertClass}>
                    <div className="flex items-start justify-between">
                      <AlertDescription className={`${textClass} flex-1 pr-2`}>
                        {recommendation}
                      </AlertDescription>
                      <Badge className={`${badgeClass} text-xs flex-shrink-0`}>
                        {priorityBadge}
                      </Badge>
                    </div>
                  </Alert>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Aktionsempfehlungen */}
      <Card className="border-2 border-dashed border-gray-300">
        <CardHeader>
          <CardTitle className="text-lg">Nächste Schritte</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {averageQualityScore < 50 && (
              <Alert className="border-red-200 bg-red-50">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-red-800">
                  <strong>Sofortige Maßnahmen erforderlich:</strong> Die KI-Qualität ist kritisch niedrig.
                  Überprüfen Sie die Algorithmus-Parameter und Trainingsdaten.
                </AlertDescription>
              </Alert>
            )}

            {averageQualityScore >= 50 && averageQualityScore < 85 && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-yellow-800">
                  <strong>Optimierung empfohlen:</strong> Die KI-Qualität ist akzeptabel,
                  aber es gibt Raum für Verbesserungen. Fokussieren Sie sich auf die Hauptverbesserungsbereiche.
                </AlertDescription>
              </Alert>
            )}

            {averageQualityScore >= 85 && (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4" />
                <AlertDescription className="text-green-800">
                  <strong>Ausgezeichnete Leistung:</strong> Die KI-Qualität ist sehr gut.
                  Behalten Sie die aktuellen Einstellungen bei und sammeln Sie weiterhin Vergleichsdaten.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AIQualityAnalysis;