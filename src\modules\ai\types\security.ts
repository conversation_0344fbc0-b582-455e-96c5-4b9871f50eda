/**
 * AI Module Security Types
 * 
 * Defines security-related types for AI module access control,
 * API key management, and input validation.
 */

export interface AIPermission {
  id: string;
  name: string;
  description: string;
  requiredRoles: string[];
}

export interface AIFeatureAccess {
  featureId: string;
  hasAccess: boolean;
  requiredRole?: string;
  reason?: string;
}

export interface AISecurityContext {
  userId: number;
  username: string;
  roles: string[];
  permissions: AIPermission[];
  featureAccess: Record<string, AIFeatureAccess>;
}

export interface APIKeyConfig {
  id: string;
  name: string;
  service: string;
  keyPattern: RegExp;
  isRequired: boolean;
  environment: 'development' | 'production' | 'both';
}

export interface SecureAIRequest {
  userId: number;
  input: string;
  feature: string;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export interface AIInputValidationResult {
  isValid: boolean;
  sanitizedInput: string;
  violations: string[];
  riskLevel: SecurityRiskLevel;
}

export interface AISecurityAuditLog {
  id: string;
  userId: number;
  username: string;
  action: string;
  feature: string;
  input?: string;
  result: 'success' | 'denied' | 'error';
  reason?: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

// AI Feature Permissions
export const AI_PERMISSIONS: Record<string, AIPermission> = {
  RAG_QUERY: {
    id: 'rag_query',
    name: 'RAG Abfragen',
    description: 'Berechtigung für RAG-basierte Abfragen und Kontextsuche',
    requiredRoles: ['Besucher', 'Benutzer', 'Administrator']
  },
  CUTTING_OPTIMIZATION: {
    id: 'cutting_optimization',
    name: 'Schnittoptimierung',
    description: 'Berechtigung für KI-gestützte Schnittoptimierung',
    requiredRoles: ['Benutzer', 'Administrator']
  },
  INVENTORY_INTELLIGENCE: {
    id: 'inventory_intelligence',
    name: 'Lager-Intelligenz',
    description: 'Berechtigung für intelligente Lageranalyse und Vorhersagen',
    requiredRoles: ['Benutzer', 'Administrator']
  },
  PROCESS_OPTIMIZATION: {
    id: 'process_optimization',
    name: 'Prozessoptimierung',
    description: 'Berechtigung für Prozessanalyse und -optimierung',
    requiredRoles: ['Benutzer', 'Administrator']
  },
  PREDICTIVE_ANALYTICS: {
    id: 'predictive_analytics',
    name: 'Vorhersageanalyse',
    description: 'Berechtigung für KPI-Vorhersagen und Anomalieerkennung',
    requiredRoles: ['Benutzer', 'Administrator']
  },
  WAREHOUSE_OPTIMIZATION: {
    id: 'warehouse_optimization',
    name: 'Lageroptimierung',
    description: 'Berechtigung für Lagerplatz- und Routenoptimierung',
    requiredRoles: ['Benutzer', 'Administrator']
  },
  SUPPLY_CHAIN_OPTIMIZATION: {
    id: 'supply_chain_optimization',
    name: 'Lieferkettenoptimierung',
    description: 'Berechtigung für Lieferketten-Analyse und -Optimierung',
    requiredRoles: ['Administrator']
  },
  AUTOMATED_REPORTING: {
    id: 'automated_reporting',
    name: 'Automatisierte Berichte',
    description: 'Berechtigung für automatische Berichtsgenerierung',
    requiredRoles: ['Benutzer', 'Administrator']
  },
  AI_CONFIGURATION: {
    id: 'ai_configuration',
    name: 'KI-Konfiguration',
    description: 'Berechtigung für KI-Systemkonfiguration und -verwaltung',
    requiredRoles: ['Administrator']
  }
};

// API Key Configurations
export const API_KEY_CONFIGS: Record<string, APIKeyConfig> = {
  OPENROUTER: {
    id: 'openrouter',
    name: 'OpenRouter API',
    service: 'OpenRouter',
    keyPattern: /^sk-or-v1-[a-f0-9]{64}$/,
    isRequired: true,
    environment: 'both'
  }
};

// Security Risk Levels
export enum SecurityRiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high'
}

// Input Validation Rules
export interface InputValidationRule {
  name: string;
  pattern: RegExp;
  message: string;
  riskLevel: SecurityRiskLevel;
}

export const INPUT_VALIDATION_RULES: InputValidationRule[] = [
  {
    name: 'sql_injection',
    pattern: /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)|(['";])/i,
    message: 'Potentielle SQL-Injection erkannt',
    riskLevel: SecurityRiskLevel.HIGH
  },
  {
    name: 'script_injection',
    pattern: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    message: 'Script-Injection erkannt',
    riskLevel: SecurityRiskLevel.HIGH
  },
  {
    name: 'prompt_injection',
    pattern: /(\bignore\b.*\binstructions?\b)|(\bsystem\b.*\bprompt\b)|(\brole\b.*\bassistant\b)/i,
    message: 'Potentielle Prompt-Injection erkannt',
    riskLevel: SecurityRiskLevel.MEDIUM
  },
  {
    name: 'excessive_length',
    pattern: /^.{10000,}$/,
    message: 'Eingabe zu lang (max. 10.000 Zeichen)',
    riskLevel: SecurityRiskLevel.MEDIUM
  },
  {
    name: 'suspicious_patterns',
    pattern: /(\bpassword\b|\bcredit.?card\b|\bssn\b|\bsocial.?security\b)/i,
    message: 'Verdächtige Muster in der Eingabe erkannt',
    riskLevel: SecurityRiskLevel.MEDIUM
  }
];