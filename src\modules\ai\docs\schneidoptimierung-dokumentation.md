# Schneidoptimierung - Detaillierte Dokumentation

## Überblick

Die Schneidoptimierung ist ein komplexes System zur optimalen Aufteilung von Kabellängen auf verfügbare Trommeln. Das System implementiert verschiedene Algorithmen zur Minimierung von Verschnitt und Maximierung der Effizienz bei der Kabelproduktion.

## Grundlegende Konzepte

### Problemstellung

Das Schneidoptimierungsproblem ist eine Variante des **Bin-Packing-Problems**:
- **Gegeben**: Eine Liste von Bestellungen mit spezifischen Längenanforderungen
- **Gegeben**: Verfügbare Trommeln mit begrenzter Kapazität
- **Ziel**: Optimale Zuordnung der Schnitte zu Trommeln unter Minimierung von Verschnitt

### Kernziele

1. **Verschnittminimierung**: Reduzierung ungenutzter Kabellängen
2. **Effizienzmaximierung**: Optimale Ausnutzung der Trommelkapazität
3. **Zeitoptimierung**: Minimierung der Gesamtschnittzeit
4. **Constraint-Erfüllung**: Einhaltung aller technischen Beschränkungen

## Datenstrukturen

### CuttingOrder (Schnittauftrag)
```typescript
interface CuttingOrder {
  id: string;              // Eindeutige Auftragsnummer
  requiredLength: number;  // Benötigte Länge in Metern
  quantity: number;        // Anzahl der benötigten Schnitte
  priority: 'high' | 'medium' | 'low';  // Priorität
  material?: string;       // Materialtyp (optional)
  tolerance?: number;      // Toleranz in mm (optional)
}
```

### AvailableDrum (Verfügbare Trommel)
```typescript
interface AvailableDrum {
  id: string;              // Trommel-ID
  availableLength?: number; // Verfügbare Länge
  remainingLength?: number; // Verbleibende Länge
  material?: string;       // Materialtyp
  diameter?: number;       // Durchmesser
}
```

### CuttingPlan (Schnittplan)
```typescript
interface CuttingPlan {
  drumAllocations: DrumAllocation[];  // Zuordnungen pro Trommel
  cuttingSequence: CuttingStep[];     // Schnittsequenz
  totalWaste: number;                 // Gesamtverschnitt in Metern
  efficiency: number;                 // Effizienz (0-1)
  estimatedTime: number;              // Geschätzte Zeit in Sekunden
}
```

## Algorithmen

### 1. First-Fit Algorithmus

**Prinzip**: Platziert jeden Schnitt in der ersten Trommel, die genügend Platz hat.

**Vorteile**:
- Sehr schnelle Ausführung (O(n×m) Komplexität)
- Einfach zu implementieren und zu verstehen
- Gut für zeitkritische Operationen
- Geringe Speicheranforderungen

**Nachteile**:
- Oft nicht optimal
- Höheres Verschnittpotential
- Weniger effiziente Raumnutzung

**Implementierung**:
```typescript
private async firstFitOptimization(request: CuttingRequest): Promise<CuttingPlan> {
  // Initialisiere Trommelzuordnungen
  const drumAllocations: DrumAllocation[] = [];
  
  // Für jeden Auftrag
  for (const order of request.orders) {
    for (let i = 0; i < order.quantity; i++) {
      // Suche erste passende Trommel
      for (const allocation of drumAllocations) {
        if (this.canPlaceCut(allocation, order, drum, constraints)) {
          // Platziere Schnitt
          const cut = this.createCut(order, allocation);
          allocation.cuts.push(cut);
          allocation.remainingLength -= order.requiredLength;
          break;
        }
      }
    }
  }
}
```

### 2. Best-Fit Algorithmus

**Prinzip**: Platziert jeden Schnitt in der Trommel mit dem geringsten verbleibenden Platz, die noch ausreicht.

**Vorteile**:
- Bessere Raumausnutzung als First-Fit
- Reduzierter Verschnitt
- Gute Balance zwischen Geschwindigkeit und Optimierung
- Praktikabel für mittlere Problemgrößen

**Nachteile**:
- Langsamer als First-Fit
- Kann kleine, unbrauchbare Räume schaffen
- Komplexere Logik

**Implementierung**:
```typescript
private async bestFitOptimization(request: CuttingRequest): Promise<CuttingPlan> {
  // Für jeden Schnitt finde beste Trommel
  for (const order of request.orders) {
    let bestAllocation: DrumAllocation | null = null;
    let bestFit = Infinity;
    
    // Suche Trommel mit kleinstem verbleibendem Platz
    for (const allocation of drumAllocations) {
      if (this.canPlaceCut(allocation, order, drum, constraints)) {
        const wasteAfterCut = allocation.remainingLength - order.requiredLength;
        if (wasteAfterCut < bestFit) {
          bestFit = wasteAfterCut;
          bestAllocation = allocation;
        }
      }
    }
    
    // Platziere in bester Trommel
    if (bestAllocation) {
      const cut = this.createCut(order, bestAllocation);
      bestAllocation.cuts.push(cut);
      bestAllocation.remainingLength -= order.requiredLength;
    }
  }
}
```

### 3. Genetischer Algorithmus

**Prinzip**: Evolutionäre Optimierung durch Population von Lösungen, die über Generationen verbessert werden.

**Vorteile**:
- Findet oft optimale oder nahezu optimale Lösungen
- Kann komplexe Constraints handhaben
- Lernt aus Mustern und Erfahrungen
- Multi-objektive Optimierung möglich

**Nachteile**:
- Längere Berechnungszeit
- Komplex zu konfigurieren
- Konvergenz nicht garantiert
- Höhere Speicheranforderungen

**Konfiguration**:
```typescript
interface GeneticAlgorithmConfig {
  populationSize: number;        // Populationsgröße (50-100)
  generations: number;           // Anzahl Generationen (100-200)
  mutationRate: number;          // Mutationsrate (0.05-0.15)
  crossoverRate: number;         // Kreuzungsrate (0.7-0.9)
  elitismRate: number;          // Elitismus-Rate (0.1-0.3)
  tournamentSize: number;        // Turniergröße (3-7)
  convergenceThreshold: number;  // Konvergenzschwelle
  maxStagnantGenerations: number; // Max. stagnierende Generationen
}
```

**Chromosom-Repräsentation**:
- Jedes Chromosom ist ein Array von Zahlen
- Index = Auftragsnummer, Wert = Trommel-ID
- Beispiel: [1, 2, 1, 3, 2] bedeutet:
  - Auftrag 0 → Trommel 1
  - Auftrag 1 → Trommel 2
  - Auftrag 2 → Trommel 1
  - etc.

**Fitness-Funktion**:
```typescript
private calculateFitness(individual: Individual): number {
  const objectives = individual.objectives;
  
  // Multi-objektive Bewertung
  const wasteScore = objectives.wasteMinimization * 0.4;
  const efficiencyScore = objectives.efficiency * 0.4;
  const timeScore = objectives.timeOptimization * 0.2;
  
  return wasteScore + efficiencyScore + timeScore;
}
```

## Berechnungslogik

### Effizienzberechnung

**Formel**:
```
Effizienz = Genutzte_Länge / Gesamte_verfügbare_Länge
```

**Implementierung**:
```typescript
private calculateEfficiency(allocations: DrumAllocation[], drums: AvailableDrum[]): number {
  const totalCapacity = drums.reduce((sum, drum) => 
    sum + (drum.availableLength || drum.remainingLength || 0), 0);
  
  const totalUsed = allocations.reduce((sum, allocation) => {
    const drum = drums.find(d => d.id === allocation.drumId);
    const drumLength = drum ? (drum.availableLength || drum.remainingLength || 0) : 0;
    return sum + (drumLength - allocation.remainingLength);
  }, 0);

  return totalCapacity > 0 ? totalUsed / totalCapacity : 0;
}
```

### Verschnittberechnung

**Gesamtverschnitt**:
```
Verschnitt = Σ(Trommel_Länge - Genutzte_Länge)
```

**Verschnitt-Prozentsatz**:
```
Verschnitt% = (Gesamtverschnitt / Gesamtkapazität) × 100
```

**Implementierung**:
```typescript
calculateWastePercentage(drumLength: number, usedLength: number): number {
  if (drumLength <= 0) return 0;
  const waste = drumLength - usedLength;
  return (waste / drumLength) * 100;
}
```

### Auslastungsberechnung

**Pro Trommel**:
```
Auslastung = (Trommel_Länge - Verbleibende_Länge) / Trommel_Länge
```

**Implementierung**:
```typescript
private calculateUtilization(allocation: DrumAllocation, drum: AvailableDrum): number {
  const totalLength = drum.availableLength || drum.remainingLength || 0;
  const usedLength = totalLength - allocation.remainingLength;
  return totalLength > 0 ? usedLength / totalLength : 0;
}
```

### Zeitschätzung

**Schnittzeit pro Länge**:
```
Schnittzeit = Basiszeit + (Länge / 100) × Längenfaktor
```

**Implementierung**:
```typescript
private estimateCuttingTime(length: number): number {
  // 30 Sekunden Basiszeit + 10 Sekunden pro Meter
  return 30 + (length / 100) * 10;
}
```

## Multi-Objektive Optimierung (NSGA-II)

### Konzept

Der **NSGA-II (Non-dominated Sorting Genetic Algorithm II)** optimiert gleichzeitig mehrere Ziele:

1. **Verschnittminimierung** (minimize waste)
2. **Effizienzmaximierung** (maximize efficiency)
3. **Zeitoptimierung** (minimize time)

### Pareto-Front

**Definition**: Menge aller nicht-dominierten Lösungen, bei denen kein Ziel verbessert werden kann, ohne ein anderes zu verschlechtern.

**Dominanz-Relation**:
- Lösung A dominiert Lösung B, wenn:
  - A ist in allen Zielen mindestens so gut wie B
  - A ist in mindestens einem Ziel besser als B

### Crowding Distance

**Zweck**: Erhaltung der Diversität in der Population

**Berechnung**:
```typescript
private calculateCrowdingDistanceForRank(solutions: ParetoSolution[]): void {
  const numObjectives = this.config.objectives.length;
  
  // Für jedes Ziel
  for (let obj = 0; obj < numObjectives; obj++) {
    // Sortiere nach Zielwert
    solutions.sort((a, b) => 
      this.getObjectiveValue(a.individual, this.config.objectives[obj].name) -
      this.getObjectiveValue(b.individual, this.config.objectives[obj].name)
    );
    
    // Randlösungen erhalten unendliche Distanz
    solutions[0].crowdingDistance = Infinity;
    solutions[solutions.length - 1].crowdingDistance = Infinity;
    
    // Berechne Distanz für mittlere Lösungen
    const range = this.getObjectiveValue(solutions[solutions.length - 1].individual, objName) -
                  this.getObjectiveValue(solutions[0].individual, objName);
    
    for (let i = 1; i < solutions.length - 1; i++) {
      if (range > 0) {
        const distance = (this.getObjectiveValue(solutions[i + 1].individual, objName) -
                         this.getObjectiveValue(solutions[i - 1].individual, objName)) / range;
        solutions[i].crowdingDistance += distance;
      }
    }
  }
}
```

## Constraints (Beschränkungen)

### Technische Constraints

1. **Maximale Schnitte pro Trommel**:
   ```typescript
   if (constraints?.maxCutsPerDrum && allocation.cuts.length >= constraints.maxCutsPerDrum) {
     return false;
   }
   ```

2. **Minimale Verschnittlänge**:
   ```typescript
   if (constraints?.minWasteLength && 
       allocation.remainingLength - order.requiredLength < constraints.minWasteLength) {
     return false;
   }
   ```

3. **Materialkompatibilität**:
   ```typescript
   if (drum.material && order.material && drum.material !== order.material) {
     return false;
   }
   ```

### Geschäftslogik-Constraints

1. **Prioritätsbehandlung**: Hochpriorisierte Aufträge werden bevorzugt behandelt
2. **Teilerfüllung**: Optional können Aufträge teilweise erfüllt werden
3. **Toleranzen**: Berücksichtigung von Längen-Toleranzen

## Bewertungsmetriken

### Plan-Score

**Gesamtbewertung eines Schnittplans**:
```typescript
private calculatePlanScore(plan: CuttingPlan): number {
  const efficiencyScore = plan.efficiency * 100;           // 50% Gewichtung
  const wasteScore = Math.max(0, 100 - plan.totalWaste);   // 30% Gewichtung
  const timeScore = Math.max(0, 100 - (plan.estimatedTime / 3600)); // 20% Gewichtung

  return (efficiencyScore * 0.5) + (wasteScore * 0.3) + (timeScore * 0.2);
}
```

### Qualitätsindikatoren

1. **Verschnittrate**: Prozentsatz ungenutzten Materials
2. **Auslastungsgrad**: Durchschnittliche Trommelauslastung
3. **Fragmentierung**: Anzahl kleiner, unbrauchbarer Reststücke
4. **Zeiteffizienz**: Verhältnis von Schnittzeit zu Produktionszeit

## Optimierungsstrategien

### Preprocessing

1. **Sortierung der Aufträge**:
   - Nach Priorität (hoch → niedrig)
   - Nach Länge (lang → kurz für bessere Packung)
   - Nach Material (Gruppierung gleicher Materialien)

2. **Trommel-Vorsortierung**:
   - Nach verfügbarer Länge
   - Nach Materialtyp
   - Nach Durchmesser

### Heuristiken

1. **Längste-Zuerst**: Große Schnitte zuerst platzieren
2. **Material-Gruppierung**: Gleiche Materialien zusammenfassen
3. **Restlängen-Optimierung**: Kleine Schnitte für Restlängen reservieren

### Postprocessing

1. **Lokale Verbesserung**: Tausch von Schnitten zwischen Trommeln
2. **Defragmentierung**: Zusammenfassung kleiner Reststücke
3. **Sequenz-Optimierung**: Optimierung der Schnittsequenz

## Performance-Charakteristika

### Komplexitätsanalyse

| Algorithmus | Zeitkomplexität | Speicherkomplexität | Qualität |
|-------------|-----------------|---------------------|----------|
| First-Fit   | O(n×m)         | O(n+m)             | Gut      |
| Best-Fit    | O(n×m)         | O(n+m)             | Besser   |
| Genetisch   | O(g×p×n×m)     | O(p×n)             | Optimal  |

**Legende**:
- n = Anzahl Aufträge
- m = Anzahl Trommeln  
- g = Generationen
- p = Populationsgröße

### Skalierbarkeit

**Kleine Probleme** (≤ 20 Aufträge, ≤ 5 Trommeln):
- Alle Algorithmen < 1 Sekunde
- Genetischer Algorithmus empfohlen für optimale Ergebnisse

**Mittlere Probleme** (20-100 Aufträge, 5-15 Trommeln):
- First-Fit: < 1 Sekunde
- Best-Fit: 1-5 Sekunden
- Genetisch: 5-30 Sekunden

**Große Probleme** (> 100 Aufträge, > 15 Trommeln):
- First-Fit: 1-5 Sekunden
- Best-Fit: 5-30 Sekunden
- Genetisch: 30-300 Sekunden

## Anwendungsszenarien

### Produktionsplanung

**Tägliche Optimierung**:
- Verwendung: Best-Fit Algorithmus
- Ziel: Balance zwischen Geschwindigkeit und Qualität
- Typische Problemgröße: 50-200 Aufträge

**Strategische Planung**:
- Verwendung: Genetischer Algorithmus
- Ziel: Maximale Effizienz und Kosteneinsparung
- Zeitrahmen: Wöchentliche/monatliche Optimierung

### Echtzeitanwendungen

**Dringende Aufträge**:
- Verwendung: First-Fit Algorithmus
- Ziel: Schnelle Reaktionszeit
- Akzeptable Effizienz-Einbußen für Geschwindigkeit

## Konfiguration und Tuning

### Algorithmus-Parameter

**Genetischer Algorithmus**:
```typescript
const optimalConfig = {
  populationSize: 50,        // Für kleine Probleme
  populationSize: 100,       // Für große Probleme
  generations: 100,          // Standard
  mutationRate: 0.1,         // 10% Mutation
  crossoverRate: 0.8,        // 80% Kreuzung
  elitismRate: 0.2,         // 20% Elite
  tournamentSize: 5,         // Turniergröße
  convergenceThreshold: 0.001, // 0.1% Verbesserung
  maxStagnantGenerations: 20   // 20 Generationen ohne Verbesserung
};
```

**Multi-Objektive Gewichtung**:
```typescript
const objectives = [
  { name: 'wasteMinimization', weight: 0.4, minimize: false },
  { name: 'efficiency', weight: 0.4, minimize: false },
  { name: 'timeOptimization', weight: 0.2, minimize: false }
];
```

### Constraint-Konfiguration

```typescript
const constraints: CuttingConstraints = {
  maxCutsPerDrum: 10,           // Max. 10 Schnitte pro Trommel
  maxWasteLength: 50,           // Max. 50cm Verschnitt
  allowPartialFulfillment: true, // Teilerfüllung erlaubt
  prioritizeHighPriority: true   // Prioritäten beachten
};
```

## Monitoring und Analyse

### Key Performance Indicators (KPIs)

1. **Verschnittrate**: Ziel < 5%
2. **Durchschnittliche Effizienz**: Ziel > 90%
3. **Optimierungszeit**: Ziel < 30 Sekunden
4. **Erfolgsrate**: Anteil erfüllter Aufträge > 95%

### Qualitätsmetriken

```typescript
interface QualityMetrics {
  wastePercentage: number;      // Verschnitt-Prozentsatz
  averageUtilization: number;   // Durchschnittliche Auslastung
  fragmentationIndex: number;   // Fragmentierungsgrad
  timeEfficiency: number;       // Zeiteffizienz
  constraintViolations: number; // Constraint-Verletzungen
}
```

### Reporting

**Tägliche Berichte**:
- Verschnitt-Analyse
- Effizienz-Trends
- Algorithmus-Performance
- Constraint-Verletzungen

**Wöchentliche Berichte**:
- Optimierungspotentiale
- Materialverbrauch-Analyse
- Kosteneinsparungen
- Verbesserungsvorschläge

## Fazit

Die Schneidoptimierung implementiert ein hochentwickeltes System zur Lösung komplexer Bin-Packing-Probleme in der Kabelproduktion. Durch die Kombination verschiedener Algorithmen (First-Fit, Best-Fit, Genetisch) und multi-objektiver Optimierung (NSGA-II) können sowohl schnelle als auch hochoptimierte Lösungen bereitgestellt werden.

Die Flexibilität des Systems ermöglicht es, verschiedene Geschäftsanforderungen zu erfüllen - von zeitkritischen Echtzeitanwendungen bis hin zu strategischen Langzeitoptimierungen. Die umfassenden Metriken und Analysewerkzeuge bieten detaillierte Einblicke in die Optimierungsqualität und ermöglichen kontinuierliche Verbesserungen.

Durch die modulare Architektur und konfigurierbare Parameter kann das System an spezifische Produktionsanforderungen angepasst und kontinuierlich weiterentwickelt werden.