import React, { useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { useAuthContext } from '@/contexts/AuthContext';
import { UserRole } from '@/types/module';

export interface ModuleGuardProps {
  moduleId: string;
  requiredRoles: UserRole[];
  children: React.ReactNode;
  fallbackRoute?: string;
}

/**
 * ModuleGuard Component
 * 
 * Schützt Module vor nicht-autorisierten Benutzern basierend auf ihren Rollen.
 * Leitet automatisch zur UserLandingPage oder einer anderen Fallback-Route weiter,
 * wenn der Benutzer nicht die erforderlichen Rollen hat.
 * 
 * Requirements: 7.3, 7.4, 8.4
 * - WHEN ein Benutzer versucht, auf ein Modul zuzugreifen AND nicht die erforderliche Rolle hat
 *   THEN soll er zur UserLandingPage umgeleitet werden
 * - WHEN ein Benutzer die erforderliche Rolle hat
 *   THEN soll das Modul normal angezeigt werden
 */
export const ModuleGuard: React.FC<ModuleGuardProps> = ({ 
  moduleId, 
  requiredRoles, 
  children, 
  fallbackRoute = '/' 
}) => {
  const { user, isAuthenticated, isLoading } = useAuthContext();
  const navigate = useNavigate();

  useEffect(() => {
    // Warte bis der Authentifizierungsstatus geladen ist
    if (isLoading) return;

    // Prüfe ob Benutzer authentifiziert ist
    if (!isAuthenticated || !user) {
      console.log('🔒 ModuleGuard: Benutzer nicht authentifiziert - Weiterleitung zur Login-Seite');
      navigate({ to: '/login' });
      return;
    }

    // Ausführliches Debug-Logging für Benutzerrollen
    console.log('🔍 ModuleGuard: Benutzer-Objekt:', user);
    console.log('🔍 ModuleGuard: Benutzerrollen (user.roles):', user.roles);
    console.log('🔍 ModuleGuard: Typ der Benutzerrollen:', typeof user.roles);
    console.log('🔍 ModuleGuard: Ist Array?', Array.isArray(user.roles));
    
    // Prüfe ob Benutzer die erforderliche Rolle hat
    let userRole;
    if (Array.isArray(user.roles)) {
      console.log('🔍 ModuleGuard: Rollen-Array Länge:', user.roles.length);
      userRole = user.roles.length > 0 ? user.roles[0] : undefined;
    } else {
      userRole = user.roles;
    }
    
    console.log('🔍 ModuleGuard: Extrahierte userRole:', userRole);
    
    // Konvertiere userRole zu UserRole-Typ, wenn es einer der gültigen Werte ist
    const userRoleTyped = (userRole === 'Besucher' || userRole === 'Benutzer' || userRole === 'Administrator') 
      ? userRole as UserRole 
      : 'Besucher' as UserRole; // Fallback auf Besucher, wenn keine gültige Rolle
    
    console.log('🔍 ModuleGuard: Konvertierte userRoleTyped:', userRoleTyped);
    console.log('🔍 ModuleGuard: Erforderliche Rollen:', requiredRoles);
    
    const hasRequiredRole = requiredRoles.includes(userRoleTyped);
    console.log('🔍 ModuleGuard: Hat erforderliche Rolle?', hasRequiredRole);
    
    if (!hasRequiredRole) {
      console.log(`🚫 ModuleGuard: Zugriff auf Modul "${moduleId}" verweigert für Rolle "${userRoleTyped}". Erforderliche Rollen: ${requiredRoles.join(', ')}`);
      navigate({ to: fallbackRoute || '/' });
      return;
    }

    console.log(`✅ ModuleGuard: Zugriff auf Modul "${moduleId}" gewährt für Rolle "${userRoleTyped}"`);
  }, [isAuthenticated, isLoading, user, moduleId, requiredRoles, navigate, fallbackRoute]);

  // Zeige Loading-Spinner während der Authentifizierungsprüfung
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-lg">Modulzugriff wird geprüft...</span>
      </div>
    );
  }

  // Prüfe nochmals die Berechtigung vor dem Rendern
  const userRole = user ? (Array.isArray(user.roles) ? user.roles[0] : user.roles) : null;
  // Konvertiere userRole zu UserRole-Typ, wenn es einer der gültigen Werte ist
  const userRoleTyped = userRole && (userRole === 'Besucher' || userRole === 'Benutzer' || userRole === 'Administrator') 
    ? userRole as UserRole 
    : null;
  if (!isAuthenticated || !user || !userRoleTyped || !requiredRoles.includes(userRoleTyped)) {
    return null;
  }

  // Rendere Kinder nur wenn berechtigt
  return <>{children}</>;
};