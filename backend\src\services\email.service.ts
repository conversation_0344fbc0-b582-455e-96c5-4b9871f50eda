/**
 * E-Mail Service
 * 
 * Service für das Versenden von E-Mails mit Anhängen über SMTP.
 * Unterstützt sowohl HTML- als auch Text-E-Mails mit Dateianhängen.
 */

import nodemailer from 'nodemailer';
import fs from 'fs';
import path from 'path';
import Handlebars from 'handlebars';
// Temporäre Typen-Definitionen
interface EmailOptions {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  text?: string;
  html?: string;
  attachments?: EmailAttachment[];
  replyTo?: string;
  priority?: 'high' | 'normal' | 'low';
}

interface EmailAttachment {
  filename: string;
  content?: Buffer | string;
  path?: string;
  contentType?: string;
  cid?: string;
}

interface StoerungData {
  titel: string;
  schweregrad: 'niedrig' | 'mittel' | 'hoch' | 'kritisch';
  system: string;
  standort: string;
  melder: string;
  beschreibung: string;
  anhaenge?: EmailAttachment[];
}

/**
 * E-Mail-Konfiguration Interface
 */
export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}



/**
 * E-Mail Service Klasse
 */
export class EmailService {
  private transporter: nodemailer.Transporter;
  private config: EmailConfig;

  constructor(config?: EmailConfig) {
    // Standard-Konfiguration für Gmail/Outlook
    this.config = config || {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || ''
      }
    };

    // Erweiterte Transporter-Konfiguration mit Pool und Rate-Limiting
    const transporterConfig = {
      ...this.config,
      pool: true, // Verwende Connection-Pool
      maxConnections: 5, // Maximal 5 gleichzeitige Verbindungen
      maxMessages: 100, // Maximal 100 Nachrichten pro Verbindung
      rateDelta: 1000, // 1 Sekunde zwischen E-Mails
      rateLimit: 5, // Maximal 5 E-Mails pro rateDelta
      // Timeout-Konfiguration
      connectionTimeout: parseInt(process.env.EMAIL_TIMEOUT || '30000'),
      greetingTimeout: parseInt(process.env.EMAIL_TIMEOUT || '30000'),
      socketTimeout: parseInt(process.env.EMAIL_TIMEOUT || '30000')
    };

    this.transporter = nodemailer.createTransport(transporterConfig);
  }

  /**
   * Überprüft die Verbindung zum SMTP-Server
   */
  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('✅ SMTP-Verbindung erfolgreich');
      return true;
    } catch (error) {
      console.error('❌ SMTP-Verbindung fehlgeschlagen:', error);
      return false;
    }
  }

  /**
   * Sendet eine E-Mail
   */
  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      // Validiere Anhänge
      if (options.attachments) {
        for (const attachment of options.attachments) {
          if (attachment.path && !fs.existsSync(attachment.path)) {
            throw new Error(`Anhang nicht gefunden: ${attachment.path}`);
          }
        }
      }

      const mailOptions = {
        from: this.config.auth.user,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        cc: options.cc ? (Array.isArray(options.cc) ? options.cc.join(', ') : options.cc) : undefined,
        bcc: options.bcc ? (Array.isArray(options.bcc) ? options.bcc.join(', ') : options.bcc) : undefined,
        subject: options.subject,
        text: options.text,
        html: options.html,
        attachments: options.attachments?.map(att => ({
          filename: att.filename,
          path: att.path,
          contentType: att.contentType
        }))
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('✅ E-Mail erfolgreich gesendet:', result.messageId);
      return true;
    } catch (error) {
      console.error('❌ Fehler beim E-Mail-Versand:', error);
      return false;
    }
  }

  /**
   * Sendet eine Störungs-E-Mail mit vordefiniertem Template
   */
  async sendStoerungEmail(
    to: string | string[],
    stoerungData: {
      title: string;
      description: string;
      severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
      affected_system: string;
      location?: string;
      reported_by?: string;
    },
    attachments?: EmailAttachment[]
  ): Promise<boolean> {
    try {
      // Schweregrad-spezifische Konfiguration
      const severityConfig = {
        CRITICAL: { emoji: '🔴', text: 'KRITISCH', priority: 'high' as const, class: 'critical' },
        HIGH: { emoji: '🟠', text: 'HOCH', priority: 'high' as const, class: 'high' },
        MEDIUM: { emoji: '🟡', text: 'MITTEL', priority: 'normal' as const, class: 'medium' },
        LOW: { emoji: '🟢', text: 'NIEDRIG', priority: 'low' as const, class: 'low' }
      };

      const config = severityConfig[stoerungData.severity];
      const subject = `${config.emoji} Störungsmeldung: ${stoerungData.title}`;

      // Template-Daten vorbereiten
      const templateData = {
        title: stoerungData.title,
        description: stoerungData.description,
        affected_system: stoerungData.affected_system,
        location: stoerungData.location,
        reported_by: stoerungData.reported_by,
        severity_text: config.text,
        severity_class: config.class,
        severity_lower: config.class.toLowerCase(),
        has_attachments: attachments && attachments.length > 0,
        attachment_count: attachments ? attachments.length : 0,
        timestamp: new Date().toLocaleString('de-DE')
      };

      // Templates laden und kompilieren
      const htmlTemplate = await this.loadTemplate('stoerung-notification.html');
      const textTemplate = await this.loadTemplate('stoerung-notification.txt');

      const htmlContent = htmlTemplate ? htmlTemplate(templateData) : this.getFallbackHtmlContent(stoerungData, config, attachments);
      const textContent = textTemplate ? textTemplate(templateData) : this.getFallbackTextContent(stoerungData, config, attachments);

      return await this.sendEmail({
        to,
        subject,
        text: textContent,
        html: htmlContent,
        attachments
      });
    } catch (error) {
      console.error('Fehler beim Senden der Störungs-E-Mail:', error);
      return false;
    }
  }

  /**
   * Lädt und kompiliert ein E-Mail-Template
   */
  private async loadTemplate(templateName: string): Promise<HandlebarsTemplateDelegate | null> {
    try {
      const templatePath = path.join(process.env.EMAIL_TEMPLATES_DIR || './templates', templateName);
      
      if (!fs.existsSync(templatePath)) {
        console.warn(`E-Mail-Template nicht gefunden: ${templatePath}`);
        return null;
      }

      const templateContent = fs.readFileSync(templatePath, 'utf-8');
      return Handlebars.compile(templateContent);
    } catch (error) {
      console.error(`Fehler beim Laden des Templates ${templateName}:`, error);
      return null;
    }
  }

  /**
   * Fallback HTML-Inhalt, falls Template nicht verfügbar
   */
  private getFallbackHtmlContent(
    stoerungData: any,
    config: any,
    attachments?: EmailAttachment[]
  ): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: ${this.getSeverityColor(stoerungData.severity)}; color: white; padding: 20px; text-align: center;">
          <h1>${config.emoji} Störungsmeldung</h1>
          <p style="margin: 0; font-size: 18px; font-weight: bold;">${stoerungData.title}</p>
          <span style="background-color: rgba(255,255,255,0.2); padding: 5px 10px; border-radius: 15px; font-size: 12px; margin-top: 10px; display: inline-block;">
            ${config.text}
          </span>
        </div>
        
        <div style="padding: 20px; background-color: #f9f9f9;">
          <h2 style="color: #333; margin-top: 0;">Details zur Störung</h2>
          
          <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <tr>
              <td style="padding: 10px; background-color: white; border: 1px solid #ddd; font-weight: bold;">Betroffenes System:</td>
              <td style="padding: 10px; background-color: white; border: 1px solid #ddd;">${stoerungData.affected_system}</td>
            </tr>
            ${stoerungData.location ? `
            <tr>
              <td style="padding: 10px; background-color: white; border: 1px solid #ddd; font-weight: bold;">Standort:</td>
              <td style="padding: 10px; background-color: white; border: 1px solid #ddd;">${stoerungData.location}</td>
            </tr>` : ''}
            ${stoerungData.reported_by ? `
            <tr>
              <td style="padding: 10px; background-color: white; border: 1px solid #ddd; font-weight: bold;">Gemeldet von:</td>
              <td style="padding: 10px; background-color: white; border: 1px solid #ddd;">${stoerungData.reported_by}</td>
            </tr>` : ''}
            <tr>
              <td style="padding: 10px; background-color: white; border: 1px solid #ddd; font-weight: bold;">Schweregrad:</td>
              <td style="padding: 10px; background-color: white; border: 1px solid #ddd;">${config.text}</td>
            </tr>
          </table>
          
          <div style="background-color: white; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #333;">Beschreibung:</h3>
            <p style="line-height: 1.6; color: #555;">${stoerungData.description}</p>
          </div>
          
          ${attachments && attachments.length > 0 ? `
          <div style="background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #1976d2;">📎 Anhänge (${attachments.length})</h3>
            <p style="margin: 0; color: #555;">Diese E-Mail enthält ${attachments.length} Anhang(e) mit weiteren Informationen zur Störung.</p>
          </div>` : ''}
        </div>
        
        <div style="background-color: #333; color: white; padding: 15px; text-align: center; font-size: 12px;">
          <p style="margin: 0;">Diese Störungsmeldung wurde automatisch vom SFM Dashboard-System generiert.</p>
          <p style="margin: 5px 0 0 0;">Zeitstempel: ${new Date().toLocaleString('de-DE')}</p>
        </div>
      </div>
    `;
  }

  /**
   * Fallback Text-Inhalt, falls Template nicht verfügbar
   */
  private getFallbackTextContent(
    stoerungData: any,
    config: any,
    attachments?: EmailAttachment[]
  ): string {
    return `
🚨 STÖRUNGSMELDUNG - ${config.text}
=====================================

Titel: ${stoerungData.title}

SYSTEM-INFORMATIONEN:
--------------------
Betroffenes System: ${stoerungData.affected_system}
${stoerungData.location ? `Standort: ${stoerungData.location}\n` : ''}${stoerungData.reported_by ? `Gemeldet von: ${stoerungData.reported_by}\n` : ''}Schweregrad: ${config.text}

BESCHREIBUNG:
------------
${stoerungData.description}

${attachments && attachments.length > 0 ? `ANHÄNGE:\n--------\nDiese E-Mail enthält ${attachments.length} Anhang(e) mit weiteren Informationen zur Störung.\n\n` : ''}=====================================
Diese Störungsmeldung wurde automatisch vom SFM Dashboard-System generiert.
Bei Fragen wenden Sie sich bitte an das zuständige Support-Team.

Gesendet am: ${new Date().toLocaleString('de-DE')}
    `.trim();
  }

  /**
   * Hilfsmethode: Bestimmt die Farbe basierend auf dem Schweregrad
   */
  private getSeverityColor(severity: string): string {
    const colors = {
      CRITICAL: '#6f42c1', // Lila
      HIGH: '#dc3545',     // Rot
      MEDIUM: '#fd7e14',   // Orange
      LOW: '#ffc107'       // Gelb
    };
    return colors[severity as keyof typeof colors] || '#6c757d';
  }

  /**
   * Schließt die Transporter-Verbindung
   */
  close(): void {
    this.transporter.close();
  }
}

// Singleton-Instanz für den Service
let emailServiceInstance: EmailService | null = null;

/**
 * Gibt die Singleton-Instanz des E-Mail-Services zurück
 */
export function getEmailService(): EmailService {
  if (!emailServiceInstance) {
    emailServiceInstance = new EmailService();
  }
  return emailServiceInstance;
}

export default EmailService;