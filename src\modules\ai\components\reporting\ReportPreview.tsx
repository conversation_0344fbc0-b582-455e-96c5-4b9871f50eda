/**
 * Report Preview Component
 * 
 * Component for previewing report templates and generating reports
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import {
  FileText,
  Download,
  Play,
  Eye,
  Calendar,
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Table,
  TrendingUp,
  Lightbulb
} from 'lucide-react';
import type {
  ReportTemplate,
  GeneratedReport,
  ReportGenerationRequest,
  ReportExportOptions,
  ReportPreview as ReportPreviewData
} from '@/types/reporting';
import type { ReportingService } from '../../services/reporting/ReportingService';

interface ReportPreviewProps {
  template: ReportTemplate | null;
  reportingService: ReportingService | null;
  onGenerateReport: (request: ReportGenerationRequest) => Promise<GeneratedReport>;
  generating: boolean;
}

/**
 * Report Preview Component
 */
export const ReportPreview: React.FC<ReportPreviewProps> = ({
  template,
  reportingService,
  onGenerateReport,
  generating
}) => {
  // State for preview configuration
  const [previewData, setPreviewData] = useState<ReportPreviewData | null>(null);
  const [generatedReport, setGeneratedReport] = useState<GeneratedReport | null>(null);
  const [loadingPreview, setLoadingPreview] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Generation options
  const [timeRange, setTimeRange] = useState<{ start: Date; end: Date }>(() => {
    const end = new Date();
    const start = new Date();
    start.setMonth(start.getMonth() - 1);
    return { start, end };
  });
  const [format, setFormat] = useState<'pdf' | 'excel' | 'html' | 'json'>('pdf');
  const [includeInsights, setIncludeInsights] = useState(true);
  const [includeRecommendations, setIncludeRecommendations] = useState(true);
  const [generationProgress, setGenerationProgress] = useState(0);

  /**
   * Load preview data when template changes
   */
  useEffect(() => {
    if (template && reportingService) {
      loadPreviewData();
    } else {
      setPreviewData(null);
      setGeneratedReport(null);
    }
  }, [template, reportingService]);

  /**
   * Load preview data for the template
   */
  const loadPreviewData = useCallback(async () => {
    if (!template || !reportingService) return;

    try {
      setLoadingPreview(true);
      setError(null);

      // Mock preview data generation
      const mockPreviewData: ReportPreviewData = {
        templateId: template.id,
        previewData: template.sections.map(section => ({
          sectionId: section.id,
          title: section.title,
          type: section.type,
          data: generateMockData(section.type),
          summary: {
            total: Math.floor(Math.random() * 1000),
            average: Math.floor(Math.random() * 100),
            trend: Math.random() > 0.5 ? 'up' : 'down',
            change: Math.floor(Math.random() * 20) - 10
          }
        })),
        estimatedSize: Math.floor(Math.random() * 5000) + 1000, // KB
        estimatedGenerationTime: Math.floor(Math.random() * 30) + 5, // seconds
        dataSourcesUsed: [...new Set(template.sections.map(s => s.dataSource))]
      };

      setPreviewData(mockPreviewData);
    } catch (err) {
      console.error('Failed to load preview data:', err);
      setError('Fehler beim Laden der Vorschau');
    } finally {
      setLoadingPreview(false);
    }
  }, [template, reportingService]);

  /**
   * Generate mock data for preview
   */
  const generateMockData = (type: string) => {
    const dataPoints = Math.floor(Math.random() * 20) + 5;
    return Array.from({ length: dataPoints }, (_, i) => ({
      id: i,
      label: `Datenpunkt ${i + 1}`,
      value: Math.floor(Math.random() * 100),
      date: new Date(Date.now() - (dataPoints - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    }));
  };

  /**
   * Handle report generation
   */
  const handleGenerateReport = useCallback(async () => {
    if (!template) return;

    try {
      setError(null);
      setGenerationProgress(0);

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + Math.random() * 20;
        });
      }, 500);

      const request: ReportGenerationRequest = {
        templateId: template.id,
        timeRange,
        format,
        includeInsights,
        includeRecommendations
      };

      const report = await onGenerateReport(request);
      setGeneratedReport(report);
      setGenerationProgress(100);

      clearInterval(progressInterval);
    } catch (err) {
      console.error('Failed to generate report:', err);
      setError('Fehler beim Generieren des Berichts');
      setGenerationProgress(0);
    }
  }, [template, timeRange, format, includeInsights, includeRecommendations, onGenerateReport]);

  /**
   * Handle report export
   */
  const handleExportReport = useCallback((report: GeneratedReport, exportFormat?: string) => {
    const exportData = {
      ...report,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${report.title.replace(/[^a-zA-Z0-9]/g, '-')}.${exportFormat || 'json'}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, []);

  /**
   * Get section type icon
   */
  const getSectionTypeIcon = (type: string) => {
    switch (type) {
      case 'chart':
        return <BarChart3 className="h-4 w-4" />;
      case 'table':
        return <Table className="h-4 w-4" />;
      case 'kpi':
        return <TrendingUp className="h-4 w-4" />;
      case 'text':
        return <FileText className="h-4 w-4" />;
      case 'insights':
        return <Lightbulb className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  if (!template) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Keine Vorlage ausgewählt
          </h3>
          <p className="text-gray-600">
            Wählen Sie eine Vorlage aus der Liste aus, um eine Vorschau zu sehen.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Template Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            {template.name}
          </CardTitle>
          <CardDescription>
            {template.description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="font-medium text-gray-600">Typ</p>
              <p>{template.type}</p>
            </div>
            <div>
              <p className="font-medium text-gray-600">Abteilung</p>
              <p>{template.department || 'Alle'}</p>
            </div>
            <div>
              <p className="font-medium text-gray-600">Format</p>
              <p>{template.format.toUpperCase()}</p>
            </div>
            <div>
              <p className="font-medium text-gray-600">Abschnitte</p>
              <p>{template.sections.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generation Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Generierungsoptionen
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Startdatum</Label>
              <DatePicker
                value={timeRange.start}
                onChange={(date) => date && setTimeRange(prev => ({ ...prev, start: date }))}
              />
            </div>
            <div className="space-y-2">
              <Label>Enddatum</Label>
              <DatePicker
                value={timeRange.end}
                onChange={(date) => date && setTimeRange(prev => ({ ...prev, end: date }))}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>Ausgabeformat</Label>
            <Select value={format} onValueChange={(value: any) => setFormat(value)}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pdf">PDF</SelectItem>
                <SelectItem value="excel">Excel</SelectItem>
                <SelectItem value="html">HTML</SelectItem>
                <SelectItem value="json">JSON</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Switch
                id="includeInsights"
                checked={includeInsights}
                onCheckedChange={setIncludeInsights}
              />
              <Label htmlFor="includeInsights">KI-Erkenntnisse einschließen</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="includeRecommendations"
                checked={includeRecommendations}
                onCheckedChange={setIncludeRecommendations}
              />
              <Label htmlFor="includeRecommendations">Empfehlungen einschließen</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preview Data */}
      {loadingPreview ? (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p>Lade Vorschau...</p>
          </CardContent>
        </Card>
      ) : previewData ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Eye className="h-5 w-5 mr-2" />
              Berichtsvorschau
            </CardTitle>
            <CardDescription>
              Geschätzte Größe: {(previewData.estimatedSize / 1024).toFixed(1)} MB |
              Geschätzte Generierungszeit: {previewData.estimatedGenerationTime}s
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Sections Preview */}
            <div className="space-y-3">
              {previewData.previewData.map((section, index) => (
                <div key={section.sectionId} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium flex items-center">
                      {getSectionTypeIcon(section.type)}
                      <span className="ml-2">{section.title}</span>
                    </h4>
                    <Badge variant="outline">
                      {section.data.length} Datenpunkte
                    </Badge>
                  </div>

                  {section.summary && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Gesamt</p>
                        <p className="font-medium">{section.summary.total?.toLocaleString('de-DE')}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Durchschnitt</p>
                        <p className="font-medium">{section.summary.average?.toLocaleString('de-DE')}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Trend</p>
                        <p className={`font-medium ${section.summary.trend === 'up' ? 'text-green-600' :
                            section.summary.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                          }`}>
                          {section.summary.trend === 'up' ? '↗ Steigend' :
                            section.summary.trend === 'down' ? '↘ Fallend' : '→ Stabil'}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-600">Änderung</p>
                        <p className={`font-medium ${(section.summary.change ?? 0) > 0 ? 'text-green-600' :
                            (section.summary.change ?? 0) < 0 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                          {section.summary.change !== undefined ?
                            `${section.summary.change > 0 ? '+' : ''}${section.summary.change}%` :
                            'N/A'
                          }
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Data Sources */}
            <div>
              <h4 className="font-medium mb-2">Verwendete Datenquellen</h4>
              <div className="flex flex-wrap gap-2">
                {previewData.dataSourcesUsed.map((source) => (
                  <Badge key={source} variant="secondary">
                    {source}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      ) : null}

      {/* Error Display */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Generation Progress */}
      {generating && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Bericht wird generiert...</span>
                <span className="text-sm text-gray-600">{Math.round(generationProgress)}%</span>
              </div>
              <Progress value={generationProgress} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Generated Report */}
      {generatedReport && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
              Bericht erfolgreich generiert
            </CardTitle>
            <CardDescription>
              {generatedReport.title} - {generatedReport.generatedAt.toLocaleString('de-DE')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="font-medium text-gray-600">Datenpunkte</p>
                <p>{generatedReport.metadata.dataPoints.toLocaleString('de-DE')}</p>
              </div>
              <div>
                <p className="font-medium text-gray-600">Verarbeitungszeit</p>
                <p>{generatedReport.metadata.processingTime}ms</p>
              </div>
              <div>
                <p className="font-medium text-gray-600">Erkenntnisse</p>
                <p>{generatedReport.insights.length}</p>
              </div>
              <div>
                <p className="font-medium text-gray-600">Empfehlungen</p>
                <p>{generatedReport.recommendations.length}</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                onClick={() => handleExportReport(generatedReport, 'pdf')}
                size="sm"
              >
                <Download className="h-4 w-4 mr-2" />
                Als PDF exportieren
              </Button>
              <Button
                variant="outline"
                onClick={() => handleExportReport(generatedReport, 'excel')}
                size="sm"
              >
                <Download className="h-4 w-4 mr-2" />
                Als Excel exportieren
              </Button>
              <Button
                variant="outline"
                onClick={() => handleExportReport(generatedReport, 'json')}
                size="sm"
              >
                <Download className="h-4 w-4 mr-2" />
                Als JSON exportieren
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Generate Button */}
      <div className="flex justify-center">
        <Button
          onClick={handleGenerateReport}
          disabled={generating || !previewData}
          size="lg"
        >
          {generating ? (
            <>
              <Clock className="h-5 w-5 mr-2 animate-spin" />
              Generiere Bericht...
            </>
          ) : (
            <>
              <Play className="h-5 w-5 mr-2" />
              Bericht generieren
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default ReportPreview;