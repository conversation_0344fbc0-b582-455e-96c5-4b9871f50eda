/**
 * Inventory Service
 * 
 * Service für Bestandsabfragen aus der bestand200-Tabelle
 * Implementiert Filterlogik für verfügbare Trommeln basierend auf
 * Material, Lagerdauer, Verfügbarkeit und Datum
 */

import { InventoryDrum } from '../types';

export interface InventoryQueryOptions {
  materials?: string[]; // Filter nach Material-Codes
  selectionDate?: Date; // Datum für aufnahmeDatum-Filter
  excludeAssignedDrums?: boolean; // Trommeln mit Liefernummer ausschließen
  sortByAge?: boolean; // Nach Lagerdauer sortieren (älteste zuerst)
  limit?: number; // Maximale Anzahl Ergebnisse
}

export interface InventoryQueryResult {
  drums: InventoryDrum[];
  totalCount: number;
  queryDate: Date;
  appliedFilters: string[];
}

export class InventoryService {
  private apiBaseUrl: string;

  constructor(apiBaseUrl: string = '/api') {
    this.apiBaseUrl = apiBaseUrl;
  }

  /**
   * Abfrage verfügbarer Trommeln aus der bestand200-Tabelle
   * Wendet die spezifizierten Filter- und Auswahlkriterien an
   */
  async getAvailableDrums(options: InventoryQueryOptions = {}): Promise<InventoryQueryResult> {
    try {
      const queryParams = this.buildQueryParams(options);
      const response = await fetch(`${this.apiBaseUrl}/inventory/drums?${queryParams}`);
      
      if (!response.ok) {
        throw new Error(`Inventory query failed: ${response.statusText}`);
      }

      const data = await response.json();
      return this.processInventoryData(data, options);
    } catch (error) {
      console.error('Error querying inventory:', error);
      throw error;
    }
  }

  /**
   * Suche nach spezifischen Trommeln anhand von Charge-Nummern
   */
  async getDrumsByCharges(chargeNumbers: string[]): Promise<InventoryDrum[]> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/inventory/drums/by-charges`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ charges: chargeNumbers }),
      });

      if (!response.ok) {
        throw new Error(`Charge lookup failed: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error looking up drums by charges:', error);
      throw error;
    }
  }

  /**
   * Filtere verfügbare Trommeln für eine spezifische Lieferung
   * Implementiert die Geschäftslogik für Trommelauswahl
   */
  async getAvailableDrumsForDelivery(
    materials: string[],
    selectionDate: Date,
    options: Partial<InventoryQueryOptions> = {}
  ): Promise<InventoryDrum[]> {
    const queryOptions: InventoryQueryOptions = {
      materials,
      selectionDate,
      excludeAssignedDrums: true, // Nur verfügbare Trommeln
      sortByAge: true, // Älteste Trommeln bevorzugen
      ...options,
    };

    const result = await this.getAvailableDrums(queryOptions);
    return result.drums;
  }

  /**
   * Erstelle Query-Parameter für die API-Anfrage
   */
  private buildQueryParams(options: InventoryQueryOptions): string {
    const params = new URLSearchParams();

    if (options.materials && options.materials.length > 0) {
      params.append('materials', options.materials.join(','));
    }

    if (options.selectionDate) {
      // Format: YYYY-MM-DD für aufnahmeDatum-Filter
      const dateStr = options.selectionDate.toISOString().split('T')[0];
      params.append('aufnahmeDatum', dateStr);
    }

    if (options.excludeAssignedDrums) {
      params.append('excludeAssigned', 'true');
    }

    if (options.sortByAge) {
      params.append('sortBy', 'dauer');
      params.append('sortOrder', 'desc'); // Längste Lagerdauer zuerst
    }

    if (options.limit) {
      params.append('limit', options.limit.toString());
    }

    return params.toString();
  }

  /**
   * Verarbeite und validiere die Inventardaten
   */
  private processInventoryData(
    rawData: any,
    options: InventoryQueryOptions
  ): InventoryQueryResult {
    const drums: InventoryDrum[] = (rawData.drums || []).map((drum: any) => ({
      id: drum.id,
      material: drum.Material,
      charge: drum.Charge,
      dauer: drum.Dauer,
      lagereinheitentyp: drum.Lagereinheitentyp,
      lieferung: drum.Lieferung,
      gesamtbestand: drum.Gesamtbestand,
      aufnahmeDatum: drum.aufnahmeDatum,
      aufnahmeZeit: drum.aufnahmeZeit,
    }));

    // Zusätzliche Filterung auf Client-Seite falls nötig
    const filteredDrums = this.applyClientSideFilters(drums, options);

    const appliedFilters = this.getAppliedFilters(options);

    return {
      drums: filteredDrums,
      totalCount: filteredDrums.length,
      queryDate: new Date(),
      appliedFilters,
    };
  }

  /**
   * Zusätzliche Client-seitige Filter
   */
  private applyClientSideFilters(
    drums: InventoryDrum[],
    options: InventoryQueryOptions
  ): InventoryDrum[] {
    let filtered = [...drums];

    // Filter: Nur Trommeln ohne Liefernummer (verfügbar)
    if (options.excludeAssignedDrums) {
      filtered = filtered.filter(drum => !drum.lieferung || drum.lieferung.trim() === '');
    }

    // Filter: Nur positive Bestände
    filtered = filtered.filter(drum => drum.gesamtbestand && drum.gesamtbestand > 0);

    // Sortierung nach Lagerdauer (älteste zuerst)
    if (options.sortByAge) {
      filtered.sort((a, b) => (b.dauer || 0) - (a.dauer || 0));
    }

    return filtered;
  }

  /**
   * Erstelle Liste der angewendeten Filter für Dokumentation
   */
  private getAppliedFilters(options: InventoryQueryOptions): string[] {
    const filters: string[] = [];

    if (options.materials && options.materials.length > 0) {
      filters.push(`Material: ${options.materials.join(', ')}`);
    }

    if (options.selectionDate) {
      filters.push(`Aufnahmedatum: ${options.selectionDate.toLocaleDateString('de-DE')}`);
    }

    if (options.excludeAssignedDrums) {
      filters.push('Nur verfügbare Trommeln (ohne Liefernummer)');
    }

    if (options.sortByAge) {
      filters.push('Sortiert nach Lagerdauer (älteste zuerst)');
    }

    if (options.limit) {
      filters.push(`Limit: ${options.limit}`);
    }

    return filters;
  }

  /**
   * Validiere Trommel-Verfügbarkeit für eine bestimmte Menge
   */
  validateDrumAvailability(
    drum: InventoryDrum,
    requiredLength: number
  ): { available: boolean; reason?: string } {
    if (!drum.gesamtbestand || drum.gesamtbestand <= 0) {
      return { available: false, reason: 'Kein Bestand verfügbar' };
    }

    if (drum.gesamtbestand < requiredLength) {
      return { 
        available: false, 
        reason: `Unzureichender Bestand: ${drum.gesamtbestand}m verfügbar, ${requiredLength}m benötigt` 
      };
    }

    if (drum.lieferung && drum.lieferung.trim() !== '') {
      return { available: false, reason: 'Trommel bereits einer Lieferung zugeordnet' };
    }

    return { available: true };
  }
}

// Singleton-Instanz für die Anwendung
export const inventoryService = new InventoryService();