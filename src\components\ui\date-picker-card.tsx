import React from 'react';
import { DateRange } from 'react-day-picker';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import { DatePicker } from '@/components/ui/date-picker';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface DatePickerCardProps {
  // Common props
  title?: string;
  description?: string;
  className?: string;
  
  // Range picker props
  rangeValue?: DateRange;
  onRangeChange?: (range: DateRange | undefined) => void;
  
  // Single picker props  
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  
  // DatePicker specific props
  label?: string;
  placeholder?: string;
  calendarTitle?: string;
  calendarDescription?: string;
}

export function DatePickerCard({
  title,
  description,
  className,
  rangeValue,
  onRangeChange,
  value,
  onChange,
  label,
  placeholder,
  calendarTitle,
  calendarDescription
}: DatePickerCardProps) {
  const isRangePicker = !!onRangeChange;
  
  return (
    <Card className={className}>
      {(title || description) && (
        <CardHeader className="pb-4">
          {title && <CardTitle className="text-lg">{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      <CardContent>
        {isRangePicker ? (
          <DateRangePicker
            value={rangeValue}
            onChange={onRangeChange}
            label={label}
            title={calendarTitle}
            description={calendarDescription}
          />
        ) : (
          <DatePicker
            value={value}
            onChange={onChange}
            label={label}
            placeholder={placeholder}
            title={calendarTitle}
            description={calendarDescription}
          />
        )}
      </CardContent>
    </Card>
  );
}