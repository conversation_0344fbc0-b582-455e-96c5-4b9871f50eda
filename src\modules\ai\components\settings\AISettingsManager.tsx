/**
 * AI Settings Manager Component
 * 
 * Manages AI module configuration and settings
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Settings,
  Save,
  RotateCcw,
  AlertCircle,
  CheckCircle,
  Database,
  Zap,
  Shield,
  Clock
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

export interface AIModuleSettings {
  // Vector Database Settings
  vectorDatabase: {
    dimensions: number;
    similarityThreshold: number;
    maxResults: number;
    enableCache: boolean;
    cacheTTL: number;
  };

  // Performance Settings
  performance: {
    maxConcurrentOperations: number;
    defaultTimeout: number;
    enableRetries: boolean;
    maxRetries: number;
    enableQueueing: boolean;
    queueMaxSize: number;
  };

  // Security Settings
  security: {
    enableRateLimit: boolean;
    rateLimitRequests: number;
    rateLimitWindow: number;
    enableInputValidation: boolean;
    enableAuditLogging: boolean;
  };

  // Service-specific Settings
  services: {
    embedding: {
      model: string;
      batchSize: number;
      enableCache: boolean;
    };
    rag: {
      contextLength: number;
      enableSourceCitation: boolean;
      confidenceThreshold: number;
    };
    optimization: {
      enableAdvancedAlgorithms: boolean;
      maxOptimizationTime: number;
      enableParallelProcessing: boolean;
    };
  };
}

const defaultSettings: AIModuleSettings = {
  vectorDatabase: {
    dimensions: 1536,
    similarityThreshold: 0.7,
    maxResults: 10,
    enableCache: true,
    cacheTTL: 3600000
  },
  performance: {
    maxConcurrentOperations: 5,
    defaultTimeout: 30000,
    enableRetries: true,
    maxRetries: 3,
    enableQueueing: true,
    queueMaxSize: 100
  },
  security: {
    enableRateLimit: true,
    rateLimitRequests: 100,
    rateLimitWindow: 3600000,
    enableInputValidation: true,
    enableAuditLogging: true
  },
  services: {
    embedding: {
      model: 'text-embedding-3-small',
      batchSize: 100,
      enableCache: true
    },
    rag: {
      contextLength: 8000,
      enableSourceCitation: true,
      confidenceThreshold: 0.8
    },
    optimization: {
      enableAdvancedAlgorithms: true,
      maxOptimizationTime: 60000,
      enableParallelProcessing: true
    }
  }
};

interface AISettingsManagerProps {
  onSettingsChange?: (settings: AIModuleSettings) => void;
  initialSettings?: Partial<AIModuleSettings>;
}

export const AISettingsManager: React.FC<AISettingsManagerProps> = ({
  onSettingsChange,
  initialSettings
}) => {
  const [settings, setSettings] = useState<AIModuleSettings>({
    ...defaultSettings,
    ...initialSettings
  });
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [selectedTab, setSelectedTab] = useState('vector');

  // Track changes
  useEffect(() => {
    const hasChanges = JSON.stringify(settings) !== JSON.stringify({
      ...defaultSettings,
      ...initialSettings
    });
    setHasChanges(hasChanges);
  }, [settings, initialSettings]);

  const updateSettings = (path: string, value: any) => {
    setSettings(prev => {
      const newSettings = { ...prev };
      const keys = path.split('.');
      let current: any = newSettings;

      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newSettings;
    });
  };

  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus('idle');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      onSettingsChange?.(settings);
      setSaveStatus('success');
      setHasChanges(false);

      // Reset status after 3 seconds
      setTimeout(() => setSaveStatus('idle'), 3000);
    } catch (error) {
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setSettings({
      ...defaultSettings,
      ...initialSettings
    });
    setSaveStatus('idle');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Settings className="h-6 w-6 text-indigo-600" />
            KI-Modul Einstellungen
          </h2>
          <p className="text-gray-600 mt-1">
            Konfiguration und Optimierung der KI-Services
          </p>
        </div>

        <div className="flex items-center gap-3">
          {saveStatus === 'success' && (
            <Badge variant="default" className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              Gespeichert
            </Badge>
          )}
          {saveStatus === 'error' && (
            <Badge variant="destructive">
              <AlertCircle className="h-3 w-3 mr-1" />
              Fehler
            </Badge>
          )}

          <Button
            variant="outline"
            onClick={handleReset}
            disabled={!hasChanges || isSaving}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Zurücksetzen
          </Button>

          <Button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Speichern...' : 'Speichern'}
          </Button>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="vector">Vector DB</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="security">Sicherheit</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
        </TabsList>

        {/* Vector Database Settings */}
        <TabsContent value="vector" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5 text-blue-600" />
                Vector Database Konfiguration
              </CardTitle>
              <CardDescription>
                Einstellungen für die Vektor-Datenbank und Ähnlichkeitssuche
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="dimensions">Vector Dimensionen</Label>
                  <Input
                    id="dimensions"
                    type="number"
                    value={settings.vectorDatabase.dimensions}
                    onChange={(e) => updateSettings('vectorDatabase.dimensions', parseInt(e.target.value))}
                    min={1}
                    max={4096}
                  />
                  <p className="text-sm text-gray-500">
                    Anzahl der Dimensionen für Embedding-Vektoren (Standard: 1536)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxResults">Max. Suchergebnisse</Label>
                  <Input
                    id="maxResults"
                    type="number"
                    value={settings.vectorDatabase.maxResults}
                    onChange={(e) => updateSettings('vectorDatabase.maxResults', parseInt(e.target.value))}
                    min={1}
                    max={100}
                  />
                  <p className="text-sm text-gray-500">
                    Maximale Anzahl der Suchergebnisse pro Abfrage
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label>Ähnlichkeitsschwelle: {settings.vectorDatabase.similarityThreshold}</Label>
                  <input
                    type="range"
                    value={settings.vectorDatabase.similarityThreshold}
                    onChange={(e) => updateSettings('vectorDatabase.similarityThreshold', parseFloat(e.target.value))}
                    min={0}
                    max={1}
                    step={0.01}
                    className="mt-2 w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Mindest-Ähnlichkeit für Suchergebnisse (0.0 - 1.0)
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableCache">Vector Cache aktivieren</Label>
                    <p className="text-sm text-gray-500">
                      Zwischenspeicherung für bessere Performance
                    </p>
                  </div>
                  <Switch
                    id="enableCache"
                    checked={settings.vectorDatabase.enableCache}
                    onCheckedChange={(checked) => updateSettings('vectorDatabase.enableCache', checked)}
                  />
                </div>

                {settings.vectorDatabase.enableCache && (
                  <div className="space-y-2">
                    <Label htmlFor="cacheTTL">Cache TTL (Minuten)</Label>
                    <Input
                      id="cacheTTL"
                      type="number"
                      value={Math.round(settings.vectorDatabase.cacheTTL / 60000)}
                      onChange={(e) => updateSettings('vectorDatabase.cacheTTL', parseInt(e.target.value) * 60000)}
                      min={1}
                      max={1440}
                    />
                    <p className="text-sm text-gray-500">
                      Lebensdauer der Cache-Einträge in Minuten
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Settings */}
        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-600" />
                Performance Konfiguration
              </CardTitle>
              <CardDescription>
                Einstellungen für Leistung und Ressourcenverbrauch
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="maxConcurrent">Max. gleichzeitige Operationen</Label>
                  <Input
                    id="maxConcurrent"
                    type="number"
                    value={settings.performance.maxConcurrentOperations}
                    onChange={(e) => updateSettings('performance.maxConcurrentOperations', parseInt(e.target.value))}
                    min={1}
                    max={20}
                  />
                  <p className="text-sm text-gray-500">
                    Maximale Anzahl parallel ausgeführter KI-Operationen
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timeout">Standard Timeout (Sekunden)</Label>
                  <Input
                    id="timeout"
                    type="number"
                    value={Math.round(settings.performance.defaultTimeout / 1000)}
                    onChange={(e) => updateSettings('performance.defaultTimeout', parseInt(e.target.value) * 1000)}
                    min={5}
                    max={300}
                  />
                  <p className="text-sm text-gray-500">
                    Standard-Timeout für KI-Operationen
                  </p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableRetries">Wiederholungen aktivieren</Label>
                    <p className="text-sm text-gray-500">
                      Automatische Wiederholung bei Fehlern
                    </p>
                  </div>
                  <Switch
                    id="enableRetries"
                    checked={settings.performance.enableRetries}
                    onCheckedChange={(checked) => updateSettings('performance.enableRetries', checked)}
                  />
                </div>

                {settings.performance.enableRetries && (
                  <div className="space-y-2">
                    <Label htmlFor="maxRetries">Max. Wiederholungen</Label>
                    <Input
                      id="maxRetries"
                      type="number"
                      value={settings.performance.maxRetries}
                      onChange={(e) => updateSettings('performance.maxRetries', parseInt(e.target.value))}
                      min={1}
                      max={10}
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableQueueing">Warteschlange aktivieren</Label>
                    <p className="text-sm text-gray-500">
                      Operationen in Warteschlange einreihen
                    </p>
                  </div>
                  <Switch
                    id="enableQueueing"
                    checked={settings.performance.enableQueueing}
                    onCheckedChange={(checked) => updateSettings('performance.enableQueueing', checked)}
                  />
                </div>

                {settings.performance.enableQueueing && (
                  <div className="space-y-2">
                    <Label htmlFor="queueMaxSize">Max. Warteschlangengröße</Label>
                    <Input
                      id="queueMaxSize"
                      type="number"
                      value={settings.performance.queueMaxSize}
                      onChange={(e) => updateSettings('performance.queueMaxSize', parseInt(e.target.value))}
                      min={10}
                      max={1000}
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-600" />
                Sicherheits-Konfiguration
              </CardTitle>
              <CardDescription>
                Sicherheitseinstellungen und Zugriffskontrolle
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Änderungen an Sicherheitseinstellungen erfordern einen Neustart des KI-Moduls.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableRateLimit">Rate Limiting aktivieren</Label>
                    <p className="text-sm text-gray-500">
                      Begrenzung der Anfragen pro Zeitfenster
                    </p>
                  </div>
                  <Switch
                    id="enableRateLimit"
                    checked={settings.security.enableRateLimit}
                    onCheckedChange={(checked) => updateSettings('security.enableRateLimit', checked)}
                  />
                </div>

                {settings.security.enableRateLimit && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="rateLimitRequests">Max. Anfragen</Label>
                      <Input
                        id="rateLimitRequests"
                        type="number"
                        value={settings.security.rateLimitRequests}
                        onChange={(e) => updateSettings('security.rateLimitRequests', parseInt(e.target.value))}
                        min={1}
                        max={10000}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="rateLimitWindow">Zeitfenster (Minuten)</Label>
                      <Input
                        id="rateLimitWindow"
                        type="number"
                        value={Math.round(settings.security.rateLimitWindow / 60000)}
                        onChange={(e) => updateSettings('security.rateLimitWindow', parseInt(e.target.value) * 60000)}
                        min={1}
                        max={1440}
                      />
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableInputValidation">Eingabe-Validierung</Label>
                    <p className="text-sm text-gray-500">
                      Validierung und Bereinigung von Benutzereingaben
                    </p>
                  </div>
                  <Switch
                    id="enableInputValidation"
                    checked={settings.security.enableInputValidation}
                    onCheckedChange={(checked) => updateSettings('security.enableInputValidation', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enableAuditLogging">Audit-Logging</Label>
                    <p className="text-sm text-gray-500">
                      Protokollierung aller KI-Operationen
                    </p>
                  </div>
                  <Switch
                    id="enableAuditLogging"
                    checked={settings.security.enableAuditLogging}
                    onCheckedChange={(checked) => updateSettings('security.enableAuditLogging', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Service Settings */}
        <TabsContent value="services" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Embedding Service */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Embedding Service</CardTitle>
                <CardDescription>
                  Konfiguration für Text-Embeddings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="embeddingModel">Embedding Modell</Label>
                  <Select
                    value={settings.services.embedding.model}
                    onValueChange={(value) => updateSettings('services.embedding.model', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text-embedding-3-small">text-embedding-3-small</SelectItem>
                      <SelectItem value="text-embedding-3-large">text-embedding-3-large</SelectItem>
                      <SelectItem value="text-embedding-ada-002">text-embedding-ada-002</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="batchSize">Batch-Größe</Label>
                  <Input
                    id="batchSize"
                    type="number"
                    value={settings.services.embedding.batchSize}
                    onChange={(e) => updateSettings('services.embedding.batchSize', parseInt(e.target.value))}
                    min={1}
                    max={1000}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="embeddingCache">Cache aktivieren</Label>
                  <Switch
                    id="embeddingCache"
                    checked={settings.services.embedding.enableCache}
                    onCheckedChange={(checked) => updateSettings('services.embedding.enableCache', checked)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* RAG Service */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">RAG Service</CardTitle>
                <CardDescription>
                  Retrieval-Augmented Generation Einstellungen
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="contextLength">Kontext-Länge</Label>
                  <Input
                    id="contextLength"
                    type="number"
                    value={settings.services.rag.contextLength}
                    onChange={(e) => updateSettings('services.rag.contextLength', parseInt(e.target.value))}
                    min={1000}
                    max={32000}
                  />
                </div>

                <div>
                  <Label>Vertrauensschwelle: {settings.services.rag.confidenceThreshold}</Label>
                  <input
                    type="range"
                    value={settings.services.rag.confidenceThreshold}
                    onChange={(e) => updateSettings('services.rag.confidenceThreshold', parseFloat(e.target.value))}
                    min={0}
                    max={1}
                    step={0.01}
                    className="mt-2 w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="sourceCitation">Quellen-Zitation</Label>
                  <Switch
                    id="sourceCitation"
                    checked={settings.services.rag.enableSourceCitation}
                    onCheckedChange={(checked) => updateSettings('services.rag.enableSourceCitation', checked)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Optimization Service */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Optimierungs-Service</CardTitle>
                <CardDescription>
                  Einstellungen für KI-Optimierungsalgorithmen
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="maxOptTime">Max. Optimierungszeit (Sekunden)</Label>
                  <Input
                    id="maxOptTime"
                    type="number"
                    value={Math.round(settings.services.optimization.maxOptimizationTime / 1000)}
                    onChange={(e) => updateSettings('services.optimization.maxOptimizationTime', parseInt(e.target.value) * 1000)}
                    min={10}
                    max={600}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="advancedAlgorithms">Erweiterte Algorithmen</Label>
                  <Switch
                    id="advancedAlgorithms"
                    checked={settings.services.optimization.enableAdvancedAlgorithms}
                    onCheckedChange={(checked) => updateSettings('services.optimization.enableAdvancedAlgorithms', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="parallelProcessing">Parallel-Verarbeitung</Label>
                  <Switch
                    id="parallelProcessing"
                    checked={settings.services.optimization.enableParallelProcessing}
                    onCheckedChange={(checked) => updateSettings('services.optimization.enableParallelProcessing', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AISettingsManager;