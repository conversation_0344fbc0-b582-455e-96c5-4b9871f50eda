import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import {
  Book,
  Search,
  Filter,
  ChevronRight,
  ChevronDown,
  ExternalLink,
  Copy,
  Check,
  Star,
  Clock,
  Tag,
  User,
  Calendar
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAIDocumentation, DocumentationSection } from './AIDocumentationProvider';

interface AIDocumentationViewerProps {
  className?: string;
}

export const AIDocumentationViewer: React.FC<AIDocumentationViewerProps> = ({ className }) => {
  const { t } = useTranslation();
  const {
    sections,
    searchDocumentation,
    getDocumentationByCategory
  } = useAIDocumentation();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');
  const [filteredSections, setFilteredSections] = useState<DocumentationSection[]>(sections);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [copiedId, setCopiedId] = useState<string | null>(null);

  useEffect(() => {
    let results = sections;

    // Apply search filter
    if (searchQuery.trim()) {
      results = searchDocumentation(searchQuery);
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      results = results.filter(section => section.category === selectedCategory);
    }

    // Apply difficulty filter
    if (selectedDifficulty !== 'all') {
      results = results.filter(section => section.difficulty === selectedDifficulty);
    }

    setFilteredSections(results);
  }, [searchQuery, selectedCategory, selectedDifficulty, sections, searchDocumentation]);

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800 border-green-200';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'advanced': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'overview': return <Book className="h-4 w-4" />;
      case 'features': return <Star className="h-4 w-4" />;
      case 'tutorials': return <User className="h-4 w-4" />;
      case 'api': return <ExternalLink className="h-4 w-4" />;
      case 'troubleshooting': return <Search className="h-4 w-4" />;
      default: return <Book className="h-4 w-4" />;
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('de-DE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  const categories = Array.from(new Set(sections.map(s => s.category)));
  const difficulties = Array.from(new Set(sections.map(s => s.difficulty)));

  return (
    <div className={`max-w-6xl mx-auto p-6 ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Book className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t('ai.documentation.title')}
            </h1>
            <p className="text-gray-600 mt-1">
              {t('ai.documentation.subtitle')}
            </p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder={t('ai.documentation.search.placeholder')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-2">
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder={t('ai.documentation.filters.category')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('ai.documentation.filters.all')}</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {t(`ai.documentation.categories.${category}`)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder={t('ai.documentation.filters.difficulty')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('ai.documentation.filters.all')}</SelectItem>
                {difficulties.map(difficulty => (
                  <SelectItem key={difficulty} value={difficulty}>
                    {t(`ai.documentation.difficulty.${difficulty}`)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Results Summary */}
        <div className="mt-4 text-sm text-gray-600">
          {t('ai.documentation.results', { count: filteredSections.length })}
        </div>
      </div>

      {/* Documentation Sections */}
      <div className="space-y-6">
        {filteredSections.map((section) => (
          <motion.div
            key={section.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="overflow-hidden hover:shadow-lg transition-shadow">
              <CardHeader
                className="cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => toggleSection(section.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getCategoryIcon(section.category)}
                      <CardTitle className="text-xl">{section.title}</CardTitle>
                      <div className="flex items-center">
                        {expandedSections.has(section.id) ? (
                          <ChevronDown className="h-5 w-5 text-gray-400" />
                        ) : (
                          <ChevronRight className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 flex-wrap">
                      <Badge
                        variant="outline"
                        className={getDifficultyColor(section.difficulty)}
                      >
                        {t(`ai.documentation.difficulty.${section.difficulty}`)}
                      </Badge>
                      
                      <Badge variant="secondary">
                        {t(`ai.documentation.categories.${section.category}`)}
                      </Badge>
                      
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <Calendar className="h-3 w-3" />
                        {formatDate(section.lastUpdated)}
                      </div>
                    </div>

                    {/* Tags */}
                    <div className="flex items-center gap-2 mt-3 flex-wrap">
                      {section.tags.slice(0, 5).map((tag) => (
                        <div
                          key={tag}
                          className="flex items-center gap-1 px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-xs"
                        >
                          <Tag className="h-3 w-3" />
                          {tag}
                        </div>
                      ))}
                      {section.tags.length > 5 && (
                        <span className="text-xs text-gray-500">
                          +{section.tags.length - 5} {t('ai.documentation.moreTags')}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        copyToClipboard(section.content, section.id);
                      }}
                      className="h-8 w-8 p-0"
                    >
                      {copiedId === section.id ? (
                        <Check className="h-4 w-4 text-green-600" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </CardHeader>

              {expandedSections.has(section.id) && (
                <CardContent className="pt-0">
                  <div className="border-t border-gray-200 pt-6">
                    <div className="prose prose-sm max-w-none">
                      <div
                        className="text-gray-700 leading-relaxed"
                        dangerouslySetInnerHTML={{
                          __html: section.content.replace(/\n/g, '<br />')
                        }}
                      />
                    </div>

                    {/* Additional Metadata */}
                    <div className="mt-6 pt-4 border-t border-gray-100">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-gray-900">
                            {t('ai.documentation.metadata.category')}:
                          </span>
                          <span className="ml-2 text-gray-600">
                            {t(`ai.documentation.categories.${section.category}`)}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-900">
                            {t('ai.documentation.metadata.difficulty')}:
                          </span>
                          <span className="ml-2 text-gray-600">
                            {t(`ai.documentation.difficulty.${section.difficulty}`)}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-900">
                            {t('ai.documentation.metadata.lastUpdated')}:
                          </span>
                          <span className="ml-2 text-gray-600">
                            {formatDate(section.lastUpdated)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          </motion.div>
        ))}

        {filteredSections.length === 0 && (
          <div className="text-center py-12">
            <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('ai.documentation.noResults.title')}
            </h3>
            <p className="text-gray-600 mb-4">
              {t('ai.documentation.noResults.description')}
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
                setSelectedDifficulty('all');
              }}
            >
              {t('ai.documentation.noResults.clearFilters')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};