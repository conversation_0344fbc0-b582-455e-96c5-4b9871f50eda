---
inclusion: always
---

# JOZI1 Lapp Dashboard - Product Guidelines

**JOZI1 Lapp Dashboard** is an Electron desktop application for real-time logistics KPI visualization with user authentication and role-based access across three departments: Dispatch (Versand), Cutting (Ablängerei), and Incoming Goods (Wareneingang).

## Authentication & Authorization

### User Management System
- **JWT-based authentication**: All API requests use Bearer tokens with 15-minute expiration
- **Three user roles**: Administrator, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> with hierarchical permissions
- **Secure password handling**: bcrypt with 12 salt rounds for password hashing
- **Session management**: Automatic logout after 15 minutes of inactivity

### Role-Based Access Control (RBAC)
- **Besucher**: Read-only access to Leitstand module only
- **Benutzer**: Access to Leitstand and/or Störungen modules with modification rights
- **Administrator**: Full access to all modules and user management functions
- **Route protection**: All protected routes must check user authentication and role permissions

### Authentication Flow Requirements
- **Login/Registration pages**: Must use German language and shadcn/ui components
- **Module selection**: Post-login dashboard showing available modules based on user role
- **Token storage**: Use localStorage for JWT tokens with proper cleanup on logout
- **Error handling**: German error messages for authentication failures

## Architecture Patterns

### Data Layer
- **Repository pattern**: All database operations through `src/repositories/`
- **TanStack Query**: Required for caching and server state management
- **Services layer**: Coordinates between repositories and UI components
- **SQLite optimization**: Use proper indexing and connection pooling
- **Authentication middleware**: Protect API routes with JWT verification

### Component Structure
- **Department organization**: `/dispatch`, `/cutting`, `/incoming-goods` routes
- **Authentication components**: `src/components/auth/` for login, registration, guards
- **Shared components**: `src/components/ui/` (shadcn/ui based)
- **Charts**: `src/components/charts/` using Recharts exclusively
- **Pages**: `src/pages/` following route structure with auth integration

### Security Patterns
- **Protected routes**: Use ProtectedRoute component for authenticated pages
- **API security**: All sensitive endpoints require JWT authentication
- **Input validation**: Validate all user inputs on both frontend and backend
- **Error boundaries**: Catch and handle authentication errors gracefully

## Code Standards

### TypeScript Rules
- **Strict mode**: No `any` types in production code
- **Type organization**: Interfaces in `src/types/` by domain, including auth types
- **Error boundaries**: Required for all React components
- **Comprehensive error handling**: All async operations must have try-catch

### Authentication Code Standards
- **Service pattern**: Use AuthService for all authentication operations
- **Type safety**: Define interfaces for LoginRequest, RegisterRequest, User, Role
- **Error codes**: Use consistent error codes (INVALID_CREDENTIALS, USER_EXISTS, etc.)
- **Token management**: Centralized token storage and retrieval through AuthService

### File Naming Conventions
- Components: PascalCase (`HomePage.tsx`, `LoginPage.tsx`, `AuthGuard.tsx`)
- Services: camelCase (`authService.ts`, `userService.ts`)
- Types: PascalCase (`User`, `Role`, `AuthResponse`)
- Constants: UPPER_SNAKE_CASE (`JWT_SECRET`, `TOKEN_EXPIRY`)

### Import Standards
- Use `@/` alias for all internal imports
- Group: external libraries → internal modules → types → auth imports
- Prefer named exports for utilities and services
- Single-responsibility components only

## UI/UX Requirements

### Authentication UI Standards
- **German language**: All auth-related text in German with English fallbacks
- **shadcn/ui consistency**: Use existing Button, Input, Form components
- **Error display**: Clear, user-friendly German error messages
- **Loading states**: Show loading indicators during auth operations
- **Responsive design**: Auth pages must work on all screen sizes

### Role-Based UI Rendering
- **Conditional rendering**: Hide/show components based on user role
- **Module access**: Display only accessible modules in navigation
- **Permission indicators**: Show user role and permissions in UI
- **Graceful degradation**: Handle missing permissions elegantly

### Internationalization
- **Primary**: German interface with English fallback
- **Pattern**: `t('auth.login.title')` for all auth-related text
- **Department names**: Versand, Ablängerei, Wareneingang (German)
- **Requirement**: All user-facing text must be translatable

### Data Visualization
- **Charts**: Recharts only for all visualizations
- **Updates**: Real-time without full re-renders
- **Loading states**: Required for all data operations
- **Large tables**: Use virtualization for >100 rows
- **Colors**: Department-specific schemes for consistency

## AI Integration Guidelines

### OpenRouter API Integration
- **Backend-only**: All AI API calls must go through backend to protect API keys
- **Endpoint**: Use `/chat/completions` for AI interactions
- **Data access**: AI can query StoerungenRepository and Leitstand repositories
- **Performance**: AI operations must not impact existing dashboard performance

### KI-Chatbot Requirements
- **Data integration**: Access to current and historical Störungen data
- **Leitstand integration**: Query all department repositories for analysis
- **Optimization features**: Restlängenoptimierung suggestions on CuttingPage
- **German responses**: AI responses must be in German language

## Performance Standards

### Authentication Performance
- **Login speed**: Authentication must complete within 2 seconds
- **Token validation**: JWT verification should be < 100ms
- **Session management**: Efficient token refresh and cleanup
- **Database queries**: Optimize user/role lookups with proper indexing

### Real-time Updates
- WebSocket or polling for live data
- Incremental UI updates, never full page refreshes
- Proper cleanup for subscriptions and timers
- Graceful offline/connection loss handling

### Database Performance
- Optimize queries for dashboard performance
- Cache KPI calculations when possible
- Use proper indexing for frequent queries
- Separate auth queries from business logic queries

## Department-Specific Rules

### Module Access Control
- **Leitstand**: Available to all authenticated users (minimum Besucher role)
- **Störungen**: Available to Benutzer and Administrator roles only
- **Backend & Automatisierung**: Administrator access only (future module)
- **User Management**: Administrator access only

### KPI Standards
- Consistent calculation methods across all departments
- Standardized service levels, picking performance, quality rates
- Same time periods for comparative metrics
- Document calculation formulas in code comments

### Route Structure
- **Public routes**: `/login`, `/register` (no authentication required)
- **Protected routes**: All other routes require authentication
- **Role-based routes**: Module access based on user permissions
- **TanStack Router**: Full type safety with auth integration
- **Breadcrumb navigation**: Include user context and permissions

### Error Handling
- German error messages for users
- Comprehensive logging with user context
- React Error Boundaries for component errors
- Try-catch blocks for all async operations
- Authentication error recovery strategies

## Security Requirements

### Password Security
- **Minimum requirements**: 8 characters, mixed case, numbers
- **Hashing**: bcrypt with 12 salt rounds minimum
- **Storage**: Never store plain text passwords
- **Validation**: Both frontend and backend password validation

### Token Security
- **JWT expiration**: 15 minutes maximum
- **Secure storage**: localStorage with proper cleanup
- **Token refresh**: Implement refresh token strategy if needed
- **HTTPS only**: All authentication endpoints must use HTTPS in production

### Data Protection
- **User data**: Encrypt sensitive user information
- **Audit logging**: Log all authentication events
- **Access control**: Strict role-based data access
- **Input sanitization**: Prevent injection attacks on all inputs