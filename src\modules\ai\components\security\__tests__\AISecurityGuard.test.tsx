/**
 * AI Security Guard Component Tests
 * 
 * Tests for the AI security guard component that protects AI routes
 * and components with role-based access control.
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { AISecurityGuard, useAISecurityContext, withAISecurity } from '../AISecurityGuard';
import { useAuthContext } from '@/contexts/AuthContext';
import { User } from '@/hooks/useAuth';

// Mock the auth context
vi.mock('@/contexts/AuthContext', () => ({
  useAuthContext: vi.fn()
}));

// Mock the AI security service
vi.mock('@/modules/ai/services/security/AISecurityService', () => ({
  aiSecurityService: {
    createSecurityContext: vi.fn(),
    hasFeatureAccess: vi.fn()
  }
}));

describe('AISecurityGuard', () => {
  const mockUser: User = {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    roles: ['<PERSON><PERSON><PERSON>']
  };

  const mockSecurityContext = {
    userId: 1,
    username: 'testuser',
    roles: ['Benutzer'],
    permissions: [
      {
        id: 'rag_query',
        name: 'RAG Abfragen',
        description: 'Berechtigung für RAG-basierte Abfragen',
        requiredRoles: ['Besucher', 'Benutzer', 'Administrator']
      }
    ],
    featureAccess: {
      rag_query: {
        featureId: 'rag_query',
        hasAccess: true
      },
      cutting_optimization: {
        featureId: 'cutting_optimization',
        hasAccess: true
      },
      ai_configuration: {
        featureId: 'ai_configuration',
        hasAccess: false,
        reason: 'Benötigt eine der Rollen: Administrator'
      }
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when user is authenticated and authorized', () => {
    beforeEach(() => {
      vi.mocked(useAuthContext).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        isLoading: false,
        token: 'mock-token',
        login: vi.fn(),
        logout: vi.fn(),
        trackActivity: vi.fn()
      });

      const { aiSecurityService } = require('@/modules/ai/services/security/AISecurityService');
      aiSecurityService.createSecurityContext.mockReturnValue(mockSecurityContext);
      aiSecurityService.hasFeatureAccess.mockReturnValue(true);
    });

    it('should render children when user has access', async () => {
      render(
        <AISecurityGuard requiredFeature="rag_query">
          <div data-testid="protected-content">Protected Content</div>
        </AISecurityGuard>
      );

      await waitFor(() => {
        expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      });
    });

    it('should render children when no specific feature required and user has permissions', async () => {
      render(
        <AISecurityGuard>
          <div data-testid="protected-content">Protected Content</div>
        </AISecurityGuard>
      );

      await waitFor(() => {
        expect(screen.getByTestId('protected-content')).toBeInTheDocument();
      });
    });
  });

  describe('when user is authenticated but not authorized', () => {
    beforeEach(() => {
      vi.mocked(useAuthContext).mockReturnValue({
        user: mockUser,
        isAuthenticated: true,
        isLoading: false,
        token: 'mock-token',
        login: vi.fn(),
        logout: vi.fn(),
        trackActivity: vi.fn()
      });

      const { aiSecurityService } = require('@/modules/ai/services/security/AISecurityService');
      aiSecurityService.createSecurityContext.mockReturnValue(mockSecurityContext);
      aiSecurityService.hasFeatureAccess.mockReturnValue(false);
    });

    it('should show access denied message', async () => {
      render(
        <AISecurityGuard requiredFeature="ai_configuration">
          <div data-testid="protected-content">Protected Content</div>
        </AISecurityGuard>
      );

      await waitFor(() => {
        expect(screen.getByText('Zugriff verweigert')).toBeInTheDocument();
        expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      });
    });

    it('should render fallback component when provided', async () => {
      render(
        <AISecurityGuard 
          requiredFeature="ai_configuration"
          fallbackComponent={<div data-testid="fallback">Fallback Content</div>}
        >
          <div data-testid="protected-content">Protected Content</div>
        </AISecurityGuard>
      );

      await waitFor(() => {
        expect(screen.getByTestId('fallback')).toBeInTheDocument();
        expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      });
    });

    it('should render nothing when showAccessDenied is false', async () => {
      const { container } = render(
        <AISecurityGuard 
          requiredFeature="ai_configuration"
          showAccessDenied={false}
        >
          <div data-testid="protected-content">Protected Content</div>
        </AISecurityGuard>
      );

      await waitFor(() => {
        expect(container.firstChild).toBeNull();
      });
    });
  });

  describe('when user is not authenticated', () => {
    beforeEach(() => {
      vi.mocked(useAuthContext).mockReturnValue({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        token: null,
        login: vi.fn(),
        logout: vi.fn(),
        trackActivity: vi.fn()
      });
    });

    it('should show access denied with login button', async () => {
      render(
        <AISecurityGuard>
          <div data-testid="protected-content">Protected Content</div>
        </AISecurityGuard>
      );

      await waitFor(() => {
        expect(screen.getByText('Zugriff verweigert')).toBeInTheDocument();
        expect(screen.getByText('Anmelden')).toBeInTheDocument();
        expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
      });
    });
  });

  describe('when authentication is loading', () => {
    beforeEach(() => {
      vi.mocked(useAuthContext).mockReturnValue({
        user: null,
        isAuthenticated: false,
        isLoading: true,
        token: null,
        login: vi.fn(),
        logout: vi.fn(),
        trackActivity: vi.fn()
      });
    });

    it('should show loading state', () => {
      render(
        <AISecurityGuard>
          <div data-testid="protected-content">Protected Content</div>
        </AISecurityGuard>
      );

      expect(screen.getByText('Sicherheitsprüfung läuft...')).toBeInTheDocument();
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    });
  });
});

describe('useAISecurityContext', () => {
  const TestComponent: React.FC = () => {
    const { securityContext, hasFeatureAccess, getUserPermissions, isAuthenticated } = useAISecurityContext();
    
    return (
      <div>
        <div data-testid="authenticated">{isAuthenticated ? 'true' : 'false'}</div>
        <div data-testid="username">{securityContext?.username || 'none'}</div>
        <div data-testid="rag-access">{hasFeatureAccess('rag_query') ? 'true' : 'false'}</div>
        <div data-testid="permissions-count">{getUserPermissions().length}</div>
      </div>
    );
  };

  beforeEach(() => {
    vi.mocked(useAuthContext).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      isLoading: false,
      token: 'mock-token',
      login: vi.fn(),
      logout: vi.fn(),
      trackActivity: vi.fn()
    });

    const { aiSecurityService } = require('@/modules/ai/services/security/AISecurityService');
    aiSecurityService.createSecurityContext.mockReturnValue(mockSecurityContext);
    aiSecurityService.hasFeatureAccess.mockReturnValue(true);
  });

  it('should provide security context information', async () => {
    render(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('authenticated')).toHaveTextContent('true');
      expect(screen.getByTestId('username')).toHaveTextContent('testuser');
      expect(screen.getByTestId('rag-access')).toHaveTextContent('true');
      expect(screen.getByTestId('permissions-count')).toHaveTextContent('1');
    });
  });
});

describe('withAISecurity HOC', () => {
  const TestComponent: React.FC<{ message: string }> = ({ message }) => (
    <div data-testid="test-component">{message}</div>
  );

  beforeEach(() => {
    vi.mocked(useAuthContext).mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      isLoading: false,
      token: 'mock-token',
      login: vi.fn(),
      logout: vi.fn(),
      trackActivity: vi.fn()
    });

    const { aiSecurityService } = require('@/modules/ai/services/security/AISecurityService');
    aiSecurityService.createSecurityContext.mockReturnValue(mockSecurityContext);
    aiSecurityService.hasFeatureAccess.mockReturnValue(true);
  });

  it('should wrap component with security guard', async () => {
    const SecuredComponent = withAISecurity(TestComponent, 'rag_query');
    
    render(<SecuredComponent message="Hello World" />);

    await waitFor(() => {
      expect(screen.getByTestId('test-component')).toBeInTheDocument();
      expect(screen.getByTestId('test-component')).toHaveTextContent('Hello World');
    });
  });

  it('should set correct display name', () => {
    const SecuredComponent = withAISecurity(TestComponent, 'rag_query');
    
    expect(SecuredComponent.displayName).toBe('withAISecurity(TestComponent)');
  });
});