import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Save, AlertTriangle, X, Image as ImageIcon, Trash2 } from 'lucide-react';
import { Stoerung, StoerungUpdateData, StoerungStatus, StoerungAttachment } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';
import stoerungKategorienService from '@/services/stoerung-kategorien.service';
import { useToast } from '@/components/ui/use-toast';
import FileUploadStoerung from '@/components/Animation/kokonutui/file-upload-stoerung';
import { bereitschaftsService } from '@/services/bereitschaftsService';
import { BereitschaftsPerson, BereitschaftsWoche } from '@/types/bereitschafts';

interface StoerungsEditFormProps {
  open: boolean;
  stoerung: Stoerung;
  onOpenChange: (open: boolean) => void;
  onClose: () => void;
  onSubmit: () => void;
}

/**
 * Komponente für die Bearbeitung bestehender Störungen
 * Ermöglicht das Ändern aller relevanten Felder einer Störung
 */
export const StoerungsEditForm: React.FC<StoerungsEditFormProps> = ({
  open,
  stoerung,
  onOpenChange,
  onClose,
  onSubmit
}) => {
  // Funktion zur Konvertierung von Backend-Status zu Frontend-kompatiblen Werten für die Anzeige
  const mapStatusFromBackend = (backendStatus: string | StoerungStatus): StoerungStatus => {
    const statusMapping: { [key: string]: StoerungStatus } = {
      // Neue Werte
      'NEW': StoerungStatus.NEU,
      'IN_PROGRESS': StoerungStatus.IN_BEARBEITUNG,
      'RESOLVED': StoerungStatus.GELÖST,
      // Alte Enum-Werte
      [String(StoerungStatus.NEU)]: StoerungStatus.NEU,
      [String(StoerungStatus.IN_BEARBEITUNG)]: StoerungStatus.IN_BEARBEITUNG,
      [String(StoerungStatus.GELÖST)]: StoerungStatus.GELÖST,
      [String(StoerungStatus.ESKALIERT_L2)]: StoerungStatus.IN_BEARBEITUNG,
      [String(StoerungStatus.ESKALIERT_L3)]: StoerungStatus.IN_BEARBEITUNG,
      [String(StoerungStatus.ABGESCHLOSSEN)]: StoerungStatus.GELÖST,
      [String(StoerungStatus.REVIEW)]: StoerungStatus.GELÖST
    };
    
    return statusMapping[String(backendStatus)] || StoerungStatus.NEU;
  };

  // Initialisiere Formular mit bestehenden Störungsdaten
  const [formData, setFormData] = useState<StoerungUpdateData>({
    title: stoerung.title,
    description: stoerung.description || '',
    severity: stoerung.severity,
    status: mapStatusFromBackend(stoerung.status),
    category: stoerung.category || '',
    affected_system: stoerung.affected_system || '',
    location: stoerung.location || 'Ludwigsburg', // Standardwert auf Ludwigsburg
    assigned_to: stoerung.assigned_to || '',
    tags: stoerung.tags || [],
    resolution_steps: stoerung.resolution_steps || '',
    root_cause: stoerung.root_cause || '',
    lessons_learned: stoerung.lessons_learned || ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Toast für Erfolgsmeldungen
  const { showToast } = useToast();

  // State für Bild-Uploads und bestehende Anhänge
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [existingAttachments, setExistingAttachments] = useState<StoerungAttachment[]>([]);
  const [attachmentsLoading, setAttachmentsLoading] = useState(true);

  // State für Bereitschaftsperson-Zuweisung
  const [bereitschaftsperson, setBereitschaftsperson] = useState<BereitschaftsPerson | null>(null);
  const [bereitschaftsLoading, setBereitschaftsLoading] = useState(false);

  // Kategorien-Management
  const [kategorien, setKategorien] = useState<any[]>([]);
  const [kategorienLoading, setKategorienLoading] = useState(true);
  const [kategorienError, setKategorienError] = useState<string | null>(null);

  // Optionen für Schweregrade
  const severityOptions = [
    { value: 'LOW', label: 'Niedrig', color: 'text-green-600' },
    { value: 'MEDIUM', label: 'Mittel', color: 'text-yellow-600' },
    { value: 'HIGH', label: 'Hoch', color: 'text-orange-600' },
    { value: 'CRITICAL', label: 'Kritisch', color: 'text-red-600' },
  ];

  // Status-Optionen - Nur Backend-kompatible Werte verwenden
  // Backend akzeptiert nur: 'NEW', 'IN_PROGRESS', 'RESOLVED'
  const statusOptions = [
    { value: 'NEW', label: 'Neu', color: 'text-blue-600' },
    { value: 'IN_PROGRESS', label: 'In Bearbeitung', color: 'text-orange-600' },
    { value: 'RESOLVED', label: 'Gelöst', color: 'text-green-600' }
  ];

  // Sichere Erstellung der Kategorie-Optionen mit verbessertem Null-Check
  const categoryOptions = kategorien
    ?.map(k => k?.name)
    .filter((name): name is string => name != null && typeof name === 'string' && name.trim().length > 0) || [];
  const [affectedSystemOptions, setAffectedSystemOptions] = useState<string[]>([]);

  // Lade Kategorien beim ersten Rendern
  useEffect(() => {
    const loadKategorien = async () => {
      try {
        setKategorienLoading(true);
        const data = await stoerungKategorienService.getKategorien();
        setKategorien(data);
        setKategorienError(null);
      } catch (err) {
        console.error('Fehler beim Laden der Kategorien:', err);
        setKategorienError('Fehler beim Laden der Kategorien');

        // Fallback auf hardcoded Daten falls Service nicht verfügbar
        const fallbackData = [
          { name: 'Systeme', systeme: ['SAP', 'ITM', 'Wamas', 'Mail', 'Citrix', 'MFR'] },
          { name: 'Infrastruktur', systeme: ['Netzwerk', 'WLAN'] },
          { name: 'Technische Komponenten', systeme: ['Förderband', 'FTS', 'RBG', 'Greifer', 'Schrumpfanlage', 'Ablängmaschinen'] },
          { name: 'Kommissionierung', systeme: ['Automatisches Trommellager (ATrL)', 'Automatisches Ringlager (ARiL)', 'Schäfer Karussell (SCS)'] },
          { name: 'Hardware', systeme: ['Mobile Drucker', 'Label Drucker', 'Lieferschein Drucker', 'Terminal', 'PC'] },
        ];
        setKategorien(fallbackData);
      } finally {
        setKategorienLoading(false);
      }
    };

    const loadAttachments = async () => {
      try {
        setAttachmentsLoading(true);
        const response = await fetch(`/api/stoerungen/${stoerung.id}/attachments`);
        if (response.ok) {
          const attachments = await response.json();
          setExistingAttachments(attachments.data || []);
        } else {
          console.error('Fehler beim Laden der Anhänge:', response.statusText);
        }
      } catch (error) {
        console.error('Fehler beim Laden der Anhänge:', error);
      } finally {
        setAttachmentsLoading(false);
      }
    };

    loadKategorien();
    loadAttachments();
    
    // Lade Bereitschaftsperson basierend auf Erstellungsdatum
    if (stoerung.created_at) {
      getBereitschaftspersonAtDate(stoerung.created_at).then(person => {
        setBereitschaftsperson(person);
        if (person && !formData.assigned_to) {
          // Setze den Namen der Bereitschaftsperson, falls noch nicht zugewiesen
          setFormData(prev => ({
            ...prev,
            assigned_to: person.name
          }));
        }
      });
    }
  }, [stoerung.id, stoerung.created_at]);

  // Aktualisiere betroffene Systeme basierend auf gewählter Kategorie
  useEffect(() => {
    if (formData.category && kategorien?.length > 0) {
      const selectedCategory = kategorien.find(k => k?.name === formData.category);
      if (selectedCategory?.systeme) {
        // Verbesserte Filterung für betroffene Systeme
        const validSystems = selectedCategory.systeme
          .filter((system: string) => system && typeof system === 'string' && system.trim().length > 0);
        setAffectedSystemOptions(validSystems || []);
      } else {
        setAffectedSystemOptions([]);
      }
    } else {
      // Wenn keine Kategorie gewählt ist, aber ein affected_system aus der DB existiert,
      // füge es zu den Optionen hinzu, damit es angezeigt werden kann
      if (formData.affected_system && formData.affected_system.trim().length > 0) {
        setAffectedSystemOptions([formData.affected_system]);
      } else {
        setAffectedSystemOptions([]);
      }
    }
  }, [formData.category, kategorien, formData.affected_system]);

  // Initialer useEffect: Stelle sicher, dass das affected_system aus der DB angezeigt wird
  useEffect(() => {
    // Debug: Zeige an, was aus der Datenbank geladen wird
    console.log('[StoerungsEditForm] Lade affected_system aus DB:', {
      stoerung_affected_system: stoerung.affected_system,
      formData_affected_system: formData.affected_system,
      current_affectedSystemOptions: affectedSystemOptions
    });
    
    // Beim ersten Laden der Komponente: Falls ein affected_system aus der DB existiert,
    // aber noch keine Kategorien geladen sind, füge es zu den Optionen hinzu
    if (stoerung.affected_system && stoerung.affected_system.trim().length > 0) {
      setAffectedSystemOptions(prev => {
        const affectedSystem = stoerung.affected_system!; // Non-null assertion da wir oben prüfen
        if (!prev.includes(affectedSystem)) {
          console.log('[StoerungsEditForm] Füge affected_system zu Optionen hinzu:', affectedSystem);
          return [affectedSystem, ...prev];
        }
        return prev;
      });
    }
  }, [stoerung.affected_system]); // Nur beim Ändern der ursprünglichen Störung

  // Funktion zur Ermittlung der Bereitschaftsperson basierend auf dem Erstellungsdatum
  const getBereitschaftspersonAtDate = async (erstellungsDatum: string): Promise<BereitschaftsPerson | null> => {
    try {
      setBereitschaftsLoading(true);
      
      // Berechne das Jahr und die KW basierend auf dem Erstellungsdatum
      const createdDate = new Date(erstellungsDatum);
      const startOfWeek = new Date(createdDate);
      const dayOfWeek = createdDate.getDay();
      const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // Montag als Wochenstart
      startOfWeek.setDate(createdDate.getDate() + mondayOffset);
      
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      
      // Hole alle Bereitschaftswochen für den relevanten Zeitraum (erweitert um ±1 Woche)
      const searchStart = new Date(startOfWeek);
      searchStart.setDate(startOfWeek.getDate() - 7);
      const searchEnd = new Date(endOfWeek);
      searchEnd.setDate(endOfWeek.getDate() + 7);
      
      const wochenplan = await bereitschaftsService.getWochenplan(
        searchStart.toISOString().split('T')[0],
        3 // 3 Wochen: -1, 0, +1
      );
      
      // Finde die passende Bereitschaftswoche
      const passendeBereitschaft = wochenplan.find((woche: BereitschaftsWoche) => {
        const wochenStartDate = new Date(woche.wochenStart);
        const wochenEndDate = new Date(woche.wochenEnde);
        
        // Prüfe ob das Erstellungsdatum in diese Woche fällt
        return createdDate >= wochenStartDate && createdDate <= wochenEndDate;
      });
      
      if (passendeBereitschaft && passendeBereitschaft.person) {
        console.log('Bereitschaftsperson gefunden für', erstellungsDatum, ':', passendeBereitschaft.person.name);
        return passendeBereitschaft.person;
      }
      
      console.warn('Keine Bereitschaftsperson für das Datum gefunden:', erstellungsDatum);
      return null;
    } catch (error) {
      console.error('Fehler beim Ermitteln der Bereitschaftsperson:', error);
      return null;
    } finally {
      setBereitschaftsLoading(false);
    }
  };

  // Funktion zur Konvertierung von Frontend-Status zu Backend-kompatiblen Werten
  const mapStatusToBackend = (frontendStatus: string | StoerungStatus): StoerungStatus => {
    const statusMapping: { [key: string]: StoerungStatus } = {
      // Neue Werte
      'NEW': StoerungStatus.NEU,
      'IN_PROGRESS': StoerungStatus.IN_BEARBEITUNG,
      'RESOLVED': StoerungStatus.GELÖST,
      // Alte Enum-Werte
      [String(StoerungStatus.NEU)]: StoerungStatus.NEU,
      [String(StoerungStatus.IN_BEARBEITUNG)]: StoerungStatus.IN_BEARBEITUNG,
      [String(StoerungStatus.GELÖST)]: StoerungStatus.GELÖST,
      [String(StoerungStatus.ESKALIERT_L2)]: StoerungStatus.IN_BEARBEITUNG,
      [String(StoerungStatus.ESKALIERT_L3)]: StoerungStatus.IN_BEARBEITUNG,
      [String(StoerungStatus.ABGESCHLOSSEN)]: StoerungStatus.GELÖST,
      [String(StoerungStatus.REVIEW)]: StoerungStatus.GELÖST
    };
    
    return statusMapping[String(frontendStatus)] || StoerungStatus.NEU;
  };

  // Funktion zum Löschen bestehender Anhänge
  const handleDeleteAttachment = async (attachmentId: number) => {
    try {
      const response = await fetch(`/api/stoerungen/attachments/${attachmentId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        setExistingAttachments(prev => prev.filter(att => att.id !== attachmentId));
        showToast(
          'Anhang gelöscht',
          'Der Anhang wurde erfolgreich entfernt.',
          { type: 'success', duration: 3000 }
        );
      } else {
        throw new Error('Fehler beim Löschen des Anhangs');
      }
    } catch (error) {
      console.error('Fehler beim Löschen des Anhangs:', error);
      showToast(
        'Fehler',
        'Der Anhang konnte nicht gelöscht werden.',
        { type: 'error', duration: 4000 }
      );
    }
  };

  const handleInputChange = (field: keyof StoerungUpdateData, value: string) => {
    // Blockiere Änderungen an unverzichtbaren Feldern
    if (field === 'location' || field === 'assigned_to') {
      console.warn(`Feld ${field} ist unverzichtbar und kann nicht geändert werden`);
      return;
    }
    
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Reset affected_system wenn category geändert wird
    if (field === 'category') {
      setFormData(prev => ({
        ...prev,
        affected_system: '',
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title?.trim()) {
      setError('Titel ist erforderlich');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Bereite Daten für Backend vor - konvertiere Status zu Backend-kompatiblen Werten
      const backendData: StoerungUpdateData = {
        ...formData,
        status: formData.status ? mapStatusToBackend(formData.status) : StoerungStatus.NEU,
        location: 'Ludwigsburg', // Standort ist immer Ludwigsburg
        assigned_to: bereitschaftsperson ? bereitschaftsperson.name : formData.assigned_to // Verwende Bereitschaftsperson falls vorhanden
      };

      console.log('Sende Daten an Backend:', backendData);

      // Aktualisiere die Störung mit Backend-kompatiblen Daten
      await stoerungenService.updateStoerung(stoerung.id, backendData);
      
      // Lade neue Bilder hoch, falls vorhanden
      if (uploadedImages.length > 0) {
        try {
          for (const image of uploadedImages) {
            const formData = new FormData();
            formData.append('image', image);
            
            const uploadResponse = await fetch(`/api/stoerungen/${stoerung.id}/upload-image`, {
              method: 'POST',
              body: formData,
            });
            
            if (!uploadResponse.ok) {
              throw new Error(`Fehler beim Hochladen von ${image.name}`);
            }
          }
          
          // Zeige Toast-Erfolgsmeldung mit Bild-Upload
          showToast(
            'Störung aktualisiert',
            `Die Störung wurde erfolgreich gespeichert und ${uploadedImages.length} Bild(er) hochgeladen.`,
            { type: 'success', duration: 4000 }
          );
        } catch (uploadError) {
          console.error('Fehler beim Hochladen der Bilder:', uploadError);
          showToast(
            'Teilweise erfolgreich',
            'Die Störung wurde gespeichert, aber es gab Probleme beim Hochladen einiger Bilder.',
            { type: 'warning', duration: 5000 }
          );
        }
      } else {
        // Zeige Toast-Erfolgsmeldung ohne Bild-Upload
        showToast(
          'Störung aktualisiert',
          'Die Störung wurde erfolgreich gespeichert.',
          { type: 'success', duration: 4000 }
        );
      }
      
      // Erfolgreich - schließe Dialog und aktualisiere Liste
      onSubmit();
      onClose();
    } catch (err) {
      console.error('Fehler beim Aktualisieren der Störung:', err);
      setError('Fehler beim Speichern der Änderungen');
    } finally {
      setLoading(false);
    }
  };

  // Bedingte Rückgabe nach allen Hook-Aufrufen, um React Rules of Hooks zu befolgen
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
      />
      
      {/* Dialog Content */}
      <div className="relative bg-white rounded-lg shadow-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between py-2 px-6 border-b bg-gray-100">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-600" />
            Störung bearbeiten
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}



        {kategorienError && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              ⚠️ {kategorienError} - Verwende Fallback-Daten
            </p>
          </div>
        )}

        {/* Titel */}
        <div className="space-y-2">
          <Label htmlFor="title">Titel *</Label>
          <Input
            id="title"
            value={formData.title || ''}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="Kurze Beschreibung der Störung"
            required
          />
        </div>

        {/* Beschreibung */}
        <div className="space-y-2">
          <Label htmlFor="description">Beschreibung</Label>
          <Textarea
            id="description"
            value={formData.description || ''}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Detaillierte Beschreibung der Störung"
            rows={3}
          />
        </div>

        {/* Schweregrad, Status und Standort/Zuweisungsfelder in 3 Spalten */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="severity">Schweregrad</Label>
            <Select value={formData.severity} onValueChange={(value) => handleInputChange('severity', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Schweregrad wählen" />
              </SelectTrigger>
              <SelectContent>
                {severityOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    <span className={option.color}>{option.label}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Status wählen" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    <span className={option.color}>{option.label}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="location">Standort</Label>
            <Input
              id="location"
              value={formData.location || 'Ludwigsburg'}
              readOnly
              disabled
              className="bg-gray-100 text-gray-600 cursor-not-allowed"
              placeholder="Standort (unveränderbar)"
            />
          </div>
        </div>

        {/* Kategorie, betroffenes System und Zugewiesen an in 3 Spalten */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="category">Kategorie</Label>
            <Select 
              value={formData.category} 
              onValueChange={(value) => handleInputChange('category', value)}
              disabled={kategorienLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={kategorienLoading ? "Lade Kategorien..." : "Kategorie wählen"} />
              </SelectTrigger>
              <SelectContent>
                {categoryOptions.map((category, index) => {
                  if (!category || typeof category !== 'string') {
                    return null;
                  }
                  return (
                    <SelectItem key={`category-${index}-${category}`} value={category}>
                      {category}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="affected_system">Betroffenes System</Label>
            <Select 
              value={formData.affected_system} 
              onValueChange={(value) => handleInputChange('affected_system', value)}
              disabled={!formData.category && !formData.affected_system}
            >
              <SelectTrigger>
                <SelectValue 
                  placeholder={
                    !formData.category && !formData.affected_system 
                      ? "Erst Kategorie wählen" 
                      : "System wählen"
                  } 
                />
              </SelectTrigger>
              <SelectContent>
                {/* Wenn ein affected_system aus der DB existiert, aber nicht in den aktuellen Optionen ist, füge es hinzu */}
                {formData.affected_system && 
                 !affectedSystemOptions.includes(formData.affected_system) && (
                  <SelectItem key={`current-system-${formData.affected_system}`} value={formData.affected_system}>
                    {formData.affected_system} (Aktuell)
                  </SelectItem>
                )}
                {affectedSystemOptions.map((system, index) => {
                  if (!system || typeof system !== 'string') {
                    return null;
                  }
                  return (
                    <SelectItem key={`system-${index}-${system}`} value={system}>
                      {system}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="assigned_to">Zugewiesen an</Label>
            <div className="relative">
              <Input
                id="assigned_to"
                value={bereitschaftsperson ? bereitschaftsperson.name : (formData.assigned_to || '')}
                readOnly
                disabled
                className="bg-gray-100 text-gray-600 cursor-not-allowed pr-8"
                placeholder="Wird automatisch zugewiesen"
              />
              {bereitschaftsLoading && (
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                </div>
              )}
            </div>
            {bereitschaftsperson && (
              <p className="text-xs text-gray-500 mt-1">
                Bereitschaft zur Zeit der Störungserstellung (KW {new Date(stoerung.created_at).toLocaleDateString('de-DE', { 
                  year: 'numeric', 
                  month: '2-digit', 
                  day: '2-digit'
                })})
              </p>
            )}
          </div>
        </div>

        {/* Lösungsschritte */}
        <div className="space-y-2">
          <Label htmlFor="resolution_steps">Lösungsschritte</Label>
          <Textarea
            id="resolution_steps"
            value={formData.resolution_steps || ''}
            onChange={(e) => handleInputChange('resolution_steps', e.target.value)}
            placeholder="Durchgeführte oder geplante Lösungsschritte"
            rows={3}
          />
        </div>

        {/* Grundursache */}
        <div className="space-y-2">
          <Label htmlFor="root_cause">Grundursache</Label>
          <Textarea
            id="root_cause"
            value={formData.root_cause || ''}
            onChange={(e) => handleInputChange('root_cause', e.target.value)}
            placeholder="Identifizierte Grundursache der Störung"
            rows={2}
          />
        </div>

        {/* Lessons Learned */}
        <div className="space-y-2">
          <Label htmlFor="lessons_learned">Lessons Learned</Label>
          <Textarea
            id="lessons_learned"
            value={formData.lessons_learned || ''}
            onChange={(e) => handleInputChange('lessons_learned', e.target.value)}
            placeholder="Erkenntnisse und Verbesserungsvorschläge"
            rows={2}
          />
        </div>

        {/* Bestehende Anhänge und neuer Upload in einem Grid Layout */}
        <div className="space-y-4">
          <Label className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            Bilder verwalten
          </Label>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Linke Seite: Bestehende Anhänge */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Bestehende Bilder ({existingAttachments.length})
              </h4>
              {existingAttachments.length > 0 ? (
                <div className="grid grid-cols-2 gap-3">
                  {existingAttachments.map((attachment) => (
                    <div key={attachment.id} className="relative group">
                      <div className="aspect-square rounded-lg overflow-hidden border border-gray-200">
                        <img
                          src={`/api/stoerungen/attachments/${attachment.id}/file`}
                          alt={attachment.filename || 'Störungsbild'}
                          className="w-full h-full object-contain bg-gray-50"
                          onError={(e) => {
                            // Fallback: versuche andere URL-Strukturen
                            const target = e.target as HTMLImageElement;
                            if (target.src.includes('/api/stoerungen/attachments/')) {
                              target.src = `/api/uploads/stoerungen/${attachment.stored_name || attachment.filename}`;
                            }
                          }}
                        />
                      </div>
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => handleDeleteAttachment(attachment.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                      <p className="text-xs text-gray-600 mt-1 truncate" title={attachment.filename || 'Störungsbild'}>
                        {attachment.filename || 'Unbekanntes Bild'}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                  <ImageIcon className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p>Keine Bilder vorhanden</p>
                </div>
              )}
            </div>
            
            {/* Rechte Seite: Neuer Upload */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Weitere Bilder zu der Störung hinzufügen
              </h4>
              <FileUploadStoerung
                acceptedFileTypes={['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']}
                maxFileSize={5 * 1024 * 1024} // 5MB
                onUploadSuccess={(file) => setUploadedImages(prev => [...prev, file])}
                onUploadError={(error) => {
                  console.error('Upload error:', error);
                  showToast(
                    'Upload-Fehler',
                    error.message,
                    { type: 'error', duration: 4000 }
                  );
                }}
                uploadDelay={0} // Sofortiger Upload ohne Simulation
                className="h-full min-h-[240px]"
              />
              
              {/* Vorschau der neuen Uploads */}
              {uploadedImages.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">
                    Neue Uploads ({uploadedImages.length})
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {uploadedImages.map((file, index) => (
                      <div key={index} className="relative group">
                        <div className="w-16 h-16 rounded overflow-hidden border border-gray-200">
                          <img
                            src={URL.createObjectURL(file)}
                            alt={file.name}
                            className="w-full h-full object-contain bg-gray-50"
                          />
                        </div>
                        <button
                          type="button"
                          onClick={() => setUploadedImages(prev => prev.filter((_, i) => i !== index))}
                          className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Submit buttons */}
        <div className="flex gap-2 pt-4">
          <Button type="submit" variant="accept" disabled={loading} className="">
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Speichere...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Änderungen speichern
              </>
            )}
          </Button>
          <Button type="button" variant="ghost" onClick={onClose}>
            Abbrechen
          </Button>
        </div>
      </form>
        </div>
      </div>
    </div>
  );
};