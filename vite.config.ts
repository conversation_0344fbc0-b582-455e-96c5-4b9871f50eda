import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  // Wichtig für Electron: relative Asset-URLs statt "/assets/..." unter file://
  base: './',
  plugins: [
    react({
      // Standard-Konfiguration für React 18.2.0
      jsxRuntime: 'automatic',
      babel: {
        plugins: [
          '@emotion/babel-plugin'
        ]
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      // Wichtige Polyfills für Electron
      'path': 'path-browserify',
      'stream': 'stream-browserify',
      'util': 'util',
      'buffer': 'buffer/'
    }
  },
  server: {
    port: 3000,
    host: true,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    sourcemap: true,
    rollupOptions: {
      external: ['better-sqlite3']
    }
  },
  define: {
    'process.platform': JSON.stringify(process.platform),
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },
  optimizeDeps: {
    exclude: ['better-sqlite3'],
    include: ['react', 'react-dom']
  }
});
