import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import jaszLogo from '@/assets/jasz-logo2.png';
import { 
  useAskJasz, 
  AskJaszContextData, 
  AskJaszPromptTemplate,
  PROMPT_TEMPLATES,
  generatePrompt
} from '@/contexts/AskJaszContext';

interface AskJaszButtonProps {
  context: AskJaszContextData;
  template?: AskJaszPromptTemplate;
  customPrompt?: string;
  position?: 'top-right' | 'bottom-right' | 'top-left' | 'bottom-left' | 'inline';
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'compact';
  className?: string;
  tooltip?: string;
  showLabel?: boolean;
  disabled?: boolean;
  onClick?: () => void;
}

const AskJaszButton: React.FC<AskJaszButtonProps> = ({
  context,
  template,
  customPrompt,
  position = 'top-right',
  size = 'md',
  variant = 'default',
  className = '',
  tooltip,
  showLabel = false,
  disabled = false,
  onClick
}) => {
  const { openChatWithPrompt, isAvailable } = useAskJasz();
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    if (disabled || !isAvailable) return;
    
    if (onClick) {
      onClick();
    }

    setIsLoading(true);
    
    try {
      let prompt: string;
      
      if (customPrompt) {
        prompt = customPrompt;
      } else if (template) {
        prompt = generatePrompt(template, context);
      } else {
        // Auto-select appropriate template based on context type
        const autoTemplate = getAutoTemplate(context.type);
        prompt = generatePrompt(autoTemplate, context);
      }
      
      openChatWithPrompt(prompt, context);
    } catch (error) {
      console.error('Failed to open Ask JASZ chat:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getAutoTemplate = (contextType: string): AskJaszPromptTemplate => {
    switch (contextType) {
      case 'page':
        return PROMPT_TEMPLATES.PAGE_EXPLANATION;
      case 'kpi':
        return PROMPT_TEMPLATES.KPI_EXPLANATION;
      case 'chart':
        return PROMPT_TEMPLATES.CHART_ANALYSIS;
      case 'workflow':
        return PROMPT_TEMPLATES.WORKFLOW_EXPLANATION;
      case 'form':
        return PROMPT_TEMPLATES.FORM_HELP;
      default:
        return PROMPT_TEMPLATES.PAGE_EXPLANATION;
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-8 w-8 p-1';
      case 'lg':
        return 'h-12 w-12 p-2';
      default:
        return 'h-4 w-4 p-0';
    }
  };

  const getLogoSize = () => {
    switch (size) {
      case 'sm':
        return 'h-6 w-6';
      case 'lg':
        return 'h-8 w-8';
      default:
        return 'h-3 w-3';
    }
  };

  const getInfoIconSize = () => {
    switch (size) {
      case 'sm':
        return 'text-xs';
      case 'lg':
        return 'text-sm';
      default:
        return 'text-xs';
    }
  };

  const getPositionClasses = () => {
    if (position === 'inline') return '';
    
    const baseClasses = 'absolute z-10';
    switch (position) {
      case 'top-right':
        return `${baseClasses} top-2 right-2`;
      case 'bottom-right':
        return `${baseClasses} bottom-2 right-2`;
      case 'top-left':
        return `${baseClasses} top-2 left-2`;
      case 'bottom-left':
        return `${baseClasses} bottom-2 left-2`;
      default:
        return `${baseClasses} top-2 right-2`;
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'minimal':
        return 'bg-slate-900 hover:bg-slate-900 border-1 border-[#b59662] hover:border-[#b59662] text-[#b59662] hover:text-orange-700 shadow-sm hover:shadow-md';
      case 'compact':
        return 'bg-slate-900 hover:bg-slate-900 border-1 border-[#b59662] hover:border-[#b59662] text-[#b59662] hover:text-orange-700 shadow-sm hover:shadow-md';
      default:
        return 'bg-slate-900 hover:bg-slate-900 border-1 border-[#b59662] hover:border-[#b59662] text-[#b59662] hover:text-orange-700 shadow-sm hover:shadow-md';
    }
  };

  const getTooltipText = () => {
    if (tooltip) return tooltip;
    if (!isAvailable) return 'JASZ ist nicht verfügbar';
    
    return `Ask JASZ über ${context.title}`;
  };
  if (!isAvailable) {
    return null;
  }

  const buttonContent = (
    <Button
      onClick={handleClick}
      disabled={disabled || isLoading}
      className={cn(
        'group relative transition-all duration-300 ease-in-out transform hover:scale-300 focus:scale-300',
        getSizeClasses(),
        getPositionClasses(),
        getVariantClasses(),
        disabled && 'opacity-50 cursor-not-allowed hover:scale-100',
        className
      )}
    >
      {/* Goldenes "i" - Standard-Zustand */}
      <div className={cn(
        'flex items-center justify-center font-bold text-[#b59662] transition-all duration-300',
        getInfoIconSize(),
        'group-hover:opacity-0 group-hover:scale-0'
      )}>
        i
      </div>
      
      {/* JASZ Logo - Hover-Zustand */}
      <img 
        src={jaszLogo} 
        alt="JASZ" 
        className={cn(
          'absolute inset-0 m-auto transition-all duration-300',
          getLogoSize(),
          'opacity-0 scale-0 group-hover:opacity-100 group-hover:scale-100',
          isLoading && 'animate-pulse',
          'group-hover:rotate-12'
        )}
      />
      
      {/* Glow effect on hover */}
      <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-slate-900 to-slate-900 opacity-0 transition-opacity duration-300 pointer-events-none" />
      
      {/* Ring effect on focus/hover */}
    </Button>
  );

  // ALL Ask JASZ buttons now ALWAYS have tooltips
  return (
    <Tooltip>

      <TooltipTrigger asChild>
        {buttonContent}
      </TooltipTrigger>
      <TooltipContent 
        side="top" 
        className="bg-gradient-to-r from-gray-900 to-gray-800 text-white border-2 border-[#b59662] shadow-xl"
        sideOffset={8}
      >
        <div className="text-center">
          <div className="flex items-center gap-2 font-semibold text-[#b59662]">
            <img src={jaszLogo} alt="JASZ" className="h-4 w-4" />
            Ask JASZ
          </div>
          <div className="text-xs text-gray-300 mt-1 max-w-48">
            {getTooltipText()}
          </div>
        </div>
      </TooltipContent>
    </Tooltip>
  );
};

export default AskJaszButton;