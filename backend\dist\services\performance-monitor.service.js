"use strict";
/**
 * Performance Monitoring Service
 *
 * Erweiterte Performance-Metriken für Cache, Datenbank und API
 * mit detaillierten Analysen und Alerting-Funktionalität.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.performanceMonitor = exports.QueryTracker = exports.PerformanceMonitorService = void 0;
class PerformanceMonitorService {
    constructor() {
        // Metric Storage
        this.queryMetrics = [];
        this.alerts = [];
        this.hourlyMetrics = new Map();
        // Configuration
        this.config = {
            slowQueryThreshold: 1000, // ms
            highMemoryThreshold: 100, // MB
            cacheMissThresholdRate: 30, // %
            errorRateThreshold: 5, // %
            maxStoredQueries: 1000,
            maxStoredAlerts: 100,
            monitoringInterval: 60000, // 1 minute
        };
        // Monitoring State
        this.monitoringTimer = null;
        this.lastMetricsSnapshot = null;
        this.startMonitoring();
    }
    static getInstance() {
        if (!PerformanceMonitorService.instance) {
            PerformanceMonitorService.instance = new PerformanceMonitorService();
        }
        return PerformanceMonitorService.instance;
    }
    /**
     * Query-Performance tracken
     */
    trackQuery(queryId, queryType) {
        const metrics = {
            queryId,
            queryType,
            startTime: Date.now(),
            cacheHit: false,
            errorOccurred: false
        };
        return new QueryTracker(metrics, (completedMetrics) => {
            this.recordQueryMetrics(completedMetrics);
        });
    }
    /**
     * Cache-Hit registrieren
     */
    recordCacheHit(queryId, dataSize) {
        const metrics = this.queryMetrics.find(m => m.queryId === queryId);
        if (metrics) {
            metrics.cacheHit = true;
            metrics.dataSize = dataSize;
            metrics.endTime = Date.now();
            metrics.duration = metrics.endTime - metrics.startTime;
        }
    }
    /**
     * Query-Fehler registrieren
     */
    recordQueryError(queryId, errorType) {
        const metrics = this.queryMetrics.find(m => m.queryId === queryId);
        if (metrics) {
            metrics.errorOccurred = true;
            metrics.errorType = errorType;
            metrics.endTime = Date.now();
            metrics.duration = metrics.endTime - metrics.startTime;
        }
    }
    /**
     * Detaillierte Cache-Metriken abrufen
     */
    getDetailedCacheMetrics() {
        const recentQueries = this.getRecentQueries(24 * 60 * 60 * 1000); // 24 Stunden
        // Basic Metrics
        const totalQueries = recentQueries.length;
        const cacheHits = recentQueries.filter(q => q.cacheHit).length;
        const hitRate = totalQueries > 0 ? (cacheHits / totalQueries) * 100 : 0;
        // Performance Metrics
        const queryDurations = recentQueries.filter(q => q.duration).map(q => q.duration);
        const avgResponseTime = queryDurations.length > 0
            ? queryDurations.reduce((sum, d) => sum + d, 0) / queryDurations.length
            : 0;
        const slowQueries = queryDurations.filter(d => d > this.config.slowQueryThreshold).length;
        // Time-based Metrics
        const last24HourMetrics = this.calculateHourlyMetrics(recentQueries);
        // Query-specific Metrics
        const queryTypeMetrics = this.calculateQueryTypeMetrics(recentQueries);
        // Health Score
        const healthScore = this.calculateHealthScore({
            hitRate,
            avgResponseTime,
            slowQueries,
            totalQueries,
            errorRate: this.calculateErrorRate(recentQueries)
        });
        // Recommendations
        const recommendations = this.generateRecommendations({
            hitRate,
            avgResponseTime,
            slowQueries,
            totalQueries
        });
        return {
            hits: cacheHits,
            misses: totalQueries - cacheHits,
            totalRequests: totalQueries,
            hitRate,
            avgResponseTime,
            slowQueries,
            cacheMemoryUsage: 0, // Wird von Cache Service bereitgestellt
            evictions: 0, // Wird von Cache Service bereitgestellt
            last24HourMetrics,
            queryTypeMetrics,
            healthScore,
            recommendations
        };
    }
    /**
     * Database-Performance-Metriken
     */
    getDatabaseMetrics() {
        const recentQueries = this.getRecentQueries(60 * 60 * 1000); // 1 Stunde
        const nonCacheQueries = recentQueries.filter(q => !q.cacheHit);
        const durations = nonCacheQueries.filter(q => q.duration).map(q => q.duration);
        const avgQueryTime = durations.length > 0
            ? durations.reduce((sum, d) => sum + d, 0) / durations.length
            : 0;
        const slowQueries = durations.filter(d => d > this.config.slowQueryThreshold).length;
        const errors = nonCacheQueries.filter(q => q.errorOccurred).length;
        const errorRate = nonCacheQueries.length > 0 ? (errors / nonCacheQueries.length) * 100 : 0;
        // Query Type Breakdown
        const queryTypeBreakdown = {};
        nonCacheQueries.forEach(q => {
            queryTypeBreakdown[q.queryType] = (queryTypeBreakdown[q.queryType] || 0) + 1;
        });
        return {
            totalQueries: nonCacheQueries.length,
            avgQueryTime,
            slowQueries,
            activeConnections: 1, // Prisma verwaltet Connections automatisch
            queryTypeBreakdown,
            errorRate,
            connectionErrors: errors
        };
    }
    /**
     * System-Performance-Metriken
     */
    getSystemMetrics() {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        return {
            memoryUsage: {
                heapUsed: memUsage.heapUsed,
                heapTotal: memUsage.heapTotal,
                external: memUsage.external,
                rss: memUsage.rss
            },
            cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000, // Convert to ms
            uptime: process.uptime(),
            eventLoopLag: this.measureEventLoopLag()
        };
    }
    /**
     * Performance-Alerts abrufen
     */
    getAlerts(severity) {
        let alerts = this.alerts;
        if (severity) {
            alerts = alerts.filter(alert => alert.severity === severity);
        }
        return alerts.slice(-this.config.maxStoredAlerts);
    }
    /**
     * Performance-Bericht generieren
     */
    generatePerformanceReport() {
        const cache = this.getDetailedCacheMetrics();
        const database = this.getDatabaseMetrics();
        const system = this.getSystemMetrics();
        const alerts = this.getAlerts();
        const criticalAlerts = alerts.filter(a => a.severity === 'critical');
        const criticalIssues = criticalAlerts.map(a => a.message);
        const overallHealthScore = Math.min(cache.healthScore, this.calculateDatabaseHealthScore(database), this.calculateSystemHealthScore(system));
        return {
            cache,
            database,
            system,
            alerts,
            summary: {
                overallHealthScore,
                criticalIssues,
                topRecommendations: cache.recommendations.slice(0, 3)
            }
        };
    }
    /**
     * Performance-Monitoring starten
     */
    startMonitoring() {
        this.monitoringTimer = setInterval(() => {
            this.performMonitoringCheck();
        }, this.config.monitoringInterval);
    }
    /**
     * Monitoring-Check durchführen
     */
    performMonitoringCheck() {
        const metrics = this.getDetailedCacheMetrics();
        const dbMetrics = this.getDatabaseMetrics();
        const sysMetrics = this.getSystemMetrics();
        // Check for alerts
        this.checkForSlowQueries(metrics);
        this.checkForHighMemoryUsage(sysMetrics);
        this.checkForCacheMissSpike(metrics);
        this.checkForErrorSpike(dbMetrics);
        // Store hourly metrics
        this.storeHourlyMetrics(metrics, dbMetrics, sysMetrics);
        // Cleanup old data
        this.cleanupOldData();
    }
    /**
     * Private Helper Methods
     */
    recordQueryMetrics(metrics) {
        this.queryMetrics.push(metrics);
        // Limit stored metrics
        if (this.queryMetrics.length > this.config.maxStoredQueries) {
            this.queryMetrics = this.queryMetrics.slice(-this.config.maxStoredQueries);
        }
    }
    getRecentQueries(timeWindowMs) {
        const cutoff = Date.now() - timeWindowMs;
        return this.queryMetrics.filter(q => q.startTime >= cutoff);
    }
    calculateHourlyMetrics(queries) {
        const hourlyData = Array(24).fill(null).map(() => ({
            hitRate: 0,
            requestCount: 0,
            responseTime: 0
        }));
        queries.forEach(q => {
            const hour = new Date(q.startTime).getHours();
            hourlyData[hour].requestCount++;
            if (q.cacheHit)
                hourlyData[hour].hitRate++;
            if (q.duration)
                hourlyData[hour].responseTime += q.duration;
        });
        // Calculate averages
        hourlyData.forEach(data => {
            if (data.requestCount > 0) {
                data.hitRate = (data.hitRate / data.requestCount) * 100;
                data.responseTime = data.responseTime / data.requestCount;
            }
        });
        return {
            hourlyHitRates: hourlyData.map(d => d.hitRate),
            hourlyRequestCounts: hourlyData.map(d => d.requestCount),
            hourlyResponseTimes: hourlyData.map(d => d.responseTime)
        };
    }
    calculateQueryTypeMetrics(queries) {
        const typeMetrics = {};
        queries.forEach(q => {
            if (!typeMetrics[q.queryType]) {
                typeMetrics[q.queryType] = {
                    hits: 0,
                    total: 0,
                    totalTime: 0
                };
            }
            typeMetrics[q.queryType].total++;
            if (q.cacheHit)
                typeMetrics[q.queryType].hits++;
            if (q.duration)
                typeMetrics[q.queryType].totalTime += q.duration;
        });
        // Calculate final metrics
        Object.keys(typeMetrics).forEach(type => {
            const metrics = typeMetrics[type];
            metrics.hitRate = (metrics.hits / metrics.total) * 100;
            metrics.avgResponseTime = metrics.totalTime / metrics.total;
            metrics.requestCount = metrics.total;
            delete metrics.hits;
            delete metrics.total;
            delete metrics.totalTime;
        });
        return typeMetrics;
    }
    calculateErrorRate(queries) {
        const errors = queries.filter(q => q.errorOccurred).length;
        return queries.length > 0 ? (errors / queries.length) * 100 : 0;
    }
    calculateHealthScore(metrics) {
        let score = 100;
        // Hit rate impact (30% weight)
        if (metrics.hitRate < 50)
            score -= 30;
        else if (metrics.hitRate < 70)
            score -= 15;
        else if (metrics.hitRate < 85)
            score -= 5;
        // Response time impact (25% weight)
        if (metrics.avgResponseTime > 2000)
            score -= 25;
        else if (metrics.avgResponseTime > 1000)
            score -= 15;
        else if (metrics.avgResponseTime > 500)
            score -= 5;
        // Slow queries impact (20% weight)
        const slowQueryRate = metrics.totalQueries > 0 ? (metrics.slowQueries / metrics.totalQueries) * 100 : 0;
        if (slowQueryRate > 20)
            score -= 20;
        else if (slowQueryRate > 10)
            score -= 10;
        else if (slowQueryRate > 5)
            score -= 5;
        // Error rate impact (25% weight)
        if (metrics.errorRate > 10)
            score -= 25;
        else if (metrics.errorRate > 5)
            score -= 15;
        else if (metrics.errorRate > 2)
            score -= 5;
        return Math.max(0, score);
    }
    generateRecommendations(metrics) {
        const recommendations = [];
        if (metrics.hitRate < 70) {
            recommendations.push('Cache Hit Rate ist niedrig. Erwägen Sie längere TTL-Zeiten oder bessere Cache-Strategien.');
        }
        if (metrics.avgResponseTime > 1000) {
            recommendations.push('Durchschnittliche Response-Zeit ist hoch. Überprüfen Sie Datenbankindizes und Query-Optimierung.');
        }
        if (metrics.slowQueries > metrics.totalQueries * 0.1) {
            recommendations.push('Viele langsame Queries erkannt. Analysieren Sie Query-Performance und Datenbankoptimierung.');
        }
        if (metrics.totalQueries < 10) {
            recommendations.push('Wenige Queries erkannt. Monitoring-Zeitraum möglicherweise zu kurz für aussagekräftige Metriken.');
        }
        return recommendations;
    }
    calculateDatabaseHealthScore(metrics) {
        let score = 100;
        if (metrics.avgQueryTime > 2000)
            score -= 30;
        else if (metrics.avgQueryTime > 1000)
            score -= 15;
        if (metrics.errorRate > 5)
            score -= 25;
        else if (metrics.errorRate > 2)
            score -= 10;
        const slowQueryRate = metrics.totalQueries > 0 ? (metrics.slowQueries / metrics.totalQueries) * 100 : 0;
        if (slowQueryRate > 10)
            score -= 20;
        return Math.max(0, score);
    }
    calculateSystemHealthScore(metrics) {
        let score = 100;
        const memUsagePercent = (metrics.memoryUsage.heapUsed / metrics.memoryUsage.heapTotal) * 100;
        if (memUsagePercent > 90)
            score -= 30;
        else if (memUsagePercent > 80)
            score -= 15;
        if (metrics.eventLoopLag > 100)
            score -= 20;
        else if (metrics.eventLoopLag > 50)
            score -= 10;
        return Math.max(0, score);
    }
    checkForSlowQueries(metrics) {
        if (metrics.slowQueries > metrics.totalRequests * 0.2) {
            this.addAlert({
                type: 'slow_query',
                severity: 'high',
                message: `Hohe Anzahl langsamer Queries: ${metrics.slowQueries} von ${metrics.totalRequests}`,
                timestamp: Date.now(),
                metrics: { slowQueries: metrics.slowQueries, totalRequests: metrics.totalRequests }
            });
        }
    }
    checkForHighMemoryUsage(metrics) {
        const memUsageMB = metrics.memoryUsage.heapUsed / (1024 * 1024);
        if (memUsageMB > this.config.highMemoryThreshold) {
            this.addAlert({
                type: 'high_memory',
                severity: 'medium',
                message: `Hoher Memory-Verbrauch: ${memUsageMB.toFixed(1)}MB`,
                timestamp: Date.now(),
                metrics: { memoryUsage: memUsageMB }
            });
        }
    }
    checkForCacheMissSpike(metrics) {
        if (metrics.hitRate < (100 - this.config.cacheMissThresholdRate)) {
            this.addAlert({
                type: 'cache_miss_spike',
                severity: 'medium',
                message: `Cache Hit Rate niedrig: ${metrics.hitRate.toFixed(1)}%`,
                timestamp: Date.now(),
                metrics: { hitRate: metrics.hitRate }
            });
        }
    }
    checkForErrorSpike(metrics) {
        if (metrics.errorRate > this.config.errorRateThreshold) {
            this.addAlert({
                type: 'error_spike',
                severity: 'high',
                message: `Hohe Error Rate: ${metrics.errorRate.toFixed(1)}%`,
                timestamp: Date.now(),
                metrics: { errorRate: metrics.errorRate }
            });
        }
    }
    addAlert(alert) {
        this.alerts.push(alert);
        if (this.alerts.length > this.config.maxStoredAlerts) {
            this.alerts = this.alerts.slice(-this.config.maxStoredAlerts);
        }
        console.warn(`[PERFORMANCE-ALERT] ${alert.severity.toUpperCase()}: ${alert.message}`);
    }
    storeHourlyMetrics(cache, db, sys) {
        const hour = new Date().toISOString().slice(0, 13); // YYYY-MM-DDTHH
        this.hourlyMetrics.set(hour, { cache, db, sys, timestamp: Date.now() });
        // Keep only last 48 hours
        const cutoff = Date.now() - (48 * 60 * 60 * 1000);
        for (const [key, value] of this.hourlyMetrics.entries()) {
            if (value.timestamp < cutoff) {
                this.hourlyMetrics.delete(key);
            }
        }
    }
    cleanupOldData() {
        const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
        this.queryMetrics = this.queryMetrics.filter(q => q.startTime >= cutoff);
        this.alerts = this.alerts.filter(a => a.timestamp >= cutoff);
    }
    measureEventLoopLag() {
        const start = Date.now();
        setImmediate(() => {
            const lag = Date.now() - start;
            return lag;
        });
        return 0; // Simplified for this implementation
    }
    /**
     * Service beenden
     */
    destroy() {
        if (this.monitoringTimer) {
            clearInterval(this.monitoringTimer);
            this.monitoringTimer = null;
        }
    }
}
exports.PerformanceMonitorService = PerformanceMonitorService;
/**
 * Query Tracker für einzelne Queries
 */
class QueryTracker {
    constructor(metrics, onComplete) {
        this.metrics = metrics;
        this.onComplete = onComplete;
    }
    recordCacheHit(dataSize) {
        this.metrics.cacheHit = true;
        this.metrics.dataSize = dataSize;
        this.complete();
    }
    recordCacheMiss() {
        this.metrics.cacheHit = false;
        // Don't complete here, wait for query to finish
    }
    recordError(errorType) {
        this.metrics.errorOccurred = true;
        this.metrics.errorType = errorType;
        this.complete();
    }
    complete(dataSize) {
        this.metrics.endTime = Date.now();
        this.metrics.duration = this.metrics.endTime - this.metrics.startTime;
        if (dataSize)
            this.metrics.dataSize = dataSize;
        this.onComplete(this.metrics);
    }
}
exports.QueryTracker = QueryTracker;
// Singleton instance
exports.performanceMonitor = PerformanceMonitorService.getInstance();
