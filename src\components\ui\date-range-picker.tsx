"use client"

import React from "react"
import { DateRange } from "react-day-picker"
import ElegantDatePicker from "@/components/customize/DatePicker/ElegantDatePicker"
import { cn } from "@/lib/utils"

interface DateRangePickerProps {
  value?: DateRange
  onChange?: (range: DateRange | undefined) => void
  className?: string
  label?: string
  description?: string
  title?: string
  headerText?: string
  headerDescription?: string
}

export function DateRangePicker({ 
  value, 
  onChange, 
  className,
  label = "Datumsbereich",
  description,
  title,
  headerText,
  headerDescription
}: DateRangePickerProps) {
  const handleRangeChange = (range: { from?: Date; to?: Date } | undefined) => {
    if (range) {
      onChange?.({ from: range.from, to: range.to } as DateRange);
    } else {
      onChange?.(undefined);
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      <ElegantDatePicker
        rangeValue={value}
        onRangeChange={handleRangeChange}
        placeholder="Zeitraum auswählen"
        label={label}
        description={description}
        title={title}
        headerText={headerText}
        headerDescription={headerDescription}
        showHeader={false}
        className={className}
      />
    </div>
  )
}