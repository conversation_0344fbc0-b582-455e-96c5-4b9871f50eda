import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AIDocumentationProvider, useAIDocumentation } from '../AIDocumentationProvider';
import { AIHelpSystem } from '../AIHelpSystem';
import { AITutorialGuide } from '../AITutorialGuide';
import { AIDocumentationViewer } from '../AIDocumentationViewer';
import { AIDocumentationService } from '../../../services/documentation/AIDocumentationService';

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>
  },
  AnimatePresence: ({ children }: any) => <>{children}</>
}));

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, options?: any) => {
      if (options?.count !== undefined) {
        return `${key}_${options.count}`;
      }
      return key;
    }
  })
}));

// Mock UI components
vi.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} {...props}>
      {children}
    </button>
  )
}));

vi.mock('@/components/ui/input', () => ({
  Input: (props: any) => <input {...props} />
}));

vi.mock('@/components/ui/card', () => ({
  Card: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  CardHeader: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  CardTitle: ({ children, ...props }: any) => <h3 {...props}>{children}</h3>
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children, ...props }: any) => <span {...props}>{children}</span>
}));

vi.mock('@/components/ui/tabs', () => ({
  Tabs: ({ children, value, onValueChange }: any) => (
    <div data-testid="tabs" data-value={value}>
      {children}
    </div>
  ),
  TabsList: ({ children }: any) => <div data-testid="tabs-list">{children}</div>,
  TabsTrigger: ({ children, value, onClick }: any) => (
    <button data-testid={`tab-${value}`} onClick={onClick}>
      {children}
    </button>
  ),
  TabsContent: ({ children, value }: any) => (
    <div data-testid={`tab-content-${value}`}>{children}</div>
  )
}));

vi.mock('@/components/ui/select', () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <div data-testid="select" data-value={value}>
      {children}
    </div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value, onClick }: any) => (
    <button data-testid={`select-item-${value}`} onClick={onClick}>
      {children}
    </button>
  ),
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>
}));

vi.mock('@/components/ui/progress', () => ({
  Progress: ({ value, ...props }: any) => (
    <div data-testid="progress" data-value={value} {...props} />
  )
}));

// Test component that uses all documentation features
const TestDocumentationApp: React.FC = () => {
  return (
    <AIDocumentationProvider>
      <div>
        <AIHelpSystem />
        <AITutorialGuide />
        <AIDocumentationViewer />
      </div>
    </AIDocumentationProvider>
  );
};

describe('AI Documentation System Integration', () => {
  let documentationService: AIDocumentationService;

  beforeEach(async () => {
    vi.clearAllMocks();
    documentationService = new AIDocumentationService();
    await documentationService.initialize();
  });

  afterEach(async () => {
    await documentationService.cleanup();
  });

  describe('complete documentation workflow', () => {
    it('should render all documentation components', () => {
      render(<TestDocumentationApp />);

      // Help system should be available
      expect(screen.getByTitle('ai.help.toggle')).toBeInTheDocument();
    });

    it('should handle help system workflow', async () => {
      render(<TestDocumentationApp />);

      // Open help system
      const helpToggle = screen.getByTitle('ai.help.toggle');
      fireEvent.click(helpToggle);

      await waitFor(() => {
        expect(screen.getByText('ai.help.title')).toBeInTheDocument();
      });

      // Should show search input
      expect(screen.getByPlaceholderText('ai.help.search.placeholder')).toBeInTheDocument();

      // Should show tabs
      expect(screen.getByTestId('tabs')).toBeInTheDocument();
    });

    it('should handle search functionality across components', async () => {
      render(<TestDocumentationApp />);

      // Open help system
      const helpToggle = screen.getByTitle('ai.help.toggle');
      fireEvent.click(helpToggle);

      await waitFor(() => {
        const searchInput = screen.getByPlaceholderText('ai.help.search.placeholder');
        fireEvent.change(searchInput, { target: { value: 'AI optimization' } });
        expect(searchInput).toHaveValue('AI optimization');
      });
    });

    it('should handle tutorial workflow', async () => {
      // This test would need mock tutorial data to properly test the tutorial flow
      render(<TestDocumentationApp />);

      // The tutorial guide should not be visible initially (no current tutorial)
      expect(screen.queryByText('ai.tutorial.step')).not.toBeInTheDocument();
    });

    it('should handle documentation viewer', () => {
      render(
        <AIDocumentationProvider>
          <AIDocumentationViewer />
        </AIDocumentationProvider>
      );

      // Should show documentation title
      expect(screen.getByText('ai.documentation.title')).toBeInTheDocument();
      expect(screen.getByText('ai.documentation.subtitle')).toBeInTheDocument();

      // Should show search and filters
      expect(screen.getByPlaceholderText('ai.documentation.search.placeholder')).toBeInTheDocument();
    });
  });

  describe('service integration', () => {
    it('should integrate with documentation service', async () => {
      const sections = await documentationService.getAllSections();
      const tutorials = await documentationService.getAllTutorials();

      expect(sections).toBeDefined();
      expect(tutorials).toBeDefined();
      expect(Array.isArray(sections)).toBe(true);
      expect(Array.isArray(tutorials)).toBe(true);
    });

    it('should handle search across service and UI', async () => {
      const searchResults = await documentationService.searchDocumentation('AI');
      expect(Array.isArray(searchResults)).toBe(true);
    });

    it('should handle tutorial recommendations', async () => {
      const recommendations = await documentationService.getRecommendedTutorials('beginner', []);
      expect(Array.isArray(recommendations)).toBe(true);
    });

    it('should track user interactions', async () => {
      await documentationService.trackTutorialCompletion('test-tutorial');
      await documentationService.trackSectionView('test-section');

      const metrics = await documentationService.getDocumentationMetrics();
      expect(metrics).toBeDefined();
      expect(typeof metrics.completionRate).toBe('number');
    });
  });

  describe('error handling integration', () => {
    it('should handle service errors gracefully', async () => {
      // Mock service error
      const mockService = {
        ...documentationService,
        searchDocumentation: vi.fn().mockRejectedValue(new Error('Service error'))
      };

      // The UI should handle service errors gracefully
      expect(() => {
        render(<TestDocumentationApp />);
      }).not.toThrow();
    });

    it('should handle missing data gracefully', () => {
      // Test with empty provider
      render(
        <AIDocumentationProvider>
          <AIDocumentationViewer />
        </AIDocumentationProvider>
      );

      // Should show no results message
      expect(screen.getByText('ai.documentation.noResults.title')).toBeInTheDocument();
    });
  });

  describe('accessibility integration', () => {
    it('should provide proper ARIA labels', async () => {
      render(<TestDocumentationApp />);

      const helpToggle = screen.getByTitle('ai.help.toggle');
      expect(helpToggle).toHaveAttribute('title');
    });

    it('should support keyboard navigation', async () => {
      render(<TestDocumentationApp />);

      const helpToggle = screen.getByTitle('ai.help.toggle');
      
      // Should be focusable
      helpToggle.focus();
      expect(document.activeElement).toBe(helpToggle);
    });
  });

  describe('performance integration', () => {
    it('should handle large datasets efficiently', async () => {
      // Should render without performance issues
      const startTime = Date.now();
      render(
        <AIDocumentationProvider>
          <AIDocumentationViewer />
        </AIDocumentationProvider>
      );
      const renderTime = Date.now() - startTime;

      // Should render within reasonable time (less than 1 second)
      expect(renderTime).toBeLessThan(1000);
    });

    it('should handle search efficiently', async () => {
      const startTime = Date.now();
      await documentationService.searchDocumentation('test query');
      const searchTime = Date.now() - startTime;

      // Search should complete quickly (less than 100ms)
      expect(searchTime).toBeLessThan(100);
    });
  });

  describe('localization integration', () => {
    it('should handle German localization', () => {
      render(<TestDocumentationApp />);

      // All text should go through translation function
      // This is verified by the mock returning the key
      expect(screen.getByTitle('ai.help.toggle')).toBeInTheDocument();
    });

    it('should handle missing translations gracefully', () => {
      // Test that the app renders without crashing even with missing translations
      render(<TestDocumentationApp />);

      // Should still render without crashing
      expect(screen.getByTitle('ai.help.toggle')).toBeInTheDocument();
    });
  });

  describe('state management integration', () => {
    it('should maintain state across components', async () => {
      render(<TestDocumentationApp />);

      // Open help system
      const helpToggle = screen.getByTitle('ai.help.toggle');
      fireEvent.click(helpToggle);

      await waitFor(() => {
        expect(screen.getByText('ai.help.title')).toBeInTheDocument();
      });

      // State should be maintained when interacting with other components
      const searchInput = screen.getByPlaceholderText('ai.help.search.placeholder');
      fireEvent.change(searchInput, { target: { value: 'test' } });

      expect(searchInput).toHaveValue('test');
    });

    it('should handle context updates', async () => {
      const TestContextComponent: React.FC = () => {
        const { setHelpContext, getContextualHelp } = useAIDocumentation();

        React.useEffect(() => {
          setHelpContext({
            currentPage: 'ai-dashboard',
            userRole: 'administrator',
            availableFeatures: ['rag', 'cutting']
          });
        }, [setHelpContext]);

        const contextualHelp = getContextualHelp();

        return (
          <div data-testid="contextual-help-count">
            {contextualHelp.length}
          </div>
        );
      };

      render(
        <AIDocumentationProvider>
          <TestContextComponent />
        </AIDocumentationProvider>
      );

      expect(screen.getByTestId('contextual-help-count')).toBeInTheDocument();
    });
  });
});