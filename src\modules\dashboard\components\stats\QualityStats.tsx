import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StatCard } from '@/components/ui/stat-card';

/**
 * Qualitätsmanagement-Statistiken Komponente
 * 
 * Zeigt wichtige QM-Kennzahlen als Statistik-Karten an:
 * - QM-Akzeptanzrate
 * - QM-Bearbeitungsrate
 */
export function QualityStats() {
  const { t } = useTranslation();
  const [stats, setStats] = useState({
    acceptanceRate: 0,
    processingRate: 0,
    previousAcceptanceRate: 0,
    previousProcessingRate: 0,
  });
  
  useEffect(() => {
    const loadData = () => {
      try {
        // Diese Daten wurden aus der Datenbank extrahiert
        // In einer echten Implementierung würden wir die Daten aus der Datenbank laden
        const currentData = {
          qm_angenommen: 42,
          qm_abgelehnt: 3,
          qm_offen: 5,
        };
        
        const previousData = {
          qm_angenommen: 38,
          qm_abgelehnt: 5,
          qm_offen: 4,
        };
        
        // Berechne die Kennzahlen
        const totalCurrent = currentData.qm_angenommen + currentData.qm_abgelehnt + currentData.qm_offen;
        const acceptanceRate = totalCurrent > 0 ? (currentData.qm_angenommen / totalCurrent) * 100 : 0;
        const processingRate = totalCurrent > 0 ? 
          ((currentData.qm_angenommen + currentData.qm_abgelehnt) / totalCurrent) * 100 : 0;
        
        const totalPrevious = previousData.qm_angenommen + previousData.qm_abgelehnt + previousData.qm_offen;
        const previousAcceptanceRate = totalPrevious > 0 ? (previousData.qm_angenommen / totalPrevious) * 100 : 0;
        const previousProcessingRate = totalPrevious > 0 ? 
          ((previousData.qm_angenommen + previousData.qm_abgelehnt) / totalPrevious) * 100 : 0;
        
        setStats({
          acceptanceRate,
          processingRate,
          previousAcceptanceRate,
          previousProcessingRate,
        });
      } catch (err) {
        // Error handling without console logging
      }
    };
    
    loadData();
  }, []);
  
  // Berechne die Trends
  const acceptanceTrend = stats.acceptanceRate > stats.previousAcceptanceRate ? 'up' : 
                         stats.acceptanceRate < stats.previousAcceptanceRate ? 'down' : 'neutral';
                       
  const processingTrend = stats.processingRate > stats.previousProcessingRate ? 'up' : 
                         stats.processingRate < stats.previousProcessingRate ? 'down' : 'neutral';
  
  // Berechne die Trendwerte in Prozent
  const acceptanceTrendValue = stats.previousAcceptanceRate > 0 ? 
    `${((stats.acceptanceRate - stats.previousAcceptanceRate) / stats.previousAcceptanceRate * 100).toFixed(1)}%` : '0%';
    
  const processingTrendValue = stats.previousProcessingRate > 0 ? 
    `${((stats.processingRate - stats.previousProcessingRate) / stats.previousProcessingRate * 100).toFixed(1)}%` : '0%';
  
  return (
    <>
      <StatCard
        title={t("qmAcceptanceRate")}
        value={`${stats.acceptanceRate.toFixed(1)}%`}
        description={t("qmAcceptanceRateDescription")}
        trend={acceptanceTrend}
        trendValue={acceptanceTrendValue}
        footer={t("comparedToPreviousDay")}
      />
      <StatCard
        title={t("qmProcessingRate")}
        value={`${stats.processingRate.toFixed(1)}%`}
        description={t("qmProcessingRateDescription")}
        trend={processingTrend}
        trendValue={processingTrendValue}
        footer={t("comparedToPreviousDay")}
      />
    </>
  );
}
