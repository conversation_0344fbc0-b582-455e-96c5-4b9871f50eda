/**
 * Modules Index
 * Central export point for all application modules
 */

import { ModuleConfig } from '@/types/module';

// Module configurations
export { dashboardModuleConfig } from './dashboard/module.config';
export { stoerungenModuleConfig } from './stoerungen/module.config';
export { backendModuleConfig } from './backend/module.config';
export { aiModuleConfig } from './ai/module.config';

// Module exports
export * from './dashboard';
export * from './stoerungen';
export * from './backend';
export * from './ai';

// All available modules
import { dashboardModuleConfig } from './dashboard/module.config';
import { stoerungenModuleConfig } from './stoerungen/module.config';
import { backendModuleConfig } from './backend/module.config';
import { aiModuleConfig } from './ai/module.config';

export const allModules: ModuleConfig[] = [
  dashboardModuleConfig,
  stoerungenModuleConfig,
  backendModuleConfig,
  aiModuleConfig
];

// Helper functions
export const getModuleById = (id: string): ModuleConfig | undefined => {
  return allModules.find(module => module.id === id);
};

export const getModulesByRole = (userRole: string): ModuleConfig[] => {
  return allModules.filter(module => 
    module.isEnabled && module.requiredRoles.includes(userRole as any)
  );
};