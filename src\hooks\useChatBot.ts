import { useState, useCallback } from 'react';

interface UseChatBotReturn {
  isChatOpen: boolean;
  chatMessage: string | undefined;
  openChatWithMessage: (message: string) => void;
  closeChatBot: () => void;
  setChatOpen: (isOpen: boolean) => void;
}

export const useChatBot = (): UseChatBotReturn => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [chatMessage, setChatMessage] = useState<string | undefined>(undefined);

  const openChatWithMessage = useCallback((message: string) => {
    setChatMessage(message);
    setIsChatOpen(true);
  }, []);

  const closeChatBot = useCallback(() => {
    setIsChatOpen(false);
    setChatMessage(undefined);
  }, []);

  const setChatOpen = useCallback((isOpen: boolean) => {
    setIsChatOpen(isOpen);
    if (!isOpen) {
      setChatMessage(undefined);
    }
  }, []);

  return {
    isChatOpen,
    chatMessage,
    openChatWithMessage,
    closeChatBot,
    setChatOpen
  };
};