/**
 * Warehouse Business Service
 * 
 * Service-Layer für Warehouse-spezifische Business Logic.
 * Trennt komplexe Geschäftslogik von den Komponenten und bietet
 * high-level Operationen für Warehouse-Management.
 */

import { BaseService, ServiceConfig } from '../base.service';
import { repositories } from '@/repositories';
import { DateRangeFilter } from '@/repositories/base.repository';

/**
 * Warehouse-Performance-Metriken
 */
export interface WarehousePerformanceMetrics {
  utilizationRate: number;
  throughput: number;
  efficiency: number;
  errorRate: number;
  trend: 'improving' | 'declining' | 'stable';
}

/**
 * Warehouse-Kapazitäts-Analyse
 */
export interface WarehouseCapacityAnalysis {
  currentCapacity: number;
  maxCapacity: number;
  utilizationPercentage: number;
  projectedFullDate?: Date;
  recommendedActions: string[];
}

/**
 * Warehouse-Operations-Dashboard
 */
export interface WarehouseOperationsDashboard {
  atrlMetrics: WarehousePerformanceMetrics;
  arilMetrics: WarehousePerformanceMetrics;
  incomingGoods: {
    totalAtrl: number;
    totalManl: number;
    dailyAverage: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  };
  utilization: {
    warehouse200: WarehouseCapacityAnalysis;
    warehouse240: WarehouseCapacityAnalysis;
  };
  alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    timestamp: Date;
  }>;
}

/**
 * Warehouse Business Service Klasse
 */
export class WarehouseBusinessService extends BaseService {
  readonly serviceName = 'WarehouseBusinessService';
  
  constructor(config?: ServiceConfig) {
    super(config);
  }
  
  /**
   * Service-spezifische Initialisierung
   */
  protected async onInitialize(): Promise<void> {
    // Teste Repository-Verfügbarkeit
    await repositories.warehouse.we.getAll();
    this.log('Warehouse repositories initialized and tested');
  }
  
  /**
   * Health Check für Warehouse-Services
   */
  protected async onHealthCheck(): Promise<void> {
    const healthCheck = await repositories.healthCheck();
    if (healthCheck.status === 'unhealthy') {
      throw new Error(`Repository health check failed: ${healthCheck.errors.join(', ')}`);
    }
  }
  
  /**
   * Gesamte Warehouse-Operations-Dashboard erstellen
   */
  async getOperationsDashboard(filter?: DateRangeFilter): Promise<WarehouseOperationsDashboard> {
    this.ensureInitialized();
    
    return await this.withRetry(async () => {
      const [
        atrlData,
        arilData,
        weTotals,
        utilization200,
        utilization240
      ] = await Promise.all([
        repositories.warehouse.atrl.getAll(filter),
        repositories.warehouse.aril.getAll(filter),
        repositories.warehouse.we.getTotals(),
        repositories.warehouse.lagerauslastung200.getUtilizationStats(filter),
        repositories.warehouse.lagerauslastung240.getUtilizationStats(filter)
      ]);
      
      // Berechne ATrL-Metriken
      const atrlMetrics = this.calculatePerformanceMetrics(atrlData, 'atrl');
      
      // Berechne ARiL-Metriken
      const arilMetrics = this.calculatePerformanceMetrics(arilData, 'aril');
      
      // Berechne Wareneingang-Trends
      const incomingGoods = this.calculateIncomingGoodsTrends(weTotals);
      
      // Erstelle Kapazitäts-Analysen
      const warehouse200Analysis = this.createCapacityAnalysis(utilization200);
      const warehouse240Analysis = this.createCapacityAnalysis(utilization240);
      
      // Generiere Alerts
      const alerts = this.generateAlerts(
        atrlMetrics,
        arilMetrics,
        warehouse200Analysis,
        warehouse240Analysis
      );
      
      return {
        atrlMetrics,
        arilMetrics,
        incomingGoods,
        utilization: {
          warehouse200: warehouse200Analysis,
          warehouse240: warehouse240Analysis
        },
        alerts
      };
    });
  }
  
  /**
   * Warehouse-Effizienz-Optimierung vorschlagen
   */
  async getEfficiencyRecommendations(filter?: DateRangeFilter): Promise<Array<{
    category: 'capacity' | 'throughput' | 'automation' | 'maintenance';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    expectedImpact: string;
    estimatedCost: 'low' | 'medium' | 'high';
  }>> {
    this.ensureInitialized();
    
    const dashboard = await this.getOperationsDashboard(filter);
    const recommendations = [];
    
    // Kapazitäts-Empfehlungen
    if (dashboard.utilization.warehouse200.utilizationPercentage > 90) {
      recommendations.push({
        category: 'capacity' as const,
        priority: 'high' as const,
        title: 'Warehouse 200 Kapazitätserweiterung',
        description: 'Warehouse 200 ist zu über 90% ausgelastet. Erwägen Sie eine Kapazitätserweiterung.',
        expectedImpact: 'Reduziert Engpässe um 30-40%',
        estimatedCost: 'high' as const
      });
    }
    
    // Effizienz-Empfehlungen
    if (dashboard.atrlMetrics.efficiency < 0.8) {
      recommendations.push({
        category: 'automation' as const,
        priority: 'medium' as const,
        title: 'ATrL Automatisierung verbessern',
        description: 'ATrL-Effizienz unter 80%. Prüfen Sie Automatisierungsprozesse.',
        expectedImpact: 'Effizienzsteigerung um 15-25%',
        estimatedCost: 'medium' as const
      });
    }
    
    // Durchsatz-Empfehlungen
    if (dashboard.incomingGoods.trend === 'decreasing') {
      recommendations.push({
        category: 'throughput' as const,
        priority: 'medium' as const,
        title: 'Wareneingang-Prozess optimieren',
        description: 'Rückläufiger Trend bei Wareneingängen. Prüfen Sie Prozessoptimierungen.',
        expectedImpact: 'Durchsatzsteigerung um 10-20%',
        estimatedCost: 'low' as const
      });
    }
    
    return recommendations;
  }
  
  /**
   * Predictive Analytics für Warehouse-Kapazität
   */
  async getPredictiveCapacityAnalysis(daysAhead: number = 30): Promise<{
    warehouse200: {
      currentTrend: number;
      projectedUtilization: number;
      capacityExceededDate?: Date;
      confidenceLevel: number;
    };
    warehouse240: {
      currentTrend: number;
      projectedUtilization: number;
      capacityExceededDate?: Date;
      confidenceLevel: number;
    };
  }> {
    this.ensureInitialized();
    
    // Hole historische Daten für Trend-Analyse
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const filter = {
      startDate: thirtyDaysAgo.toISOString().split('T')[0]
    };
    
    const [utilization200Data, utilization240Data] = await Promise.all([
      repositories.warehouse.lagerauslastung200.getAll(filter),
      repositories.warehouse.lagerauslastung240.getAll(filter)
    ]);
    
    return {
      warehouse200: this.calculatePredictiveAnalysis(utilization200Data, daysAhead),
      warehouse240: this.calculatePredictiveAnalysis(utilization240Data, daysAhead)
    };
  }
  
  /**
   * Cache für Warehouse-Daten invalidieren
   */
  async invalidateCache(): Promise<void> {
    repositories.warehouse.invalidateAllCache();
    this.log('Warehouse cache invalidated');
  }
  
  /**
   * Private Hilfsmethoden
   */
  
  private calculatePerformanceMetrics(data: any[], type: 'atrl' | 'aril'): WarehousePerformanceMetrics {
    if (!data || data.length === 0) {
      return {
        utilizationRate: 0,
        throughput: 0,
        efficiency: 0,
        errorRate: 0,
        trend: 'stable'
      };
    }
    
    // Berechne grundlegende Metriken
    const utilizationRate = this.calculateUtilizationRate(data);
    const throughput = this.calculateThroughput(data);
    const efficiency = this.calculateEfficiency(data);
    const errorRate = this.calculateErrorRate(data);
    const trend = this.calculateTrend(data);
    
    return {
      utilizationRate,
      throughput,
      efficiency,
      errorRate,
      trend
    };
  }
  
  private calculateUtilizationRate(data: any[]): number {
    // Beispiel-Berechnung - anpassen je nach Datenformat
    const total = data.reduce((sum, item) => sum + (item.plaetze_bel || item.belegtePlaetze || 0), 0);
    const capacity = data.reduce((sum, item) => sum + (item.max_plaetze || item.maxPlaetze || 100), 0);
    return capacity > 0 ? (total / capacity) * 100 : 0;
  }
  
  private calculateThroughput(data: any[]): number {
    // Beispiel-Berechnung für Durchsatz
    return data.reduce((sum, item) => sum + (item.all_bew || item.alleBewegungen || 0), 0) / data.length;
  }
  
  private calculateEfficiency(data: any[]): number {
    // Beispiel-Effizienz-Berechnung
    const movements = data.reduce((sum, item) => sum + (item.all_bew || item.alleBewegungen || 0), 0);
    const capacity = data.reduce((sum, item) => sum + (item.max_plaetze || item.maxPlaetze || 100), 0);
    return capacity > 0 ? Math.min((movements / capacity) * 100, 100) : 0;
  }
  
  private calculateErrorRate(data: any[]): number {
    // Beispiel-Fehlerrate-Berechnung
    const errors = data.reduce((sum, item) => sum + (item.errors || 0), 0);
    const total = data.reduce((sum, item) => sum + (item.all_bew || item.alleBewegungen || 1), 0);
    return total > 0 ? (errors / total) * 100 : 0;
  }
  
  private calculateTrend(data: any[]): 'improving' | 'declining' | 'stable' {
    if (data.length < 2) return 'stable';
    
    const firstHalf = data.slice(0, Math.floor(data.length / 2));
    const secondHalf = data.slice(Math.floor(data.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, item) => sum + (item.all_bew || item.alleBewegungen || 0), 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, item) => sum + (item.all_bew || item.alleBewegungen || 0), 0) / secondHalf.length;
    
    const change = ((secondAvg - firstAvg) / firstAvg) * 100;
    
    if (change > 5) return 'improving';
    if (change < -5) return 'declining';
    return 'stable';
  }
  
  private calculateIncomingGoodsTrends(totals: any): any {
    return {
      totalAtrl: totals.totalAtrl || 0,
      totalManl: totals.totalManl || 0,
      dailyAverage: ((totals.totalAtrl || 0) + (totals.totalManl || 0)) / 30, // Beispiel
      trend: 'stable' as const
    };
  }
  
  private createCapacityAnalysis(utilizationStats: any): WarehouseCapacityAnalysis {
    const maxCapacity = 1000; // Beispiel-Kapazität
    const currentCapacity = Math.floor(maxCapacity * (utilizationStats.currentUtilization / 100));
    
    return {
      currentCapacity,
      maxCapacity,
      utilizationPercentage: utilizationStats.currentUtilization || 0,
      recommendedActions: this.generateCapacityRecommendations(utilizationStats.currentUtilization)
    };
  }
  
  private generateCapacityRecommendations(utilization: number): string[] {
    const recommendations = [];
    
    if (utilization > 90) {
      recommendations.push('Sofortige Kapazitätserweiterung erforderlich');
      recommendations.push('Prüfung alternativer Lagerstandorte');
    } else if (utilization > 75) {
      recommendations.push('Kapazitätserweiterung in den nächsten 6 Monaten planen');
      recommendations.push('Optimierung der Lagerplatz-Nutzung');
    } else if (utilization < 50) {
      recommendations.push('Mögliche Überkapazität - Kostenoptimierung prüfen');
    }
    
    return recommendations;
  }
  
  private generateAlerts(
    atrlMetrics: WarehousePerformanceMetrics,
    arilMetrics: WarehousePerformanceMetrics,
    warehouse200: WarehouseCapacityAnalysis,
    warehouse240: WarehouseCapacityAnalysis
  ): any[] {
    const alerts = [];
    
    // Kritische Auslastung
    if (warehouse200.utilizationPercentage > 95) {
      alerts.push({
        type: 'error' as const,
        message: 'Warehouse 200 kritisch ausgelastet (>95%)',
        timestamp: new Date()
      });
    }
    
    // Performance-Warnungen
    if (atrlMetrics.efficiency < 60) {
      alerts.push({
        type: 'warning' as const,
        message: 'ATrL-Effizienz unter kritischem Schwellwert (60%)',
        timestamp: new Date()
      });
    }
    
    // Positive Trends
    if (atrlMetrics.trend === 'improving' && arilMetrics.trend === 'improving') {
      alerts.push({
        type: 'info' as const,
        message: 'Positive Trends in allen Warehouse-Bereichen',
        timestamp: new Date()
      });
    }
    
    return alerts;
  }
  
  private calculatePredictiveAnalysis(data: any[], daysAhead: number): any {
    // Vereinfachte Predictive Analytics
    if (!data || data.length < 3) {
      return {
        currentTrend: 0,
        projectedUtilization: 0,
        confidenceLevel: 0
      };
    }
    
    // Linear regression für Trend-Berechnung
    const utilizationValues = data.map((item: any) => parseFloat(item.auslastungA || item.auslastung || '0'));
    const avgUtilization = utilizationValues.reduce((sum, val) => sum + val, 0) / utilizationValues.length;
    
    // Einfacher linearer Trend
    const firstVal = utilizationValues[0];
    const lastVal = utilizationValues[utilizationValues.length - 1];
    const trend = (lastVal - firstVal) / utilizationValues.length;
    
    const projectedUtilization = Math.max(0, Math.min(100, avgUtilization + (trend * daysAhead)));
    
    return {
      currentTrend: trend,
      projectedUtilization,
      confidenceLevel: data.length > 10 ? 85 : 60 // Confidence basierend auf Datenmenge
    };
  }
}