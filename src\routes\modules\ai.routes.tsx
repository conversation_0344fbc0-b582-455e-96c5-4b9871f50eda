/**
 * AI Module Routes
 * 
 * Complete routes for the AI module matching the module configuration
 */

import { createRoute } from '@tanstack/react-router';
import { lazy } from 'react';
import { ModuleGuard } from '@/components/auth';
import { RootRoute } from '../__root';

// Lazy-Load AI Components
const AIDashboardPage = lazy(() => import('@/modules/ai/pages/AIDashboardPage'));
const AIChatPage = lazy(() => import('@/modules/ai/pages/RAGManagementPage'));
const RAGManagementPage = lazy(() => import('@/modules/ai/pages/RAGManagementPage'));
const CuttingOptimizationPage = lazy(() => import('@/modules/ai/pages/CuttingOptimizationPage'));
const InventoryIntelligencePage = lazy(() => import('@/modules/ai/pages/InventoryIntelligencePage'));
const ProcessOptimizationPage = lazy(() => import('@/modules/ai/pages/ProcessOptimizationPage'));
const PredictiveAnalyticsPage = lazy(() => import('@/modules/ai/pages/PredictiveAnalyticsPage'));
const WarehouseOptimizationPage = lazy(() => import('@/modules/ai/pages/WarehouseOptimizationPage'));
const ReportingPage = lazy(() => import('@/modules/ai/pages/ReportingPage'));
const SupplyChainAnalyticsPage = lazy(() => import('@/modules/ai/pages/SupplyChainAnalyticsPage'));
const AISettingsPage = lazy(() => import('@/modules/ai/pages/AISettingsPage'));

// AI Security Dashboard (lazy load when implemented)
const AISecurityDashboard = lazy(() =>
  import('@/modules/ai/components/security/AISecurityDashboard').then(module => ({
    default: module.AISecurityDashboard
  }))
);

// AI Main Dashboard Route
const aiDashboardRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Besucher', 'Benutzer', 'Administrator']}
    >
      <AIDashboardPage />
    </ModuleGuard>
  )
});

// AI Chat Route
const aiChatRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/chat',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Besucher', 'Benutzer', 'Administrator']}
    >
      <AIChatPage />
    </ModuleGuard>
  )
});

// RAG Management Route
const ragManagementRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/rag-management',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Besucher', 'Benutzer', 'Administrator']}
    >
      <RAGManagementPage />
    </ModuleGuard>
  )
});

// AI Analysis Route (placeholder for now, redirects to dashboard)
const aiAnalysisRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/analysis',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Besucher', 'Benutzer', 'Administrator']}
    >
      <AIDashboardPage />
    </ModuleGuard>
  )
});

// AI Cutting Optimization Route
const aiCuttingOptimizationRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/cutting-optimization',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Benutzer', 'Administrator']}
    >
      <CuttingOptimizationPage />
    </ModuleGuard>
  )
});

// AI Inventory Intelligence Route
const aiInventoryIntelligenceRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/inventory-intelligence',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Benutzer', 'Administrator']}
    >
      <InventoryIntelligencePage />
    </ModuleGuard>
  )
});

// AI Warehouse Optimization Route
const aiWarehouseOptimizationRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/warehouse-optimization',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Benutzer', 'Administrator']}
    >
      <WarehouseOptimizationPage />
    </ModuleGuard>
  )
});

// AI Process Optimization Route
const aiProcessOptimizationRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/process-optimization',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Benutzer', 'Administrator']}
    >
      <ProcessOptimizationPage />
    </ModuleGuard>
  )
});

// AI Predictive Analytics Route
const aiPredictiveAnalyticsRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/predictive-analytics',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Benutzer', 'Administrator']}
    >
      <PredictiveAnalyticsPage />
    </ModuleGuard>
  )
});

// AI Supply Chain Analytics Route
const aiSupplyChainAnalyticsRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/supply-chain-analytics',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Benutzer', 'Administrator']}
    >
      <SupplyChainAnalyticsPage />
    </ModuleGuard>
  )
});

// AI Supply Chain Optimization Route
const aiSupplyChainOptimizationRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/supply-chain-optimization',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Administrator']}
    >
      <SupplyChainAnalyticsPage />
    </ModuleGuard>
  )
});

// AI Reporting Route
const aiReportingRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/reporting',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Benutzer', 'Administrator']}
    >
      <ReportingPage />
    </ModuleGuard>
  )
});

// AI Automated Reporting Route
const aiAutomatedReportingRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/automated-reporting',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Benutzer', 'Administrator']}
    >
      <ReportingPage />
    </ModuleGuard>
  )
});

// AI Security Dashboard Route
const aiSecurityRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/security',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Administrator']}
    >
      <AISecurityDashboard />
    </ModuleGuard>
  )
});

// AI Settings Route
const aiSettingsRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/modules/ai/settings',
  component: () => (
    <ModuleGuard
      moduleId="ai"
      requiredRoles={['Administrator']}
    >
      <AISettingsPage />
    </ModuleGuard>
  )
});

export const aiRoutes = [
  aiDashboardRoute,
  aiChatRoute,
  ragManagementRoute,
  aiAnalysisRoute,
  aiCuttingOptimizationRoute,
  aiInventoryIntelligenceRoute,
  aiWarehouseOptimizationRoute,
  aiProcessOptimizationRoute,
  aiPredictiveAnalyticsRoute,
  aiSupplyChainAnalyticsRoute,
  aiSupplyChainOptimizationRoute,
  aiReportingRoute,
  aiAutomatedReportingRoute,
  aiSecurityRoute,
  aiSettingsRoute
];