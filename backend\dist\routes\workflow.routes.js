"use strict";
/**
 * Workflow-API-Routen
 *
 * API-Endpunkte für die Überwachung und Steuerung automatisierter Python-Workflows.
 * Folgt den etablierten Backend-Patterns mit Error-Handling und Rate-Limiting.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const validation_middleware_1 = require("../middleware/validation.middleware");
const rate_limiting_middleware_1 = require("../middleware/rate-limiting.middleware");
const router = express_1.default.Router();
console.log('[ROUTES] Lade Workflow-Routen...');
/**
 * Helper function to map SAPWorkflowProcess status to WorkflowStatus
 */
function mapProcessStatusToWorkflowStatus(processStatus) {
    switch (processStatus) {
        case 'idle':
            return 'scheduled';
        case 'running':
            return 'running';
        case 'completed':
            return 'completed';
        case 'error':
            return 'error';
        default:
            return 'scheduled';
    }
}
// Router-Debugging
router.use((req, res, next) => {
    console.log(`[WORKFLOW-ROUTES] ${req.method} ${req.originalUrl}`);
    next();
});
// Basis-Middleware
router.use(validation_middleware_1.sanitizationMiddleware);
router.use(rate_limiting_middleware_1.rateLimitConfig.dataEndpoint);
/**
 * Hilfsfunktion für einheitliche Request-Verarbeitung
 */
async function handleRequest(req, res, operation) {
    try {
        console.log(`[WORKFLOW-ROUTES] Verarbeite: ${req.method} ${req.originalUrl}`);
        const result = await operation();
        const response = {
            success: true,
            data: result
        };
        console.log(`[WORKFLOW-ROUTES] Erfolgreiche Antwort für ${req.originalUrl}`);
        res.json(response);
    }
    catch (error) {
        console.error(`[WORKFLOW-ROUTES] Fehler für ${req.originalUrl}:`, error);
        const response = {
            success: false,
            error: error instanceof Error ? error.message : 'Unbekannter Workflow-Fehler'
        };
        res.status(500).json(response);
    }
}
// Test-Route
router.get('/test', (req, res) => {
    console.log('[WORKFLOW-ROUTES] Test-Route aufgerufen!');
    res.json({
        message: 'Workflow-Routen funktionieren!',
        timestamp: new Date().toISOString(),
        service: 'WorkflowService'
    });
});
/**
 * GET /api/workflows
 * Alle Workflows mit aktuellem Status abrufen
 */
router.get('/', async (req, res) => {
    await handleRequest(req, res, async () => {
        // Use WorkflowService for consistency
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        const processes = await workflowServiceInstance.getProcesses();
        // Transform processes to workflow format
        const workflows = processes.map(process => ({
            id: process.id,
            name: process.name,
            description: process.description,
            sourceType: 'SAP',
            frequency: 'manual',
            targetTables: [process.dbTable],
            scriptPath: `scripts/workflows/${process.id}`,
            isActive: true,
            status: mapProcessStatusToWorkflowStatus(process.status),
            lastExecution: process.lastRun || null,
            nextExecution: null,
            duration: process.duration || null,
            successRate: 95, // Default success rate
            errorCount: 0,
            totalRuns: 0
        }));
        return {
            workflows,
            totalCount: workflows.length,
            lastUpdated: new Date()
        };
    });
});
/**
 * GET /api/workflows/:id/status
 * Detaillierter Status für einzelnen Workflow
 */
router.get('/:id/status', async (req, res) => {
    const { id } = req.params;
    await handleRequest(req, res, async () => {
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        const processes = await workflowServiceInstance.getProcesses();
        const process = processes.find(p => p.id === id);
        if (!process) {
            throw new Error(`Workflow mit ID ${id} nicht gefunden`);
        }
        // Transform process to workflow format
        const workflow = {
            id: process.id,
            name: process.name,
            description: process.description,
            sourceType: 'SAP',
            frequency: 'manual',
            targetTables: [process.dbTable],
            scriptPath: `scripts/workflows/${process.id}`,
            isActive: true,
            status: mapProcessStatusToWorkflowStatus(process.status),
            lastExecution: process.lastRun || null,
            nextExecution: null,
            duration: process.duration || null,
            successRate: 95,
            errorCount: 0,
            totalRuns: 0
        };
        return {
            workflow,
            metrics: {
                workflowId: workflow.id,
                averageDuration: workflow.duration || 0,
                successRate: workflow.successRate,
                lastSuccessfulRun: workflow.lastExecution,
                totalExecutions: workflow.totalRuns,
                errorCount: workflow.errorCount,
                dataFreshness: 0
            },
            recentExecutions: [],
            recentLogs: []
        };
    });
});
/**
 * GET /api/workflows/logs
 * Log-Einträge für Workflows abrufen (kompatibel mit workflowRoutes.ts)
 */
router.get('/logs', async (req, res) => {
    await handleRequest(req, res, async () => {
        const { workflowId, limit = 50 } = req.query;
        // Use WorkflowService for consistency
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        // For now, return empty logs as the WorkflowService doesn't implement log storage yet
        // In a real implementation, this would read from log files or database
        return [];
    });
});
/**
 * GET /api/workflows/:id/logs
 * Log-Einträge für einen Workflow abrufen
 */
router.get('/:id/logs', async (req, res) => {
    const { id } = req.params;
    const limit = parseInt(req.query.limit) || 50;
    await handleRequest(req, res, async () => {
        // Use WorkflowService for consistency
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        // For now, return empty logs as the WorkflowService doesn't implement log storage yet
        // In a real implementation, this would read from log files or database
        return [];
    });
});
/**
 * PUT /api/workflows/:id/toggle
 * Workflow aktivieren/deaktivieren
 */
router.put('/:id/toggle', rate_limiting_middleware_1.rateLimitConfig.writeOperation, async (req, res) => {
    const { id } = req.params;
    await handleRequest(req, res, async () => {
        // For now, just return a success response
        // In a real implementation, this would update the workflow configuration
        return {
            isActive: true,
            message: `Workflow ${id} toggle requested (not implemented yet)`
        };
    });
});
/**
 * GET /api/workflows/:id/running
 * Prüfen ob Workflow gerade läuft
 */
router.get('/:id/running', async (req, res) => {
    const { id } = req.params;
    await handleRequest(req, res, async () => {
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        const processes = await workflowServiceInstance.getProcesses();
        const process = processes.find(p => p.id === id);
        return {
            isRunning: (process === null || process === void 0 ? void 0 : process.status) === 'running' || false,
            checkedAt: new Date()
        };
    });
});
/**
 * GET /api/workflows/:id/config
 * Workflow-Konfiguration abrufen
 */
router.get('/:id/config', async (req, res) => {
    const { id } = req.params;
    await handleRequest(req, res, async () => {
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        const config = await workflowServiceInstance.getWorkflowConfig(id);
        return config;
    });
});
/**
 * PUT /api/workflows/:id/config
 * Workflow-Konfiguration aktualisieren
 */
router.put('/:id/config', rate_limiting_middleware_1.rateLimitConfig.writeOperation, async (req, res) => {
    const { id } = req.params;
    const configData = req.body;
    await handleRequest(req, res, async () => {
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        const updatedConfig = await workflowServiceInstance.updateWorkflowConfig(id, configData);
        return updatedConfig;
    });
});
/**
 * POST /api/workflows/execute
 * SAP Workflow Prozess ausführen
 */
router.post('/execute', rate_limiting_middleware_1.rateLimitConfig.writeOperation, async (req, res) => {
    await handleRequest(req, res, async () => {
        const { processId } = req.body;
        if (!processId) {
            throw new Error('Process ID is required');
        }
        console.log(`[WORKFLOW-ROUTES] Executing process: ${processId}`);
        // Use WorkflowService for consistent execution
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        const result = await workflowServiceInstance.executeProcess(processId);
        console.log(`[WORKFLOW-ROUTES] Process execution result:`, result);
        return result;
    });
});
/**
 * POST /api/workflows/:id/restart
 * Workflow neu starten (falls möglich)
 */
router.post('/:id/restart', rate_limiting_middleware_1.rateLimitConfig.writeOperation, async (req, res) => {
    const { id } = req.params;
    await handleRequest(req, res, async () => {
        // Implementierung für Workflow-Neustart
        // In realer Implementierung würde hier das Python-Skript gestartet
        console.log(`[WORKFLOW-ROUTES] Neustart angefordert für Workflow: ${id}`);
        return {
            success: true,
            message: `Neustart für Workflow ${id} wurde eingeleitet`
        };
    });
});
/**
 * GET /api/workflows/:id/history
 * Ausführungshistorie für einen Workflow
 */
router.get('/:id/history', async (req, res) => {
    const { id } = req.params;
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 20;
    await handleRequest(req, res, async () => {
        // Simulation der Historien-Daten
        // In realer Implementierung aus Datenbank oder Log-Files
        const executions = [];
        const totalCount = 0;
        return {
            executions,
            totalCount,
            pageSize,
            currentPage: page
        };
    });
});
/**
 * GET /api/workflows/health
 * Gesundheitscheck für alle Workflows
 */
router.get('/health', async (req, res) => {
    await handleRequest(req, res, async () => {
        // Use WorkflowService instead of WorkflowMonitorService for consistency
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        const processes = await workflowServiceInstance.getProcesses();
        const totalWorkflows = processes.length;
        const activeWorkflows = processes.filter(p => p.status !== 'idle').length;
        const runningWorkflows = processes.filter(p => p.status === 'running').length;
        const failedWorkflows = processes.filter(p => p.status === 'error').length;
        let overallHealth = 'healthy';
        if (failedWorkflows > 0) {
            overallHealth = failedWorkflows > totalWorkflows / 2 ? 'critical' : 'warning';
        }
        return {
            totalWorkflows,
            activeWorkflows,
            runningWorkflows,
            failedWorkflows,
            overallHealth
        };
    });
});
/**
 * Additional endpoints from workflowRoutes.ts for compatibility
 */
/**
 * GET /api/workflows/processes
 * Lädt alle verfügbaren Workflow-Prozesse
 */
router.get('/processes', async (req, res) => {
    await handleRequest(req, res, async () => {
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        const processes = await workflowServiceInstance.getProcesses();
        return {
            processes,
            count: processes.length
        };
    });
});
/**
 * GET /api/workflows/executions
 * Lädt Workflow-Ausführungen
 */
router.get('/executions', async (req, res) => {
    await handleRequest(req, res, async () => {
        const { workflowId, limit = 50 } = req.query;
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        const executions = await workflowServiceInstance.getExecutions(workflowId, Number(limit));
        return {
            executions,
            count: executions.length
        };
    });
});
/**
 * GET /api/workflows/stats
 * Lädt Workflow-Statistiken
 */
router.get('/stats', async (req, res) => {
    await handleRequest(req, res, async () => {
        const { WorkflowService } = await Promise.resolve().then(() => __importStar(require('../services/workflowService')));
        const workflowServiceInstance = new WorkflowService();
        const stats = await workflowServiceInstance.getStats();
        return stats;
    });
});
exports.default = router;
