import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { AICacheService } from '../AICacheService';
import { CacheService } from '@/services/cache.service';

// Mock CacheService
vi.mock('@/services/cache.service');

describe('AICacheService', () => {
  let aiCacheService: AICacheService;
  let mockCacheService: vi.Mocked<CacheService>;

  beforeEach(() => {
    mockCacheService = {
      get: vi.fn(),
      set: vi.fn(),
      delete: vi.fn(),
      clear: vi.fn(),
      has: vi.fn(),
      keys: vi.fn(),
      size: vi.fn()
    } as any;

    aiCacheService = new AICacheService(mockCacheService, {
      maxSize: 100,
      defaultTtl: 1000,
      maxMemoryUsage: 1024 * 1024, // 1MB
      evictionPolicy: 'lru',
      compressionEnabled: false
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Cache Operations', () => {
    it('should store and retrieve values', async () => {
      const key = 'test-key';
      const value = { data: 'test-data' };

      await aiCacheService.set(key, value);
      const retrieved = await aiCacheService.get(key);

      expect(retrieved).toEqual(value);
    });

    it('should return null for non-existent keys', async () => {
      const result = await aiCacheService.get('non-existent');
      expect(result).toBeNull();
    });

    it('should respect TTL and expire entries', async () => {
      const key = 'ttl-test';
      const value = 'test-value';
      const shortTtl = 100; // 100ms

      await aiCacheService.set(key, value, shortTtl);
      
      // Should be available immediately
      let retrieved = await aiCacheService.get(key);
      expect(retrieved).toBe(value);

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 150));
      
      retrieved = await aiCacheService.get(key);
      expect(retrieved).toBeNull();
    });

    it('should update access statistics on cache hits', async () => {
      const key = 'stats-test';
      const value = 'test-value';

      await aiCacheService.set(key, value);
      
      // Multiple gets should increase hit count
      await aiCacheService.get(key);
      await aiCacheService.get(key);
      await aiCacheService.get(key);

      const stats = aiCacheService.getStats();
      expect(stats.hitRate).toBeGreaterThan(0);
      expect(stats.totalEntries).toBe(1);
    });
  });

  describe('Cache Eviction', () => {
    it('should evict entries when max size is exceeded', async () => {
      // Fill cache to max size
      for (let i = 0; i < 100; i++) {
        await aiCacheService.set(`key-${i}`, `value-${i}`);
      }

      const statsBeforeEviction = aiCacheService.getStats();
      expect(statsBeforeEviction.totalEntries).toBe(100);

      // Add one more entry to trigger eviction
      await aiCacheService.set('overflow-key', 'overflow-value');

      const statsAfterEviction = aiCacheService.getStats();
      expect(statsAfterEviction.totalEntries).toBeLessThanOrEqual(100);
      expect(statsAfterEviction.evictionCount).toBeGreaterThan(0);
    });

    it('should use LRU eviction policy', async () => {
      // Set eviction policy to LRU
      const lruCache = new AICacheService(mockCacheService, {
        maxSize: 3,
        evictionPolicy: 'lru'
      });

      // Add entries
      await lruCache.set('key1', 'value1');
      await lruCache.set('key2', 'value2');
      await lruCache.set('key3', 'value3');

      // Access key1 to make it recently used
      await lruCache.get('key1');

      // Add another entry to trigger eviction
      await lruCache.set('key4', 'value4');

      // key2 should be evicted (least recently used)
      expect(await lruCache.get('key1')).toBe('value1'); // Recently accessed
      expect(await lruCache.get('key2')).toBeNull(); // Should be evicted
      expect(await lruCache.get('key3')).toBe('value3');
      expect(await lruCache.get('key4')).toBe('value4');
    });
  });

  describe('Memoization', () => {
    it('should memoize function results', async () => {
      let callCount = 0;
      const expensiveFunction = async (input: string) => {
        callCount++;
        return `processed-${input}`;
      };

      const memoizedFunction = aiCacheService.memoize(expensiveFunction);

      // First call should execute function
      const result1 = await memoizedFunction('test');
      expect(result1).toBe('processed-test');
      expect(callCount).toBe(1);

      // Second call with same input should use cache
      const result2 = await memoizedFunction('test');
      expect(result2).toBe('processed-test');
      expect(callCount).toBe(1); // Should not increase

      // Different input should execute function again
      const result3 = await memoizedFunction('different');
      expect(result3).toBe('processed-different');
      expect(callCount).toBe(2);
    });

    it('should use custom key generator for memoization', async () => {
      const fn = async (obj: { id: number; data: string }) => `result-${obj.id}`;
      const keyGenerator = (obj: { id: number; data: string }) => `custom-${obj.id}`;
      
      const memoizedFn = aiCacheService.memoize(fn, keyGenerator);

      const result1 = await memoizedFn({ id: 1, data: 'first' });
      const result2 = await memoizedFn({ id: 1, data: 'second' }); // Different data, same id

      expect(result1).toBe(result2); // Should be cached based on id only
    });
  });

  describe('Cache Management', () => {
    it('should clear cache with pattern matching', async () => {
      await aiCacheService.set('user:1', 'user1');
      await aiCacheService.set('user:2', 'user2');
      await aiCacheService.set('product:1', 'product1');

      await aiCacheService.clear('user:*');

      expect(await aiCacheService.get('user:1')).toBeNull();
      expect(await aiCacheService.get('user:2')).toBeNull();
      expect(await aiCacheService.get('product:1')).toBe('product1');
    });

    it('should invalidate cache by tags', async () => {
      await aiCacheService.set('key1:tag:user', 'value1');
      await aiCacheService.set('key2:tag:user', 'value2');
      await aiCacheService.set('key3:tag:product', 'value3');

      await aiCacheService.invalidateByTag('user');

      expect(await aiCacheService.get('key1:tag:user')).toBeNull();
      expect(await aiCacheService.get('key2:tag:user')).toBeNull();
      expect(await aiCacheService.get('key3:tag:product')).toBe('value3');
    });

    it('should preload cache entries', async () => {
      const entries = [
        { key: 'preload1', value: 'value1' },
        { key: 'preload2', value: 'value2', ttl: 500 }
      ];

      await aiCacheService.preload(entries);

      expect(await aiCacheService.get('preload1')).toBe('value1');
      expect(await aiCacheService.get('preload2')).toBe('value2');
    });
  });

  describe('Performance Monitoring', () => {
    it('should track cache statistics', async () => {
      // Perform various cache operations
      await aiCacheService.set('key1', 'value1');
      await aiCacheService.set('key2', 'value2');
      
      await aiCacheService.get('key1'); // Hit
      await aiCacheService.get('key1'); // Hit
      await aiCacheService.get('nonexistent'); // Miss

      const stats = aiCacheService.getStats();
      
      expect(stats.totalEntries).toBe(2);
      expect(stats.hitRate).toBeCloseTo(0.67, 1); // 2 hits out of 3 requests
      expect(stats.missRate).toBeCloseTo(0.33, 1); // 1 miss out of 3 requests
    });

    it('should calculate memory usage', async () => {
      const largeValue = 'x'.repeat(1000); // 1KB string
      
      await aiCacheService.set('large-key', largeValue);
      
      const stats = aiCacheService.getStats();
      expect(stats.totalSize).toBeGreaterThan(1000);
    });
  });

  describe('Error Handling', () => {
    it('should handle cache errors gracefully', async () => {
      // Mock cache service to throw error
      mockCacheService.get.mockRejectedValue(new Error('Cache error'));

      // Should not throw, should return null
      const result = await aiCacheService.get('error-key');
      expect(result).toBeNull();
    });

    it('should handle set errors gracefully', async () => {
      mockCacheService.set.mockRejectedValue(new Error('Set error'));

      // Should not throw
      await expect(aiCacheService.set('error-key', 'value')).resolves.not.toThrow();
    });
  });

  describe('Memory Management', () => {
    it('should enforce memory limits', async () => {
      const smallMemoryCache = new AICacheService(mockCacheService, {
        maxMemoryUsage: 1000, // 1KB limit
        evictionPolicy: 'lru'
      });

      // Add entries that exceed memory limit
      const largeValue = 'x'.repeat(500); // 500 bytes each
      
      await smallMemoryCache.set('key1', largeValue);
      await smallMemoryCache.set('key2', largeValue);
      await smallMemoryCache.set('key3', largeValue); // Should trigger eviction

      const stats = smallMemoryCache.getStats();
      expect(stats.memoryUsage).toBeLessThanOrEqual(1000);
      expect(stats.evictionCount).toBeGreaterThan(0);
    });
  });
});