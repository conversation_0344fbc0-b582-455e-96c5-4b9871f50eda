import apiService from './api.service';
import { Runbook, RunbookCreateData, RunbookUpdateData } from '@/types/runbooks.types';

class RunbookService {
  private baseUrl = '/runbooks';

  async getAllRunbooks(): Promise<Runbook[]> {
    return apiService.get<Runbook[]>(this.baseUrl);
  }

  async getRunbookById(id: number): Promise<Runbook> {
    return apiService.get<Runbook>(`${this.baseUrl}/${id}`);
  }

  async createRunbook(data: RunbookCreateData): Promise<Runbook> {
    return apiService.post<Runbook>(this.baseUrl, data);
  }

  async updateRunbook(id: number, data: RunbookUpdateData): Promise<Runbook> {
    return apiService.put<Runbook>(`${this.baseUrl}/${id}`, data);
  }

  async deleteRunbook(id: number): Promise<void> {
    return apiService.delete<void>(`${this.baseUrl}/${id}`);
  }

  async searchRunbooks(query: string): Promise<Runbook[]> {
    return apiService.get<Runbook[]>(`${this.baseUrl}/search?q=${encodeURIComponent(query)}`);
  }
}

export const runbookService = new RunbookService();
