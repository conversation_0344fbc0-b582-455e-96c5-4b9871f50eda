/**
 * Inventory Intelligence Service Tests
 * 
 * Unit tests for the main inventory intelligence service
 */

import { InventoryIntelligenceService } from '../InventoryIntelligenceService';
import { WarehouseRepository } from '@/repositories/warehouse.repository';
import { AIServiceError } from '../../types';
import { vi } from 'vitest';

// Mock the warehouse repository
vi.mock('@/repositories/warehouse.repository');

describe('InventoryIntelligenceService', () => {
  let service: InventoryIntelligenceService;
  let mockWarehouseRepository: any;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Create service instance
    service = new InventoryIntelligenceService({
      inventoryConfig: {
        abcAnalysis: {
          enabled: true,
          updateFrequency: 24,
          classAThreshold: 80,
          classBThreshold: 95,
          analysisWindow: 90
        },
        demandForecasting: {
          enabled: true,
          forecastHorizon: 30,
          updateFrequency: 12,
          minHistoryDays: 14,
          models: ['simple_moving_average', 'exponential_smoothing']
        },
        anomalyDetection: {
          enabled: true,
          sensitivity: 0.7,
          checkFrequency: 6,
          alertThreshold: 0.8
        },
        reorderOptimization: {
          enabled: true,
          safetyStockDays: 7,
          leadTimeDays: 14,
          serviceLevel: 0.95
        }
      }
    });

    // Mock warehouse repository
    mockWarehouseRepository = {
      getOverallStats: vi.fn().mockResolvedValue({
        warehouse200: { avgUtilization: 75 },
        warehouse240: { avgUtilization: 80 },
        wareneingang: { totalAtrl: 1000, totalManl: 800 },
        atrlData: 30,
        arilData: 25
      })
    };
    (service as any).warehouseRepository = mockWarehouseRepository;
  });

  describe('initialization', () => {
    it('should initialize successfully with default config', async () => {
      await service.initialize();
      
      const status = await service.healthCheck();
      expect(status.isHealthy).toBe(true);
    });

    it('should initialize with custom config', async () => {
      const customConfig = {
        inventoryConfig: {
          abcAnalysis: {
            enabled: false,
            updateFrequency: 48,
            classAThreshold: 70,
            classBThreshold: 90,
            analysisWindow: 60
          },
          demandForecasting: {
            enabled: true,
            forecastHorizon: 14,
            updateFrequency: 6,
            minHistoryDays: 7,
            models: ['simple_moving_average']
          },
          anomalyDetection: {
            enabled: true,
            sensitivity: 0.8,
            checkFrequency: 12,
            alertThreshold: 0.9
          },
          reorderOptimization: {
            enabled: false,
            safetyStockDays: 14,
            leadTimeDays: 21,
            serviceLevel: 0.98
          }
        }
      };

      await service.initialize(customConfig);
      
      const status = await service.healthCheck();
      expect(status.isHealthy).toBe(true);
    });
  });

  describe('performABCAnalysis', () => {
    it('should perform ABC analysis successfully', async () => {
      await service.initialize();
      
      const result = await service.performABCAnalysis();
      
      expect(result).toBeDefined();
      expect(result.classA).toBeDefined();
      expect(result.classB).toBeDefined();
      expect(result.classC).toBeDefined();
      expect(result.analysisDate).toBeInstanceOf(Date);
      expect(result.criteria).toBeDefined();
      
      // Total items should match input
      const totalClassified = result.classA.length + result.classB.length + result.classC.length;
      expect(totalClassified).toBeGreaterThan(0);
    });

    it('should handle empty inventory gracefully', async () => {
      await service.initialize();
      
      // Mock empty inventory
      vi.spyOn(service as any, 'getInventoryItems').mockResolvedValue([]);
      
      const result = await service.performABCAnalysis();
      
      expect(result.classA).toHaveLength(0);
      expect(result.classB).toHaveLength(0);
      expect(result.classC).toHaveLength(0);
    });

    it('should cache analysis results', async () => {
      await service.initialize();
      
      const result1 = await service.performABCAnalysis();
      const result2 = await service.performABCAnalysis();
      
      // Results should be consistent (cached)
      expect(result1.analysisDate.getTime()).toBeLessThanOrEqual(result2.analysisDate.getTime());
    });

    it('should handle analysis errors with fallback', async () => {
      await service.initialize();
      
      // Mock error in analysis
      vi.spyOn(service as any, 'getInventoryItems').mockRejectedValue(new Error('Database error'));
      
      const result = await service.performABCAnalysis();
      
      // Should return fallback empty classification
      expect(result.classA).toHaveLength(0);
      expect(result.classB).toHaveLength(0);
      expect(result.classC).toHaveLength(0);
    });
  });

  describe('updateClassifications', () => {
    it('should update classifications and detect changes', async () => {
      await service.initialize();
      
      // Perform initial analysis
      await service.performABCAnalysis();
      
      // Update classifications
      const result = await service.updateClassifications();
      
      expect(result).toBeDefined();
      expect(result.updated).toBeGreaterThanOrEqual(0);
      expect(result.reclassifications).toBeGreaterThanOrEqual(0);
    });

    it('should handle update errors gracefully', async () => {
      await service.initialize();
      
      // Mock error
      vi.spyOn(service, 'performABCAnalysis').mockRejectedValue(new Error('Analysis failed'));
      
      const result = await service.updateClassifications();
      
      expect(result.updated).toBe(0);
      expect(result.reclassifications).toBe(0);
    });
  });

  describe('forecastDemand', () => {
    it('should generate demand forecast for valid item', async () => {
      await service.initialize();
      
      const forecast = await service.forecastDemand('item-001', 14);
      
      expect(forecast).toBeDefined();
      expect(forecast.itemId).toBe('item-001');
      expect(forecast.predictions).toHaveLength(14);
      expect(forecast.confidence).toBeGreaterThanOrEqual(0);
      expect(forecast.confidence).toBeLessThanOrEqual(1);
      expect(forecast.trendDirection).toMatch(/increasing|decreasing|stable/);
      expect(forecast.forecastMethod).toBeTruthy();
      expect(forecast.generatedAt).toBeInstanceOf(Date);
      
      // Check prediction structure
      forecast.predictions.forEach(prediction => {
        expect(prediction.date).toBeInstanceOf(Date);
        expect(prediction.predictedDemand).toBeGreaterThanOrEqual(0);
        expect(prediction.confidenceInterval.lower).toBeGreaterThanOrEqual(0);
        expect(prediction.confidenceInterval.upper).toBeGreaterThan(prediction.confidenceInterval.lower);
      });
    });

    it('should use default horizon when not specified', async () => {
      await service.initialize();
      
      const forecast = await service.forecastDemand('item-001');
      
      expect(forecast.predictions).toHaveLength(30); // Default horizon
    });

    it('should handle insufficient data gracefully', async () => {
      await service.initialize();
      
      // Mock insufficient consumption data
      jest.spyOn(service as any, 'getItemConsumptionData').mockResolvedValue([]);
      
      const forecast = await service.forecastDemand('item-no-data');
      
      // Should return fallback forecast
      expect(forecast.itemId).toBe('item-no-data');
      expect(forecast.predictions).toHaveLength(0);
      expect(forecast.confidence).toBe(0);
    });

    it('should cache forecast results', async () => {
      await service.initialize();
      
      const forecast1 = await service.forecastDemand('item-001', 7);
      const forecast2 = await service.forecastDemand('item-001', 7);
      
      // Second call should return cached result
      expect(forecast1.generatedAt.getTime()).toBeLessThanOrEqual(forecast2.generatedAt.getTime());
    });
  });

  describe('detectSeasonality', () => {
    it('should detect seasonality patterns', async () => {
      await service.initialize();
      
      const seasonality = await service.detectSeasonality('item-001');
      
      expect(seasonality).toBeDefined();
      expect(seasonality.itemId).toBe('item-001');
      expect(seasonality.hasSeasonality).toBeDefined();
      expect(seasonality.patterns).toBeDefined();
      expect(seasonality.strength).toBeGreaterThanOrEqual(0);
      expect(seasonality.strength).toBeLessThanOrEqual(1);
      expect(seasonality.detectedAt).toBeInstanceOf(Date);
    });

    it('should handle items with insufficient data', async () => {
      await service.initialize();
      
      // Mock insufficient data
      jest.spyOn(service as any, 'getItemConsumptionData').mockResolvedValue([]);
      
      const seasonality = await service.detectSeasonality('item-no-data');
      
      expect(seasonality.hasSeasonality).toBe(false);
      expect(seasonality.patterns).toHaveLength(0);
      expect(seasonality.strength).toBe(0);
    });
  });

  describe('calculateOptimalReorderPoint', () => {
    it('should calculate reorder recommendation', async () => {
      await service.initialize();
      
      const recommendation = await service.calculateOptimalReorderPoint('item-001');
      
      expect(recommendation).toBeDefined();
      expect(recommendation.itemId).toBe('item-001');
      expect(recommendation.currentStock).toBeGreaterThanOrEqual(0);
      expect(recommendation.reorderPoint).toBeGreaterThanOrEqual(0);
      expect(recommendation.recommendedOrderQuantity).toBeGreaterThan(0);
      expect(recommendation.urgency).toMatch(/low|medium|high|critical/);
      expect(recommendation.reasoning).toBeTruthy();
      expect(recommendation.confidence).toBeGreaterThanOrEqual(0);
      expect(recommendation.confidence).toBeLessThanOrEqual(1);
      expect(recommendation.leadTime).toBeGreaterThan(0);
      expect(recommendation.safetyStock).toBeGreaterThanOrEqual(0);
    });

    it('should handle items with no consumption data', async () => {
      await service.initialize();
      
      // Mock no consumption data
      jest.spyOn(service as any, 'getItemConsumptionData').mockResolvedValue([]);
      
      const recommendation = await service.calculateOptimalReorderPoint('item-no-data');
      
      // Should return fallback recommendation
      expect(recommendation.itemId).toBe('item-no-data');
      expect(recommendation.currentStock).toBe(0);
      expect(recommendation.reorderPoint).toBe(0);
      expect(recommendation.urgency).toBe('low');
    });

    it('should determine urgency levels correctly', async () => {
      await service.initialize();
      
      // Mock different stock levels
      jest.spyOn(service as any, 'getCurrentStock')
        .mockResolvedValueOnce(5)   // Very low stock
        .mockResolvedValueOnce(50)  // Medium stock
        .mockResolvedValueOnce(200); // High stock
      
      const lowStockRec = await service.calculateOptimalReorderPoint('item-low');
      const mediumStockRec = await service.calculateOptimalReorderPoint('item-medium');
      const highStockRec = await service.calculateOptimalReorderPoint('item-high');
      
      // Low stock should have higher urgency
      expect(['high', 'critical']).toContain(lowStockRec.urgency);
      expect(['low', 'medium']).toContain(highStockRec.urgency);
    });
  });

  describe('generatePurchaseRecommendations', () => {
    it('should generate purchase recommendations', async () => {
      await service.initialize();
      
      const recommendations = await service.generatePurchaseRecommendations();
      
      expect(Array.isArray(recommendations)).toBe(true);
      
      recommendations.forEach(rec => {
        expect(rec.itemId).toBeTruthy();
        expect(rec.recommendedQuantity).toBeGreaterThan(0);
        expect(rec.estimatedCost).toBeGreaterThan(0);
        expect(rec.priority).toBeGreaterThan(0);
        expect(rec.reasoning).toBeTruthy();
      });
    });

    it('should prioritize recommendations correctly', async () => {
      await service.initialize();
      
      const recommendations = await service.generatePurchaseRecommendations();
      
      // Should be sorted by priority
      for (let i = 1; i < recommendations.length; i++) {
        expect(recommendations[i].priority).toBeGreaterThanOrEqual(recommendations[i - 1].priority);
      }
    });

    it('should handle errors gracefully', async () => {
      await service.initialize();
      
      // Mock error in reorder calculation
      jest.spyOn(service, 'calculateOptimalReorderPoint').mockRejectedValue(new Error('Calculation failed'));
      
      const recommendations = await service.generatePurchaseRecommendations();
      
      expect(Array.isArray(recommendations)).toBe(true);
      // Should return empty array on error
    });
  });

  describe('detectStockAnomalies', () => {
    it('should detect stock anomalies', async () => {
      await service.initialize();
      
      const anomalies = await service.detectStockAnomalies();
      
      expect(Array.isArray(anomalies)).toBe(true);
      
      anomalies.forEach(anomaly => {
        expect(anomaly.id).toBeTruthy();
        expect(anomaly.itemId).toBeTruthy();
        expect(anomaly.anomalyType).toMatch(/sudden_spike|sudden_drop|unusual_pattern|stockout_risk|overstock/);
        expect(anomaly.severity).toMatch(/low|medium|high|critical/);
        expect(anomaly.detectedAt).toBeInstanceOf(Date);
        expect(anomaly.description).toBeTruthy();
        expect(anomaly.currentValue).toBeGreaterThanOrEqual(0);
        expect(anomaly.expectedValue).toBeGreaterThanOrEqual(0);
        expect(anomaly.deviation).toBeGreaterThanOrEqual(0);
        expect(Array.isArray(anomaly.recommendations)).toBe(true);
        expect(anomaly.confidence).toBeGreaterThanOrEqual(0);
        expect(anomaly.confidence).toBeLessThanOrEqual(1);
      });
    });

    it('should handle items with insufficient data', async () => {
      await service.initialize();
      
      // Mock insufficient consumption data
      jest.spyOn(service as any, 'getItemConsumptionData').mockResolvedValue([]);
      
      const anomalies = await service.detectStockAnomalies();
      
      expect(Array.isArray(anomalies)).toBe(true);
      // Should return empty array for items without sufficient data
    });
  });

  describe('analyzeConsumptionPatterns', () => {
    it('should analyze consumption patterns', async () => {
      await service.initialize();
      
      const analysis = await service.analyzeConsumptionPatterns('item-001');
      
      expect(analysis).toBeDefined();
      expect(analysis.itemId).toBe('item-001');
      expect(analysis.averageDailyConsumption).toBeGreaterThanOrEqual(0);
      expect(analysis.consumptionTrend).toMatch(/increasing|decreasing|stable/);
      expect(analysis.volatility).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(analysis.patterns)).toBe(true);
      expect(Array.isArray(analysis.outliers)).toBe(true);
      expect(analysis.analysisDate).toBeInstanceOf(Date);
    });

    it('should handle items with no consumption data', async () => {
      await service.initialize();
      
      // Mock no consumption data
      jest.spyOn(service as any, 'getItemConsumptionData').mockResolvedValue([]);
      
      const analysis = await service.analyzeConsumptionPatterns('item-no-data');
      
      // Should return fallback analysis
      expect(analysis.itemId).toBe('item-no-data');
      expect(analysis.averageDailyConsumption).toBe(0);
      expect(analysis.consumptionTrend).toBe('stable');
      expect(analysis.volatility).toBe(0);
    });

    it('should identify consumption patterns correctly', async () => {
      await service.initialize();
      
      const analysis = await service.analyzeConsumptionPatterns('item-001');
      
      analysis.patterns.forEach(pattern => {
        expect(pattern.type).toMatch(/regular|irregular|seasonal|trending/);
        expect(pattern.description).toBeTruthy();
        expect(pattern.strength).toBeGreaterThanOrEqual(0);
        expect(pattern.strength).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('healthCheck', () => {
    it('should return comprehensive health status', async () => {
      await service.initialize();
      
      const status = await service.healthCheck();
      
      expect(status).toBeDefined();
      expect(status.isHealthy).toBeDefined();
      expect(status.lastAnalysisRun).toBeInstanceOf(Date);
      expect(status.totalItemsAnalyzed).toBeGreaterThanOrEqual(0);
      expect(status.classificationsUpToDate).toBeDefined();
      expect(status.forecastsUpToDate).toBeDefined();
      expect(status.anomaliesDetected).toBeGreaterThanOrEqual(0);
      expect(status.reorderRecommendations).toBeGreaterThanOrEqual(0);
      expect(status.performance).toBeDefined();
      expect(status.performance.analysisTime).toBeGreaterThanOrEqual(0);
      expect(status.performance.forecastAccuracy).toBeGreaterThanOrEqual(0);
      expect(status.performance.anomalyDetectionRate).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(status.errors)).toBe(true);
    });

    it('should indicate unhealthy status on service errors', async () => {
      await service.initialize();
      
      // Force an error state
      (service as any).lastError = new Error('Service error');
      
      const status = await service.healthCheck();
      
      expect(status.errors.length).toBeGreaterThan(0);
    });
  });

  describe('error handling and resilience', () => {
    it('should handle repository errors gracefully', async () => {
      await service.initialize();
      
      // Mock repository error
      mockWarehouseRepository.getOverallStats.mockRejectedValue(new Error('Database connection failed'));
      
      // Service should still function with fallback behavior
      const analysis = await service.performABCAnalysis();
      expect(analysis).toBeDefined();
    });

    it('should handle concurrent operations', async () => {
      await service.initialize();
      
      // Run multiple operations concurrently
      const promises = [
        service.performABCAnalysis(),
        service.forecastDemand('item-001'),
        service.detectStockAnomalies(),
        service.generatePurchaseRecommendations()
      ];
      
      const results = await Promise.allSettled(promises);
      
      // All operations should complete (either fulfilled or rejected gracefully)
      results.forEach(result => {
        expect(['fulfilled', 'rejected']).toContain(result.status);
      });
    });

    it('should maintain service state consistency', async () => {
      await service.initialize();
      
      const initialStatus = await service.healthCheck();
      
      // Perform various operations
      await service.performABCAnalysis();
      await service.forecastDemand('item-001');
      
      const finalStatus = await service.healthCheck();
      
      // Service should remain healthy
      expect(finalStatus.isHealthy).toBe(true);
      expect(finalStatus.totalItemsAnalyzed).toBeGreaterThanOrEqual(initialStatus.totalItemsAnalyzed);
    });
  });

  describe('configuration management', () => {
    it('should respect configuration settings', async () => {
      const customService = new InventoryIntelligenceService({
        inventoryConfig: {
          abcAnalysis: {
            enabled: false,
            updateFrequency: 48,
            classAThreshold: 70,
            classBThreshold: 90,
            analysisWindow: 60
          },
          demandForecasting: {
            enabled: true,
            forecastHorizon: 14,
            updateFrequency: 6,
            minHistoryDays: 7,
            models: ['simple_moving_average']
          },
          anomalyDetection: {
            enabled: false,
            sensitivity: 0.8,
            checkFrequency: 12,
            alertThreshold: 0.9
          },
          reorderOptimization: {
            enabled: true,
            safetyStockDays: 14,
            leadTimeDays: 21,
            serviceLevel: 0.98
          }
        }
      });

      await customService.initialize();
      
      // Configuration should be applied
      const status = await customService.healthCheck();
      expect(status.isHealthy).toBe(true);
    });

    it('should validate configuration parameters', async () => {
      // Test with invalid configuration
      const invalidService = new InventoryIntelligenceService({
        inventoryConfig: {
          abcAnalysis: {
            enabled: true,
            updateFrequency: -1, // Invalid
            classAThreshold: 150, // Invalid (>100)
            classBThreshold: 50,  // Invalid (<classAThreshold)
            analysisWindow: 0     // Invalid
          },
          demandForecasting: {
            enabled: true,
            forecastHorizon: -5,  // Invalid
            updateFrequency: 0,   // Invalid
            minHistoryDays: -1,   // Invalid
            models: []            // Invalid (empty)
          },
          anomalyDetection: {
            enabled: true,
            sensitivity: 2,       // Invalid (>1)
            checkFrequency: -1,   // Invalid
            alertThreshold: -0.5  // Invalid (<0)
          },
          reorderOptimization: {
            enabled: true,
            safetyStockDays: -1,  // Invalid
            leadTimeDays: 0,      // Invalid
            serviceLevel: 1.5     // Invalid (>1)
          }
        }
      });

      // Service should still initialize with fallback values
      await invalidService.initialize();
      const status = await invalidService.healthCheck();
      expect(status.isHealthy).toBe(true);
    });
  });
});