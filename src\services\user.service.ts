import { ApiService } from './api.service';

export interface UpdateProfileRequest {
  username?: string;
  email?: string;
  name?: string;
  bio?: string;
}

export interface UpdateProfileResponse {
  success: boolean;
  message: string;
  data?: {
    id: number;
    username: string;
    email: string;
    name?: string;
    bio?: string;
    roles: string[];
    updatedAt: string;
  };
  error?: string;
  code?: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface ChangePasswordResponse {
  success: boolean;
  message: string;
  error?: string;
  code?: string;
}

export class UserService {
  private apiService: ApiService;

  constructor() {
    this.apiService = new ApiService();
  }

  /**
   * Updates the current user's profile information
   * @param data Profile update data
   * @returns Promise with update response
   */
  async updateProfile(data: UpdateProfileRequest): Promise<UpdateProfileResponse> {
    try {
      const response = await fetch(`${this.apiService.getBaseUrl()}/user/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      
      // Handle successful API response
      if (result && result.success) {
        // Update stored user data if profile update was successful
        if (result.data) {
          const currentUser = JSON.parse(localStorage.getItem('user_data') || '{}');
          const updatedUser = { ...currentUser, ...result.data };
          localStorage.setItem('user_data', JSON.stringify(updatedUser));
        }
        
        return {
          success: true,
          message: result.message || 'Profil erfolgreich aktualisiert',
          data: result.data
        };
      }
      
      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Profil-Update fehlgeschlagen',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Profil-Update fehlgeschlagen. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Changes the current user's password
   * @param data Password change data
   * @returns Promise with change password response
   */
  async changePassword(data: ChangePasswordRequest): Promise<ChangePasswordResponse> {
    try {
      const response = await fetch(`${this.apiService.getBaseUrl()}/user/change-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      
      // Handle successful API response
      if (result && result.success) {
        return {
          success: true,
          message: result.message || 'Passwort erfolgreich geändert'
        };
      }
      
      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Passwort-Änderung fehlgeschlagen',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Passwort-Änderung fehlgeschlagen. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Gets the current user's profile information
   * @returns Promise with user profile data
   */
  async getProfile(): Promise<UpdateProfileResponse> {
    try {
      const response = await fetch(`${this.apiService.getBaseUrl()}/user/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      const result = await response.json();
      
      // Handle successful API response
      if (result && result.success) {
        return {
          success: true,
          message: result.message || 'Profil erfolgreich geladen',
          data: result.data
        };
      }
      
      // Handle API error response
      return {
        success: false,
        message: result?.error || result?.message || 'Profil konnte nicht geladen werden',
        error: result?.error || 'UNKNOWN_ERROR'
      };
    } catch (error: any) {
      // Handle network or other errors
      return {
        success: false,
        message: error.message || 'Profil konnte nicht geladen werden. Bitte versuchen Sie es später erneut.',
        error: 'NETWORK_ERROR',
        code: 'NETWORK_ERROR'
      };
    }
  }
}

export const userService = new UserService();