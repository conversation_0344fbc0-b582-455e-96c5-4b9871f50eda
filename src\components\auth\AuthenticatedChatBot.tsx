import React, { useState, useEffect } from 'react';
import { useLocation } from '@tanstack/react-router';
import { useAuthContext } from '@/contexts/AuthContext';
import { ChatBot } from '@/modules/ai/components/chat';
import { apiService } from '@/services/api.service';

/**
 * AuthenticatedChatBot Component
 * 
 * Renders the ChatBot only when:
 * - User is authenticated
 * - User is not on login/registration pages
 * - Backend is available
 */
export const AuthenticatedChatBot: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuthContext();
  const location = useLocation();
  const [isBackendReady, setIsBackendReady] = useState(true); // Default to true for graceful degradation
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  useEffect(() => {
    // Only check backend connection if user is authenticated
    if (isAuthenticated && !isLoading) {
      checkBackendConnection();
    }
  }, [isAuthenticated, isLoading, retryCount]);

  const checkBackendConnection = async () => {
    try {
      // Use the API service to check backend health with timeout
      const response = await Promise.race([
        apiService.get('/health'),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Health check timeout')), 5000)
        )
      ]);
      
      if (response && (response as any).status === 'ok') {
        setIsBackendReady(true);
        setRetryCount(0); // Reset retry count on success
        console.log('✅ Backend ist verfügbar, Chat-Funktionalität aktiviert');
      } else {
        throw new Error('Invalid health check response');
      }
    } catch (error) {
      console.warn('⚠️ Backend nicht sofort erreichbar, Chat wird trotzdem angezeigt', error);
      setIsBackendReady(true); // Allow chat to be shown anyway
      
      // Retry logic with exponential backoff
      if (retryCount < maxRetries) {
        const delay = Math.pow(2, retryCount) * 2000; // 2s, 4s, 8s
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
        }, delay);
      }
    }
  };

  // Don't render if still loading authentication status
  if (isLoading) {
    return null;
  }

  // Don't render if user is not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // Don't render on login/registration pages
  const currentPath = location.pathname;
  const isOnPublicPage = currentPath === '/login' || currentPath === '/register';
  if (isOnPublicPage) {
    return null;
  }

  // Always render ChatBot - it will handle backend unavailability gracefully
  return <ChatBot />;
};