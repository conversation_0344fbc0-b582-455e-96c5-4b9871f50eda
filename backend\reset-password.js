const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function resetPassword() {
  const prisma = new PrismaClient();
  
  try {
    // Hash das neue Passwort
    const newPassword = 'password';
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    // Update den jozi1 Benutzer
    const updatedUser = await prisma.user.update({
      where: { username: 'jozi1' },
      data: { passwordHash: hashedPassword }
    });
    
    console.log('✅ Passwort für jozi1 wurde auf "password" gesetzt');
    console.log('🔑 Login-Daten:');
    console.log('   Username: jozi1');
    console.log('   Password: password');
    
  } catch (error) {
    console.error('❌ Fehler beim Passwort-Reset:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetPassword();