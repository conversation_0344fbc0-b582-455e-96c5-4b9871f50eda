import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { AlertDialog as Dialog, AlertDialogContent as DialogContent, AlertDialogHeader as DialogHeader, AlertDialogTitle as DialogTitle, AlertDialogTrigger as DialogTrigger } from '@/components/ui/alert-dialog';
import {
  Users,
  Plus,
  Edit,
  Trash2,
  Phone,
  Mail,
  Building,
  GripVertical,
  RefreshCw,
  Save,
  X
} from 'lucide-react';
import { bereitschaftsService } from '@/services/bereitschaftsService';
import {
  BereitschaftsPerson,
  CreateBereitschaftsPersonRequest,
  UpdateBereitschaftsPersonRequest,
  ABTEILUNGEN,
  Abteilung
} from '@/types/bereitschafts';
import { useToast } from '@/components/ui/use-toast';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface BereitschaftsPersonenVerwaltungProps {
  onPersonenChange?: () => void;
}

interface SortablePersonItemProps {
  person: BereitschaftsPerson;
  index: number;
  onEdit: (person: BereitschaftsPerson) => void;
  onDelete: (person: BereitschaftsPerson) => void;
}

const SortablePersonItem: React.FC<SortablePersonItemProps> = ({
  person,
  index,
  onEdit,
  onDelete
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: person.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors ${isDragging ? 'shadow-lg z-10' : ''
        }`}
    >
      <div className="flex items-center gap-4">
        <div
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-200 rounded"
        >
          <GripVertical className="h-5 w-5 text-gray-400" />
        </div>

        <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-semibold text-sm">
          {index + 1}
        </div>

        <div>
          <h4 className="font-semibold text-gray-900">{person.name}</h4>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Building className="h-3 w-3" />
              <span>{person.abteilung}</span>
            </div>
            <div className="flex items-center gap-1">
              <Phone className="h-3 w-3" />
              <a href={`tel:${person.telefon}`} className="hover:text-blue-600">
                {person.telefon}
              </a>
            </div>
            <div className="flex items-center gap-1">
              <Mail className="h-3 w-3" />
              <a href={`mailto:${person.email}`} className="hover:text-blue-600">
                {person.email}
              </a>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Badge variant="outline" className="text-green-600 border-green-300">
          Aktiv
        </Badge>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onEdit(person)}
          className="text-blue-600 hover:text-blue-700"
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDelete(person)}
          className="text-red-600 hover:text-red-700"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export const BereitschaftsPersonenVerwaltung: React.FC<BereitschaftsPersonenVerwaltungProps> = ({
  onPersonenChange
}) => {
  const [personen, setPersonen] = useState<BereitschaftsPerson[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingPerson, setEditingPerson] = useState<BereitschaftsPerson | null>(null);
  const [formData, setFormData] = useState<CreateBereitschaftsPersonRequest>({
    name: '',
    telefon: '',
    email: '',
    abteilung: 'LSI'
  });
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadPersonen();
  }, []);

  const loadPersonen = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await bereitschaftsService.getAllPersonen();
      setPersonen(data);
    } catch (error) {
      console.error('Fehler beim Laden der Personen:', error);
      const message = error instanceof Error ? error.message : 'Personen konnten nicht geladen werden';
      setError(message);
      toast({
        title: 'Fehler beim Laden',
        description: message,
        type: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (person?: BereitschaftsPerson) => {
    if (person) {
      setEditingPerson(person);
      setFormData({
        name: person.name,
        telefon: person.telefon,
        email: person.email,
        abteilung: person.abteilung
      });
    } else {
      setEditingPerson(null);
      setFormData({
        name: '',
        telefon: '',
        email: '',
        abteilung: 'LSI'
      });
    }
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setEditingPerson(null);
    setFormData({
      name: '',
      telefon: '',
      email: '',
      abteilung: 'LSI'
    });
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Validierung
      if (!formData.name.trim()) {
        toast({
          title: 'Validierungsfehler',
          description: 'Name ist ein Pflichtfeld',
          type: 'destructive'
        });
        return;
      }

      if (!formData.telefon.trim()) {
        toast({
          title: 'Validierungsfehler',
          description: 'Telefon ist ein Pflichtfeld',
          type: 'destructive'
        });
        return;
      }

      if (!formData.email.trim()) {
        toast({
          title: 'Validierungsfehler',
          description: 'E-Mail ist ein Pflichtfeld',
          type: 'destructive'
        });
        return;
      }

      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        toast({
          title: 'Validierungsfehler',
          description: 'Ungültige E-Mail-Adresse',
          type: 'destructive'
        });
        return;
      }

      if (editingPerson) {
        await bereitschaftsService.updatePerson(editingPerson.id, formData);
        toast({
          title: 'Person aktualisiert',
          description: `${formData.name} wurde erfolgreich aktualisiert`,
          type: 'success'
        });
      } else {
        await bereitschaftsService.createPerson(formData);
        toast({
          title: 'Person erstellt',
          description: `${formData.name} wurde erfolgreich zur Bereitschaftsliste hinzugefügt`,
          type: 'success'
        });
      }

      await loadPersonen();
      handleCloseDialog();
      onPersonenChange?.();
    } catch (error) {
      console.error('Fehler beim Speichern der Person:', error);
      toast({
        title: 'Speichern fehlgeschlagen',
        description: error instanceof Error ? error.message : 'Person konnte nicht gespeichert werden',
        type: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (person: BereitschaftsPerson) => {
    if (!confirm(`Möchten Sie ${person.name} wirklich aus der Bereitschaftsliste entfernen?`)) {
      return;
    }

    try {
      await bereitschaftsService.deletePerson(person.id);
      toast({
        title: 'Person entfernt',
        description: `${person.name} wurde erfolgreich aus der Bereitschaftsliste entfernt`,
        type: 'success'
      });
      await loadPersonen();
      onPersonenChange?.();
    } catch (error) {
      console.error('Fehler beim Löschen der Person:', error);
      toast({
        title: 'Löschen fehlgeschlagen',
        description: error instanceof Error ? error.message : 'Person konnte nicht gelöscht werden',
        type: 'destructive'
      });
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const oldIndex = personen.findIndex(person => person.id === active.id);
      const newIndex = personen.findIndex(person => person.id === over.id);

      const newOrder = arrayMove(personen, oldIndex, newIndex);
      setPersonen(newOrder);

      try {
        await bereitschaftsService.updatePersonenReihenfolge({
          personenIds: newOrder.map(p => p.id)
        });
        toast({
          title: 'Reihenfolge aktualisiert',
          description: 'Die Bereitschafts-Reihenfolge wurde erfolgreich gespeichert',
          type: 'success'
        });
        onPersonenChange?.();
      } catch (error) {
        console.error('Fehler beim Ändern der Reihenfolge:', error);
        // Rollback bei Fehler
        setPersonen(personen);
        toast({
          title: 'Reihenfolge-Änderung fehlgeschlagen',
          description: 'Die Reihenfolge konnte nicht geändert werden',
          type: 'destructive'
        });
      }
    }
  };

  if (loading) {
    return (
      <Card >
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Personen-Verwaltung
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
            <span className="ml-2">Personen werden geladen...</span>
          </div>
        </CardContent>
      </Card>
   );
  }

  return (
    <div>
      <Card className="border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              Personen-Verwaltung
            </CardTitle>
            {error && (
              <div className="mt-2 rounded-md border border-red-300 bg-red-50 px-3 py-2 text-sm text-red-700">
                Fehler beim Laden: {error}
              </div>
            )}
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => handleOpenDialog()} variant="accept">
                <Plus className="h-4 w-4 mr-2" />
                Person hinzufügen
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingPerson ? 'Person bearbeiten' : 'Neue Person hinzufügen'}
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Vor- und Nachname"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="telefon">Telefon *</Label>
                  <Input
                    id="telefon"
                    value={formData.telefon}
                    onChange={(e) => setFormData(prev => ({ ...prev, telefon: e.target.value }))}
                    placeholder="+49 ************"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">E-Mail *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="abteilung">Abteilung</Label>
                  <Select
                    value={formData.abteilung}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, abteilung: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Abteilung auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {ABTEILUNGEN.map((abteilung) => (
                        <SelectItem key={abteilung} value={abteilung}>
                          {abteilung}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={handleCloseDialog} disabled={saving}>
                    <X className="h-4 w-4 mr-2" />
                    Abbrechen
                  </Button>
                  <Button onClick={handleSave} disabled={saving} variant="accept">
                    {saving ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    {saving ? 'Speichern...' : 'Speichern'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>

      <CardContent>
        {personen.length === 0 ? (
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Keine Personen vorhanden</h3>
            <p className="text-gray-600 mb-4">
              Fügen Sie Personen hinzu, um den Bereitschaftsplan zu erstellen.
            </p>
            <Button onClick={() => handleOpenDialog()} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Erste Person hinzufügen
            </Button>
          </div>
        ) : (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={personen.map(p => p.id)} strategy={verticalListSortingStrategy}>
              <div className="space-y-3">
                {personen.map((person, index) => (
                  <SortablePersonItem
                    key={person.id}
                    person={person}
                    index={index}
                    onEdit={handleOpenDialog}
                    onDelete={handleDelete}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        )}
      </CardContent>
      </Card>
      {personen.length > 0 && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Hinweis:</strong> Die Reihenfolge bestimmt die Rotation der Bereitschaftspersonen.
            Ziehen Sie die Personen mit dem <GripVertical className="inline h-4 w-4 mx-1" /> Symbol, um die Reihenfolge zu ändern.
          </p>
        </div>
      )}
    </div>
  );
};

export default BereitschaftsPersonenVerwaltung;