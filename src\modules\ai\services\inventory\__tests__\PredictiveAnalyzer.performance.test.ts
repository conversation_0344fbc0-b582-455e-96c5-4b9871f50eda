/**
 * Performance Tests for Predictive Analytics
 * 
 * Tests to ensure predictive analytics operations complete within acceptable time limits
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { PredictiveAnalyzer } from '../algorithms/PredictiveAnalyzer';
import { ConsumptionData, InventoryItem } from '../types';

describe('PredictiveAnalyzer Performance Tests', () => {
  let largeConsumptionDataset: ConsumptionData[];
  let manyInventoryItems: InventoryItem[];

  beforeEach(() => {
    // Create large dataset for performance testing
    largeConsumptionDataset = [];
    const startDate = new Date('2023-01-01');
    
    // Generate 365 days of data
    for (let i = 0; i < 365; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      // Simulate realistic consumption patterns
      const dayOfWeek = date.getDay();
      const dayOfMonth = date.getDate();
      const month = date.getMonth();
      
      // Weekly pattern (lower on weekends)
      const weeklyMultiplier = dayOfWeek === 0 || dayOfWeek === 6 ? 0.6 : 1.0;
      
      // Monthly pattern (higher at month start/end)
      const monthlyMultiplier = dayOfMonth <= 5 || dayOfMonth >= 25 ? 1.2 : 1.0;
      
      // Seasonal pattern (higher in winter months)
      const seasonalMultiplier = month >= 10 || month <= 2 ? 1.3 : 0.9;
      
      const baseConsumption = 15;
      const noise = (Math.random() - 0.5) * 4;
      const quantity = Math.max(0, baseConsumption * weeklyMultiplier * monthlyMultiplier * seasonalMultiplier + noise);
      
      largeConsumptionDataset.push({
        itemId: 'perf-test-item',
        date,
        quantity,
        value: quantity * 25.50
      });
    }

    // Create many inventory items for bulk operations
    manyInventoryItems = [];
    for (let i = 0; i < 100; i++) {
      manyInventoryItems.push({
        id: `item-${i.toString().padStart(3, '0')}`,
        name: `Test Item ${i}`,
        category: 'Test Category',
        currentStock: Math.floor(Math.random() * 1000) + 50,
        unitPrice: Math.random() * 100 + 10,
        supplier: `Supplier ${Math.floor(i / 10)}`,
        location: `Warehouse ${String.fromCharCode(65 + (i % 5))}`,
        lastUpdated: new Date()
      });
    }
  });

  describe('Seasonal Pattern Detection Performance', () => {
    it('should detect seasonal patterns within 2 seconds for 1 year of data', () => {
      const startTime = Date.now();
      
      const result = PredictiveAnalyzer.detectSeasonalPatterns(
        'perf-test-item',
        largeConsumptionDataset
      );
      
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(2000); // 2 seconds
      expect(result).toBeDefined();
      expect(result.seasonalityPattern.hasSeasonality).toBeDefined();
    });

    it('should handle multiple cycle detection efficiently', () => {
      const startTime = Date.now();
      
      const result = PredictiveAnalyzer.detectSeasonalPatterns(
        'perf-test-item',
        largeConsumptionDataset,
        {
          minCycleLength: 3,
          maxCycleLength: 90,
          significanceThreshold: 0.05
        }
      );
      
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(3000); // 3 seconds for comprehensive analysis
      expect(result.seasonalityPattern.patterns.length).toBeGreaterThanOrEqual(0);
    });

    it('should scale linearly with data size', () => {
      const smallDataset = largeConsumptionDataset.slice(0, 90); // 3 months
      const mediumDataset = largeConsumptionDataset.slice(0, 180); // 6 months
      const largeDataset = largeConsumptionDataset; // 12 months
      
      const startSmall = Date.now();
      PredictiveAnalyzer.detectSeasonalPatterns('test', smallDataset);
      const timeSmall = Date.now() - startSmall;
      
      const startMedium = Date.now();
      PredictiveAnalyzer.detectSeasonalPatterns('test', mediumDataset);
      const timeMedium = Date.now() - startMedium;
      
      const startLarge = Date.now();
      PredictiveAnalyzer.detectSeasonalPatterns('test', largeDataset);
      const timeLarge = Date.now() - startLarge;
      
      // Should scale roughly linearly (allowing for some variance)
      expect(timeMedium).toBeLessThan(timeSmall * 3);
      expect(timeLarge).toBeLessThan(timeSmall * 5);
    });
  });

  describe('Reorder Point Optimization Performance', () => {
    it('should optimize reorder point within 500ms', () => {
      const startTime = Date.now();
      
      const result = PredictiveAnalyzer.optimizeReorderPoint(
        'perf-test-item',
        500,
        largeConsumptionDataset,
        {
          targetServiceLevel: 0.95,
          leadTimeDays: 14,
          considerSeasonality: true
        }
      );
      
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(500); // 500ms
      expect(result.optimizedReorderPoint).toBeGreaterThan(0);
    });

    it('should handle complex seasonality optimization efficiently', () => {
      const startTime = Date.now();
      
      const result = PredictiveAnalyzer.optimizeReorderPoint(
        'perf-test-item',
        500,
        largeConsumptionDataset,
        {
          targetServiceLevel: 0.99,
          leadTimeDays: 30,
          safetyStockMultiplier: 1.5,
          considerSeasonality: true
        }
      );
      
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(1000); // 1 second for complex optimization
      expect(result.riskAssessment).toBeDefined();
    });
  });

  describe('Anomaly Detection Performance', () => {
    it('should detect anomalies for 100 items within 5 seconds', () => {
      // Create consumption data map for all items
      const consumptionDataMap = new Map<string, ConsumptionData[]>();
      
      manyInventoryItems.forEach((item, index) => {
        // Create smaller datasets for each item to simulate realistic scenario
        const itemData = largeConsumptionDataset.slice(0, 60).map(d => ({
          ...d,
          itemId: item.id
        }));
        
        // Add some anomalies to a few items
        if (index % 10 === 0) {
          itemData.push({
            itemId: item.id,
            date: new Date(),
            quantity: 100, // Anomalous spike
            value: 100 * item.unitPrice
          });
        }
        
        consumptionDataMap.set(item.id, itemData);
      });
      
      const startTime = Date.now();
      
      const result = PredictiveAnalyzer.detectStockAnomalies(
        manyInventoryItems,
        consumptionDataMap,
        { sensitivityLevel: 'medium' }
      );
      
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(5000); // 5 seconds for 100 items
      expect(result.totalItemsAnalyzed).toBe(100);
      expect(result.anomalies.length).toBeGreaterThanOrEqual(0);
    });

    it('should scale efficiently with number of items', () => {
      const createTestData = (itemCount: number) => {
        const items = manyInventoryItems.slice(0, itemCount);
        const dataMap = new Map<string, ConsumptionData[]>();
        
        items.forEach(item => {
          const itemData = largeConsumptionDataset.slice(0, 30).map(d => ({
            ...d,
            itemId: item.id
          }));
          dataMap.set(item.id, itemData);
        });
        
        return { items, dataMap };
      };
      
      // Test with different item counts
      const { items: items10, dataMap: dataMap10 } = createTestData(10);
      const { items: items50, dataMap: dataMap50 } = createTestData(50);
      
      const start10 = Date.now();
      PredictiveAnalyzer.detectStockAnomalies(items10, dataMap10);
      const time10 = Date.now() - start10;
      
      const start50 = Date.now();
      PredictiveAnalyzer.detectStockAnomalies(items50, dataMap50);
      const time50 = Date.now() - start50;
      
      // Should scale roughly linearly
      expect(time50).toBeLessThan(time10 * 6); // Allow some overhead
    });
  });

  describe('Consumption Pattern Analysis Performance', () => {
    it('should analyze consumption patterns within 300ms', () => {
      const startTime = Date.now();
      
      const result = PredictiveAnalyzer.analyzeConsumptionPatterns(
        'perf-test-item',
        largeConsumptionDataset,
        {
          trendWindow: 14,
          outlierThreshold: 2.5,
          patternMinLength: 7
        }
      );
      
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(300); // 300ms
      expect(result.patterns).toBeDefined();
      expect(result.outliers).toBeDefined();
    });

    it('should handle outlier detection efficiently', () => {
      // Create dataset with many outliers
      const dataWithOutliers = [...largeConsumptionDataset];
      
      // Add 50 outliers
      for (let i = 0; i < 50; i++) {
        const randomDate = new Date(largeConsumptionDataset[Math.floor(Math.random() * largeConsumptionDataset.length)].date);
        dataWithOutliers.push({
          itemId: 'perf-test-item',
          date: randomDate,
          quantity: Math.random() > 0.5 ? 200 : 0.1, // High or low outliers
          value: (Math.random() > 0.5 ? 200 : 0.1) * 25.50
        });
      }
      
      const startTime = Date.now();
      
      const result = PredictiveAnalyzer.analyzeConsumptionPatterns(
        'perf-test-item',
        dataWithOutliers,
        { outlierThreshold: 2.0 }
      );
      
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(500); // 500ms even with many outliers
      expect(result.outliers.length).toBeGreaterThan(0);
    });
  });

  describe('Memory Usage and Resource Management', () => {
    it('should not cause memory leaks with large datasets', () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Run multiple operations
      for (let i = 0; i < 10; i++) {
        PredictiveAnalyzer.detectSeasonalPatterns(
          `test-item-${i}`,
          largeConsumptionDataset
        );
        
        PredictiveAnalyzer.optimizeReorderPoint(
          `test-item-${i}`,
          100,
          largeConsumptionDataset
        );
        
        PredictiveAnalyzer.analyzeConsumptionPatterns(
          `test-item-${i}`,
          largeConsumptionDataset
        );
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });

    it('should handle concurrent operations efficiently', async () => {
      const startTime = Date.now();
      
      // Run multiple operations concurrently
      const operations = [];
      for (let i = 0; i < 5; i++) {
        operations.push(
          Promise.resolve(PredictiveAnalyzer.detectSeasonalPatterns(
            `concurrent-item-${i}`,
            largeConsumptionDataset.slice(i * 50, (i + 1) * 50 + 100)
          ))
        );
        
        operations.push(
          Promise.resolve(PredictiveAnalyzer.analyzeConsumptionPatterns(
            `concurrent-item-${i}`,
            largeConsumptionDataset.slice(i * 50, (i + 1) * 50 + 100)
          ))
        );
      }
      
      const results = await Promise.all(operations);
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(3000); // 3 seconds for concurrent operations
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result).toBeDefined();
      });
    });
  });

  describe('Edge Case Performance', () => {
    it('should handle empty datasets quickly', () => {
      const startTime = Date.now();
      
      const result = PredictiveAnalyzer.detectSeasonalPatterns('test', []);
      
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(10); // Should be nearly instantaneous
      expect(result.seasonalityPattern.hasSeasonality).toBe(false);
    });

    it('should handle single data point efficiently', () => {
      const singlePoint: ConsumptionData[] = [{
        itemId: 'test',
        date: new Date(),
        quantity: 10,
        value: 250
      }];
      
      const startTime = Date.now();
      
      const seasonalResult = PredictiveAnalyzer.detectSeasonalPatterns('test', singlePoint);
      const patternResult = PredictiveAnalyzer.analyzeConsumptionPatterns('test', singlePoint);
      
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(50); // Very fast for single point
      expect(seasonalResult.seasonalityPattern.hasSeasonality).toBe(false);
      expect(patternResult.patterns.length).toBeGreaterThanOrEqual(0);
    });

    it('should handle extreme values without performance degradation', () => {
      const extremeData: ConsumptionData[] = [];
      
      for (let i = 0; i < 100; i++) {
        extremeData.push({
          itemId: 'extreme-test',
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
          quantity: i === 50 ? 1000000 : 10, // One extreme value
          value: (i === 50 ? 1000000 : 10) * 25.50
        });
      }
      
      const startTime = Date.now();
      
      const result = PredictiveAnalyzer.analyzeConsumptionPatterns('extreme-test', extremeData);
      
      const executionTime = Date.now() - startTime;
      
      expect(executionTime).toBeLessThan(200); // Should handle extreme values efficiently
      expect(result.outliers.length).toBeGreaterThan(0);
    });
  });
});