import { aiModuleConfig } from '../module.config';
import { AIPerformanceMonitor } from '../services/performance/AIPerformanceMonitor';

/**
 * AI Module Health Check System
 * 
 * Provides comprehensive health monitoring for the AI module,
 * integrating with the existing application monitoring infrastructure.
 */
export class AIModuleHealthCheck {
  private performanceMonitor: AIPerformanceMonitor;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private lastHealthStatus: AIModuleHealthStatus | null = null;

  constructor() {
    this.performanceMonitor = new AIPerformanceMonitor();
  }

  /**
   * Start continuous health monitoring
   */
  startHealthMonitoring(intervalMs: number = 30000): void {
    console.log('🏥 Starting AI Module health monitoring...');
    
    // Initial health check
    this.performHealthCheck();
    
    // Set up periodic health checks
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, intervalMs);
  }

  /**
   * Stop health monitoring
   */
  stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('🛑 AI Module health monitoring stopped');
    }
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck(): Promise<AIModuleHealthStatus> {
    const startTime = Date.now();
    
    try {
      console.log('🔍 Performing AI Module health check...');
      
      const healthStatus: AIModuleHealthStatus = {
        timestamp: new Date().toISOString(),
        moduleId: aiModuleConfig.id,
        overallStatus: 'HEALTHY',
        components: {},
        metrics: {},
        issues: [],
        recommendations: []
      };

      // Check module configuration health
      await this.checkModuleConfiguration(healthStatus);
      
      // Check service health
      await this.checkServiceHealth(healthStatus);
      
      // Check performance metrics
      await this.checkPerformanceMetrics(healthStatus);
      
      // Check security status
      await this.checkSecurityStatus(healthStatus);
      
      // Check resource usage
      await this.checkResourceUsage(healthStatus);
      
      // Determine overall status
      this.determineOverallStatus(healthStatus);
      
      // Store last health status
      this.lastHealthStatus = healthStatus;
      
      const duration = Date.now() - startTime;
      console.log(`✅ AI Module health check completed in ${duration}ms - Status: ${healthStatus.overallStatus}`);
      
      return healthStatus;
    } catch (error) {
      console.error('❌ AI Module health check failed:', error);
      
      const errorStatus: AIModuleHealthStatus = {
        timestamp: new Date().toISOString(),
        moduleId: aiModuleConfig.id,
        overallStatus: 'CRITICAL',
        components: {},
        metrics: {},
        issues: [`Health check failed: ${error}`],
        recommendations: ['Investigate health check system failure']
      };
      
      this.lastHealthStatus = errorStatus;
      return errorStatus;
    }
  }

  /**
   * Get last health status
   */
  getLastHealthStatus(): AIModuleHealthStatus | null {
    return this.lastHealthStatus;
  }

  /**
   * Get health summary for monitoring dashboard
   */
  getHealthSummary(): AIModuleHealthSummary {
    const status = this.lastHealthStatus;
    
    if (!status) {
      return {
        moduleId: aiModuleConfig.id,
        status: 'UNKNOWN',
        lastCheck: null,
        issueCount: 0,
        uptime: 0
      };
    }

    return {
      moduleId: status.moduleId,
      status: status.overallStatus,
      lastCheck: status.timestamp,
      issueCount: status.issues.length,
      uptime: this.calculateUptime()
    };
  }

  /**
   * Check module configuration health
   */
  private async checkModuleConfiguration(healthStatus: AIModuleHealthStatus): Promise<void> {
    try {
      // Validate module configuration
      if (!aiModuleConfig.isEnabled) {
        healthStatus.components.configuration = 'DISABLED';
        healthStatus.issues.push('AI module is disabled in configuration');
        return;
      }

      // Check required configuration properties
      const requiredProps = ['id', 'name', 'baseRoute', 'requiredRoles'];
      const missingProps = requiredProps.filter(prop => !aiModuleConfig[prop as keyof typeof aiModuleConfig]);
      
      if (missingProps.length > 0) {
        healthStatus.components.configuration = 'UNHEALTHY';
        healthStatus.issues.push(`Missing configuration properties: ${missingProps.join(', ')}`);
        return;
      }

      healthStatus.components.configuration = 'HEALTHY';
    } catch (error) {
      healthStatus.components.configuration = 'CRITICAL';
      healthStatus.issues.push(`Configuration check failed: ${error}`);
    }
  }

  /**
   * Check service health
   */
  private async checkServiceHealth(healthStatus: AIModuleHealthStatus): Promise<void> {
    try {
      // Check AI services availability
      const services = ['chat', 'analysis', 'optimization'];
      
      for (const service of services) {
        try {
          // This would normally test actual service availability
          healthStatus.components[`service-${service}`] = 'HEALTHY';
        } catch (error) {
          healthStatus.components[`service-${service}`] = 'UNHEALTHY';
          healthStatus.issues.push(`Service ${service} is not responding`);
        }
      }
    } catch (error) {
      healthStatus.issues.push(`Service health check failed: ${error}`);
    }
  }

  /**
   * Check performance metrics
   */
  private async checkPerformanceMetrics(healthStatus: AIModuleHealthStatus): Promise<void> {
    try {
      // Get performance metrics from monitor
      const metrics = await this.performanceMonitor.getMetrics();
      
      healthStatus.metrics = {
        responseTime: metrics.averageResponseTime || 0,
        throughput: metrics.requestsPerSecond || 0,
        errorRate: metrics.errorRate || 0,
        memoryUsage: metrics.memoryUsage || 0,
        cpuUsage: metrics.cpuUsage || 0
      };

      // Check performance thresholds
      if (healthStatus.metrics.responseTime > 5000) {
        healthStatus.issues.push('High response time detected');
        healthStatus.recommendations.push('Consider optimizing AI service performance');
      }

      if (healthStatus.metrics.errorRate > 0.05) {
        healthStatus.issues.push('High error rate detected');
        healthStatus.recommendations.push('Investigate and fix recurring errors');
      }

      healthStatus.components.performance = healthStatus.issues.length === 0 ? 'HEALTHY' : 'DEGRADED';
    } catch (error) {
      healthStatus.components.performance = 'CRITICAL';
      healthStatus.issues.push(`Performance metrics check failed: ${error}`);
    }
  }

  /**
   * Check security status
   */
  private async checkSecurityStatus(healthStatus: AIModuleHealthStatus): Promise<void> {
    try {
      const securityConfig = aiModuleConfig.security;
      
      // Check security configuration
      const initialIssueCount = healthStatus.issues.length;
      
      if (!securityConfig.requiresAuthentication) {
        healthStatus.issues.push('Authentication not required - security risk');
        healthStatus.recommendations.push('Enable authentication for AI module');
      }

      if (!securityConfig.enableInputValidation) {
        healthStatus.issues.push('Input validation disabled - security risk');
        healthStatus.recommendations.push('Enable input validation');
      }

      if (!securityConfig.enableAuditLogging) {
        healthStatus.issues.push('Audit logging disabled - compliance risk');
        healthStatus.recommendations.push('Enable audit logging');
      }

      // Check API key validation
      if (!securityConfig.apiKeyValidation?.enabled) {
        healthStatus.issues.push('API key validation disabled');
        healthStatus.recommendations.push('Enable API key validation');
      }

      const securityIssues = healthStatus.issues.length - initialIssueCount;
      healthStatus.components.security = securityIssues === 0 ? 'HEALTHY' : 'DEGRADED';
    } catch (error) {
      healthStatus.components.security = 'CRITICAL';
      healthStatus.issues.push(`Security status check failed: ${error}`);
    }
  }

  /**
   * Check resource usage
   */
  private async checkResourceUsage(healthStatus: AIModuleHealthStatus): Promise<void> {
    try {
      // Check memory usage
      const memoryUsage = process.memoryUsage();
      const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;
      
      if (memoryUsageMB > 500) {
        healthStatus.issues.push('High memory usage detected');
        healthStatus.recommendations.push('Monitor memory usage and optimize if necessary');
      }

      healthStatus.components.resources = 'HEALTHY';
    } catch (error) {
      healthStatus.components.resources = 'CRITICAL';
      healthStatus.issues.push(`Resource usage check failed: ${error}`);
    }
  }

  /**
   * Determine overall health status
   */
  private determineOverallStatus(healthStatus: AIModuleHealthStatus): void {
    const componentStatuses = Object.values(healthStatus.components);
    
    if (componentStatuses.includes('CRITICAL')) {
      healthStatus.overallStatus = 'CRITICAL';
    } else if (componentStatuses.includes('UNHEALTHY')) {
      healthStatus.overallStatus = 'UNHEALTHY';
    } else if (componentStatuses.includes('DEGRADED')) {
      healthStatus.overallStatus = 'DEGRADED';
    } else if (componentStatuses.includes('DISABLED')) {
      healthStatus.overallStatus = 'DISABLED';
    } else {
      healthStatus.overallStatus = 'HEALTHY';
    }
  }

  /**
   * Calculate module uptime
   */
  private calculateUptime(): number {
    // This would normally track actual uptime
    return Date.now();
  }
}

/**
 * AI Module Health Status Interface
 */
export interface AIModuleHealthStatus {
  timestamp: string;
  moduleId: string;
  overallStatus: 'HEALTHY' | 'DEGRADED' | 'UNHEALTHY' | 'CRITICAL' | 'DISABLED';
  components: Record<string, string>;
  metrics: Record<string, number>;
  issues: string[];
  recommendations: string[];
}

/**
 * AI Module Health Summary Interface
 */
export interface AIModuleHealthSummary {
  moduleId: string;
  status: string;
  lastCheck: string | null;
  issueCount: number;
  uptime: number;
}

/**
 * Global health check instance
 */
export const aiModuleHealthCheck = new AIModuleHealthCheck();