{"compilerOptions": {"jsx": "react-jsx", "target": "ES2020", "module": "ESNext", "moduleResolution": "node", "lib": ["dom", "dom.iterable", "ESNext"], "skipLibCheck": true, "experimentalDecorators": true, "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "strict": true, "noImplicitAny": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsxImportSource": "@emotion/react", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "outDir": "dist", "types": ["vite/client", "node"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "src/**/*.json", "package.json", "forge.config.ts"], "exclude": ["node_modules", "dist"]}