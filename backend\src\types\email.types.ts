/**
 * E-Mail-Service Typen und Interfaces
 * 
 * Diese <PERSON>i definiert alle TypeScript-Typen und Interfaces,
 * die für den E-Mail-Service verwendet werden.
 */

/**
 * Konfiguration für den E-Mail-Service
 */
export interface EmailConfig {
  /** SMTP-Server Host */
  host: string;
  /** SMTP-Server Port */
  port: number;
  /** Sichere Verbindung verwenden (TLS/SSL) */
  secure: boolean;
  /** Authentifizierungsdaten */
  auth: {
    user: string;
    pass: string;
  };
  /** Absender-Informationen */
  from: {
    name: string;
    address: string;
  };
  /** Connection-Pool-Einstellungen */
  pool?: boolean;
  /** Maximale Anzahl gleichzeitiger Verbindungen */
  maxConnections?: number;
  /** Maximale Anzahl Nachrichten pro Verbindung */
  maxMessages?: number;
  /** Rate-Limiting (Nachrichten pro Sekunde) */
  rateDelta?: number;
  rateLimit?: number;
  /** Timeout-Einstellungen (in Millisekunden) */
  connectionTimeout?: number;
  greetingTimeout?: number;
  socketTimeout?: number;
}

/**
 * E-Mail-Anhang Interface
 */
export interface EmailAttachment {
  /** Dateiname des Anhangs */
  filename: string;
  /** Dateipfad oder Buffer-Inhalt */
  path?: string;
  content?: Buffer | string;
  /** MIME-Typ der Datei */
  contentType?: string;
  /** Content-ID für eingebettete Bilder */
  cid?: string;
  /** Encoding für den Anhang */
  encoding?: string;
}

/**
 * E-Mail-Optionen für das Senden
 */
export interface EmailOptions {
  /** Empfänger (einzeln oder Array) */
  to: string | string[];
  /** CC-Empfänger (optional) */
  cc?: string | string[];
  /** BCC-Empfänger (optional) */
  bcc?: string | string[];
  /** E-Mail-Betreff */
  subject: string;
  /** Nur-Text-Inhalt */
  text?: string;
  /** HTML-Inhalt */
  html?: string;
  /** Anhänge */
  attachments?: EmailAttachment[];
  /** Reply-To-Adresse */
  replyTo?: string;
  /** Priorität der E-Mail */
  priority?: 'high' | 'normal' | 'low';
  /** Custom Headers */
  headers?: { [key: string]: string };
}

/**
 * Störungsdaten für E-Mail-Templates
 */
export interface StoerungEmailData {
  /** Titel der Störung */
  title: string;
  /** Beschreibung der Störung */
  description: string;
  /** Schweregrad der Störung */
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  /** Betroffenes System */
  affected_system: string;
  /** Standort (optional) */
  location?: string;
  /** Melder (optional) */
  reported_by?: string;
  /** Zeitstempel der Meldung */
  created_at?: Date;
  /** Störungs-ID */
  id?: string;
}

/**
 * Template-Variablen für E-Mail-Templates
 */
export interface EmailTemplateVars {
  [key: string]: string | number | boolean | Date;
}

/**
 * E-Mail-Service-Antwort
 */
export interface EmailServiceResponse {
  /** Erfolgreich gesendet */
  success: boolean;
  /** Fehlermeldung (falls vorhanden) */
  error?: string;
  /** Nachrichten-ID */
  messageId?: string;
  /** Zusätzliche Informationen */
  info?: any;
}

/**
 * E-Mail-Template-Konfiguration
 */
export interface EmailTemplate {
  /** Template-Name */
  name: string;
  /** Pfad zur HTML-Template-Datei */
  htmlPath: string;
  /** Pfad zur Text-Template-Datei (optional) */
  textPath?: string;
  /** Standard-Betreff */
  defaultSubject: string;
  /** Erforderliche Template-Variablen */
  requiredVars: string[];
}

/**
 * Schweregrad-Konfiguration für Störungs-E-Mails
 */
export interface SeverityConfig {
  /** Anzeigename */
  text: string;
  /** Farbe für UI */
  color: string;
  /** CSS-Klasse */
  cssClass: string;
  /** Priorität (höher = wichtiger) */
  priority: number;
}

/**
 * E-Mail-Service-Statistiken
 */
export interface EmailServiceStats {
  /** Anzahl gesendeter E-Mails */
  sentCount: number;
  /** Anzahl fehlgeschlagener E-Mails */
  failedCount: number;
  /** Letzte Aktivität */
  lastActivity: Date;
  /** Durchschnittliche Sendezeit (ms) */
  averageSendTime: number;
}

/**
 * E-Mail-Validierungsregeln
 */
export interface EmailValidationRules {
  /** Maximale Anzahl Empfänger */
  maxRecipients: number;
  /** Maximale Betreff-Länge */
  maxSubjectLength: number;
  /** Maximale Nachrichtengröße (Bytes) */
  maxMessageSize: number;
  /** Erlaubte Anhang-Typen */
  allowedAttachmentTypes: string[];
  /** Maximale Anhang-Größe (Bytes) */
  maxAttachmentSize: number;
  /** Maximale Anzahl Anhänge */
  maxAttachments: number;
}