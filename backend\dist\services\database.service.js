"use strict";
/**
 * Zentrale Datenbankservice für das Backend
 *
 * Dieser Service nutzt Prisma für Datenbankoperationen und stellt
 * eine einheitliche Schnittstelle für alle Datenbankzugriffe bereit.
 *
 * V2: Erweitert um intelligentes Caching für Performance-Optimierung
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const client_1 = require("@prisma-sfm-dashboard/client");
const cache_service_1 = require("./cache.service");
const repository_factory_1 = require("../repositories/repository.factory");
/**
 * Cache-TTL-Konfiguration für verschiedene Datentypen
 */
const CACHE_TTL = {
    // Statische/seltene Änderungen - Lange Cache-Zeit
    MACHINE_EFFICIENCY: 15 * 60 * 1000, // 15 Minuten
    SCHNITTE_DATA: 15 * 60 * 1000, // 15 Minuten
    // Moderate Änderungen - Mittlere Cache-Zeit  
    WAREHOUSE_DATA: 5 * 60 * 1000, // 5 Minuten
    ABLANGEREI_DATA: 5 * 60 * 1000, // 5 Minuten
    WE_DATA: 5 * 60 * 1000, // 5 Minuten
    // Häufige Änderungen - Kurze Cache-Zeit
    DISPATCH_DATA: 2 * 60 * 1000, // 2 Minuten
    PICKING_DATA: 2 * 60 * 1000, // 2 Minuten
    RETURNS_DATA: 2 * 60 * 1000, // 2 Minuten
    ARIL_ATRL_DATA: 3 * 60 * 1000, // 3 Minuten
    // Echtzeit-ähnliche Daten - Sehr kurze Cache-Zeit
    SERVICE_LEVEL: 1 * 60 * 1000, // 1 Minute
    DAILY_PERFORMANCE: 1 * 60 * 1000, // 1 Minute
    SYSTEM_FTS_DATA: 5 * 60 * 1000, // 5 Minuten - System-Verfügbarkeitsdaten
    SYSTEM_STATS: 30 * 1000 // 30 Sekunden
};
/**
 * Zentrale Datenbankservice-Klasse
 *
 * Verwendet Prisma für alle Datenbankoperationen und bietet
 * eine einheitliche API für Frontend-Anfragen.
 *
 * V2: Erweitert um intelligentes Caching für Performance-Optimierung
 */
class DatabaseService {
    constructor() {
        this.cache = (0, cache_service_1.getBackendCache)({
            defaultTTL: 5 * 60 * 1000, // 5 Minuten Standard
            maxMemoryMB: 100, // 100MB für Datenbankserver
            maxEntries: 2000, // Mehr Entries für Datenbank-Cache
            cleanupInterval: 2 * 60 * 1000, // 2 Minuten Cleanup
            enableLogging: process.env.NODE_ENV === 'development'
        });
        try {
            console.log('Initialisiere Prisma-Client mit Caching und Repository Pattern...');
            this.prisma = new client_1.PrismaClient({
                log: ['query', 'info', 'warn', 'error'],
                errorFormat: 'pretty',
            });
            // Repository Factory initialisieren
            (0, repository_factory_1.initializeRepositoryFactory)(this.prisma);
            console.log('✅ Prisma-Client mit Backend-Cache und Repository Pattern erfolgreich initialisiert');
        }
        catch (error) {
            console.error('Fehler bei der Initialisierung des Prisma-Clients:', error);
            throw error;
        }
    }
    /**
     * Initialisiert die Datenbankverbindung
     */
    async connect() {
        try {
            console.log('Stelle Verbindung zur Datenbank her...');
            await this.prisma.$connect();
            // Testabfrage durchführen, um die Verbindung zu überprüfen
            await this.prisma.$queryRaw `SELECT 1 as test`;
            console.log('✅ Datenbankverbindung erfolgreich hergestellt');
            return true;
        }
        catch (error) {
            console.error('❌ Fehler beim Verbinden mit der Datenbank:');
            // Type Guard für Error-Objekte
            const isErrorWithMessage = (e) => e !== null && typeof e === 'object' && 'message' in e;
            const isErrorWithCode = (e) => isErrorWithMessage(e) && 'code' in e;
            // Fehlermeldung ausgeben
            if (isErrorWithMessage(error)) {
                console.error('Fehlermeldung:', error.message);
                // Spezifische Fehlercodes behandeln
                if (isErrorWithCode(error)) {
                    switch (error.code) {
                        case 'ENOENT':
                            console.error('Die Datenbankdatei wurde nicht gefunden. Bitte überprüfen Sie den Pfad.');
                            break;
                        case 'EACCES':
                            console.error('Keine Berechtigung zum Zugriff auf die Datenbank. Bitte überprüfen Sie die Dateiberechtigungen.');
                            break;
                        case 'SQLITE_CORRUPT':
                            console.error('Die Datenbankdatei ist beschädigt. Bitte führen Sie eine Sicherung wiederher.');
                            break;
                        default:
                            console.error('Fehlercode:', error.code);
                    }
                }
                // Stack Trace ausgeben, falls verfügbar
                if (error.stack) {
                    console.error('Stack Trace:', error.stack);
                }
            }
            else {
                console.error('Unbekannter Fehler aufgetreten:', String(error));
            }
            return false;
        }
    }
    /**
     * Schließt die Datenbankverbindung und beendet Cache
     */
    async disconnect() {
        await this.prisma.$disconnect();
        this.cache.destroy();
    }
    /**
     * Cache-Statistiken abrufen (für Monitoring)
     */
    getCacheStats() {
        return this.cache.getStats();
    }
    /**
     * Cache für bestimmte Datentypen invalidieren
     * @param dataTypes Array von Datentypen zum Invalidieren
     */
    invalidateCache(dataTypes = ['dispatch', 'warehouse', 'cutting']) {
        return this.cache.invalidateByDataTypes(dataTypes);
    }
    /**
     * Kompletten Cache leeren (für Development/Testing)
     */
    clearAllCache() {
        this.cache.destroy();
        // Cache wird automatisch neu initialisiert beim nächsten Zugriff
    }
    /**
     * Ruft die Servicelevel-Daten für das Diagramm ab (mit Repository Pattern)
     */
    async getServiceLevelData() {
        try {
            const dispatchRepository = (0, repository_factory_1.getDispatchRepository)();
            return await dispatchRepository.getServiceLevelData();
        }
        catch (error) {
            console.error('Fehler beim Abrufen der ServiceLevel-Daten:', error);
            throw error;
        }
    }
    /**
     * Ruft die täglichen Leistungsdaten für das Diagramm ab (mit Repository Pattern)
     */
    async getDailyPerformanceData() {
        try {
            const dispatchRepository = (0, repository_factory_1.getDispatchRepository)();
            return await dispatchRepository.getDailyPerformanceData();
        }
        catch (error) {
            console.error('Fehler beim Abrufen der täglichen Leistungsdaten:', error);
            throw error;
        }
    }
    /**
     * Ruft die Kommissionierungsdaten für das Diagramm ab
     */
    async getPickingData() {
        try {
            const result = await this.prisma.dispatchData.findMany({
                select: {
                    datum: true,
                    atrl: true,
                    aril: true,
                    fuellgrad_aril: true,
                },
                where: {
                    datum: { not: null },
                    atrl: { not: null },
                    aril: { not: null },
                    fuellgrad_aril: { not: null },
                },
                orderBy: {
                    datum: 'asc',
                },
            });
            return result.map((item) => ({
                date: item.datum,
                atrl: item.atrl || 0,
                aril: item.aril || 0,
                fuellgrad_aril: item.fuellgrad_aril || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Kommissionierungsdaten:', error);
            throw error;
        }
    }
    /**
     * Ruft die Retourendaten für das Diagramm ab (mit Repository Pattern)
     */
    async getReturnsData() {
        try {
            const dispatchRepository = (0, repository_factory_1.getDispatchRepository)();
            return await dispatchRepository.getReturnsData();
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Retourendaten:', error);
            throw error;
        }
    }
    /**
     * Ruft die Lieferpositionsdaten für das Diagramm ab
     */
    async getDeliveryPositionsData() {
        try {
            const result = await this.prisma.dispatchData.findMany({
                select: {
                    datum: true,
                    ausgeliefert_lup: true,
                    rueckstaendig: true,
                },
                where: {
                    datum: { not: null },
                    ausgeliefert_lup: { not: null },
                    rueckstaendig: { not: null },
                },
                orderBy: {
                    datum: 'asc',
                },
            });
            return result.map((item) => ({
                date: item.datum,
                ausgeliefert_lup: item.ausgeliefert_lup || 0,
                rueckstaendig: item.rueckstaendig || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Lieferpositionsdaten:', error);
            throw error;
        }
    }
    /**
     * Ruft die Tagesleistungsdaten für das Diagramm ab
     */
    async getTagesleistungData() {
        try {
            const result = await this.prisma.dispatchData.findMany({
                select: {
                    datum: true,
                    produzierte_tonnagen: true,
                    direktverladung_kiaa: true,
                    umschlag: true,
                    kg_pro_colli: true,
                    elefanten: true,
                },
                where: {
                    datum: { not: null },
                    produzierte_tonnagen: { not: null },
                },
                orderBy: {
                    datum: 'asc',
                },
            });
            return result.map((item) => ({
                date: item.datum,
                produzierte_tonnagen: item.produzierte_tonnagen || 0,
                direktverladung_kiaa: item.direktverladung_kiaa || 0,
                umschlag: item.umschlag || 0,
                kg_pro_colli: item.kg_pro_colli || 0,
                elefanten: item.elefanten || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Tagesleistungsdaten:', error);
            throw error;
        }
    }
    /**
     * Importiert die Daten aus der Abl.csv-Datei in die Ablaengerei-Tabelle
     */
    async importAblaengereiCsvData() {
        try {
            // CSV-Import-Logik mit Prisma implementieren
            // Vorläufig: Erfolg zurückgeben
            return true;
        }
        catch (error) {
            console.error('Fehler beim Importieren der Ablaengerei-CSV-Daten:', error);
            return false;
        }
    }
    /**
     * Ruft die Daten aus der Ablaengerei-Tabelle ab
     */
    async getAblaengereiData() {
        try {
            const result = await this.prisma.ablaengerei.findMany({
                orderBy: {
                    Datum: 'asc',
                },
            });
            return result.map((item) => ({
                id: item.id,
                datum: item.Datum || '',
                cutTT: item.cutTT || 0,
                cutTR: item.cutTR || 0,
                cutRR: item.cutRR || 0,
                pickCut: item.pickCut || 0,
                lagerCut220: item.lagerCut220 || 0,
                lagerCut240: item.lagerCut240 || 0,
                lagerCut200: item.lagerCut200 || 0,
                cutLagerK200: item.cutLagerK200 || 0,
                cutLagerK240: item.cutLagerK240 || 0,
                cutLagerK220: item.cutLagerK220 || 0,
                cutLager200: item.cutLager200 || 0,
                cutLagerR240: item.cutLagerR240 || 0,
                cutLagerR220: item.cutLagerR220 || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Ablaengerei-Daten:', error);
            throw error;
        }
    }
    /**
     * Ruft die WE-Daten (Wareneingang) für das Diagramm ab
     */
    async getWEData() {
        try {
            const result = await this.prisma.wE.findMany({
                orderBy: {
                    Datum: 'asc',
                },
            });
            return result.map((item) => ({
                id: item.id,
                datum: item.Datum || '',
                weAtrl: item.weAtrl || 0,
                weManl: item.weManl || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der WE-Daten:', error);
            throw error;
        }
    }
    /**
     * Ruft die Lagerauslastung200-Daten für das Diagramm ab (mit Caching)
     * @param startDate - Optionales Startdatum im Format YYYY-MM-DD
     * @param endDate - Optionales Enddatum im Format YYYY-MM-DD
     */
    async getLagerauslastung200Data(startDate, endDate) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forService('DatabaseService', 'getLagerauslastung200Data', {
            startDate,
            endDate
        });
        return await this.cache.cachedQuery(cacheKey, async () => {
            try {
                // Basisabfrage erstellen
                let query = {
                    select: {
                        aufnahmeDatum: true,
                        auslastungA: true,
                        auslastungB: true,
                        auslastungC: true
                    },
                    orderBy: {
                        aufnahmeDatum: 'asc'
                    }
                };
                // Datumsfilter hinzufügen, wenn vorhanden
                if (startDate && endDate) {
                    query.where = {
                        aufnahmeDatum: {
                            gte: startDate,
                            lte: endDate
                        }
                    };
                }
                else if (startDate) {
                    query.where = {
                        aufnahmeDatum: {
                            gte: startDate
                        }
                    };
                }
                else if (endDate) {
                    query.where = {
                        aufnahmeDatum: {
                            lte: endDate
                        }
                    };
                }
                // Daten mit Prisma abfragen
                const results = await this.prisma.auslastung200.findMany(query);
                // Daten in das erwartete Format umwandeln und Gesamt-Wert berechnen
                const mappedResults = results.map((item) => {
                    // Auslastungswerte als Dezimalzahl (0-1) parsen
                    const auslastungA = parseFloat(item.auslastungA || '0');
                    const auslastungB = parseFloat(item.auslastungB || '0');
                    const auslastungC = parseFloat(item.auslastungC || '0');
                    // Gesamtdurchschnitt berechnen (als Dezimalzahl belassen)
                    const gesamt = (auslastungA + auslastungB + auslastungC) / 3;
                    return {
                        date: item.aufnahmeDatum || '',
                        auslastungA,
                        auslastungB,
                        auslastungC,
                        gesamt
                    };
                });
                return mappedResults;
            }
            catch (error) {
                console.error('Fehler beim Abrufen der Lagerauslastung200-Daten:', error);
                throw error;
            }
        }, CACHE_TTL.WAREHOUSE_DATA);
    }
    /**
     * Ruft die Lagerauslastung240-Daten für das Diagramm ab
     * @param startDate - Optionales Startdatum im Format YYYY-MM-DD
     * @param endDate - Optionales Enddatum im Format YYYY-MM-DD
     */
    async getLagerauslastung240Data(startDate, endDate) {
        try {
            // Basisabfrage erstellen
            let query = {
                select: {
                    aufnahmeDatum: true,
                    auslastungA: true,
                    auslastungB: true,
                    auslastungC: true
                },
                orderBy: {
                    aufnahmeDatum: 'asc'
                }
            };
            // Datumsfilter hinzufügen, wenn vorhanden
            if (startDate && endDate) {
                query.where = {
                    aufnahmeDatum: {
                        gte: startDate,
                        lte: endDate
                    }
                };
            }
            else if (startDate) {
                query.where = {
                    aufnahmeDatum: {
                        gte: startDate
                    }
                };
            }
            else if (endDate) {
                query.where = {
                    aufnahmeDatum: {
                        lte: endDate
                    }
                };
            }
            // Daten mit Prisma abfragen
            const results = await this.prisma.auslastung240.findMany(query);
            // Daten in das erwartete Format umwandeln und Gesamt-Wert berechnen
            const mappedResults = results.map((item) => {
                // Auslastungswerte als Dezimalzahl (0-1) parsen
                const auslastungA = parseFloat(item.auslastungA || '0');
                const auslastungB = parseFloat(item.auslastungB || '0');
                const auslastungC = parseFloat(item.auslastungC || '0');
                // Gesamtdurchschnitt berechnen (als Dezimalzahl belassen)
                const gesamt = (auslastungA + auslastungB + auslastungC) / 3;
                return {
                    date: item.aufnahmeDatum || '',
                    auslastungA,
                    auslastungB,
                    auslastungC,
                    gesamt
                };
            });
            return mappedResults;
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Lagerauslastung240-Daten:', error);
            throw error;
        }
    }
    async getSchnitteData() {
        try {
            return await this.prisma.schnitte.findMany();
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Schnitte-Daten:', error);
            throw error;
        }
    }
    async getAtrlData(startDate, endDate) {
        try {
            const where = {};
            if (startDate || endDate) {
                where.Datum = {};
                if (startDate) {
                    where.Datum.gte = new Date(startDate);
                }
                if (endDate) {
                    where.Datum.lte = new Date(endDate);
                }
            }
            const result = await this.prisma.aTrL.findMany({
                where,
                orderBy: {
                    Datum: 'asc',
                },
            });
            return result.map((item) => ({
                Datum: item.Datum ? new Date(item.Datum).toISOString().split('T')[0] : '',
                weAtrl: item.weAtrl || 0,
                waTaPositionen: item.waTaPositionen || 0,
                EinlagerungAblKunde: item.EinlagerungAblKunde || 0,
                EinlagerungAblRest: item.EinlagerungAblRest || 0,
                umlagerungen: item.umlagerungen || 0,
                AuslagerungAbl: item.AuslagerungAbl || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der ARiL-Daten:', error);
            throw error;
        }
    }
    async getArilData(startDate, endDate) {
        try {
            const where = {};
            if (startDate || endDate) {
                where.Datum = {};
                if (startDate) {
                    where.Datum.gte = startDate;
                }
                if (endDate) {
                    where.Datum.lte = endDate;
                }
            }
            const result = await this.prisma.aRiL.findMany({
                where,
                orderBy: {
                    Datum: 'asc',
                },
            });
            return result.map((item) => ({
                Datum: item.Datum || '',
                waTaPositionen: item.waTaPositionen || 0,
                cuttingLagerKunde: item.cuttingLagerKunde || 0,
                cuttingLagerRest: item.cuttingLagerRest || 0,
                Umlagerungen: item.Umlagerungen || 0,
                lagerCutting: item.lagerCutting || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der ARiL-Daten:', error);
            throw error;
        }
    }
    async getMaschinenEfficiency() {
        try {
            const schnitte = await this.prisma.schnitte.findMany();
            const maschinen = await this.prisma.maschinen.findMany();
            const maschinenMap = new Map(maschinen.map((m) => [m.Machine, m.schnitteProStd]));
            const efficiencyData = schnitte.flatMap((s) => {
                if (!s.Datum)
                    return [];
                return Object.entries(s)
                    .map(([key, value]) => {
                    if (key.startsWith('M') && typeof value === 'number') {
                        // Konvertiere Prisma-Key (z.B. M5_R_H1) zu Maschinen-Key (z.B. M5-R-H1)
                        const machineKey = key.replace(/_/g, '-');
                        const sollSchnitte = maschinenMap.get(machineKey);
                        if (sollSchnitte === undefined || sollSchnitte === null) {
                            console.log(`Warnung: Keine Soll-Schnitte für Maschine ${machineKey} gefunden`);
                            return null; // Überspringe Maschinen ohne Soll-Werte
                        }
                        // Explizite Typzuweisung für TypeScript
                        const sollSchnitteWert = sollSchnitte;
                        const tagesSchnitte = value;
                        // Annahme: Ein Standard-Arbeitstag hat 21 Stunden.
                        const standardArbeitsstunden = 21;
                        // Berechne die Soll-Schnitte für einen Standard-Arbeitstag.
                        const sollSchnitteProTag = sollSchnitteWert * standardArbeitsstunden;
                        // Die Effizienz vergleicht die tatsächlichen Tagesschnitte mit den Soll-Tagesschnitten.
                        // Ein Wert > 100% bedeutet, dass mehr als das 21-Stunden-Soll produziert wurde,
                        // was z.B. durch längere Laufzeiten (Mehrschichtbetrieb) zustande kommen kann.
                        // Da wir oben bereits null-Werte ausfiltern, ist sollSchnitte hier immer definiert
                        const effizienzProzent = (tagesSchnitte / sollSchnitteProTag) * 100;
                        // Die "Ist-Schnitte pro Stunde" sind eine abgeleitete Metrik basierend auf der 21-Stunden-Annahme.
                        const istSchnitteProStunde = tagesSchnitte / standardArbeitsstunden;
                        return {
                            Datum: s.Datum,
                            Machine: machineKey, // Verwende den korrigierten Key
                            sollSchnitte: sollSchnitteWert,
                            tagesSchnitte,
                            istSchnitteProStunde,
                            effizienzProzent,
                        };
                    }
                    return null;
                })
                    .filter((item) => item !== null);
            });
            return efficiencyData;
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Maschinen-Effizienz-Daten:', error);
            throw error;
        }
    }
    async getCuttingChartData() {
        try {
            const result = await this.prisma.ablaengerei.findMany({
                select: {
                    Datum: true,
                    cutTT: true,
                    cutTR: true,
                    cutRR: true,
                    pickCut: true,
                },
                orderBy: {
                    Datum: 'asc',
                },
            });
            return result.map((item) => ({
                name: item.Datum,
                date: item.Datum,
                cutTT: item.cutTT || 0,
                cutTR: item.cutTR || 0,
                cutRR: item.cutRR || 0,
                pickCut: item.pickCut || 0,
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Cutting-Chart-Daten:', error);
            throw error;
        }
    }
    async getLagerCutsChartData() {
        try {
            const result = await this.prisma.ablaengerei.findMany({
                select: {
                    Datum: true,
                    lagerCut200: true,
                    lagerCut220: true,
                    lagerCut240: true,
                    cutLagerK200: true,
                    cutLagerK220: true,
                    cutLagerK240: true,
                    cutLagerR220: true,
                    cutLagerR240: true,
                },
                orderBy: {
                    Datum: 'asc',
                },
            });
            return result.map((item) => ({
                name: item.Datum,
                date: item.Datum,
                lagerSumme: (item.lagerCut200 || 0) + (item.lagerCut220 || 0) + (item.lagerCut240 || 0),
                cutLagerKSumme: (item.cutLagerK200 || 0) + (item.cutLagerK220 || 0) + (item.cutLagerK240 || 0),
                cutLagerRSumme: (item.cutLagerR220 || 0) + (item.cutLagerR240 || 0),
            }));
        }
        catch (error) {
            console.error('Fehler beim Abrufen der Lager-Cuts-Chart-Daten:', error);
            throw error;
        }
    }
    /**
     * System FTS Verfügbarkeitsdaten mit Cache-Support und Datums-Filterung
     */
    async getSystemFTSData(dateRange) {
        const cacheKey = cache_service_1.BackendCacheKeyGenerator.forService('DatabaseService', 'getSystemFTSData', {
            startDate: dateRange === null || dateRange === void 0 ? void 0 : dateRange.startDate,
            endDate: dateRange === null || dateRange === void 0 ? void 0 : dateRange.endDate
        });
        return await this.cache.cachedQuery(cacheKey, async () => {
            try {
                console.log('System FTS Daten aus Datenbank laden...');
                // Baue Prisma where-Kondition für Datums-Filterung (String-Format: YYYY-MM-DD)
                const whereCondition = {};
                if ((dateRange === null || dateRange === void 0 ? void 0 : dateRange.startDate) || (dateRange === null || dateRange === void 0 ? void 0 : dateRange.endDate)) {
                    whereCondition.Datum = {};
                    if (dateRange.startDate) {
                        whereCondition.Datum.gte = dateRange.startDate; // String-Vergleich für YYYY-MM-DD Format
                    }
                    if (dateRange.endDate) {
                        whereCondition.Datum.lte = dateRange.endDate; // String-Vergleich für YYYY-MM-DD Format
                    }
                }
                const result = await this.prisma.system.findMany({
                    select: {
                        id: true,
                        Datum: true,
                        verfuegbarkeitFTS: true,
                    },
                    where: whereCondition,
                    orderBy: {
                        Datum: 'asc',
                    },
                });
                // Transformiere Prisma-Daten zu SystemFTSDataPoint
                const transformedData = result
                    .filter((item) => item.verfuegbarkeitFTS !== null) // Nur Datensätze mit FTS-Werten
                    .map((item) => ({
                    id: item.id,
                    Datum: item.Datum || '', // Datum ist bereits String im YYYY-MM-DD Format
                    verfuegbarkeitFTS: item.verfuegbarkeitFTS || 0,
                }));
                console.log(`✅ ${transformedData.length} System FTS Datensätze aus Datenbank geladen`);
                return transformedData;
            }
            catch (error) {
                console.error('Fehler beim Abrufen der System FTS Daten:', error);
                throw error;
            }
        }, CACHE_TTL.SYSTEM_FTS_DATA);
    }
    /**
     * Materialdaten aus der Datenbank abrufen
     * Lädt alle verfügbaren Materialdaten mit MATNR, Materialkurztext und Kabeldurchmesser
     */
    async getMaterialdaten() {
        return this.cache.cachedQuery(cache_service_1.BackendCacheKeyGenerator.forQuery('materialdaten', 'getAll'), async () => {
            try {
                console.log('🔍 Lade Materialdaten aus der Datenbank...');
                // Verwende direkte SQL-Abfrage statt Prisma wegen Datentyp-Problemen
                const result = await this.prisma.$queryRaw `
            SELECT 
              matnr,
              materialkurztext,
              kabeldurchmesser,
              zuschlagKabeldurchmesser,
              biegefaktor,
              kleinsterErlauberFreiraum,
              bruttogewicht,
              created_at,
              updated_at
            FROM materialdaten
            ORDER BY matnr
          `;
                // Daten für Frontend-Kompatibilität transformieren
                const transformedData = result.map(item => ({
                    MATNR: item.matnr,
                    Materialkurztext: item.materialkurztext || '',
                    Kabeldurchmesser: item.kabeldurchmesser || 0,
                    ZuschlagKabeldurchmesser: item.zuschlagKabeldurchmesser || 0,
                    Biegefaktor: item.biegefaktor || 0,
                    KleinsterErlauberFreiraum: item.kleinsterErlauberFreiraum || 0,
                    Bruttogewicht: item.bruttogewicht || 0
                }));
                console.log(`✅ ${transformedData.length} Materialdaten aus Datenbank geladen`);
                return transformedData;
            }
            catch (error) {
                console.error('Fehler beim Abrufen der Materialdaten:', error);
                throw error;
            }
        }, CACHE_TTL.WAREHOUSE_DATA // 5 Minuten Cache für Stammdaten
        );
    }
    /**
     * Trommeldaten aus der Datenbank abrufen
     * Lädt alle verfügbaren Trommeldaten mit Trommelname und Außendurchmesser
     */
    async getTrommeldaten() {
        return this.cache.cachedQuery(cache_service_1.BackendCacheKeyGenerator.forQuery('trommeldaten', 'getAll'), async () => {
            try {
                console.log('🔍 Lade Trommeldaten aus der Datenbank...');
                // Verwende direkte SQL-Abfrage statt Prisma wegen Datentyp-Problemen
                const result = await this.prisma.$queryRaw `
            SELECT 
              trommeldaten,
              aussendurchmesser,
              kerndurchmesser,
              freiraum_mm,
              wickelbreite_mm,
              maxTragkraft_Kg,
              max_Laenge,
              max_Gewicht,
              created_at,
              updated_at
            FROM trommeldaten
            ORDER BY trommeldaten
          `;
                // Daten für Frontend-Kompatibilität transformieren
                const transformedData = result.map(item => ({
                    Trommelname: item.trommeldaten,
                    Außendurchmesser: item.aussendurchmesser || 0,
                    Kerndurchmesser: item.kerndurchmesser || 0,
                    Freiraum_mm: item.freiraum_mm || 0,
                    Wickelbreite_mm: item.wickelbreite_mm || 0,
                    MaxTragkraft_Kg: item.maxTragkraft_Kg || 0,
                    Max_Laenge: item.max_Laenge || 0,
                    Max_Gewicht: item.max_Gewicht || 0
                }));
                console.log(`✅ ${transformedData.length} Trommeldaten aus Datenbank geladen`);
                return transformedData;
            }
            catch (error) {
                console.error('Fehler beim Abrufen der Trommeldaten:', error);
                throw error;
            }
        }, CACHE_TTL.WAREHOUSE_DATA // 5 Minuten Cache für Stammdaten
        );
    }
}
exports.DatabaseService = DatabaseService;
