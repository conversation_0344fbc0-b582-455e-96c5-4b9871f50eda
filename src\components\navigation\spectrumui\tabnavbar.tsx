import * as React from "react";
import { cn } from "@/lib/utils";
import { useMatchRoute, Link } from "@tanstack/react-router";
import { ChevronRight } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { MenuItem } from "@/config/navigation/types";

interface TabnavbarProps {
  menu: MenuItem[];
}

export default function Tabnavbar({ menu }: TabnavbarProps) {
  return (
    <div className="py-2 relative">
      {/* WICHTIG: relative hier und viewport={false} unten, damit das Dropdown exakt am Trigger ausgerichtet wird */}
      <NavigationMenu viewport={false}>
        <NavigationMenuList>
          {menu.map((item, index) => (
            <NavigationMenuItem key={index}>
              {item.items ? (
                <DropdownMenuItem item={item} />
              ) : (
                <NavMenuItem item={item} />
              )}
            </NavigationMenuItem>
          ))}
        </NavigationMenuList>
      </NavigationMenu>
    </div>
  );
}

// Einfaches Navigationsmenü-Element
const NavMenuItem = ({ item }: { item: MenuItem }) => {
  const matchRoute = useMatchRoute();
  const isActive = matchRoute({ to: item.to });

  return (
    <NavigationMenuLink asChild>
      <Link
        to={item.to}
        className={cn(
          "group inline-flex h-9 w-max items-center justify-start rounded-md px-4 py-2 text-sm font-medium transition-colors",
          isActive
            ? 'bg-white text-foreground'
            : 'bg-transparent text-foreground hover:bg-foreground/10 hover:text-accent-foreground'
        )}
      >
        <div className="flex items-center gap-2">
          {item.icon && <span className="flex-shrink-0 w-5 h-5 flex items-center justify-center">{item.icon}</span>}
          <span className="whitespace-nowrap">{item.title}</span>
        </div>
      </Link>
    </NavigationMenuLink>
  );
};

// Dropdown-Menü-Element mit Untermenüs
const DropdownMenuItem = ({ item }: { item: MenuItem }) => {
  const matchRoute = useMatchRoute();
  const hasActiveSubItem = item.items?.some(subItem => matchRoute({ to: subItem.to }));

  return (
    <>
      <NavigationMenuTrigger
        className={cn(
          hasActiveSubItem
            ? 'bg-white text-foreground'
            : 'bg-transparent text-foreground hover:bg-foreground/10'
        )}
      >
        <div className="flex items-center gap-2">
          {item.icon && <span className="flex-shrink-0 w-5 h-5 flex items-center justify-center">{item.icon}</span>}
          <span className="whitespace-nowrap">{item.title}</span>
        </div>
      </NavigationMenuTrigger>
      {/* Positionierung direkt unter dem Trigger erzwingen */}
      <NavigationMenuContent className="absolute left-0 mt-2 bg-white/95 backdrop-blur-md border border-gray-200/50 shadow-2xl rounded-xl overflow-hidden">
        <motion.ul
          className="grid gap-1 p-2 w-[420px]"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
        >
          {item.items?.map((subItem, index) => (
            <motion.li
              key={subItem.title}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.15, delay: index * 0.05 }}
            >
              <ListItem
                to={subItem.to}
                title={subItem.title}
                icon={subItem.icon}
                index={index}
                totalItems={item.items?.length || 0}
              >
                {subItem.description}
              </ListItem>
            </motion.li>
          ))}
        </motion.ul>
      </NavigationMenuContent>
    </>
  );
};

const ListItem = React.forwardRef<
  React.ElementRef<typeof Link>,
  React.ComponentPropsWithoutRef<typeof Link> & {
    title: string;
    children: React.ReactNode;
    icon?: React.ReactNode;
    index?: number;
    totalItems?: number;
  }
>(({ className, title, children, icon, index = 0, totalItems = 0, ...props }, ref) => {
  const matchRoute = useMatchRoute();
  const isActive = matchRoute({ to: props.to });

  return (
    <NavigationMenuLink asChild>
      <Link
        ref={ref}
        className={cn(
          "group flex flex-row gap-4 rounded-lg p-4 leading-none no-underline transition-all duration-200 outline-none select-none relative overflow-hidden",
          isActive 
            ? 'bg-gradient-to-r from-[#ff7a05]/10 to-[#ff7a05]/5 text-[#ff7a05] border-l-4 border-[#ff7a05]' 
            : 'hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100/50 hover:shadow-md hover:scale-[1.02] transform',
          className,
        )}
        {...props}
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#f0f0f0_1px,transparent_1px),linear-gradient(to_bottom,#f0f0f0_1px,transparent_1px)] bg-[size:20px_20px] opacity-30 group-hover:opacity-50 transition-opacity duration-200"></div>
        
        {/* Icon Container */}
        {icon && (
          <motion.div 
            className={cn(
              "relative z-10 flex h-12 w-12 items-center justify-center rounded-lg shadow-sm transition-all duration-200 flex-shrink-0",
              isActive 
                ? 'bg-[#ff7a05]/10 text-[#ff7a05] shadow-[#ff7a05]/20' 
                : 'bg-gray-100 text-gray-600 group-hover:bg-white group-hover:shadow-md group-hover:text-[#ff7a05]'
            )}
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.15 }}
          >
            {icon}
          </motion.div>
        )}
        
        {/* Content */}
        <div className="relative z-10 flex-1">
          <div className={cn(
            "text-base font-semibold mb-1 transition-colors duration-200",
            isActive ? 'text-[#ff7a05]' : 'text-gray-900 group-hover:text-gray-900'
          )}>
            {title}
          </div>
          {children && (
            <p className="text-sm leading-relaxed text-gray-600 group-hover:text-gray-700 transition-colors duration-200">
              {children}
            </p>
          )}
        </div>
        
        {/* Arrow Icon */}
        <motion.div 
          className="relative z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          initial={{ x: -10 }}
          whileHover={{ x: 0 }}
        >
          <ChevronRight className="h-5 w-5 text-[#ff7a05]" />
        </motion.div>
        
        {/* Active Indicator */}
        {isActive && (
          <motion.div
            className="absolute right-0 top-0 bottom-0 w-1 bg-gradient-to-b from-[#ff7a05] to-[#ff7a05]/60"
            initial={{ scaleY: 0 }}
            animate={{ scaleY: 1 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          />
        )}
        
        {/* Separator */}
        {index < totalItems - 1 && (
          <motion.div 
            className="absolute bottom-0 left-4 right-4 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"
            initial={{ scaleX: 0 }}
            animate={{ scaleX: 1 }}
            transition={{ duration: 0.3, delay: index * 0.05 + 0.1 }}
          />
        )}
      </Link>
    </NavigationMenuLink>
  );
});
ListItem.displayName = "ListItem";
