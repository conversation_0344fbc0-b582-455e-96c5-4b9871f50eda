# Requirements Document

## Introduction

Diese Anforderungen definieren die Umstrukturierung der JOZI1 Lapp Dashboard Anwendung von einer monolithischen Frontend-Struktur zu einer modularen Architektur. Das Ziel ist es, die Codebasis in Vier klar getrennte Module zu organisieren: Leitstand, Störungen, Backend & Automatisierung und AI. Diese modulare Trennung ermöglicht eine bessere Wartbarkeit, Skalierbarkeit und rollenbasierte Zugriffskontrolle.

Die UserLandingPage dient als zentrale Modulauswahl nach der Benutzeranmeldung, wo Benutzer basierend auf ihren Rollen Zugriff auf die entsprechenden Module erhalten.

## Requirements

### Requirement 1: Modulare Verzeichnisstruktur erstellen

**User Story:** Als Entwickler möchte ich eine klare modulare Verzeichnisstruktur haben, damit die Codebasis besser organisiert und wartbar ist.

#### Acceptance Criteria

1. WHEN die Anwendung refactored wird THEN soll eine neue Verzeichnisstruktur `src/modules/leitstand`, `src/modules/stoerungen`, `src/modules/backend`, `src/modules/ai` erstellt werden
2. WHEN die Module erstellt werden THEN soll jedes Modul eine eigene `pages/` und `components/` Unterstruktur haben
3. WHEN die Struktur erstellt wird THEN sollen alle bestehenden Funktionalitäten weiterhin verfügbar sein

### Requirement 2: Leitstand-Modul organisieren

**User Story:** Als Entwickler möchte ich alle Leitstand-bezogenen Komponenten in einem eigenen Modul haben, damit die Leitstand-Funktionalität klar abgegrenzt ist.

#### Acceptance Criteria

1. WHEN das Leitstand-Modul erstellt wird THEN sollen alle Dashboard-Seiten (HomePage, DispatchPage, CuttingPage, IncomingGoodsPage, ArilPage, AtrlPage, MachinesPage) nach `src/modules/leitstand/pages/` verschoben werden
2. WHEN die Seiten verschoben werden THEN sollen alle zugehörigen Chart-Komponenten nach `src/modules/leitstand/components/` verschoben werden
3. WHEN das Modul organisiert wird THEN sollen alle statistischen Komponenten (`src/components/stats/`) dem Leitstand-Modul zugeordnet werden
4. WHEN die Umstrukturierung abgeschlossen ist THEN sollen alle Import-Pfade korrekt funktionieren

### Requirement 3: Störungen-Modul organisieren

**User Story:** Als Entwickler möchte ich alle Störungen-bezogenen Komponenten in einem eigenen Modul haben, damit die Störungsmanagement-Funktionalität isoliert ist.

#### Acceptance Criteria

1. WHEN das Störungen-Modul erstellt wird THEN soll die StoerungenPage nach `src/modules/stoerungen/pages/` verschoben werden
2. WHEN die Seite verschoben wird THEN sollen alle Störungen-Komponenten (`src/components/stoerungen/`) nach `src/modules/stoerungen/components/` verschoben werden
3. WHEN das Modul organisiert wird THEN sollen alle Monitoring-Komponenten (`src/components/monitoring/`) dem Störungen-Modul zugeordnet werden

### Requirement 4: Backend & Automatisierung-Modul organisieren

**User Story:** Als Entwickler möchte ich alle Backend- und Automatisierungs-bezogenen Komponenten in einem eigenen Modul haben, damit die Systemverwaltung klar strukturiert ist.

#### Acceptance Criteria

1. WHEN das Backend-Modul erstellt wird THEN sollen SystemPage und WorkflowPage nach `src/modules/backend/pages/` verschoben werden
2. WHEN die Seiten verschoben werden THEN sollen alle Workflow-Komponenten (`src/components/workflows/`) nach `src/modules/backend/components/` verschoben werden
3. WHEN das Modul organisiert wird THEN sollen Settings-bezogene Komponenten dem Backend-Modul zugeordnet werden

### Requirement 5: AI-Modul organisieren

**User Story:** Als Entwickler möchte ich alle KI-bezogenen Komponenten in einem eigenen Modul haben, damit die KI-Funktionalität klar strukturiert und erweiterbar ist.

#### Acceptance Criteria

1. WHEN das AI-Modul erstellt wird THEN sollen alle KI-Komponenten (`src/components/ai/`) nach `src/modules/ai/components/` verschoben werden
2. WHEN das Modul organisiert wird THEN sollen alle Chat-Komponenten (`src/components/chat/`) dem AI-Modul zugeordnet werden
3. WHEN zukünftige KI-Seiten erstellt werden THEN sollen diese in `src/modules/ai/pages/` platziert werden
4. WHEN das AI-Modul vollständig ist THEN soll es als eigenständiges Modul in der UserLandingPage verfügbar sein

### Requirement 6: UserLandingPage als Modulauswahl konfigurieren

**User Story:** Als Benutzer möchte ich nach dem Login eine Modulauswahl sehen, damit ich zu den für mich verfügbaren Modulen navigieren kann.

#### Acceptance Criteria

1. WHEN ein Benutzer sich anmeldet THEN soll die UserLandingPage als erste Seite nach dem Login angezeigt werden
2. WHEN die UserLandingPage angezeigt wird THEN sollen die Cards zu den entsprechenden Modulen verlinkt werden
3. WHEN ein Benutzer auf eine Modul-Card klickt THEN soll er zur Hauptseite des jeweiligen Moduls weitergeleitet werden
4. WHEN die Navigation implementiert wird THEN soll die rollenbasierte Zugriffskontrolle berücksichtigt werden

### Requirement 7: Import-Pfade und Navigation aktualisieren

**User Story:** Als Entwickler möchte ich, dass alle Import-Pfade nach der Umstrukturierung korrekt funktionieren, damit die Anwendung fehlerfrei läuft.

#### Acceptance Criteria

1. WHEN Dateien in Module verschoben werden THEN sollen alle Import-Pfade in der gesamten Codebasis entsprechend angepasst werden
2. WHEN die Pfade aktualisiert werden THEN soll das `@/` Alias-System weiterhin korrekt funktionieren
3. WHEN die Navigation umstrukturiert wird THEN sollen alle Routen zu den neuen Modulpfaden führen
4. WHEN die Umstrukturierung abgeschlossen ist THEN soll die TanStack Router Konfiguration die neuen Modulpfade reflektieren

### Requirement 8: Rollenbasierte Modulzugriffe implementieren

**User Story:** Als Benutzer möchte ich nur die Module sehen, für die ich berechtigt bin, damit die Benutzeroberfläche übersichtlich und sicher ist.

#### Acceptance Criteria

1. WHEN ein Benutzer mit der Rolle "Besucher" die UserLandingPage besucht THEN sollen die Module Leitstand und AI sichtbar und zugänglich sein
2. WHEN ein Benutzer mit der Rolle "Benutzer" die UserLandingPage besucht THEN sollen die Module Leitstand, Störungen und AI sichtbar und zugänglich sein
3. WHEN ein Benutzer mit der Rolle "Administrator" die UserLandingPage besucht THEN sollen alle Module (Leitstand, Störungen, Backend & Automatisierung, AI) sichtbar und zugänglich sein
4. WHEN ein Benutzer versucht, auf ein nicht autorisiertes Modul zuzugreifen THEN soll er zur UserLandingPage umgeleitet werden

### Requirement 9: Regressionstests und Funktionalitätsprüfung

**User Story:** Als Entwickler möchte ich sicherstellen, dass alle bestehenden Funktionalitäten nach der Umstrukturierung weiterhin funktionieren, damit keine Features verloren gehen.

#### Acceptance Criteria

1. WHEN die Umstrukturierung abgeschlossen ist THEN sollen alle bestehenden Unit-Tests weiterhin erfolgreich durchlaufen
2. WHEN die Module getestet werden THEN sollen alle E2E-Tests erfolgreich sein
3. WHEN die Anwendung manuell getestet wird THEN sollen alle Leitstand-Funktionen (Charts, KPIs, Navigation) wie zuvor funktionieren
4. WHEN die Störungen-, Backend- und AI-Module getestet werden THEN sollen alle spezifischen Funktionalitäten weiterhin verfügbar sein