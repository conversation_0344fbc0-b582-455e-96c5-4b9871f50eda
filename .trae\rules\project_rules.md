## Project Overview

This project is a desktop application named "Lapp Leitstand" designed for visualizing logistics and production data. It features a dashboard with various charts and statistics to provide insights into key performance indicators. The application is built with a modern technology stack, including:

*   **Core:** Electron, Vite, SWC
*   **UI:** React, Tailwind CSS, shadcn/ui, Recharts
*   **State Management:** React Query (TanStack)
*   **Routing:** TanStack Router
*   **Backend:** Node.js with Express.js
*   **Database:** Prisma with better-sqlite3
*   **Languages:** TypeScript
*   **Testing:** Vitest, Playwright

1. Projektstruktur  
   - root/  
     - src/app/ (Electron main & preload)  
     - src/ (React UI)  
     - packages/common/ (shared TS utils, Zustand-Stores, zod-Schemas)  
     - prisma/ (schema.prisma, migrations/)  
   - Pfade via tsconfig paths: @desktop, @renderer, @common, @db.

2. Build & Scripts  
   - Paket-Manager: npm (workspaces).  
   - electron-builder (targets: dmg, nsis, AppImage).  
   - Dev-Scripts:  
     - npm run dev --filter apps/desktop (Vite + Electron reload)  
     - npm run db:generate  
     - npm run db:migrate  
     - lint, test, typecheck müssen grün sein, bevor ein Commit akzeptiert wird.

3. TypeScript  
   - strict, noImplicitAny, exactOptionalPropertyTypes.  
   - Nur funktionale React-Komponenten + Hooks.

4. React-Konventionen  
   - Zustand für globale App-State.  
   - React Context ausschließlich für UI-Belange (Theme, i18n).  
   - Komponenten ≤ 200 LOC, ansonsten Presentational / Container-Split.

5. Styling (Tailwind v4)  
   - TailwindCSS v4 mit neuer content-Glob-Syntax und JIT standardmäßig aktiv.  
   - Plugins:  
     - @tailwindcss/typography  
     - @tailwindcss/forms@latest (v4-kompatible Version)  
   - shadcn/ui Komponenten werden per  
     npx shadcn-ui@latest add ...  
     importiert und in packages/ui gekapselt.  
   - Keine Inline-Styles außer dynamischen CSS-Variablen.  
   - printWidth in Prettier weiterhin 100.

6. Electron-Security  
   - contextIsolation: true, nodeIntegration: false, sandbox: true.  
   - IPC ausschließlich über preload-Bridge (exposedInMainWorld).  
   - Jede IPC-Payload wird mit zod Schema aus packages/common/schemas validiert.  
   - remote-module deaktiviert, Auto-Update nur signierte Artefakte.

7. Data Persistence & Backend (Prisma + SQLite3)  
   - ORM: Prisma v5 (generator client output in packages/db).  
   - Datenbank: SQLite3 Datei unter userData/db.sqlite (entwicklungs- & produktions­pfad unterschiedlich).  
   - Alle DB-Zugriffe erfolgen nur im main-Prozess über den Prisma Client; Renderer nutzt IPC.  
   - Migrations:  
     - Jede Schema-Änderung → pnpm db:migrate (creates timestamped migrations in prisma/migrations).  
     - Migrations niemals manuell editieren, bei Fehler revert & neu erzeugen.  
   - Transaktionen mit prisma.$transaction; Long-Running Queries verboten im Renderer.  
   - Seed-Daten via prisma/seed.ts, ausführbar mit pnpm db:seed.  
   - Versionierung: Änderungen an schema.prisma erfordern gleichnamigen ADR-Eintrag.

8. Testing  
   - Unit: vitest + happy-dom (React) + prisma-test-client (DB).  
   - Component: playwright-component.  
   - E2E (packaged): playwright-electron.  
   - Tests verwenden eine in-memory SQLite (mode=memory, cache=shared) und prisma migrate deploy.  
   - Coverage-Schwelle ≥ 85 %.

9. Git Branching  
   - main → release-branch via changesets, conventional commits.  
   - feature/<username>/<ticket-id-kurz>.  
   - Merge-PR braucht grüne CI inkl. db:migrate --preview.

10. CI/CD (GitHub Actions)  
    - Matrix: macos-latest, windows-2022, ubuntu-latest.  
    - Steps:  
      - setup-npm → npm install --frozen-lockfile  
      - npm db:generate  
      - npm db:migrate deploy (in-memory)  
      - lint, typecheck, test, build --publish=never  
    - Sign & notarize Builds nur auf release-Branches.

11. Logging & Telemetry  
    - main: electron-log (level info).  
    - renderer: console.info in dev, prod über IPC → main-Log.  
    - Opt-in Telemetry via posthog-electron; abschaltbar in Settings.

12. Error Handling  
    - process.on('uncaughtException') → Dialog + Log-Dump.  
    - react-error-boundary in UI.  
    - IPC-Fehler geben ErrorCode + Message zurück, keine Stacktrace an UI.

13. I18n  
    - react-i18next; default en-US, fallback de-DE.  
    - Übersetzungs-JSONs in packages/i18n. Keine Hard-Strings.

14. Accessibility  
    - shadcn/ui Defaults respektieren; interaktive Elemente mit aria-Labels.  
    - Dark/Light Theme erfüllt WCAG AA.

15. Versioning & Releases  
    - SemVer; prerelease Tags alpha, beta, rc.  
    - Auto-Changelog via changesets.  
    - CI bump & git tag nach grünem Release-Workflow.

16. ESLint & Prettier  
    - eslint-config-airbnb-typescript, eslint-plugin-react-hooks, eslint-plugin-tailwindcss v4.  
    - Prettier printWidth 100.  
    - npm lint --max-warnings 0 ist Gate.