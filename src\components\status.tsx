import React, { useState, useEffect } from 'react';
import { Status, StatusIndicator, StatusLabel } from '@/components/ui/kibo-ui/status';

interface SystemStatusIndicatorProps {
    className?: string;
}

export const SystemStatusIndicator: React.FC<SystemStatusIndicatorProps> = ({ className }) => {
    const [status, setStatus] = useState<'online' | 'offline' | 'maintenance' | 'degraded'>('online');

    useEffect(() => {
        // Simuliere Statuswechsel basierend auf Netzwerk oder Backend-Verbindung
        const checkStatus = async () => {
            try {
                // Prüfe Backend-Verbindung
                const response = await fetch('/api/health', {
                    method: 'GET',
                    timeout: 5000
                } as any);

                if (response.ok) {
                    setStatus('online');
                } else {
                    setStatus('degraded');
                }
            } catch (error) {
                setStatus('offline');
            }
        };

        // Initial check
        checkStatus();

        // Regelmäßige Überprüfung alle 30 Sekunden
        const interval = setInterval(checkStatus, 30000);

        return () => clearInterval(interval);
    }, []);

    return (
        <div className={className}>
            <Status status={status}>
                <StatusIndicator />
                <StatusLabel />
            </Status>
        </div>
    );
};

// Beispiel-Komponente für alle Status-Typen
const StatusExample = () => (
    <div className="flex gap-2">
        <Status status="online">
            <StatusIndicator />
            <StatusLabel />
        </Status>
        <Status status="offline">
            <StatusIndicator />
            <StatusLabel />
        </Status>
        <Status status="maintenance">
            <StatusIndicator />
            <StatusLabel />
        </Status>
        <Status status="degraded">
            <StatusIndicator />
            <StatusLabel />
        </Status>
    </div>
);

export default StatusExample;
