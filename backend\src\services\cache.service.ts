/**
 * Backend In-Memory Cache Service
 * 
 * Implementiert Server-seitiges Caching für Datenbankabfragen mit
 * intelligenter Invalidierung und Performance-Optimierung.
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  size: number; // Geschätzte Größe in Bytes
}

interface CacheConfig {
  defaultTTL: number; // Standard TTL in Millisekunden
  maxMemoryMB: number; // Maximaler Memory-Verbrauch in MB
  maxEntries: number; // Maximale Anzahl von Cache-Einträgen
  cleanupInterval: number; // Intervall für Cleanup in Millisekunden
  enableLogging: boolean; // Cache-Logging aktivieren
}

interface CacheStats {
  hits: number;
  misses: number;
  totalRequests: number;
  hitRate: number;
  memoryUsageMB: number;
  entryCount: number;
  evictions: number;
  totalQueries: number;
}

/**
 * Backend Cache Service für Datenbankabfragen
 */
export class BackendCacheService {
  private cache = new Map<string, CacheEntry<any>>();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    totalRequests: 0,
    hitRate: 0,
    memoryUsageMB: 0,
    entryCount: 0,
    evictions: 0,
    totalQueries: 0
  };
  
  private cleanupTimer: NodeJS.Timeout | null = null;
  private config: CacheConfig;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5 Minuten Standard
      maxMemoryMB: 50, // 50MB max Memory
      maxEntries: 1000,
      cleanupInterval: 60 * 1000, // 1 Minute
      enableLogging: process.env.NODE_ENV === 'development',
      ...config
    };

    this.startCleanupTimer();
    this.log('Backend Cache Service initialisiert', undefined, 
      `MaxMemory: ${this.config.maxMemoryMB}MB, MaxEntries: ${this.config.maxEntries}`);
  }

  /**
   * Cached Database Query Wrapper
   * @param key Cache-Schlüssel
   * @param queryFunction Datenbankabfrage-Funktion
   * @param ttl Cache TTL (optional)
   * @returns Cached oder frische Daten
   */
  async cachedQuery<T>(
    key: string,
    queryFunction: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    this.stats.totalRequests++;
    this.stats.totalQueries++;
    
    // Performance-Tracking starten
    const queryId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const queryType = this.extractQueryType(key);
    
    // Versuche Daten aus dem Cache zu laden
    const cachedData = this.get<T>(key);
    if (cachedData !== undefined) {
      // Performance Monitoring für Cache Hit
      this.trackPerformanceHit(queryId, queryType, this.estimateDataSize(cachedData));
      return cachedData;
    }

    // Cache Miss - führe Datenbankabfrage durch
    try {
      const startTime = Date.now();
      const data = await queryFunction();
      const queryTime = Date.now() - startTime;
      const dataSize = this.estimateDataSize(data);
      
      // Speichere nur gültige Daten im Cache
      if (data !== null && data !== undefined) {
        this.set(key, data, ttl);
      }
      
      // Performance Monitoring für Cache Miss
      this.trackPerformanceMiss(queryId, queryType, queryTime, dataSize);
      
      this.log('DB Query executed', key, `Time: ${queryTime}ms`);
      return data;
    } catch (error) {
      // Performance Monitoring für Fehler
      this.trackPerformanceError(queryId, queryType, error);
      
      console.error(`[BACKEND-CACHE] Query failed for key: ${key}`, error);
      throw error;
    }
  }

  /**
   * Daten aus dem Cache abrufen
   */
  private get<T>(key: string): T | undefined {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      return undefined;
    }

    // Prüfe TTL
    const now = Date.now();
    if (now > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      this.updateHitRate();
      this.log('Cache EXPIRED', key);
      return undefined;
    }

    // Update Access-Metadaten
    entry.accessCount++;
    entry.lastAccessed = now;
    
    this.stats.hits++;
    this.updateHitRate();
    this.log('Cache HIT', key);
    
    return entry.data;
  }

  /**
   * Daten in den Cache einfügen
   */
  private set<T>(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const actualTTL = ttl || this.config.defaultTTL;
    const dataSize = this.estimateDataSize(data);

    // Prüfe Memory-Limits vor dem Einfügen
    this.enforceMemoryLimits(dataSize);

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      ttl: actualTTL,
      accessCount: 1,
      lastAccessed: now,
      size: dataSize
    };

    this.cache.set(key, entry);
    this.updateStats();
    this.log('Cache SET', key, `TTL: ${actualTTL}ms, Size: ${Math.round(dataSize/1024)}KB`);
  }

  /**
   * Cache-Einträge für bestimmte Datentypen invalidieren
   * @param dataTypes Array von Datentypen zum Invalidieren
   */
  invalidateByDataTypes(dataTypes: string[]): number {
    let invalidatedCount = 0;
    
    for (const dataType of dataTypes) {
      invalidatedCount += this.deleteByPrefix(dataType);
    }
    
    if (invalidatedCount > 0) {
      this.log('Cache INVALIDATION', undefined, 
        `${invalidatedCount} entries invalidated for types: ${dataTypes.join(', ')}`);
    }
    
    return invalidatedCount;
  }

  /**
   * Cache-Einträge mit Prefix löschen
   */
  private deleteByPrefix(prefix: string): number {
    let deletedCount = 0;
    
    for (const key of Array.from(this.cache.keys())) {
      if (key.startsWith(prefix)) {
        this.cache.delete(key);
        deletedCount++;
      }
    }
    
    if (deletedCount > 0) {
      this.updateStats();
    }
    
    return deletedCount;
  }

  /**
   * Memory-Limits durchsetzen
   */
  private enforceMemoryLimits(newDataSize: number): void {
    const currentMemoryMB = this.stats.memoryUsageMB;
    const newDataSizeMB = newDataSize / (1024 * 1024);
    
    // Prüfe ob neuer Eintrag Memory-Limit überschreiten würde
    if (currentMemoryMB + newDataSizeMB > this.config.maxMemoryMB) {
      this.evictByMemoryPressure(newDataSizeMB);
    }
    
    // Prüfe Entry-Limit
    if (this.cache.size >= this.config.maxEntries) {
      this.evictLeastUsed();
    }
  }

  /**
   * Memory-basierte Eviction
   */
  private evictByMemoryPressure(requiredMB: number): void {
    const targetMemoryMB = this.config.maxMemoryMB * 0.8; // Evict bis 80% der Kapazität
    let evictedCount = 0;
    
    // Sortiere Einträge nach Access-Pattern (LRU + Größe)
    const entries = Array.from(this.cache.entries())
      .map(([key, entry]) => ({
        key,
        entry,
        score: entry.lastAccessed + (entry.size / 1024) // Größere Items haben höhere Wahrscheinlichkeit evicted zu werden
      }))
      .sort((a, b) => a.score - b.score);
    
    for (const { key } of entries) {
      if (this.stats.memoryUsageMB <= targetMemoryMB) break;
      
      this.cache.delete(key);
      evictedCount++;
      this.stats.evictions++;
      this.updateStats();
    }
    
    if (evictedCount > 0) {
      this.log('Memory EVICTION', undefined, 
        `${evictedCount} entries evicted, Memory: ${this.stats.memoryUsageMB.toFixed(1)}MB`);
    }
  }

  /**
   * LRU Eviction
   */
  private evictLeastUsed(): void {
    if (this.cache.size === 0) return;

    let oldestKey = '';
    let oldestAccess = Date.now();

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (entry.lastAccessed < oldestAccess) {
        oldestAccess = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.stats.evictions++;
      this.updateStats();
      this.log('LRU EVICTION', oldestKey);
    }
  }

  /**
   * Schätze Datengröße für Memory-Management
   */
  private estimateDataSize(data: any): number {
    try {
      // Bessere Größenschätzung als JSON.stringify
      if (Array.isArray(data)) {
        return data.length * 100; // Schätzung: 100 Bytes pro Array-Element
      } else if (typeof data === 'object' && data !== null) {
        return Object.keys(data).length * 50; // Schätzung: 50 Bytes pro Object-Property
      } else {
        return JSON.stringify(data).length * 2; // UTF-16
      }
    } catch {
      return 1024; // Fallback: 1KB
    }
  }

  /**
   * Cache-Aufräumung
   */
  cleanup(): number {
    const now = Date.now();
    let expiredCount = 0;

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      this.updateStats();
      this.log('Cache CLEANUP', undefined, `${expiredCount} expired entries removed`);
    }

    return expiredCount;
  }

  /**
   * Cache-Statistiken abrufen
   */
  getStats(): CacheStats {
    this.updateStats(); // Aktuelle Stats sicherstellen
    return { ...this.stats };
  }

  /**
   * Cache-Service beenden
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    
    this.cache.clear();
    this.log('Backend Cache Service destroyed');
  }

  /**
   * Private Hilfsmethoden
   */
  private updateHitRate(): void {
    this.stats.hitRate = this.stats.totalRequests > 0 
      ? (this.stats.hits / this.stats.totalRequests) * 100 
      : 0;
  }

  private updateStats(): void {
    this.stats.entryCount = this.cache.size;
    
    // Berechne Gesamt-Memory-Nutzung
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += entry.size;
    }
    this.stats.memoryUsageMB = totalSize / (1024 * 1024);
    
    this.updateHitRate();
  }

  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  private log(action: string, key?: string, details?: string): void {
    if (!this.config.enableLogging) return;
    
    const logMessage = [
      `[BACKEND-CACHE] ${action}`,
      key && `Key: ${key}`,
      details && `Details: ${details}`,
      `(${this.stats.entryCount} entries, ${this.stats.hitRate.toFixed(1)}% hit rate, ${this.stats.memoryUsageMB.toFixed(1)}MB)`
    ].filter(Boolean).join(' | ');
    
    console.log(logMessage);
  }

  /**
   * Performance Monitoring Helper Methods
   */
  private extractQueryType(key: string): string {
    // Extrahiere Query-Typ aus Cache-Key (z.B. "db:dispatch:getServiceLevelData" -> "dispatch.getServiceLevelData")
    const parts = key.split(':');
    if (parts.length >= 3) {
      return `${parts[1]}.${parts[2]}`;
    }
    return parts[1] || 'unknown';
  }

  private trackPerformanceHit(queryId: string, queryType: string, dataSize: number): void {
    // Lazy-load Performance Monitor um zirkuläre Abhängigkeiten zu vermeiden
    try {
      const { performanceMonitor } = require('./performance-monitor.service');
      const tracker = performanceMonitor.trackQuery(queryId, queryType);
      tracker.recordCacheHit(dataSize);
    } catch (error) {
      // Ignore errors from performance monitoring to not affect core functionality
      if (this.config.enableLogging) {
        console.debug('[BACKEND-CACHE] Performance monitoring fehler (Hit):', error);
      }
    }
  }

  private trackPerformanceMiss(queryId: string, queryType: string, queryTime: number, dataSize: number): void {
    try {
      const { performanceMonitor } = require('./performance-monitor.service');
      const tracker = performanceMonitor.trackQuery(queryId, queryType);
      tracker.recordCacheMiss();
      tracker.complete(dataSize);
    } catch (error) {
      if (this.config.enableLogging) {
        console.debug('[BACKEND-CACHE] Performance monitoring fehler (Miss):', error);
      }
    }
  }

  private trackPerformanceError(queryId: string, queryType: string, error: any): void {
    try {
      const { performanceMonitor } = require('./performance-monitor.service');
      const tracker = performanceMonitor.trackQuery(queryId, queryType);
      const errorType = error instanceof Error ? error.constructor.name : 'UnknownError';
      tracker.recordError(errorType);
    } catch (perfError) {
      if (this.config.enableLogging) {
        console.debug('[BACKEND-CACHE] Performance monitoring fehler (Error):', perfError);
      }
    }
  }
}

/**
 * Cache-Schlüssel-Generator für Backend
 */
export class BackendCacheKeyGenerator {
  /**
   * Generiert Cache-Schlüssel für Datenbankabfragen
   */
  static forQuery(table: string, method: string, params?: Record<string, any>): string {
    const baseKey = `db:${table}:${method}`;
    
    if (!params || Object.keys(params).length === 0) {
      return baseKey;
    }

    // Parameter sortieren für konsistente Schlüssel
    const sortedParams = Object.keys(params)
      .sort()
      .filter(key => params[key] !== undefined) // Ignoriere undefined Werte
      .map(key => `${key}=${String(params[key])}`)
      .join('&');

    return sortedParams ? `${baseKey}:${sortedParams}` : baseKey;
  }

  /**
   * Cache-Schlüssel für Service-Methoden
   */
  static forService(service: string, method: string, params?: Record<string, any>): string {
    return this.forQuery(service, method, params);
  }

  /**
   * Cache-Invalidierungskeys für Datentypen
   */
  static getInvalidationKeys(dataTypes: string[]): string[] {
    return dataTypes.map(type => `db:${type}`);
  }

  /**
   * Repository-spezifische Cache-Key-Generatoren
   */
  static warehouse = {
    lagerauslastung200: (dateRange?: any) => 
      this.forQuery('warehouse', 'lagerauslastung200', dateRange),
    lagerauslastung240: (dateRange?: any) => 
      this.forQuery('warehouse', 'lagerauslastung240', dateRange),
    aril: (dateRange?: any) => 
      this.forQuery('warehouse', 'aril', dateRange),
    atrl: (dateRange?: any) => 
      this.forQuery('warehouse', 'atrl', dateRange)
  };

  static cutting = {
    ablaengerei: (dateRange?: any) => 
      this.forQuery('cutting', 'ablaengerei', dateRange),
    weData: (dateRange?: any) => 
      this.forQuery('cutting', 'weData', dateRange)
  };

  static dispatch = {
    serviceLevel: (dateRange?: any) => 
      this.forQuery('dispatch', 'serviceLevel', dateRange),
    performance: (dateRange?: any) => 
      this.forQuery('dispatch', 'performance', dateRange),
    picking: (dateRange?: any) => 
      this.forQuery('dispatch', 'picking', dateRange)
  };
}

// Singleton Cache Instance für Backend
let backendCacheInstance: BackendCacheService | null = null;

/**
 * Globale Backend-Cache-Instanz abrufen
 */
export function getBackendCache(config?: Partial<CacheConfig>): BackendCacheService {
  if (!backendCacheInstance) {
    backendCacheInstance = new BackendCacheService(config);
  }
  return backendCacheInstance;
}

/**
 * Backend-Cache zerstören
 */
export function destroyBackendCache(): void {
  if (backendCacheInstance) {
    backendCacheInstance.destroy();
    backendCacheInstance = null;
  }
}