/**
 * Simple migration runner for adding metadata column
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

const dbPath = path.join(__dirname, 'rag_knowledge.db');
const migrationPath = path.join(__dirname, 'migrations', 'add-metadata-column.sql');

console.log('Running migration to add metadata column...');

try {
  const db = new Database(dbPath);
  
  // Check if metadata column already exists
  const tableInfo = db.prepare("PRAGMA table_info(documents)").all();
  const hasMetadata = tableInfo.some(col => col.name === 'metadata');
  
  if (hasMetadata) {
    console.log('Metadata column already exists, skipping migration.');
    db.close();
    process.exit(0);
  }
  
  // Read and execute migration
  const migration = fs.readFileSync(migrationPath, 'utf8');
  const statements = migration.split(';').filter(stmt => stmt.trim());
  
  db.transaction(() => {
    for (const statement of statements) {
      if (statement.trim()) {
        console.log('Executing:', statement.trim().substring(0, 50) + '...');
        db.exec(statement);
      }
    }
  })();
  
  console.log('Migration completed successfully!');
  db.close();
  
} catch (error) {
  console.error('Migration failed:', error);
  process.exit(1);
}