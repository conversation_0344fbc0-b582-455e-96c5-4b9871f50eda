import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { X, Save, BookOpen } from 'lucide-react';
import { Runbook, RunbookCreateData, RunbookUpdateData } from '@/types/runbooks.types';
import { runbookService } from '@/services/runbookService';
import { useToast } from "@/components/ui/use-toast";

interface RunbookEditorProps {
  runbook: Runbook | null;
  onClose: () => void;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: () => void;
}

export const RunbookEditor: React.FC<RunbookEditorProps> = ({ runbook, onClose, open, onOpenChange, onSubmit }) => {
  const [formData, setFormData] = useState<Partial<RunbookCreateData>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Toast für Erfolgsmeldungen
  const { showToast } = useToast();

  const handleClose = () => {
    onOpenChange(false);
  };

  useEffect(() => {
    if (runbook) {
      setFormData(runbook);
    } else {
      setFormData({
        title: '',
        content: '',
        affected_systems: [],
        tags: [],
      });
    }
  }, [runbook]);

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    if (!formData.title?.trim()) {
      setError('Titel ist ein Pflichtfeld.');
      setLoading(false);
      return;
    }

    try {
      if (runbook) {
        await runbookService.updateRunbook(runbook.id, formData as RunbookUpdateData);
        showToast(
          'Runbook aktualisiert',
          'Das Runbook wurde erfolgreich aktualisiert.',
          { type: 'success', duration: 4000 }
        );
      } else {
        await runbookService.createRunbook(formData as RunbookCreateData);
        showToast(
          'Runbook erstellt',
          'Das neue Runbook wurde erfolgreich erstellt.',
          { type: 'success', duration: 4000 }
        );
      }
      
      // Modal schließen und Callback ausführen
      setTimeout(() => {
        onOpenChange(false);
        onSubmit();
      }, 1000);
    } catch (error) {
      console.error('Fehler beim Speichern des Runbooks:', error);
      setError('Fehler beim Speichern des Runbooks.');
      showToast(
        'Fehler beim Speichern',
        'Das Runbook konnte nicht gespeichert werden. Bitte versuchen Sie es erneut.',
        { type: 'error', duration: 5000 }
      );
    } finally {
      setLoading(false);
    }
  };

  const handleStringToArray = (field: 'tags' | 'affected_systems', value: string) => {
    setFormData({...formData, [field]: value.split(',').map(s => s.trim()).filter(Boolean)})
  }

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50"
        onClick={handleClose}
      />
      
      {/* Dialog Content */}
      <div className="relative bg-white rounded-lg shadow-lg max-w-2xl w-full mx-4 overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between py-2 px-6 border-b bg-gray-100">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-blue-600" />
            {runbook ? 'Eintrag bearbeiten' : 'Neuen Eintrag erstellen'}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-6 overflow-y-auto">
          <form onSubmit={handleSave} className="space-y-4">
            {/* Error Message */}
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            )}

            {/* Titel */}
            <div className="space-y-2">
              <Label htmlFor="title">Titel *</Label>
              <Input
                id="title"
                value={formData.title || ''}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                placeholder="Titel des Runbooks"
                required
              />
            </div>

            {/* Inhalt */}
            <div className="space-y-2">
              <Label htmlFor="content">Inhalt (Markdown)</Label>
              <Textarea
                id="content"
                value={formData.content || ''}
                onChange={(e) => setFormData({...formData, content: e.target.value})}
                placeholder="Inhalt des Runbooks in Markdown-Format"
                rows={8}
              />
            </div>

            {/* Tags und Systeme */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  value={formData.tags?.join(', ') || ''}
                  onChange={(e) => handleStringToArray('tags', e.target.value)}
                  placeholder="komma-getrennt"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="affected_systems">Betroffene Systeme</Label>
                <Input
                  id="affected_systems"
                  value={formData.affected_systems?.join(', ') || ''}
                  onChange={(e) => handleStringToArray('affected_systems', e.target.value)}
                  placeholder="komma-getrennt"
                />
              </div>
            </div>

            {/* Buttons */}
            <div className="flex gap-2 pt-4">
              <Button type="submit" variant="accept" disabled={loading} className="">
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Speichere...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Speichern
                  </>
                )}
              </Button>
              
              <Button type="button" variant="ghost" onClick={handleClose}>
                Abbrechen
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};