// src/routes/router.tsx
import { createRouter, Navigate, createHashHistory } from '@tanstack/react-router'
import { RootRoute } from './__root'
import { allModuleRoutes } from './modules'

// Importiere die Basis-Seiten
import { createRoute } from '@tanstack/react-router'
import { lazy } from 'react'

// Lazy-Load für Basis-Seiten
const LoginPage = lazy(() => import('@/pages/LoginPage'))
const RegistrationPage = lazy(() => import('@/pages/RegistrationPage'))
const UserLandingPage = lazy(() => import('@/pages/UserLandingPage'))

// Login-Route
const loginRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: 'login',
  component: () => <LoginPage />
})

// Registrierungs-Route
const registrationRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: 'register',
  component: () => <RegistrationPage />
})

// User-Landing-Route
const userLandingRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/',
  component: () => <UserLandingPage />
})

// User-Settings-Route
const UserSettingsPage = lazy(() => import('@/pages/UserSettingsPage'))
const userSettingsRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/user-settings',
  component: () => <UserSettingsPage />
})

// Settings-Route (Haupteinstellungen)
const SettingsPage = lazy(() => import('@/pages/SettingsPage'))
const settingsRoute = createRoute({
  getParentRoute: () => RootRoute,
  path: '/settings',
  component: () => <SettingsPage />
})

// Erstelle den Router mit allen Routen
export const router = createRouter({
  // Registriere die Root-Route
  routeTree: RootRoute.addChildren([
    // Basis-Routen
    loginRoute,
    registrationRoute,
    userLandingRoute,
    userSettingsRoute,
    settingsRoute,
    // Modul-Routen aus den einzelnen Modulen
    ...allModuleRoutes
  ]),
  // Verwende Hash-History für Electron (file://), damit Pfade wie #/login funktionieren
  history: createHashHistory(),
  // Saubere Weiterleitung bei 404 auf Startseite (ohne Hash-Hack)
  defaultNotFoundComponent: () => <Navigate to="/" />
})

// Typen registrieren
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}