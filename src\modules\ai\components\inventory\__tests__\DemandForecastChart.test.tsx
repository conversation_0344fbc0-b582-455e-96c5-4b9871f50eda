import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { DemandForecastChart } from '../DemandForecastChart';
import { DemandForecast, SeasonalityPattern } from '../../../services/inventory/types';

// Mock recharts components
vi.mock('recharts', () => ({
  LineChart: ({ children, data }: any) => (
    <div data-testid="line-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Line: ({ dataKey, stroke }: any) => (
    <div data-testid="line" data-key={dataKey} data-stroke={stroke} />
  ),
  Area: ({ dataKey, fill }: any) => (
    <div data-testid="area" data-key={dataKey} data-fill={fill} />
  ),
  AreaChart: ({ children, data }: any) => (
    <div data-testid="area-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  XAxis: ({ dataKey }: any) => <div data-testid="x-axis" data-key={dataKey} />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  ReferenceLine: ({ x, label }: any) => (
    <div data-testid="reference-line" data-x={x} data-label={JSON.stringify(label)} />
  ),
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="responsive-container">{children}</div>
  )
}));

// Mock chart components
vi.mock('@/components/ui/chart', () => ({
  ChartContainer: ({ children }: any) => (
    <div data-testid="chart-container">{children}</div>
  ),
  ChartTooltip: ({ children }: any) => (
    <div data-testid="chart-tooltip">{children}</div>
  ),
  ChartTooltipContent: ({ children }: any) => (
    <div data-testid="chart-tooltip-content">{children}</div>
  )
}));

describe('DemandForecastChart', () => {
  const mockForecast: DemandForecast = {
    itemId: 'test-item',
    predictions: [
      {
        date: new Date('2024-01-01'),
        predictedDemand: 50,
        confidenceInterval: { lower: 40, upper: 60 },
        seasonalAdjustment: 5
      },
      {
        date: new Date('2024-01-02'),
        predictedDemand: 55,
        confidenceInterval: { lower: 45, upper: 65 },
        seasonalAdjustment: -2
      },
      {
        date: new Date('2024-01-03'),
        predictedDemand: 48,
        confidenceInterval: { lower: 38, upper: 58 },
        seasonalAdjustment: 0
      }
    ],
    confidence: 0.85,
    seasonalFactors: [
      { period: 'weekly', factor: 1.2, confidence: 0.8 },
      { period: 'monthly', factor: 0.9, confidence: 0.7 }
    ],
    trendDirection: 'increasing',
    forecastMethod: 'exponential_smoothing',
    accuracy: 0.92,
    generatedAt: new Date('2024-01-01')
  };

  const mockSeasonality: SeasonalityPattern = {
    itemId: 'test-item',
    hasSeasonality: true,
    patterns: [
      {
        type: 'weekly',
        cycle: 7,
        amplitude: 0.3,
        phase: 2
      },
      {
        type: 'monthly',
        cycle: 30,
        amplitude: 0.2,
        phase: 15
      }
    ],
    strength: 0.6,
    detectedAt: new Date('2024-01-01')
  };

  const mockHistoricalData = [
    { date: new Date('2023-12-28'), actualDemand: 45 },
    { date: new Date('2023-12-29'), actualDemand: 52 },
    { date: new Date('2023-12-30'), actualDemand: 48 }
  ];

  const mockOnDateSelect = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders demand forecast chart with forecast data', () => {
    render(
      <DemandForecastChart
        forecast={mockForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    // Check if summary cards are rendered
    expect(screen.getByText('Ø Tagesbedarf')).toBeInTheDocument();
    expect(screen.getByText('51')).toBeInTheDocument(); // Average daily demand
    expect(screen.getByText('Trend')).toBeInTheDocument();
    expect(screen.getByText('Steigend')).toBeInTheDocument();
  });

  it('displays forecast accuracy and confidence metrics', () => {
    render(
      <DemandForecastChart
        forecast={mockForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    // Check accuracy and confidence
    expect(screen.getByText('92%')).toBeInTheDocument(); // Accuracy
    expect(screen.getByText('85%')).toBeInTheDocument(); // Confidence
    expect(screen.getByText('exponential_smoothing')).toBeInTheDocument(); // Method
  });

  it('shows seasonal patterns when seasonality is detected', () => {
    render(
      <DemandForecastChart
        forecast={mockForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    // Check seasonal patterns section
    expect(screen.getByText('Saisonale Muster')).toBeInTheDocument();
    expect(screen.getByText('Erkannte saisonale Schwankungen (Stärke: 60%)')).toBeInTheDocument();
    expect(screen.getByText('Wöchentlich')).toBeInTheDocument();
    expect(screen.getByText('7 Tage Zyklus')).toBeInTheDocument();
  });

  it('displays detailed forecast table with predictions', () => {
    render(
      <DemandForecastChart
        forecast={mockForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    // Check detailed forecast table
    expect(screen.getByText('Detaillierte Prognose')).toBeInTheDocument();
    expect(screen.getByText('Tägliche Bedarfsprognose mit Konfidenzintervallen')).toBeInTheDocument();
    
    // Check table headers
    expect(screen.getByText('Datum')).toBeInTheDocument();
    expect(screen.getByText('Prognose')).toBeInTheDocument();
    expect(screen.getByText('Untergrenze')).toBeInTheDocument();
    expect(screen.getByText('Obergrenze')).toBeInTheDocument();
  });

  it('handles different trend directions correctly', () => {
    const decreasingForecast = {
      ...mockForecast,
      trendDirection: 'decreasing' as const
    };

    render(
      <DemandForecastChart
        forecast={decreasingForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    expect(screen.getByText('Fallend')).toBeInTheDocument();
  });

  it('handles stable trend direction', () => {
    const stableForecast = {
      ...mockForecast,
      trendDirection: 'stable' as const
    };

    render(
      <DemandForecastChart
        forecast={stableForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    expect(screen.getByText('Stabil')).toBeInTheDocument();
  });

  it('calls onDateSelect when date is clicked in table', async () => {
    render(
      <DemandForecastChart
        forecast={mockForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    // Find and click on a date in the table
    const dateElement = screen.getByText('1.1.2024');
    fireEvent.click(dateElement);

    await waitFor(() => {
      expect(mockOnDateSelect).toHaveBeenCalledWith(new Date('2024-01-01'));
    });
  });

  it('displays combined historical and forecast data in chart', () => {
    render(
      <DemandForecastChart
        forecast={mockForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    // Check if line chart is rendered
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    
    // Check if chart data includes both historical and forecast data
    const lineChart = screen.getByTestId('line-chart');
    const chartData = JSON.parse(lineChart.getAttribute('data-chart-data') || '[]');
    expect(chartData.length).toBeGreaterThan(0);
    
    // Should have both historical and forecast data points
    const historicalPoints = chartData.filter((d: any) => d.type === 'historical');
    const forecastPoints = chartData.filter((d: any) => d.type === 'forecast');
    expect(historicalPoints.length).toBe(3);
    expect(forecastPoints.length).toBe(3);
  });

  it('shows forecast metrics correctly', () => {
    render(
      <DemandForecastChart
        forecast={mockForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    // Check forecast metrics
    expect(screen.getByText('Prognose-Metriken')).toBeInTheDocument();
    expect(screen.getByText('Detaillierte Genauigkeitsanalyse')).toBeInTheDocument();
    expect(screen.getByText('Gesamtgenauigkeit')).toBeInTheDocument();
    expect(screen.getByText('Konfidenz')).toBeInTheDocument();
  });

  it('handles missing seasonality gracefully', () => {
    render(
      <DemandForecastChart
        forecast={mockForecast}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    // Should still render without seasonality section
    expect(screen.getByText('Bedarfsprognose')).toBeInTheDocument();
    // Seasonality section should not be present
    expect(screen.queryByText('Saisonale Muster')).not.toBeInTheDocument();
  });

  it('handles empty historical data', () => {
    render(
      <DemandForecastChart
        forecast={mockForecast}
        seasonality={mockSeasonality}
        historicalData={[]}
        onDateSelect={mockOnDateSelect}
      />
    );

    // Should still render forecast data
    expect(screen.getByText('Bedarfsprognose')).toBeInTheDocument();
    expect(screen.getByText('51')).toBeInTheDocument(); // Average demand
  });

  it('displays seasonal adjustment values in forecast table', () => {
    render(
      <DemandForecastChart
        forecast={mockForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    // Check seasonal adjustment column
    expect(screen.getByText('Saisonal')).toBeInTheDocument();
    expect(screen.getByText('+5')).toBeInTheDocument(); // Positive adjustment
    expect(screen.getByText('-2')).toBeInTheDocument(); // Negative adjustment
  });

  it('truncates long forecast tables correctly', () => {
    // Create forecast with many predictions
    const longForecast = {
      ...mockForecast,
      predictions: Array.from({ length: 50 }, (_, i) => ({
        date: new Date(`2024-01-${i + 1}`),
        predictedDemand: 50 + i,
        confidenceInterval: { lower: 40 + i, upper: 60 + i },
        seasonalAdjustment: i % 2 === 0 ? 5 : -2
      }))
    };

    render(
      <DemandForecastChart
        forecast={longForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
        onDateSelect={mockOnDateSelect}
      />
    );

    // Should show truncation message
    expect(screen.getByText('... und 20 weitere Tage')).toBeInTheDocument();
  });

  it('handles missing onDateSelect prop gracefully', () => {
    render(
      <DemandForecastChart
        forecast={mockForecast}
        seasonality={mockSeasonality}
        historicalData={mockHistoricalData}
      />
    );

    // Should render without errors even without onDateSelect
    expect(screen.getByText('Bedarfsprognose')).toBeInTheDocument();
  });
});