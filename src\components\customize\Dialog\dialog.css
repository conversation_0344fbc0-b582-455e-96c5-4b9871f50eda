/* Dialog/Modal Komponenten CSS */

.dialog-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-content {
  position: relative;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 56rem; /* max-w-4xl */
  width: 100%;
  margin: 0 1rem;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-content.dark {
  background-color: #111827; /* gray-900 */
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb; /* gray-100 */
  flex-shrink: 0;
}

.dialog-header.dark {
  border-bottom-color: #374151; /* gray-700 */
  background-color: #1f2937; /* gray-800 */
}

.dialog-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.dialog-title.dark {
  color: #f9fafb;
}

.dialog-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1 1 0%;
  min-height: 400px; /* Explizite Mindesthöhe */
}

.dialog-close-button {
  height: 2rem;
  width: 2rem;
  padding: 0;
  border: none;
  background: transparent;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.dialog-close-button:hover {
  background-color: #f3f4f6; /* gray-100 */
}

.dialog-close-button.dark:hover {
  background-color: #374151; /* gray-700 */
}

.dialog-close-icon {
  height: 1rem;
  width: 1rem;
  color: #6b7280; /* gray-500 */
}

.dialog-close-icon.dark {
  color: #9ca3af; /* gray-400 */
}