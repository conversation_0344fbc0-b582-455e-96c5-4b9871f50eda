/**
 * Data Enrichment Service Types
 * 
 * Type definitions for AI chatbot database integration
 */

export interface QueryIntent {
  type: 'stoerungen' | 'dispatch' | 'cutting' | 'general';
  keywords: string[];
  timeRange?: DateRange;
  specificMetrics?: string[];
  confidence: number; // 0-1 confidence score
}

export interface DateRange {
  startDate?: string;
  endDate?: string;
}

export interface EnrichedContext {
  originalMessage: string;
  detectedIntents: QueryIntent[];
  databaseContext: string;
  hasData: boolean;
  dataTypes: string[];
  timestamp: Date;
  requestId?: string;
  processingTime?: number;
  fallbackReason?: string;
  partialFailure?: boolean;
}

export interface QueryResult {
  dataType: string;
  data: any;
  summary: string;
  timestamp: Date;
  success: boolean;
  error?: string;
  queryTime?: number;
  intent?: string;
  fallback?: boolean;
}

export interface IntentPatterns {
  stoerungen: string[];
  dispatch: string[];
  cutting: string[];
  timeRange: string[];
  metrics: string[];
}

export interface DataFormatterOptions {
  maxItems?: number;
  includeTimestamp?: boolean;
  format?: 'summary' | 'detailed' | 'minimal';
}

export interface DataEnrichmentConfig {
  intentThreshold: number; // Minimum confidence for intent detection
  maxQueryTime: number; // Maximum time for database queries in ms
  cacheEnabled: boolean;
  cacheTTL: number; // Cache time-to-live in ms
}