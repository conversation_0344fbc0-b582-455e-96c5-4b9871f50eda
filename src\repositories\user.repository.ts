import { eq, or } from 'drizzle-orm';
import { db } from '../db';
import { user, role, roleToUser } from '../db/schema';

export class UserRepository {
  private static instance: UserRepository;

  constructor() {}

  static getInstance(): UserRepository {
    if (!UserRepository.instance) {
      UserRepository.instance = new UserRepository();
    }
    return UserRepository.instance;
  }

  async findByEmailOrUsername(email: string, username: string) {
    const [foundUser] = await db
      .select()
      .from(user)
      .where(or(
        eq(user.email, email),
        eq(user.username, username)
      ))
      .limit(1);

    return foundUser || null;
  }

  async findByEmail(email: string) {
    const [foundUser] = await db
      .select()
      .from(user)
      .where(eq(user.email, email))
      .limit(1);

    return foundUser || null;
  }

  async findByUsername(username: string) {
    // Find user with roles using join
    const userWithRoles = await db
      .select({
        id: user.id,
        email: user.email,
        username: user.username,
        name: user.name,
        passwordHash: user.passwordHash,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        roleId: role.id,
        roleName: role.name,
      })
      .from(user)
      .leftJoin(roleToUser, eq(roleToUser.B, user.id))
      .leftJoin(role, eq(role.id, roleToUser.A))
      .where(eq(user.username, username));

    if (userWithRoles.length === 0) return null;

    // Transform to match expected structure
    const userData = userWithRoles[0];
    const roles = userWithRoles
      .filter(row => row.roleId !== null)
      .map(row => ({
        id: row.roleId,
        name: row.roleName,
      }));

    return {
      id: userData.id,
      email: userData.email,
      username: userData.username,
      name: userData.name,
      passwordHash: userData.passwordHash,
      createdAt: userData.createdAt,
      updatedAt: userData.updatedAt,
      roles,
    };
  }

  async createUser(userData: {
    email: string;
    username: string;
    name?: string;
    passwordHash: string;
  }) {
    const [createdUser] = await db
      .insert(user)
      .values({
        ...userData,
        createdAt: Math.floor(Date.now() / 1000),
        updatedAt: Math.floor(Date.now() / 1000),
      })
      .returning();

    return createdUser;
  }

  async findById(id: number) {
    // Find user with roles using join
    const userWithRoles = await db
      .select({
        id: user.id,
        email: user.email,
        username: user.username,
        name: user.name,
        passwordHash: user.passwordHash,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        roleId: role.id,
        roleName: role.name,
      })
      .from(user)
      .leftJoin(roleToUser, eq(roleToUser.B, user.id))
      .leftJoin(role, eq(role.id, roleToUser.A))
      .where(eq(user.id, id));

    if (userWithRoles.length === 0) return null;

    // Transform to match expected structure
    const userData = userWithRoles[0];
    const roles = userWithRoles
      .filter(row => row.roleId !== null)
      .map(row => ({
        id: row.roleId,
        name: row.roleName,
      }));

    return {
      id: userData.id,
      email: userData.email,
      username: userData.username,
      name: userData.name,
      passwordHash: userData.passwordHash,
      createdAt: userData.createdAt,
      updatedAt: userData.updatedAt,
      roles,
    };
  }
}

// Export singleton instance
export const userRepository = UserRepository.getInstance();
