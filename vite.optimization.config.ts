/**
 * Vite Bundle Optimization Configuration
 * 
 * This configuration optimizes the build output for better performance
 * and smaller bundle sizes through code splitting and tree shaking.
 */

import { defineConfig } from 'vite';

export const optimizationConfig = defineConfig({
  build: {
    // Enable code splitting
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunks
          'vendor-react': ['react', 'react-dom'],
          'vendor-router': ['@tanstack/react-router'],
          'vendor-query': ['@tanstack/react-query'],
          'vendor-ui': ['lucide-react', 'framer-motion'],
          'vendor-charts': ['recharts'],
          'vendor-forms': ['react-hook-form'],
          
          // Module-specific chunks
          'module-leitstand': [
            './src/modules/leitstand/pages/DashboardPage',
            './src/modules/leitstand/pages/DispatchPage',
            './src/modules/leitstand/pages/CuttingPage',
            './src/modules/leitstand/pages/IncomingGoodsPage',
            './src/modules/leitstand/pages/CSR',
            './src/modules/leitstand/pages/ArilPage',
            './src/modules/leitstand/pages/AtrlPage',
            './src/modules/leitstand/pages/MachinesPage'
          ],
          'module-stoerungen': [
            './src/modules/stoerungen/pages/StoerungenPage'
          ],
          'module-backend': [
            './src/modules/backend/pages/SystemPage',
            './src/modules/backend/pages/WorkflowPage'
          ],
          'module-ai': [
            './src/modules/ai/components'
          ],
          
          // Shared utilities
          'shared-services': [
            './src/services/api.service',
            './src/services/auth.service',
            './src/services/cache.service'
          ],
          'shared-components': [
            './src/components/ui',
            './src/components/auth'
          ]
        },
        
        // Optimize chunk file names
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? 
            chunkInfo.facadeModuleId.split('/').pop()?.replace('.tsx', '').replace('.ts', '') : 
            'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        },
        
        // Optimize asset file names
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const ext = info[info.length - 1];
          
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext || '')) {
            return `images/[name]-[hash][extname]`;
          }
          if (/css/i.test(ext || '')) {
            return `css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    
    // Optimize build settings
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug']
      },
      mangle: {
        safari10: true
      }
    },
    
    // Source maps for debugging (disable in production)
    sourcemap: process.env.NODE_ENV !== 'production',
    
    // Chunk size warnings
    chunkSizeWarningLimit: 1000,
    
    // Asset inlining threshold
    assetsInlineLimit: 4096
  },
  
  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@tanstack/react-router',
      '@tanstack/react-query',
      'lucide-react',
      'recharts'
    ],
    exclude: [
      // Exclude large dependencies that should be loaded on demand
      'electron'
    ]
  },
  
  // Enable tree shaking
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  }
});

/**
 * Performance Budget Configuration
 * 
 * Define performance budgets for different bundle types
 */
export const performanceBudget = {
  // Maximum sizes in KB
  maxBundleSize: 500, // Main bundle
  maxChunkSize: 200,  // Individual chunks
  maxAssetSize: 100,  // Individual assets
  
  // Performance thresholds
  thresholds: {
    fcp: 1500,  // First Contentful Paint (ms)
    lcp: 2500,  // Largest Contentful Paint (ms)
    fid: 100,   // First Input Delay (ms)
    cls: 0.1    // Cumulative Layout Shift
  }
};

/**
 * Bundle Analysis Configuration
 */
export const bundleAnalysisConfig = {
  // Enable bundle analyzer in development
  analyzer: process.env.ANALYZE === 'true',
  
  // Bundle size tracking
  sizeTracking: {
    enabled: true,
    threshold: 0.1, // Alert if bundle size increases by more than 10%
    baseline: './bundle-size-baseline.json'
  }
};

export default optimizationConfig;