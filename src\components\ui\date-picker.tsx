import React from "react"
import ElegantDatePicker from "@/components/customize/DatePicker/ElegantDatePicker"
import { cn } from "@/lib/utils"

interface DatePickerProps {
  value?: Date
  onChange?: (date: Date | undefined) => void
  placeholder?: string
  className?: string
  label?: string
  description?: string
  title?: string
  headerText?: string
  headerDescription?: string
}

export function DatePicker({ 
  value, 
  onChange, 
  placeholder = "Datum auswählen",
  className,
  label,
  description,
  title,
  headerText,
  headerDescription
}: DatePickerProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <ElegantDatePicker
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        label={label || "Datum auswählen"}
        description={description}
        title={title}
        headerText={headerText}
        headerDescription={headerDescription}
        showHeader={false}
        className={className}
      />
    </div>
  )
} 