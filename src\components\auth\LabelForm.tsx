import React, { forwardRef, useCallback, useEffect, useRef } from 'react';
import { useMotionPreference } from '@/hooks/useMotionPreference';
import { useAccessibility } from '@/hooks/useAccessibility';
import '@/styles/label-auth.css';

/**
 * Label form configuration interface
 */
export interface LabelStyleConfig {
  fontFamily: 'monospace' | 'typewriter';
  backgroundColor: 'transparent' | 'semi-transparent';
  borderStyle: 'none' | 'dotted' | 'dashed';
  textColor: 'dark' | 'high-contrast';
}

/**
 * LabelForm component props interface
 */
export interface LabelFormProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  className?: string;
  /**
   * Optional Font-Size overrides (e.g. '2rem', '24px').
   * Hintergrund: Wir nutzen Inline-Styles für den Header.
   * Diese Props erlauben eine einfache, gezielte Anpassung ohne globale CSS-Änderungen.
   */
  titleSize?: string;
  descriptionSize?: string;
  /**
   * Optional margin overrides for header spacing. Diese drei Props erlauben es,
   * den Abstand zwischen Titel (h1) und Beschreibung (p) pro Instanz fein zu steuern,
   * ohne globale CSS-Regeln anpassen zu müssen.
   */
  titleMarginBottom?: string; // default: '0.25rem'
  descriptionMarginBottom?: string; // default: '1.5rem'
  descriptionMarginTop?: string; // default: '0'
  onSubmit?: (e: React.FormEvent) => void;
  isLoading?: boolean;
  error?: string | null;
  success?: string | null;
  styleConfig?: Partial<LabelStyleConfig>;
  'aria-label'?: string;
  'aria-describedby'?: string;
  role?: string;
}

/**
 * Default style configuration for label forms
 */
const DEFAULT_STYLE_CONFIG: LabelStyleConfig = {
  fontFamily: 'monospace',
  backgroundColor: 'semi-transparent',
  borderStyle: 'none',
  textColor: 'high-contrast',
};

/**
 * LabelForm base component for label-styled authentication forms
 * Implements requirements 2.1, 2.2, 2.3, 2.4, 4.3, 6.4
 */
export const LabelForm = forwardRef<HTMLFormElement, LabelFormProps>(
  (
    {
      children,
      title,
      description,
      titleSize,
      descriptionSize,
      titleMarginBottom,
      descriptionMarginBottom,
      descriptionMarginTop,
      className = '',
      onSubmit,
      isLoading = false,
      error = null,
      success = null,
      styleConfig = {},
      'aria-label': ariaLabel,
      'aria-describedby': ariaDescribedBy,
      role = 'form',
      ...props
    },
    ref
  ) => {
    const formRef = useRef<HTMLFormElement>(null);
    const titleRef = useRef<HTMLHeadingElement>(null);
    const hasInitiallyFocused = useRef(false);
    const { prefersReducedMotion, shouldAllowTransitions } = useMotionPreference();
    
    // Enhanced accessibility support
    const {
      announce,
      focusManagement,
      generateId,
      getAnnouncementRef,
      prefersHighContrast,
      screenReaderActive,
      keyboardNavigationActive,
    } = useAccessibility({
      trapFocus: true,
      restoreFocus: true,
      announceChanges: true,
    });

    // Merge default and custom style configurations
    const finalStyleConfig: LabelStyleConfig = {
      ...DEFAULT_STYLE_CONFIG,
      ...styleConfig,
    };

    /**
     * Handle form submission with validation
     */
    const handleSubmit = useCallback(
      (e: React.FormEvent) => {
        e.preventDefault();
        
        if (isLoading) {
          return; // Prevent submission while loading
        }

        onSubmit?.(e);
      },
      [onSubmit, isLoading]
    );

    /**
     * Handle keyboard navigation for accessibility
     */
    const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
      const form = formRef.current;
      if (!form) return;

      // Handle Escape key to clear focus or restore previous focus
      if (e.key === 'Escape') {
        const activeElement = document.activeElement as HTMLElement;
        if (activeElement && activeElement.blur) {
          activeElement.blur();
          // Restore focus to form title for screen readers
          if (titleRef.current) {
            titleRef.current.focus();
          }
        }
      }

      // Handle Tab key for focus trapping when keyboard navigation is active
      if (e.key === 'Tab' && keyboardNavigationActive) {
        const focusableElements = form.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        if (e.shiftKey) {
          // Shift + Tab - moving backwards
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          // Tab - moving forwards
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }

      // Handle Enter key on form elements
      if (e.key === 'Enter') {
        const target = e.target as HTMLElement;
        if (target.tagName === 'BUTTON' && target.getAttribute('type') === 'submit') {
          // Let the form handle submit
          return;
        } else if (target.tagName === 'INPUT') {
          // Move to next input or submit if last input
          const inputs = form.querySelectorAll('input');
          const currentIndex = Array.from(inputs).indexOf(target as HTMLInputElement);
          if (currentIndex < inputs.length - 1) {
            e.preventDefault();
            (inputs[currentIndex + 1] as HTMLInputElement).focus();
          } else {
            // Focus submit button if available
            const submitButton = form.querySelector('button[type="submit"]') as HTMLButtonElement;
            if (submitButton) {
              e.preventDefault();
              submitButton.focus();
            }
          }
        }
      }
    }, [keyboardNavigationActive]);

    /**
     * Focus management for accessibility
     */
    useEffect(() => {
      const form = formRef.current;
      if (!form) return;

      // Set up focus trap for keyboard navigation
      let focusTrapCleanup: (() => void) | undefined;
      
      if (keyboardNavigationActive) {
        focusTrapCleanup = focusManagement.trapFocus(form);
      }

      // Only focus the first input on initial mount for screen readers
      // Avoid refocusing when user is actively interacting with the form
      if ((screenReaderActive || keyboardNavigationActive) && !hasInitiallyFocused.current) {
        const firstInput = form.querySelector('input') as HTMLInputElement;
        if (firstInput && document.activeElement === document.body) {
          // Only focus if no other element is currently focused
          setTimeout(() => {
            if (document.activeElement === document.body && !hasInitiallyFocused.current) {
              firstInput.focus();
              hasInitiallyFocused.current = true;
            }
          }, 100);
        }
      }

      // Announce form loading to screen readers
      if (screenReaderActive) {
        announce(`${title} Formular geladen`, 'polite');
      }

      return () => {
        if (focusTrapCleanup) {
          focusTrapCleanup();
        }
      };
    }, [prefersReducedMotion, screenReaderActive, keyboardNavigationActive, focusManagement, announce, title]);

    /**
     * Generate CSS classes based on style configuration and accessibility preferences
     */
    const getFormClasses = () => {
      const baseClasses = [
        'label-form-container',
        'label-typography',
      ];

      // Handle background based on configuration
      if (finalStyleConfig.backgroundColor === 'semi-transparent') {
        baseClasses.push('label-form-background');
      } else if (finalStyleConfig.backgroundColor === 'transparent') {
        baseClasses.push('label-form-transparent');
      } else {
        baseClasses.push('bg-white');
      }

      if (prefersReducedMotion) {
        baseClasses.push('motion-reduce');
      }

      if (isLoading) {
        baseClasses.push('label-loading');
      }

      // Add high contrast support
      if (prefersHighContrast) {
        baseClasses.push('high-contrast');
      }

      // Add keyboard navigation support
      if (keyboardNavigationActive) {
        baseClasses.push('keyboard-navigation');
      }

      // Add screen reader support
      if (screenReaderActive) {
        baseClasses.push('screen-reader-active');
      }

      return baseClasses.join(' ');
    };

    /**
     * Generate unique IDs for accessibility
     */
    const titleId = generateId('label-form-title');
    const descriptionId = description ? generateId('label-form-desc') : undefined;
    const errorId = error ? generateId('label-form-error') : undefined;
    const successId = success ? generateId('label-form-success') : undefined;
    const formId = generateId('label-form');

    /**
     * Build aria-describedby attribute
     */
    const buildAriaDescribedBy = () => {
      const ids = [ariaDescribedBy, descriptionId, errorId, successId].filter(Boolean);
      return ids.length > 0 ? ids.join(' ') : undefined;
    };

    return (
      <div 
        className={`label-form-wrapper ${className}`}
        style={{
          position: 'relative',
          zIndex: 10,
          maxWidth: '400px',
          width: '100%',
          margin: '0 auto',
        }}
      >
        <form
          ref={ref || formRef}
          id={formId}
          className={getFormClasses()}
          onSubmit={handleSubmit}
          onKeyDown={handleKeyDown}
          role={role}
          aria-label={ariaLabel || `${title} Formular`}
          aria-describedby={buildAriaDescribedBy()}
          aria-busy={isLoading}
          aria-live="polite"
          noValidate
          {...props}
        >
          {/* Form Header */}
          <div className="label-form-header">
            <h1
              ref={titleRef}
              id={titleId}
              className="label-text-primary"
              style={{
                // Standardgröße kann per Prop überschrieben werden
                fontSize: titleSize || '1.5rem',
                fontWeight: 600,
                // Abstände können pro Instanz überschrieben werden
                marginBottom: titleMarginBottom ?? '0.25rem',
                textAlign: 'center',
                textTransform: 'uppercase',
                letterSpacing: '0.05em',
              }}
              tabIndex={-1}
            >
              {title}
            </h1>
            
            {description && (
              <p
                id={descriptionId}
                className="label-text-secondary"
                style={{
                  // Standardgröße kann per Prop überschrieben werden
                  fontSize: descriptionSize || '0.9rem',
                  textAlign: 'center',
                  // Abstände können pro Instanz überschrieben werden
                  marginBottom: descriptionMarginBottom ?? '1.5rem',
                  marginTop: descriptionMarginTop ?? '0',
                  lineHeight: 1.4,
                }}
              >
                {description}
              </p>
            )}
          </div>

          {/* Success Message */}
          {success && (
            <div
              id={successId}
              className="label-success"
              role="status"
              aria-live="polite"
              style={{ marginBottom: '1rem' }}
            >
              {success}
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div
              id={errorId}
              className="label-error"
              role="alert"
              aria-live="assertive"
              style={{ marginBottom: '1rem' }}
            >
              {error}
            </div>
          )}

          {/* Form Content */}
          <div 
            className="label-form-content label-stack"
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem',
            }}
          >
            {children}
          </div>

          {/* Loading Indicator */}
          {isLoading && (
            <div
              className="label-loading-indicator"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '4px',
                zIndex: 1,
              }}
              aria-hidden="true"
            >
              <div
                style={{
                  width: '24px',
                  height: '24px',
                  border: '2px solid rgba(0, 0, 0, 0.1)',
                  borderTop: '2px solid #000',
                  borderRadius: '50%',
                  animation: shouldAllowTransitions() ? 'spin 1s linear infinite' : 'none',
                }}
              />
            </div>
          )}
        </form>

        {/* Enhanced screen reader announcements */}
        <div className="label-sr-only" aria-live="polite" aria-atomic="true">
          {isLoading && 'Formular wird verarbeitet, bitte warten'}
          {error && `Fehler: ${error}`}
          {success && `Erfolg: ${success}`}
        </div>

        {/* Additional accessibility announcements */}
        <div
          ref={getAnnouncementRef()}
          className="sr-only"
          aria-live="polite"
          aria-atomic="true"
          style={{
            position: 'absolute',
            left: '-10000px',
            width: '1px',
            height: '1px',
            overflow: 'hidden',
          }}
        />
      </div>
    );
  }
);

LabelForm.displayName = 'LabelForm';

export default LabelForm;