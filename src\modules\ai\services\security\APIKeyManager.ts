/**
 * API Key Manager
 * 
 * Handles secure storage, validation, and management of API keys
 * for AI services like OpenRouter.
 */

import { APIKeyConfig, API_KEY_CONFIGS } from '@/modules/ai/types/security';

export interface StoredAPIKey {
  id: string;
  service: string;
  keyHash: string; // Hashed version for security
  isActive: boolean;
  createdAt: Date;
  lastUsed?: Date;
  environment: 'development' | 'production' | 'both';
}

export interface APIKeyValidationResult {
  isValid: boolean;
  service?: string;
  errors: string[];
}

export class APIKeyManager {
  private readonly storageKey = 'ai_api_keys';
  private readonly encryptionKey = 'ai_module_encryption_key'; // In production, use proper key derivation

  /**
   * Stores an API key securely
   */
  async storeAPIKey(service: string, apiKey: string): Promise<{ success: boolean; error?: string }> {
    try {
      const config = API_KEY_CONFIGS[service.toUpperCase()];
      if (!config) {
        return { success: false, error: `Unbekannter Service: ${service}` };
      }

      // Validate key format
      const validation = this.validateAPIKey(apiKey, service);
      if (!validation.isValid) {
        return { success: false, error: validation.errors.join(', ') };
      }

      // Hash the key for storage
      const keyHash = await this.hashAPIKey(apiKey);
      
      const storedKey: StoredAPIKey = {
        id: `${service}_${Date.now()}`,
        service: service.toLowerCase(),
        keyHash,
        isActive: true,
        createdAt: new Date(),
        environment: config.environment
      };

      // Get existing keys
      const existingKeys = this.getStoredKeys();
      
      // Deactivate old keys for the same service
      const updatedKeys = existingKeys.map(key => 
        key.service === service.toLowerCase() ? { ...key, isActive: false } : key
      );

      // Add new key
      updatedKeys.push(storedKey);

      // Store encrypted
      await this.saveKeys(updatedKeys);

      return { success: true };
    } catch (error) {
      console.error('Error storing API key:', error);
      return { success: false, error: 'Fehler beim Speichern des API-Schlüssels' };
    }
  }

  /**
   * Retrieves an API key for a service (returns the actual key, not hash)
   */
  async getAPIKey(service: string): Promise<string | null> {
    try {
      // In a real implementation, this would decrypt the stored key
      // For now, we'll use environment variables as fallback
      const envKey = this.getEnvironmentAPIKey(service);
      if (envKey) {
        return envKey;
      }

      // Check stored keys (this would require storing encrypted keys, not hashes)
      const storedKeys = this.getStoredKeys();
      const activeKey = storedKeys.find(key => 
        key.service === service.toLowerCase() && key.isActive
      );

      if (activeKey) {
        // In production, decrypt the stored key here
        // For now, return null as we only store hashes
        return null;
      }

      return null;
    } catch (error) {
      console.error('Error retrieving API key:', error);
      return null;
    }
  }

  /**
   * Validates API key format
   */
  validateAPIKey(apiKey: string, service: string): APIKeyValidationResult {
    const errors: string[] = [];
    const config = API_KEY_CONFIGS[service.toUpperCase()];

    if (!config) {
      errors.push(`Unbekannter Service: ${service}`);
      return { isValid: false, errors };
    }

    if (!apiKey || apiKey.trim().length === 0) {
      errors.push('API-Schlüssel darf nicht leer sein');
    }

    if (!config.keyPattern.test(apiKey)) {
      errors.push(`API-Schlüssel entspricht nicht dem erwarteten Format für ${config.name}`);
    }

    return {
      isValid: errors.length === 0,
      service: config.name,
      errors
    };
  }

  /**
   * Lists all stored API keys (without revealing the actual keys)
   */
  listAPIKeys(): Array<Omit<StoredAPIKey, 'keyHash'>> {
    return this.getStoredKeys().map(({ keyHash, ...key }) => key);
  }

  /**
   * Removes an API key
   */
  async removeAPIKey(keyId: string): Promise<boolean> {
    try {
      const existingKeys = this.getStoredKeys();
      const updatedKeys = existingKeys.filter(key => key.id !== keyId);
      
      await this.saveKeys(updatedKeys);
      return true;
    } catch (error) {
      console.error('Error removing API key:', error);
      return false;
    }
  }

  /**
   * Checks if required API keys are configured
   */
  checkRequiredKeys(): { allConfigured: boolean; missing: string[] } {
    const missing: string[] = [];
    
    Object.values(API_KEY_CONFIGS).forEach(config => {
      if (config.isRequired) {
        const hasEnvKey = !!this.getEnvironmentAPIKey(config.id);
        const hasStoredKey = this.getStoredKeys().some(key => 
          key.service === config.id.toLowerCase() && key.isActive
        );
        
        if (!hasEnvKey && !hasStoredKey) {
          missing.push(config.name);
        }
      }
    });

    return {
      allConfigured: missing.length === 0,
      missing
    };
  }

  /**
   * Updates last used timestamp for an API key
   */
  async updateLastUsed(service: string): Promise<void> {
    try {
      const existingKeys = this.getStoredKeys();
      const updatedKeys = existingKeys.map(key => 
        key.service === service.toLowerCase() && key.isActive
          ? { ...key, lastUsed: new Date() }
          : key
      );
      
      await this.saveKeys(updatedKeys);
    } catch (error) {
      console.error('Error updating last used timestamp:', error);
    }
  }

  /**
   * Private helper methods
   */
  private getStoredKeys(): StoredAPIKey[] {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (!stored) return [];
      
      // In production, decrypt here
      return JSON.parse(stored).map((key: any) => ({
        ...key,
        createdAt: new Date(key.createdAt),
        lastUsed: key.lastUsed ? new Date(key.lastUsed) : undefined
      }));
    } catch (error) {
      console.error('Error reading stored keys:', error);
      return [];
    }
  }

  private async saveKeys(keys: StoredAPIKey[]): Promise<void> {
    try {
      // In production, encrypt before storing
      const serialized = JSON.stringify(keys);
      localStorage.setItem(this.storageKey, serialized);
    } catch (error) {
      console.error('Error saving keys:', error);
      throw error;
    }
  }

  private async hashAPIKey(apiKey: string): Promise<string> {
    // Simple hash for demo - in production use proper cryptographic hashing
    const encoder = new TextEncoder();
    const data = encoder.encode(apiKey + this.encryptionKey);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private getEnvironmentAPIKey(service: string): string | null {
    // Check environment variables
    switch (service.toLowerCase()) {
      case 'openrouter':
        // Vite verwendet import.meta.env anstelle von process.env
        return import.meta.env.VITE_OPENROUTER_API_KEY || null;
      default:
        return null;
    }
  }
}

export const apiKeyManager = new APIKeyManager();