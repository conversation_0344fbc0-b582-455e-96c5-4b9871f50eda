@echo off
echo Starting Backend for Lapp Dashboard...

REM Check if backend directory exists
if not exist "backend" (
    echo ERROR: Backend directory not found!
    pause
    exit /b 1
)

REM Change to backend directory
cd backend

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing backend dependencies...
    npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies!
        pause
        exit /b 1
    )
)

REM Generate Prisma clients
echo Generating Prisma clients...
npm run prisma:generate
if errorlevel 1 (
    echo WARNING: Prisma generation failed, but continuing...
)

REM Start the backend
echo Starting backend server on port 3001...
npm run dev