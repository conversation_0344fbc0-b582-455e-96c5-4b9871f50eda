"use client"

import { useState, useCallback, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CheckCircle, Search, Loader2 } from "lucide-react"
import TrommelrechnungService from "@/modules/ai/services/cutting/TrommelrechnungService"

// Props Interface für die MaterialSelector-Komponente
interface MaterialSelectorProps {
  verfügbareMaterialien?: any[];
  selectedMaterial?: string;
  onMaterialChange?: (value: string) => void;
  materialSuchOpen?: boolean;
  onMaterialSuchOpenChange?: (open: boolean) => void;
  materialSuchWert?: string;
  onMaterialSuchWertChange?: (value: string) => void;
  onMaterialSelect?: (material: any) => void;
}

export default function MaterialSelector({ 
  verfügbareMaterialien = [],
  selectedMaterial = "",
  onMaterialChange,
  materialSuchOpen = false,
  onMaterialSuchOpenChange,
  materialSuchWert = "",
  onMaterialSuchWertChange,
  onMaterialSelect
}: MaterialSelectorProps) {
  // State für dynamische Suche
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchTotal, setSearchTotal] = useState(0);
  
  // Debounced Search - Suche erst nach 300ms ohne weitere Eingabe
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  
  // Dynamische Suche implementieren
  const performSearch = useCallback(async (searchTerm: string) => {
    if (searchTerm.length < 2) {
      // Bei weniger als 2 Zeichen keine Suche durchführen
      setSearchResults([]);
      setSearchTotal(0);
      return;
    }
    
    setIsSearching(true);
    try {
      const { materials, total } = await TrommelrechnungService.searchKabelmaterialien(searchTerm, 100);
      setSearchResults(materials);
      setSearchTotal(total);
    } catch (error) {
      console.error('Fehler bei der Materialsuche:', error);
      setSearchResults([]);
      setSearchTotal(0);
    } finally {
      setIsSearching(false);
    }
  }, []);
  
  // Debounced Search Handler
  const handleSearchChange = useCallback((value: string) => {
    onMaterialSuchWertChange?.(value);
    
    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    // Set new timeout for search
    const newTimeout = setTimeout(() => {
      performSearch(value);
    }, 300); // 300ms Debounce
    
    setSearchTimeout(newTimeout);
  }, [searchTimeout, onMaterialSuchWertChange, performSearch]);
  
  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);
  
  // Bestimme welche Materialien angezeigt werden sollen
  const materialsToShow = materialSuchWert.length >= 2 ? searchResults : verfügbareMaterialien.slice(0, 100);
  
  // Verwende die dynamischen Suchergebnisse oder die ersten 100 Materialien
  const displayedMaterials = materialsToShow;
  const hasMoreResults = materialSuchWert.length >= 2 ? searchTotal > 100 : verfügbareMaterialien.length > 100;
  const totalResults = materialSuchWert.length >= 2 ? searchTotal : verfügbareMaterialien.length;

  return (
    <div className="w-full">
      <Popover open={materialSuchOpen} onOpenChange={onMaterialSuchOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={materialSuchOpen}
            className="w-full justify-between text-left"
          >
            <span className="truncate flex-1 mr-2">
              {selectedMaterial && verfügbareMaterialien.length > 0
                ? (() => {
                    const material = verfügbareMaterialien.find((m) => m.MATNR === selectedMaterial);
                    return material ? `${material.MATNR} - ${material.Materialkurztext || 'Kein Text'} (Ø ${material.Kabeldurchmesser}mm)` : "Kabelmaterial auswählen...";
                  })()
                : "Kabelmaterial auswählen..."}
            </span>
            <Search className="h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput 
              placeholder="Nach MATNR oder Materialkurztext suchen... (min. 2 Zeichen)"
              value={materialSuchWert}
              onValueChange={handleSearchChange}
            />
            <CommandEmpty>Kein Material gefunden.</CommandEmpty>
            <CommandList>
              <CommandGroup>
                {isSearching && (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span className="text-sm text-muted-foreground">Suche läuft...</span>
                  </div>
                )}
                {!isSearching && materialsToShow.map((material) => (
                  <CommandItem
                    key={material.MATNR}
                    value={material.MATNR}
                    onSelect={(currentValue) => {
                      onMaterialChange?.(currentValue === selectedMaterial ? "" : currentValue);
                      const selectedMat = materialsToShow.find(m => m.MATNR === currentValue);
                      if (selectedMat && onMaterialSelect) {
                        onMaterialSelect(selectedMat);
                      }
                      onMaterialSuchOpenChange?.(false);
                      onMaterialSuchWertChange?.("");
                    }}
                  >
                    <CheckCircle
                      className={`mr-2 h-4 w-4 ${
                        material.MATNR === selectedMaterial ? "opacity-100" : "opacity-0"
                      }`}
                    />
                    <div className="flex flex-col">
                      <span className="font-medium">{material.MATNR} - {material.Materialkurztext || 'Kein Text'}</span>
                      <span className="text-sm text-gray-500">Ø {material.Kabeldurchmesser}mm</span>
                    </div>
                  </CommandItem>
                ))}
                {/* Zeige Hinweis für dynamische Suche oder lokale Filterung */}
                {!isSearching && materialSuchWert.length >= 2 && searchTotal > 100 && (
                  <div className="px-2 py-1 text-xs text-gray-500 border-t">
                    {searchTotal} Ergebnisse gefunden. Nur die ersten 100 werden angezeigt.
                  </div>
                )}
                {!isSearching && materialSuchWert.length < 2 && verfügbareMaterialien.length > 100 && (
                  <div className="px-2 py-1 text-xs text-gray-500 border-t">
                    Nur die ersten 100 Materialien werden angezeigt. Verwenden Sie die Suche für spezifischere Ergebnisse.
                  </div>
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}