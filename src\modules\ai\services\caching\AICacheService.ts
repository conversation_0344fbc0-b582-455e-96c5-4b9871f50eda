import { IService } from '@/services/base.service';
import { CacheService } from '@/services/cache.service';

export interface CacheEntry<T = any> {
  key: string;
  value: T;
  timestamp: number;
  ttl: number;
  hitCount: number;
  lastAccessed: number;
  size: number;
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
  memoryUsage: number;
}

export interface CacheConfig {
  maxSize: number;
  defaultTtl: number;
  maxMemoryUsage: number;
  evictionPolicy: 'lru' | 'lfu' | 'ttl';
  compressionEnabled: boolean;
}

/**
 * Advanced caching service for AI operations with intelligent cache management
 */
export class AICacheService implements IService {
  readonly serviceName = 'AICacheService';

  private cache = new Map<string, CacheEntry>();
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0
  };

  private config: CacheConfig = {
    maxSize: 1000,
    defaultTtl: 3600000, // 1 hour
    maxMemoryUsage: 100 * 1024 * 1024, // 100MB
    evictionPolicy: 'lru',
    compressionEnabled: true
  };

  constructor(
    private cacheService: CacheService,
    config?: Partial<CacheConfig>
  ) {
    if (config) {
      this.config = { ...this.config, ...config };
    }

    // Start cleanup interval
    setInterval(() => this.cleanup(), 60000); // Every minute
  }

  /**
   * Get cached value with intelligent cache management
   */
  async get<T>(key: string): Promise<T | null> {
    this.stats.totalRequests++;

    const entry = this.cache.get(key);
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check TTL
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    // Update access statistics
    entry.hitCount++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;

    return entry.value as T;
  }

  /**
   * Set cached value with compression and size management
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const actualTtl = ttl || this.config.defaultTtl;
    const size = this.calculateSize(value);

    // Check memory limits
    if (this.getCurrentMemoryUsage() + size > this.config.maxMemoryUsage) {
      await this.evictEntries(size);
    }

    const entry: CacheEntry<T> = {
      key,
      value: this.config.compressionEnabled ? this.compress(value) : value,
      timestamp: Date.now(),
      ttl: actualTtl,
      hitCount: 0,
      lastAccessed: Date.now(),
      size
    };

    this.cache.set(key, entry);

    // Enforce max size
    if (this.cache.size > this.config.maxSize) {
      await this.evictEntries(0); // Force eviction regardless of space
    }
  }

  /**
   * Memoize expensive AI operations
   */
  memoize<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    keyGenerator?: (...args: T) => string,
    ttl?: number
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      const key = keyGenerator ? keyGenerator(...args) : this.generateKey(fn.name, args);

      const cached = await this.get<R>(key);
      if (cached !== null) {
        return cached;
      }

      const result = await fn(...args);
      await this.set(key, result, ttl);
      return result;
    };
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalSize = Array.from(this.cache.values())
      .reduce((sum, entry) => sum + entry.size, 0);

    return {
      totalEntries: this.cache.size,
      totalSize,
      hitRate: this.stats.totalRequests > 0 ? this.stats.hits / this.stats.totalRequests : 0,
      missRate: this.stats.totalRequests > 0 ? this.stats.misses / this.stats.totalRequests : 0,
      evictionCount: this.stats.evictions,
      memoryUsage: this.getCurrentMemoryUsage()
    };
  }

  /**
   * Clear cache with optional pattern matching
   */
  async clear(pattern?: string): Promise<void> {
    if (!pattern) {
      this.cache.clear();
      return;
    }

    const regex = new RegExp(pattern);
    const keysToDelete = Array.from(this.cache.keys()).filter(key => regex.test(key));
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Invalidate cache entries by tags
   */
  async invalidateByTag(tag: string): Promise<void> {
    const keysToDelete = Array.from(this.cache.keys())
      .filter(key => key.includes(`tag:${tag}`));
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Preload cache with frequently used data
   */
  async preload(entries: Array<{ key: string; value: any; ttl?: number }>): Promise<void> {
    for (const entry of entries) {
      await this.set(entry.key, entry.value, entry.ttl);
    }
  }

  private generateKey(functionName: string, args: any[]): string {
    const argsHash = this.hashArgs(args);
    return `${functionName}:${argsHash}`;
  }

  private hashArgs(args: any[]): string {
    return Buffer.from(JSON.stringify(args)).toString('base64');
  }

  private calculateSize(value: any): number {
    return JSON.stringify(value).length * 2; // Rough estimate in bytes
  }

  private compress<T>(value: T): T {
    // Simple compression simulation - in real implementation use actual compression
    return value;
  }

  private getCurrentMemoryUsage(): number {
    return Array.from(this.cache.values())
      .reduce((sum, entry) => sum + entry.size, 0);
  }

  private async evictEntries(requiredSpace?: number): Promise<void> {
    const entries = Array.from(this.cache.entries());

    switch (this.config.evictionPolicy) {
      case 'lru':
        entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);
        break;
      case 'lfu':
        entries.sort(([, a], [, b]) => a.hitCount - b.hitCount);
        break;
      case 'ttl':
        entries.sort(([, a], [, b]) => a.timestamp - b.timestamp);
        break;
    }

    let freedSpace = 0;
    const targetSpace = requiredSpace || this.config.maxMemoryUsage * 0.1; // Free 10% by default
    const entriesToEvict = Math.max(1, Math.ceil(entries.length * 0.1)); // Evict at least 1 entry or 10%

    for (let i = 0; i < Math.min(entriesToEvict, entries.length); i++) {
      const [key, entry] = entries[i];
      if (requiredSpace && freedSpace >= targetSpace) break;

      this.cache.delete(key);
      freedSpace += entry.size;
      this.stats.evictions++;
    }
  }

  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
  }
}