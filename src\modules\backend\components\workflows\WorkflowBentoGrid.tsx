import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import {
  ArrowRight,
  Database,
  FileSpreadsheet,
  Mail,
  Clock,
  CheckCircle,
  AlertTriangle,
  Play,
  Settings,
  Calendar,
  Activity
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import WiFiButton from '@/components/ui/wifi-button';
import { workflowService } from '@/services/workflowService';
import { SAPWorkflowProcess } from '@/types/workflow';
import { ServicegradBentoCard } from './ServicegradBentoCard';
import { BestandBentoCard } from './BestandBentoCard';

interface WorkflowBentoItemProps {
  process: SAPWorkflowProcess;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  onExecute: (processId: string) => Promise<void>;
  isExecuting: boolean;
}

const WorkflowBentoItem = ({
  process,
  className,
  size = 'medium',
  onExecute,
  isExecuting
}: WorkflowBentoItemProps) => {
  const [config, setConfig] = useState<any>(null);
  const [executionProgress, setExecutionProgress] = useState(0);
  const [showConfigDialog, setShowConfigDialog] = useState(false);
  const [isWifiEnabled, setIsWifiEnabled] = useState(false);

  const handleWifiToggle = (enabled: boolean) => {
    setIsWifiEnabled(enabled);
    if (enabled && process.status !== 'running' && !isExecuting) {
      // Optionally auto-execute when WiFi is enabled
      // handleExecute();
    }
  };

  // Color scheme based on process type
  const getColorScheme = () => {
    switch (process.id) {
      case 'servicegrad':
        return {
          bg: 'bg-white hover:bg-blue-50/20',
          border: 'border-blue-300/60 hover:border-blue-400/80',
          icon: 'bg-blue-100/70 text-blue-600 shadow-blue-200/50 group-hover:bg-blue-200/80 group-hover:shadow-blue-300/60',
          pattern: 'bg-[linear-gradient(to_right,#3b82f660_1px,transparent_1px),linear-gradient(to_bottom,#3b82f660_1px,transparent_1px)]',
          bgIcon: 'text-blue-200/50 group-hover:text-blue-300/70',
          gradient: 'from-blue-400 to-blue-300'
        };
      case 'database':
        return {
          bg: 'bg-white hover:bg-green-50/20',
          border: 'border-green-300/60 hover:border-green-400/80',
          icon: 'bg-green-100/70 text-green-600 shadow-green-200/50 group-hover:bg-green-200/80 group-hover:shadow-green-300/60',
          pattern: 'bg-[linear-gradient(to_right,#22c55e60_1px,transparent_1px),linear-gradient(to_bottom,#22c55e60_1px,transparent_1px)]',
          bgIcon: 'text-green-200/50 group-hover:text-green-300/70',
          gradient: 'from-green-400 to-green-300'
        };
      case 'cache':
        return {
          bg: 'bg-white hover:bg-purple-50/20',
          border: 'border-purple-300/60 hover:border-purple-400/80',
          icon: 'bg-purple-100/70 text-purple-600 shadow-purple-200/50 group-hover:bg-purple-200/80 group-hover:shadow-purple-300/60',
          pattern: 'bg-[linear-gradient(to_right,#a855f760_1px,transparent_1px),linear-gradient(to_bottom,#a855f760_1px,transparent_1px)]',
          bgIcon: 'text-purple-200/50 group-hover:text-purple-300/70',
          gradient: 'from-purple-400 to-purple-300'
        };
      default:
        return {
          bg: 'bg-white hover:bg-orange-50/20',
          border: 'border-orange-300/60 hover:border-orange-400/80',
          icon: 'bg-orange-100/70 text-orange-600 shadow-orange-200/50 group-hover:bg-orange-200/80 group-hover:shadow-orange-300/60',
          pattern: 'bg-[linear-gradient(to_right,#f9731660_1px,transparent_1px),linear-gradient(to_bottom,#f9731660_1px,transparent_1px)]',
          bgIcon: 'text-orange-200/50 group-hover:text-orange-300/70',
          gradient: 'from-orange-400 to-orange-300'
        };
    }
  };

  const colorScheme = getColorScheme();

  useEffect(() => {
    loadConfig();
  }, [process.id]);

  const loadConfig = async () => {
    try {
      const loadedConfig = await workflowService.getWorkflowConfig(process.id);
      setConfig(loadedConfig);
    } catch (error) {
      console.error('Failed to load workflow config:', error);
    }
  };

  const handleConfigDialogClose = () => {
    setShowConfigDialog(false);
    // Reload config after dialog closes to get updated values
    loadConfig();
  };

  const getStatusIcon = () => {
    switch (process.status) {
      case 'running':
        return <Clock className="h-5 w-5 text-blue-600 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Database className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (process.status) {
      case 'running':
        return 'text-blue-600';
      case 'completed':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusText = () => {
    switch (process.status) {
      case 'running':
        return 'Läuft';
      case 'completed':
        return 'Abgeschlossen';
      case 'error':
        return 'Fehler';
      default:
        return 'Bereit';
    }
  };

  const handleExecute = async () => {
    setExecutionProgress(0);

    const progressInterval = setInterval(() => {
      setExecutionProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + Math.random() * 10;
      });
    }, 2000);

    try {
      await onExecute(process.id);
      setExecutionProgress(100);
    } catch (error) {
      console.error('Execution failed:', error);
    } finally {
      clearInterval(progressInterval);
      setTimeout(() => setExecutionProgress(0), 2000);
    }
  };

  const variants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: 'spring' as const, damping: 25 },
    },
  };

  return (
    <motion.div
      variants={variants}
      className={cn(
        'group relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl border-2 px-6 pt-6 pb-6 shadow-lg transition-all duration-500',
        colorScheme.bg,
        colorScheme.border,
        className,
      )}
    >
      {/* Background Pattern */}
      <div className={cn("absolute top-0 -right-1/2 z-0 size-full cursor-pointer [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]", colorScheme.pattern)}></div>

      {/* Large Background Icon */}
      <div className={cn("absolute right-1 bottom-3 scale-[4] transition-all duration-700 group-hover:scale-[4.2]", colorScheme.bgIcon)}>
        <Database className="h-8 w-8" />
      </div>

      <div className="relative z-10 flex h-full flex-col justify-between">
        {/* Header */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <div className={cn("flex h-12 w-12 items-center justify-center rounded-full shadow-lg transition-all duration-500", colorScheme.icon)}>
              {getStatusIcon()}
            </div>
            <div className="flex items-center gap-2">
              {config?.schedule?.enabled && (
                <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                  <Calendar className="h-3 w-3 mr-1" />
                  Geplant
                </Badge>
              )}
              <Badge
                className={cn(
                  "text-xs font-medium shadow-sm border-0",
                  process.status === 'running' && "bg-blue-500 text-white",
                  process.status === 'completed' && "bg-green-500 text-white",
                  process.status === 'error' && "bg-red-500 text-white",
                  process.status === 'idle' && "bg-gray-500 text-white"
                )}
              >
                {getStatusText()}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowConfigDialog(true)}
                className="h-8 w-8 p-0 border-gray-300 text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 shadow-sm"
              >
                <Settings className="h-3 w-3" />
              </Button>
            </div>
          </div>

          <h3 className="mb-2 text-lg font-semibold tracking-tight">{process.name}</h3>
          <p className="text-muted-foreground text-sm mb-4">{process.description}</p>

          {/* Workflow Steps - nur bei large size */}
          {size === 'large' && (
            <div className="space-y-2 mb-4">
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <Database className="h-3 w-3 text-blue-500" />
                <span>SAP-Export ({process.tcode})</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <FileSpreadsheet className="h-3 w-3 text-green-500" />
                <span>Excel-Verarbeitung</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <Mail className="h-3 w-3 text-orange-500" />
                <span>E-Mail-Versand</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <Database className="h-3 w-3 text-purple-500" />
                <span>Datenbank-Speicherung</span>
              </div>
            </div>
          )}

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-4 mb-4 text-xs">
            <div>
              <div className="text-muted-foreground">Ziel-Tabelle</div>
              <div className="font-mono text-blue-600">{process.dbTable}</div>
            </div>
            {process.lastRun && (
              <div>
                <div className="text-muted-foreground">Letzter Lauf</div>
                <div className="text-green-600">{process.lastRun.toLocaleDateString('de-DE')}</div>
              </div>
            )}
            {config?.schedule?.enabled && (
              <div className="col-span-2">
                <div className="text-muted-foreground">Zeitplan</div>
                <div className="text-green-600 text-xs">
                  {config.schedule.frequency === 'hourly' && `Alle ${config.schedule.interval}h`}
                  {config.schedule.frequency === 'daily' && `Täglich um ${config.schedule.time}`}
                  {config.schedule.frequency === 'weekly' && `Wöchentlich ${['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'][config.schedule.dayOfWeek || 1]} um ${config.schedule.time}`}
                </div>
              </div>
            )}
          </div>

          {/* Progress Bar (when running) */}
          {(process.status === 'running' || executionProgress > 0) && (
            <div className="mb-4">
              <div className="flex justify-between text-xs mb-1">
                <span className="text-muted-foreground">Fortschritt</span>
                <span className="text-blue-600">{Math.round(executionProgress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1.5">
                <div
                  className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                  style={{ width: `${executionProgress}%` }}
                ></div>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between">
          <Button
            onClick={handleExecute}
            disabled={process.status === 'running' || isExecuting}
            className="w-fit px-4 bg-blue-600 hover:bg-blue-700 text-white text-sm shadow-md"
            size="sm"
          >
            {process.status === 'running' || isExecuting ? (
              <>
                <Clock className="h-3 w-3 mr-2 animate-spin" />
                Läuft...
              </>
            ) : (
              <>
                <Play className="h-3 w-3 mr-2" />
                Prozess Starten
              </>
            )}
          </Button>
          
          {/* WiFi Button */}
          <div className="flex flex-col items-center gap-1">
            <WiFiButton
              checked={isWifiEnabled}
              onChange={handleWifiToggle}
              disabled={process.status === 'running' || isExecuting}
              className="scale-50"
            />
            <span className="text-xs text-muted-foreground">
              {isWifiEnabled ? 'Aktiv' : 'Inaktiv'}
            </span>
          </div>
        </div>
      </div>

      {/* Bottom Gradient */}
      <div className={cn("absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg", colorScheme.gradient)} />

      {/* Drawer ersetzt den bisherigen Dialog - Handhabung nun in ServicegradBentoCard */}
    </motion.div>
  );
};

interface WorkflowBentoGridProps {
  processes: SAPWorkflowProcess[];
  onExecute: (processId: string) => Promise<void>;
  isExecuting: boolean;
}

export function WorkflowBentoGrid({ processes, onExecute, isExecuting }: WorkflowBentoGridProps) {
  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.12,
        delayChildren: 0.1,
      },
    },
  };

  return (
    <div className="w-full">
      <motion.div
        className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {processes.map((process) => {
          // Use specialized Servicegrad card
          if (process.id === 'servicegrad') {
            return (
              <ServicegradBentoCard
                key={process.id}
                process={process}
                onExecute={onExecute}
                isExecuting={isExecuting}
                size="large"
                className="h-full min-h-[400px]"
              />
            );
          }

          // Use specialized Bestand card
          if (process.id === 'bestand') {
            return (
              <BestandBentoCard
                key={process.id}
                className="h-full min-h-[400px]"
                size="large"
              />
            );
          }
  
          // Default bento item for other workflows
          return (
            <WorkflowBentoItem
              key={process.id}
              process={process}
              onExecute={onExecute}
              isExecuting={isExecuting}
              size="medium"
              className="h-full min-h-[350px]"
            />
          );
        })}
      </motion.div>
    </div>
  );
}