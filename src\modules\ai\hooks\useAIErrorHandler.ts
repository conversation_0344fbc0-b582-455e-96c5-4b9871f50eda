/**
 * AI Error Handler Hook
 * React hook for handling AI service errors with recovery and fallbacks
 */

import { useState, useCallback, useRef } from 'react';
import { AIErrorHandler } from '../services/error-handling/AIErrorHandler';
import { AIOperationLogger } from '../services/error-handling/AIOperationLogger';
import { AIServiceError, AIServiceErrorCode } from '../types/errors';
import { formatErrorForUser } from '../utils/errorMessages';

interface UseAIErrorHandlerOptions {
  serviceName: string;
  enableRetry?: boolean;
  maxRetries?: number;
  enableFallback?: boolean;
  onError?: (error: AIServiceError) => void;
  onRecovery?: (error: AIServiceError, result: any) => void;
}

interface AIErrorState {
  error: AIServiceError | null;
  isRetrying: boolean;
  retryCount: number;
  hasRecovered: boolean;
  userMessage: string | null;
}

export function useAIErrorHandler(options: UseAIErrorHandlerOptions) {
  const [errorState, setErrorState] = useState<AIErrorState>({
    error: null,
    isRetrying: false,
    retryCount: 0,
    hasRecovered: false,
    userMessage: null
  });

  const errorHandler = useRef(AIErrorHandler.getInstance());
  const logger = useRef(AIOperationLogger.getInstance());

  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isRetrying: false,
      retryCount: 0,
      hasRecovered: false,
      userMessage: null
    });
  }, []);

  const handleError = useCallback(async <T>(
    error: Error | AIServiceError,
    operation: string,
    originalInput?: any,
    context?: Record<string, any>
  ): Promise<T | null> => {
    // Create structured AI error if it's not already one
    let aiError: AIServiceError;
    if ('code' in error && 'severity' in error) {
      aiError = error as AIServiceError;
    } else {
      aiError = errorHandler.current.createError(
        AIServiceErrorCode.SERVICE_UNAVAILABLE,
        options.serviceName,
        operation,
        error,
        context
      );
    }

    // Update error state
    setErrorState(prev => ({
      ...prev,
      error: aiError,
      userMessage: formatErrorForUser(aiError),
      hasRecovered: false
    }));

    // Call custom error handler
    if (options.onError) {
      options.onError(aiError);
    }

    // Attempt error handling and recovery
    if (options.enableRetry || options.enableFallback) {
      setErrorState(prev => ({ ...prev, isRetrying: true }));

      try {
        const recoveredResult = await errorHandler.current.handleError<T>(
          aiError,
          originalInput,
          options.enableFallback ? options.serviceName : undefined
        );

        if (recoveredResult !== null) {
          setErrorState(prev => ({
            ...prev,
            hasRecovered: true,
            isRetrying: false,
            error: null,
            userMessage: null
          }));

          if (options.onRecovery) {
            options.onRecovery(aiError, recoveredResult);
          }

          return recoveredResult;
        }
      } catch (recoveryError) {
        console.error('[AI Error Handler] Recovery failed:', recoveryError);
      } finally {
        setErrorState(prev => ({ ...prev, isRetrying: false }));
      }
    }

    return null;
  }, [options]);

  const retryOperation = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName: string,
    maxRetries: number = options.maxRetries || 3
  ): Promise<T | null> => {
    if (!errorState.error || errorState.retryCount >= maxRetries) {
      return null;
    }

    setErrorState(prev => ({
      ...prev,
      isRetrying: true,
      retryCount: prev.retryCount + 1
    }));

    try {
      const result = await operation();
      
      // Success - clear error state
      setErrorState({
        error: null,
        isRetrying: false,
        retryCount: 0,
        hasRecovered: true,
        userMessage: null
      });

      return result;

    } catch (error) {
      const retryError = error as Error;
      await handleError(retryError, operationName);
      return null;
    }
  }, [errorState, options.maxRetries, handleError]);

  const executeWithErrorHandling = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName: string,
    operationType: 'query' | 'optimization' | 'prediction' | 'analysis' | 'configuration' = 'query',
    context?: Record<string, any>
  ): Promise<T | null> => {
    // Clear previous errors
    clearError();

    // Start operation logging
    const operationId = logger.current.startOperation(
      options.serviceName,
      operationName,
      operationType,
      context
    );

    try {
      const result = await operation();
      
      // Log successful operation
      logger.current.completeOperation(operationId, true, { result });
      
      return result;

    } catch (error) {
      const operationError = error as Error;
      
      // Handle the error
      const recoveredResult = await handleError<T>(
        operationError,
        operationName,
        context
      );

      // Log operation completion
      const aiError = errorState.error || errorHandler.current.createError(
        AIServiceErrorCode.SERVICE_UNAVAILABLE,
        options.serviceName,
        operationName,
        operationError,
        context
      );

      logger.current.completeOperation(
        operationId, 
        recoveredResult !== null, 
        recoveredResult ? { result: recoveredResult } : undefined,
        aiError
      );

      return recoveredResult;
    }
  }, [options.serviceName, clearError, handleError, errorState.error]);

  const createErrorFromCode = useCallback((
    code: AIServiceErrorCode,
    operation: string,
    originalError?: Error,
    context?: Record<string, any>
  ): AIServiceError => {
    return errorHandler.current.createError(
      code,
      options.serviceName,
      operation,
      originalError,
      context
    );
  }, [options.serviceName]);

  return {
    // Error state
    error: errorState.error,
    userMessage: errorState.userMessage,
    isRetrying: errorState.isRetrying,
    retryCount: errorState.retryCount,
    hasRecovered: errorState.hasRecovered,
    hasError: errorState.error !== null,

    // Error handling functions
    handleError,
    clearError,
    retryOperation,
    executeWithErrorHandling,
    createErrorFromCode,

    // Utility functions
    formatUserError: (error: any, context?: string) => formatErrorForUser(error, context),
    canRetry: errorState.error?.recoverable && errorState.retryCount < (options.maxRetries || 3),
    
    // Error information
    errorSeverity: errorState.error?.severity,
    errorCode: errorState.error?.code,
    isRecoverable: errorState.error?.recoverable || false
  };
}

/**
 * Simplified hook for basic error handling
 */
export function useAIError(serviceName: string) {
  return useAIErrorHandler({
    serviceName,
    enableRetry: true,
    enableFallback: true,
    maxRetries: 3
  });
}

/**
 * Hook for error handling with custom configuration
 */
export function useAIErrorWithConfig(
  serviceName: string,
  config: Partial<UseAIErrorHandlerOptions>
) {
  return useAIErrorHandler({
    serviceName,
    enableRetry: true,
    enableFallback: true,
    maxRetries: 3,
    ...config
  });
}