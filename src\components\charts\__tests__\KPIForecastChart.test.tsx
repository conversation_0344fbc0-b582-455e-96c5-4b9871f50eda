/**
 * KPI Forecast Chart Tests
 * 
 * Tests for the KPI forecast visualization component.
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { KPIForecastChart } from '@/components/charts/KPIForecastChart';
import type { KPIForecast } from '@/types/predictive-analytics';

// Mock Recharts components
vi.mock('recharts', () => ({
  Line: ({ dataKey }: any) => <div data-testid={`line-${dataKey}`} />,
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  Area: ({ dataKey }: any) => <div data-testid={`area-${dataKey}`} />,
  AreaChart: ({ children }: any) => <div data-testid="area-chart">{children}</div>,
}));

const mockForecast: KPIForecast = {
  kpi_id: 'service_level',
  forecast_horizon_hours: 24,
  current_value: 95.5,
  predictions: [
    {
      timestamp: '2024-01-01T13:00:00Z',
      predicted_value: 96.0,
      confidence_interval_lower: 94.0,
      confidence_interval_upper: 98.0,
      confidence_score: 0.85
    },
    {
      timestamp: '2024-01-01T14:00:00Z',
      predicted_value: 95.8,
      confidence_interval_lower: 93.8,
      confidence_interval_upper: 97.8,
      confidence_score: 0.82
    }
  ],
  trend_direction: 'up',
  overall_confidence: 0.835,
  model_used: 'linear_regression',
  generated_at: '2024-01-01T12:00:00Z'
};

describe('KPIForecastChart', () => {
  it('renders chart with correct KPI information', () => {
    render(<KPIForecastChart forecast={mockForecast} />);
    
    expect(screen.getByText('KPI: service_level')).toBeInTheDocument();
    expect(screen.getByText('84% Konfidenz')).toBeInTheDocument();
    expect(screen.getByText(/Aktueller Wert: 95\.50/)).toBeInTheDocument();
    expect(screen.getByText(/Modell: linear_regression/)).toBeInTheDocument();
    expect(screen.getByText(/Horizont: 24h/)).toBeInTheDocument();
  });

  it('displays correct trend icon for upward trend', () => {
    render(<KPIForecastChart forecast={mockForecast} />);
    
    // Should show trending up icon (tested via class or data attributes)
    expect(screen.getByText('Steigend')).toBeInTheDocument();
  });

  it('displays correct trend icon for downward trend', () => {
    const downwardForecast = {
      ...mockForecast,
      trend_direction: 'down' as const
    };
    
    render(<KPIForecastChart forecast={downwardForecast} />);
    
    expect(screen.getByText('Fallend')).toBeInTheDocument();
  });

  it('displays correct trend icon for stable trend', () => {
    const stableForecast = {
      ...mockForecast,
      trend_direction: 'stable' as const
    };
    
    render(<KPIForecastChart forecast={stableForecast} />);
    
    expect(screen.getByText('Stabil')).toBeInTheDocument();
  });

  it('shows low confidence warning when confidence is below 60%', () => {
    const lowConfidenceForecast = {
      ...mockForecast,
      overall_confidence: 0.5
    };
    
    render(<KPIForecastChart forecast={lowConfidenceForecast} />);
    
    expect(screen.getByText('Niedrige Konfidenz - Prognose mit Vorsicht interpretieren')).toBeInTheDocument();
  });

  it('does not show low confidence warning when confidence is above 60%', () => {
    render(<KPIForecastChart forecast={mockForecast} />);
    
    expect(screen.queryByText('Niedrige Konfidenz - Prognose mit Vorsicht interpretieren')).not.toBeInTheDocument();
  });

  it('renders chart components', () => {
    render(<KPIForecastChart forecast={mockForecast} />);
    
    expect(screen.getByTestId('area-chart')).toBeInTheDocument();
    expect(screen.getByTestId('cartesian-grid')).toBeInTheDocument();
    expect(screen.getByTestId('x-axis')).toBeInTheDocument();
    expect(screen.getByTestId('y-axis')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <KPIForecastChart forecast={mockForecast} className="custom-class" />
    );
    
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('formats generated timestamp correctly', () => {
    render(<KPIForecastChart forecast={mockForecast} />);
    
    // Should display German formatted date
    expect(screen.getByText(/Generiert:/)).toBeInTheDocument();
  });

  it('displays confidence with correct color coding', () => {
    // High confidence (>= 80%) should be green
    const highConfidenceForecast = {
      ...mockForecast,
      overall_confidence: 0.9
    };
    
    render(<KPIForecastChart forecast={highConfidenceForecast} />);
    
    const confidenceBadge = screen.getByText('90% Konfidenz');
    expect(confidenceBadge).toBeInTheDocument();
  });

  it('handles empty predictions array', () => {
    const emptyPredictionsForecast = {
      ...mockForecast,
      predictions: []
    };
    
    render(<KPIForecastChart forecast={emptyPredictionsForecast} />);
    
    // Should still render basic information
    expect(screen.getByText('KPI: service_level')).toBeInTheDocument();
  });

  it('formats prediction values correctly in summary', () => {
    render(<KPIForecastChart forecast={mockForecast} />);
    
    // Should show formatted current value
    expect(screen.getByText(/95\.50/)).toBeInTheDocument();
  });
});