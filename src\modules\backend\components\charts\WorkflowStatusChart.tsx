import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON> } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
} from '@/components/ui/chart';

interface WorkflowStatusChartProps {
  data: Array<{
    name: string;
    value: number;
    color: string;
  }>;
}

export function WorkflowStatusChart({ data }: WorkflowStatusChartProps) {
  const chartConfig = {
    value: {
      label: "Ausführungen",
    },
    erfolgreich: {
      label: "Erfolgreich",
      color: "var(--chart-1)",
    },
    fehlgeschlagen: {
      label: "Fehlgeschlagen", 
      color: "var(--chart-2)",
    },
  } satisfies ChartConfig;

  // Transform data for the pie chart
  const chartData = data.map((item, index) => ({
    status: item.name.toLowerCase().replace('ü', 'u').replace('ä', 'a'),
    visitors: item.value,
    fill: index === 0 ? "var(--color-erfolgreich)" : "var(--color-fehlgeschlagen)",
    label: item.name,
  }));

  const total = data.reduce((sum, item) => sum + item.value, 0);

  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>Status Verteilung</CardTitle>
        <CardDescription>Gesamtverteilung aller Ausführungen ({total} gesamt)</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[300px]"
        >
          <PieChart>
            <Pie 
              data={chartData} 
              dataKey="visitors"
              nameKey="label"
              cx="50%"
              cy="50%"
              outerRadius={80}
            />
            <ChartLegend
              content={<ChartLegendContent nameKey="label" />}
              className="-translate-y-2 flex-wrap gap-2 *:basis-1/4 *:justify-center"
            />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}