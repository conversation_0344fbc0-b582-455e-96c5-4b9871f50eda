import React, { useEffect, useState, memo } from "react";
import {
  Bar,
  CartesianGrid,
  BarChart,
  XAxis,
  YAxis,
} from "recharts";
import { useTranslation } from "react-i18next";
import apiService from "@/services/api.service";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

/**
 * ATrL-Chart Komponente
 * 
 * Zeigt die ATrL-Daten als drei gestapelte Balken:
 * 
 * Balken 1 (Einlagerung - gestapelt):
 * - WE ATrL (unten)
 * - Einlagerung Kunde (mitte)
 * - Einlagerung Rest (oben)
 * 
 * Balken 2 (Auslagerung - gestapelt):
 * - Auslagerung (unten)
 * - WaTa Positionen (oben)
 * 
 * Balken 3 (Umlagerungen - einzeln):
 * - Umlagerungen
 * 
 * @param data Optional: <PERSON><PERSON><PERSON> mit den ATrL-Daten
 */

// Definition des Datentyps für den DateRange
interface DateRange {
  from?: Date;
  to?: Date;
}

// Definition des Datentyps für die ATrL-Daten
interface AtrlDataPoint {
  Datum: string;
  weAtrl: number;
  EinlagerungAblKunde: number;
  EinlagerungAblRest: number;
  umlagerungen: number;
  waTaPositionen: number;
  AuslagerungAbl: number;
}

// Definition des Datentyps für Datenbankzeilen
interface DatabaseRow {
  Datum: string;
  weAtrl: number;
  EinlagerungAblKunde: number;
  EinlagerungAblRest: number;
  umlagerungen: number;
  waTaPositionen: number;
  AuslagerungAbl: number;
}

interface AtrlDataChartProps {
  // Optional, da die Daten jetzt auch direkt geladen werden können
  data?: AtrlDataPoint[];
  dateRange?: DateRange;
}

// Footer-Komponente für die ATrL-Chart - Verwendet nur Daten aus der Datenbank
export function AtrlDataChartFooter() {
  const { t } = useTranslation();
  
  return (
    <div className="w-full">
      <div className="flex justify-between w-full">
        <div className="text-muted-foreground text-xs">
          {t("updated")}: {new Date().toLocaleDateString()}
        </div>
      </div>
    </div>
  );
}

export const AtrlDataChart = memo(function AtrlDataChart({ data: propData, dateRange }: AtrlDataChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<AtrlDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Konfiguration für das Diagramm - Gestapelte Balken
  const chartConfig = {
    // Balken 1: Einlagerung (WE + Kunde + Rest)
    weAtrl: {
      label: "WE ATrL",
      color: "var(--chart-1)",
    },
    EinlagerungAblKunde: {
      label: "Einlagerung Kunde", 
      color: "var(--chart-2)",
    },
    EinlagerungAblRest: {
      label: "Einlagerung Rest",
      color: "var(--chart-3)",
    },
    // Balken 2: Auslagerung (Auslagerung + Positionen)
    AuslagerungAbl: {
      label: "Auslagerung",
      color: "var(--chart-4)",
    },
    waTaPositionen: {
      label: "WaTa Positionen",
      color: "var(--chart-5)",
    },
    // Balken 3: Umlagerungen (einzeln)
    umlagerungen: {
      label: "Umlagerungen",
      color: "var(--chart-6)",
    },
  };

  // Lade Daten aus der Datenbank, wenn keine Props übergeben wurden
  useEffect(() => {
    if (propData) {
      // Verwende die übergebenen Daten, wenn vorhanden
      setChartData(propData);
    } else {
      // Lade Daten aus der Datenbank
      const loadData = async () => {
        try {
          setLoading(true);
          setError(null);
          
          // Versuche Daten aus der Datenbank zu laden
          const result = await apiService.getAtrlData();
          
          if (result && Array.isArray(result)) {
            
            // Konvertiere Datenbankdaten zu Chart-Format für gestapelte Balken
            let processedData = result.map((row: unknown) => {
              const dbRow = row as DatabaseRow;
              return {
                Datum: dbRow.Datum,
                // Balken 1: Einlagerung (gestapelt)
                weAtrl: Number(dbRow.weAtrl) || 0,
                EinlagerungAblKunde: Number(dbRow.EinlagerungAblKunde) || 0,
                EinlagerungAblRest: Number(dbRow.EinlagerungAblRest) || 0,
                // Balken 2: Auslagerung (gestapelt)
                AuslagerungAbl: Number(dbRow.AuslagerungAbl) || 0,
                waTaPositionen: Number(dbRow.waTaPositionen) || 0,
                // Balken 3: Umlagerungen (einzeln)
                umlagerungen: Number(dbRow.umlagerungen) || 0
              };
            });
            
            // Filtere Daten basierend auf dem ausgewählten Datumsbereich
            if (dateRange?.from || dateRange?.to) {
              processedData = processedData.filter((item) => {
                const itemDate = new Date(item.Datum);
                
                if (dateRange.from && itemDate < dateRange.from) return false;
                if (dateRange.to && itemDate > dateRange.to) return false;
                
                return true;
              });
            }
            
            setChartData(processedData);
          } else {
            throw new Error('Keine gültigen Daten erhalten');
          }
        } catch (err) {
          // Keine Fallback-Daten mehr verwenden, stattdessen Fehler anzeigen
          setChartData([]);
          setError('Fehler beim Laden der Daten aus der Datenbank')
        } finally {
          setLoading(false);
        }
      };
      
      loadData();
    }
  }, [propData, dateRange]);

  // Berechne Durchschnittswerte für den Footer, wenn Daten vorhanden sind
  const avgUmlagerungen = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.umlagerungen, 0) / chartData.length : 0;
  const avgEinlagerungKunde = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.EinlagerungAblKunde + item.EinlagerungAblRest + item.weAtrl, 0) / chartData.length : 0;
  const avgAuslagerung = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.AuslagerungAbl + item.waTaPositionen, 0) / chartData.length : 0;
  const totalUmlagerungen = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.umlagerungen, 0) : 0;
  const totalEinlagerung = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.EinlagerungAblKunde + item.EinlagerungAblRest + item.weAtrl, 0) : 0;
  const totalAuslagerung = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.AuslagerungAbl + item.waTaPositionen, 0) : 0;

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">{t("loading")}...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="text-red-500 text-4xl mb-2">⚠️</div>
        <p className="font-bold text-red-500">{error}</p>
      </div>
    );
  }

  // Zeige Hinweis an, wenn keine Daten vorhanden sind
  if (chartData.length === 0) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="font-bold">{t("noData")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black w-full h-105 border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <CardTitle>Bewegungen ATrL</CardTitle>
        <CardDescription>
          Vergleich der Bewegungen: Einlagerung, Auslagerung, Umlagerungen
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="w-full h-60"
        >
          <BarChart data={chartData} margin={{ top: 5, right: 12, left: 12, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="Datum" 
              className="text-xs sm:text-sm"
              tickLine={false}
              axisLine={false}
              tickMargin={6}
              height={40}
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => {
                try {
                  const date = new Date(value);
                  // Formatiere das Datum als TT.MM
                  return date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
                } catch {
                  return value;
                }
              }}
            />
            <YAxis 
              className="text-xs sm:text-sm"
              tickLine={false}
              axisLine={false}
              tickMargin={1}
              tick={{ fontSize: 12 }}
              width={35}
              // Label für die Y-Achse
              label={{ 
                value: "Anzahl", 
                angle: -90, 
                position: "insideLeft",
                style: { textAnchor: "middle", fontSize: 12 },
                offset: -5
              }}
            />
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  indicator="dot"
                  labelClassName="font-bold"
                  labelFormatter={(label) => {
                    // Formatiere das Datum im Tooltip als DD.MM.YYYY
                    try {
                      const date = new Date(label);
                      return `Datum: ${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getFullYear()}`;
                    } catch {
                      return `Datum: ${label}`;
                    }
                  }}
                />
              }
            />
            <ChartLegend content={({ payload }) => (
              <ChartLegendContent 
                payload={payload} 
                className="p-2 rounded-md"
              />
            )} />
            
            {/* Balken 1: Einlagerung (gestapelt) - WE ATrL, Einlagerung Kunde, Einlagerung Rest */}
            <Bar 
              dataKey="weAtrl" 
              name="WE ATrL" 
              fill="var(--color-weAtrl)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="einlagerung"
            />
            <Bar 
              dataKey="EinlagerungAblKunde" 
              name="Einlagerung Kunde" 
              fill="var(--color-EinlagerungAblKunde)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="einlagerung"
            />
            <Bar 
              dataKey="EinlagerungAblRest" 
              name="Einlagerung Rest" 
              fill="var(--color-EinlagerungAblRest)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="einlagerung"
            />
            
            {/* Balken 2: Auslagerung (gestapelt) - Auslagerung, WaTa Positionen */}
            <Bar 
              dataKey="AuslagerungAbl" 
              name="Auslagerung" 
              fill="var(--color-AuslagerungAbl)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="auslagerung"
            />
            <Bar 
              dataKey="waTaPositionen" 
              name="WaTa Positionen" 
              fill="var(--color-waTaPositionen)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="auslagerung"
            />
            
            {/* Balken 3: Umlagerungen (einzeln) */}
            <Bar 
              dataKey="umlagerungen" 
              name="Umlagerungen" 
              fill="var(--color-umlagerungen)"
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar"
              stackId="umlagerungen"
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Durchschnitt - Umlagerungen: {avgUmlagerungen.toFixed(0)} | Einlagerung gesamt: {avgEinlagerungKunde.toFixed(0)} | Auslagerung gesamt: {avgAuslagerung.toFixed(0)}
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Gesamt - Umlagerungen: {totalUmlagerungen.toLocaleString()} | Einlagerung: {totalEinlagerung.toLocaleString()} | Auslagerung: {totalAuslagerung.toLocaleString()}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});