import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verifyMaterialdaten() {
  try {
    console.log('Verifying Materialdaten import...');
    
    // Count total records
    const totalCount = await prisma.materialdaten.count();
    console.log(`Total records in materialdaten table: ${totalCount}`);
    
    // Get first 5 records as sample
    const sampleRecords = await prisma.materialdaten.findMany({
      take: 5,
      orderBy: { id: 'asc' }
    });
    
    console.log('\nSample records:');
    sampleRecords.forEach((record, index) => {
      console.log(`${index + 1}. MATNR: ${record.matnr}`);
      console.log(`   Material: ${record.materialkurztext}`);
      console.log(`   Kabeldurchmesser: ${record.kabeldurchmesser}`);
      console.log(`   Bruttogewicht: ${record.bruttogewicht}`);
      console.log(`   Created: ${record.createdAt}`);
      console.log('');
    });
    
    // Check for unique MATNR constraint
    const uniqueMatnrCount = await prisma.materialdaten.groupBy({
      by: ['matnr'],
      _count: { matnr: true }
    });
    
    const duplicates = uniqueMatnrCount.filter(group => group._count.matnr > 1);
    
    if (duplicates.length > 0) {
      console.log(`⚠️  Found ${duplicates.length} duplicate MATNR entries:`);
      duplicates.forEach(dup => {
        console.log(`   MATNR: ${dup.matnr} (${dup._count.matnr} times)`);
      });
    } else {
      console.log('✅ All MATNR entries are unique');
    }
    
    // Check data types and ranges
    const statsQuery = await prisma.$queryRaw`
      SELECT 
        COUNT(*) as total_records,
        COUNT(CASE WHEN kabeldurchmesser IS NOT NULL THEN 1 END) as has_kabeldurchmesser,
        COUNT(CASE WHEN bruttogewicht IS NOT NULL THEN 1 END) as has_bruttogewicht,
        MIN(kabeldurchmesser) as min_kabeldurchmesser,
        MAX(kabeldurchmesser) as max_kabeldurchmesser,
        MIN(bruttogewicht) as min_bruttogewicht,
        MAX(bruttogewicht) as max_bruttogewicht
      FROM materialdaten
    ` as any[];
    
    console.log('\nData statistics:');
    console.log(statsQuery);
    
    console.log('\n✅ Verification completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during verification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the verification
verifyMaterialdaten()
  .then(() => {
    console.log('Verification script finished.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Verification script failed:', error);
    process.exit(1);
  });