import React from 'react';
import { StoerungsFormModal } from '../components/stoerungen/StoerungsFormModal';

/**
 * Utility function to open the Störung form modal
 * This can be used from anywhere in the app where the useDialog hook is available
 * 
 * @param openDialog - The openDialog function from useDialog hook
 * @param onSubmit - Callback function to execute after successful form submission
 */
export const openStoerungsForm = (
  openDialog: (content: React.ReactNode) => void,
  onSubmit?: () => void
) => {
  openDialog(
    <StoerungsFormModal 
      onSubmit={onSubmit || (() => {})}
    />
  );
};