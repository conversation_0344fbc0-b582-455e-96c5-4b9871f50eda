"use client";
import React, { useState, ReactNode } from "react";

export interface AccordionSection {
  id: string;
  title: string;
  content: ReactNode;
  defaultOpen?: boolean;
}

interface AccordionItemProps {
  title: string;
  isOpen: boolean;
  onClick: () => void;
  children: ReactNode;
}

export function AccordionItem({ title, isOpen, onClick, children }: AccordionItemProps) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 mb-3 overflow-hidden">
      {/* Titel dauerhaft mit blauem Hintergrund wie im Hover-State, gemäß Feedback */}
      <button
        className="w-full flex justify-between items-center text-left px-4 py-3 transition-colors bg-blue-50"
        onClick={onClick}
        type="button"
      >
        <span className="text-sm font-medium text-blue-800">{title}</span>
        <svg
          className={`w-5 h-5 transition-transform duration-300 ${isOpen ? "rotate-180 text-blue-700" : "text-blue-700"}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <div className={`overflow-hidden transition-all duration-300 ${isOpen ? "max-h-[800px]" : "max-h-0"}`}>
        <div className={`text-black border-shadow-md border-slate-800 bg-white p-6 transition-all duration-300 hover:border-slate-200 ${isOpen ? "opacity-100" : "opacity-0"}`}>
          {children}
        </div>
      </div>
    </div>
  );
}

export default function AccordionWorkflow({
  sections,
  className,
}: {
  sections: AccordionSection[];
  className?: string;
}) {
  const initial = sections.findIndex((s) => s.defaultOpen) ?? -1;
  const [openIndex, setOpenIndex] = useState<number>(initial >= 0 ? initial : 0);

  return (
    <div className={className ?? ""}>
      {sections.map((s, idx) => (
        <AccordionItem
          key={s.id}
          title={s.title}
          isOpen={openIndex === idx}
          onClick={() => setOpenIndex(openIndex === idx ? -1 : idx)}
        >
          {s.content}
        </AccordionItem>
      ))}
    </div>
  );
}
