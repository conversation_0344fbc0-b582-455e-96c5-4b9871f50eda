#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SAP Servicegrad Automatisierung
Führt nur den ersten SAP-Prozess aus und schließt SAP-Excel
Berechnet Servicegrad, versendet E-Mail und speichert ergebnis in der Datenbank
"""

import os
import sys

# Setze UTF-8 Encoding für Windows-Konsole
if sys.platform.startswith('win'):
    try:
        # Versuche UTF-8 Encoding für stdout zu setzen
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except (AttributeError, OSError):
        # Fallback für ältere Python-Versionen oder wenn reconfigure nicht verfügbar ist
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

import os
import shutil
import pandas as pd
import subprocess
import time
from datetime import datetime, timedelta
import base64  # Für Bild-Einbettung in E-Mails
import sys
import io
# Imports für E-Mail-Funktionalität
import win32com.client as win32
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from email.mime.application import MIMEApplication
from pathlib import Path
from workflow_logger import WorkflowLogger


# === DUAL LOGGING SYSTEM ===
class DualLogger:
    """Logger, der sowohl in Konsole als auch in Datei schreibt"""
    def __init__(self, log_file_path=None):
        if log_file_path is None:
            # Erstelle Log-Datei mit Zeitstempel
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file_path = f"Servicegrad_Workflow_Log_{timestamp}.txt"
        
        self.log_file_path = log_file_path
        self.original_stdout = sys.stdout
        
        # Erstelle Log-Datei und schreibe Header
        with open(self.log_file_path, 'w', encoding='utf-8') as f:
            f.write(f"=== SERVICEGRAD WORKFLOW LOG ===")
            f.write(f"Gestartet: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            f.write(f"=" * 50)
            f.write("")
    
    def write(self, text):
        """Schreibt Text sowohl in Konsole als auch in Datei"""
        # Schreibe in Konsole
        self.original_stdout.write(text)
        self.original_stdout.flush()
        
        # Schreibe in Datei
        try:
            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                f.write(text)
                f.flush()
        except Exception:
            pass  # Ignoriere Fehler beim Datei-Schreiben
    
    def flush(self):
        """Flush sowohl Konsole als auch Datei"""
        self.original_stdout.flush()
    
    def close(self):
        """Schließe das Logging und schreibe Footer"""
        try:
            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                f.write("")
                f.write(f"=" * 50)
                f.write(f"Beendet: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                f.write(f"=== LOG ENDE ===")
        except Exception:
            pass


# === KONFIGURATION ===
# SAP Daten
SAP_EXECUTABLE_PATH = r"C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe"
SAP_SYSTEM_ID = "PS4"
SAP_CLIENT = "009"
SAP_LANGUAGE = "DE"
SCRIPT_DATE = datetime.now()
# -- Konfiguration für den Servicegrad Prozess --
TCODE = "/n/LSGIT/VS_DLV_CHECK"
EXPORT_DIR = r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\SG"
EXPORT_BASENAME = "SG"
# Excel Pfade
SAP_EXCEL_PATH = r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\SourceData\SG"
MAIN_EXCEL_PATH = r"\\adsgroup\Group\UIL-CL-Zentral\10 Dashboard-App\Workflows\Servicegrad_BluePrint.xlsx"
TARGET_EXCEL_PATH = r"\\adsgroup\Group\UIL-CL-Zentral\04 Statistiken & Auswertungen\01 Statistiken\Servicegrad LO\Geschaeftsjahr 2425\Servicegrad LSS Bot\Servicegrad Kennzahlen.xlsx"
# E-Mail-Konfiguration
EMAIL_RECIPIENT = "<EMAIL>"
EMAIL_SUBJECT = "Servicegrad"
# SMTP-Konfiguration (für alternative E-Mail-Methode)
SMTP_SERVER = "smtp.gmail.com"  # Ändern Sie dies entsprechend Ihrem E-Mail-Anbieter
SMTP_PORT = 587
SMTP_USERNAME = "<EMAIL>"  # Tragen Sie hier Ihre E-Mail-Adresse ein
SMTP_PASSWORD = "Power-Automate7"  # Tragen Sie hier Ihr Passwort oder App-Passwort ein
USE_SMTP_IF_OUTLOOK_FAILS = True  # Auf True gesetzt, um SMTP zu verwenden, wenn Outlook fehlschlägt

# === SAP-Excel close ===
def close_excel_sap(logger: WorkflowLogger):
    """Schließt alle laufenden Excel-Prozesse und SAP GUI robust mit taskkill."""
    # Schließe Excel-Prozesse
    logger.info("Versuche, alle Excel-Prozesse zu schließen...")
    try:
        result = subprocess.run(['taskkill', '/F', '/IM', 'excel.exe'], 
                              check=True, capture_output=True, text=True)
        logger.info("Excel wurde erfolgreich geschlossen.")
    except subprocess.CalledProcessError as e:
        if "not found" in e.stderr or "Der Prozess" in e.stderr:
            logger.info("Excel war nicht geöffnet, nichts zu tun.")
        else:
            logger.warning(f"Ein Fehler ist beim Schließen von Excel aufgetreten: {e.stderr}")
    except FileNotFoundError:
        logger.error("Fehler: Der Befehl 'taskkill' wurde nicht gefunden.")
    
    # Schließe SAP GUI-Prozesse
    logger.info("Versuche, alle SAP GUI-Prozesse zu schließen...")
    sap_processes = ['saplogon.exe', 'sapgui.exe']
    
    for process in sap_processes:
        try:
            result = subprocess.run(['taskkill', '/F', '/IM', process], 
                                  check=True, capture_output=True, text=True)
            logger.info(f"{process} wurde erfolgreich geschlossen.")
        except subprocess.CalledProcessError as e:
            if "not found" in e.stderr or "Der Prozess" in e.stderr:
                logger.debug(f"{process} war nicht geöffnet.")
            else:
                logger.warning(f"Ein Fehler ist beim Schließen von {process} aufgetreten: {e.stderr}")
        except FileNotFoundError:
            logger.error("Fehler: Der Befehl 'taskkill' wurde nicht gefunden.")
    
    logger.info("Excel und SAP GUI-Prozesse wurden geschlossen.")

# === SAP wait for UI element ===
def wait_for_element(session, element_id, timeout=30, logger=None):
    """Wartet aktiv darauf, dass ein UI-Element in der SAP-Sitzung erscheint."""
    if logger:
        logger.debug(f"Warte auf Element mit ID: {element_id}...")
    else:
        print(f"   - Warte auf Element mit ID: {element_id}...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            element = session.findById(element_id)
            if logger:
                logger.log_sap_action(f"Element gefunden: {element_id}", element_id, True)
            else:
                print(f"   - Element gefunden: {element_id}")
            return element
        except Exception:
            time.sleep(0.5)
    
    error_msg = f"Zeitüberschreitung: Element '{element_id}' wurde nach {timeout} Sekunden nicht gefunden."
    if logger:
        logger.log_sap_action(f"Element nicht gefunden: {element_id}", element_id, False)
        logger.error(error_msg)
    else:
        print(f"   - FEHLER: {error_msg}")
    raise TimeoutError(error_msg)

# === SAP connection ===
def get_sap_session(logger: WorkflowLogger):
    """Stellt eine Verbindung zu einer SAP-Sitzung her oder startet SAP."""
    try:
        sap_gui_auto = win32.GetObject("SAPGUI")
        application = sap_gui_auto.GetScriptingEngine
        connection = application.Children(0)
        session = connection.Children(0)
        logger.info("Bestehende SAP GUI-Instanz gefunden.")
    except Exception as e:
        logger.info("SAP GUI nicht gefunden. Starte SAP über sapshcut.exe...")
        logger.debug(f"SAP GUI Fehler: {str(e)}")
        
        command = f'"{SAP_EXECUTABLE_PATH}" -system="{SAP_SYSTEM_ID}" -client="{SAP_CLIENT}" -language="{SAP_LANGUAGE}" -maxgui'
        logger.debug(f"SAP Startbefehl: {command}")
        
        subprocess.Popen(command)
        logger.info("Warte 15 Sekunden, bis SAP gestartet ist...")
        time.sleep(15)
        
        try:
            sap_gui_auto = win32.GetObject("SAPGUI")
            application = sap_gui_auto.GetScriptingEngine
            connection = application.Children(0)
            session = connection.Children(0)
        except Exception as e:
            logger.error(f"Fehler beim Verbinden zu SAP nach Start: {str(e)}")
            raise
    
    wait_for_element(session, "wnd[0]", logger=logger)
    logger.info("SAP-Sitzung erfolgreich verbunden und bereit.")
    return session

# === SAP Servicegrad SAP-Automatisierung ===
def run_servicegrad_process(session, script_date, logger: WorkflowLogger):
    """Führt den Servicegrad SAP-Prozess aus."""
    try:
        logger.log_process_start("Servicegrad SAP-Automatisierung")
        
        # Intelligente Datumsberechnung basierend auf Wochentag
        today = datetime.now()
        weekday = today.weekday()  # 0=Montag, 1=Dienstag, ..., 6=Sonntag
        
        if weekday == 0:  # Montag
            # -3 Tage = Freitag der Vorwoche
            target_date = today - timedelta(days=3)
            logger.info(f"Montag erkannt: Verwende Freitag der Vorwoche ({target_date.strftime('%d.%m.%Y')})")
        elif weekday >= 1 and weekday <= 4:  # Dienstag bis Freitag
            # -1 Tag = vorheriger Arbeitstag
            target_date = today - timedelta(days=1)
            logger.info(f"Wochentag {weekday+1} erkannt: Verwende vorherigen Tag ({target_date.strftime('%d.%m.%Y')})")
        else:  # Samstag oder Sonntag (falls das Skript am Wochenende läuft)
            # Verwende den letzten Freitag
            days_back = weekday - 4  # Tage zurück zum Freitag
            target_date = today - timedelta(days=days_back)
            logger.info(f"Wochenende erkannt: Verwende letzten Freitag ({target_date.strftime('%d.%m.%Y')})")
        
        formatted_date = target_date.strftime("%d.%m.%Y")
        export_full_filename = f"{EXPORT_BASENAME}-{formatted_date}.xlsx"
        
        logger.info(f"Starte Transaktion: {TCODE} mit Datum: {formatted_date}")
        session.findById("wnd[0]").maximize()
        session.findById("wnd[0]/tbar[0]/okcd").text = TCODE
        session.findById("wnd[0]").sendVKey(0)
        
        date_field = wait_for_element(session, "wnd[0]/usr/ctxtS_LDDAT-LOW", logger=logger)
        date_field.text = formatted_date
        date_field.setFocus()
        logger.log_sap_action(f"Datum gesetzt: {formatted_date}")
        
        wait_for_element(session, "wnd[0]/tbar[1]/btn[8]", logger=logger).press()
        logger.log_sap_action("Bericht ausgeführt")
        
        wait_for_element(session, "wnd[0]/tbar[1]/btn[33]", logger=logger).press()
        logger.log_sap_action("Layout-Dialog geöffnet")
        
        layout_grid = wait_for_element(session, "wnd[1]/usr/subSUB_CONFIGURATION:SAPLSALV_CUL_LAYOUT_CHOOSE:0500/cntlD500_CONTAINER/shellcont/shell", logger=logger)
        layout_grid.setCurrentCell(2, "TEXT")
        layout_grid.selectedRows = "2"
        layout_grid.clickCurrentCell()
        logger.log_sap_action("Layout ausgewählt (Zeile 2)")
        
        wait_for_element(session, "wnd[0]", logger=logger).findById("mbar/menu[0]/menu[3]/menu[1]").select()
        logger.log_sap_action("Export-Dialog geöffnet")
        
        export_config_field = wait_for_element(session, "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME", logger=logger)
        export_config_field.text = EXPORT_BASENAME
        session.findById("wnd[1]/tbar[0]/btn[20]").press()
        logger.log_sap_action(f"Export-Dateiname gesetzt: {EXPORT_BASENAME}")
        
        logger.info("Warte 2 Sekunden auf finalen Speichern-Dialog...")
        time.sleep(2)
        
        path_field = wait_for_element(session, "wnd[1]/usr/ctxtDY_PATH", logger=logger)
        os.makedirs(EXPORT_DIR, exist_ok=True)
        path_field.text = EXPORT_DIR
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = export_full_filename
        session.findById("wnd[1]/tbar[0]/btn[0]").press()
        logger.log_sap_action(f"Export gestartet nach: {EXPORT_DIR}")

        full_export_path = os.path.join(EXPORT_DIR, export_full_filename)
        logger.info(f"Warte auf Dateisystem-Export: {full_export_path}")
        time.sleep(5)
        
        # Überprüfe, ob die Datei tatsächlich erstellt wurde
        if os.path.exists(full_export_path):
            logger.log_process_complete("Servicegrad SAP-Automatisierung", full_export_path)
            return full_export_path
        else:
            logger.error(f"Export-Datei wurde nicht erstellt: {full_export_path}")
            return None
            
    except Exception as e:
        logger.log_process_error("Servicegrad SAP-Automatisierung", str(e))
        return None


def calculate_script_date():
    """Berechnet Script-Datum basierend auf Wochentag"""
    today = datetime.now()
    weekday = today.weekday()
    
    if weekday == 0:  # Montag
        days_back = 3
    elif weekday in [1, 2, 3, 4]:  # Dienstag bis Freitag
        days_back = 1
    else: # Samstag & Sonntag
        days_back = weekday - 4 # Freitag
    
    return today - timedelta(days=days_back)


def kill_excel_processes():
    """Beendet alle Excel-Prozesse sicher"""
    try:
        subprocess.run(['taskkill', '/F', '/IM', 'excel.exe'], 
                      capture_output=True, text=True, check=False)
        time.sleep(2)
        print("🔧 Excel-Prozesse beendet")
    except FileNotFoundError:
        print("🔧 taskkill nicht gefunden, überspringe Prozess-Beendigung (wahrscheinlich nicht auf Windows).")
    except Exception:
        pass


def safe_copy_sap_data():
    """Kopiert die SAP-Daten nach Tabelle1."""
    try:
        import xlwings as xw
        
        print("=== DATENKOPIE (ALLE SPALTEN A-Q) ===")
        kill_excel_processes()
        
        # Bestimme den korrekten SAP-Dateipfad
        sap_file_path = SAP_EXCEL_PATH
        
        # Falls SAP_EXCEL_PATH ein Verzeichnis ist, suche nach der neuesten Excel-Datei
        if os.path.isdir(SAP_EXCEL_PATH):
            print(f"📁 SAP_EXCEL_PATH ist Verzeichnis: {SAP_EXCEL_PATH}")
            excel_files = [f for f in os.listdir(SAP_EXCEL_PATH) if f.endswith('.xlsx') and f.startswith('SG-')]
            if excel_files:
                # Sortiere nach Änderungsdatum (neueste zuerst)
                excel_files.sort(key=lambda x: os.path.getmtime(os.path.join(SAP_EXCEL_PATH, x)), reverse=True)
                sap_file_path = os.path.join(SAP_EXCEL_PATH, excel_files[0])
                print(f"📄 Verwende neueste SAP-Datei: {excel_files[0]}")
            else:
                print("❌ Keine SAP-Excel-Dateien im Verzeichnis gefunden!")
                return False
        
        print(f"📂 Öffne SAP-Datei: {sap_file_path}")
        
        app = xw.App(visible=False, add_book=False)
        app.display_alerts = False
        app.screen_updating = False
        
        try:
            sap_wb = app.books.open(sap_file_path)
            sap_ws = sap_wb.sheets['Data'] if 'Data' in [s.name for s in sap_wb.sheets] else sap_wb.sheets[0]
            last_row_a = sap_ws.range('A' + str(sap_ws.cells.last_cell.row)).end('up').row
            print(f"📊 SAP-Daten bis Zeile: {last_row_a}")
            
            print("📂 Öffne Main-Excel...")
            main_wb = app.books.open(MAIN_EXCEL_PATH)
            main_ws = main_wb.sheets['Tabelle1'] if 'Tabelle1' in [s.name for s in main_wb.sheets] else main_wb.sheets[0]
            
            print("🗑️ Lösche alte Daten (Formeln bleiben)...")
            main_last_row = main_ws.range('A' + str(main_ws.cells.last_cell.row)).end('up').row
            if main_last_row >= 5:
                main_ws.range(f"A5:Q{main_last_row}").clear_contents()
            
            print("📊 Kopiere SAP-Daten (A-Q)...")
            source_data = sap_ws.range(f'A2:Q{last_row_a}').value
            if source_data:
                main_ws.range('A5').value = source_data
                print(f"✅ {len(source_data) if isinstance(source_data, list) else 1} Datenzeilen kopiert!")

            main_wb.save()
            print("✅ Datenkopie erfolgreich!")
            return True
        finally:
            if app.books:
                for wb in app.books:
                    try:
                        wb.close()
                    except:
                        pass
            try:
                app.quit()
            except:
                pass
            kill_excel_processes()
    except Exception as e:
        print(f"❌ Fehler bei Datenkopie: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_summary_correctly():
    """
    Erstellt die Zusammenfassung in Tabelle2.
    WICHTIG: Schreibt NUR die reinen Zahlen, KEINE Formatierung.
    """
    try:
        import xlwings as xw
        
        print("📊 Erstelle Zusammenfassung (nur reine Daten)...")
        kill_excel_processes()
        app = xw.App(visible=False, add_book=False)
        try:
            wb = app.books.open(MAIN_EXCEL_PATH)
            data_ws = wb.sheets['Tabelle1']
            
            print("   - Lese Daten aus Tabelle1...")
            last_row = data_ws.range('A' + str(data_ws.cells.last_cell.row)).end('up').row
            if last_row < 5:
                print("   - Keine Daten in Tabelle1 gefunden.")
                return False
            
            data_range = data_ws.range(f'A5:R{last_row}').value
            df = pd.DataFrame(data_range, columns=['Werk', 'Lager', 'Lieferung', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'Status'])

            df.dropna(subset=['Werk', 'Lager', 'Status'], inplace=True)
            df['Werk'] = df['Werk'].apply(lambda x: str(int(x)) if pd.notnull(x) else '')
            df['Lager'] = df['Lager'].apply(lambda x: str(int(x)) if pd.notnull(x) else '')

            lager_configs = [
                {'lagernr': '510', 'name': 'WM Stuttgart LC1', 'werk': '5100', 'lager': ['1000']},
                {'lagernr': '512', 'name': 'WM Ludwigsburg LC6', 'werk': '5100', 'lager': ['1010']},
                {'lagernr': '511', 'name': 'WM Hannover LC3', 'werk': '5110', 'lager': ['1000']},
                {'lagernr': '590', 'name': 'WM Polen LC9', 'werk': '5900', 'lager': ['1000']},
                {'lagernr': '51B', 'name': 'WM Illingen LC8', 'werk': '5100', 'lager': ['1090']}
            ]
            
            # Berechne das Script-Datum (das gleiche für beide Felder)
            script_date = calculate_script_date()
            
            # Berechne das Datum für die Spalte "Kalendertag"
            kalendertag = script_date.strftime("%d.%m.%Y")
            
            # Erstelle das Format "Jul 2025" für KalJahr/Monat - verwende deutsche Monatsabkürzungen
            month_names = {
                1: 'Jan', 2: 'Feb', 3: 'Mär', 4: 'Apr', 5: 'Mai', 6: 'Jun',
                7: 'Jul', 8: 'Aug', 9: 'Sep', 10: 'Okt', 11: 'Nov', 12: 'Dez'
            }
            monat_jahr = f"{month_names[script_date.month]} {script_date.year}"
            
            results = []
            print("   - Analysiere Daten pro Lager...")
            for config in lager_configs:
                mask = (df['Werk'] == config['werk']) & (df['Lager'].isin(config['lager']))
                sub_df = df.loc[mask]
                
                erreicht = (sub_df['Status'] == 'Erreicht').sum()
                nicht_erreicht = (sub_df['Status'] == 'Nicht Erreicht').sum()
                total = erreicht + nicht_erreicht
                servicegrad = erreicht / total if total > 0 else 0
                
                results.append({
                    'lagernr': config['lagernr'], 
                    'name': config['name'], 
                    'monat_jahr': monat_jahr,  # NEU: Monat und Jahr
                    'kalendertag': kalendertag,  # NEU: berechnetes Datum
                    'servicegrad': servicegrad, 
                    'total': total, 
                    'erreicht': erreicht, 
                    'nicht_erreicht': nicht_erreicht, 
                    'lieferungen': sub_df['Lieferung'].nunique()
                })

            summary_ws = wb.sheets['Tabelle2'] if 'Tabelle2' in [s.name for s in wb.sheets] else wb.sheets.add('Tabelle2')
            summary_ws.clear()

            headers = ['KalJahr/Monat', 'Lagernummer', 'Lager', 'Kalendertag', 'Servicegrad', 'Anz.\nLieferungsunterpositionen', 'Anz.\nLieferunterpositionen erreicht', 'Anz.\nLieferunterpositionen nicht erreicht', 'Anz.\nLieferungen']
            summary_ws.range('B12').value = headers
            
            # WICHTIG: Setze das Format der KalJahr/Monat Spalte auf Text, bevor Daten geschrieben werden
            summary_ws.range('B13:B17').number_format = '@'  # @ = Text-Format in Excel
            
            print("   - Schreibe Ergebnisse nach Tabelle2 (ohne Formatierung)...")
            for i, res in enumerate(results):
                row_num = 13 + i
                summary_ws.range(f'B{row_num}').value = [
                    res['monat_jahr'],  # NEU: Monat und Jahr - wird jetzt als Text behandelt
                    res['lagernr'], 
                    res['name'], 
                    res['kalendertag'],  # NEU: berechnetes Datum
                    res['servicegrad'], 
                    res['total'], 
                    res['erreicht'], 
                    res['nicht_erreicht'], 
                    res['lieferungen']
                ]

            total_all = sum(r['total'] for r in results)
            erreicht_all = sum(r['erreicht'] for r in results)
            summary_ws.range('B18').value = 'Gesamtergebnis'
            summary_ws.range('F18').value = erreicht_all / total_all if total_all > 0 else 0
            summary_ws.range('G18').value = total_all
            summary_ws.range('H18').value = erreicht_all
            summary_ws.range('I18').value = total_all - erreicht_all
            summary_ws.range('J18').value = sum(r['lieferungen'] for r in results)
            
            wb.save()
            print("✅ Zusammenfassung (nur Daten) erfolgreich erstellt.")
            return True
        finally:
            if app.books:
                for wb in app.books:
                    try:
                        wb.close()
                    except:
                        pass
            try:
                app.quit()
            except:
                pass
            kill_excel_processes()
    except Exception as e:
        print(f"❌ Fehler bei Zusammenfassung: {e}")
        import traceback
        traceback.print_exc()
        return False


def safe_transfer_to_kennzahlen():
    """Überträgt die Daten in die Kennzahlen-Datei (ohne Diagramm)."""
    print("📊 Übertrage Daten zu Kennzahlen-Datei...")
    try:
        import xlwings as xw
        
        kill_excel_processes()
        app = xw.App(visible=False, add_book=False)
        try:
            main_wb = app.books.open(MAIN_EXCEL_PATH)
            main_ws = main_wb.sheets['Tabelle2']
            
            target_wb = app.books.open(TARGET_EXCEL_PATH)
            current_month_sheet_name = datetime.now().strftime("%b %y").replace("Mrz", "Mär").replace("Mai", "Mai")
            target_ws = target_wb.sheets[current_month_sheet_name]

            script_date = calculate_script_date()
            print(f"   - Suche nach Datum {script_date.strftime('%d.%m.%Y')} in Ziel-Sheet...")
            b_values = target_ws.range('B4:B34').value 
            target_row = -1
            for i, cell_val in enumerate(b_values):
                if isinstance(cell_val, datetime) and cell_val.date() == script_date.date():
                    target_row = i + 4
                    break
            
            if target_row == -1:
                print(f"⚠️ Datum {script_date.strftime('%d.%m.%Y')} nicht in Kennzahlen-Datei gefunden. Überspringe Datenübertragung.")
                return True

            print(f"   - Datum gefunden in Zeile {target_row}. Übertrage Daten...")
            lager_mapping = {13: 'C', 14: 'G', 15: 'K', 16: 'S', 17: 'O'}
            for src_row, dest_col in lager_mapping.items():
                gesamt = main_ws.range(f'G{src_row}').value
                erreicht = main_ws.range(f'H{src_row}').value
                target_ws.range(f'{dest_col}{target_row}').value = gesamt
                next_col = chr(ord(dest_col) + 1)
                target_ws.range(f'{next_col}{target_row}').value = erreicht

            # KEIN DIAGRAMM-KOPIEREN MEHR!

            main_wb.save()
            target_wb.save()
            print("✅ Datenübertragung erfolgreich.")
            return True
        finally:
            if app.books:
                for wb in app.books:
                    try:
                        wb.close()
                    except:
                        pass
            try:
                app.quit()
            except:
                pass
            kill_excel_processes()
    except Exception as e:
        print(f"❌ Fehler bei Datenübertragung: {e}")
        import traceback
        traceback.print_exc()
        return True


def final_format_and_style(file_path):
    """
    Öffnet die Datei und wendet die Formatierung und Styles als allerletzten
    Schritt an. Verwendet den korrekten DE-Locale Format-String.
    """
    print("\n🎨 Finale Formatierung & Styling (mit korrektem DE-Format)")
    print(f"📂 Öffne erneut: {file_path}")

    kill_excel_processes()
    app = None
    try:
        import xlwings as xw
        app = xw.App(visible=True, add_book=False)
        wb = app.books.open(file_path)
        time.sleep(1)

        ws = wb.sheets['Tabelle2']
        
        
        print("   - Wende visuelles Styling an...")
        ws.range('B12:J12').color = (190, 190, 190)
        ws.range('B12:J12').api.HorizontalAlignment = -4108  # xlCenter
        ws.range('B13:E17').color = (190, 190, 190)
        # ÄNDERUNG: Orange Färbung für Zeile 17 (letzte Lagerzeile) statt Zeile 18
        ws.range('B17:J17').color = (255, 140, 0)
        ws.range('B18:J18').color = (255, 255, 255)  # Weiß für Gesamtergebnis
        ws.range('B12:J18').api.Borders.LineStyle = 1
        ws.range('B12:J18').api.Borders.Weight = 2

        print("   - Wende aggressive Prozent-Formatierung an...")
        target_range = ws.range('F13:F18')
        
        german_percent_format = '0,00%'
        
        print(f"   - Setze Format: '{german_percent_format}'")
        target_range.number_format = german_percent_format
        
        print("✅ Finale Formatierung & Styling erfolgreich angewendet.")
        
        wb.save()
        time.sleep(1)
        wb.close()
        return True
            
    except Exception as e:
        print(f"❌ Fehler bei der finalen Formatierung: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if app:
            try:
                app.quit()
            except:
                pass
        kill_excel_processes()


def save_excel_range_as_image(ws, range_address, output_path):
    """
    Speichert einen Excel-Bereich als Bild-Datei.
    Verwendet verschiedene Methoden, um die Kompatibilität zu maximieren.
    """
    try:
        # Methode 1: Direkte Export-Methode für neuere Excel-Versionen
        range_obj = ws.range(range_address)
        range_obj.api.CopyPicture(Appearance=1, Format=2)
        
        # Versuche, das Bild zu speichern (verschiedene Methoden)
        try:
            # Methode 1a: Verwende pictures.paste (funktioniert in manchen Excel-Versionen)
            ws.pictures.paste(left=range_obj.left, top=range_obj.top + range_obj.height + 20)
            last_pic = ws.pictures[-1]
            last_pic.api.Export(output_path)
            last_pic.delete()
            return True
        except Exception:
            # Methode 1b: Verwende Shape.Export (funktioniert in anderen Excel-Versionen)
            try:
                temp_sheet = ws.parent.sheets.add()
                temp_sheet.api.Paste()
                if temp_sheet.shapes:
                    shape = temp_sheet.shapes[0]
                    shape.api.Export(output_path)
                    temp_sheet.delete()
                    return True
            except Exception:
                # Methode 1c: Verwende SaveAs mit speziellen Parametern
                try:
                    temp_wb = ws.api.Application.Workbooks.Add()
                    temp_sheet = temp_wb.Sheets(1)
                    temp_sheet.Paste()
                    temp_wb.SaveAs(output_path, FileFormat=5)  # 5 = PNG
                    temp_wb.Close(False)
                    return True
                except Exception:
                    return False
    except Exception:
        return False


def save_chart_from_kennzahlen(temp_chart_path):
    """
    Speichert das Diagramm aus der Kennzahlen-Datei als Bild.
    """
    try:
        import xlwings as xw
        
        print("   - Öffne Kennzahlen-Datei für Diagramm-Export...")
        kill_excel_processes()
        app = xw.App(visible=False, add_book=False)
        try:
            target_wb = app.books.open(TARGET_EXCEL_PATH)
            current_month_sheet_name = datetime.now().strftime("%b %y").replace("Mrz", "Mär").replace("Mai", "Mai")
            target_ws = target_wb.sheets[current_month_sheet_name]
            
            # Überprüfe ChartObjects (eingebettete Diagramme)
            if target_ws.api.ChartObjects().Count > 0:
                chart_obj = target_ws.api.ChartObjects(1)  # Erstes Diagrammobjekt
                # ÄNDERUNG: Diagramm vor dem Export vergrößern für bessere Sichtbarkeit
                chart_obj.Width = 1200
                chart_obj.Height = 400
                chart_obj.Chart.Export(temp_chart_path)
                print("   - Diagramm aus Kennzahlen-Datei exportiert")
                return True
            else:
                print("   - Kein Diagramm in Kennzahlen-Datei gefunden")
                return False
        finally:
            if app.books:
                for wb in app.books:
                    try:
                        wb.close()
                    except:
                        pass
            try:
                app.quit()
            except:
                pass
            kill_excel_processes()
    except Exception as e:
        print(f"   - Fehler beim Exportieren des Diagramms: {e}")
        return False


def embed_image_in_html(image_path):
    """Bettet ein Bild in HTML als base64-kodierten String ein"""
    if not os.path.exists(image_path):
        return None
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode()
            return f"data:image/png;base64,{encoded_string}"
    except Exception as e:
        print(f"   - Fehler beim Einbetten des Bildes: {e}")
        return None


def save_servicegrad_to_database(file_path):
    """
    Liest den Servicegrad von Lagernummer 512 und das Kalendertag-Datum aus der Excel-Datei
    und trägt diese Daten in die SQLite3-Datenbank ein.
    """
    print("\n💾 Speichere Servicegrad-Daten in SQLite3-Datenbank...")
    
    # Datenbankpfad
    db_path = r"C:\Users\<USER>\OneDrive\Desktop\Neuer Ordner\sfm_dashboard.db"
    
    kill_excel_processes()
    app_excel = None
    
    try:
        import xlwings as xw
        import sqlite3
        from datetime import datetime
        
        # Excel-Datei öffnen und Daten lesen
        app_excel = xw.App(visible=False)
        wb = app_excel.books.open(file_path)
        ws = wb.sheets['Tabelle2']
        
        # Suche nach Lagernummer 512 in den Zeilen 13-17
        servicegrad_512 = None
        kalendertag = None
        
        for row_num in range(13, 18):  # Zeilen 13-17 durchsuchen
            lagernummer = ws.range(f'C{row_num}').value  # Spalte C = Lagernummer
            if lagernummer == 512:
                servicegrad_512 = ws.range(f'F{row_num}').value  # Spalte F = Servicegrad
                kalendertag = ws.range(f'E{row_num}').value  # Spalte E = Kalendertag
                print(f"   - Lagernummer 512 gefunden in Zeile {row_num}")
                print(f"   - Servicegrad: {servicegrad_512}")
                print(f"   - Kalendertag: {kalendertag}")
                break
        
        # Excel-Datei schließen
        try:
            wb.close()
        except:
            pass
        try:
            app_excel.quit()
        except:
            pass
        app_excel = None
        
        if servicegrad_512 is None or kalendertag is None:
            print("   ⚠️ Lagernummer 512 nicht gefunden oder Daten unvollständig")
            return False
        
        # Datenformatierung
        # Servicegrad von Prozent (z.B. 0.9777) zu Dezimal mit genau 4 Nachkommastellen
        servicegrad_decimal = round(float(servicegrad_512), 4)
        
        # Datum formatieren - je nach Eingabeformat
        if isinstance(kalendertag, datetime):
            datum_str = kalendertag.strftime('%Y-%m-%d')
        elif isinstance(kalendertag, str):
            # Versuche verschiedene Datumsformate zu parsen
            try:
                # Format DD.MM.YYYY
                if '.' in kalendertag:
                    datum_obj = datetime.strptime(kalendertag, '%d.%m.%Y')
                    datum_str = datum_obj.strftime('%Y-%m-%d')
                else:
                    # Andere Formate...
                    datum_str = kalendertag
            except:
                print(f"   ⚠️ Konnte Datum nicht parsen: {kalendertag}")
                return False
        else:
            print(f"   ⚠️ Unbekanntes Datumsformat: {type(kalendertag)} - {kalendertag}")
            return False
        
        print(f"   - Formatierte Daten: Datum={datum_str}, Servicegrad={servicegrad_decimal:.4f}")
        
        # SQLite3-Datenbankverbindung
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Prüfe, ob Tabelle existiert
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS dispatch_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    datum DATE,
                    servicegrad REAL
                )
            """)
            
            # Prüfe, ob bereits ein Eintrag für dieses Datum existiert
            cursor.execute(
                "SELECT id FROM dispatch_data WHERE datum = ?", 
                (datum_str,)
            )
            existing = cursor.fetchone()
            
            if existing:
                # Update existierenden Eintrag
                cursor.execute(
                    "UPDATE dispatch_data SET servicegrad = ? WHERE datum = ?",
                    (servicegrad_decimal, datum_str)
                )
                print(f"   - Bestehender Eintrag für {datum_str} aktualisiert")
            else:
                # Neuen Eintrag hinzufügen
                cursor.execute(
                    "INSERT INTO dispatch_data (datum, servicegrad) VALUES (?, ?)",
                    (datum_str, servicegrad_decimal)
                )
                print(f"   - Neuer Eintrag für {datum_str} hinzugefügt")
            
            conn.commit()
            conn.close()
            
            print("✅ Servicegrad-Daten erfolgreich in Datenbank gespeichert")
            return True
            
        except sqlite3.Error as e:
            print(f"   ❌ Datenbankfehler: {e}")
            return False
        except Exception as e:
            print(f"   ❌ Allgemeiner Fehler bei Datenbankoperation: {e}")
            return False
            
    except Exception as e:
        print(f"   ❌ Fehler beim Lesen der Excel-Daten: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if app_excel:
            try:
                app_excel.quit()
            except:
                pass
        kill_excel_processes()


def send_report_by_email(file_path):
    """
    Erstellt eine E-Mail mit dem Bericht. Versucht zuerst Outlook zu verwenden,
    und falls das fehlschlägt, wird SMTP als Alternative verwendet.
    Wenn beides fehlschlägt, wird der Bericht lokal gespeichert.
    """
    print("\n📧 Erstelle und versende E-Mail-Bericht...")
    
    kill_excel_processes()
    app_excel = None
    temp_files = []
    report_saved_locally = False
    table_data = None  # Speichert die Tabellendaten
    
    try:
        import xlwings as xw

        app_excel = xw.App(visible=False)
        wb = app_excel.books.open(file_path)
        ws = wb.sheets['Tabelle2']

        # Speichere Tabelle und Diagramm als Bilder für E-Mail
        temp_dir = os.path.dirname(file_path)
        temp_table_path = os.path.join(temp_dir, "temp_table.png")
        temp_chart_path = os.path.join(temp_dir, "temp_chart.png")
        
        # Bestimme den Pfad für die temporäre Berichtskopie
        report_path = os.path.join(temp_dir, "Servicegrad_Bericht.xlsx")
            
        temp_files = [temp_table_path, temp_chart_path, report_path]
        
        # Speichere eine Kopie der Excel-Datei für den Anhang oder lokalen Bericht
        wb.save(report_path)
        report_saved_locally = True
        print(f"   - Bericht gespeichert unter: {report_path}")
        
        # Lese die Tabelle vor dem Schließen der Excel-Datei
        try:
            print("   - Lese Tabellendaten aus Tabelle2...")
            results_range = ws.range("B12:J18")
            table_data = []
            for row in range(results_range.rows.count):
                row_data = []
                for col in range(results_range.columns.count):
                    cell = results_range[row, col]
                    cell_value = cell.value
                    row_data.append(cell_value)
                table_data.append(row_data)
            print("   - Tabellendaten erfolgreich ausgelesen")
        except Exception as e:
            print(f"   - Fehler beim Auslesen der Tabellendaten: {e}")
            table_data = None
        
        # Versuche, die Tabelle als Bild zu speichern (als Backup)
        table_saved = save_excel_range_as_image(ws, "B12:J18", temp_table_path)
        if table_saved:
            print("   - Tabelle als Bild gespeichert für E-Mail")
        
        # Schließe die Excel-Datei vor dem Erstellen der E-Mail
        try:
            wb.close()
        except:
            pass
        try:
            app_excel.quit()
        except:
            pass
        app_excel = None
        
        # Speichere das Diagramm direkt aus der Kennzahlen-Datei
        chart_saved = save_chart_from_kennzahlen(temp_chart_path)

        # Versuche zuerst Outlook zu verwenden, wenn nicht übersprungen
        outlook_success = False
        try_outlook = True  # Standardmäßig versuchen wir Outlook zu verwenden
        
        # Überprüfe, ob wir Outlook überspringen sollen
        if not try_outlook:
            print("   - Überspringe Outlook-Versuch")
        else:
            print("   - Versuche Outlook zu verwenden...")
            try:
                # Versuche verschiedene Methoden, um Outlook zu starten
                outlook = None
                mail = None
                
                # Methode 1: Standard-Dispatch
                try:
                    outlook = win32.Dispatch('Outlook.Application')
                    mail = outlook.CreateItem(0)
                    print("   - Outlook-Verbindung erfolgreich")
                except Exception as e1:
                    print(f"   - Outlook-Verbindung fehlgeschlagen: {e1}")
                    mail = None
                    
                # Methode 2: Alternative Dispatch-Methode falls Methode 1 fehlschlägt
                if mail is None:
                    try:
                        outlook = win32.Dispatch("Outlook.Application")
                        mail = outlook.CreateItem(0)  # 0 = olMailItem
                        print("   - Outlook-Verbindung mit alternativer Methode erfolgreich")
                    except Exception as e2:
                        print(f"   - Alternative Outlook-Verbindung fehlgeschlagen: {e2}")
                        mail = None
                
                if mail:
                    mail.To = EMAIL_RECIPIENT
                    mail.Subject = f"{EMAIL_SUBJECT} für den {datetime.now().strftime('%d.%m.%Y')}"
                    mail.BodyFormat = 2  # HTML-Format
                    
                    # Erstelle HTML-Body mit Tabelleninhalt
                    html_body = "<html><body>"
                    html_body += "<p>Sehr geehrte Damen und Herren,</p>"
                    html_body += "<p>hier der aktuelle Servicegrad:</p>"
                    
                    # Füge die Tabelle als HTML-Tabelle ein
                    if table_data:
                        # Container für die Tabelle - linksbündig ohne Zentrierung
                        html_body += "<div style='width: 100%; max-width: 1200px; overflow-x: auto;'>"
                        
                        # Tabellengröße und -stil definieren - reduzierte Zellenhöhe
                        html_table = "<table border='1' cellspacing='0' cellpadding='3' style='border-collapse: collapse; width: 100%; max-width: 1150px; font-family: Arial, sans-serif; font-size: 11px; line-height: 1.2;'>"
                        for i, row in enumerate(table_data):
                            html_table += "<tr>"
                            for j, cell_value in enumerate(row):
                                # Formatiere Zellen
                                style = ""
                                # Für die erste Zeile (Header) grau hinterlegen
                                if i == 0:
                                    style += "background-color: #BEBEBE; text-align: center;"
                                # ÄNDERUNG: Für die vorletzte Zeile (letzte Lagerzeile) orange hinterlegen
                                elif i == len(table_data) - 1:
                                    style += "background-color: #FF8C00; text-align: center;"
                                # Für die Spalten B-E (Datum, Lagernummer, Lager, Kalendertag) grau hinterlegen
                                elif j < 4:
                                    style += "background-color: #BEBEBE;"
                                
                                # Spaltenbreiten definieren
                                if j == 0:  # KalJahr/Monat
                                    style += "width: 80px;"
                                elif j == 1:  # Lagernummer
                                    style += "width: 80px;"
                                elif j == 2:  # Lager
                                    style += "width: 280px;"
                                elif j == 3:  # Kalendertag
                                    style += "width: 100px;"
                                elif j == 4:  # Servicegrad
                                    style += "width: 80px; text-align: center;"
                                else:  # Andere Spalten
                                    style += "width: 80px; text-align: right;"
                                
                                # Formatiere Zahlen und Prozente
                                if isinstance(cell_value, (int, float)):
                                    if j == 4:  # Servicegrad-Spalte (E)
                                        cell_value = f"{cell_value:.2%}".replace('.', ',')
                                    else:
                                        cell_value = f"{cell_value:,.0f}".replace(',', '.').replace('.', ',')
                                
                                # Leere Zellen
                                if cell_value is None:
                                    cell_value = "&nbsp;"
                                
                                html_table += f"<td style='{style}'>{cell_value}</td>"
                            html_table += "</tr>"
                        html_table += "</table>"
                        html_body += html_table
                        # Container schließen
                        html_body += "</div>"
                    else:
                        html_body += "<p><i>Die Tabelle steht nicht zur Verfügung.</i></p>"
                    
                    # Hinweis zum Zielwert hinzufügen
                    html_body += "<p style='font-weight: bold; color: #FF0000;'>Hinweis: Sobald der Zielwert von 98,5% unterschritten wird, müssen entsprechende Maßnahmen abgeleitet werden.</p>"
                    
                    # Füge das Diagramm als Bild ein
                    html_body += "<br><br><h3>Servicegrad Monatsverlauf:</h3>"
                    if chart_saved and os.path.exists(temp_chart_path) and os.path.getsize(temp_chart_path) > 0:
                        # Füge das Diagramm als Anhang hinzu
                        mail.Attachments.Add(temp_chart_path)
                        
                        # Erstelle eine CID für das eingebettete Bild
                        chart_cid = "servicegrad_chart"
                        
                        # Füge das Bild als eingebettetes Bild hinzu
                        attachment = mail.Attachments.Add(temp_chart_path)
                        attachment.PropertyAccessor.SetProperty(
                            "http://schemas.microsoft.com/mapi/proptag/0x3712001F", 
                            chart_cid
                        )
                        
                        # ÄNDERUNG: Diagramm verkleinern
                        html_body += f'<img src="cid:{chart_cid}" alt="Servicegrad Monatsverlauf" style="max-width: auto; height: auto;">'
                    else:
                        html_body += "<p><i>Das Diagramm steht nicht zur Verfügung.</i></p>"
                    
                    print("   - Tabelleninhalt und Diagramm erfolgreich in E-Mail eingefügt")
                    
                    html_body += "</body></html>"
                    mail.HTMLBody = html_body
                    
                    # ÄNDERUNG: E-Mail automatisch senden statt nur anzeigen
                    mail.Send()
                    print("✅ Outlook-E-Mail wurde automatisch gesendet.")
                    outlook_success = True
            except Exception as outlook_err:
                print(f"   - Outlook-Methode fehlgeschlagen: {outlook_err}")
        
        # Wenn Outlook fehlschlägt und SMTP konfiguriert ist, verwende SMTP
        if not outlook_success and USE_SMTP_IF_OUTLOOK_FAILS and SMTP_USERNAME and SMTP_PASSWORD:
            try:
                print("   - Versuche SMTP als Alternative...")
                
                # Erstelle MIME-Nachricht
                msg = MIMEMultipart('related')  # 'related' für eingebettete Bilder
                msg['From'] = SMTP_USERNAME
                msg['To'] = EMAIL_RECIPIENT
                msg['Subject'] = f"{EMAIL_SUBJECT} für den {datetime.now().strftime('%d.%m.%Y')}"
                
                # Erstelle HTML-Version mit Tabelleninhalt
                try:
                    # Erstelle HTML-Tabelle
                    html_body = "<html><body>"
                    html_body += "<p>Sehr geehrte Damen und Herren,</p>"
                    html_body += "<p>hier die aktuelle Servicegrad-Auswertung:</p>"
                    
                    # Füge die Tabelle als HTML-Tabelle ein
                    if table_data:
                        html_table = "<table border='1' cellspacing='0' cellpadding='5' style='border-collapse: collapse;'>"
                        for i, row in enumerate(table_data):
                            html_table += "<tr>"
                            for j, cell_value in enumerate(row):
                                # Formatiere Zellen
                                style = ""
                                # Für die erste Zeile (Header) grau hinterlegen
                                if i == 0:
                                    style += "background-color: #BEBEBE; text-align: center;"
                                # ÄNDERUNG: Für die vorletzte Zeile (letzte Lagerzeile) orange hinterlegen
                                elif i == len(table_data) - 2:
                                    style += "background-color: #FF8C00; text-align: center;"
                                # Für die Spalten B-E (Datum, Lagernummer, Lager, Kalendertag) grau hinterlegen
                                elif j < 4:
                                    style += "background-color: #BEBEBE;"
                                
                                # Formatiere Zahlen und Prozente
                                if isinstance(cell_value, (int, float)):
                                    if j == 4:  # Servicegrad-Spalte (E)
                                        cell_value = f"{cell_value:.2%}".replace('.', ',')
                                    else:
                                        cell_value = f"{cell_value:,.0f}".replace(',', '.').replace('.', ',')
                                
                                # Leere Zellen
                                if cell_value is None:
                                    cell_value = "&nbsp;"
                                
                                html_table += f"<td style='{style}'>{cell_value}</td>"
                            html_table += "</tr>"
                        html_table += "</table>"
                        html_body += html_table
                    else:
                        html_body += "<p><i>Die Tabelle steht nicht zur Verfügung.</i></p>"
                    
                    # Füge das Diagramm als Bild ein
                    html_body += "<br><br><h3>Servicegrad-Diagramm:</h3>"
                    if chart_saved and os.path.exists(temp_chart_path) and os.path.getsize(temp_chart_path) > 0:
                        # Erstelle eine CID für das eingebettete Bild
                        chart_cid = "servicegrad_chart"
                        
                        # Füge das Bild als eingebettetes Bild hinzu
                        with open(temp_chart_path, 'rb') as img_file:
                            img_attachment = MIMEImage(img_file.read())
                            img_attachment.add_header('Content-ID', f'<{chart_cid}>')
                            msg.attach(img_attachment)
                        
                        # ÄNDERUNG: Diagramm verkleinern
                        html_body += f'<img src="cid:{chart_cid}" style="max-width: auto; height: auto;">'
                    else:
                        html_body += "<p><i>Das Diagramm steht nicht zur Verfügung.</i></p>"
                    
                    html_body += "</body></html>"
                    
                    # Füge den HTML-Teil hinzu
                    msg.attach(MIMEText(html_body, 'html'))
                    print("   - Tabelleninhalt und Diagramm erfolgreich in E-Mail eingefügt")
                except Exception as table_err:
                    print(f"   - Fehler beim Einfügen des Tabelleninhalts: {table_err}")
                    html_body = "<html><body><p>Sehr geehrte Damen und Herren,</p><p>hier die aktuelle Servicegrad-Auswertung.</p>"
                    html_body += "<p><i>Der Tabelleninhalt konnte nicht eingefügt werden.</i></p></body></html>"
                    msg.attach(MIMEText(html_body, 'html'))
                
                # Füge die Excel-Datei als Anhang hinzu
                with open(report_path, 'rb') as excel_file:
                    excel_attachment = MIMEApplication(excel_file.read(), Name=os.path.basename(report_path))
                    excel_attachment['Content-Disposition'] = f'attachment; filename="{os.path.basename(report_path)}"'
                    msg.attach(excel_attachment)
                
                # Verbinde mit SMTP-Server und sende E-Mail
                try:
                    with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
                        server.starttls()
                        server.login(SMTP_USERNAME, SMTP_PASSWORD)
                        server.send_message(msg)
                    
                    print("✅ E-Mail wurde über SMTP versendet.")
                    return True
                except Exception as smtp_conn_err:
                    print(f"   - SMTP-Verbindung fehlgeschlagen: {smtp_conn_err}")
                    print("   - Versuche alternative SMTP-Einstellungen...")
                    
                    # Versuche alternative SMTP-Einstellungen für Gmail
                    try:
                        with smtplib.SMTP_SSL("smtp.gmail.com", 465) as server:
                            server.login(SMTP_USERNAME, SMTP_PASSWORD)
                            server.send_message(msg)
                        
                        print("✅ E-Mail wurde über alternative SMTP-Verbindung versendet.")
                        return True
                    except Exception as alt_smtp_err:
                        print(f"   - Alternative SMTP-Verbindung fehlgeschlagen: {alt_smtp_err}")
                        raise  # Weitergabe des Fehlers an die äußere Exception-Behandlung
            except Exception as smtp_err:
                print(f"   - SMTP-Methode fehlgeschlagen: {smtp_err}")
        
        # Wenn weder Outlook noch SMTP funktioniert haben
        if not outlook_success and (not USE_SMTP_IF_OUTLOOK_FAILS or not SMTP_USERNAME or not SMTP_PASSWORD):
            if SAVE_REPORT_LOCALLY and report_saved_locally:
                print("✅ E-Mail konnte nicht erstellt werden, aber der Bericht wurde lokal gespeichert.")
                print(f"   - Sie finden den Bericht hier: {report_path}")
            else:
                print("ℹ️ Hinweis: Um E-Mails ohne Outlook zu versenden, konfigurieren Sie die SMTP-Einstellungen.")
                print("   - Tragen Sie SMTP_USERNAME und SMTP_PASSWORD ein und setzen Sie USE_SMTP_IF_OUTLOOK_FAILS=True.")
                print("   - Alternativ können Sie SAVE_REPORT_LOCALLY=True setzen, um den Bericht lokal zu speichern.")
                print("⚠️ E-Mail konnte nicht erstellt werden. Der Bericht wurde trotzdem generiert.")
                print(f"   - Sie finden den Bericht hier: {file_path}")
                if os.path.exists(report_path) and report_path != file_path:
                    print(f"   - Eine Kopie des Berichts wurde hier gespeichert: {report_path}")
                    # Diese Datei nicht löschen, damit der Benutzer sie manuell versenden kann
                    if report_path in temp_files:
                        temp_files.remove(report_path)
        
        return True

    except Exception as e:
        print(f"❌ Fehler beim Erstellen der E-Mail: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Lösche temporäre Dateien
        for temp_file in temp_files:
            if os.path.exists(temp_file) and temp_file.endswith(".png"):  # Behalte Excel-Datei für manuelle Verwendung
                try:
                    os.remove(temp_file)
                except:
                    pass
        
        # Stelle sicher, dass Excel beendet wird
        if app_excel:
            try:
                app_excel.quit()
            except:
                pass
        kill_excel_processes()


# ======================= NEUE AUFRÄUMFUNKTION =======================
def cleanup_main_excel(file_path):
    """
    Setzt die Haupt-Arbeitsmappe in ihren ursprünglichen Zustand zurück,
    indem alle generierten Inhalte entfernt werden.
    """
    print("\n🧹 Räume die Arbeitsmappe auf...")
    
    kill_excel_processes()
    app = None
    try:
        import xlwings as xw
        app = xw.App(visible=False)
        wb = app.books.open(file_path)

        # 1. Tabelle2 aufräumen (Inhalte und Diagramm)
        print("   - Leere Tabelle2...")
        ws2 = wb.sheets['Tabelle2']
        ws2.clear() # Löscht alle Inhalte und Formate
        # Lösche alle Formen/Diagramme auf dem Blatt
        for shape in ws2.shapes:
            shape.delete()
        
        # 2. Tabelle1 aufräumen (importierte Daten)
        print("   - Leere importierte Daten in Tabelle1...")
        ws1 = wb.sheets['Tabelle1']
        last_row = ws1.range('A' + str(ws1.cells.last_cell.row)).end('up').row
        if last_row >= 5:
            # Löscht nur die Daten, lässt die Formeln in Spalte R unberührt
            ws1.range(f"A5:Q{last_row}").clear_contents()

        wb.save()
        print("✅ Arbeitsmappe erfolgreich aufgeräumt.")
        return True

    except Exception as e:
        print(f"❌ Fehler beim Aufräumen der Arbeitsmappe: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if app:
            try:
                app.quit()
            except:
                pass
        kill_excel_processes()
# ====================================================================


def run_sap_export_process():
    """Führt den SAP-Export-Prozess aus und kopiert die Datei zum lokalen Pfad"""
    global SAP_EXCEL_PATH  # Global-Deklaration am Anfang der Funktion
    try:
        # Logger für den SAP-Workflow
        main_logger = WorkflowLogger("sap_servicegrad_workflow")
        
        main_logger.info("=== SAP Servicegrad Export gestartet ===")
        
        # SAP-Sitzung herstellen
        sap_session = get_sap_session(main_logger)
        
        if sap_session:
            # SAP-Prozess ausführen
            main_logger.info("=" * 20 + " STARTE SAP SERVICEGRAD EXPORT " + "=" * 20)
            sg_logger = WorkflowLogger("servicegrad")
            script_date = calculate_script_date()
            exported_file = run_servicegrad_process(sap_session, script_date, sg_logger)
            
            if exported_file:
                main_logger.info(f"SAP-Export erfolgreich erstellt: {exported_file}")
                
                # Warte, bis SAP die Datei freigibt
                main_logger.info("Warte 5 Sekunden, bis SAP die Datei freigibt...")
                time.sleep(5)
                
                # Schließe Excel-Prozesse vor dem Kopieren
                close_excel_sap(main_logger)
                time.sleep(2)
                
                # Bestimme Ziel-Dateiname mit korrektem Pfad
                export_filename = os.path.basename(exported_file)
                target_file_path = os.path.join(SAP_EXCEL_PATH, export_filename)
                
                # Kopiere die exportierte Datei zum Zielverzeichnis
                try:
                    # Stelle sicher, dass Zielverzeichnis existiert
                    os.makedirs(SAP_EXCEL_PATH, exist_ok=True)
                    
                    # Kopiere Datei
                    shutil.copy2(exported_file, target_file_path)
                    main_logger.info(f"Datei erfolgreich kopiert nach: {target_file_path}")
                    print(f"✅ SAP-Export erfolgreich: {export_filename}")
                    print(f"   - Kopiert nach: {target_file_path}")
                    
                    return True
                except Exception as copy_error:
                    main_logger.error(f"Fehler beim Kopieren der Datei: {copy_error}")
                    print(f"❌ Fehler beim Kopieren der SAP-Datei: {copy_error}")
                    
                    # Fallback: Verwende die Original-Export-Datei direkt
                    if os.path.exists(exported_file):
                        main_logger.info(f"Fallback: Verwende Original-Export-Datei: {exported_file}")
                        print(f"⚠️ Fallback: Verwende Original-Export-Datei")
                        # Aktualisiere SAP_EXCEL_PATH für nachfolgende Schritte
                        SAP_EXCEL_PATH = exported_file
                        return True
                    return False
            else:
                main_logger.error("SAP-Export fehlgeschlagen!")
                print("❌ SAP-Export fehlgeschlagen!")
                return False
        else:
            main_logger.error("SAP-Sitzung konnte nicht hergestellt werden!")
            print("❌ SAP-Sitzung konnte nicht hergestellt werden!")
            return False
            
    except Exception as e:
        print(f"❌ Kritischer Fehler im SAP-Prozess: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    # Initialisiere Dual-Logging (Konsole + Datei)
    dual_logger = DualLogger()
    sys.stdout = dual_logger
    
    try:
        print("🎯 VOLLSTÄNDIGER WORKFLOW: SAP-EXPORT + SERVICEGRAD-BERECHNUNG + E-MAIL + DATENBANK")
        print("=" * 80)
        print(f"📄 Log-Datei: {dual_logger.log_file_path}")
        print("")
        
        print("\n🚀 SCHRITT 0: SAP-Export ausführen")
        if not run_sap_export_process():
            # Fallback: Prüfe, ob SAP-Dateien im Verzeichnis existieren
            sap_files_found = False
            if os.path.isdir(SAP_EXCEL_PATH):
                excel_files = [f for f in os.listdir(SAP_EXCEL_PATH) if f.endswith('.xlsx') and f.startswith('SG-')]
                if excel_files:
                    sap_files_found = True
                    print(f"⚠️ SAP-Export fehlgeschlagen, aber {len(excel_files)} SAP-Datei(en) im Verzeichnis gefunden")
                    print(f"   - Verzeichnis: {SAP_EXCEL_PATH}")
                    print("   - Verwende vorhandene SAP-Datei für die Berechnung")
            elif os.path.isfile(SAP_EXCEL_PATH):
                sap_files_found = True
                print(f"⚠️ SAP-Export fehlgeschlagen, aber vorhandene Datei gefunden: {SAP_EXCEL_PATH}")
                print("   - Verwende vorhandene SAP-Datei für die Berechnung")
            
            if not sap_files_found:
                print("❌ Workflow abgebrochen bei SAP-Export - keine SAP-Datei verfügbar.")
                return
        
        print("\n🔧 SCHRITT 1: Datenkopie")
        if not safe_copy_sap_data():
            print("❌ Workflow abgebrochen bei Datenkopie.")
            return

        print("\n📊 SCHRITT 2: Zusammenfassung erstellen (nur Daten)")
        if not create_summary_correctly():
            print("❌ Workflow abgebrochen bei Zusammenfassung.")
            return

        print("\n📊 SCHRITT 3: Datenübertragung zu Kennzahlen")
        safe_transfer_to_kennzahlen()

        print("\n🎨 SCHRITT 4: Finale Formatierung & Styling")
        if not final_format_and_style(MAIN_EXCEL_PATH):
            print("⚠️ WORKFLOW WIRD FORTGESETZT, aber die finale Formatierung ist fehlgeschlagen.")

        print("\n📧 SCHRITT 5: E-Mail-Bericht erstellen")
        send_report_by_email(MAIN_EXCEL_PATH)
        
        print("\n💾 SCHRITT 6: Servicegrad-Daten in Datenbank speichern")
        save_servicegrad_to_database(MAIN_EXCEL_PATH)
        
        # ======================= NEUER AUFRÄUMSCHRITT =======================
        print("\n🧹 SCHRITT 7: Arbeitsmappe für nächsten Lauf vorbereiten")
        cleanup_main_excel(MAIN_EXCEL_PATH)
        # ====================================================================

        print("\n🎉 *** WORKFLOW ERFOLGREICH ABGESCHLOSSEN! ***")
        
    except Exception as e:
        print(f"\n❌ KRITISCHER FEHLER: {e}")
        import traceback
        traceback.print_exc()
        print(f"\n📄 Vollständiges Log siehe: {dual_logger.log_file_path}")
    finally:
        # Stelle stdout wieder her und schließe Logger
        sys.stdout = dual_logger.original_stdout
        dual_logger.close()
        print(f"\n📄 Log gespeichert in: {dual_logger.log_file_path}")


if __name__ == "__main__":
    try:
        import xlwings as xw
        main()
    except ImportError:
        print("FEHLER: Wichtige Bibliotheken nicht installiert! Bitte installieren mit:")
        print("pip install xlwings pandas pywin32")


# === MAIN EXECUTION FOR DASHBOARD INTEGRATION ===
def main_workflow():
    """Hauptfunktion für die Servicegrad-Automatisierung (Dashboard Integration)"""
    logger = WorkflowLogger("servicegrad")
    dual_logger = None
    
    try:
        logger.log_process_start("Servicegrad Automatisierung")
        
        # Aktiviere Dual Logging
        dual_logger = DualLogger()
        sys.stdout = dual_logger
        
        print("=== SERVICEGRAD AUTOMATISIERUNG GESTARTET ===")
        print(f"Zeitstempel: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 1. SAP-Verbindung herstellen
        print("\n1. SAP-Verbindung herstellen...")
        session = get_sap_session(logger)
        
        # 2. Servicegrad-Prozess ausführen
        print("\n2. Servicegrad-Prozess ausführen...")
        export_path = run_servicegrad_process(session, SCRIPT_DATE, logger)
        
        if export_path:
            logger.info(f"SAP-Export erfolgreich: {export_path}")
            print(f"✅ SAP-Export erfolgreich: {export_path}")
            
            # 3. Excel-Verarbeitung
            print("\n3. Excel-Datenverarbeitung...")
            
            # 3a. Daten kopieren
            if safe_copy_sap_data():
                logger.info("Datenkopie erfolgreich")
                print("✅ Datenkopie erfolgreich")
                
                # 3b. Zusammenfassung erstellen
                if create_summary_correctly():
                    logger.info("Zusammenfassung erstellt")
                    print("✅ Zusammenfassung erstellt")
                    
                    # 3c. Daten zu Kennzahlen übertragen
                    if safe_transfer_to_kennzahlen():
                        logger.info("Datenübertragung zu Kennzahlen erfolgreich")
                        print("✅ Datenübertragung zu Kennzahlen erfolgreich")
                        
                        # 3d. Finale Formatierung
                        if final_format_and_style(MAIN_EXCEL_PATH):
                            logger.info("Finale Formatierung erfolgreich")
                            print("✅ Finale Formatierung erfolgreich")
                        else:
                            logger.warning("Finale Formatierung fehlgeschlagen")
                            print("⚠️ Finale Formatierung fehlgeschlagen")
                            
                        # 3e. E-Mail versenden
                        print("\n4. E-Mail-Bericht erstellen und versenden...")
                        if send_report_by_email(MAIN_EXCEL_PATH):
                            logger.info("E-Mail-Bericht erfolgreich versendet")
                            print("✅ E-Mail-Bericht erfolgreich versendet")
                        else:
                            logger.warning("E-Mail-Versand fehlgeschlagen")
                            print("⚠️ E-Mail-Versand fehlgeschlagen")
                            
                        # 3f. Datenbank-Speicherung
                        print("\n5. Servicegrad-Daten in Datenbank speichern...")
                        if save_servicegrad_to_database(MAIN_EXCEL_PATH):
                            logger.info("Datenbank-Speicherung erfolgreich")
                            print("✅ Datenbank-Speicherung erfolgreich")
                        else:
                            logger.warning("Datenbank-Speicherung fehlgeschlagen")
                            print("⚠️ Datenbank-Speicherung fehlgeschlagen")
                            
                        # 3g. Aufräumen
                        print("\n6. Arbeitsmappe aufräumen...")
                        if cleanup_main_excel(MAIN_EXCEL_PATH):
                            logger.info("Arbeitsmappe erfolgreich aufgeräumt")
                            print("✅ Arbeitsmappe erfolgreich aufgeräumt")
                        else:
                            logger.warning("Arbeitsmappe-Aufräumen fehlgeschlagen")
                            print("⚠️ Arbeitsmappe-Aufräumen fehlgeschlagen")
                            
                    else:
                        logger.error("Datenübertragung zu Kennzahlen fehlgeschlagen")
                        print("❌ Datenübertragung zu Kennzahlen fehlgeschlagen")
                else:
                    logger.error("Zusammenfassung konnte nicht erstellt werden")
                    print("❌ Zusammenfassung konnte nicht erstellt werden")
            else:
                logger.error("Datenkopie fehlgeschlagen")
                print("❌ Datenkopie fehlgeschlagen")
        else:
            logger.error("SAP-Export fehlgeschlagen")
            print("❌ SAP-Export fehlgeschlagen")
            return False
        
        # 7. SAP und Excel schließen
        print("\n7. Aufräumen...")
        close_excel_sap(logger)
        
        logger.log_process_complete("Servicegrad Automatisierung", export_path)
        print("\n✅ SERVICEGRAD AUTOMATISIERUNG ERFOLGREICH ABGESCHLOSSEN")
        return True
        
    except Exception as e:
        error_msg = f"Unerwarteter Fehler: {str(e)}"
        logger.log_process_error("Servicegrad Automatisierung", error_msg)
        print(f"\n❌ FEHLER: {error_msg}")
        
        # Aufräumen bei Fehler
        try:
            close_excel_sap(logger)
        except:
            pass
            
        return False
        
    finally:
        # Dual Logging beenden
        if dual_logger:
            sys.stdout = dual_logger.original_stdout
            dual_logger.close()


if __name__ == "__main__":
    # Verwende die vollständige main() Funktion für direkte Ausführung
    # oder main_workflow() für Dashboard-Integration
    try:
        import xlwings as xw
        # Prüfe, ob das Skript direkt ausgeführt wird oder über Dashboard
        if len(sys.argv) > 1 and sys.argv[1] == "--dashboard":
            success = main_workflow()
        else:
            success = main()  # Vollständiger Workflow
        sys.exit(0 if success else 1)
    except ImportError:
        print("❌ Wichtige Bibliotheken nicht installiert! Bitte installieren mit:")
        print("pip install xlwings pandas pywin32")
        sys.exit(1)