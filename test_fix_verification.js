/**
 * Verifikations-Test für die Verschnitt-Problem-Korrektur
 * 
 * Dieser Test simuliert die korrigierte firstFitOptimization Logik
 * und überprüft, ob die Verschnitt-Berechnung jetzt korrekt ist.
 */

// Simuliere die korrigierte Logik aus CuttingOptimizerService.ts
function simulateFixedFirstFitOptimization(drums, orders) {
  const drumAllocations = [];
  
  // Initialize drum allocations with corrected logic
  for (const drum of drums) {
    drumAllocations.push({
      drumId: drum.id,
      cuts: [],
      // KORRIGIERTE LOGIK: totalLength als Fallback hinzugefügt
      remainingLength: drum.availableLength || drum.totalLength || drum.remainingLength || 0,
      utilization: 0
    });
  }
  
  // Process orders (vereinfachte Simulation)
  for (const order of orders) {
    for (let i = 0; i < order.quantity; i++) {
      // Find first drum that can fit this cut
      const suitableDrum = drumAllocations.find(allocation => 
        allocation.remainingLength >= order.requiredLength
      );
      
      if (suitableDrum) {
        suitableDrum.cuts.push({
          orderId: order.id,
          length: order.requiredLength,
          cutNumber: i + 1
        });
        suitableDrum.remainingLength -= order.requiredLength;
        suitableDrum.utilization = ((suitableDrum.remainingLength === 0 ? 
          drums.find(d => d.id === suitableDrum.drumId).totalLength : 
          drums.find(d => d.id === suitableDrum.drumId).totalLength - suitableDrum.remainingLength
        ) / drums.find(d => d.id === suitableDrum.drumId).totalLength) * 100;
      }
    }
  }
  
  return drumAllocations;
}

// Test-Szenarien
const testScenarios = [
  {
    name: "Szenario 1: Normale Trommel mit availableLength",
    drums: [
      {
        id: "TROMMEL_001",
        cableType: "H07V-K 1,5",
        totalLength: 500,
        availableLength: 500,
        remainingLength: undefined
      }
    ],
    orders: [
      {
        id: "ORDER_001",
        cableType: "H07V-K 1,5",
        requiredLength: 200,
        quantity: 2
      }
    ],
    expectedWaste: 100 // 500 - (200 * 2) = 100
  },
  {
    name: "Szenario 2: Trommel mit availableLength = 0, aber totalLength gesetzt (Hauptproblem)",
    drums: [
      {
        id: "TROMMEL_002",
        cableType: "H07V-K 1,5",
        totalLength: 500,
        availableLength: 0, // Problem-Fall!
        remainingLength: undefined
      }
    ],
    orders: [
      {
        id: "ORDER_002",
        cableType: "H07V-K 1,5",
        requiredLength: 200,
        quantity: 2
      }
    ],
    expectedWaste: 100 // 500 - (200 * 2) = 100
  },
  {
    name: "Szenario 3: Trommel mit availableLength undefined, totalLength gesetzt",
    drums: [
      {
        id: "TROMMEL_003",
        cableType: "H07V-K 1,5",
        totalLength: 500,
        availableLength: undefined,
        remainingLength: 300 // Alter Wert
      }
    ],
    orders: [
      {
        id: "ORDER_003",
        cableType: "H07V-K 1,5",
        requiredLength: 200,
        quantity: 2
      }
    ],
    expectedWaste: 100 // 500 - (200 * 2) = 100 (sollte totalLength verwenden, nicht remainingLength)
  }
];

console.log("=== VERIFIKATION DER VERSCHNITT-KORREKTUR ===");
console.log("");

testScenarios.forEach((scenario, index) => {
  console.log(`=== ${scenario.name} ===`);
  console.log("Input:");
  console.log(`  Trommel: ${scenario.drums[0].id}`);
  console.log(`  totalLength: ${scenario.drums[0].totalLength}`);
  console.log(`  availableLength: ${scenario.drums[0].availableLength}`);
  console.log(`  remainingLength: ${scenario.drums[0].remainingLength}`);
  console.log(`  Bestellung: ${scenario.orders[0].requiredLength}m × ${scenario.orders[0].quantity} = ${scenario.orders[0].requiredLength * scenario.orders[0].quantity}m`);
  console.log(`  Erwarteter Verschnitt: ${scenario.expectedWaste}m`);
  
  const result = simulateFixedFirstFitOptimization(scenario.drums, scenario.orders);
  const allocation = result[0];
  
  console.log("");
  console.log("Ergebnis:");
  console.log(`  Initialisierte remainingLength: ${scenario.drums[0].availableLength || scenario.drums[0].totalLength || scenario.drums[0].remainingLength || 0}m`);
  console.log(`  Anzahl Schnitte: ${allocation.cuts.length}`);
  console.log(`  Verbleibende Länge: ${allocation.remainingLength}m`);
  console.log(`  Utilization: ${allocation.utilization.toFixed(1)}%`);
  
  const actualWaste = allocation.remainingLength;
  const success = actualWaste === scenario.expectedWaste;
  
  console.log(`  Tatsächlicher Verschnitt: ${actualWaste}m`);
  console.log(`  ${success ? '✅' : '❌'} ${success ? 'KORREKT' : 'FEHLER'} - Verschnitt ${success ? 'stimmt' : 'stimmt nicht'}`);
  
  if (!success) {
    console.log(`     Erwartet: ${scenario.expectedWaste}m`);
    console.log(`     Erhalten: ${actualWaste}m`);
    console.log(`     Differenz: ${Math.abs(actualWaste - scenario.expectedWaste)}m`);
  }
  
  console.log("");
});

console.log("=== ZUSAMMENFASSUNG ===");
console.log("Die Korrektur in CuttingOptimizerService.ts Zeile 527:");
console.log("VORHER: remainingLength: drum.availableLength || drum.remainingLength || 0,");
console.log("NACHHER: remainingLength: drum.availableLength || drum.totalLength || drum.remainingLength || 0,");
console.log("");
console.log("Diese Änderung stellt sicher, dass totalLength als Fallback verwendet wird,");
console.log("wenn availableLength nicht verfügbar ist. Dies löst das Problem mit");
console.log("falschen Verschnitt-Berechnungen bei Trommeln aus der Datenbank.");