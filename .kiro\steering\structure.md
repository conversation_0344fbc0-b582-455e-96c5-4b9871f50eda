---
inclusion: always
---

# Project Structure & Organization Rules

## Core Department Structure
This application is organized around **three departments** - all code must follow this pattern:
- **Dispatch** (`/dispatch`) - Versand/shipping operations
- **Cutting** (`/cutting`) - Ablängerei/cutting operations  
- **Incoming Goods** (`/incoming-goods`) - Wareneingang operations

## File Placement Rules

### When creating authentication components:
- **Auth components**: `src/components/auth/` (e.g., `LoginForm.tsx`, `ProtectedRoute.tsx`, `AuthGuard.tsx`)
- **Auth pages**: `src/pages/auth/` (e.g., `LoginPage.tsx`, `RegistrationPage.tsx`)
- **Auth services**: `src/services/auth.service.ts` (frontend authentication logic)
- **Auth types**: `src/types/auth.ts` (User, Role, AuthResponse interfaces)
- **Auth hooks**: `src/hooks/useAuth.ts` (authentication state management)

### When creating new components:
- **Department-specific pages**: `src/pages/{department}/` (e.g., `src/pages/dispatch/OrdersPage.tsx`)
- **Shared UI components**: `src/components/ui/` (shadcn/ui based)
- **Chart components**: `src/components/charts/` (Recharts only)
- **Department widgets**: `src/components/{department}/` (e.g., `src/components/dispatch/`)

### When creating new functionality:
- **API services**: `src/services/{department}Service.ts` (e.g., `dispatchService.ts`)
- **Data repositories**: `src/repositories/{department}Repository.ts`
- **Type definitions**: `src/types/{department}.ts` or `src/types/shared.ts`
- **Custom hooks**: `src/hooks/use{Department}{Feature}.ts` (e.g., `useDispatchKpis.ts`)

### When adding routes:
- **Department routes**: `src/routes/{department}/` with nested structure
- **Route files**: Follow TanStack Router conventions with `.tsx` extension
- **Layout components**: `src/layouts/{Department}Layout.tsx`

## Mandatory Naming Conventions

### Files & Components
- **React components**: PascalCase ending in component type (`OrdersPage.tsx`, `KpiChart.tsx`)
- **Services**: camelCase ending in `Service.ts` (`dispatchService.ts`)
- **Repositories**: camelCase ending in `Repository.ts` (`ordersRepository.ts`)
- **Hooks**: camelCase starting with `use` (`useDispatchData.ts`)
- **Types**: PascalCase interfaces (`DispatchKpi`, `OrderStatus`)
- **Utils**: camelCase descriptive names (`formatCurrency.ts`, `dateHelpers.ts`)

### Directories
- **Department folders**: kebab-case matching route names (`dispatch`, `cutting`, `incoming-goods`)
- **Component folders**: camelCase (`src/components/charts`, `src/components/ui`)
- **Feature folders**: camelCase (`src/services`, `src/repositories`)

## Import Path Requirements
- **Always use `@/` alias** for internal imports: `import { Button } from '@/components/ui/button'`
- **Never use relative imports** beyond same directory: `import './styles.css'` ✓, `import '../utils/helper'` ✗
- **Group imports**: external libraries → `@/` imports → types → relative imports

## Department Code Organization

### Each department must have:
1. **Page component**: `src/pages/{department}/index.tsx` (main dashboard)
2. **Service layer**: `src/services/{department}Service.ts` (API calls)
3. **Repository**: `src/repositories/{department}Repository.ts` (data access)
4. **Types**: `src/types/{department}.ts` (TypeScript definitions)
5. **Routes**: `src/routes/{department}/` (TanStack Router config)

### Shared resources location:
- **Common types**: `src/types/shared.ts`
- **Shared utilities**: `src/utils/`
- **Base components**: `src/components/ui/`
- **Chart components**: `src/components/charts/`
- **Global hooks**: `src/hooks/` (prefix with `useShared` if truly global)

## Backend Structure Rules
- **Authentication**: `backend/src/services/auth.service.ts`, `backend/src/repositories/user.repository.ts`
- **Auth Controllers**: `backend/src/controllers/authController.ts`
- **Auth Routes**: `backend/src/routes/authRoutes.ts`
- **Auth Middleware**: `backend/src/middleware/auth.middleware.ts`
- **Controllers**: `backend/src/controllers/{department}Controller.ts`
- **Routes**: `backend/src/routes/{department}Routes.ts`
- **Services**: `backend/src/services/{department}Service.ts`
- **Models**: Use Prisma schema in `backend/prisma/schema.prisma`

## Database Schema Organization
- **User Management**: `User`, `Role` models with proper relationships
- **Department Data**: Existing models (ARiL, ATrL, Ablaengerei, etc.)
- **System Data**: SystemStatus, Stoerungen models
- **Migration Strategy**: Additive migrations only, never modify existing tables

## Critical Path Mapping
- Frontend: `src/` → Backend: `backend/src/`
- Database: `backend/prisma/` (schema) and `backend/database/` (utilities)
- Assets: `src/assets/` (bundled) and `assets/` (Electron app icons)
- Config: Root level (`*.config.ts`, `package.json`)
- Documentation: `Instructions/` and `docs/`