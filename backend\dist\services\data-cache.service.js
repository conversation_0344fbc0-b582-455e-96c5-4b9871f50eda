"use strict";
/**
 * Data Cache Service
 *
 * Implements intelligent caching for frequently requested data combinations
 * to improve performance of the AI chatbot database integration.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dataCache = exports.DataCacheService = void 0;
const events_1 = require("events");
const performance_monitoring_service_1 = __importDefault(require("./performance-monitoring.service"));
const DEFAULT_CONFIG = {
    maxSize: 100 * 1024 * 1024, // 100MB
    maxEntries: 1000,
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    cleanupInterval: 60 * 1000, // 1 minute
    enableMetrics: true
};
class DataCacheService extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.cache = new Map();
        this.stats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            totalAccessTime: 0,
            accessCount: 0
        };
        this.config = { ...DEFAULT_CONFIG, ...config };
        this.startCleanupTimer();
    }
    /**
     * Generate cache key for query intents
     */
    generateQueryKey(intents, additionalParams = {}) {
        const intentSignature = intents
            .map(intent => {
            var _a;
            return ({
                type: intent.type,
                timeRange: intent.timeRange,
                specificMetrics: (_a = intent.specificMetrics) === null || _a === void 0 ? void 0 : _a.sort()
            });
        })
            .sort((a, b) => a.type.localeCompare(b.type));
        const keyData = {
            intents: intentSignature,
            params: additionalParams
        };
        return `query_${this.hashObject(keyData)}`;
    }
    /**
     * Generate cache key for enriched context
     */
    generateContextKey(message, intents) {
        const normalizedMessage = message.toLowerCase().trim();
        const intentTypes = intents.map(i => i.type).sort();
        const timeRanges = intents.map(i => i.timeRange).filter(Boolean);
        const keyData = {
            messageHash: this.hashString(normalizedMessage),
            intentTypes,
            timeRanges
        };
        return `context_${this.hashObject(keyData)}`;
    }
    /**
     * Get cached data
     */
    async get(key) {
        const startTime = Date.now();
        try {
            const entry = this.cache.get(key);
            if (!entry) {
                this.stats.misses++;
                this.recordAccessTime(Date.now() - startTime);
                if (this.config.enableMetrics) {
                    performance_monitoring_service_1.default.recordQueryPerformance('combined', Date.now() - startTime, false, {
                        cacheHit: false
                    });
                }
                return null;
            }
            // Check if entry has expired
            if (this.isExpired(entry)) {
                this.cache.delete(key);
                this.stats.misses++;
                this.recordAccessTime(Date.now() - startTime);
                if (this.config.enableMetrics) {
                    performance_monitoring_service_1.default.recordQueryPerformance('combined', Date.now() - startTime, false, {
                        cacheHit: false
                    });
                }
                return null;
            }
            // Update access statistics
            entry.accessCount++;
            entry.lastAccessed = new Date();
            this.stats.hits++;
            this.recordAccessTime(Date.now() - startTime);
            if (this.config.enableMetrics) {
                performance_monitoring_service_1.default.recordQueryPerformance('combined', Date.now() - startTime, true, {
                    cacheHit: true,
                    dataSize: entry.size
                });
            }
            this.emit('hit', { key, entry });
            return entry.data;
        }
        catch (error) {
            console.error(`[CACHE] Error retrieving key ${key}:`, error);
            this.stats.misses++;
            this.recordAccessTime(Date.now() - startTime);
            return null;
        }
    }
    /**
     * Set cached data
     */
    async set(key, data, options = {}) {
        try {
            const ttl = options.ttl || this.config.defaultTTL;
            const tags = options.tags || [];
            const size = this.estimateSize(data);
            // Check if we need to make space
            if (this.shouldEvict(size)) {
                this.evictEntries(size);
            }
            const entry = {
                key,
                data,
                timestamp: new Date(),
                ttl,
                accessCount: 0,
                lastAccessed: new Date(),
                size,
                tags
            };
            this.cache.set(key, entry);
            this.emit('set', { key, entry });
            return true;
        }
        catch (error) {
            console.error(`[CACHE] Error setting key ${key}:`, error);
            return false;
        }
    }
    /**
     * Cache query results with intelligent key generation
     */
    async cacheQueryResults(intents, results, additionalParams = {}) {
        const key = this.generateQueryKey(intents, additionalParams);
        // Determine TTL based on data type and freshness requirements
        let ttl = this.config.defaultTTL;
        // Störungen data changes more frequently
        if (intents.some(i => i.type === 'stoerungen')) {
            ttl = Math.min(ttl, 2 * 60 * 1000); // 2 minutes max
        }
        // Time-specific queries should have shorter TTL
        if (intents.some(i => i.timeRange)) {
            ttl = Math.min(ttl, 3 * 60 * 1000); // 3 minutes max
        }
        const tags = [
            ...intents.map(i => `intent:${i.type}`),
            ...results.map(r => `dataType:${r.dataType}`),
            'queryResults'
        ];
        await this.set(key, results, { ttl, tags });
        return key;
    }
    /**
     * Cache enriched context
     */
    async cacheEnrichedContext(message, intents, context, ttl) {
        const key = this.generateContextKey(message, intents);
        const tags = [
            ...intents.map(i => `intent:${i.type}`),
            'enrichedContext'
        ];
        await this.set(key, context, { ttl, tags });
        return key;
    }
    /**
     * Get cached query results
     */
    async getCachedQueryResults(intents, additionalParams = {}) {
        const key = this.generateQueryKey(intents, additionalParams);
        return this.get(key);
    }
    /**
     * Get cached enriched context
     */
    async getCachedEnrichedContext(message, intents) {
        const key = this.generateContextKey(message, intents);
        return this.get(key);
    }
    /**
     * Invalidate cache entries by tags
     */
    invalidateByTags(tags) {
        let invalidatedCount = 0;
        for (const [key, entry] of this.cache.entries()) {
            if (entry.tags.some(tag => tags.includes(tag))) {
                this.cache.delete(key);
                invalidatedCount++;
            }
        }
        this.emit('invalidate', { tags, count: invalidatedCount });
        return invalidatedCount;
    }
    /**
     * Invalidate cache entries by data type
     */
    invalidateByDataType(dataType) {
        return this.invalidateByTags([`dataType:${dataType}`, `intent:${dataType}`]);
    }
    /**
     * Clear all cache entries
     */
    clear() {
        const count = this.cache.size;
        this.cache.clear();
        this.emit('clear', { count });
    }
    /**
     * Get cache statistics
     */
    getStats() {
        const totalRequests = this.stats.hits + this.stats.misses;
        const hitRate = totalRequests > 0 ? this.stats.hits / totalRequests : 0;
        const missRate = totalRequests > 0 ? this.stats.misses / totalRequests : 0;
        const totalSize = Array.from(this.cache.values())
            .reduce((sum, entry) => sum + entry.size, 0);
        const averageAccessTime = this.stats.accessCount > 0
            ? this.stats.totalAccessTime / this.stats.accessCount
            : 0;
        // Calculate top keys by access count
        const topKeys = Array.from(this.cache.entries())
            .map(([key, entry]) => ({
            key,
            accessCount: entry.accessCount,
            hitRate: entry.accessCount > 0 ? 1 : 0 // Simplified hit rate per key
        }))
            .sort((a, b) => b.accessCount - a.accessCount)
            .slice(0, 10);
        return {
            totalEntries: this.cache.size,
            totalSize,
            hitRate,
            missRate,
            evictionCount: this.stats.evictions,
            averageAccessTime,
            topKeys
        };
    }
    /**
     * Get frequently requested data patterns
     */
    getFrequentPatterns() {
        const patterns = new Map();
        // Analyze cache entries to identify patterns
        for (const [key, entry] of this.cache.entries()) {
            let pattern = 'unknown';
            if (key.startsWith('query_')) {
                if (entry.tags.includes('intent:stoerungen'))
                    pattern = 'störungen_queries';
                else if (entry.tags.includes('intent:dispatch'))
                    pattern = 'dispatch_queries';
                else if (entry.tags.includes('intent:cutting'))
                    pattern = 'cutting_queries';
                else
                    pattern = 'mixed_queries';
            }
            else if (key.startsWith('context_')) {
                pattern = 'context_enrichment';
            }
            if (!patterns.has(pattern)) {
                patterns.set(pattern, { count: 0, totalTime: 0, cacheHits: 0 });
            }
            const patternData = patterns.get(pattern);
            patternData.count++;
            patternData.cacheHits += entry.accessCount;
        }
        return Array.from(patterns.entries()).map(([pattern, data]) => ({
            pattern,
            frequency: data.count,
            averageResponseTime: data.count > 0 ? data.totalTime / data.count : 0,
            cacheEffectiveness: data.count > 0 ? data.cacheHits / data.count : 0
        }));
    }
    /**
     * Optimize cache based on usage patterns
     */
    optimizeCache() {
        const optimizations = [];
        let evicted = 0;
        // Remove rarely accessed entries
        const cutoffTime = new Date(Date.now() - 30 * 60 * 1000); // 30 minutes ago
        const lowAccessThreshold = 2;
        for (const [key, entry] of this.cache.entries()) {
            if (entry.lastAccessed < cutoffTime && entry.accessCount < lowAccessThreshold) {
                this.cache.delete(key);
                evicted++;
            }
        }
        if (evicted > 0) {
            optimizations.push(`Removed ${evicted} rarely accessed entries`);
        }
        // Adjust TTL for frequently accessed items
        let ttlAdjustments = 0;
        for (const [key, entry] of this.cache.entries()) {
            if (entry.accessCount > 10) {
                // Extend TTL for frequently accessed items
                entry.ttl = Math.min(entry.ttl * 1.5, 15 * 60 * 1000); // Max 15 minutes
                ttlAdjustments++;
            }
        }
        if (ttlAdjustments > 0) {
            optimizations.push(`Extended TTL for ${ttlAdjustments} frequently accessed entries`);
        }
        this.emit('optimize', { evicted, optimizations });
        return { evicted, optimizations };
    }
    // Private methods
    isExpired(entry) {
        return Date.now() - entry.timestamp.getTime() > entry.ttl;
    }
    shouldEvict(newEntrySize) {
        const currentSize = Array.from(this.cache.values())
            .reduce((sum, entry) => sum + entry.size, 0);
        return (this.cache.size >= this.config.maxEntries ||
            currentSize + newEntrySize > this.config.maxSize);
    }
    evictEntries(requiredSpace) {
        // LRU eviction strategy with size consideration
        const entries = Array.from(this.cache.entries())
            .sort((a, b) => a[1].lastAccessed.getTime() - b[1].lastAccessed.getTime());
        let freedSpace = 0;
        let evictedCount = 0;
        for (const [key, entry] of entries) {
            if (freedSpace >= requiredSpace && evictedCount >= 10) {
                break; // Don't evict too many at once
            }
            this.cache.delete(key);
            freedSpace += entry.size;
            evictedCount++;
            this.stats.evictions++;
        }
        this.emit('evict', { count: evictedCount, freedSpace });
    }
    estimateSize(data) {
        try {
            return JSON.stringify(data).length * 2; // Rough estimate (UTF-16)
        }
        catch (_a) {
            return 1024; // Default size if estimation fails
        }
    }
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(36);
    }
    hashObject(obj) {
        return this.hashString(JSON.stringify(obj));
    }
    recordAccessTime(time) {
        this.stats.totalAccessTime += time;
        this.stats.accessCount++;
    }
    startCleanupTimer() {
        this.cleanupTimer = setInterval(() => {
            this.cleanup();
        }, this.config.cleanupInterval);
    }
    cleanup() {
        let cleanedCount = 0;
        for (const [key, entry] of this.cache.entries()) {
            if (this.isExpired(entry)) {
                this.cache.delete(key);
                cleanedCount++;
            }
        }
        if (cleanedCount > 0) {
            this.emit('cleanup', { count: cleanedCount });
        }
    }
    /**
     * Destroy the cache service
     */
    destroy() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
        }
        this.clear();
        this.removeAllListeners();
    }
}
exports.DataCacheService = DataCacheService;
// Singleton instance
exports.dataCache = new DataCacheService();
exports.default = exports.dataCache;
