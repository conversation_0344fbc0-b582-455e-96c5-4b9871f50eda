/**
 * Cutting optimization types and interfaces
 */

export interface CuttingOrder {
  id: string;
  requiredLength: number;
  quantity: number;
  priority: 'high' | 'medium' | 'low';
  material?: string;
  tolerance?: number;
}

export interface AvailableDrum {
  id: string;
  availableLength?: number;
  remainingLength?: number;
  material?: string;
  diameter?: number;
}

export interface CuttingRequest {
  orders: CuttingOrder[];
  availableDrums: AvailableDrum[];
  constraints?: CuttingConstraints;
}

export interface CuttingConstraints {
  maxCutsPerDrum?: number;
  maxWasteLength?: number;
  allowPartialFulfillment?: boolean;
  prioritizeHighPriority?: boolean;
}

export interface Cut {
  orderId: string;
  length: number;
  startPosition: number;
  endPosition: number;
  priority?: 'high' | 'medium' | 'low';
}

export interface DrumAllocation {
  drumId: string;
  cuts: Cut[];
  remainingLength: number;
  utilization: number;
}

export interface CuttingStep {
  stepNumber: number;
  drumId: string;
  cuts: Cut[];
  estimatedTime: number;
}

export interface CuttingPlan {
  drumAllocations: DrumAllocation[];
  cuttingSequence: CuttingStep[];
  totalWaste: number;
  efficiency: number;
  estimatedTime: number;
}

export interface CuttingAlternative {
  id: string;
  plan: CuttingPlan;
  score: number;
  advantages: string[];
  disadvantages: string[];
}

/**
 * Extended interfaces for manual delivery tracking and drum comparison
 */
export interface ManualDrumSelection {
  chargeNumber: string; // Eindeutige Charge-Nummer der Trommel
  material: string; // Material-Code
  lagereinheitentyp: string; // Trommelgröße (z.B. L040, L090)
  gesamtbestand: number; // Verfügbare Kabellänge in Metern
  selectionReason?: string; // Grund für die Auswahl
  aufnahmedatum?: string; // Aufnahmedatum der Trommel (optional)
}

export interface DeliveryTracking {
  deliveryId: string;
  selectionDate: Date; // Datum der Trommelauswahl
  selectedDrums: ManualDrumSelection[]; // Tatsächlich ausgewählte Trommeln
  aiSuggestedDrums?: AvailableDrum[]; // Von der KI vorgeschlagene Trommeln
  notes?: string; // Zusätzliche Notizen
}

export interface DrumComparisonResult {
  deliveryId: string;
  comparisonDate: Date;
  aiSuggestions: AvailableDrum[];
  manualSelections: ManualDrumSelection[];
  matches: DrumMatch[]; // Übereinstimmungen zwischen KI und manueller Auswahl
  differences: DrumDifference[]; // Unterschiede
  qualityScore: number; // Qualitätsbewertung der KI-Auswahl (0-100)
  efficiency: {
    aiEfficiency: number;
    manualEfficiency: number;
    difference: number;
  };
  waste: {
    aiWaste: number;
    manualWaste: number;
    difference: number;
  };
}

export interface DrumMatch {
  aiDrumId: string;
  manualChargeNumber: string;
  matchType: 'exact' | 'similar' | 'alternative';
  confidence: number; // Vertrauenswert der Übereinstimmung (0-1)
}

export interface DrumDifference {
  type: 'missing_ai_suggestion' | 'missing_manual_selection' | 'different_material' | 'different_size';
  description: string;
  impact: 'low' | 'medium' | 'high';
  aiDrum?: AvailableDrum;
  manualDrum?: ManualDrumSelection;
}