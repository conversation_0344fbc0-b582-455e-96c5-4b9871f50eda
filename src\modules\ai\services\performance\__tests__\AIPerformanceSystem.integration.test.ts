import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { AICacheService } from '../caching/AICacheService';
import { AIPerformanceMonitor } from '../performance/AIPerformanceMonitor';
import { ResourceTracker } from '../performance/ResourceTracker';
import { AIPerformanceOptimizer } from '../performance/AIPerformanceOptimizer';
import { MonitorPerformance, Cacheable, setPerformanceMonitor, setCacheService } from '../decorators/performance';
import { CacheService } from '@/services/cache.service';

// Mock dependencies
vi.mock('@/services/cache.service');

describe('AI Performance System Integration', () => {
  let cacheService: AICacheService;
  let performanceMonitor: AIPerformanceMonitor;
  let resourceTracker: ResourceTracker;
  let performanceOptimizer: AIPerformanceOptimizer;
  let mockCacheService: vi.Mocked<CacheService>;

  beforeEach(() => {
    mockCacheService = {
      get: vi.fn(),
      set: vi.fn(),
      delete: vi.fn(),
      clear: vi.fn(),
      has: vi.fn(),
      keys: vi.fn(),
      size: vi.fn()
    } as any;

    cacheService = new AICacheService(mockCacheService);
    performanceMonitor = new AIPerformanceMonitor();
    resourceTracker = new ResourceTracker();
    performanceOptimizer = new AIPerformanceOptimizer(
      performanceMonitor,
      resourceTracker,
      cacheService
    );

    // Set up decorators
    setPerformanceMonitor(performanceMonitor);
    setCacheService(cacheService);

    vi.useFakeTimers();
  });

  afterEach(() => {
    resourceTracker.stopCollection();
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  describe('End-to-End Performance Optimization', () => {
    class TestAIService {
      @MonitorPerformance('embedding-generation')
      @Cacheable({ ttl: 300000, tags: ['embeddings'] })
      async generateEmbedding(text: string): Promise<number[]> {
        // Simulate expensive embedding generation
        await new Promise(resolve => setTimeout(resolve, 100));
        return Array.from({ length: 1536 }, () => Math.random());
      }

      @MonitorPerformance('vector-search')
      async searchVectors(query: number[], limit: number = 10): Promise<any[]> {
        // Simulate vector search
        await new Promise(resolve => setTimeout(resolve, 50));
        return Array.from({ length: limit }, (_, i) => ({
          id: `result-${i}`,
          similarity: Math.random(),
          content: `Content ${i}`
        }));
      }

      @MonitorPerformance('rag-query')
      async processRAGQuery(query: string): Promise<string> {
        // Simulate RAG processing
        const embedding = await this.generateEmbedding(query);
        const results = await this.searchVectors(embedding, 5);
        
        // Simulate LLM processing
        await new Promise(resolve => setTimeout(resolve, 200));
        
        return `Processed query: ${query} with ${results.length} context items`;
      }
    }

    it('should demonstrate complete performance optimization workflow', async () => {
      const aiService = new TestAIService();

      // 1. Initial operations to establish baseline
      console.log('Phase 1: Establishing baseline performance...');
      
      const queries = [
        'What is machine learning?',
        'How does neural network work?',
        'Explain deep learning concepts',
        'What is machine learning?', // Duplicate for cache testing
        'Define artificial intelligence'
      ];

      for (const query of queries) {
        await aiService.processRAGQuery(query);
        vi.advanceTimersByTime(100);
      }

      // 2. Check initial performance metrics
      const initialStats = performanceMonitor.getAllOperationStats();
      expect(initialStats.length).toBeGreaterThan(0);

      const ragStats = performanceMonitor.getOperationStats('rag-query');
      const embeddingStats = performanceMonitor.getOperationStats('embedding-generation');
      const searchStats = performanceMonitor.getOperationStats('vector-search');

      console.log('Initial Performance Stats:');
      console.log(`RAG Query - Calls: ${ragStats.totalCalls}, Avg Duration: ${ragStats.averageDuration}ms`);
      console.log(`Embedding - Calls: ${embeddingStats.totalCalls}, Avg Duration: ${embeddingStats.averageDuration}ms`);
      console.log(`Vector Search - Calls: ${searchStats.totalCalls}, Avg Duration: ${searchStats.averageDuration}ms`);

      // 3. Verify caching is working
      const cacheStats = cacheService.getStats();
      expect(cacheStats.totalEntries).toBeGreaterThan(0);
      expect(cacheStats.hitRate).toBeGreaterThan(0); // Should have cache hits from duplicate query

      console.log(`Cache Stats - Entries: ${cacheStats.totalEntries}, Hit Rate: ${(cacheStats.hitRate * 100).toFixed(1)}%`);

      // 4. Analyze performance and get recommendations
      console.log('Phase 2: Analyzing performance and generating recommendations...');
      
      const analysis = await performanceOptimizer.analyzePerformance();
      expect(analysis.recommendations).toBeDefined();
      expect(analysis.currentMetrics).toBeDefined();

      console.log(`Generated ${analysis.recommendations.length} optimization recommendations`);

      // 5. Apply optimizations
      console.log('Phase 3: Applying optimizations...');
      
      const strategies = performanceOptimizer.getStrategies();
      const cacheStrategy = strategies.find(s => s.id === 'cache-optimization');
      
      if (cacheStrategy) {
        try {
          const optimizationResult = await performanceOptimizer.applyOptimization(cacheStrategy.id);
          expect(optimizationResult.applied).toBe(true);
          console.log('Cache optimization applied successfully');
        } catch (error) {
          console.log('Cache optimization failed (expected in test environment)');
        }
      }

      // 6. Run more operations to test optimized performance
      console.log('Phase 4: Testing optimized performance...');
      
      vi.advanceTimersByTime(60000); // Advance time for metrics stabilization
      
      for (let i = 0; i < 10; i++) {
        await aiService.processRAGQuery(`Optimized query ${i}`);
        vi.advanceTimersByTime(50);
      }

      // 7. Compare performance metrics
      const finalStats = performanceMonitor.getAllOperationStats();
      const finalRagStats = performanceMonitor.getOperationStats('rag-query');
      const finalCacheStats = cacheService.getStats();

      console.log('Final Performance Stats:');
      console.log(`RAG Query - Calls: ${finalRagStats.totalCalls}, Avg Duration: ${finalRagStats.averageDuration}ms`);
      console.log(`Cache Stats - Entries: ${finalCacheStats.totalEntries}, Hit Rate: ${(finalCacheStats.hitRate * 100).toFixed(1)}%`);

      // 8. Verify improvements
      expect(finalRagStats.totalCalls).toBeGreaterThan(ragStats.totalCalls);
      expect(finalCacheStats.hitRate).toBeGreaterThan(0);

      // 9. Check resource usage
      const resourceMetrics = resourceTracker.getCurrentMetrics();
      const resourceAlerts = resourceTracker.getResourceAlerts();
      
      console.log(`Resource Alerts: ${resourceAlerts.length}`);
      console.log(`Memory Usage: ${(resourceMetrics.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`);
      console.log(`CPU Usage: ${resourceMetrics.cpuUsage.percent.toFixed(1)}%`);

      // 10. Verify optimization history
      const optimizationHistory = performanceOptimizer.getOptimizationHistory();
      expect(optimizationHistory.length).toBeGreaterThan(0);

      console.log('Integration test completed successfully!');
    });

    it('should handle performance degradation and auto-optimization', async () => {
      const aiService = new TestAIService();

      // Simulate performance degradation by adding artificial delays
      class DegradedAIService extends TestAIService {
        @MonitorPerformance('slow-operation')
        async slowOperation(): Promise<string> {
          // Simulate degraded performance
          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
          return 'Slow operation completed';
        }
      }

      const degradedService = new DegradedAIService();

      // 1. Perform slow operations to trigger alerts
      console.log('Simulating performance degradation...');
      
      for (let i = 0; i < 5; i++) {
        await degradedService.slowOperation();
        vi.advanceTimersByTime(2100);
      }

      // 2. Check for performance alerts
      const alerts = performanceMonitor.getAlerts();
      const latencyAlert = alerts.find(a => a.type === 'high_latency');
      
      expect(latencyAlert).toBeDefined();
      console.log(`Generated ${alerts.length} performance alerts`);

      // 3. Trigger auto-optimization
      console.log('Triggering auto-optimization...');
      
      const autoOptimizationResults = await performanceOptimizer.runAutoOptimization();
      expect(Array.isArray(autoOptimizationResults)).toBe(true);
      
      console.log(`Applied ${autoOptimizationResults.length} automatic optimizations`);

      // 4. Verify optimization recommendations
      const recommendations = resourceTracker.getOptimizationRecommendations();
      expect(recommendations.length).toBeGreaterThan(0);
      
      const highPriorityRecs = recommendations.filter(r => r.priority === 'high' || r.priority === 'critical');
      expect(highPriorityRecs.length).toBeGreaterThan(0);

      console.log(`Generated ${recommendations.length} optimization recommendations`);
      console.log(`High priority recommendations: ${highPriorityRecs.length}`);
    });

    it('should demonstrate memory optimization under load', async () => {
      const aiService = new TestAIService();

      // 1. Generate high memory load
      console.log('Generating high memory load...');
      
      const largeDataOperations = [];
      for (let i = 0; i < 100; i++) {
        const largeText = 'x'.repeat(10000); // 10KB text
        largeDataOperations.push(aiService.generateEmbedding(largeText));
      }

      await Promise.all(largeDataOperations);
      vi.advanceTimersByTime(1000);

      // 2. Check memory usage
      const resourceMetrics = resourceTracker.getCurrentMetrics();
      const memoryUsageMB = resourceMetrics.memoryUsage.heapUsed / 1024 / 1024;
      
      console.log(`Memory usage after load: ${memoryUsageMB.toFixed(2)}MB`);

      // 3. Check for memory alerts
      const resourceAlerts = resourceTracker.getResourceAlerts();
      const memoryAlert = resourceAlerts.find(a => a.resource === 'memory');
      
      if (memoryAlert) {
        console.log(`Memory alert generated: ${memoryAlert.message}`);
      }

      // 4. Get memory optimization recommendations
      const recommendations = resourceTracker.getOptimizationRecommendations();
      const memoryRecs = recommendations.filter(r => r.type === 'memory');
      
      expect(memoryRecs.length).toBeGreaterThan(0);
      console.log(`Memory optimization recommendations: ${memoryRecs.length}`);

      // 5. Apply memory optimization
      const memoryStrategy = performanceOptimizer.getStrategies().find(s => s.id === 'memory-cleanup');
      if (memoryStrategy) {
        try {
          await performanceOptimizer.applyOptimization(memoryStrategy.id);
          console.log('Memory optimization applied');
        } catch (error) {
          console.log('Memory optimization failed (expected in test environment)');
        }
      }

      // 6. Verify cache management
      const cacheStats = cacheService.getStats();
      expect(cacheStats.totalEntries).toBeGreaterThan(0);
      
      // Clear cache to free memory
      await cacheService.clear();
      const clearedCacheStats = cacheService.getStats();
      expect(clearedCacheStats.totalEntries).toBe(0);
      
      console.log('Cache cleared to free memory');
    });

    it('should demonstrate real-time performance monitoring', async () => {
      const aiService = new TestAIService();

      // 1. Start continuous monitoring
      console.log('Starting real-time performance monitoring...');
      
      let monitoringActive = true;
      const monitoringResults: any[] = [];

      // Simulate real-time monitoring
      const monitoringInterval = setInterval(() => {
        if (!monitoringActive) return;

        const currentMetrics = resourceTracker.getCurrentMetrics();
        const performanceStats = performanceMonitor.getAllOperationStats(60000); // Last minute
        const alerts = performanceMonitor.getAlerts();

        monitoringResults.push({
          timestamp: Date.now(),
          metrics: currentMetrics,
          stats: performanceStats,
          alertCount: alerts.length
        });
      }, 1000);

      // 2. Perform various operations while monitoring
      const operations = [
        () => aiService.generateEmbedding('test embedding 1'),
        () => aiService.searchVectors([1, 2, 3]),
        () => aiService.processRAGQuery('What is AI monitoring?'),
        () => aiService.generateEmbedding('test embedding 2'),
        () => aiService.processRAGQuery('How does performance tracking work?')
      ];

      for (let i = 0; i < 20; i++) {
        const operation = operations[i % operations.length];
        await operation();
        vi.advanceTimersByTime(1100); // Advance time to trigger monitoring
      }

      // 3. Stop monitoring
      monitoringActive = false;
      clearInterval(monitoringInterval);

      // 4. Analyze monitoring results
      expect(monitoringResults.length).toBeGreaterThan(0);
      
      const avgAlertCount = monitoringResults.reduce((sum, r) => sum + r.alertCount, 0) / monitoringResults.length;
      const maxMemoryUsage = Math.max(...monitoringResults.map(r => r.metrics.memoryUsage.heapUsed));
      
      console.log(`Monitoring samples collected: ${monitoringResults.length}`);
      console.log(`Average alerts per sample: ${avgAlertCount.toFixed(2)}`);
      console.log(`Peak memory usage: ${(maxMemoryUsage / 1024 / 1024).toFixed(2)}MB`);

      // 5. Verify trend detection
      const trends = resourceTracker.getTrends(60000);
      expect(trends).toHaveProperty('memory');
      expect(trends).toHaveProperty('cpu');
      expect(trends).toHaveProperty('network');

      console.log(`Memory trend: ${trends.memory.trend} (rate: ${trends.memory.rate.toFixed(4)})`);
      console.log(`CPU trend: ${trends.cpu.trend} (rate: ${trends.cpu.rate.toFixed(4)})`);
    });
  });

  describe('Performance Decorator Integration', () => {
    it('should integrate decorators with monitoring system', async () => {
      class DecoratedService {
        @MonitorPerformance('decorated-operation')
        @Cacheable({ ttl: 60000 })
        async decoratedMethod(input: string): Promise<string> {
          await new Promise(resolve => setTimeout(resolve, 100));
          return `Processed: ${input}`;
        }
      }

      const service = new DecoratedService();

      // 1. Test performance monitoring decorator
      await service.decoratedMethod('test1');
      await service.decoratedMethod('test2');
      await service.decoratedMethod('test1'); // Should hit cache

      vi.advanceTimersByTime(200);

      // 2. Verify monitoring data
      const stats = performanceMonitor.getOperationStats('decorated-operation');
      expect(stats.totalCalls).toBe(3);
      expect(stats.averageDuration).toBeGreaterThan(0);

      // 3. Verify caching
      const cacheStats = cacheService.getStats();
      expect(cacheStats.hitRate).toBeGreaterThan(0);

      console.log(`Decorated method stats - Calls: ${stats.totalCalls}, Avg Duration: ${stats.averageDuration}ms`);
      console.log(`Cache hit rate: ${(cacheStats.hitRate * 100).toFixed(1)}%`);
    });
  });
});