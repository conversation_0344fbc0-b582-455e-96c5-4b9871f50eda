/**
 * Integration Tests for Inventory Intelligence Service with Predictive Analytics
 * 
 * Tests the integration of predictive analytics features with existing inventory repositories
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { InventoryIntelligenceService } from '../InventoryIntelligenceService';
import { WarehouseRepository } from '@/repositories/warehouse.repository';
import { ConsumptionData, InventoryItem } from '../types';

// Mock the warehouse repository
vi.mock('@/repositories/warehouse.repository');

describe('InventoryIntelligenceService - Predictive Analytics Integration', () => {
  let service: InventoryIntelligenceService;
  let mockWarehouseRepository: vi.Mocked<WarehouseRepository>;

  beforeEach(() => {
    mockWarehouseRepository = vi.mocked(new WarehouseRepository());
    
    service = new InventoryIntelligenceService({
      inventoryConfig: {
        abcAnalysis: {
          enabled: true,
          updateFrequency: 24,
          classAThreshold: 80,
          classBThreshold: 95,
          analysisWindow: 90
        },
        demandForecasting: {
          enabled: true,
          forecastHorizon: 30,
          updateFrequency: 12,
          minHistoryDays: 14,
          models: ['simple_moving_average', 'exponential_smoothing']
        },
        anomalyDetection: {
          enabled: true,
          sensitivity: 0.7,
          checkFrequency: 6,
          alertThreshold: 0.8
        },
        reorderOptimization: {
          enabled: true,
          safetyStockDays: 7,
          leadTimeDays: 14,
          serviceLevel: 0.95
        }
      }
    });

    // Mock warehouse repository methods
    mockWarehouseRepository.getOverallStats.mockResolvedValue({
      totalItems: 100,
      totalValue: 50000,
      lowStockItems: 5,
      lastUpdated: new Date()
    });
  });

  describe('Advanced Seasonality Detection', () => {
    it('should detect seasonal patterns with repository integration', async () => {
      await service.initialize();

      const result = await service.detectAdvancedSeasonality('test-item-001');

      expect(result).toBeDefined();
      expect(result.itemId).toBe('test-item-001');
      expect(result.seasonalityPattern).toBeDefined();
      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.seasonalIndices).toBeInstanceOf(Array);
    });

    it('should handle items with insufficient data', async () => {
      await service.initialize();

      const result = await service.detectAdvancedSeasonality('non-existent-item');

      expect(result.seasonalityPattern.hasSeasonality).toBe(false);
      expect(result.seasonalStrength).toBe(0);
      expect(result.confidence).toBe(0);
    });

    it('should cache seasonal analysis results', async () => {
      await service.initialize();

      const result1 = await service.detectAdvancedSeasonality('test-item-001');
      const result2 = await service.detectAdvancedSeasonality('test-item-001');

      expect(result1.seasonalityPattern.detectedAt).toEqual(result2.seasonalityPattern.detectedAt);
    });
  });

  describe('Reorder Point Optimization', () => {
    it('should optimize reorder points with different service levels', async () => {
      await service.initialize();

      const highServiceLevel = await service.optimizeReorderPoint('test-item-001', {
        targetServiceLevel: 0.99,
        leadTimeDays: 14
      });

      const lowServiceLevel = await service.optimizeReorderPoint('test-item-001', {
        targetServiceLevel: 0.85,
        leadTimeDays: 14
      });

      expect(highServiceLevel.optimizedReorderPoint).toBeGreaterThanOrEqual(lowServiceLevel.optimizedReorderPoint);
      expect(highServiceLevel.riskAssessment.serviceLevel).toBeGreaterThanOrEqual(lowServiceLevel.riskAssessment.serviceLevel);
    });

    it('should consider seasonality in optimization', async () => {
      await service.initialize();

      const withSeasonality = await service.optimizeReorderPoint('test-item-001', {
        considerSeasonality: true
      });

      const withoutSeasonality = await service.optimizeReorderPoint('test-item-001', {
        considerSeasonality: false
      });

      // Results should potentially be different
      expect(withSeasonality).toBeDefined();
      expect(withoutSeasonality).toBeDefined();
    });

    it('should provide comprehensive risk assessment', async () => {
      await service.initialize();

      const result = await service.optimizeReorderPoint('test-item-001');

      expect(result.riskAssessment).toBeDefined();
      expect(result.riskAssessment.stockoutProbability).toBeGreaterThanOrEqual(0);
      expect(result.riskAssessment.stockoutProbability).toBeLessThanOrEqual(1);
      expect(result.riskAssessment.overstockRisk).toBeGreaterThanOrEqual(0);
      expect(result.riskAssessment.overstockRisk).toBeLessThanOrEqual(1);
      expect(result.riskAssessment.serviceLevel).toBeGreaterThanOrEqual(0);
      expect(result.riskAssessment.serviceLevel).toBeLessThanOrEqual(1);
    });

    it('should generate actionable reasoning', async () => {
      await service.initialize();

      const result = await service.optimizeReorderPoint('test-item-001');

      expect(result.reasoning).toBeInstanceOf(Array);
      expect(result.reasoning.length).toBeGreaterThan(0);
      expect(result.reasoning.every(reason => typeof reason === 'string')).toBe(true);
    });
  });

  describe('Advanced Anomaly Detection', () => {
    it('should detect anomalies across multiple items', async () => {
      await service.initialize();

      const result = await service.detectAdvancedStockAnomalies({
        sensitivityLevel: 'medium',
        lookbackDays: 30
      });

      expect(result).toBeDefined();
      expect(result.anomalies).toBeInstanceOf(Array);
      expect(result.totalItemsAnalyzed).toBeGreaterThanOrEqual(0);
      expect(result.anomalyRate).toBeGreaterThanOrEqual(0);
      expect(result.severityDistribution).toBeDefined();
    });

    it('should respect sensitivity levels', async () => {
      await service.initialize();

      const highSensitivity = await service.detectAdvancedStockAnomalies({
        sensitivityLevel: 'high'
      });

      const lowSensitivity = await service.detectAdvancedStockAnomalies({
        sensitivityLevel: 'low'
      });

      // High sensitivity should potentially detect more anomalies
      expect(highSensitivity.anomalies.length).toBeGreaterThanOrEqual(lowSensitivity.anomalies.length);
    });

    it('should categorize anomalies by severity', async () => {
      await service.initialize();

      const result = await service.detectAdvancedStockAnomalies();

      if (result.anomalies.length > 0) {
        result.anomalies.forEach(anomaly => {
          expect(anomaly.severity).toMatch(/low|medium|high|critical/);
          expect(anomaly.anomalyType).toMatch(/sudden_spike|sudden_drop|unusual_pattern|stockout_risk|overstock/);
          expect(anomaly.recommendations).toBeInstanceOf(Array);
          expect(anomaly.confidence).toBeGreaterThanOrEqual(0);
          expect(anomaly.confidence).toBeLessThanOrEqual(1);
        });
      }
    });

    it('should integrate with existing anomaly detection', async () => {
      await service.initialize();

      const advancedResult = await service.detectAdvancedStockAnomalies();
      const standardResult = await service.detectStockAnomalies();

      // Both should return arrays of anomalies
      expect(advancedResult.anomalies).toBeInstanceOf(Array);
      expect(standardResult).toBeInstanceOf(Array);
    });
  });

  describe('Enhanced Consumption Pattern Analysis', () => {
    it('should provide comprehensive pattern analysis', async () => {
      await service.initialize();

      const result = await service.analyzeConsumptionPatterns('test-item-001');

      expect(result).toBeDefined();
      expect(result.itemId).toBe('test-item-001');
      expect(result.averageDailyConsumption).toBeGreaterThanOrEqual(0);
      expect(result.consumptionTrend).toMatch(/increasing|decreasing|stable/);
      expect(result.volatility).toBeGreaterThanOrEqual(0);
      expect(result.patterns).toBeInstanceOf(Array);
      expect(result.outliers).toBeInstanceOf(Array);
      expect(result.analysisDate).toBeInstanceOf(Date);
    });

    it('should identify different pattern types', async () => {
      await service.initialize();

      const result = await service.analyzeConsumptionPatterns('test-item-001');

      if (result.patterns.length > 0) {
        result.patterns.forEach(pattern => {
          expect(pattern.type).toMatch(/regular|irregular|seasonal|trending/);
          expect(pattern.description).toBeDefined();
          expect(pattern.strength).toBeGreaterThanOrEqual(0);
          expect(pattern.strength).toBeLessThanOrEqual(1);
        });
      }
    });

    it('should detect outliers with context', async () => {
      await service.initialize();

      const result = await service.analyzeConsumptionPatterns('test-item-001');

      if (result.outliers.length > 0) {
        result.outliers.forEach(outlier => {
          expect(outlier.date).toBeInstanceOf(Date);
          expect(outlier.value).toBeGreaterThanOrEqual(0);
          expect(outlier.expectedValue).toBeGreaterThanOrEqual(0);
          expect(outlier.deviation).toBeGreaterThan(0);
          expect(outlier.possibleCause).toBeDefined();
        });
      }
    });
  });

  describe('Trend Analysis', () => {
    it('should analyze consumption trends', async () => {
      await service.initialize();

      const result = await service.analyzeTrends('test-item-001');

      expect(result).toBeDefined();
      expect(result.itemId).toBe('test-item-001');
      expect(result.trendDirection).toMatch(/increasing|decreasing|stable/);
      expect(result.trendStrength).toBeGreaterThanOrEqual(0);
      expect(result.changeRate).toBeDefined();
      expect(result.trendConfidence).toBeGreaterThanOrEqual(0);
      expect(result.trendConfidence).toBeLessThanOrEqual(1);
      expect(result.projectedChange).toBeDefined();
    });

    it('should handle different trend windows', async () => {
      await service.initialize();

      const shortWindow = await service.analyzeTrends('test-item-001', { trendWindow: 7 });
      const longWindow = await service.analyzeTrends('test-item-001', { trendWindow: 21 });

      expect(shortWindow.trendDirection).toMatch(/increasing|decreasing|stable/);
      expect(longWindow.trendDirection).toMatch(/increasing|decreasing|stable/);
    });

    it('should provide time to significant change when applicable', async () => {
      await service.initialize();

      const result = await service.analyzeTrends('test-item-001');

      if (result.trendDirection !== 'stable' && result.trendConfidence > 0.7) {
        // Should potentially have time to significant change
        expect(result.timeToSignificantChange).toBeDefined();
      }
    });
  });

  describe('Service Integration and Performance', () => {
    it('should maintain service health with predictive features', async () => {
      await service.initialize();

      const healthStatus = await service.healthCheck();

      expect(healthStatus).toBeDefined();
      expect(healthStatus.isHealthy).toBeDefined();
      expect(healthStatus.performance).toBeDefined();
      expect(healthStatus.performance.analysisTime).toBeGreaterThanOrEqual(0);
    });

    it('should handle concurrent predictive operations', async () => {
      await service.initialize();

      const operations = [
        service.detectAdvancedSeasonality('test-item-001'),
        service.optimizeReorderPoint('test-item-001'),
        service.analyzeConsumptionPatterns('test-item-001'),
        service.analyzeTrends('test-item-001')
      ];

      const results = await Promise.all(operations);

      expect(results).toHaveLength(4);
      results.forEach(result => {
        expect(result).toBeDefined();
      });
    });

    it('should gracefully handle repository errors', async () => {
      // Mock repository to throw error
      mockWarehouseRepository.getOverallStats.mockRejectedValue(new Error('Database connection failed'));

      await service.initialize();

      // Operations should still work with fallback data
      const result = await service.detectAdvancedSeasonality('test-item-001');
      expect(result).toBeDefined();
    });

    it('should cache results appropriately', async () => {
      await service.initialize();

      const start1 = Date.now();
      await service.analyzeConsumptionPatterns('test-item-001');
      const time1 = Date.now() - start1;

      const start2 = Date.now();
      await service.analyzeConsumptionPatterns('test-item-001');
      const time2 = Date.now() - start2;

      // Second call should potentially be faster due to caching
      expect(time2).toBeLessThanOrEqual(time1 * 2); // Allow some variance
    });
  });

  describe('Configuration Integration', () => {
    it('should respect service configuration for predictive features', async () => {
      const customService = new InventoryIntelligenceService({
        inventoryConfig: {
          abcAnalysis: {
            enabled: true,
            updateFrequency: 12,
            classAThreshold: 70,
            classBThreshold: 90,
            analysisWindow: 60
          },
          demandForecasting: {
            enabled: true,
            forecastHorizon: 60,
            updateFrequency: 6,
            minHistoryDays: 21,
            models: ['exponential_smoothing', 'linear_regression']
          },
          anomalyDetection: {
            enabled: true,
            sensitivity: 0.9,
            checkFrequency: 3,
            alertThreshold: 0.9
          },
          reorderOptimization: {
            enabled: true,
            safetyStockDays: 14,
            leadTimeDays: 21,
            serviceLevel: 0.99
          }
        }
      });

      await customService.initialize();

      const result = await customService.optimizeReorderPoint('test-item-001');
      
      // Should use custom configuration values (check that reasoning exists)
      expect(result.reasoning.length).toBeGreaterThan(0);
    });

    it('should disable features when configured', async () => {
      const disabledService = new InventoryIntelligenceService({
        inventoryConfig: {
          abcAnalysis: {
            enabled: false,
            updateFrequency: 24,
            classAThreshold: 80,
            classBThreshold: 95,
            analysisWindow: 90
          },
          demandForecasting: {
            enabled: false,
            forecastHorizon: 30,
            updateFrequency: 12,
            minHistoryDays: 14,
            models: []
          },
          anomalyDetection: {
            enabled: false,
            sensitivity: 0.7,
            checkFrequency: 6,
            alertThreshold: 0.8
          },
          reorderOptimization: {
            enabled: false,
            safetyStockDays: 7,
            leadTimeDays: 14,
            serviceLevel: 0.95
          }
        }
      });

      await disabledService.initialize();

      // Operations should still work but with reduced functionality
      const result = await disabledService.detectAdvancedSeasonality('test-item-001');
      expect(result).toBeDefined();
    });
  });

  describe('Error Handling and Resilience', () => {
    it('should handle invalid item IDs gracefully', async () => {
      await service.initialize();

      const result = await service.analyzeConsumptionPatterns('invalid-item-id');
      
      expect(result.averageDailyConsumption).toBe(0);
      expect(result.patterns).toHaveLength(0);
      expect(result.outliers).toHaveLength(0);
    });

    it('should provide fallback results on service errors', async () => {
      await service.initialize();

      // All operations should provide fallback results even on errors
      const seasonalResult = await service.detectAdvancedSeasonality('error-item');
      const optimizationResult = await service.optimizeReorderPoint('error-item');
      const anomalyResult = await service.detectAdvancedStockAnomalies();
      const patternResult = await service.analyzeConsumptionPatterns('error-item');
      const trendResult = await service.analyzeTrends('error-item');

      expect(seasonalResult).toBeDefined();
      expect(optimizationResult).toBeDefined();
      expect(anomalyResult).toBeDefined();
      expect(patternResult).toBeDefined();
      expect(trendResult).toBeDefined();
    });

    it('should handle service operations without errors', async () => {
      await service.initialize();
      
      // All operations should complete without throwing errors
      const result = await service.analyzeConsumptionPatterns('test-item-001');
      expect(result).toBeDefined();
      expect(result.itemId).toBe('test-item-001');
    });
  });
});