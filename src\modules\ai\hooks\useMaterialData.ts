import { useState, useEffect } from 'react';
import materialData from '../services/cutting/Materialdaten.json';

export interface MaterialData {
  MATNR: string;
  Kabeldurchmesser: number;
  Zuschlag_Kabedurchmesser: number;
  Biegefaktor: number;
  Ringauslieferung: string | null;
  Klein<PERSON>_erlauber_Freiraum: number;
  Bruttogewicht_pro_m: number;
}

export interface CableType {
  value: string;
  label: string;
  diameter: number;
  weight: number;
  matnr: string;
}

export const useMaterialData = () => {
  const [cableTypes, setCableTypes] = useState<CableType[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      // Konvertiere Materialdaten zu Kabeltypen für Dropdown
      const types: CableType[] = (materialData as MaterialData[]).map((item) => ({
        value: item.MATNR,
        label: `${item.MATNR} (Ø ${item.Kabeldurchmesser}mm, ${item.Bruttogewicht_pro_m}kg/m)`,
        diameter: item.Kabeldurchmesser,
        weight: item.Bruttogewicht_pro_m,
        matnr: item.MATNR
      }));

      setCableTypes(types);
      setIsLoading(false);
    } catch (error) {
      console.error('Fehler beim Laden der Materialdaten:', error);
      setIsLoading(false);
    }
  }, []);

  const getCableTypeByMatnr = (matnr: string): CableType | undefined => {
    return cableTypes.find(type => type.value === matnr);
  };

  return {
    cableTypes,
    isLoading,
    getCableTypeByMatnr
  };
};