import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * React Error Boundary Component
 * 
 * Fängt JavaScript-Fehler in der Komponenten-Hierarchie ab und zeigt
 * eine benutzerfreundliche Fehlermeldung anstelle eines weißen Bildschirms.
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Update state with error details
    this.setState({
      error,
      errorInfo
    });

    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Card className="w-full max-w-2xl mx-auto mt-8 border-2 border-red-500">
          <CardHeader className="text-center">
            <div className="flex items-center justify-center mb-4">
              <AlertTriangle className="h-12 w-12 text-red-500" />
            </div>
            <CardTitle className="text-red-600">
              Oops! Etwas ist schiefgelaufen
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <p className="text-gray-600 mb-4">
                Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.
              </p>
              
              <Button 
                onClick={this.handleRetry}
                className="mb-4"
                variant="outline"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Erneut versuchen
              </Button>
            </div>

            {/* Error details for development */}
            {/* Vite verwendet import.meta.env anstelle von process.env */}
            {import.meta.env.DEV && this.state.error && (
              <details className="mt-4 p-4 bg-gray-100 rounded border">
                <summary className="cursor-pointer font-medium text-red-600 mb-2">
                  Fehlerdetails (Development Mode)
                </summary>
                <pre className="text-xs overflow-auto bg-red-50 p-2 rounded border">
                  <code>
                    {this.state.error.toString()}
                    {this.state.errorInfo?.componentStack}
                  </code>
                </pre>
              </details>
            )}
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook-based Error Boundary Wrapper
 * 
 * Da Error Boundaries nur als Class Components funktionieren,
 * bietet dieser Wrapper eine einfache Möglichkeit, Error Boundaries
 * in funktionalen Komponenten zu verwenden.
 */
interface ErrorBoundaryWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

export const ErrorBoundaryWrapper: React.FC<ErrorBoundaryWrapperProps> = ({ 
  children, 
  fallback, 
  onError 
}) => {
  return (
    <ErrorBoundary fallback={fallback} onError={onError}>
      {children}
    </ErrorBoundary>
  );
};

/**
 * Chart Error Boundary
 * 
 * Spezialisierte Error Boundary für Chart-Komponenten
 */
export const ChartErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={
        <Card className="w-full h-80 flex items-center justify-center border-2 border-orange-500">
          <CardContent className="text-center">
            <AlertTriangle className="h-8 w-8 text-orange-500 mx-auto mb-2" />
            <p className="font-medium text-orange-600">Chart konnte nicht geladen werden</p>
            <p className="text-sm text-gray-500 mt-1">
              Bitte prüfen Sie die Datenverbindung oder die Browser-Konsole für Details
            </p>
            <details className="mt-2 text-xs text-left">
              <summary className="cursor-pointer">Debug-Informationen</summary>
              <p className="mt-1">Prüfen Sie:</p>
              <ul className="list-disc list-inside">
                <li>Browser-Konsole (F12) für Fehlerdetails</li>
                <li>Backend-Verbindung auf Port 3001</li>
                <li>API-Endpunkt-Verfügbarkeit</li>
              </ul>
            </details>
          </CardContent>
        </Card>
      }
      onError={(error, errorInfo) => {
        console.error('🚨 Chart Error Details:', {
          error: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          timestamp: new Date().toISOString()
        });
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default ErrorBoundary;