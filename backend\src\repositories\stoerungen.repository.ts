import { PrismaClient } from '@prisma-sfm-dashboard/client';
import { 
  StoerungCreateData, 
  StoerungUpdateData, 
  StoerungWithComments, 
  StoerungCommentCreateData,
  StoerungsStats,
  SystemStatusData,
  SystemStatusUpdateData
} from '../types/stoerungen.types';

// Simple in-memory cache for störungen
const simpleCache = new Map<string, { data: any; expires: number }>();

const getCached = <T>(key: string): T | null => {
  const entry = simpleCache.get(key);
  if (entry && entry.expires > Date.now()) {
    return entry.data;
  }
  simpleCache.delete(key);
  return null;
};

const setCache = (key: string, data: any, ttlMs: number = 300000) => {
  simpleCache.set(key, { data, expires: Date.now() + ttlMs });
};

const clearCachePattern = (pattern: string) => {
  for (const key of simpleCache.keys()) {
    if (key.includes(pattern)) {
      simpleCache.delete(key);
    }
  }
};

export class StoerungenRepository {
  private static instance: StoerungenRepository;

  constructor(private prisma: PrismaClient) {}

  static getInstance(prisma: PrismaClient): StoerungenRepository {
    if (!StoerungenRepository.instance) {
      StoerungenRepository.instance = new StoerungenRepository(prisma);
    }
    return StoerungenRepository.instance;
  }

  async createStoerung(data: StoerungCreateData) {
    const tagsJson = data.tags ? JSON.stringify(data.tags) : null;
    
    const stoerung = await this.prisma.stoerungen.create({
      data: {
        ...data,
        tags: tagsJson,
        updated_at: new Date(),
      },
    });

    // Fetch comments separately
    const comments = await this.prisma.stoerungsComments.findMany({
      where: { stoerung_id: stoerung.id },
      orderBy: { created_at: 'desc' },
    });

    clearCachePattern('stoerungen');
    return this.formatStoerung({ ...stoerung, comments });
  }

  async getStoerungen(options?: {
    status?: string;
    severity?: string;
    category?: string;
    affected_system?: string;
    limit?: number;
    offset?: number;
  }) {
    const cacheKey = `stoerungen:list:${JSON.stringify(options || {})}`;
    const cached = getCached<StoerungWithComments[]>(cacheKey);
    if (cached) return cached;

    const where: any = {};
    if (options?.status) where.status = options.status;
    if (options?.severity) where.severity = options.severity;
    if (options?.category) where.category = options.category;
    if (options?.affected_system) where.affected_system = options.affected_system;

    const stoerungen = await this.prisma.stoerungen.findMany({
      where,
      orderBy: { created_at: 'desc' },
      take: options?.limit,
      skip: options?.offset,
    });

    // Fetch comments for each störung
    const stoerungsWithComments = await Promise.all(
      stoerungen.map(async (stoerung) => {
        const comments = await this.prisma.stoerungsComments.findMany({
          where: { stoerung_id: stoerung.id },
          orderBy: { created_at: 'desc' },
        });
        return { ...stoerung, comments };
      })
    );

    const formatted = stoerungsWithComments.map(this.formatStoerung);
    setCache(cacheKey, formatted, 300000); // 5 minutes TTL
    return formatted;
  }

  async getStoerungById(id: number): Promise<StoerungWithComments | null> {
    const cacheKey = `stoerungen:detail:${id}`;
    const cached = getCached<StoerungWithComments>(cacheKey);
    if (cached) return cached;

    const stoerung = await this.prisma.stoerungen.findUnique({
      where: { id },
    });

    if (!stoerung) return null;

    // Fetch comments separately
    const comments = await this.prisma.stoerungsComments.findMany({
      where: { stoerung_id: stoerung.id },
      orderBy: { created_at: 'desc' },
    });

    const formatted = this.formatStoerung({ ...stoerung, comments });
    setCache(cacheKey, formatted, 300000);
    return formatted;
  }

  async updateStoerung(id: number, data: StoerungUpdateData) {
    const updateData: any = { ...data };
    
    if (data.tags) {
      updateData.tags = JSON.stringify(data.tags);
    }

    if (data.status === 'RESOLVED' && !data.resolved_at) {
      updateData.resolved_at = new Date();
    }

    const stoerung = await this.prisma.stoerungen.update({
      where: { id },
      data: updateData,
    });

    // Fetch comments separately
    const comments = await this.prisma.stoerungsComments.findMany({
      where: { stoerung_id: stoerung.id },
      orderBy: { created_at: 'desc' },
    });

    clearCachePattern('stoerungen');
    return this.formatStoerung({ ...stoerung, comments });
  }

  async deleteStoerung(id: number): Promise<boolean> {
    try {
      await this.prisma.stoerungen.delete({
        where: { id },
      });
      clearCachePattern('stoerungen');
      return true;
    } catch {
      return false;
    }
  }

  async addComment(data: StoerungCommentCreateData) {
    const comment = await this.prisma.stoerungsComments.create({
      data,
    });

    clearCachePattern('stoerungen');
    return comment;
  }

  async getStoerungsStats(): Promise<StoerungsStats> {
    const cacheKey = 'stoerungen:stats';
    const cached = getCached<StoerungsStats>(cacheKey);
    if (cached) return cached;

    const [total, active, resolved, bySeverity, avgMttr, recent] = await Promise.all([
      this.prisma.stoerungen.count(),
      this.prisma.stoerungen.count({ where: { status: { in: ['NEW', 'IN_PROGRESS'] } } }),
      this.prisma.stoerungen.count({ where: { status: 'RESOLVED' } }),
      this.prisma.stoerungen.groupBy({
        by: ['severity'],
        _count: { severity: true },
      }),
      this.prisma.stoerungen.aggregate({
        _avg: { mttr_minutes: true },
        where: { status: 'RESOLVED', mttr_minutes: { not: null } },
      }),
      this.prisma.stoerungen.count({
        where: {
          status: 'RESOLVED',
          resolved_at: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24h
          },
        },
      }),
    ]);

    const severityCounts = bySeverity.reduce((acc, item) => {
      acc[item.severity.toLowerCase()] = item._count.severity;
      return acc;
    }, {} as Record<string, number>);

    const stats: StoerungsStats = {
      total,
      active,
      resolved,
      critical: severityCounts.critical || 0,
      high: severityCounts.high || 0,
      medium: severityCounts.medium || 0,
      low: severityCounts.low || 0,
      avg_mttr_minutes: Math.round(avgMttr._avg.mttr_minutes || 0),
      resolution_rate_24h: recent,
    };

    setCache(cacheKey, stats, 900000); // 15 minutes TTL
    return stats;
  }

  async getSystemStatus(): Promise<SystemStatusData[]> {
    const cacheKey = 'system:status:all';
    const cached = getCached<SystemStatusData[]>(cacheKey);
    if (cached) return cached;

    const statuses = await this.prisma.systemStatus.findMany({
      orderBy: { system_name: 'asc' },
    });

    // Load status messages for each system status with category-based selection
    const statusesWithMessages = await Promise.all(
      statuses.map(async (status) => {
        // Determine category based on system name
        const category = this.determineSystemCategory(status.system_name);
        
        // Get detailed status messages for this category and status
        const statusMessages = await this.prisma.systemStatusMessage.findMany({
          where: {
            category,
            status: status.status,
            is_active: 1
          },
          orderBy: { priority: 'asc' },
          take: 3 // Limit to 3 messages as requested
        });

        return {
          ...status,
          status: status.status as 'OK' | 'WARNING' | 'ERROR' | 'OFF',
          metadata: status.metadata ? JSON.parse(status.metadata) : null,
          statusMessages: statusMessages.map(msg => ({
            id: msg.id,
            title: msg.title,
            description: msg.description,
            priority: msg.priority,
            category: msg.category
          }))
        };
      })
    );

    setCache(cacheKey, statusesWithMessages, 30000); // 30 seconds TTL for live data
    return statusesWithMessages;
  }

  async updateSystemStatus(data: SystemStatusUpdateData): Promise<SystemStatusData> {
    const metadataJson = data.metadata ? JSON.stringify(data.metadata) : null;

    // First try to find existing record
    const existingStatus = await this.prisma.systemStatus.findFirst({
      where: { system_name: data.system_name }
    });

    let status;
    if (existingStatus) {
      // Update existing record
      status = await this.prisma.systemStatus.update({
        where: { id: existingStatus.id },
        data: {
          status: data.status,
          metadata: metadataJson,
          last_check: new Date(),
          updated_at: new Date(),
        },
      });
    } else {
      // Create new record
      status = await this.prisma.systemStatus.create({
        data: {
          system_name: data.system_name,
          status: data.status,
          metadata: metadataJson,
          updated_at: new Date(),
        },
      });
    }

    clearCachePattern('system:status');
    
    return {
      ...status,
      status: status.status as 'OK' | 'WARNING' | 'ERROR',
      metadata: status.metadata ? JSON.parse(status.metadata) : null,
    };
  }

  private formatStoerung(stoerung: any): StoerungWithComments {
    let tags: string[] = [];
    
    try {
      if (stoerung.tags) {
        if (typeof stoerung.tags === 'string') {
          // Try to parse as JSON first
          try {
            tags = JSON.parse(stoerung.tags);
          } catch {
            // If JSON parsing fails, split by comma
            tags = stoerung.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean);
          }
        } else if (Array.isArray(stoerung.tags)) {
          tags = stoerung.tags;
        }
      }
    } catch (error) {
      console.error('Error parsing tags for störung:', stoerung.id, error);
      tags = [];
    }

    return {
      ...stoerung,
      tags,
    };
  }

  /**
   * Determine system category based on system name
   * This matches the categorization logic from SystemStatusHeatmap component
   */
  private determineSystemCategory(systemName: string): string {
    if (systemName.includes('Datenbank')) return 'Datenbanken';
    if (systemName.includes('Terminal')) return 'Terminals';
    if (systemName.includes('Fördertechnik')) return 'Fördertechnik';
    if (systemName.includes('Schrumpfanlage')) return 'Anlagen';
    if (systemName.includes('Wlan')) return 'Netzwerk';
    if (systemName.includes('Automatisches Trommellager') || systemName.includes('Automatisches Ringlager')) return 'Läger';
    if (systemName.includes('Stapler') || systemName.includes('FTS')) return 'Flurförderzeuge';
    if (systemName.includes('SAP')) return 'SAP';
    if (systemName.includes('ITM')) return 'ITM';
    
    // Check for machine identifiers (M1-T-H3, M2-T-H3, etc.)
    const machineIdentifiers = [
      'M1-T-H3', 'M2-T-H3', 'M3-R-H3', 'M4-T-H3', 'M5-R-H3',
      'M6-T-H1', 'M7-R-H1', 'M8-T-H1', 'M9-R-H1', 'M10-T-H1',
      'M11-R-H1', 'M12-T-H1', 'M13-R-H1', 'M14-T-H1', 'M15-R-H1',
      'M16-T-H1', 'M17-R-H1', 'M18-T-H1', 'M19-T-H1', 'M20-T-H1',
      'M21-R-H1', 'M22-T-H3', 'M23-T-H1', 'M24-T-H3', 'M25-RR-H1',
      'M26-T-H1', 'M27-R-H3', 'M28-T-H1'
    ];
    
    if (machineIdentifiers.some(id => systemName.includes(id))) {
      return 'Maschinen';
    }
    
    // Default fallback
    return 'System';
  }
}