import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Activity, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { SystemStatus, SYSTEM_STATUS_COLORS } from '@/types/stoerungen.types';
import stoerungenService from '@/services/stoerungen.service';

interface SystemStatusOverviewProps {
  refreshKey?: number;
}

export const SystemStatusOverview: React.FC<SystemStatusOverviewProps> = React.memo(({ refreshKey = 0 }) => {
  const [systemStatuses, setSystemStatuses] = useState<SystemStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSystemStatus = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await stoerungenService.getSystemStatus();
        setSystemStatuses(data);
      } catch (err) {
        console.error('Error fetching system status:', err);
        setError('Fehler beim Laden der Systemstatus');

        // Fallback: Mock data for development
        setSystemStatuses([
          {
            id: 1,
            system_name: 'FTS',
            status: 'OK',
            last_check: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 2,
            system_name: 'ARiL',
            status: 'WARNING',
            last_check: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 3,
            system_name: 'ATrL',
            status: 'OK',
            last_check: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 4,
            system_name: 'SAP',
            status: 'ERROR',
            last_check: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchSystemStatus();
  }, [refreshKey]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OK':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'WARNING':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'ERROR':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'OK':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'WARNING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'ERROR':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatLastCheck = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffMinutes < 1) return 'Gerade eben';
    if (diffMinutes < 60) return `vor ${diffMinutes} Min`;
    if (diffMinutes < 1440) return `vor ${Math.floor(diffMinutes / 60)} Std`;
    return `vor ${Math.floor(diffMinutes / 1440)} Tagen`;
  };

  const getOverallStatus = () => {
    if (systemStatuses.length === 0) return { status: 'UNKNOWN', count: 0 };

    const errorCount = systemStatuses.filter(s => s.status === 'ERROR').length;
    const warningCount = systemStatuses.filter(s => s.status === 'WARNING').length;
    const okCount = systemStatuses.filter(s => s.status === 'OK').length;

    if (errorCount > 0) return { status: 'ERROR', count: errorCount };
    if (warningCount > 0) return { status: 'WARNING', count: warningCount };
    return { status: 'OK', count: okCount };
  };

  const overallStatus = getOverallStatus();

  if (loading) {
    return (
      <Card className="h-full ">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Activity className="h-4 w-4" />
            System Status
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-center py-6">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-base">
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            System Status
          </div>
          <Badge className={`${getStatusBadgeVariant(overallStatus.status)} text-xs px-2 py-1`}>
            {overallStatus.status === 'OK' && `${overallStatus.count} OK`}
            {overallStatus.status === 'WARNING' && `${overallStatus.count} Warn`}
            {overallStatus.status === 'ERROR' && `${overallStatus.count} Error`}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        {error && (
          <div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
            <p className="text-yellow-800">Beispieldaten</p>
          </div>
        )}

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-2">
          {systemStatuses.map((system) => (
            <div
              key={system.id}
              className="flex flex-col items-center p-3 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors text-center"
            >
              <div className="flex items-center gap-1 mb-1">
                {getStatusIcon(system.status)}
                <Badge className={`${getStatusBadgeVariant(system.status)} text-xs px-1.5 py-0.5`}>
                  {system.status === 'OK' && 'OK'}
                  {system.status === 'WARNING' && 'Warn'}
                  {system.status === 'ERROR' && 'Error'}
                </Badge>
              </div>

              <h4 className="font-medium text-xs text-gray-900 truncate w-full mb-1">{system.system_name}</h4>
              <p className="text-xs text-gray-500">
                {formatLastCheck(system.last_check)}
              </p>
            </div>
          ))}
        </div>

        {systemStatuses.length === 0 && !loading && (
          <div className="text-center py-6 text-gray-500">
            <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Keine Daten</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

SystemStatusOverview.displayName = 'SystemStatusOverview';