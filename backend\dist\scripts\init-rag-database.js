"use strict";
/**
 * Initialize RAG Database Script
 *
 * Creates and initializes the separate RAG SQLite database
 * with sample knowledge bases and documents
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const VectorDatabaseService_1 = __importDefault(require("../services/rag/VectorDatabaseService"));
const EmbeddingService_1 = __importDefault(require("../services/rag/EmbeddingService"));
const path_1 = __importDefault(require("path"));
class RAGDatabaseInitializer {
    constructor() {
        const dbPath = path_1.default.join(process.cwd(), 'backend', 'database', 'rag_knowledge.db');
        this.vectorService = new VectorDatabaseService_1.default(dbPath);
        // Try to use Ollama first, fallback to OpenAI or mock
        this.embeddingService = new EmbeddingService_1.default({
            provider: 'ollama', // Will fallback automatically if not available
            defaultModel: 'mxbai-embed-large:latest'
        });
    }
    /**
     * Initialize RAG database with sample data
     */
    async initialize() {
        console.log('🚀 Initializing RAG Database...');
        // Check embedding service availability
        await this.checkEmbeddingService();
        try {
            // Create sample documents
            const sampleDocuments = this.getSampleDocuments();
            for (const doc of sampleDocuments) {
                await this.addSampleDocument(doc);
            }
            console.log('✅ RAG Database initialized successfully!');
            console.log(`📊 Added ${sampleDocuments.length} sample documents`);
            // Show statistics
            const stats = await this.vectorService.getStatistics();
            console.log('📈 Database Statistics:');
            console.log(`   - Documents: ${stats.totalDocuments}`);
            console.log(`   - Chunks: ${stats.totalChunks}`);
            console.log(`   - Embeddings: ${stats.totalEmbeddings}`);
            console.log(`   - Storage: ${(stats.storageSize / 1024 / 1024).toFixed(2)} MB`);
        }
        catch (error) {
            console.error('❌ Error initializing RAG database:', error);
            throw error;
        }
    }
    /**
     * Add a sample document to the database
     */
    async addSampleDocument(doc) {
        try {
            // Store document
            const documentId = await this.storeDocument(doc);
            // Chunk the document
            const chunks = this.chunkDocument(doc.content);
            const chunkIds = [];
            // Store chunks
            for (let i = 0; i < chunks.length; i++) {
                const chunkId = await this.storeChunk(documentId, chunks[i], i);
                chunkIds.push(chunkId);
            }
            // Generate and store embeddings
            for (const chunkId of chunkIds) {
                const chunk = chunks[chunkIds.indexOf(chunkId)];
                await this.generateAndStoreEmbedding(chunkId, chunk);
            }
            console.log(`📄 Added document: ${doc.title} (${chunks.length} chunks)`);
        }
        catch (error) {
            console.error(`❌ Error adding document "${doc.title}":`, error);
        }
    }
    /**
     * Store document in database
     */
    async storeDocument(doc) {
        const documentId = this.generateId();
        const now = new Date().toISOString();
        const stmt = this.vectorService['db'].prepare(`
      INSERT INTO documents (id, title, content, source, content_type, language, created_at, updated_at, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
        stmt.run(documentId, doc.title, doc.content, 'sample', 'text/plain', doc.language, now, now, 'indexed');
        return documentId;
    }
    /**
     * Store chunk in database
     */
    async storeChunk(documentId, content, index) {
        const chunkId = this.generateId();
        const now = new Date().toISOString();
        const tokenCount = Math.ceil(content.length / 4); // Rough estimate
        const stmt = this.vectorService['db'].prepare(`
      INSERT INTO chunks (id, document_id, content, chunk_index, token_count, start_position, end_position, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
        stmt.run(chunkId, documentId, content, index, tokenCount, 0, // start_position - would need proper calculation
        content.length, // end_position
        now);
        return chunkId;
    }
    /**
     * Generate and store embedding for chunk
     */
    async generateAndStoreEmbedding(chunkId, content) {
        try {
            const providerInfo = this.embeddingService.getProviderInfo();
            const embeddingResponse = await this.embeddingService.generateEmbedding({
                text: content,
                model: providerInfo.model // Use the correct model for the provider
            });
            await this.vectorService.storeEmbedding(chunkId, embeddingResponse.embedding, embeddingResponse.model);
        }
        catch (error) {
            console.warn(`⚠️ Could not generate embedding for chunk ${chunkId}:`, error);
        }
    }
    /**
     * Simple document chunking
     */
    chunkDocument(content, maxChunkSize = 500) {
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const chunks = [];
        let currentChunk = '';
        for (const sentence of sentences) {
            if (currentChunk.length + sentence.length > maxChunkSize && currentChunk.length > 0) {
                chunks.push(currentChunk.trim());
                currentChunk = sentence.trim();
            }
            else {
                currentChunk += (currentChunk.length > 0 ? '. ' : '') + sentence.trim();
            }
        }
        if (currentChunk.length > 0) {
            chunks.push(currentChunk.trim());
        }
        return chunks.length > 0 ? chunks : [content]; // Fallback to full content
    }
    /**
     * Generate unique ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    /**
     * Get sample documents for different categories
     */
    getSampleDocuments() {
        return [
            {
                title: 'Versand KPI Definitionen',
                content: `Servicegrad: Der Servicegrad misst den Anteil der termingerecht ausgelieferten Sendungen. Ein Servicegrad von 95% bedeutet, dass 95% aller Lieferungen pünktlich beim Kunden ankommen. 

        Kommissionierungsrate: Die Anzahl der pro Stunde kommissionierten Artikel. Eine hohe Kommissionierungsrate zeigt effiziente Lagerprozesse an.

        Tonnage: Das Gesamtgewicht der versendeten Waren pro Tag oder Monat. Die Tonnage ist ein wichtiger Indikator für die Auslastung des Versandbereichs.

        ATRL (Automatisches Transport- und Lagersystem): Verfügbarkeit und Performance des automatisierten Lagersystems. Eine hohe ATRL-Verfügbarkeit ist kritisch für den reibungslosen Betrieb.`,
                category: 'dispatch',
                language: 'de'
            },
            {
                title: 'Ablängerei Optimierungsverfahren',
                content: `Schnittoptimierung: Durch intelligente Schnittmuster kann der Materialverbrauch um bis zu 15% reduziert werden. Dabei werden Restlängen systematisch erfasst und wiederverwendet.

        Maschinenauslastung: Die optimale Verteilung der Schnittaufträge auf H1 und H3 Maschinen sorgt für gleichmäßige Auslastung und reduziert Wartezeiten.

        Qualitätskontrolle: Regelmäßige Kalibrierung der Schnittmaschinen gewährleistet präzise Schnitte und minimiert Ausschuss.

        Effizienzsteigerung: Durch Batch-Processing ähnlicher Aufträge können Rüstzeiten reduziert und die Gesamteffizienz gesteigert werden.`,
                category: 'cutting',
                language: 'de'
            },
            {
                title: 'Wareneingang Prozesse',
                content: `Wareneingangskontrolle: Jede eingehende Lieferung wird auf Vollständigkeit und Qualität geprüft. Abweichungen werden sofort dokumentiert und an den Lieferanten gemeldet.

        Lagerplatz-Zuordnung: Neue Waren werden basierend auf ABC-Analyse und Umschlagshäufigkeit optimal im Lager platziert. A-Artikel erhalten Plätze mit kurzen Wegen.

        Bestandsführung: Alle Warenbewegungen werden in Echtzeit im System erfasst. Dies ermöglicht präzise Bestandsübersicht und verhindert Fehlbestände.

        Auslastungsmanagement: Die Lagerauslastung wird kontinuierlich überwacht. Bei Überschreitung von 90% werden automatisch Umlagerungen eingeleitet.`,
                category: 'incoming-goods',
                language: 'de'
            },
            {
                title: 'System Verfügbarkeit und Monitoring',
                content: `Systemverfügbarkeit: Die Gesamtverfügbarkeit aller kritischen Systeme (ATrL, ARiL, FTS) sollte mindestens 95% betragen. Ausfälle werden sofort eskaliert.

        Störungsmanagement: Alle Störungen werden kategorisiert nach Schweregrad (Critical, High, Medium, Low) und entsprechend priorisiert bearbeitet.

        Präventive Wartung: Regelmäßige Wartungszyklen reduzieren ungeplante Ausfälle um bis zu 60%. Wartungsfenster werden außerhalb der Hauptbetriebszeiten geplant.

        Performance Monitoring: Kontinuierliche Überwachung der Systemleistung ermöglicht frühzeitige Erkennung von Leistungseinbußen und proaktive Maßnahmen.`,
                category: 'system',
                language: 'de'
            },
            {
                title: 'Troubleshooting Guide',
                content: `Häufige Probleme und Lösungen:

        Problem: Niedriger Servicegrad
        Lösung: Prüfen Sie die Kapazitätsauslastung, Personalplanung und Systemverfügbarkeit. Oft hilft eine Umverteilung der Arbeitslasten.

        Problem: Hohe Schnittabfälle
        Lösung: Überprüfen Sie die Schnittmuster-Optimierung und stellen Sie sicher, dass Restlängen korrekt erfasst werden.

        Problem: Lagerüberlauf
        Lösung: Initiieren Sie sofortige Auslagerungen und prüfen Sie die Bestandsplanung. Kontaktieren Sie das Disposition-Team.

        Problem: Systemausfall
        Lösung: Folgen Sie dem Eskalationsplan, aktivieren Sie Backup-Systeme und informieren Sie alle betroffenen Bereiche.`,
                category: 'troubleshooting',
                language: 'de'
            }
        ];
    }
    /**
     * Check embedding service availability and provide feedback
     */
    async checkEmbeddingService() {
        const providerInfo = this.embeddingService.getProviderInfo();
        console.log(`🔧 Embedding Provider: ${providerInfo.provider}`);
        console.log(`📊 Model: ${providerInfo.model}`);
        if (providerInfo.provider === 'ollama') {
            const isAvailable = await this.embeddingService.isOllamaAvailable();
            if (isAvailable) {
                const modelAvailable = await this.embeddingService.isModelAvailable(providerInfo.model);
                if (modelAvailable) {
                    console.log('✅ Ollama is available and model is ready');
                }
                else {
                    console.log(`⚠️ Ollama is available but model '${providerInfo.model}' not found`);
                    console.log(`   Run: ollama pull ${providerInfo.model}`);
                }
            }
            else {
                console.log('⚠️ Ollama not available, will use mock embeddings');
                console.log('   Install Ollama: https://ollama.ai/');
            }
        }
    }
    /**
     * Cleanup resources
     */
    async cleanup() {
        await this.vectorService.disconnect();
    }
}
// Run initialization if called directly
if (require.main === module) {
    const initializer = new RAGDatabaseInitializer();
    initializer.initialize()
        .then(() => {
        console.log('🎉 RAG Database initialization completed!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 RAG Database initialization failed:', error);
        process.exit(1);
    })
        .finally(() => {
        initializer.cleanup();
    });
}
exports.default = RAGDatabaseInitializer;
