/**
 * Production Business Service
 * 
 * Service-Layer für Production-spezifische Business Logic.
 * Bietet high-level Operationen für Produktions-Management,
 * Maschinen-Effizienz und Schnitt-Optimierung.
 */

import { BaseService, ServiceConfig } from '../base.service';
import { repositories } from '@/repositories';
import { DateRangeFilter } from '@/repositories/base.repository';

/**
 * Maschinen-Performance-Metriken
 */
export interface MachinePerformanceMetrics {
  machineId: string;
  efficiency: number;
  uptime: number;
  throughput: number;
  qualityRate: number;
  trend: 'improving' | 'declining' | 'stable';
  status: 'operational' | 'maintenance' | 'down';
}

/**
 * Produktions-Dashboard
 */
export interface ProductionDashboard {
  overview: {
    totalProduction: number;
    dailyAverage: number;
    efficiency: number;
    activeLines: number;
  };
  machines: MachinePerformanceMetrics[];
  cutting: {
    totalCuts: number;
    warehouseDistribution: Record<string, number>;
    efficiency: number;
  };
  alerts: Array<{
    type: 'critical' | 'warning' | 'info';
    machine?: string;
    message: string;
    timestamp: Date;
    actionRequired: boolean;
  }>;
  kpis: {
    oee: number; // Overall Equipment Effectiveness
    availability: number;
    performance: number;
    quality: number;
  };
}

/**
 * Maintenance-Empfehlung
 */
export interface MaintenanceRecommendation {
  machineId: string;
  priority: 'urgent' | 'high' | 'medium' | 'low';
  type: 'preventive' | 'corrective' | 'predictive';
  description: string;
  estimatedDowntime: number; // in Stunden
  costImpact: 'low' | 'medium' | 'high';
  recommendedDate: Date;
}

/**
 * Production Business Service Klasse
 */
export class ProductionBusinessService extends BaseService {
  readonly serviceName = 'ProductionBusinessService';
  
  constructor(config?: ServiceConfig) {
    super(config);
  }
  
  /**
   * Service-spezifische Initialisierung
   */
  protected async onInitialize(): Promise<void> {
    // Teste Repository-Verfügbarkeit
    await repositories.production.schnitte.getAll();
    this.log('Production repositories initialized and tested');
  }
  
  /**
   * Health Check für Production-Services
   */
  protected async onHealthCheck(): Promise<void> {
    const healthCheck = await repositories.healthCheck();
    if (healthCheck.status === 'unhealthy') {
      throw new Error(`Repository health check failed: ${healthCheck.errors.join(', ')}`);
    }
  }
  
  /**
   * Produktions-Dashboard erstellen
   */
  async getProductionDashboard(filter?: DateRangeFilter): Promise<ProductionDashboard> {
    this.ensureInitialized();
    
    return await this.withRetry(async () => {
      const [
        dailySums,
        machineEfficiency,
        warehousePerf,
        cuttingData,
        trends
      ] = await Promise.all([
        repositories.production.schnitte.getDailySums(),
        repositories.production.maschinenEfficiency.getAll(),
        repositories.production.ablaengerei.getWarehousePerformance(),
        repositories.production.cuttingChart.getAll(),
        repositories.production.cuttingChart.getTrendAnalysis()
      ]);
      
      // Berechne Overview-Metriken
      const overview = this.calculateOverviewMetrics(dailySums, machineEfficiency);
      
      // Erstelle Maschinen-Performance-Liste
      const machines = await this.createMachinePerformanceList(machineEfficiency);
      
      // Berechne Cutting-Metriken
      const cutting = this.calculateCuttingMetrics(warehousePerf, cuttingData);
      
      // Generiere Alerts
      const alerts = this.generateProductionAlerts(machines, overview);
      
      // Berechne KPIs
      const kpis = this.calculateProductionKPIs(machines, overview);
      
      return {
        overview,
        machines,
        cutting,
        alerts,
        kpis
      };
    });
  }
  
  /**
   * Maschinen-Effizienz-Analyse
   */
  async getMachineEfficiencyAnalysis(): Promise<{
    topPerformers: MachinePerformanceMetrics[];
    underPerformers: MachinePerformanceMetrics[];
    averageEfficiency: number;
    improvementPotential: number;
    recommendations: Array<{
      machineId: string;
      action: string;
      expectedImprovement: number;
    }>;
  }> {
    this.ensureInitialized();
    
    const [topPerformers, underPerformers, avgEfficiency] = await Promise.all([
      repositories.production.maschinenEfficiency.getTopPerformers(5),
      repositories.production.maschinenEfficiency.getLowPerformers(50), // Unter 50% Effizienz
      repositories.production.maschinenEfficiency.getAverageEfficiency()
    ]);
    
    const machines = await this.createMachinePerformanceList([...topPerformers, ...underPerformers]);
    
    const topMachines = machines.slice(0, 5);
    const underMachines = machines.filter(m => m.efficiency < 50);
    
    const recommendations = underMachines.map(machine => ({
      machineId: machine.machineId,
      action: this.generateMachineRecommendation(machine),
      expectedImprovement: Math.min(25, 80 - machine.efficiency) // Max 25% Verbesserung
    }));
    
    return {
      topPerformers: topMachines,
      underPerformers: underMachines,
      averageEfficiency: avgEfficiency.average,
      improvementPotential: this.calculateImprovementPotential(machines),
      recommendations
    };
  }
  
  /**
   * Predictive Maintenance Empfehlungen
   */
  async getMaintenanceRecommendations(): Promise<MaintenanceRecommendation[]> {
    this.ensureInitialized();
    
    const machines = await repositories.production.maschinenEfficiency.getAll();
    const recommendations: MaintenanceRecommendation[] = [];
    
    for (const machine of machines) {
      const efficiency = (machine as any).efficiency || 0;
      const machineId = (machine as any).Machine || (machine as any).machine || 'Unknown';
      
      // Urgente Wartung bei sehr niedriger Effizienz
      if (efficiency < 30) {
        recommendations.push({
          machineId,
          priority: 'urgent',
          type: 'corrective',
          description: 'Kritische Effizienz - sofortige Inspektion erforderlich',
          estimatedDowntime: 4,
          costImpact: 'high',
          recommendedDate: new Date()
        });
      }
      // Vorbeugende Wartung bei mittlerer Effizienz
      else if (efficiency < 60) {
        const recommendedDate = new Date();
        recommendedDate.setDate(recommendedDate.getDate() + 7); // In einer Woche
        
        recommendations.push({
          machineId,
          priority: 'high',
          type: 'preventive',
          description: 'Effizienz unter Sollwert - vorbeugende Wartung empfohlen',
          estimatedDowntime: 2,
          costImpact: 'medium',
          recommendedDate
        });
      }
      // Predictive Maintenance bei guter aber sinkender Effizienz
      else if (efficiency < 80) {
        const recommendedDate = new Date();
        recommendedDate.setDate(recommendedDate.getDate() + 30); // In einem Monat
        
        recommendations.push({
          machineId,
          priority: 'medium',
          type: 'predictive',
          description: 'Planmäßige Wartung zur Effizienzoptimierung',
          estimatedDowntime: 1,
          costImpact: 'low',
          recommendedDate
        });
      }
    }
    
    // Sortiere nach Priorität
    const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 };
    return recommendations.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);
  }
  
  /**
   * Produktions-Optimierung vorschlagen
   */
  async getProductionOptimizations(): Promise<Array<{
    category: 'efficiency' | 'capacity' | 'quality' | 'automation';
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    effort: 'high' | 'medium' | 'low';
    roi: number; // Return on Investment in %
    timeframe: 'immediate' | 'short' | 'medium' | 'long';
  }>> {
    this.ensureInitialized();
    
    const [dashboard, efficiency] = await Promise.all([
      this.getProductionDashboard(),
      this.getMachineEfficiencyAnalysis()
    ]);
    
    const optimizations = [];
    
    // Effizienz-Optimierungen
    if (dashboard.overview.efficiency < 75) {
      optimizations.push({
        category: 'efficiency' as const,
        title: 'Maschinen-Effizienz-Programm',
        description: 'Systematische Verbesserung der Maschinen-Effizienz durch Optimierung und Schulungen',
        impact: 'high' as const,
        effort: 'medium' as const,
        roi: this.calculateROI(dashboard.overview.efficiency, 85),
        timeframe: 'medium' as const
      });
    }
    
    // Kapazitäts-Optimierungen
    if (dashboard.overview.activeLines < 10) { // Beispiel-Schwellwert
      optimizations.push({
        category: 'capacity' as const,
        title: 'Produktionslinie-Erweiterung',
        description: 'Aktivierung zusätzlicher Produktionslinien zur Kapazitätssteigerung',
        impact: 'high' as const,
        effort: 'high' as const,
        roi: 25,
        timeframe: 'long' as const
      });
    }
    
    // Automatisierungs-Optimierungen
    if (efficiency.underPerformers.length > 3) {
      optimizations.push({
        category: 'automation' as const,
        title: 'Automatisierungs-Upgrade',
        description: 'Modernisierung unterperformanter Maschinen mit neuer Automatisierungstechnik',
        impact: 'medium' as const,
        effort: 'high' as const,
        roi: 35,
        timeframe: 'long' as const
      });
    }
    
    // Qualitäts-Optimierungen
    if (dashboard.kpis.quality < 95) {
      optimizations.push({
        category: 'quality' as const,
        title: 'Qualitäts-Management-System',
        description: 'Implementierung verbesserter Qualitätskontrolle und -sicherung',
        impact: 'medium' as const,
        effort: 'medium' as const,
        roi: 20,
        timeframe: 'short' as const
      });
    }
    
    return optimizations.sort((a, b) => b.roi - a.roi); // Sortiere nach ROI
  }
  
  /**
   * Cache für Production-Daten invalidieren
   */
  async invalidateCache(): Promise<void> {
    repositories.production.invalidateAllCache();
    this.log('Production cache invalidated');
  }
  
  /**
   * Private Hilfsmethoden
   */
  
  private calculateOverviewMetrics(dailySums: any[], machineData: any[]): any {
    const totalProduction = dailySums.reduce((sum, day) => sum + day.grandTotal, 0);
    const dailyAverage = totalProduction / Math.max(dailySums.length, 1);
    
    const avgEfficiency = machineData.length > 0 
      ? machineData.reduce((sum, machine) => sum + ((machine as any).efficiency || 0), 0) / machineData.length
      : 0;
    
    const activeLines = machineData.filter(m => (m as any).efficiency > 0).length;
    
    return {
      totalProduction,
      dailyAverage,
      efficiency: avgEfficiency,
      activeLines
    };
  }
  
  private async createMachinePerformanceList(machineData: any[]): Promise<MachinePerformanceMetrics[]> {
    return machineData.map(machine => {
      const efficiency = (machine as any).efficiency || 0;
      const machineId = (machine as any).Machine || (machine as any).machine || 'Unknown';
      
      return {
        machineId,
        efficiency,
        uptime: this.calculateUptime(efficiency),
        throughput: this.calculateThroughput(machine),
        qualityRate: this.calculateQualityRate(efficiency),
        trend: this.calculateMachineTrend(efficiency),
        status: this.getMachineStatus(efficiency)
      };
    });
  }
  
  private calculateCuttingMetrics(warehousePerf: any, cuttingData: any[]): any {
    return {
      totalCuts: warehousePerf.totalCuts || 0,
      warehouseDistribution: warehousePerf.byWarehouse || {},
      efficiency: warehousePerf.efficiency || 0
    };
  }
  
  private generateProductionAlerts(machines: MachinePerformanceMetrics[], overview: any): any[] {
    const alerts = [];
    
    // Kritische Maschinen
    const criticalMachines = machines.filter(m => m.efficiency < 30);
    for (const machine of criticalMachines) {
      alerts.push({
        type: 'critical' as const,
        machine: machine.machineId,
        message: `Maschine ${machine.machineId} kritische Effizienz (${machine.efficiency.toFixed(1)}%)`,
        timestamp: new Date(),
        actionRequired: true
      });
    }
    
    // Niedrige Gesamteffizienz
    if (overview.efficiency < 60) {
      alerts.push({
        type: 'warning' as const,
        message: `Gesamteffizienz unter Sollwert (${overview.efficiency.toFixed(1)}%)`,
        timestamp: new Date(),
        actionRequired: true
      });
    }
    
    // Positive Entwicklungen
    const improvingMachines = machines.filter(m => m.trend === 'improving').length;
    if (improvingMachines > machines.length * 0.7) {
      alerts.push({
        type: 'info' as const,
        message: `${improvingMachines} Maschinen zeigen positive Trends`,
        timestamp: new Date(),
        actionRequired: false
      });
    }
    
    return alerts;
  }
  
  private calculateProductionKPIs(machines: MachinePerformanceMetrics[], overview: any): any {
    const avgAvailability = machines.reduce((sum, m) => sum + m.uptime, 0) / machines.length;
    const avgPerformance = overview.efficiency;
    const avgQuality = machines.reduce((sum, m) => sum + m.qualityRate, 0) / machines.length;
    
    // OEE = Availability × Performance × Quality
    const oee = (avgAvailability / 100) * (avgPerformance / 100) * (avgQuality / 100) * 100;
    
    return {
      oee,
      availability: avgAvailability,
      performance: avgPerformance,
      quality: avgQuality
    };
  }
  
  private calculateUptime(efficiency: number): number {
    // Vereinfachte Berechnung: Höhere Effizienz = höhere Uptime
    return Math.min(95, efficiency + 20);
  }
  
  private calculateThroughput(machine: any): number {
    return (machine as any).schnitteProStd || (machine as any).throughput || 0;
  }
  
  private calculateQualityRate(efficiency: number): number {
    // Vereinfachte Annahme: Effizienz korreliert mit Qualität
    return Math.min(99, efficiency + 15);
  }
  
  private calculateMachineTrend(efficiency: number): 'improving' | 'declining' | 'stable' {
    // Vereinfachte Trend-Berechnung basierend auf Effizienz
    if (efficiency > 80) return 'improving';
    if (efficiency < 40) return 'declining';
    return 'stable';
  }
  
  private getMachineStatus(efficiency: number): 'operational' | 'maintenance' | 'down' {
    if (efficiency > 70) return 'operational';
    if (efficiency > 30) return 'maintenance';
    return 'down';
  }
  
  private generateMachineRecommendation(machine: MachinePerformanceMetrics): string {
    if (machine.efficiency < 30) {
      return 'Sofortige Wartung und Systemdiagnose erforderlich';
    } else if (machine.efficiency < 50) {
      return 'Kalibrierung und vorbeugende Wartung empfohlen';
    } else if (machine.efficiency < 70) {
      return 'Optimierung der Betriebsparameter prüfen';
    }
    return 'Planmäßige Wartung beibehalten';
  }
  
  private calculateImprovementPotential(machines: MachinePerformanceMetrics[]): number {
    const currentAvg = machines.reduce((sum, m) => sum + m.efficiency, 0) / machines.length;
    const targetEfficiency = 85; // Ziel-Effizienz
    return Math.max(0, targetEfficiency - currentAvg);
  }
  
  private calculateROI(currentEfficiency: number, targetEfficiency: number): number {
    const improvement = targetEfficiency - currentEfficiency;
    // Vereinfachte ROI-Berechnung: 2% ROI pro 1% Effizienzsteigerung
    return improvement * 2;
  }
}