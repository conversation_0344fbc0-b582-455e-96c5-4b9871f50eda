/**
 * Cutting Pattern Visualization and Analysis Tools
 * 
 * Provides visualization data and analysis for cutting patterns,
 * including pattern efficiency, waste distribution, and optimization insights
 */

import { 
  CuttingPlan, 
  DrumAllocation, 
  Cut, 
  CuttingRequest,
  WasteAnalysis 
} from '../../types';

export interface CuttingPatternVisualization {
  drumVisualizations: DrumVisualization[];
  wasteDistribution: WasteDistribution;
  efficiencyMetrics: EfficiencyMetrics;
  patternAnalysis: PatternAnalysis;
  recommendations: PatternRecommendation[];
}

export interface DrumVisualization {
  drumId: string;
  totalLength: number;
  cuts: CutVisualization[];
  wasteSegments: WasteSegment[];
  utilization: number;
  efficiency: number;
  cableType: string;
  quality: string;
}

export interface CutVisualization {
  orderId: string;
  startPosition: number;
  endPosition: number;
  length: number;
  color: string;
  priority: 'low' | 'medium' | 'high';
  cableType: string;
  orderInfo: {
    customerName?: string;
    deadline?: Date;
    quantity: number;
  };
}

export interface WasteSegment {
  startPosition: number;
  endPosition: number;
  length: number;
  isUsable: boolean;
  recommendedUse?: string;
}

export interface WasteDistribution {
  totalWaste: number;
  wastePercentage: number;
  wasteByLength: {
    range: string;
    count: number;
    totalLength: number;
    percentage: number;
  }[];
  usableWaste: number;
  unusableWaste: number;
  wasteHeatmap: {
    drumId: string;
    wastePercentage: number;
    severity: 'low' | 'medium' | 'high';
  }[];
}

export interface EfficiencyMetrics {
  overallEfficiency: number;
  drumEfficiencies: {
    drumId: string;
    efficiency: number;
    rank: number;
  }[];
  timeEfficiency: number;
  materialEfficiency: number;
  processEfficiency: number;
  benchmarkComparison: {
    industry: number;
    company: number;
    improvement: number;
  };
}

export interface PatternAnalysis {
  patternType: 'optimal' | 'good' | 'suboptimal' | 'poor';
  strengths: string[];
  weaknesses: string[];
  optimizationPotential: number;
  complexityScore: number;
  riskFactors: RiskFactor[];
  qualityScore: number;
}

export interface RiskFactor {
  type: 'waste' | 'time' | 'quality' | 'complexity';
  severity: 'low' | 'medium' | 'high';
  description: string;
  impact: number;
  mitigation: string;
}

export interface PatternRecommendation {
  type: 'waste_reduction' | 'efficiency_improvement' | 'time_optimization' | 'quality_enhancement';
  priority: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  expectedImprovement: number;
  implementationEffort: 'low' | 'medium' | 'high';
  costImpact: number;
}

/**
 * Cutting pattern analyzer and visualizer
 */
export class CuttingPatternAnalyzer {
  private readonly colorPalette = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];

  private readonly wasteRanges = [
    { min: 0, max: 10, label: '0-10cm' },
    { min: 10, max: 25, label: '10-25cm' },
    { min: 25, max: 50, label: '25-50cm' },
    { min: 50, max: 100, label: '50-100cm' },
    { min: 100, max: 200, label: '100-200cm' },
    { min: 200, max: Infinity, label: '200cm+' }
  ];

  /**
   * Generate comprehensive visualization data for cutting pattern
   */
  async generateVisualization(
    plan: CuttingPlan, 
    request: CuttingRequest
  ): Promise<CuttingPatternVisualization> {
    const drumVisualizations = this.generateDrumVisualizations(plan, request);
    const wasteDistribution = this.analyzeWasteDistribution(plan, request);
    const efficiencyMetrics = this.calculateEfficiencyMetrics(plan, request);
    const patternAnalysis = this.analyzePattern(plan, request);
    const recommendations = this.generateRecommendations(plan, request, patternAnalysis);

    return {
      drumVisualizations,
      wasteDistribution,
      efficiencyMetrics,
      patternAnalysis,
      recommendations
    };
  }

  /**
   * Generate drum-level visualizations
   */
  private generateDrumVisualizations(
    plan: CuttingPlan, 
    request: CuttingRequest
  ): DrumVisualization[] {
    return plan.drumAllocations.map((allocation, index) => {
      const drum = request.availableDrums.find(d => d.id === allocation.drumId);
      if (!drum) {
        throw new Error(`Drum ${allocation.drumId} not found`);
      }

      const totalLength = drum.availableLength || drum.remainingLength || 0;
      const cuts = this.generateCutVisualizations(allocation.cuts, request, index);
      const wasteSegments = this.generateWasteSegments(allocation, totalLength);

      return {
        drumId: allocation.drumId,
        totalLength,
        cuts,
        wasteSegments,
        utilization: allocation.utilization,
        efficiency: this.calculateDrumEfficiency(allocation, totalLength),
        cableType: drum.cableType,
        quality: drum.quality
      };
    });
  }

  /**
   * Generate cut visualizations with colors and metadata
   */
  private generateCutVisualizations(
    cuts: Cut[], 
    request: CuttingRequest, 
    drumIndex: number
  ): CutVisualization[] {
    return cuts.map((cut, cutIndex) => {
      const order = request.orders.find(o => o.id === cut.orderId);
      const colorIndex = (drumIndex * 3 + cutIndex) % this.colorPalette.length;

      return {
        orderId: cut.orderId,
        startPosition: cut.startPosition,
        endPosition: cut.endPosition,
        length: cut.length,
        color: this.colorPalette[colorIndex],
        priority: order?.priority || 'medium',
        cableType: order?.cableType || 'unknown',
        orderInfo: {
          deadline: order?.deadline,
          quantity: order?.quantity || 1
        }
      };
    });
  }

  /**
   * Generate waste segments visualization
   */
  private generateWasteSegments(
    allocation: DrumAllocation, 
    totalLength: number
  ): WasteSegment[] {
    const segments: WasteSegment[] = [];
    let currentPosition = 0;

    // Sort cuts by position
    const sortedCuts = [...allocation.cuts].sort((a, b) => a.startPosition - b.startPosition);

    // Find gaps between cuts
    for (const cut of sortedCuts) {
      if (cut.startPosition > currentPosition) {
        const wasteLength = cut.startPosition - currentPosition;
        segments.push({
          startPosition: currentPosition,
          endPosition: cut.startPosition,
          length: wasteLength,
          isUsable: wasteLength >= 10, // 10cm minimum usable waste
          recommendedUse: this.getWasteRecommendation(wasteLength)
        });
      }
      currentPosition = cut.endPosition;
    }

    // Final waste segment
    if (currentPosition < totalLength) {
      const wasteLength = totalLength - currentPosition;
      segments.push({
        startPosition: currentPosition,
        endPosition: totalLength,
        length: wasteLength,
        isUsable: wasteLength >= 10,
        recommendedUse: this.getWasteRecommendation(wasteLength)
      });
    }

    return segments;
  }

  /**
   * Get recommendation for waste segment usage
   */
  private getWasteRecommendation(length: number): string {
    if (length < 10) return 'Discard - too small';
    if (length < 25) return 'Small repairs/patches';
    if (length < 50) return 'Short connections';
    if (length < 100) return 'Medium connections';
    if (length < 200) return 'Standard orders';
    return 'Priority orders';
  }

  /**
   * Analyze waste distribution across the cutting plan
   */
  private analyzeWasteDistribution(
    plan: CuttingPlan, 
    request: CuttingRequest
  ): WasteDistribution {
    const totalCapacity = request.availableDrums.reduce((sum, drum) => 
      sum + (drum.availableLength || drum.remainingLength || 0), 0
    );

    const wasteByLength = this.wasteRanges.map(range => {
      const wastesInRange = plan.drumAllocations.flatMap(allocation => {
        const drum = request.availableDrums.find(d => d.id === allocation.drumId);
        const totalLength = drum ? (drum.availableLength || drum.remainingLength || 0) : 0;
        const wasteSegments = this.generateWasteSegments(allocation, totalLength);
        
        return wasteSegments.filter(segment => 
          segment.length >= range.min && segment.length < range.max
        );
      });

      const totalLength = wastesInRange.reduce((sum, segment) => sum + segment.length, 0);

      return {
        range: range.label,
        count: wastesInRange.length,
        totalLength,
        percentage: totalCapacity > 0 ? (totalLength / totalCapacity) * 100 : 0
      };
    });

    const usableWaste = plan.drumAllocations.reduce((sum, allocation) => {
      const drum = request.availableDrums.find(d => d.id === allocation.drumId);
      const totalLength = drum ? (drum.availableLength || drum.remainingLength || 0) : 0;
      const wasteSegments = this.generateWasteSegments(allocation, totalLength);
      
      return sum + wasteSegments
        .filter(segment => segment.isUsable)
        .reduce((segSum, segment) => segSum + segment.length, 0);
    }, 0);

    const unusableWaste = plan.totalWaste - usableWaste;

    const wasteHeatmap = plan.drumAllocations.map(allocation => {
      const drum = request.availableDrums.find(d => d.id === allocation.drumId);
      const totalLength = drum ? (drum.availableLength || drum.remainingLength || 0) : 0;
      const wastePercentage = totalLength > 0 ? (allocation.remainingLength / totalLength) * 100 : 0;
      
      let severity: 'low' | 'medium' | 'high' = 'low';
      if (wastePercentage > 20) severity = 'high';
      else if (wastePercentage > 10) severity = 'medium';

      return {
        drumId: allocation.drumId,
        wastePercentage,
        severity
      };
    });

    return {
      totalWaste: plan.totalWaste,
      wastePercentage: totalCapacity > 0 ? (plan.totalWaste / totalCapacity) * 100 : 0,
      wasteByLength,
      usableWaste,
      unusableWaste,
      wasteHeatmap
    };
  }

  /**
   * Calculate comprehensive efficiency metrics
   */
  private calculateEfficiencyMetrics(
    plan: CuttingPlan, 
    request: CuttingRequest
  ): EfficiencyMetrics {
    const drumEfficiencies = plan.drumAllocations.map(allocation => {
      const drum = request.availableDrums.find(d => d.id === allocation.drumId);
      const totalLength = drum ? (drum.availableLength || drum.remainingLength || 0) : 0;
      const efficiency = this.calculateDrumEfficiency(allocation, totalLength);

      return {
        drumId: allocation.drumId,
        efficiency,
        rank: 0 // Will be set after sorting
      };
    }).sort((a, b) => b.efficiency - a.efficiency);

    // Set ranks
    drumEfficiencies.forEach((drum, index) => {
      drum.rank = index + 1;
    });

    const totalCapacity = request.availableDrums.reduce((sum, drum) => 
      sum + (drum.availableLength || drum.remainingLength || 0), 0
    );
    const totalUsed = totalCapacity - plan.totalWaste;
    const materialEfficiency = totalCapacity > 0 ? totalUsed / totalCapacity : 0;

    // Time efficiency (based on estimated vs optimal time)
    const optimalTime = request.orders.reduce((sum, order) => sum + order.quantity, 0) * 60; // 1 min per cut
    const timeEfficiency = optimalTime > 0 ? Math.min(1, optimalTime / plan.estimatedTime) : 0;

    // Process efficiency (combination of material and time)
    const processEfficiency = (materialEfficiency + timeEfficiency) / 2;

    return {
      overallEfficiency: plan.efficiency,
      drumEfficiencies,
      timeEfficiency,
      materialEfficiency,
      processEfficiency,
      benchmarkComparison: {
        industry: 0.75, // Industry average
        company: 0.80,  // Company average
        improvement: ((plan.efficiency - 0.80) / 0.80) * 100
      }
    };
  }

  /**
   * Calculate efficiency for a single drum
   */
  private calculateDrumEfficiency(allocation: DrumAllocation, totalLength: number): number {
    if (totalLength === 0) return 0;
    const usedLength = totalLength - allocation.remainingLength;
    return usedLength / totalLength;
  }

  /**
   * Analyze cutting pattern quality and characteristics
   */
  private analyzePattern(plan: CuttingPlan, request: CuttingRequest): PatternAnalysis {
    const strengths: string[] = [];
    const weaknesses: string[] = [];
    const riskFactors: RiskFactor[] = [];

    // Analyze efficiency
    if (plan.efficiency > 0.85) {
      strengths.push('Excellent material utilization');
    } else if (plan.efficiency > 0.70) {
      strengths.push('Good material utilization');
    } else {
      weaknesses.push('Low material utilization');
      riskFactors.push({
        type: 'waste',
        severity: 'high',
        description: 'High material waste detected',
        impact: (1 - plan.efficiency) * 100,
        mitigation: 'Consider alternative cutting algorithms'
      });
    }

    // Analyze waste distribution
    const totalCapacity = request.availableDrums.reduce((sum, drum) => 
      sum + (drum.availableLength || drum.remainingLength || 0), 0
    );
    const wastePercentage = totalCapacity > 0 ? (plan.totalWaste / totalCapacity) * 100 : 0;

    if (wastePercentage < 10) {
      strengths.push('Minimal waste generation');
    } else if (wastePercentage > 20) {
      weaknesses.push('High waste generation');
      riskFactors.push({
        type: 'waste',
        severity: 'medium',
        description: 'Waste exceeds acceptable threshold',
        impact: wastePercentage,
        mitigation: 'Review drum selection and cutting sequence'
      });
    }

    // Analyze time efficiency
    const avgTimePerCut = plan.estimatedTime / plan.cuttingSequence.length;
    if (avgTimePerCut < 90) { // Less than 1.5 minutes per cut
      strengths.push('Efficient cutting process');
    } else if (avgTimePerCut > 180) { // More than 3 minutes per cut
      weaknesses.push('Slow cutting process');
      riskFactors.push({
        type: 'time',
        severity: 'medium',
        description: 'Cutting process is slower than expected',
        impact: avgTimePerCut - 120,
        mitigation: 'Optimize cutting sequence and setup times'
      });
    }

    // Calculate complexity score
    const complexityScore = this.calculateComplexityScore(plan, request);
    
    if (complexityScore > 0.7) {
      weaknesses.push('High pattern complexity');
      riskFactors.push({
        type: 'complexity',
        severity: 'low',
        description: 'Pattern may be difficult to execute',
        impact: complexityScore * 100,
        mitigation: 'Provide detailed cutting instructions'
      });
    }

    // Determine pattern type
    let patternType: PatternAnalysis['patternType'] = 'good';
    if (plan.efficiency > 0.85 && wastePercentage < 10) {
      patternType = 'optimal';
    } else if (plan.efficiency < 0.60 || wastePercentage > 25) {
      patternType = 'poor';
    } else if (plan.efficiency < 0.70 || wastePercentage > 20) {
      patternType = 'suboptimal';
    }

    // Calculate quality score
    const qualityScore = this.calculateQualityScore(plan, request, riskFactors);

    // Calculate optimization potential
    const optimizationPotential = Math.max(0, (0.90 - plan.efficiency) * 100);

    return {
      patternType,
      strengths,
      weaknesses,
      optimizationPotential,
      complexityScore,
      riskFactors,
      qualityScore
    };
  }

  /**
   * Calculate pattern complexity score
   */
  private calculateComplexityScore(plan: CuttingPlan, request: CuttingRequest): number {
    let complexityFactors = 0;
    let totalFactors = 0;

    // Number of different cable types
    const cableTypes = new Set(request.orders.map(order => order.cableType));
    complexityFactors += Math.min(cableTypes.size / 5, 1); // Normalize to 0-1
    totalFactors++;

    // Number of drums used
    const drumsUsed = plan.drumAllocations.filter(allocation => allocation.cuts.length > 0).length;
    complexityFactors += Math.min(drumsUsed / request.availableDrums.length, 1);
    totalFactors++;

    // Variation in cut lengths
    const allCuts = plan.drumAllocations.flatMap(allocation => allocation.cuts);
    if (allCuts.length > 0) {
      const lengths = allCuts.map(cut => cut.length);
      const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
      const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length;
      const coefficient = Math.sqrt(variance) / avgLength;
      complexityFactors += Math.min(coefficient, 1);
      totalFactors++;
    }

    return totalFactors > 0 ? complexityFactors / totalFactors : 0;
  }

  /**
   * Calculate overall quality score
   */
  private calculateQualityScore(
    plan: CuttingPlan, 
    request: CuttingRequest, 
    riskFactors: RiskFactor[]
  ): number {
    let score = 100;

    // Deduct points for risk factors
    for (const risk of riskFactors) {
      const deduction = risk.severity === 'high' ? 20 : risk.severity === 'medium' ? 10 : 5;
      score -= deduction;
    }

    // Bonus for high efficiency
    if (plan.efficiency > 0.85) {
      score += 10;
    }

    // Bonus for low waste
    const totalCapacity = request.availableDrums.reduce((sum, drum) => 
      sum + (drum.availableLength || drum.remainingLength || 0), 0
    );
    const wastePercentage = totalCapacity > 0 ? (plan.totalWaste / totalCapacity) * 100 : 0;
    
    if (wastePercentage < 10) {
      score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Generate optimization recommendations
   */
  private generateRecommendations(
    plan: CuttingPlan, 
    request: CuttingRequest, 
    analysis: PatternAnalysis
  ): PatternRecommendation[] {
    const recommendations: PatternRecommendation[] = [];

    // Waste reduction recommendations
    if (analysis.riskFactors.some(risk => risk.type === 'waste')) {
      recommendations.push({
        type: 'waste_reduction',
        priority: 'high',
        title: 'Reduce Material Waste',
        description: 'Consider using different cutting algorithms or drum selection to minimize waste',
        expectedImprovement: analysis.optimizationPotential,
        implementationEffort: 'medium',
        costImpact: analysis.optimizationPotential * 0.5
      });
    }

    // Time optimization recommendations
    if (analysis.riskFactors.some(risk => risk.type === 'time')) {
      recommendations.push({
        type: 'time_optimization',
        priority: 'medium',
        title: 'Optimize Cutting Sequence',
        description: 'Reorganize cutting sequence to reduce setup times and improve workflow',
        expectedImprovement: 15,
        implementationEffort: 'low',
        costImpact: 5
      });
    }

    // Efficiency improvement recommendations
    if (plan.efficiency < 0.80) {
      recommendations.push({
        type: 'efficiency_improvement',
        priority: 'high',
        title: 'Improve Overall Efficiency',
        description: 'Use advanced optimization algorithms like genetic algorithm for better results',
        expectedImprovement: (0.85 - plan.efficiency) * 100,
        implementationEffort: 'high',
        costImpact: 10
      });
    }

    // Quality enhancement recommendations
    if (analysis.qualityScore < 80) {
      recommendations.push({
        type: 'quality_enhancement',
        priority: 'medium',
        title: 'Enhance Pattern Quality',
        description: 'Address identified risk factors to improve overall cutting pattern quality',
        expectedImprovement: (90 - analysis.qualityScore) / 2,
        implementationEffort: 'medium',
        costImpact: 8
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }
}