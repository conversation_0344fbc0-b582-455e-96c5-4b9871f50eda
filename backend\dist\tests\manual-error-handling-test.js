"use strict";
/**
 * Manual Test Script for Error Handling and Fallback Mechanisms
 *
 * This script can be run manually to test the actual error handling implementation
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testErrorHandling = testErrorHandling;
const client_1 = require("@prisma/client");
const data_enrichment_service_1 = __importDefault(require("../services/data-enrichment.service"));
async function testErrorHandling() {
    console.log('🧪 Starting Manual Error Handling Tests...\n');
    const prisma = new client_1.PrismaClient();
    // Test with short timeout to trigger timeout scenarios
    const dataEnrichmentService = new data_enrichment_service_1.default(prisma, {
        maxQueryTime: 100, // Very short timeout to trigger errors
        cacheEnabled: false,
        intentThreshold: 0.1
    });
    try {
        // Test 1: No intents detected
        console.log('📝 Test 1: No intents detected');
        const result1 = await dataEnrichmentService.enrichChatContext('hello world');
        console.log('Result:', {
            hasData: result1.hasData,
            fallbackReason: result1.fallbackReason,
            contextPreview: result1.databaseContext.substring(0, 100) + '...'
        });
        console.log('✅ Test 1 passed\n');
        // Test 2: Valid intents but likely timeout due to short timeout
        console.log('📝 Test 2: Valid intents with potential timeout');
        const result2 = await dataEnrichmentService.enrichChatContext('störungen heute system status');
        console.log('Result:', {
            hasData: result2.hasData,
            fallbackReason: result2.fallbackReason,
            partialFailure: result2.partialFailure,
            processingTime: result2.processingTime,
            requestId: result2.requestId,
            contextPreview: result2.databaseContext.substring(0, 100) + '...'
        });
        console.log('✅ Test 2 passed\n');
        // Test 3: Multiple intents
        console.log('📝 Test 3: Multiple intents');
        const result3 = await dataEnrichmentService.enrichChatContext('störungen versand ablängerei overview');
        console.log('Result:', {
            hasData: result3.hasData,
            dataTypes: result3.dataTypes,
            detectedIntents: result3.detectedIntents.map(i => ({ type: i.type, confidence: i.confidence })),
            fallbackReason: result3.fallbackReason,
            partialFailure: result3.partialFailure
        });
        console.log('✅ Test 3 passed\n');
        // Test 4: Empty message
        console.log('📝 Test 4: Empty message');
        const result4 = await dataEnrichmentService.enrichChatContext('');
        console.log('Result:', {
            hasData: result4.hasData,
            fallbackReason: result4.fallbackReason,
            detectedIntents: result4.detectedIntents.length
        });
        console.log('✅ Test 4 passed\n');
        console.log('🎉 All manual tests completed successfully!');
    }
    catch (error) {
        console.error('❌ Manual test failed:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
// Run the test if this file is executed directly
if (require.main === module) {
    testErrorHandling().catch(console.error);
}
