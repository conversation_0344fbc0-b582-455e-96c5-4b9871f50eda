import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StatCard } from '@/components/ui/stat-card';

/**
 * KPI-Dashboard Komponente
 * 
 * Zeigt wichtige Leistungskennzahlen als Statistik-Karten an:
 * - Gesamtleistungsindex
 * - Zielerreichungsgrad
 * - Ampelsystem für KPIs
 */
export function KPIDashboard() {
  const { t } = useTranslation();
  const [stats, setStats] = useState({
    performanceIndex: 0,
    targetAchievement: 0,
    trafficLightStatus: 'yellow', // 'green', 'yellow', 'red'
  });
  
  useEffect(() => {
    const loadData = () => {
      try {
        // Diese Daten wurden aus der Datenbank extrahiert
        // In einer echten Implementierung würden wir die Daten aus der Datenbank laden
        const currentData = {
          servicegrad: 95.2,
          ausgeliefert_lup: 1250,
          rueckstaendig: 62,
          produzierte_tonnagen: 18.5,
          mitarbeiter_std: 160.5,
          qm_angenommen: 42,
          qm_abgelehnt: 3,
          qm_offen: 5,
        };
        
        // Zielwerte
        const targets = {
          servicegrad: 96.0,
          ausgeliefert_lup: 1300,
          rueckstaendig: 50,
          produzierte_tonnagen: 19.0,
          mitarbeiter_std: 155.0,
          qm_angenommen: 45,
          qm_abgelehnt: 2,
          qm_offen: 3,
        };
        
        // Berechne den Gesamtleistungsindex (gewichteter Durchschnitt der Kennzahlen)
        // Gewichtung: Servicegrad (40%), Produktivität (30%), Qualität (30%)
        const servicegradIndex = currentData.servicegrad / targets.servicegrad;
        const produktivitaetIndex = (currentData.produzierte_tonnagen / currentData.mitarbeiter_std) / 
                                   (targets.produzierte_tonnagen / targets.mitarbeiter_std);
        const qualitaetIndex = (currentData.qm_angenommen / (currentData.qm_angenommen + currentData.qm_abgelehnt + currentData.qm_offen)) / 
                              (targets.qm_angenommen / (targets.qm_angenommen + targets.qm_abgelehnt + targets.qm_offen));
        
        const performanceIndex = (servicegradIndex * 0.4 + produktivitaetIndex * 0.3 + qualitaetIndex * 0.3) * 100;
        
        // Berechne den Zielerreichungsgrad (Durchschnitt der Zielerreichung aller Kennzahlen)
        const servicegradAchievement = (currentData.servicegrad / targets.servicegrad) * 100;
        const ausgeliefertAchievement = (currentData.ausgeliefert_lup / targets.ausgeliefert_lup) * 100;
        const rueckstaendigAchievement = (targets.rueckstaendig / currentData.rueckstaendig) * 100; // Umgekehrt, da weniger besser ist
        const produktivitaetAchievement = ((currentData.produzierte_tonnagen / currentData.mitarbeiter_std) / 
                                         (targets.produzierte_tonnagen / targets.mitarbeiter_std)) * 100;
        const qualitaetAchievement = ((currentData.qm_angenommen / (currentData.qm_angenommen + currentData.qm_abgelehnt + currentData.qm_offen)) / 
                                    (targets.qm_angenommen / (targets.qm_angenommen + targets.qm_abgelehnt + targets.qm_offen))) * 100;
        
        const targetAchievement = (servicegradAchievement + ausgeliefertAchievement + rueckstaendigAchievement + 
                                 produktivitaetAchievement + qualitaetAchievement) / 5;
        
        // Bestimme den Ampelstatus
        let trafficLightStatus = 'yellow';
        if (targetAchievement >= 95) {
          trafficLightStatus = 'green';
        } else if (targetAchievement < 85) {
          trafficLightStatus = 'red';
        }
        
        setStats({
          performanceIndex,
          targetAchievement,
          trafficLightStatus,
        });
      } catch (err) {
        // Fehler beim Laden der KPI-Daten
      }
    };
    
    loadData();
  }, []);
  
  // Funktion zur Bestimmung der Ampelfarbe
  const getTrafficLightColor = () => {
    switch (stats.trafficLightStatus) {
      case 'green': return 'bg-green-500';
      case 'yellow': return 'bg-yellow-500';
      case 'red': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };
  
  return (
    <>
      <StatCard
        title={t("performanceIndex")}
        value={stats.performanceIndex.toFixed(1)}
        description={t("performanceIndexDescription")}
        footer={t("basedOnWeightedAverage")}
      />
      <StatCard
        title={t("targetAchievement")}
        value={`${stats.targetAchievement.toFixed(1)}%`}
        description={t("targetAchievementDescription")}
        footer={t("comparedToTargets")}
      />
      <StatCard
        title={t("overallStatus")}
        value={t(stats.trafficLightStatus)}
        description={t("overallStatusDescription")}
        icon={
          <div className={`w-4 h-4 rounded-full ${getTrafficLightColor()}`}></div>
        }
        footer={t("basedOnTargetAchievement")}
      />
    </>
  );
}
