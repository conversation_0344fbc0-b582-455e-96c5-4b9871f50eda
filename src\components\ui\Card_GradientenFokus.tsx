import * as React from "react";

// Definieren der Props für die Komponente
interface GradientenFokusCardProps {
  /**
   * Der Titel der Karte
   */
  title: string;
  /**
   * Die Beschreibung der Karte
   */
  description: string;
  /**
   * Optionaler Click-Handler
   */
  onClick?: () => void;
  /**
   * Zusätzliche Klassen für das äußere div
   */
  className?: string;
}

/**
 * Eine Bento-Card mit Fokus auf Gradienten und Hover-Effekten.
 * Inspiriert von einem Design mit TailwindCSS.
 */
const GradientenFokusCard = React.forwardRef<
  HTMLDivElement,
  GradientenFokusCardProps
>(
  (
    { title, description, onClick, className = "", ...props },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={`group relative flex h-full cursor-pointer flex-col justify-between overflow-hidden rounded-xl bg-gradient-to-br from-gray-900 via-slate-900 to-rose-900/50 p-6 shadow-lg transition-all duration-500 hover:shadow-rose-500/20 ${className}`}
        onClick={onClick}
        {...props}
      >
        {/* Hover-Effekt-Gradient */}
        <div className="absolute inset-0 bg-gradient-to-tl from-rose-500/20 to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>
        
        {/* Inhalt der Karte */}
        <div className="relative z-10 flex h-full flex-col justify-between">
          <div>
            {/* Icon-Container */}
            <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-rose-500/10 text-rose-400 ring-1 ring-rose-500/20 transition-all duration-500 group-hover:bg-rose-500/20 group-hover:ring-rose-500/40">
              {/* Accessibility Icon (Dreieck mit innerem Element) */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="size-6"
              >
                <path d="m21.73 18-8-14a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z" />
                <path d="m12 9 4.5 8H7.5L12 9Z" />
              </svg>
            </div>
            
            {/* Titel */}
            <h3 className="mb-2 text-xl font-semibold tracking-tight text-white">
              {title}
            </h3>
            
            {/* Beschreibung */}
            <p className="text-sm text-slate-400">
              {description}
            </p>
          </div>
          
          {/* "Learn more"-Link mit Pfeil */}
          <div className="mt-4 flex items-center text-sm text-rose-400 transition-all duration-500">
            <span className="mr-1">Learn more</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="size-4 transition-all duration-500 group-hover:translate-x-2"
            >
              <path d="M5 12h14" />
              <path d="m12 5 7 7-7 7" />
            </svg>
          </div>
        </div>
      </div>
    );
  }
);

GradientenFokusCard.displayName = "GradientenFokusCard";

export { GradientenFokusCard, type GradientenFokusCardProps };