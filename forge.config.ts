import type { ForgeConfig } from "@electron-forge/shared-types";
import { MakerSquirrel } from "@electron-forge/maker-squirrel";
import { MakerZIP } from "@electron-forge/maker-zip";
import { MakerDeb } from "@electron-forge/maker-deb";
import { MakerRpm } from "@electron-forge/maker-rpm";
import { VitePlugin } from "@electron-forge/plugin-vite";
import * as path from "path";
import * as fs from "fs";

/**
 * Post-Make Hook: Kopiert Frontend (dist) und Backend (dist, node_modules, database, prisma)
 * in das erzeugte Ausgabeverzeichnis (resources/app).
 * Dies sorgt für ein vollständiges, portables Paket.
 */
function copyRecursiveSync(src: string, dest: string) {
  if (!fs.existsSync(src)) return;
  const stat = fs.statSync(src);
  if (stat.isDirectory()) {
    if (!fs.existsSync(dest)) fs.mkdirSync(dest, { recursive: true });
    for (const item of fs.readdirSync(src)) {
      const s = path.join(src, item);
      const d = path.join(dest, item);
      copyRecursiveSync(s, d);
    }
  } else {
    const dir = path.dirname(dest);
    if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
    fs.copyFileSync(src, dest);
  }
}

const config: ForgeConfig = {
  // electron-packager Optionen
  packagerConfig: {
    // ASAR wieder aktiv, aber native Module/Prisma-Engines werden gezielt entpackt
    // Dadurch bleiben *.node-Binaries und Engines außerhalb des ASAR
    asar: {
      unpack: "**/{better-sqlite3,sqlite3,@prisma/engines}/**"
    },
    // Ordner ignorieren, die nicht ins Paket sollen
    ignore: [
      /\/portable-build(-new)?\//,
      /\/out\//,
      /\/dist\/main\/.*\.map$/,
      /\/dist\/.*\.map$/
    ]
  },
  rebuildConfig: {},
  makers: [
    // TEMP: nur ZIP für Windows, um den Build zu stabilisieren
    new MakerZIP({}),
    // new MakerSquirrel({}), // später wieder aktivieren
    new MakerRpm({}),
    new MakerDeb({}),
  ],
  plugins: [
    new VitePlugin({
      build: [
        {
          entry: "src/main/electron-main.ts",
          config: "vite.main.config.ts",
          target: "main",
        },
        {
          entry: "src/main/preload.ts",
          config: "vite.preload.config.ts",
          target: "preload",
        },
      ],
      renderer: [
        {
          name: "main_window",
          config: "vite.renderer.config.mts",
        },
      ],
    }),
  ],
  hooks: {
    // Vor dem Packen: Backend-Produktions-Dependencies sicherstellen (ohne dev)
    // und native Module für Electron neu bauen
    prePackage: async () => {
      const { execSync } = await import("child_process");
      try {
        execSync("npm ci --omit=dev", { cwd: "backend", stdio: "inherit", windowsHide: true });
      } catch {
        // ignore, wir versuchen trotzdem weiter zu packen
      }
      try {
        // Zwingendes Rebuild der nativen Module (prod-Typen) für aktuelle Electron-Version
        execSync("npx electron-rebuild --force --types prod", { stdio: "inherit", windowsHide: true });
      } catch {
        // nicht fatal, Forge versucht dennoch weiter
      }
    },
    // Nach dem Make: dist und backend in resources/app injizieren
    postMake: async (_forgeConfig, results) => {
      const projectRoot = process.cwd();
      const frontendDist = path.join(projectRoot, "dist");
      const backendRoot = path.join(projectRoot, "backend");

      for (const result of results) {
        // Versuche, einen entpackten App-Ordner zu finden: in der Regel der Ordner, der die .exe enthält
        let candidateDirs: string[] = [];

        // 1) Prüfe artifacts auf .exe und leite das Basisverzeichnis ab
        for (const artifact of result.artifacts) {
          if (artifact.toLowerCase().endsWith(".exe")) {
            candidateDirs.push(path.dirname(artifact));
          } else if (fs.existsSync(artifact) && fs.statSync(artifact).isDirectory()) {
            // Manches Maker-Target liefert einen Ordner; evtl. enthält er resources/
            candidateDirs.push(artifact);
          }
        }

        // Eindeutige Kandidaten
        candidateDirs = Array.from(new Set(candidateDirs));

        for (const baseDir of candidateDirs) {
          const resourcesDir = path.join(baseDir, "resources");
          const appDir = path.join(resourcesDir, "app");
          const backendDest = path.join(appDir, "backend");

          if (!fs.existsSync(resourcesDir)) {
            // Kein resources-Verzeichnis -> vermutlich ZIP/Installer, hier können wir nicht direkt injizieren
            continue;
          }
          if (!fs.existsSync(appDir)) {
            fs.mkdirSync(appDir, { recursive: true });
          }

          // 1) Frontend dist -> resources/app
          if (fs.existsSync(frontendDist)) {
            // Frontend-Inhalte (außer backend) ersetzen
            for (const entry of fs.readdirSync(appDir)) {
              if (entry.toLowerCase() === "backend") continue;
              const p = path.join(appDir, entry);
              fs.rmSync(p, { recursive: true, force: true });
            }
            for (const entry of fs.readdirSync(frontendDist)) {
              copyRecursiveSync(path.join(frontendDist, entry), path.join(appDir, entry));
            }
          }

          // 2) Backend-Struktur kopieren
          const backendDist = path.join(backendRoot, "dist");
          if (fs.existsSync(backendDist)) {
            copyRecursiveSync(backendDist, path.join(backendDest, "dist"));
          }

          const backendNodeModules = path.join(backendRoot, "node_modules");
          if (fs.existsSync(backendNodeModules)) {
            copyRecursiveSync(backendNodeModules, path.join(backendDest, "node_modules"));
          }

          const backendPkg = path.join(backendRoot, "package.json");
          if (fs.existsSync(backendPkg)) {
            copyRecursiveSync(backendPkg, path.join(backendDest, "package.json"));
          }

          const backendDb = path.join(backendRoot, "database");
          if (fs.existsSync(backendDb)) {
            copyRecursiveSync(backendDb, path.join(backendDest, "database"));
          }

          const backendPrisma = path.join(backendRoot, "prisma");
          if (fs.existsSync(backendPrisma)) {
            copyRecursiveSync(backendPrisma, path.join(backendDest, "prisma"));
          }

          // 3) Statische Ressourcen aus Projektwurzel injizieren
          const imagesSrc = path.join(projectRoot, "images");
          if (fs.existsSync(imagesSrc)) {
            copyRecursiveSync(imagesSrc, path.join(appDir, "images"));
          }

          const assetsSrc = path.join(projectRoot, "assets");
          if (fs.existsSync(assetsSrc)) {
            copyRecursiveSync(assetsSrc, path.join(appDir, "assets"));
          }

          // eslint-disable-next-line no-console
          console.log(`[forge postMake] Injected frontend+backend+resources into: ${appDir}`);
        }
      }
    },
  },
};

export default config;
