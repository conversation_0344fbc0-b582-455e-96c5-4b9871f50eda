import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StatCard } from '@/components/ui/stat-card';

/**
 * Logistik-Statistiken Komponente
 * 
 * Zeigt wichtige Logistik-Kennzahlen als Statistik-Karten an:
 * - Direktverladungsquote
 * - Durchschnittliches Gewicht pro Colli
 * - Elefanten-Quote
 */
export function LogisticsStats() {
  const { t } = useTranslation();
  const [stats, setStats] = useState({
    directLoadingRatio: 0,
    avgWeightPerColli: 0,
    elephantRatio: 0,
    previousDirectLoadingRatio: 0,
    previousAvgWeightPerColli: 0,
    previousElephantRatio: 0,
  });
  
  useEffect(() => {
    const loadData = () => {
      try {
        // Diese Daten wurden aus der Datenbank extrahiert
        // In einer echten Implementierung würden wir die Daten aus der Datenbank laden
        const currentData = {
          direktverladung_kiaa: 45,
          umschlag: 120,
          kg_pro_colli: 28.4,
          elefanten: 3,
        };
        
        const previousData = {
          direktverladung_kiaa: 42,
          umschlag: 115,
          kg_pro_colli: 27.9,
          elefanten: 2,
        };
        
        // Berechne die Kennzahlen
        const directLoadingRatio = (currentData.direktverladung_kiaa / currentData.umschlag) * 100;
        const avgWeightPerColli = currentData.kg_pro_colli;
        const elephantRatio = (currentData.elefanten / currentData.umschlag) * 100;
        
        const previousDirectLoadingRatio = (previousData.direktverladung_kiaa / previousData.umschlag) * 100;
        const previousAvgWeightPerColli = previousData.kg_pro_colli;
        const previousElephantRatio = (previousData.elefanten / previousData.umschlag) * 100;
        
        setStats({
          directLoadingRatio,
          avgWeightPerColli,
          elephantRatio,
          previousDirectLoadingRatio,
          previousAvgWeightPerColli,
          previousElephantRatio,
        });
      } catch (err) {
        // Error handling without console log
      }
    };
    
    loadData();
  }, []);
  
  // Berechne die Trends
  const directLoadingTrend = stats.directLoadingRatio > stats.previousDirectLoadingRatio ? 'up' : 
                            stats.directLoadingRatio < stats.previousDirectLoadingRatio ? 'down' : 'neutral';
                       
  const weightTrend = stats.avgWeightPerColli > stats.previousAvgWeightPerColli ? 'up' : 
                     stats.avgWeightPerColli < stats.previousAvgWeightPerColli ? 'down' : 'neutral';
                     
  const elephantTrend = stats.elephantRatio > stats.previousElephantRatio ? 'up' : 
                       stats.elephantRatio < stats.previousElephantRatio ? 'down' : 'neutral';
  
  // Berechne die Trendwerte in Prozent
  const directLoadingTrendValue = stats.previousDirectLoadingRatio > 0 ? 
    `${((stats.directLoadingRatio - stats.previousDirectLoadingRatio) / stats.previousDirectLoadingRatio * 100).toFixed(1)}%` : '0%';
    
  const weightTrendValue = stats.previousAvgWeightPerColli > 0 ? 
    `${((stats.avgWeightPerColli - stats.previousAvgWeightPerColli) / stats.previousAvgWeightPerColli * 100).toFixed(1)}%` : '0%';
    
  const elephantTrendValue = stats.previousElephantRatio > 0 ? 
    `${((stats.elephantRatio - stats.previousElephantRatio) / stats.previousElephantRatio * 100).toFixed(1)}%` : '0%';
  
  return (
    <>
      <StatCard
        title={t("directLoadingRatio")}
        value={`${stats.directLoadingRatio.toFixed(1)}%`}
        description={t("directLoadingRatioDescription")}
        trend={directLoadingTrend}
        trendValue={directLoadingTrendValue}
        footer={t("comparedToPreviousDay")}
      />
      <StatCard
        title={t("avgWeightPerColli")}
        value={`${stats.avgWeightPerColli.toFixed(1)} kg`}
        description={t("avgWeightPerColliDescription")}
        trend={weightTrend}
        trendValue={weightTrendValue}
        footer={t("comparedToPreviousDay")}
      />
      <StatCard
        title={t("elephantRatio")}
        value={`${stats.elephantRatio.toFixed(1)}%`}
        description={t("elephantRatioDescription")}
        trend={elephantTrend}
        trendValue={elephantTrendValue}
        footer={t("comparedToPreviousDay")}
      />
    </>
  );
}
