"use strict";
/**
 * Simple validation script for performance optimization features
 *
 * This script validates that all performance monitoring and optimization
 * components are working correctly without requiring the full Jest setup.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const data_cache_service_1 = require("../services/data-cache.service");
const performance_monitoring_service_1 = require("../services/performance-monitoring.service");
async function validatePerformanceOptimization() {
    console.log('🚀 Starting Performance Optimization Validation...\n');
    // Test 1: Performance Monitoring Service
    console.log('📊 Testing Performance Monitoring Service...');
    const performanceMonitor = new performance_monitoring_service_1.PerformanceMonitoringService();
    // Record some test metrics
    performanceMonitor.recordQueryPerformance('stoerungen', 150, true, {
        dataSize: 1024,
        cacheHit: false,
        retryCount: 0
    });
    performanceMonitor.recordIntentRecognition('Wie viele Störungen haben wir?', ['stoerungen'], 0.85, 50, ['störungen'], 0.9);
    performanceMonitor.recordResponseTime('/api/chat/enhanced', true, 1500, 800, 700, 2048);
    const stats = performanceMonitor.getPerformanceStats();
    console.log(`✅ Performance stats recorded: ${stats.totalRequests} requests, ${stats.averageResponseTime.toFixed(0)}ms avg`);
    const alerts = performanceMonitor.getPerformanceAlerts();
    console.log(`✅ Performance alerts: ${alerts.length} alerts generated`);
    // Test 2: Data Cache Service
    console.log('\n💾 Testing Data Cache Service...');
    const cacheService = new data_cache_service_1.DataCacheService({
        maxSize: 1024 * 1024, // 1MB for testing
        maxEntries: 10,
        defaultTTL: 30 * 1000, // 30 seconds
        cleanupInterval: 5 * 1000,
        enableMetrics: true
    });
    const testIntents = [{
            type: 'stoerungen',
            keywords: ['störungen', 'status'],
            confidence: 0.8
        }];
    const mockResults = [{
            dataType: 'stoerungen',
            data: { statistics: { total: 5, active: 2 } },
            summary: 'Test data',
            timestamp: new Date(),
            success: true,
            queryTime: 500,
            intent: 'stoerungen'
        }];
    // Test cache miss
    const cachedResults1 = await cacheService.getCachedQueryResults(testIntents);
    console.log(`✅ Cache miss test: ${cachedResults1 === null ? 'PASS' : 'FAIL'}`);
    // Cache the results
    await cacheService.cacheQueryResults(testIntents, mockResults);
    // Test cache hit
    const cachedResults2 = await cacheService.getCachedQueryResults(testIntents);
    console.log(`✅ Cache hit test: ${cachedResults2 !== null ? 'PASS' : 'FAIL'}`);
    const cacheStats = cacheService.getStats();
    console.log(`✅ Cache stats: ${cacheStats.totalEntries} entries, ${(cacheStats.hitRate * 100).toFixed(1)}% hit rate`);
    // Test 3: Cache Performance Improvement
    console.log('\n⚡ Testing Cache Performance Improvement...');
    const iterations = 100;
    const testData = Array.from({ length: iterations }, (_, i) => ({
        intents: [{
                type: 'dispatch',
                keywords: [`test${i}`],
                confidence: 0.7
            }],
        results: [{
                dataType: 'dispatch',
                data: { test: i },
                summary: `Test ${i}`,
                timestamp: new Date(),
                success: true,
                queryTime: 100,
                intent: 'dispatch'
            }]
    }));
    // First round - cache misses
    const startTime1 = Date.now();
    for (const { intents } of testData.slice(0, 10)) {
        await cacheService.getCachedQueryResults(intents);
    }
    const missTime = Date.now() - startTime1;
    // Cache the data
    for (const { intents, results } of testData.slice(0, 10)) {
        await cacheService.cacheQueryResults(intents, results);
    }
    // Second round - cache hits
    const startTime2 = Date.now();
    for (const { intents } of testData.slice(0, 10)) {
        await cacheService.getCachedQueryResults(intents);
    }
    const hitTime = Date.now() - startTime2;
    console.log(`✅ Performance improvement: Cache hits ${((missTime - hitTime) / missTime * 100).toFixed(1)}% faster`);
    console.log(`   - Cache misses: ${missTime}ms for 10 operations`);
    console.log(`   - Cache hits: ${hitTime}ms for 10 operations`);
    // Test 4: Cache Optimization
    console.log('\n🔧 Testing Cache Optimization...');
    // Fill cache with test data
    for (const { intents, results } of testData) {
        await cacheService.cacheQueryResults(intents, results);
    }
    const preOptimizationStats = cacheService.getStats();
    console.log(`✅ Pre-optimization: ${preOptimizationStats.totalEntries} entries`);
    const optimization = cacheService.optimizeCache();
    const postOptimizationStats = cacheService.getStats();
    console.log(`✅ Optimization results: ${optimization.evicted} evicted, ${optimization.optimizations.length} optimizations`);
    console.log(`✅ Post-optimization: ${postOptimizationStats.totalEntries} entries`);
    // Test 5: Performance Monitoring Integration
    console.log('\n🔗 Testing Performance Monitoring Integration...');
    // Simulate some performance scenarios
    const scenarios = [
        { type: 'stoerungen', time: 120, success: true, cacheHit: true },
        { type: 'dispatch', time: 250, success: true, cacheHit: false },
        { type: 'cutting', time: 180, success: true, cacheHit: true },
        { type: 'stoerungen', time: 3500, success: false, cacheHit: false }, // Slow/failed
    ];
    scenarios.forEach((scenario, index) => {
        performanceMonitor.recordQueryPerformance(scenario.type, scenario.time, scenario.success, {
            cacheHit: scenario.cacheHit,
            dataSize: 1024,
            retryCount: scenario.success ? 0 : 2
        });
    });
    const finalStats = performanceMonitor.getPerformanceStats();
    const finalAlerts = performanceMonitor.getPerformanceAlerts();
    console.log(`✅ Final performance stats:`);
    console.log(`   - Total requests: ${finalStats.totalRequests}`);
    console.log(`   - Success rate: ${(finalStats.successRate * 100).toFixed(1)}%`);
    console.log(`   - Cache hit rate: ${(finalStats.cacheHitRate * 100).toFixed(1)}%`);
    console.log(`   - Average response time: ${finalStats.averageResponseTime.toFixed(0)}ms`);
    console.log(`   - Alerts generated: ${finalAlerts.length}`);
    // Test 6: Frequent Patterns Analysis
    console.log('\n📈 Testing Frequent Patterns Analysis...');
    const patterns = cacheService.getFrequentPatterns();
    console.log(`✅ Identified ${patterns.length} usage patterns`);
    patterns.forEach(pattern => {
        console.log(`   - ${pattern.pattern}: ${pattern.frequency} occurrences, ${pattern.cacheEffectiveness.toFixed(2)} effectiveness`);
    });
    // Test 7: Memory Management
    console.log('\n🧠 Testing Memory Management...');
    const initialMemory = process.memoryUsage();
    // Create many cache entries to test memory management
    for (let i = 0; i < 200; i++) {
        const largeData = [{
                dataType: 'stoerungen',
                data: { largeData: 'x'.repeat(1000), index: i },
                summary: `Large data ${i}`,
                timestamp: new Date(),
                success: true,
                queryTime: 100,
                intent: 'stoerungen'
            }];
        await cacheService.cacheQueryResults([{
                type: 'stoerungen',
                keywords: [`large${i}`],
                confidence: 0.8
            }], largeData);
    }
    const finalMemory = process.memoryUsage();
    const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
    console.log(`✅ Memory management test:`);
    console.log(`   - Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    console.log(`   - Cache entries: ${cacheService.getStats().totalEntries}`);
    console.log(`   - Cache size: ${(cacheService.getStats().totalSize / 1024 / 1024).toFixed(2)}MB`);
    // Cleanup
    cacheService.destroy();
    performanceMonitor.destroy();
    console.log('\n🎉 Performance Optimization Validation Complete!');
    console.log('✅ All tests passed successfully');
    return true;
}
// Run validation if this file is executed directly
if (require.main === module) {
    validatePerformanceOptimization()
        .then(() => {
        console.log('\n✅ Validation completed successfully');
        process.exit(0);
    })
        .catch((error) => {
        console.error('\n❌ Validation failed:', error);
        process.exit(1);
    });
}
exports.default = validatePerformanceOptimization;
