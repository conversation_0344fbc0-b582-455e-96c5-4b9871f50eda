import React, { useEffect, useState } from 'react';
import { Activity, CheckCircle, AlertTriangle, Clock, Database } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { SubtlePatternCard } from '@/components/ui/Card_SubtlePattern';
import { apiService } from '@/services/api.service';

interface WorkflowStatusProps {
  className?: string;
}

interface WorkflowHealth {
  totalWorkflows: number;
  activeWorkflows: number;
  runningWorkflows: number;
  failedWorkflows: number;
  overallHealth: 'healthy' | 'warning' | 'critical';
}

export function WorkflowStatus({ className }: WorkflowStatusProps) {
  const [health, setHealth] = useState<WorkflowHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHealth = async () => {
      try {
        setLoading(true);
        setError(null);

        const result = await apiService.get('/workflows/health');
        setHealth(result as WorkflowHealth);
      } catch (err) {
        console.error('Failed to fetch workflow health:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchHealth();

    // Refresh every 30 seconds
    const interval = setInterval(fetchHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  const getHealthIcon = (healthStatus: string) => {
    switch (healthStatus) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Activity className="h-5 w-5 text-gray-400" />;
    }
  };

  const getHealthBadge = (healthStatus: string) => {
    switch (healthStatus) {
      case 'healthy':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Gesund</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warnung</Badge>;
      case 'critical':
        return <Badge variant="destructive">Kritisch</Badge>;
      default:
        return <Badge variant="outline">Unbekannt</Badge>;
    }
  };

  if (loading) {
    return (
      <div className={className}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold">Workflow-Status</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <SubtlePatternCard
              key={i}
              title="Laden..."
              value={<Clock className="h-6 w-6 animate-spin text-blue-600" />}
              subtitle="Daten werden geladen..."
              icon={<Database className="h-5 w-5" />}
            />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold">Workflow-Status</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <SubtlePatternCard
            title="Fehler"
            value="--"
            subtitle={`Fehler: ${error}`}
            icon={<AlertTriangle className="h-5 w-5" />}
            valueClassName="text-red-600"
          />
        </div>
      </div>
    );
  }

  if (!health) {
    return (
      <div className={className}>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold">Workflow-Status</h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <SubtlePatternCard
            title="Keine Daten"
            value="--"
            subtitle="Keine Workflow-Daten verfügbar"
            icon={<Database className="h-5 w-5" />}
            valueClassName="text-gray-500"
          />
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Header mit Status-Badge */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          {getHealthIcon(health.overallHealth)}
          <h2 className="text-lg font-semibold">Workflow-Status</h2>
        </div>
        {getHealthBadge(health.overallHealth)}
      </div>

      {/* Grid mit einzelnen Status-Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <SubtlePatternCard
          title="Gesamt"
          value={health.totalWorkflows}
          subtitle="Alle Workflows"
          icon={<Database className="h-5 w-5" />}
          iconColor="#3b82f6"
          valueClassName="text-blue-600"
        />

        <SubtlePatternCard
          title="Aktiv"
          value={health.activeWorkflows}
          subtitle="Aktive Workflows"
          icon={<CheckCircle className="h-5 w-5" />}
          iconColor="#10b981"
          valueClassName="text-green-600"
        />

        <SubtlePatternCard
          title="Laufend"
          value={health.runningWorkflows}
          subtitle="Laufende Workflows"
          icon={<Clock className="h-5 w-5" />}
          iconColor="#8b5cf6"
          valueClassName="text-blue-600"
        />

        <SubtlePatternCard
          title="Fehler"
          value={health.failedWorkflows}
          subtitle="Fehlgeschlagene Workflows"
          icon={<AlertTriangle className="h-5 w-5" />}
          iconColor="#ef4444"
          valueClassName="text-red-600"
        />
      </div>
    </div>
  );
}