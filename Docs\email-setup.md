# E-Mail-Setup für Störungsmeldungen

Diese Anleitung erklärt, wie Sie die automatische E-Mail-Funktionalität für Störungsmeldungen einrichten.

## Übersicht

Das System unterstützt den automatischen Versand von E-Mails beim E<PERSON><PERSON>n von Störungen. Die E-Mails werden über SMTP versendet und können an zugewiesene Personen gesendet werden.

## Konfiguration

### 1. SMTP-Einstellungen in der .env-Datei

Öffnen Sie die Datei `backend/.env` und konfigurieren Sie die folgenden E-Mail-Einstellungen:

```env
# E-Mail SMTP Konfiguration
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=ihr-passwort
EMAIL_TIMEOUT=30000
```

### 2. Outlook/Hotmail-Konfiguration (Empfohlen)

Für Outlook.com/Hotmail verwenden Sie folgende Einstellungen:

```env
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=ihr-outlook-passwort
```

**Wichtige Hinweise für Outlook:**
- Verwenden Sie Ihr normales Outlook-Passwort
- Stellen Sie sicher, dass "Weniger sichere Apps" in den Outlook-Einstellungen aktiviert ist
- Bei Problemen können Sie auch ein App-Passwort erstellen

### 3. Gmail-Konfiguration (Alternative)

Für Gmail benötigen Sie ein **App-Passwort** (nicht Ihr normales Gmail-Passwort):

1. Gehen Sie zu [Google Account Settings](https://myaccount.google.com/)
2. Wählen Sie "Sicherheit" → "2-Schritt-Verifizierung"
3. Scrollen Sie nach unten zu "App-Passwörter"
4. Erstellen Sie ein neues App-Passwort für "Mail"
5. Verwenden Sie dieses 16-stellige Passwort als `SMTP_PASS`

**Gmail-Einstellungen:**
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=abcd-efgh-ijkl-mnop  # 16-stelliges App-Passwort
```

### 4. Andere E-Mail-Anbieter

Für andere SMTP-Server passen Sie die Einstellungen entsprechend an:

- **Host:** SMTP-Server Ihres Anbieters
- **Port:** Meist 587 (STARTTLS) oder 465 (SSL)
- **Secure:** `true` für Port 465, `false` für Port 587

## Funktionsweise

### Automatischer E-Mail-Versand

1. **Störung erstellen:** Beim Erstellen einer Störung im Frontend
2. **E-Mail-Protokoll aktivieren:** Checkbox "Störungsprotokoll per E-Mail versenden" aktivieren
3. **Person zuweisen:** Eine E-Mail-Adresse im Feld "Zugewiesen an" eingeben
4. **Automatischer Versand:** Das Backend sendet automatisch eine E-Mail an die zugewiesene Person

### E-Mail-Template

Die E-Mail enthält:
- **Betreff:** "Neue Störung: [Titel der Störung]"
- **Inhalt:** Alle wichtigen Störungsinformationen
- **Format:** HTML und Text-Version
- **Template-Dateien:**
  - `backend/src/templates/stoerung-notification.html`
  - `backend/src/templates/stoerung-notification.txt`

## Fehlerbehebung

### Häufige Probleme

#### 1. "Authentication Required" Fehler
```
code: 'EENVELOPE',
response: '530-5.7.0 Authentication Required'
```

**Lösung:**
- Überprüfen Sie `SMTP_USER` und `SMTP_PASS`
- Bei Gmail: Verwenden Sie ein App-Passwort
- Bei Outlook: Aktivieren Sie "Weniger sichere Apps" oder verwenden Sie OAuth2

#### 2. "Connection Timeout" Fehler

**Lösung:**
- Erhöhen Sie `EMAIL_TIMEOUT` auf 60000 (60 Sekunden)
- Überprüfen Sie Firewall-Einstellungen
- Testen Sie die Verbindung mit einem anderen SMTP-Server

#### 3. "Invalid Login" Fehler

**Lösung:**
- Überprüfen Sie die E-Mail-Adresse und das Passwort
- Stellen Sie sicher, dass 2FA aktiviert ist (bei Gmail)
- Verwenden Sie das korrekte App-Passwort

### Verbindung testen

Sie können die SMTP-Verbindung testen, indem Sie eine Test-Störung erstellen:

1. Starten Sie den Server: `npm run dev`
2. Erstellen Sie eine neue Störung
3. Aktivieren Sie "Störungsprotokoll per E-Mail versenden"
4. Geben Sie eine gültige E-Mail-Adresse ein
5. Speichern Sie die Störung
6. Überprüfen Sie die Server-Logs auf Erfolg oder Fehlermeldungen

### Server-Logs überprüfen

Die E-Mail-Logs finden Sie in der Konsole:

```bash
# Erfolgreiche E-Mail
✅ SMTP-Verbindung erfolgreich
Störungs-E-Mail erfolgreich versendet an: <EMAIL>

# Fehlgeschlagene E-Mail
❌ SMTP-Verbindung fehlgeschlagen: [Fehlerdetails]
Störungs-E-Mail konnte nicht versendet werden
```

## Sicherheitshinweise

1. **App-Passwörter verwenden:** Niemals Ihr Haupt-E-Mail-Passwort verwenden
2. **.env-Datei schützen:** Die .env-Datei niemals in die Versionskontrolle committen
3. **Umgebungsvariablen:** In Produktionsumgebungen echte Umgebungsvariablen verwenden
4. **Verschlüsselung:** STARTTLS (Port 587) oder SSL (Port 465) verwenden

## Support

Bei Problemen:
1. Überprüfen Sie die Server-Logs
2. Testen Sie die SMTP-Einstellungen mit einem E-Mail-Client
3. Konsultieren Sie die Dokumentation Ihres E-Mail-Anbieters
4. Überprüfen Sie Firewall- und Netzwerkeinstellungen