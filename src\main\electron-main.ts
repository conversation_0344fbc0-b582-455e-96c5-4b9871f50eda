import { app, BrowserWindow, ipc<PERSON>ain } from 'electron';
import path from 'path';
import fs from 'fs';
import { spawn, ChildProcessWithoutNullStreams } from 'child_process';
import { initializeConfig, registerConfigHandlers } from '@/helpers/ipc/config/config-handlers';

/**
 * SIMPLE FILE LOGGING FOR EARLY CRASH DIAGNOSIS IN PORTABLE BUILD
 * - Writes to a log file next to the executable in production (process.resourcesPath/..)
 * - Writes to project root in development (process.cwd())
 */
const LOG_FILE = (() => {
  try {
    const base = app?.isPackaged ? path.join(process.resourcesPath, '..') : process.cwd();
    return path.join(base, 'electron-main.log');
  } catch {
    return path.join(process.cwd(), 'electron-main.log');
  }
})();

function logLine(msg: string, data?: unknown) {
  try {
    const line = `[${new Date().toISOString()}] ${msg}${data !== undefined ? ' ' + JSON.stringify(data) : ''}\n`;
    fs.appendFileSync(LOG_FILE, line);
  } catch {
    // ignore
  }
}

// Early boot logs
logLine('=== Electron main boot ===', {
  isPackaged: app?.isPackaged,
  cwd: process.cwd(),
  resourcesPath: process.resourcesPath
});

// Registry-Typen
type WorkflowId = 'bestand';

type WorkflowDefinition = {
  id: WorkflowId;
  name: string;
  scriptPath: string;
  env?: Record<string, string>;
  logGlob?: string;
};

type WorkflowStatus = 'idle' | 'running' | 'success' | 'error';

type WorkflowRuntime = {
  proc: ChildProcessWithoutNullStreams | null;
  status: WorkflowStatus;
  lastLogFilePath: string | null;
  startedAt: number | null;
  finishedAt: number | null;
  buffer: string[];
};

// Minimale Registry
const registry: Record<WorkflowId, WorkflowDefinition> = {
  bestand: {
    id: 'bestand',
    name: 'Bestand',
    scriptPath: path.join(process.cwd(), 'backend', 'scripts', 'workflows', 'Bestand', 'Bestand-Workflow.py'),
    env: {},
    logGlob: path.join(process.cwd(), 'backend', 'workflows', 'logs'),
  },
};

// Laufzeit-Tracker
const runtimeState: Record<WorkflowId, WorkflowRuntime> = {
  bestand: {
    proc: null,
    status: 'idle',
    lastLogFilePath: null,
    startedAt: null,
    finishedAt: null,
    buffer: [],
  },
};

let mainWindow: BrowserWindow | null = null;

// Backend - sowohl integriert als auch separater Prozess
let backendServer: any = null;
let backendProc: ChildProcessWithoutNullStreams | null = null;

// Backend für portable Build - verwendet das gebaute Backend
async function startPortableBackend() {
  try {
    if (backendProc) return;
    
    // Prüfe ob wir im portable Build sind
    const isPortable = !fs.existsSync(path.join(process.cwd(), 'src'));

    // Robuste Basis für Pfade:
    // - In Produktion: process.resourcesPath (z.B. .../resources)
    // - In Dev: process.cwd()
    const basePath = app.isPackaged ? process.resourcesPath : process.cwd();

    if (isPortable) {
      // Portable Build: Starte das gebaute Backend
      // Bevorzugter Pfad relativ zu resources/ (eine Ebene hoch zu portable-root)
      const preferredBackendRoot = path.join(basePath, '..', 'backend');
      const fallbackBackendRoot = path.join(process.cwd(), 'backend');

      const backendRoot = fs.existsSync(preferredBackendRoot) ? preferredBackendRoot : fallbackBackendRoot;
      const backendPath = path.join(backendRoot, 'dist', 'server.js');
      const dbFilePath = path.join(backendRoot, 'database', 'sfm_dashboard.db');

      if (fs.existsSync(backendPath)) {
        console.log('🚀 Starting portable backend from:', backendPath);
        backendProc = spawn('node', [backendPath], {
          cwd: backendRoot,
          env: {
            ...process.env,
            NODE_ENV: 'production',
            DATABASE_URL: `file:${dbFilePath}`
          },
          shell: false,
          windowsHide: true,
        });
      } else {
        console.warn('⚠️ Backend build not found at', backendPath, '- using integrated backend');
        return startIntegratedBackend();
      }
    } else {
      // Development: Verwende npm run dev
      backendProc = spawn(process.platform === 'win32' ? 'cmd' : 'bash',
        [process.platform === 'win32' ? '/c' : '-lc', 'cd backend && npm run dev'],
        {
          cwd: process.cwd(),
          env: { ...process.env },
          shell: false,
          windowsHide: true,
        }
      );
    }

    backendProc.stdout.on('data', (d) => {
      const line = d.toString();
      mainWindow?.webContents.send('backend:log', line);
    });

    backendProc.stderr.on('data', (d) => {
      const line = d.toString();
      mainWindow?.webContents.send('backend:log', line);
    });

    backendProc.on('close', (code) => {
      mainWindow?.webContents.send('backend:exit', code);
      backendProc = null;
    });
  } catch (error) {
    console.error('Original Backend-Start-Fehler:', error);
  }
}

// Backend als separaten Prozess im Development starten (Original-Backend)
async function startOriginalBackend() {
  try {
    if (backendProc) return;

    // Development: Verwende npm run dev im backend-Ordner
    backendProc = spawn(process.platform === 'win32' ? 'cmd' : 'bash',
      [process.platform === 'win32' ? '/c' : '-lc', 'cd backend && npm run dev'],
      {
        cwd: process.cwd(),
        env: { ...process.env },
        shell: false,
        windowsHide: true,
      }
    );

    backendProc.stdout.on('data', (d) => {
      const line = d.toString();
      mainWindow?.webContents.send('backend:log', line);
    });

    backendProc.stderr.on('data', (d) => {
      const line = d.toString();
      mainWindow?.webContents.send('backend:log', line);
    });

    backendProc.on('close', (code) => {
      mainWindow?.webContents.send('backend:exit', code);
      backendProc = null;
    });
  } catch (error) {
    console.error('Original Backend-Start-Fehler:', error);
  }
}

// Backend direkt in Electron starten (für portable Build)
async function startIntegratedBackend() {
  try {
    if (backendServer) return;
    
    console.log('🚀 Starting integrated backend...');
    
    // Importiere das Backend direkt
    const express = require('express');
    const cors = require('cors');
    console.log('✅ Express and CORS loaded');
    
    // Optional dependencies - fallback if not available
    // Optional dependencies with proper typing (loaded dynamically)
    let bcrypt: typeof import('bcryptjs') | null = null;
    let jwt: typeof import('jsonwebtoken') | null = null;
    let PrismaClientCtor: typeof import('@prisma/client').PrismaClient | null = null;
    try {
      bcrypt = require('bcryptjs') as typeof import('bcryptjs');
      jwt = require('jsonwebtoken') as typeof import('jsonwebtoken');
      console.log('✅ Auth dependencies loaded');
    } catch (error) {
      console.warn('⚠️ Auth dependencies not available:', error);
    }
    
    try {
      PrismaClientCtor = require('@prisma/client').PrismaClient as typeof import('@prisma/client').PrismaClient;
      console.log('✅ Prisma loaded');
    } catch (error) {
      console.warn('⚠️ Prisma not available:', error);
    }
    
    const serverApp = express();
    const port = 3001;
    const JWT_SECRET = 'your-secret-key-here'; // In Produktion aus Umgebungsvariable
    
    // Basis-Middleware
    serverApp.use(cors({
      origin: true,
      credentials: true
    }));
    serverApp.use(express.json());
    
    // Prisma-Client für SQLite (optional)
    let prisma: InstanceType<typeof PrismaClientCtor> | null = null;
    if (PrismaClientCtor) {
      try {
        prisma = new PrismaClientCtor({
          datasources: {
            db: {
              url: `file:${path.join(process.cwd(), 'database', 'sfm_dashboard.db')}`
            }
          }
        });
        console.log('✅ Prisma client initialized');
      } catch (error) {
        console.warn('⚠️ Prisma initialization failed:', error);
      }
    }
    
    // Health-Check
    serverApp.get('/api/health', (req: any, res: any) => {
      res.json({ status: 'ok', message: 'Backend läuft!' });
    });
    
    serverApp.get('/api/performance/health', (req: any, res: any) => {
      res.json({ status: 'ok', message: 'Performance API läuft!' });
    });
    
    // Auth-Routen
    serverApp.post('/api/auth/login', async (req: any, res: any) => {
      try {
        const { username, password } = req.body;
        console.log(`🔐 Login attempt: ${username}`);
        
        // Für Demo: Standard-Benutzer
        if (username === 'jozi1' && password === 'password') {
          let token = 'demo-token-12345'; // Fallback token
          
          if (jwt) {
            try {
              token = jwt.sign(
                { userId: 1, username: 'jozi1', role: 'Administrator' },
                JWT_SECRET,
                { expiresIn: '24h' }
              );
            } catch (error) {
              console.warn('⚠️ JWT signing failed, using fallback token');
            }
          }
          
          console.log('✅ Login successful');
          res.json({
            success: true,
            token,
            user: {
              id: 1,
              username: 'jozi1',
              role: 'Administrator',
              modules: ['Leitstand', 'Störungen', 'Backend']
            }
          });
        } else {
          console.log('❌ Login failed: Invalid credentials');
          res.status(401).json({
            success: false,
            error: 'Ungültige Anmeldedaten'
          });
        }
      } catch (error) {
        console.error('❌ Login error:', error);
        res.status(500).json({
          success: false,
          error: 'Server-Fehler'
        });
      }
    });
    
    // Basis-Routen für die wichtigsten Funktionen
    serverApp.get('/api/database/status', async (req: any, res: any) => {
      try {
        if (prisma) {
          await prisma.$queryRaw`SELECT 1`;
          res.json({ status: 'connected' });
        } else {
          res.json({ status: 'mock', message: 'Database not available, using mock data' });
        }
      } catch (error) {
        res.status(500).json({ status: 'error', error: (error as Error).message });
      }
    });
    
    backendServer = serverApp.listen(port, 'localhost', () => {
      console.log(`✅ Integriertes Backend läuft auf Port ${port}`);
      mainWindow?.webContents.send('backend:log', `Backend gestartet auf Port ${port}`);
    });
    
    backendServer.on('error', (error: any) => {
      console.error('❌ Backend Server Error:', error);
      mainWindow?.webContents.send('backend:log', `Backend-Server-Fehler: ${error.message}`);
    });
    
  } catch (error) {
    console.error('❌ Backend-Start-Fehler:', error);
    mainWindow?.webContents.send('backend:log', `Backend-Fehler: ${(error as Error).message}`);
  }
}

// Beim App-Beenden Backend sauber terminieren
function stopBackend() {
  try {
    // Integriertes Backend stoppen
    if (backendServer) {
      backendServer.close();
      backendServer = null;
    }
    
    // Originales Backend stoppen
    if (backendProc && !backendProc.killed) {
      if (process.platform === 'win32') {
        spawn('taskkill', ['/F', '/T', '/PID', String(backendProc.pid)]);
      } else {
        backendProc.kill('SIGTERM');
      }
      backendProc = null;
    }
  } catch {
    // ignore
  }
}

function createWindow() {
  // Bestimme Preload-Pfad dynamisch (Prod/Dev)
  const prodPreload = path.join(__dirname, '../preload/preload.js');
  const devPreload = path.join(__dirname, 'preload.js');
  const preloadPath = fs.existsSync(prodPreload)
    ? prodPreload
    : (fs.existsSync(devPreload) ? devPreload : prodPreload);

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    frame: true,
    webPreferences: {
      // Preload-Pfad (funktioniert in Dev/Prod)
      preload: preloadPath,
      contextIsolation: true,
      nodeIntegration: false,
      // Sicherheit aktivieren – entfernt die Warnungen in Dev, bleibt korrekt für Prod
      webSecurity: true,
      allowRunningInsecureContent: false,
    },
  });

  // Ziele für Dev/Prod vorab bestimmen
  const isDev = !app.isPackaged;
  const envDevUrl = process.env.MAIN_WINDOW_VITE_DEV_SERVER_URL;
  const devUrl = isDev ? (envDevUrl && envDevUrl.trim().length > 0 ? envDevUrl : 'http://localhost:5173') : undefined;
  const indexPath = path.join(__dirname, '../index.html');

  // Explizite Diagnose
  console.log('Environment decision', { isPackaged: app.isPackaged, envDevUrl });
  logLine('Renderer target decision', { isPackaged: app.isPackaged, envDevUrl, indexPath });

  // Renderer laden: In Dev IMMER Dev-Server, in Prod lokale index.html
  try {
    if (isDev) {
      console.log('Loading DEV URL:', devUrl);
      logLine('Loading DEV URL', devUrl);
      mainWindow.loadURL(devUrl!);
      try { mainWindow.webContents.openDevTools({ mode: 'detach' }); } catch {}
    } else {
      console.log('Loading PROD index from:', indexPath);
      logLine('Loading PROD index', indexPath);
      mainWindow.loadFile(indexPath);
      // In Produktion: SPA-Routen-Reload absichern – bei file:// Navigationsversuch auf index.html zurückfallen
      mainWindow.webContents.on('will-navigate', (event, url) => {
        try {
          const isFile = url.startsWith('file://');
          const endsWithIndex = url.endsWith('/index.html') || url.endsWith('index.html');
          if (isFile && !endsWithIndex) {
            event.preventDefault();
            mainWindow!.loadFile(indexPath);
          }
        } catch {}
      });
    }
  } catch (e) {
    console.error('Failed to load renderer:', e);
    logLine('Failed to load renderer', String(e));
  }

  mainWindow.once('ready-to-show', () => {
    try {
      mainWindow!.show();
      mainWindow!.maximize();
      console.log('Main window ready-to-show and maximized');
    } catch (e) {
      console.error('Error on ready-to-show:', e);
    }
  });

  // Einfache On-load Diagnose
  mainWindow.webContents.once('did-finish-load', () => {
    try {
      const wasFullScreen = mainWindow?.isFullScreen?.() ?? false;
      const isMax = mainWindow?.isMaximized?.() ?? false;
      console.log(`did-finish-load | fullscreen=${wasFullScreen} maximized=${isMax}`);
    } catch {
      // ignore
    }
  });

  // Tastenkürzel-Reload robust behandeln (Strg+R, Strg+Shift+R)
  mainWindow.webContents.on('before-input-event', (event, input) => {
    const key = input.key?.toLowerCase();
    if (input.control && (key === 'r')) {
      event.preventDefault();
      try {
        if (isDev) mainWindow!.loadURL(devUrl!);
        else mainWindow!.loadFile(indexPath);
      } catch (e) {
        console.error('Reload handler error:', e);
      }
    }
  });

  // Fallback bei Ladefehlern: kurz warten und korrektes Ziel erneut laden
  mainWindow.webContents.on('did-fail-load', (_e, errorCode, errorDescription, validatedURL) => {
    console.error('did-fail-load:', { errorCode, errorDescription, validatedURL });
    setTimeout(() => {
      try {
        if (!mainWindow || mainWindow.isDestroyed()) return;
        if (isDev) mainWindow.loadURL(devUrl!);
        else mainWindow.loadFile(indexPath);
      } catch (e) {
        console.error('did-fail-load retry error:', e);
      }
    }, 350);
  });

  // Diagnose-Events
  mainWindow.webContents.on('render-process-gone', (_e, details) => {
    console.error('render-process-gone:', details);
  });
  mainWindow.on('unresponsive', () => {
    console.error('BrowserWindow unresponsive');
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.whenReady().then(() => {
  logLine('app.whenReady()');

  // Konfigurations-IPC registrieren und Initialwerte setzen
  try {
    registerConfigHandlers();
  } catch (e) {
    console.error('Failed to register config IPC handlers:', e);
  }
  try {
    const environment = app.isPackaged ? 'production' : 'development';
    initializeConfig({
      environment: environment as any,
      version: app.getVersion(),
      apiBaseUrl: process.env.API_BASE_URL || 'http://localhost:3001/api',
      apiKey: process.env.SFM_API_KEY || ''
    });
  } catch (e) {
    console.error('Failed to initialize config:', e);
  }

  createWindow();

  app.on('activate', () => {
    logLine('app.activate()');
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  logLine('window-all-closed');
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  logLine('before-quit');
  try {
    stopBackend();
  } catch (e) {
    logLine('stopBackend error', { error: String(e) });
  }
});

// Utility: neueste Logdatei finden
function findLatestLogFile(dir: string, prefix = 'Bestand_Workflow_Log_') {
  try {
    const files = fs.readdirSync(dir)
      .filter(f => f.startsWith(prefix))
      .map(f => ({ f, t: fs.statSync(path.join(dir, f)).mtime.getTime() }))
      .sort((a, b) => b.t - a.t);
    if (files.length > 0) {
      return path.join(dir, files[0].f);
    }
  } catch {
    // ignore
  }
  return null;
}

// Workflow starten
function startWorkflow(id: WorkflowId, params?: Record<string, string>): { ok: boolean; message?: string } {
  const def = registry[id];
  if (!def) return { ok: false, message: `Workflow ${id} nicht gefunden` };

  const rt = runtimeState[id];
  if (rt.status === 'running') return { ok: false, message: `Workflow ${id} läuft bereits` };

  if (!fs.existsSync(def.scriptPath)) {
    return { ok: false, message: `Script nicht gefunden: ${def.scriptPath}` };
  }

  const envVars = {
    ...process.env,
    ...(def.env ?? {}),
    ...(params ?? {}),
  };

  // Python-Ausführung
  const pythonCmd = process.env.PYTHON_PATH || 'python';
  const proc = spawn(pythonCmd, [def.scriptPath], {
    cwd: process.cwd(),
    env: envVars,
    shell: process.platform === 'win32',
    windowsHide: true,
  });

  rt.proc = proc;
  rt.status = 'running';
  rt.startedAt = Date.now();
  rt.finishedAt = null;
  rt.buffer = [];

  // Versuche direkt die neueste Logdatei zu erfassen
  if (def.logGlob) {
    // Polling nach Start (einige ms warten bis Datei erzeugt wurde)
    setTimeout(() => {
      rt.lastLogFilePath = findLatestLogFile(def.logGlob!);
    }, 1500);
  }

  proc.stdout.on('data', (d) => {
    const line = d.toString();
    rt.buffer.push(line);
    mainWindow?.webContents.send('workflows:log', { id, line });
  });

  proc.stderr.on('data', (d) => {
    const line = d.toString();
    rt.buffer.push(line);
    mainWindow?.webContents.send('workflows:log', { id, line });
  });

  proc.on('close', (code) => {
    rt.status = code === 0 ? 'success' : 'error';
    rt.finishedAt = Date.now();
    mainWindow?.webContents.send('workflows:finished', { id, code, lastLog: rt.lastLogFilePath });
  });

  return { ok: true };
}

// Status abfragen
function getWorkflowStatus(id: WorkflowId) {
  const rt = runtimeState[id];
  if (!rt) return { status: 'idle' as WorkflowStatus };
  return {
    status: rt.status,
    startedAt: rt.startedAt,
    finishedAt: rt.finishedAt,
    lastLogFilePath: rt.lastLogFilePath,
  };
}

// Logs aus Datei streamen (Tail-ähnlich, einfaches Interval-Read)
const tailIntervals: Record<WorkflowId, NodeJS.Timeout | null> = { bestand: null };
const tailPositions: Record<WorkflowId, number> = { bestand: 0 };

function startTailLogs(id: WorkflowId) {
  const rt = runtimeState[id];
  const def = registry[id];
  if (!def?.logGlob) return;

  if (!rt.lastLogFilePath) {
    rt.lastLogFilePath = findLatestLogFile(def.logGlob);
    tailPositions[id] = 0;
  }
  if (!rt.lastLogFilePath) return;

  const file = rt.lastLogFilePath;
  if (tailIntervals[id]) clearInterval(tailIntervals[id]!);

  tailIntervals[id] = setInterval(() => {
    try {
      const stat = fs.statSync(file);
      const size = stat.size;
      const pos = tailPositions[id] || 0;
      if (size > pos) {
        const fd = fs.openSync(file, 'r');
        const buf = Buffer.alloc(size - pos);
        fs.readSync(fd, buf, 0, size - pos, pos);
        fs.closeSync(fd);
        const chunk = buf.toString('utf-8');
        tailPositions[id] = size;
        chunk.split(/\r?\n/).filter(Boolean).forEach(line => {
          mainWindow?.webContents.send('workflows:log', { id, line: line + '\n' });
        });
      }
    } catch {
      // ignore errors (file may rotate)
    }
  }, 1000);
}

function stopTailLogs(id: WorkflowId) {
  if (tailIntervals[id]) {
    clearInterval(tailIntervals[id]!);
    tailIntervals[id] = null;
  }
}

// IPC-Handler
ipcMain.handle('workflows:list', async () => {
  return Object.values(registry).map(r => ({ id: r.id, name: r.name }));
});

ipcMain.handle('workflows:start', async (_e, payload: { id: WorkflowId; params?: Record<string, string> }) => {
  return startWorkflow(payload.id, payload.params);
});

ipcMain.handle('workflows:status', async (_e, payload: { id: WorkflowId }) => {
  return getWorkflowStatus(payload.id);
});

ipcMain.on('workflows:logs:start', (_e, payload: { id: WorkflowId }) => {
  startTailLogs(payload.id);
});

ipcMain.on('workflows:logs:stop', (_e, payload: { id: WorkflowId }) => {
  stopTailLogs(payload.id);
});