/**
 * Vector Database Service
 * 
 * Manages vector storage and similarity search using SQLite with sqlite-vss extension.
 * Provides high-performance vector operations for RAG and semantic search.
 */

import { AIBaseService, AIServiceConfig, AIServiceStatus } from '../base/AIBaseService';
import { 
  VectorEntry, 
  VectorMatch, 
  VectorSearchResult, 
  VectorMetadata, 
  IndexStats,
  AIServiceError 
} from '../types';

/**
 * Vector Database Service Configuration
 */
export interface VectorDatabaseConfig extends AIServiceConfig {
  databasePath?: string;
  indexName?: string;
  autoCreateIndex?: boolean;
  batchSize?: number;
}

/**
 * Vector Database Service for managing embeddings and similarity search
 */
export class VectorDatabaseService extends AIBaseService {
  readonly serviceName = 'VectorDatabaseService';
  private db: any; // Will be initialized with better-sqlite3
  private vectorConfig: VectorDatabaseConfig;

  constructor(config: VectorDatabaseConfig = {}) {
    super(config);
    this.vectorConfig = {
      databasePath: process.env.VECTOR_DB_PATH || './database/vectors.db',
      indexName: 'vectors',
      autoCreateIndex: true,
      batchSize: 100,
      vectorDimensions: 1536, // OpenAI embedding dimensions
      ...config
    };
  }

  /**
   * Initialize the vector database service
   */
  protected async onInitialize(): Promise<void> {
    try {
      // Import better-sqlite3 dynamically
      const Database = require('better-sqlite3');
      this.db = new Database(this.vectorConfig.databasePath);
      
      // Enable WAL mode for better performance
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('synchronous = NORMAL');
      this.db.pragma('cache_size = 1000000');
      this.db.pragma('temp_store = memory');
      
      // Load sqlite-vss extension
      await this.loadVSSExtension();
      
      // Create vector tables and indices
      if (this.vectorConfig.autoCreateIndex) {
        await this.createVectorTables();
      }
      
      this.log('Vector database initialized successfully');
    } catch (error) {
      this.log('Failed to initialize vector database', error);
      throw error;
    }
  }

  /**
   * Load sqlite-vss extension
   */
  private async loadVSSExtension(): Promise<void> {
    try {
      // Load the sqlite-vss extension using the proper path
      const vss = require('sqlite-vss');
      vss.load(this.db);
      this.log('sqlite-vss extension loaded successfully');
    } catch (error) {
      this.log('Failed to load sqlite-vss extension', error);
      throw new Error('sqlite-vss extension not available. Please install sqlite-vss.');
    }
  }

  /**
   * Create vector tables and indices
   */
  private async createVectorTables(): Promise<void> {
    try {
      // Create virtual table for vectors using vss0
      const createVectorTable = `
        CREATE VIRTUAL TABLE IF NOT EXISTS ${this.vectorConfig.indexName} USING vss0(
          embedding(${this.vectorConfig.vectorDimensions})
        )
      `;
      this.db.exec(createVectorTable);

      // Create metadata table for vector information
      const createMetadataTable = `
        CREATE TABLE IF NOT EXISTS ${this.vectorConfig.indexName}_metadata (
          id TEXT PRIMARY KEY,
          content TEXT NOT NULL,
          document_id TEXT,
          chunk_index INTEGER,
          title TEXT,
          source TEXT,
          department TEXT,
          data_type TEXT,
          metadata JSON,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;
      this.db.exec(createMetadataTable);

      // Create indices for better query performance
      const createIndices = `
        CREATE INDEX IF NOT EXISTS idx_metadata_document_id ON ${this.vectorConfig.indexName}_metadata(document_id);
        CREATE INDEX IF NOT EXISTS idx_metadata_department ON ${this.vectorConfig.indexName}_metadata(department);
        CREATE INDEX IF NOT EXISTS idx_metadata_data_type ON ${this.vectorConfig.indexName}_metadata(data_type);
        CREATE INDEX IF NOT EXISTS idx_metadata_created_at ON ${this.vectorConfig.indexName}_metadata(created_at);
      `;
      this.db.exec(createIndices);

      this.log('Vector tables and indices created successfully');
    } catch (error) {
      this.log('Failed to create vector tables', error);
      throw error;
    }
  }

  /**
   * Insert a single vector with metadata
   */
  async insertVector(id: string, vector: number[], metadata: VectorMetadata): Promise<void> {
    return this.handleAIError(
      async () => {
        const insertVector = this.db.prepare(`
          INSERT OR REPLACE INTO ${this.vectorConfig.indexName}(rowid, embedding) 
          VALUES (?, ?)
        `);
        
        const insertMetadata = this.db.prepare(`
          INSERT OR REPLACE INTO ${this.vectorConfig.indexName}_metadata 
          (id, content, document_id, chunk_index, title, source, department, data_type, metadata, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `);

        // Use transaction for consistency
        const transaction = this.db.transaction(() => {
          // Insert vector (using hash of id as rowid for consistency)
          const rowid = this.hashStringToNumber(id);
          insertVector.run(rowid, JSON.stringify(vector));
          
          // Insert metadata
          insertMetadata.run(
            id,
            metadata.content || '',
            metadata.documentId || null,
            metadata.chunkIndex || null,
            metadata.title || null,
            metadata.source || null,
            metadata.department || null,
            metadata.dataType || null,
            JSON.stringify(metadata)
          );
        });

        transaction();
        this.log(`Vector inserted successfully: ${id}`);
      },
      async () => {
        throw new Error('No fallback available for vector insertion');
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Insert multiple vectors in batch
   */
  async insertVectorsBatch(vectors: VectorEntry[]): Promise<void> {
    return this.handleAIError(
      async () => {
        const insertVector = this.db.prepare(`
          INSERT OR REPLACE INTO ${this.vectorConfig.indexName}(rowid, embedding) 
          VALUES (?, ?)
        `);
        
        const insertMetadata = this.db.prepare(`
          INSERT OR REPLACE INTO ${this.vectorConfig.indexName}_metadata 
          (id, content, document_id, chunk_index, title, source, department, data_type, metadata, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `);

        // Process in batches
        const batchSize = this.vectorConfig.batchSize || 100;
        for (let i = 0; i < vectors.length; i += batchSize) {
          const batch = vectors.slice(i, i + batchSize);
          
          const transaction = this.db.transaction(() => {
            for (const entry of batch) {
              const rowid = this.hashStringToNumber(entry.id);
              insertVector.run(rowid, JSON.stringify(entry.vector));
              
              const metadata = entry.metadata || {};
              insertMetadata.run(
                entry.id,
                metadata.content || '',
                metadata.documentId || null,
                metadata.chunkIndex || null,
                metadata.title || null,
                metadata.source || null,
                metadata.department || null,
                metadata.dataType || null,
                JSON.stringify(metadata)
              );
            }
          });

          transaction();
        }

        this.log(`Batch inserted ${vectors.length} vectors successfully`);
      },
      async () => {
        throw new Error('No fallback available for batch vector insertion');
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Search for similar vectors
   */
  async searchSimilar(
    queryVector: number[], 
    limit: number = 10, 
    threshold?: number
  ): Promise<VectorMatch[]> {
    return this.handleAIError(
      async () => {
        const similarityThreshold = threshold || this.aiConfig.similarityThreshold || 0.7;
        
        // Perform vector similarity search
        const searchQuery = `
          SELECT 
            v.rowid,
            v.distance,
            m.id,
            m.content,
            m.document_id,
            m.chunk_index,
            m.title,
            m.source,
            m.department,
            m.data_type,
            m.metadata
          FROM ${this.vectorConfig.indexName} v
          JOIN ${this.vectorConfig.indexName}_metadata m ON v.rowid = ?
          WHERE v.embedding MATCH ? 
          AND v.distance < ?
          ORDER BY v.distance
          LIMIT ?
        `;

        const results = this.db.prepare(searchQuery).all(
          this.hashStringToNumber('query'), // This needs to be handled differently
          JSON.stringify(queryVector),
          1 - similarityThreshold, // VSS uses distance, not similarity
          limit
        );

        // Convert results to VectorMatch format
        const matches: VectorMatch[] = results.map((row: any) => ({
          id: row.id,
          similarity: 1 - row.distance, // Convert distance back to similarity
          metadata: {
            content: row.content,
            documentId: row.document_id,
            chunkIndex: row.chunk_index,
            title: row.title,
            source: row.source,
            department: row.department,
            dataType: row.data_type,
            ...JSON.parse(row.metadata || '{}')
          }
        }));

        this.log(`Found ${matches.length} similar vectors`);
        return matches;
      },
      async () => {
        // Fallback: return empty results
        this.log('Vector search failed, returning empty results');
        return [];
      },
      AIServiceError.VECTOR_SEARCH_FAILED
    );
  }

  /**
   * Delete a vector by ID
   */
  async deleteVector(id: string): Promise<boolean> {
    return this.handleAIError(
      async () => {
        const rowid = this.hashStringToNumber(id);
        
        const deleteVector = this.db.prepare(`DELETE FROM ${this.vectorConfig.indexName} WHERE rowid = ?`);
        const deleteMetadata = this.db.prepare(`DELETE FROM ${this.vectorConfig.indexName}_metadata WHERE id = ?`);

        const transaction = this.db.transaction(() => {
          deleteVector.run(rowid);
          deleteMetadata.run(id);
        });

        transaction();
        this.log(`Vector deleted successfully: ${id}`);
        return true;
      },
      async () => {
        this.log(`Failed to delete vector: ${id}`);
        return false;
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Update vector metadata
   */
  async updateVectorMetadata(id: string, metadata: VectorMetadata): Promise<void> {
    return this.handleAIError(
      async () => {
        const updateMetadata = this.db.prepare(`
          UPDATE ${this.vectorConfig.indexName}_metadata 
          SET content = ?, document_id = ?, chunk_index = ?, title = ?, source = ?, 
              department = ?, data_type = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `);

        updateMetadata.run(
          metadata.content || '',
          metadata.documentId || null,
          metadata.chunkIndex || null,
          metadata.title || null,
          metadata.source || null,
          metadata.department || null,
          metadata.dataType || null,
          JSON.stringify(metadata),
          id
        );

        this.log(`Vector metadata updated successfully: ${id}`);
      },
      async () => {
        throw new Error('No fallback available for metadata update');
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Get index statistics
   */
  async getIndexStats(): Promise<IndexStats> {
    return this.handleAIError(
      async () => {
        const countQuery = `SELECT COUNT(*) as total FROM ${this.vectorConfig.indexName}_metadata`;
        const result = this.db.prepare(countQuery).get();
        
        const sizeQuery = `SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()`;
        const sizeResult = this.db.prepare(sizeQuery).get();

        const lastUpdatedQuery = `SELECT MAX(updated_at) as last_updated FROM ${this.vectorConfig.indexName}_metadata`;
        const lastUpdatedResult = this.db.prepare(lastUpdatedQuery).get();

        return {
          totalVectors: result.total || 0,
          dimensions: this.vectorConfig.vectorDimensions || 1536,
          indexSize: sizeResult.size || 0,
          lastUpdated: lastUpdatedResult.last_updated ? new Date(lastUpdatedResult.last_updated) : new Date()
        };
      },
      async () => {
        return {
          totalVectors: 0,
          dimensions: this.vectorConfig.vectorDimensions || 1536,
          indexSize: 0,
          lastUpdated: new Date()
        };
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Get vector statistics for health check
   */
  protected async getVectorStats(): Promise<{ totalVectors: number; lastIndexUpdate: Date }> {
    const stats = await this.getIndexStats();
    return {
      totalVectors: stats.totalVectors,
      lastIndexUpdate: stats.lastUpdated
    };
  }

  /**
   * Rebuild vector index
   */
  async rebuildIndex(): Promise<void> {
    return this.handleAIError(
      async () => {
        // Drop and recreate the vector table
        this.db.exec(`DROP TABLE IF EXISTS ${this.vectorConfig.indexName}`);
        await this.createVectorTables();
        this.log('Vector index rebuilt successfully');
      },
      async () => {
        throw new Error('No fallback available for index rebuild');
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Hash string to number for consistent rowid generation
   */
  private hashStringToNumber(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Cleanup database resources
   */
  protected async onDestroy(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.log('Vector database connection closed');
    }
  }

  /**
   * Health check for vector database
   */
  protected async onHealthCheck(): Promise<void> {
    if (!this.db) {
      throw new Error('Database connection not available');
    }

    // Test database connectivity
    try {
      this.db.prepare('SELECT 1').get();
    } catch (error) {
      throw new Error('Database connectivity test failed');
    }

    // Test vector table existence
    try {
      const tableExists = this.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='${this.vectorConfig.indexName}_metadata'
      `).get();
      
      if (!tableExists) {
        throw new Error('Vector tables not found');
      }
    } catch (error) {
      throw new Error('Vector table validation failed');
    }
  }
}