import express from 'express';
import { PrismaClient } from '@prisma-sfm-dashboard/client';

const router = express.Router();
const prisma = new PrismaClient();

// Debug-Route für ARiL-Daten
router.get('/debug', async (req, res) => {
  try {
    console.log('🔍 ARiL Debug-Route aufgerufen');
    
    // Teste direkte Prisma-Abfrage mit korrektem Modell-Namen (ARiL mit großem R)
    const result = await prisma.aRiL.findMany({
      take: 5, // Nur erste 5 Datensätze für Debug
      orderBy: {
        Datum: 'desc',
      },
    });
    
    console.log('✅ ARiL Daten aus Datenbank:', result);
    
    res.json({
      success: true,
      count: result.length,
      data: result,
      fields: result.length > 0 ? Object.keys(result[0]) : [],
    });
  } catch (error) {
    console.error('❌ ARiL Debug Fehler:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unbekann<PERSON> Fehler',
      details: error
    });
  }
});

export default router;