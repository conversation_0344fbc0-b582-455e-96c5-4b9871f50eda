import { useState } from "react";
import { AlertTriangle, CheckCircle, Activity, Database, BarChart3 } from "lucide-react";
import iconWorkflow from "@/assets/iconWorkflow.png";
import { ChartErrorBoundary } from "@/components/ErrorBoundary";
import SmoothTab from "@/components/Animation/kokonutui/smooth-tab";
import { WorkflowGrid } from "../components/workflows/WorkflowGrid";
import { WorkflowOverview } from "../components/workflows/WorkflowOverview";
import { WorkflowPerformanceChart } from "../components/charts/WorkflowPerformanceChart";
import { WorkflowLogViewer } from "../components/workflows/WorkflowLogViewer";
import { WorkflowStatus } from "../components/workflows/WorkflowStatus";
import { PerformanceAnalyticsChart } from "../components/charts/PerformanceAnalyticsChart";
import { AskJaszButton } from "@/modules/ai/components";
import { createPageContext, PROMPT_TEMPLATES } from "@/contexts/AskJaszContext";


export default function WorkflowPage() {
  const [activeTab, setActiveTab] = useState("sap");

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  // Context data for different tabs
  const getTabContext = () => {
    switch (activeTab) {
      case "sap":
        return createPageContext(
          "SAP Workflows",
          [
            "SAP Workflow-Status überwachen",
            "Automatisierte Prozesse verwalten",
            "Workflow-Konfigurationen anpassen",
            "System-Integrationen überwachen"
          ],
          "Zentrale Verwaltung aller SAP-basierten Workflows und Automatisierungen"
        );
      case "overview":
        return createPageContext(
          "Workflow Übersicht",
          [
            "Alle Workflows im Überblick",
            "Status und Performance",
            "Abhängigkeiten visualisieren",
            "Gesamtsystem-Gesundheit"
          ],
          "Umfassende Übersicht aller Workflow-Systeme und deren Status"
        );
      case "performance":
        return createPageContext(
          "Performance Analytics",
          [
            "Workflow-Performance analysieren",
            "Bottlenecks identifizieren",
            "Trend-Analysen durchführen",
            "Optimierungspotentiale erkennen"
          ],
          "Detaillierte Performance-Analyse der Workflow-Systeme"
        );
      case "logs":
        return createPageContext(
          "System Logs",
          [
            "System-Logs analysieren",
            "Fehler-Protokolle einsehen",
            "Debug-Informationen auswerten",
            "Audit-Trails verfolgen"
          ],
          "Umfassende Log-Analyse für Troubleshooting und Monitoring"
        );
      default:
        return createPageContext("Workflows", ["Workflow-Management", "Performance", "Logs"]);
    }
  };

  return (
    <div className="p-6 md:p-6">
      {/* Header mit Überschrift */}
      <div className="mb-6 flex justify-between items-center">
        <div className="flex items-center gap-3">
          <h1 className="text-3xl font-bold text-black flex items-center gap-2">
            <img src={iconWorkflow} alt="Workflow Icon" className="h-8 w-8" />
            WORKFLOWS
          </h1>
          <div className="-mt-6">
            <AskJaszButton
              context={createPageContext(
                "Workflows Hauptseite",
                [
                  "SAP Workflows verwalten",
                  "Performance überwachen",
                  "System-Logs analysieren",
                  "Workflow-Übersichten"
                ],
                "Die zentrale Workflow-Management-Seite des Leitstands"
              )}
              template={PROMPT_TEMPLATES.PAGE_EXPLANATION}
              position="inline"
              size="md"
              tooltip="Lass dir die Workflow-Seite erklären und erfahre, wie du Workflows verwalten kannst"
            />
          </div>
        </div>
      </div>

      {/* Smooth Tabs für verschiedene Workflow-Ansichten */}
      <div className="space-y-6">
        <div className="flex justify-center">
          <div className="w-full max-w-4xl flex items-center gap-4">
            <div className="flex-1">
              <SmoothTab
                items={[
                  {
                    id: "sap",
                    title: "SAP Workflows",
                    icon: Database,
                    color: "bg-purple-500 hover:bg-purple-600",
                  },
                  {
                    id: "overview",
                    title: "Workflow Übersicht",
                    icon: Activity,
                    color: "bg-blue-500 hover:bg-blue-600",
                  },
                  {
                    id: "performance",
                    title: "Performance Analytics",
                    icon: BarChart3,
                    color: "bg-green-500 hover:bg-green-600",
                  },
                  {
                    id: "logs",
                    title: "System Logs",
                    icon: AlertTriangle,
                    color: "bg-yellow-500 hover:bg-yellow-600",
                  },
                ]}
                defaultTabId="sap"
                onChange={handleTabChange}
                hideCardContent={true}
              />
            </div>
            <div className="-mt-8">
              {/* Ask JASZ Button rechts neben den Tabs */}
              <AskJaszButton
                context={getTabContext()}
                template={PROMPT_TEMPLATES.PAGE_EXPLANATION}
                position="inline"
                size="md"
                tooltip={`Lass dir ${getTabContext().title} erklären und erfahre, wie du diese Workflows nutzen kannst`}
              />
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === "sap" && (
            <ChartErrorBoundary>
              <div className="space-y-6">
                <WorkflowStatus />
                <WorkflowGrid />
              </div>
            </ChartErrorBoundary>
          )}

          {activeTab === "overview" && (
            <ChartErrorBoundary>
              <WorkflowOverview />
            </ChartErrorBoundary>
          )}

          {activeTab === "performance" && (
            <ChartErrorBoundary>
              <PerformanceAnalyticsChart />
            </ChartErrorBoundary>
          )}

          {activeTab === "logs" && (
            <ChartErrorBoundary>
              <WorkflowLogViewer />
            </ChartErrorBoundary>
          )}
        </div>
      </div>
    </div>
  );
}