import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON>ert<PERSON>riangle,
  AlertCircle,
  CheckCircle,
  Clock,
  X,
  RefreshCw
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface PerformanceAlert {
  id: number;
  type: 'warning' | 'error';
  message: string;
  metric: string;
  value: number;
  threshold: number;
  createdAt: string;
  resolved: boolean;
}

interface PerformanceAlertsPanelProps {
  alerts: PerformanceAlert[];
  onRefresh: () => void;
}

export function PerformanceAlertsPanel({ alerts, onRefresh }: PerformanceAlertsPanelProps) {
  const [resolving, setResolving] = useState<number | null>(null);

  const resolveAlert = async (alertId: number) => {
    setResolving(alertId);
    try {
      // Alert resolution not available in basic performance routes
      // Just simulate success for now
      console.log(`Resolving alert ${alertId}`);

      // Simulate successful resolution
      onRefresh(); // Refresh the alerts list
    } catch (error) {
      console.error('Error resolving alert:', error);
    } finally {
      setResolving(null);
    }
  };

  const formatValue = (metric: string, value: number) => {
    switch (metric) {
      case 'averageResponseTime':
        return `${Math.round(value)}ms`;
      case 'successRate':
      case 'cacheHitRate':
        return `${(value * 100).toFixed(1)}%`;
      default:
        return value.toString();
    }
  };

  const formatThreshold = (metric: string, threshold: number) => {
    switch (metric) {
      case 'averageResponseTime':
        return `${Math.round(threshold)}ms`;
      case 'successRate':
      case 'cacheHitRate':
        return `${(threshold * 100).toFixed(1)}%`;
      default:
        return threshold.toString();
    }
  };

  const getAlertIcon = (type: 'warning' | 'error') => {
    return type === 'error' ? (
      <AlertCircle className="h-5 w-5 text-red-600" />
    ) : (
      <AlertTriangle className="h-5 w-5 text-yellow-600" />
    );
  };

  const getAlertBadge = (type: 'warning' | 'error') => {
    return (
      <Badge
        variant={type === 'error' ? 'destructive' : 'outline'}
        className={type === 'warning' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : ''}
      >
        {type === 'error' ? 'Kritisch' : 'Warnung'}
      </Badge>
    );
  };

  const getMetricDisplayName = (metric: string) => {
    switch (metric) {
      case 'averageResponseTime':
        return 'Durchschnittliche Antwortzeit';
      case 'successRate':
        return 'Erfolgsrate';
      case 'cacheHitRate':
        return 'Cache Trefferrate';
      case 'intentAccuracy':
        return 'KI Genauigkeit';
      default:
        return metric;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Gerade eben';
    if (diffInMinutes < 60) return `vor ${diffInMinutes} Min`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `vor ${diffInHours} Std`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `vor ${diffInDays} Tag${diffInDays > 1 ? 'en' : ''}`;
  };

  const activeAlerts = alerts.filter(alert => !alert.resolved);
  const errorAlerts = activeAlerts.filter(alert => alert.type === 'error');
  const warningAlerts = activeAlerts.filter(alert => alert.type === 'warning');

  return (
    <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-4 transition-all duration-300 hover:border-slate-200 h-[320px]">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              Performance Alerts
              {activeAlerts.length > 0 && (
                <Badge variant="outline" className="ml-2">
                  {activeAlerts.length} aktiv
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Aktuelle Performance-Warnungen und kritische Meldungen
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>

        {/* Alert Summary */}
        {activeAlerts.length > 0 && (
          <div className="flex gap-4 mt-3">
            {errorAlerts.length > 0 && (
              <div className="flex items-center gap-2 text-red-600">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">{errorAlerts.length} Kritisch</span>
              </div>
            )}
            {warningAlerts.length > 0 && (
              <div className="flex items-center gap-2 text-yellow-600">
                <AlertTriangle className="h-4 w-4" />
                <span className="text-sm font-medium">{warningAlerts.length} Warnungen</span>
              </div>
            )}
          </div>
        )}
      </CardHeader>
      <CardContent className="pt-0">
        {activeAlerts.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-4 text-center">
            <CheckCircle className="h-8 w-8 text-green-600 mb-2" />
            <h3 className="text-sm font-medium text-green-800 mb-1">Alles in Ordnung!</h3>
            <p className="text-xs text-muted-foreground">
              Keine aktiven Performance-Alerts vorhanden.
            </p>
          </div>
        ) : (
          <div className="space-y-2 max-h-[180px] overflow-y-auto">
            <AnimatePresence>
              {activeAlerts
                .sort((a, b) => {
                  if (a.type !== b.type) {
                    return a.type === 'error' ? -1 : 1;
                  }
                  return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                })
                .slice(0, 3) // Limit to 3 alerts for compact view
                .map((alert) => (
                  <motion.div
                    key={alert.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className={`p-3 border rounded ${alert.type === 'error'
                      ? 'border-red-500 bg-red-50'
                      : 'border-yellow-500 bg-yellow-50'
                      }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-2 flex-1">
                        {alert.type === 'error' ? (
                          <AlertCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge
                              variant={alert.type === 'error' ? 'destructive' : 'outline'}
                              className={`text-xs ${alert.type === 'warning' ? 'bg-yellow-200 text-yellow-800 border-yellow-300' : ''}`}
                            >
                              {alert.type === 'error' ? 'Kritisch' : 'Warnung'}
                            </Badge>
                            <span className="text-xs text-muted-foreground flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatTimeAgo(alert.createdAt)}
                            </span>
                          </div>
                          <p className="text-sm font-medium text-foreground mb-1 line-clamp-1">
                            {alert.message}
                          </p>
                          <div className="text-xs text-muted-foreground">
                            <span className="font-medium">{getMetricDisplayName(alert.metric)}:</span> {formatValue(alert.metric, alert.value)} (Limit: {formatThreshold(alert.metric, alert.threshold)})
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => resolveAlert(alert.id)}
                        disabled={resolving === alert.id}
                        className="ml-2 h-6 w-6 p-0"
                      >
                        {resolving === alert.id ? (
                          <RefreshCw className="h-3 w-3 animate-spin" />
                        ) : (
                          <X className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  </motion.div>
                ))}
            </AnimatePresence>
            {activeAlerts.length > 3 && (
              <div className="text-xs text-muted-foreground text-center py-1">
                +{activeAlerts.length - 3} weitere Alerts
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}