"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_service_1 = require("../services/auth.service");
const rate_limiting_middleware_1 = require("../middleware/rate-limiting.middleware");
const router = express_1.default.Router();
const authService = new auth_service_1.AuthService();
// POST /api/auth/register
router.post('/register', rate_limiting_middleware_1.rateLimitConfig.general, async (req, res) => {
    try {
        const { email, username, name, password } = req.body;
        // Basic validation
        if (!email || !username || !password) {
            return res.status(400).json({
                success: false,
                error: 'Validation Error',
                message: 'Em<PERSON>, Benutzername und Passwort sind erforderlich.',
                code: 'MISSING_FIELDS'
            });
        }
        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                success: false,
                error: 'Validation Error',
                message: 'Ungültiges E-Mail-Format.',
                code: 'INVALID_EMAIL'
            });
        }
        // Password length validation
        if (password.length < 6) {
            return res.status(400).json({
                success: false,
                error: 'Validation Error',
                message: 'Das Passwort muss mindestens 6 Zeichen lang sein.',
                code: 'PASSWORD_TOO_SHORT'
            });
        }
        const result = await authService.register(email, username, name, password);
        res.status(201).json({
            success: true,
            message: 'Benutzer erfolgreich registriert.',
            data: {
                id: result.id,
                email: result.email,
                username: result.username,
                name: result.name,
                createdAt: result.createdAt
            }
        });
    }
    catch (error) {
        console.error('[AUTH-REGISTER-ERROR]', error);
        // Handle specific errors
        if (error.code === 'USER_EXISTS') {
            return res.status(409).json({
                success: false,
                error: 'Conflict',
                message: error.message,
                code: error.code
            });
        }
        res.status(500).json({
            success: false,
            error: 'Internal Server Error',
            message: 'Ein unerwarteter Fehler ist aufgetreten.',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/auth/login
router.post('/login', rate_limiting_middleware_1.rateLimitConfig.general, async (req, res) => {
    try {
        const { username, password } = req.body;
        // Basic validation
        if (!username || !password) {
            return res.status(400).json({
                success: false,
                error: 'Validation Error',
                message: 'Benutzername und Passwort sind erforderlich.',
                code: 'MISSING_FIELDS'
            });
        }
        const result = await authService.login(username, password);
        res.status(200).json({
            success: true,
            message: 'Erfolgreich angemeldet.',
            data: {
                token: result.token,
                user: result.user
            }
        });
    }
    catch (error) {
        console.error('[AUTH-LOGIN-ERROR]', error);
        // Handle specific errors
        if (error.code === 'INVALID_CREDENTIALS') {
            return res.status(401).json({
                success: false,
                error: 'Unauthorized',
                message: error.message,
                code: error.code
            });
        }
        res.status(500).json({
            success: false,
            error: 'Internal Server Error',
            message: 'Ein unerwarteter Fehler ist aufgetreten.',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/auth/refresh
router.post('/refresh', rate_limiting_middleware_1.rateLimitConfig.general, async (req, res) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                error: 'Unauthorized',
                message: 'Kein gültiger Token bereitgestellt.',
                code: 'NO_TOKEN'
            });
        }
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        const result = await authService.refreshToken(token);
        res.status(200).json({
            success: true,
            message: 'Token erfolgreich aktualisiert.',
            data: {
                token: result.token,
                user: result.user
            }
        });
    }
    catch (error) {
        console.error('[AUTH-REFRESH-ERROR]', error);
        // Handle specific errors
        if (error.code === 'INVALID_TOKEN' || error.code === 'TOKEN_TOO_OLD' || error.code === 'USER_NOT_FOUND') {
            return res.status(401).json({
                success: false,
                error: 'Unauthorized',
                message: error.message,
                code: error.code
            });
        }
        res.status(500).json({
            success: false,
            error: 'Internal Server Error',
            message: 'Ein unerwarteter Fehler ist aufgetreten.',
            code: 'INTERNAL_ERROR'
        });
    }
});
exports.default = router;
