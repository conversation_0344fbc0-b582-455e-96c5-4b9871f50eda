/**
 * Process Optimization Service Types
 * Defines interfaces and types for process analysis, simulation, and optimization
 */

export interface ProcessData {
  processId: string;
  name: string;
  steps: ProcessStep[];
  resources: ProcessResource[];
  metrics: ProcessMetrics;
  constraints: ProcessConstraint[];
  historicalData: ProcessHistoricalData[];
}

export interface ProcessStep {
  stepId: string;
  name: string;
  duration: number; // in minutes
  resourceRequirements: ResourceRequirement[];
  dependencies: string[]; // stepIds that must complete before this step
  capacity: number; // maximum concurrent executions
  currentUtilization: number; // 0-1 scale
}

export interface ProcessResource {
  resourceId: string;
  name: string;
  type: 'human' | 'machine' | 'material' | 'space';
  capacity: number;
  currentLoad: number; // 0-1 scale
  availability: TimeWindow[];
  costPerHour: number;
}

export interface ResourceRequirement {
  resourceId: string;
  quantity: number;
  duration: number; // in minutes
}

export interface ProcessMetrics {
  throughput: number; // items per hour
  cycleTime: number; // minutes
  leadTime: number; // minutes
  efficiency: number; // 0-1 scale
  utilization: number; // 0-1 scale
  qualityRate: number; // 0-1 scale
  cost: number; // per item
}

export interface ProcessConstraint {
  constraintId: string;
  type: 'capacity' | 'time' | 'resource' | 'quality' | 'dependency';
  description: string;
  parameters: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface ProcessHistoricalData {
  timestamp: Date;
  metrics: ProcessMetrics;
  events: ProcessEvent[];
  resourceUtilization: Record<string, number>;
}

export interface ProcessEvent {
  eventId: string;
  timestamp: Date;
  type: 'start' | 'complete' | 'delay' | 'error' | 'resource_change';
  stepId?: string;
  resourceId?: string;
  duration?: number;
  description: string;
  impact: 'positive' | 'negative' | 'neutral';
}

export interface TimeWindow {
  start: Date;
  end: Date;
  availability: number; // 0-1 scale
}

export interface Bottleneck {
  stepId: string;
  resourceId?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  impact: number; // percentage impact on overall process
  description: string;
  suggestedActions: string[];
  estimatedImprovement: number; // percentage
}

export interface ProcessAnalysis {
  processId: string;
  currentEfficiency: number;
  bottlenecks: Bottleneck[];
  improvementPotential: number;
  recommendations: ProcessRecommendation[];
  analysisTimestamp: Date;
}

export interface ProcessRecommendation {
  recommendationId: string;
  type: 'resource_allocation' | 'process_redesign' | 'capacity_increase' | 'automation' | 'scheduling';
  priority: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  expectedImprovement: number; // percentage
  implementationCost: number;
  implementationTime: number; // days
  riskLevel: 'low' | 'medium' | 'high';
  prerequisites: string[];
}

export interface ProcessChange {
  changeId: string;
  name: string; // Human-readable name for the change/scenario
  type: 'step_modification' | 'resource_addition' | 'capacity_change' | 'automation' | 'resequencing';
  description: string;
  affectedSteps: string[];
  affectedResources: string[];
  parameters: Record<string, any>;
  estimatedCost: number;
  estimatedTime: number; // implementation time in days
}

export interface SimulationConfig {
  processId: string;
  duration: number; // simulation duration in hours
  iterations: number;
  changes: ProcessChange[];
  randomSeed?: number;
  warmupPeriod: number; // hours
  confidenceLevel: number; // 0-1 scale
}

export interface SimulationResult {
  scenarioName: string;
  
  // Common properties
  riskFactors: RiskFactor[];
  simulationStats: SimulationStats;
  
  // Discrete event simulation properties
  baselineMetrics?: ProcessMetrics;
  projectedMetrics?: ProcessMetrics;
  improvement?: ProcessImprovement;
  confidence?: number; // 0-1 scale
  
  // Monte Carlo simulation properties
  expectedImprovement?: number;
  implementationCost?: number;
  roi?: number;
}

export interface ProcessImprovement {
  throughputImprovement: number; // percentage
  cycleTimeReduction: number; // percentage
  efficiencyGain: number; // percentage
  costReduction: number; // percentage
  qualityImprovement: number; // percentage
}

export interface RiskFactor {
  riskId: string;
  description: string;
  probability: number; // 0-1 scale
  impact: 'low' | 'medium' | 'high' | 'critical';
  mitigation: string;
}

export interface SimulationStats {
  // Common simulation statistics
  totalEvents?: number;
  averageWaitTime?: number;
  resourceUtilization?: Record<string, number>;
  bottleneckOccurrences?: Record<string, number>;
  qualityIssues?: number;
  completedItems?: number;
  
  // Monte Carlo simulation specific statistics
  iterations?: number;
  convergenceAchieved?: boolean;
  executionTime?: number;
}

export interface SimulationOutput {
  results: SimulationResult[];
  bestScenario: SimulationResult;
  worstScenario: SimulationResult;
  averageScenario: SimulationResult;
  recommendations: ProcessRecommendation[];
}

export interface EfficiencyMetrics {
  overall: number; // 0-1 scale
  byStep: Record<string, number>;
  byResource: Record<string, number>;
  trends: EfficiencyTrend[];
  benchmarks: EfficiencyBenchmark[];
}

export interface EfficiencyTrend {
  metric: string;
  values: { timestamp: Date; value: number }[];
  trend: 'improving' | 'declining' | 'stable';
  changeRate: number; // percentage per period
}

export interface EfficiencyBenchmark {
  metric: string;
  currentValue: number;
  industryAverage: number;
  bestPractice: number;
  gap: number; // percentage
}

export interface OptimizationSuggestion {
  suggestionId: string;
  category: 'bottleneck_removal' | 'resource_optimization' | 'process_redesign' | 'automation' | 'scheduling' | 'monte_carlo_optimization';
  title: string;
  description: string;
  expectedBenefit: ProcessImprovement;
  implementationEffort: 'low' | 'medium' | 'high';
  priority: number; // 1-10 scale
  dependencies: string[];
  timeline: string;
  confidence?: number; // Confidence level for Monte Carlo-based suggestions
  riskAssessment?: {
    riskScore: number;
    riskFactors: any[];
    mitigationStrategies: any[];
  };
}

// Discrete Event Simulation Types
export interface SimulationEntity {
  entityId: string;
  type: string;
  attributes: Record<string, any>;
  currentStep?: string;
  arrivalTime: number;
  startTime?: number;
  completionTime?: number;
  waitTimes: Record<string, number>;
}

export interface SimulationEvent {
  eventId: string;
  timestamp: number;
  type: 'arrival' | 'start_processing' | 'end_processing' | 'resource_available' | 'resource_busy';
  entityId?: string;
  stepId?: string;
  resourceId?: string;
  priority: number;
}

export interface SimulationState {
  currentTime: number;
  entities: Map<string, SimulationEntity>;
  resources: Map<string, ProcessResource>;
  eventQueue: SimulationEvent[];
  statistics: SimulationStatistics;
}

export interface SimulationStatistics {
  entitiesProcessed: number;
  totalWaitTime: number;
  totalProcessingTime: number;
  resourceUtilization: Map<string, number>;
  stepThroughput: Map<string, number>;
  qualityMetrics: Map<string, number>;
}

// Error types
export class ProcessOptimizationError extends Error {
  constructor(
    message: string,
    public code: string,
    public processId?: string
  ) {
    super(message);
    this.name = 'ProcessOptimizationError';
  }
}

export class SimulationError extends Error {
  constructor(
    message: string,
    public code: string,
    public simulationId?: string
  ) {
    super(message);
    this.name = 'SimulationError';
  }
}