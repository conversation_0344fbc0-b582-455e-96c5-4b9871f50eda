#!/usr/bin/env python3
"""
Test-Skript für den optimierten WorkflowLogger
Demonstriert die Verwendung des strukturierten Logging-Systems
"""

import time
import random
from workflow_logger import WorkflowLogger

def test_workflow_logging():
    """Testet das optimierte Workflow-Logging-System"""
    
    print("=== Teste optimiertes Workflow-Logging-System ===\n")
    
    # Logger für Test-Workflow erstellen
    logger = WorkflowLogger("test_workflow")
    
    logger.info("=== Test Workflow gestartet ===")
    
    # Simuliere verschiedene Log-Typen
    logger.log_process_start("Test SAP-Prozess")
    
    # Simuliere SAP-Aktionen
    logger.log_sap_action("Verbindung zu SAP GUI", "wnd[0]", True)
    logger.log_sap_action("Transaktion starten", "/nSE80", True)
    
    # Simuliere Wartezeit
    time.sleep(1)
    
    # Simuliere Datei-Operationen
    test_file = "test_export.xlsx"
    logger.log_file_operation("Export", test_file, True)
    
    # Simuliere Datenbank-Operationen
    record_count = random.randint(100, 1000)
    logger.log_database_operation("Import", "test_table", record_count, True)
    
    # Simuliere Warning
    logger.warning("Test-Warnung: Langsame Verbindung erkannt", {
        "response_time": 5.2,
        "threshold": 3.0,
        "recommendation": "Netzwerkverbindung prüfen"
    })
    
    # Simuliere Error (aber Prozess läuft weiter)
    logger.error("Test-Fehler: Temporärer Verbindungsfehler", {
        "error_code": "CONN_TIMEOUT",
        "retry_count": 1,
        "next_retry": "in 30 seconds"
    })
    
    # Simuliere Debug-Information
    logger.debug("Debug-Info: Speicher-Status", {
        "memory_usage": "45%",
        "available_memory": "8GB",
        "process_count": 12
    })
    
    # Prozess erfolgreich abschließen
    logger.log_process_complete("Test SAP-Prozess", test_file)
    
    logger.info("=== Test Workflow abgeschlossen ===")
    
    # Logs aus Datenbank lesen und anzeigen
    print("\n" + "="*60)
    print("LOGS AUS DATENBANK:")
    print("="*60)
    
    logs = WorkflowLogger.get_logs_for_workflow("test_workflow")
    for log in logs:
        timestamp = log['timestamp']
        level = log['level'].upper().ljust(7)
        message = log['message']
        print(f"[{timestamp}] {level} {message}")
        
        if log['details']:
            details = log['details']
            if isinstance(details, dict):
                for key, value in details.items():
                    print(f"    {key}: {value}")
    
    print(f"\nInsgesamt {len(logs)} Log-Einträge gefunden.")
    
    # Teste auch das Laden aller Logs
    print("\n" + "="*60)
    print("ALLE LOGS (LETZTE 10):")
    print("="*60)
    
    all_logs = WorkflowLogger.get_all_logs(limit=10)
    for log in all_logs:
        timestamp = log['timestamp']
        level = log['level'].upper().ljust(7)
        workflow_id = log['workflow_id'].ljust(15)
        message = log['message']
        print(f"[{timestamp}] {level} [{workflow_id}] {message}")
    
    print(f"\nInsgesamt {len(all_logs)} Log-Einträge in der Datenbank.")


def test_multiple_workflows():
    """Testet mehrere gleichzeitige Workflows"""
    print("\n" + "="*60)
    print("TESTE MEHRERE WORKFLOWS:")
    print("="*60)
    
    workflows = ["servicegrad", "rueckstandsliste", "lx03_240"]
    
    for workflow_name in workflows:
        logger = WorkflowLogger(workflow_name)
        logger.log_process_start(f"Test {workflow_name}")
        
        # Simuliere unterschiedliche Ausführungszeiten
        time.sleep(random.uniform(0.1, 0.5))
        
        if random.random() > 0.2:  # 80% Erfolgsrate
            logger.log_process_complete(f"Test {workflow_name}", f"{workflow_name}_export.xlsx")
        else:
            logger.log_process_error(f"Test {workflow_name}", "Simulierter Fehler")
    
    print("Mehrere Workflows getestet.")


if __name__ == "__main__":
    test_workflow_logging()
    test_multiple_workflows()
    
    print("\n" + "="*60)
    print("✅ ALLE TESTS ABGESCHLOSSEN")
    print("="*60)
    print("Das optimierte Logging-System ist bereit für den Produktionseinsatz!")
    print("Logs werden automatisch in backend/database/sfm_dashboard.db gespeichert.")