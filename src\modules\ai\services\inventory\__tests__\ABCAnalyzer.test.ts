/**
 * ABC Analyzer Tests
 * 
 * Unit tests for ABC analysis algorithms
 */

import { ABCAnalyzer } from '../algorithms/ABCAnalyzer';
import { InventoryItem, ConsumptionData, ABCCriteria } from '../types';

describe('ABCAnalyzer', () => {
  // Mock data
  const mockInventoryItems: InventoryItem[] = [
    {
      id: 'item-001',
      name: 'High Value Item',
      category: 'Electronics',
      currentStock: 100,
      unitPrice: 1000,
      lastUpdated: new Date()
    },
    {
      id: 'item-002',
      name: 'Medium Value Item',
      category: 'Components',
      currentStock: 200,
      unitPrice: 100,
      lastUpdated: new Date()
    },
    {
      id: 'item-003',
      name: 'Low Value Item',
      category: 'Supplies',
      currentStock: 500,
      unitPrice: 10,
      lastUpdated: new Date()
    },
    {
      id: 'item-004',
      name: 'Another Low Value Item',
      category: 'Supplies',
      currentStock: 300,
      unitPrice: 5,
      lastUpdated: new Date()
    }
  ];

  const mockConsumptionData: ConsumptionData[] = [
    // High value, high consumption
    { itemId: 'item-001', date: new Date('2024-01-01'), quantity: 10, value: 10000 },
    { itemId: 'item-001', date: new Date('2024-01-02'), quantity: 8, value: 8000 },
    { itemId: 'item-001', date: new Date('2024-01-03'), quantity: 12, value: 12000 },
    
    // Medium value, medium consumption
    { itemId: 'item-002', date: new Date('2024-01-01'), quantity: 20, value: 2000 },
    { itemId: 'item-002', date: new Date('2024-01-02'), quantity: 15, value: 1500 },
    { itemId: 'item-002', date: new Date('2024-01-03'), quantity: 25, value: 2500 },
    
    // Low value, low consumption
    { itemId: 'item-003', date: new Date('2024-01-01'), quantity: 50, value: 500 },
    { itemId: 'item-003', date: new Date('2024-01-02'), quantity: 30, value: 300 },
    
    // Very low value, very low consumption
    { itemId: 'item-004', date: new Date('2024-01-01'), quantity: 10, value: 50 },
    { itemId: 'item-004', date: new Date('2024-01-02'), quantity: 5, value: 25 }
  ];

  const defaultCriteria: ABCCriteria = {
    method: 'value',
    classAThreshold: 80,
    classBThreshold: 95,
    timeWindow: 30
  };

  describe('performAnalysis', () => {
    it('should classify items correctly based on value', () => {
      const result = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mockConsumptionData,
        defaultCriteria
      );

      // Should classify all items
      const totalClassified = result.classA.length + result.classB.length + result.classC.length;
      expect(totalClassified).toBe(mockInventoryItems.length);
      expect(result.analysisDate).toBeInstanceOf(Date);
      expect(result.criteria).toEqual(defaultCriteria);
      
      // At least some items should be classified (not necessarily in each class)
      expect(totalClassified).toBeGreaterThan(0);
    });

    it('should handle empty inventory items', () => {
      const result = ABCAnalyzer.performAnalysis([], mockConsumptionData, defaultCriteria);
      
      expect(result.classA).toHaveLength(0);
      expect(result.classB).toHaveLength(0);
      expect(result.classC).toHaveLength(0);
    });

    it('should handle empty consumption data', () => {
      const result = ABCAnalyzer.performAnalysis(mockInventoryItems, [], defaultCriteria);
      
      // All items should be classified as C since no consumption data
      expect(result.classC.length).toBe(mockInventoryItems.length);
      expect(result.classA).toHaveLength(0);
      expect(result.classB).toHaveLength(0);
    });

    it('should work with different analysis methods', () => {
      const quantityCriteria: ABCCriteria = {
        ...defaultCriteria,
        method: 'quantity'
      };

      const result = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mockConsumptionData,
        quantityCriteria
      );

      expect(result.criteria.method).toBe('quantity');
      expect(result.classA.length + result.classB.length + result.classC.length)
        .toBe(mockInventoryItems.length);
    });

    it('should work with frequency-based analysis', () => {
      const frequencyCriteria: ABCCriteria = {
        ...defaultCriteria,
        method: 'frequency'
      };

      const result = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mockConsumptionData,
        frequencyCriteria
      );

      expect(result.criteria.method).toBe('frequency');
      expect(result.classA.length + result.classB.length + result.classC.length)
        .toBe(mockInventoryItems.length);
    });

    it('should work with combined analysis method', () => {
      const combinedCriteria: ABCCriteria = {
        ...defaultCriteria,
        method: 'combined'
      };

      const result = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mockConsumptionData,
        combinedCriteria
      );

      expect(result.criteria.method).toBe('combined');
      expect(result.classA.length + result.classB.length + result.classC.length)
        .toBe(mockInventoryItems.length);
    });

    it('should respect time window constraints', () => {
      const shortTimeWindow: ABCCriteria = {
        ...defaultCriteria,
        timeWindow: 1 // Only 1 day
      };

      // Create consumption data with some old entries
      const mixedConsumptionData = [
        ...mockConsumptionData,
        { itemId: 'item-001', date: new Date('2023-01-01'), quantity: 100, value: 100000 } // Old data
      ];

      const result = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mixedConsumptionData,
        shortTimeWindow
      );

      // Should still work but with limited data
      expect(result.classA.length + result.classB.length + result.classC.length)
        .toBe(mockInventoryItems.length);
    });
  });

  describe('identifyReclassifications', () => {
    it('should identify reclassifications between analyses', () => {
      const analysis1 = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mockConsumptionData,
        defaultCriteria
      );

      // Create modified consumption data that would change classifications
      const modifiedConsumptionData = mockConsumptionData.map(item => ({
        ...item,
        value: item.itemId === 'item-003' ? item.value * 10 : item.value // Boost item-003
      }));

      const analysis2 = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        modifiedConsumptionData,
        defaultCriteria
      );

      const reclassifications = ABCAnalyzer.identifyReclassifications(analysis2, analysis1);

      expect(Array.isArray(reclassifications)).toBe(true);
      // Should detect changes if any occurred
    });

    it('should handle identical analyses', () => {
      const analysis1 = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mockConsumptionData,
        defaultCriteria
      );

      const analysis2 = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mockConsumptionData,
        defaultCriteria
      );

      const reclassifications = ABCAnalyzer.identifyReclassifications(analysis2, analysis1);

      expect(reclassifications).toHaveLength(0);
    });

    it('should provide meaningful reclassification reasons', () => {
      // Create two different analyses
      const highValueData = mockConsumptionData.map(item => ({
        ...item,
        value: item.value * 2
      }));

      const analysis1 = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mockConsumptionData,
        defaultCriteria
      );

      const analysis2 = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        highValueData,
        defaultCriteria
      );

      const reclassifications = ABCAnalyzer.identifyReclassifications(analysis2, analysis1);

      reclassifications.forEach(change => {
        expect(change.reason).toBeTruthy();
        expect(typeof change.reason).toBe('string');
        expect(change.confidence).toBeGreaterThan(0);
        expect(change.confidence).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('calculateStatistics', () => {
    it('should calculate correct statistical summary', () => {
      const analysisResults = [
        { itemId: 'item-001', classification: 'A' as const, value: 1000, percentage: 50, cumulativePercentage: 50, rank: 1, confidence: 0.9 },
        { itemId: 'item-002', classification: 'B' as const, value: 500, percentage: 25, cumulativePercentage: 75, rank: 2, confidence: 0.8 },
        { itemId: 'item-003', classification: 'C' as const, value: 300, percentage: 15, cumulativePercentage: 90, rank: 3, confidence: 0.7 },
        { itemId: 'item-004', classification: 'C' as const, value: 200, percentage: 10, cumulativePercentage: 100, rank: 4, confidence: 0.6 }
      ];

      const stats = ABCAnalyzer.calculateStatistics(analysisResults);

      expect(stats.count).toBe(4);
      expect(stats.mean).toBe(500); // (1000 + 500 + 300 + 200) / 4
      expect(stats.min).toBe(200);
      expect(stats.max).toBe(1000);
      expect(stats.median).toBe(400); // (300 + 500) / 2
      expect(stats.standardDeviation).toBeGreaterThan(0);
      expect(stats.variance).toBeGreaterThan(0);
      expect(stats.percentiles.p25).toBeGreaterThan(0);
      expect(stats.percentiles.p75).toBeGreaterThan(0);
      expect(stats.percentiles.p90).toBeGreaterThan(0);
      expect(stats.percentiles.p95).toBeGreaterThan(0);
    });

    it('should handle empty analysis results', () => {
      const stats = ABCAnalyzer.calculateStatistics([]);

      expect(stats.count).toBe(0);
      expect(stats.mean).toBe(0);
      expect(stats.median).toBe(0);
      expect(stats.min).toBe(0);
      expect(stats.max).toBe(0);
      expect(stats.standardDeviation).toBe(0);
      expect(stats.variance).toBe(0);
    });

    it('should handle single item analysis', () => {
      const singleItemResult = [
        { itemId: 'item-001', classification: 'A' as const, value: 1000, percentage: 100, cumulativePercentage: 100, rank: 1, confidence: 1.0 }
      ];

      const stats = ABCAnalyzer.calculateStatistics(singleItemResult);

      expect(stats.count).toBe(1);
      expect(stats.mean).toBe(1000);
      expect(stats.median).toBe(1000);
      expect(stats.min).toBe(1000);
      expect(stats.max).toBe(1000);
      expect(stats.standardDeviation).toBe(0);
      expect(stats.variance).toBe(0);
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle items with zero consumption', () => {
      const itemsWithZeroConsumption = [
        ...mockInventoryItems,
        {
          id: 'item-zero',
          name: 'Zero Consumption Item',
          category: 'Unused',
          currentStock: 1000,
          unitPrice: 50,
          lastUpdated: new Date()
        }
      ];

      const result = ABCAnalyzer.performAnalysis(
        itemsWithZeroConsumption,
        mockConsumptionData, // No data for item-zero
        defaultCriteria
      );

      expect(result.classA.length + result.classB.length + result.classC.length)
        .toBe(itemsWithZeroConsumption.length);
    });

    it('should handle extreme threshold values', () => {
      const extremeCriteria: ABCCriteria = {
        ...defaultCriteria,
        classAThreshold: 99.9,
        classBThreshold: 99.99
      };

      const result = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mockConsumptionData,
        extremeCriteria
      );

      // Most items should be in class C with extreme thresholds
      expect(result.classC.length).toBeGreaterThan(0);
    });

    it('should handle very small threshold values', () => {
      const smallCriteria: ABCCriteria = {
        ...defaultCriteria,
        classAThreshold: 10,
        classBThreshold: 20
      };

      const result = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mockConsumptionData,
        smallCriteria
      );

      // Should classify all items
      const totalClassified = result.classA.length + result.classB.length + result.classC.length;
      expect(totalClassified).toBe(mockInventoryItems.length);
      
      // With small thresholds, most items should be in class B or C
      expect(result.classB.length + result.classC.length).toBeGreaterThan(0);
    });

    it('should maintain data integrity across classifications', () => {
      const result = ABCAnalyzer.performAnalysis(
        mockInventoryItems,
        mockConsumptionData,
        defaultCriteria
      );

      const totalClassified = result.classA.length + result.classB.length + result.classC.length;
      expect(totalClassified).toBe(mockInventoryItems.length);

      // Check for duplicates
      const allClassifiedIds = [
        ...result.classA.map(item => item.id),
        ...result.classB.map(item => item.id),
        ...result.classC.map(item => item.id)
      ];

      const uniqueIds = new Set(allClassifiedIds);
      expect(uniqueIds.size).toBe(allClassifiedIds.length);
    });
  });

  describe('confidence calculation', () => {
    it('should calculate confidence based on data quality', () => {
      // Create consumption data with varying amounts of data per item
      const varyingConsumptionData: ConsumptionData[] = [
        // Item with lots of data (high confidence expected)
        ...Array.from({ length: 30 }, (_, i) => ({
          itemId: 'item-high-confidence',
          date: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
          quantity: 10 + Math.random() * 2, // Consistent consumption
          value: (10 + Math.random() * 2) * 100
        })),
        // Item with little data (low confidence expected)
        {
          itemId: 'item-low-confidence',
          date: new Date(),
          quantity: 5,
          value: 500
        }
      ];

      const testItems: InventoryItem[] = [
        {
          id: 'item-high-confidence',
          name: 'High Confidence Item',
          category: 'Test',
          currentStock: 100,
          unitPrice: 100,
          lastUpdated: new Date()
        },
        {
          id: 'item-low-confidence',
          name: 'Low Confidence Item',
          category: 'Test',
          currentStock: 50,
          unitPrice: 100,
          lastUpdated: new Date()
        }
      ];

      const result = ABCAnalyzer.performAnalysis(
        testItems,
        varyingConsumptionData,
        defaultCriteria
      );

      // The analysis should complete successfully
      expect(result.classA.length + result.classB.length + result.classC.length)
        .toBe(testItems.length);
    });
  });
});