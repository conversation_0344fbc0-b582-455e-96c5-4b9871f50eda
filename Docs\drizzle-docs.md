# Umfassender Leitfaden: Migration von Prisma zu Drizzle ORM

Dieser Leitfaden ist darauf ausgel<PERSON>t, den Übergang so reibungslos wie möglich zu gestalten. Der Prozess lässt sich in sechs logische Schritte unterteilen, von der Vorbereitung über die Schema-Konvertierung bis hin zur Umstellung des Anwendungscodes.

# Schritt 1: Vorbereitung und Installation

Der erste Schritt besteht darin, die alte Umgebung zu bereinigen und die neue einzurichten.

1. Prisma deinstallieren: Entfernen Sie Prisma und den zugehörigen Client vollständig aus Ihrem Projekt, um Konflikte zu vermeiden.

```Bash
pnpm remove @prisma/client prisma
```

2. Drizzle installieren: Installieren Sie Drizzle ORM, das CLI-Tool **drizzle-kit** und den für Ihre SQLite-Datenbank optimierten Treiber **better-sqlite3**.

```Bash
# Produktionsabhängigkeiten
pnpm add drizzle-orm better-sqlite3

# Entwicklungsabhängigkeiten
pnpm add -D drizzle-kit @types/better-sqlite3
```

# Schritt 2: Erstellen der Drizzle-Konfiguration

**drizzle-kit** benötigt eine Konfigurationsdatei, um zu wissen, wie es sich mit Ihrer Datenbank verbinden und wo es Schema- und Migrationsdateien finden soll.   

Erstellen Sie eine Datei namens **drizzle.config.ts** im Stammverzeichnis Ihres Projekts:

```TypeScript
import { defineConfig } from 'drizzle-kit';

export default defineConfig({
  dialect: 'sqlite', // Gibt an, dass Sie SQLite verwenden
  schema: './src/db/schema.ts', // Pfad zu Ihrer zukünftigen Schema-Datei
  out: './drizzle', // Verzeichnis für generierte Migrationsdateien
  dbCredentials: {
    url: './sqlite.db', // Pfad zu Ihrer lokalen SQLite-Datenbankdatei
  },
  verbose: true, // Gibt detailliertere Logs während der Migration aus
  strict: true, // Stellt sicher, dass alle Änderungen im Schema abgedeckt sind
});
```

# Schritt 3: Automatische Schema-Generierung aus Ihrer bestehenden Datenbank

Dies ist der entscheidende und zeitsparendste Schritt. Anstatt Ihr **schema.prisma** manuell zu übersetzen, lassen Sie Drizzle die Arbeit machen, indem es Ihre vorhandene, von Prisma erstellte Datenbank inspiziert.   

1. Datenbank inspizieren: Führen Sie den folgenden Befehl in Ihrem Terminal aus:

```Bash
npx drizzle-kit introspect
```

Dieser Befehl verbindet sich mit der in **drizzle.config.ts** angegebenen Datenbank, analysiert deren Struktur (Tabellen, Spalten, Indizes, Beziehungen) und generiert automatisch eine **schema.ts**-Datei unter dem von Ihnen angegebenen Pfad **(./src/db/schema.ts)**.   

Generiertes Schema bereinigen: Öffnen Sie die neu erstellte **schema.ts**. Sie werden feststellen, dass **drizzle-kit** auch eine Tabelle für Prismas interne Migrationen erstellt hat **(_prisma_migrations)**. Da diese für Drizzle nicht relevant ist,    

löschen Sie die Definition dieser Tabelle aus der **schema.ts**-Datei. Dies ist ein wichtiger Schritt für einen sauberen Umzug.

# Schritt 4: Datenbankverbindung in der Anwendung herstellen

Erstellen Sie eine zentrale Datei, um die Drizzle-Datenbankinstanz zu initialisieren und in Ihrer gesamten Anwendung wiederzuverwenden.

Erstellen Sie eine Datei, z. B. unter **src/db/index.ts**:

```TypeScript
import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import * as schema from './schema';

// Pfad zu Ihrer SQLite-Datenbankdatei
const sqlite = new Database('./sqlite.db');

// Wichtige PRAGMA-Anweisungen für Performance und Integrität
sqlite.pragma('journal_mode = WAL');
sqlite.pragma('synchronous = NORMAL');
sqlite.pragma('foreign_keys = ON');

// Drizzle-Instanz mit dem Schema erstellen
export const db = drizzle(sqlite, { schema });
```

# Schritt 5: Umstellung der Prisma-Abfragen auf Drizzle

Dies ist der manuelle Teil des Prozesses, bei dem Sie Ihren Anwendungscode durchgehen und alle Prisma-Client-Aufrufe durch die Drizzle-Syntax ersetzen. Drizzle ist bewusst SQL-näher, was Ihnen mehr Kontrolle gibt.   

Hier sind einige typische Beispiele für die Umstellung:

## Einzelnen Benutzer abrufen:

- **Prisma:**

```TypeScript
const user = await prisma.user.findUnique({ where: { id: 1 } });

- **Drizzle:**

```TypeScript
import { eq } from 'drizzle-orm';
import { users } from './db/schema'; // Importieren Sie Ihr Schema
const user = await db.select().from(users).where(eq(users.id, 1)).get(); //.get() für einen einzelnen Datensatz
```

## Alle Benutzer abrufen:

- **Prisma:**
```TypeScript
const allUsers = await prisma.user.findMany();
```

- **Drizzle:**
```TypeScript
const allUsers = await db.select().from(users).all(); //.all() für alle Datensätze
```

## Neuen Benutzer erstellen:

- **Prisma:**
```TypeScript
const newUser = await prisma.user.create({ data: { name: 'Alice', email: '<EMAIL>' } });
```

- **Drizzle:**
```TypeScript

const newUser = await db.insert(users).values({ name: 'Alice', email: '<EMAIL>' }).returning();
```

## Benutzer aktualisieren:

- **Prisma:**
```TypeScript
const updatedUser = await prisma.user.update({ where: { id: 1 }, data: { name: 'Bob' } });
```

- **Drizzle:**
```TypeScript
const updatedUser = await db.update(users).set({ name: 'Bob' }).where(eq(users.id, 1)).returning();
```

## Benutzer löschen:

- **Prisma:**
```TypeScript
await prisma.user.delete({ where: { id: 1 } });
```

- **Drizzle:**
```TypeScript
await db.delete(users).where(eq(users.id, 1));
```

# Schritt 6: Wichtige Themen und Best Practices für einen sauberen Umzug

Beachten Sie die folgenden Punkte, um sicherzustellen, dass Ihre neue Drizzle-Implementierung robust und wartbar ist.

1. **Umgang mit nativen Modulen in Electron (Extrem wichtig!):**
**better-sqlite3** ist ein natives Node.js-Modul. Electron verwendet eine eigene Node.js-Version, was zu einem Konflikt führt. Die für Ihre App kompilierte Version funktioniert nicht mit dem **drizzle-kit**-CLI und umgekehrt.

    - Problem: Sie erhalten einen **NODE_MODULE_VERSION**-Fehler, wenn Sie **drizzle-kit** ausführen, nachdem Sie Ihre App gestartet haben.

    - Lösung: Verwalten Sie zwei Builds des Moduls mit Skripten in Ihrer **package.json**   

    ```JSON
    "scripts": {
    "start": "pnpm rebuild:electron && electron-forge start",
    "rebuild:electron": "electron-rebuild -f -w better-sqlite3",
    "rebuild:drizzle": "pnpm rebuild better-sqlite3",
    "db:push": "pnpm rebuild:drizzle && npx drizzle-kit push:sqlite",
    "db:generate": "pnpm rebuild:drizzle && npx drizzle-kit generate"
    }
    ```

    - Anwendung: Führen Sie **pnpm start** aus, um Ihre App zu entwickeln. Wenn Sie eine Schemaänderung vornehmen müssen, verwenden Sie **pnpm db:push** (für schnelle Prototypen) oder **pnpm db:generate** (um eine SQL-Migrationsdatei zu erstellen). Diese Skripte kompilieren **better-sqlite3** vor der Ausführung für die richtige Umgebung neu.

2. Der neue, verbesserte Migrations-Workflow:
Sie haben Prisma wegen der umständlichen Migrationen verlassen. Drizzle löst dieses Problem elegant: 
  
- Für die Entwicklung **(drizzle-kit push:sqlite)**: Dieser Befehl synchronisiert Ihr Schema direkt mit der Datenbank, ohne eine Migrationsdatei zu erstellen. Perfekt für schnelles Prototyping und Iteration. 
  
- Für die Produktion **(drizzle-kit generate):** Dieser Befehl vergleicht Ihr Schema mit dem letzten Stand und generiert eine saubere, lesbare .sql-Datei. Sie haben die volle Kontrolle und Transparenz über die Änderungen, die in der Produktion angewendet werden.   

3. Beziehungen explizit definieren:
Während **introspect** die Tabellen und Spalten gut erkennt, müssen Sie möglicherweise die relationalen Abfragefunktionen manuell in Ihrer **schema.ts** definieren, um die volle Typsicherheit und den Komfort von Drizzles relationalen Abfragen zu nutzen.

```TypeScript
import { relations } from 'drizzle-orm';
//... Ihre Tabellendefinitionen für users und posts

export const usersRelations = relations(users, ({ many }) => ({
  posts: many(posts),
}));

export const postsRelations = relations(posts, ({ one }) => ({
  author: one(users, {
    fields: [posts.authorId],
    references: [users.id],
  }),
}));
```

4. Vollständige Bereinigung:
Nachdem Sie alle Abfragen umgestellt haben und alles funktioniert, stellen Sie sicher, dass Sie alle Überreste von Prisma entfernen:

- Löschen Sie den **prisma**-Ordner (der Ihr altes Schema und die Migrationen enthält).

- Entfernen Sie alle Prisma-bezogenen Skripte aus Ihrer **package.json**.