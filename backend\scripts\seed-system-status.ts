#!/usr/bin/env tsx

import { PrismaClient } from '../node_modules/@prisma-sfm-dashboard/client';

const prisma = new PrismaClient();

// Maschinen-Identifikatoren aus der SystemStatusHeatmap-Komponente
const machineIdentifiers = [
  'M1-T-H3', 'M2-T-H3', 'M3-R-H3', 'M4-T-H3', 'M5-R-H3',
  'M6-T-H1', 'M7-R-H1', 'M8-T-H1', 'M9-R-H1', 'M10-T-H1',
  'M11-R-H1', 'M12-T-H1', 'M13-R-H1', 'M14-T-H1', 'M15-R-H1',
  'M16-T-H1', 'M17-R-H1', 'M18-T-H1', 'M19-T-H1', 'M20-T-H1',
  'M21-R-H1', 'M22-T-H3', 'M23-T-H1', 'M24-T-H3', 'M25-RR-H1',
  'M26-T-H1', 'M27-R-H3', 'M28-T-H1'
];

// Alle System-Definitionen
const systemDefinitions = [
  // Maschinen
  ...machineIdentifiers.map(id => ({
    system_name: id,
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'machine',
      location: id.includes('H3') ? 'Halle 3' : 'Halle 1',
      machine_type: id.includes('-T-') ? 'Terminal' : id.includes('-R-') ? 'Ring' : id.includes('-RR-') ? 'Ring-Ring' : 'Unknown',
      category: 'Maschinen'
    })
  })),
  
  // Datenbanken
  {
    system_name: 'Datenbank Primary Server',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'database',
      location: 'Serverraum',
      category: 'Datenbanken',
      role: 'primary'
    })
  },
  {
    system_name: 'Datenbank Backup Server',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'database',
      location: 'Serverraum',
      category: 'Datenbanken',
      role: 'backup'
    })
  },
  
  // Terminals
  {
    system_name: 'Terminal Station 1',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'terminal',
      location: 'Halle 1',
      category: 'Terminals',
      station_id: 1
    })
  },
  {
    system_name: 'Terminal Station 2',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'terminal',
      location: 'Halle 2',
      category: 'Terminals',
      station_id: 2
    })
  },
  {
    system_name: 'Terminal Station 3',
    status: 'WARNING' as const,
    metadata: JSON.stringify({
      type: 'terminal',
      location: 'Halle 3',
      category: 'Terminals',
      station_id: 3,
      issue: 'Wartung erforderlich'
    })
  },
  
  // Fördertechnik
  {
    system_name: 'Fördertechnik Zone A',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'conveyor',
      location: 'Zone A',
      category: 'Fördertechnik',
      capacity: '500 Pakete/h'
    })
  },
  {
    system_name: 'Fördertechnik Zone B',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'conveyor',
      location: 'Zone B',
      category: 'Fördertechnik',
      capacity: '500 Pakete/h'
    })
  },
  {
    system_name: 'Fördertechnik Zone C',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'conveyor',
      location: 'Zone C',
      category: 'Fördertechnik',
      capacity: '300 Pakete/h'
    })
  },
  
  // Schrumpfanlagen
  {
    system_name: 'Schrumpfanlage A01',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'shrink_wrap',
      location: 'Halle 1',
      category: 'Anlagen',
      model: 'A01',
      capacity: '150 Pakete/h'
    })
  },
  {
    system_name: 'Schrumpfanlage B02',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'shrink_wrap',
      location: 'Halle 2',
      category: 'Anlagen',
      model: 'B02',
      capacity: '200 Pakete/h'
    })
  },
  
  // Netzwerk
  {
    system_name: 'Wlan Access Point 1',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'network',
      location: 'Halle 1',
      category: 'Netzwerk',
      device_type: 'access_point'
    })
  },
  {
    system_name: 'Wlan Access Point 2',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'network',
      location: 'Halle 2',
      category: 'Netzwerk',
      device_type: 'access_point'
    })
  },
  {
    system_name: 'Wlan Core Switch',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'network',
      location: 'Serverraum',
      category: 'Netzwerk',
      device_type: 'core_switch'
    })
  },
  
  // Lager
  {
    system_name: 'Automatisches Trommellager',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'storage',
      location: 'Halle 1',
      category: 'Läger',
      storage_type: 'drum',
      capacity: '10000 Plätze'
    })
  },
  {
    system_name: 'Automatisches Ringlager',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'storage',
      location: 'Halle 2',
      category: 'Läger',
      storage_type: 'ring',
      capacity: '8000 Plätze'
    })
  },
  
  // Flurförderzeuge
  {
    system_name: 'Stapler Fleet Manager',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'forklift',
      location: 'Zentralsteuerung',
      category: 'Flurförderzeuge',
      fleet_size: 12
    })
  },
  {
    system_name: 'FTS System',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'agv',
      location: 'Produktionshalle',
      category: 'Flurförderzeuge',
      vehicle_count: 8
    })
  },
  
  // SAP
  {
    system_name: 'SAP ERP System',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'erp',
      location: 'Datacenter',
      category: 'SAP',
      modules: ['WM', 'PP', 'MM', 'FI']
    })
  },
  {
    system_name: 'SAP WM Interface',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'interface',
      location: 'Datacenter',
      category: 'SAP',
      connection_type: 'RFC'
    })
  },
  
  // ITM
  {
    system_name: 'ITM Warehouse Control',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'wcs',
      location: 'Serverraum',
      category: 'ITM',
      version: '2.4.1'
    })
  },
  {
    system_name: 'ITM Material Flow',
    status: 'OK' as const,
    metadata: JSON.stringify({
      type: 'mfc',
      location: 'Serverraum',
      category: 'ITM',
      throughput: '1000 Pakete/h'
    })
  }
];

async function seedSystemStatus() {
  console.log('🌱 Starting SystemStatus seeding...');
  
  try {
    // Lösche zuerst alle bestehenden Einträge
    console.log('🗑️  Clearing existing SystemStatus entries...');
    await prisma.systemStatus.deleteMany();
    console.log('✅ Cleared existing entries');
    
    // Füge alle System-Definitionen hinzu
    console.log('📝 Creating new SystemStatus entries...');
    const createdSystems = [];
    
    for (const systemDef of systemDefinitions) {
      const system = await prisma.systemStatus.create({
        data: {
          ...systemDef,
          updated_at: new Date()
        }
      });
      createdSystems.push(system);
    }
    
    console.log(`✅ Created ${createdSystems.length} SystemStatus entries`);
    
    // Zeige eine Zusammenfassung der erstellten Systeme
    const categories = createdSystems.reduce((acc, system) => {
      const metadata = system.metadata ? JSON.parse(system.metadata) : {};
      const category = metadata.category || 'Unknown';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log('\n📊 Created systems by category:');
    Object.entries(categories).forEach(([category, count]) => {
      console.log(`  - ${category}: ${count} systems`);
    });
    
    // Zeige Status-Verteilung
    const statusCounts = createdSystems.reduce((acc, system) => {
      acc[system.status] = (acc[system.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    console.log('\n🚦 Status distribution:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`  - ${status}: ${count} systems`);
    });
    
    console.log('\n🎉 SystemStatus seeding completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during SystemStatus seeding:', error);
    throw error;
  }
}

async function main() {
  try {
    await seedSystemStatus();
  } catch (error) {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Führe das Script aus, wenn es direkt aufgerufen wird
if (require.main === module) {
  main();
}

export default seedSystemStatus;
