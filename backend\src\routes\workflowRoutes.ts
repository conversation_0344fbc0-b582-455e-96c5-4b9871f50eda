import { Router } from 'express';
import { WorkflowController } from '../controllers/workflowController';

const router = Router();
const workflowController = new WorkflowController();

/**
 * GET /api/workflows/logs
 * Lädt Workflow-Logs
 * Query-Parameter:
 * - workflowId (optional): Filtert Logs für einen bestimmten Workflow
 * - limit (optional): Maximale Anzahl der Logs (default: 100)
 */
router.get('/logs', (req, res) => workflowController.getLogs(req, res));

/**
 * GET /api/workflows/processes
 * Lädt alle verfügbaren Workflow-Prozesse
 */
router.get('/processes', (req, res) => workflowController.getProcesses(req, res));

/**
 * POST /api/workflows/execute
 * Führt einen Workflow-Prozess aus
 * Body: { processId: string }
 */
router.post('/execute', (req, res) => workflowController.executeProcess(req, res));

/**
 * GET /api/workflows/executions
 * Lädt Workflow-Ausführungen
 * Query-Parameter:
 * - workflowId (optional): Filtert Ausführungen für einen bestimmten Workflow
 * - limit (optional): Maximale Anzahl der Ausführungen (default: 50)
 */
router.get('/executions', (req, res) => workflowController.getExecutions(req, res));

/**
 * GET /api/workflows/stats
 * Lädt Workflow-Statistiken
 */
router.get('/stats', (req, res) => workflowController.getStats(req, res));

export default router;