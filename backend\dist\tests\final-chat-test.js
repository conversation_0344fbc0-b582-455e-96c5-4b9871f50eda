"use strict";
/**
 * Finaler Test der Chat-Funktionalität mit spezifischen Datumsangaben
 */
Object.defineProperty(exports, "__esModule", { value: true });
const openrouter_service_1 = require("../services/openrouter.service");
const data_enrichment_service_1 = require("../services/data-enrichment.service");
const client_1 = require("@prisma/client");
async function testFinalChatFunctionality() {
    console.log('🚀 Final Chat Test with Specific Date Query\n');
    const prisma = new client_1.PrismaClient();
    const enrichmentService = new data_enrichment_service_1.DataEnrichmentService(prisma);
    try {
        const query = '<PERSON><PERSON>, kannst du mir sagen wie der Servicegrad am 17.04.2025 war?';
        console.log(`User Query: "${query}"`);
        // Step 1: Enrich context
        console.log('\n1️⃣ ENRICHING CONTEXT...');
        const enrichedContext = await enrichmentService.enrichChatContext(query);
        console.log('✅ Context enriched successfully');
        console.log(`- Has Data: ${enrichedContext.hasData}`);
        console.log(`- Data Types: ${enrichedContext.dataTypes.join(', ')}`);
        console.log(`- Database Context: ${enrichedContext.databaseContext.substring(0, 150)}...`);
        // Step 2: Generate AI response
        console.log('\n2️⃣ GENERATING AI RESPONSE...');
        const chatResponse = await openrouter_service_1.OpenRouterService.generateResponse({
            message: query,
            enrichedContext: enrichedContext,
            includeInsights: true,
            includeAnomalies: false
        });
        console.log('✅ AI response generated successfully');
        console.log(`- Response length: ${chatResponse.response.length} characters`);
        console.log(`- Has enriched context: ${chatResponse.hasEnrichedContext}`);
        console.log(`- Data types: ${chatResponse.dataTypes.join(', ')}`);
        console.log('\n📝 AI RESPONSE:');
        console.log('='.repeat(80));
        console.log(chatResponse.response);
        console.log('='.repeat(80));
        // Step 3: Verify the response contains correct data
        console.log('\n3️⃣ VERIFYING RESPONSE CONTENT...');
        const responseText = chatResponse.response.toLowerCase();
        const checks = [
            { name: 'Contains date 17.04.2025', test: responseText.includes('17.04.2025') || responseText.includes('17. april') },
            { name: 'Contains servicegrad/service level', test: responseText.includes('servicegrad') || responseText.includes('service') },
            { name: 'Contains percentage value', test: /\d+[.,]\d*\s*%/.test(responseText) },
            { name: 'Contains tonnage information', test: responseText.includes('tonnage') || responseText.includes('tonnen') },
            { name: 'Response is substantial', test: chatResponse.response.length > 100 }
        ];
        checks.forEach(check => {
            console.log(`${check.test ? '✅' : '❌'} ${check.name}`);
        });
        const allPassed = checks.every(check => check.test);
        console.log(`\n${allPassed ? '🎉' : '⚠️'} Overall test result: ${allPassed ? 'PASSED' : 'NEEDS ATTENTION'}`);
    }
    catch (error) {
        console.error('❌ Test failed:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
// Run the test
testFinalChatFunctionality().catch(console.error);
