/**
 * API-Service für die Kommunikation mit dem Backend
 * 
 * Dieser Service stellt Methoden bereit, um mit der Backend-API zu kommunizieren.
 * Er ersetzt die direkte Datenbankinteraktion im Frontend.
 */

import {
  ServiceLevelDataPoint,
  PickingDataPoint,
  TagesleistungDataPoint,
  AblaengereiRecord as AblaengereiDataPoint,
  WEDataPoint,
  Lagerauslastung200DataPoint,
  Lagerauslastung240DataPoint,
  SystemFTSDataPoint,
  DeliveryDataPoint as DeliveryPositionsDataPoint,
  QMDataPoint as ReturnsDataPoint
} from '../types/database.types';

// Create a type alias for DailyPerformanceDataPoint if it doesn't exist
interface DailyPerformanceDataPoint {
  datum: string;
  value: number;
}
import { getApiConfig, getApiConfigSync, createAuthHeaders, loadApiConfig } from './api-config.service';
import { getCache, CacheKeyGenerator } from './cache.service';

// Authentication error handler type
type AuthErrorHandler = () => void;

// API-Konfiguration Constants
const API_CONSTANTS = {
  TIMEOUT: 8000,
  CHAT_TIMEOUT: 30000, // 30 seconds for chat requests (AI responses can take longer)
  MAX_RETRIES: 3,
  RETRY_DELAY: 2000
};

// Cache-Konfiguration
const cache = getCache({
  defaultTTL: 5 * 60 * 1000, // 5 Minuten für Standard-Daten
  maxEntries: 500,
  enableLogging: process.env.NODE_ENV === 'development'
});

// Cache TTL für verschiedene Datentypen (in Millisekunden)
const CACHE_TTL = {
  STATIC_DATA: 30 * 60 * 1000, // 30 Minuten für selten ändernde Daten
  DYNAMIC_DATA: 5 * 60 * 1000, // 5 Minuten für häufig ändernde Daten
  REAL_TIME_DATA: 1 * 60 * 1000, // 1 Minute für Echtzeit-ähnliche Daten
  SYSTEM_STATS: 2 * 60 * 1000 // 2 Minuten für System-Statistiken
};

/**
 * Generische Antwortstruktur für API-Aufrufe
 */
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Hilfsfunktion für API-Aufrufe
 * @param endpoint API-Endpunkt
 * @param options Fetch-Optionen
 * @returns Typisierte API-Antwort
 */
/**
 * Hilfsfunktion zur Initialisierung der API-Konfiguration
 * Wird beim ersten API-Aufruf automatisch aufgerufen
 */
let configInitialized = false;
async function ensureConfigInitialized(): Promise<void> {
  if (!configInitialized) {
    await loadApiConfig();
    configInitialized = true;
  }
}

/**
 * Cached API-Aufruf Wrapper
 * @param cacheKey Cache-Schlüssel
 * @param apiCall API-Aufruf-Funktion
 * @param ttl Cache TTL (optional)
 * @returns Cached oder frische Daten
 */
async function cachedApiCall<T>(
  cacheKey: string,
  apiCall: () => Promise<T>,
  ttl?: number
): Promise<T> {
  // Versuche Daten aus dem Cache zu laden
  const cachedData = cache.get<T>(cacheKey);
  if (cachedData !== undefined) {
    return cachedData;
  }

  // Cache Miss - führe API-Aufruf durch
  try {
    const data = await apiCall();

    // Speichere nur gültige Daten im Cache
    if (data !== null && data !== undefined) {
      cache.set(cacheKey, data, ttl);
    }

    return data;
  } catch (error) {
    // Bei Fehlern keine Daten cachen
    throw error;
  }
}

const fetchApi = async <T>(
  endpoint: string,
  options: RequestInit = {},
  retryCount = 0
): Promise<T> => {
  // Stelle sicher, dass die Konfiguration geladen ist
  await ensureConfigInitialized();
  const config = getApiConfigSync();

  // Stelle sicher, dass der Endpunkt mit einem Slash beginnt
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  const url = `${config.apiBaseUrl}${normalizedEndpoint}`;

  console.log(`[${new Date().toISOString()}] API-Aufruf (Versuch ${retryCount + 1}): ${url}`, {
    method: options.method || 'GET',
    authenticated: !!config.apiKey,
    environment: config.environment
  });

  try {
    const controller = new AbortController();
    // Use longer timeout for chat endpoints
    const isChatEndpoint = url.includes('/chat') || url.includes('/rag');
    const timeoutMs = isChatEndpoint ? API_CONSTANTS.CHAT_TIMEOUT : API_CONSTANTS.TIMEOUT;
    const timeoutId = setTimeout(() => {
      console.warn(`API-Aufruf an ${url} hat das Timeout von ${timeoutMs}ms überschritten`);
      controller.abort();
    }, timeoutMs);

    const fetchPromise = fetch(url, {
      ...options,
      signal: controller.signal,
      headers: createAuthHeaders(options.headers as Record<string, string>),
      credentials: 'include',
    });

    // Timeout- und Fetch-Logik kombinieren
    const response = await Promise.race([
      fetchPromise,
      new Promise<Response>((_, reject) =>
        setTimeout(() => reject(new Error(`API-Timeout nach ${timeoutMs}ms`)), timeoutMs)
      )
    ]).finally(() => clearTimeout(timeoutId));

    console.log(`[${new Date().toISOString()}] API-Antwort (${response.status}): ${response.statusText}`, {
      url,
      status: response.status,
      statusText: response.statusText
    });

    if (!response.ok) {
      // Spezielle Behandlung für Authentifizierungsfehler
      if (response.status === 401) {
        console.error('❌ Authentifizierungsfehler: Token ungültig oder abgelaufen');

        // Try token refresh first before logging out
        try {
          const { authService } = await import('@/services/auth.service');
          const refreshResult = await authService.refreshToken();
          
          if (refreshResult.success && refreshResult.data?.token) {
            console.log('✅ Token successfully refreshed, retrying request');
            // Retry the original request with new token
            const retryOptions = {
              ...options,
              headers: {
                ...options.headers,
                'Authorization': `Bearer ${refreshResult.data.token}`
              }
            };
            
            const retryResponse = await fetch(url, retryOptions);
            if (retryResponse.ok) {
              const retryText = await retryResponse.text();
              return retryText ? JSON.parse(retryText) : {};
            }
          }
        } catch (refreshError) {
          console.error('❌ Token refresh failed:', refreshError);
        }

        // If refresh failed or retry failed, trigger logout
        if (globalAuthErrorHandler) {
          console.log('🚪 Triggering automatic logout due to 401 response');
          globalAuthErrorHandler();
        }

        throw new Error('Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.');
      }

      if (response.status === 403) {
        console.error('❌ Autorisierungsfehler: Zugriff verweigert');
        throw new Error('Zugriff verweigert. Sie haben nicht die erforderlichen Berechtigungen.');
      }

      // Bei Serverfehlern (5xx) wiederholen
      if (response.status >= 500 && retryCount < API_CONSTANTS.MAX_RETRIES - 1) {
        const delay = Math.min(API_CONSTANTS.RETRY_DELAY * (retryCount + 1), 10000); // Max 10 Sekunden Verzögerung
        console.warn(`Serverfehler (${response.status}), wiederhole in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return fetchApi<T>(endpoint, options, retryCount + 1);
      }

      let errorDetails = '';
      try {
        const errorBody = await response.text();
        errorDetails = errorBody;
        console.error('API-Fehler-Details:', errorBody);

        // Versuche, den Fehler als JSON zu parsen, falls möglich
        try {
          const errorJson = JSON.parse(errorBody);
          errorDetails = errorJson.error || errorJson.message || errorBody;
        } catch (e) {
          // Kein JSON, verwende den rohen Text
        }
      } catch (e) {
        console.error('Fehler beim Lesen der Fehlerdetails:', e);
      }

      throw new Error(
        `API-Fehler (${response.status}): ${response.statusText}${errorDetails ? ` - ${errorDetails}` : ''}`
      );
    }

    const responseText = await response.text();
    let parsed: any = null;

    // Robustere JSON-Parsing-Logik inkl. leerem Body und falschem Content-Type
    if (!responseText || responseText.trim().length === 0) {
      // 200 mit leerem Body -> als leeres Objekt interpretieren
      parsed = {};
    } else {
      try {
        parsed = JSON.parse(responseText);
      } catch (e) {
        // Wenn Content-Type nicht JSON ist oder der Body kein gültiges JSON enthält,
        // aber der Status 200 ist, gebe den rohen Text zurück statt generischen Fehler.
        console.error('Fehler beim Parsen der API-Antwort:', e, 'Antwort:', responseText);
        // Für 200-OK: Tolerant sein und den Text als Daten behandeln
        if (response.status >= 200 && response.status < 300) {
          // Versuche einfache Erfolgsform zu konstruieren
          return (undefined as unknown) as T;
        }
        throw new Error('Ungültiges JSON in der API-Antwort');
      }
    }

    // Einheitliche Normalisierung: Backend kann entweder {success,data,error} ODER direkt Daten liefern
    const data: ApiResponse<T> | T = parsed;

    // Fall A: standardisierte ApiResponse-Struktur
    if (data && typeof data === 'object' && 'success' in (data as any)) {
      const apiResp = data as ApiResponse<T>;

      // Wenn success explizit false ist -> Fehler werfen
      if (apiResp.success === false) {
        console.error('API-Fehler:', apiResp.error || 'Unbekannter Fehler', {
          url,
          status: response.status,
          error: apiResp.error,
          data: apiResp.data
        });
        throw new Error(apiResp.error || 'Unbekannter API-Fehler');
      }

      // success true oder success fehlt aber ohnedies in diesem Zweig -> Daten extrahieren
      return (apiResp.data as T);
    }

    // Fall B: keine ApiResponse-Hülle -> Direktdaten zurückgeben
    return (data as T);
  } catch (error: unknown) {
    console.error(`API-Fehler bei ${url}:`, error);

    // Typenprüfung für den Fehler
    const isNetworkError =
      error instanceof Error && (
        error.name === 'AbortError' ||
        error.name === 'TimeoutError' ||
        (error.message && error.message.includes('timeout')) ||
        !navigator.onLine
      );

    if (isNetworkError && retryCount < API_CONSTANTS.MAX_RETRIES - 1) {
      const delay = Math.min(API_CONSTANTS.RETRY_DELAY * (retryCount + 1), 10000);
      console.warn(`Netzwerkfehler, wiederhole in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return fetchApi<T>(endpoint, options, retryCount + 1);
    }

    // Fehlermeldung für den Benutzer aufbereiten
    let errorMessage = 'Ein unbekannter Fehler ist aufgetreten';

    if (error instanceof Error) {
      if (isNetworkError) {
        errorMessage = `Netzwerkfehler: ${error.message || 'Verbindung zum Backend-Server fehlgeschlagen'}`;
      } else {
        errorMessage = error.message;
      }
    }

    throw new Error(errorMessage);
  }
}

/**
 * Global authentication error handler
 * This will be set by the AuthContext to handle automatic logout
 */
let globalAuthErrorHandler: AuthErrorHandler | null = null;

/**
 * Sets the global authentication error handler
 * @param handler Function to call when authentication errors occur
 */
export const setAuthErrorHandler = (handler: AuthErrorHandler): void => {
  globalAuthErrorHandler = handler;
};

/**
 * API-Service für die Kommunikation mit dem Backend
 */
export class ApiService {
  /**
   * Gets the base URL for API calls
   * @returns The base API URL
   */
  getBaseUrl(): string {
    const config = getApiConfigSync();
    return config.apiBaseUrl;
  }

  /**
   * Ruft die Servicelevel-Daten für das Diagramm ab
   * @returns Promise mit den Servicelevel-Daten
   */
  async getServiceLevelData(): Promise<ServiceLevelDataPoint[]> {
    const cacheKey = CacheKeyGenerator.forDataType('service-level');
    return await cachedApiCall(
      cacheKey,
      async () => (await fetchApi<ServiceLevelDataPoint[]>('/database/service-level')) ?? [],
      CACHE_TTL.DYNAMIC_DATA
    );
  }

  /**
   * Ruft die täglichen Leistungsdaten für das Diagramm ab
   * @returns Promise mit den täglichen Leistungsdaten
   */
  async getDailyPerformanceData(): Promise<DailyPerformanceDataPoint[]> {
    const cacheKey = CacheKeyGenerator.forDataType('daily-performance');
    return await cachedApiCall(
      cacheKey,
      async () => (await fetchApi<DailyPerformanceDataPoint[]>('/database/daily-performance')) ?? [],
      CACHE_TTL.DYNAMIC_DATA
    );
  }

  /**
   * Ruft die Kommissionierungsdaten für das Diagramm ab
   * @returns Promise mit den Kommissionierungsdaten
   */
  async getPickingData(): Promise<PickingDataPoint[]> {
    const cacheKey = CacheKeyGenerator.forDataType('picking');
    return await cachedApiCall(
      cacheKey,
      async () => (await fetchApi<PickingDataPoint[]>('/database/picking')) ?? [],
      CACHE_TTL.REAL_TIME_DATA
    );
  }

  /**
   * Ruft die Retourendaten für das Diagramm ab
   * @returns Promise mit den Retourendaten
   */
  async getReturnsData(): Promise<ReturnsDataPoint[]> {
    const cacheKey = CacheKeyGenerator.forDataType('returns');
    return await cachedApiCall(
      cacheKey,
      async () => (await fetchApi<ReturnsDataPoint[]>('/database/returns')) ?? [],
      CACHE_TTL.DYNAMIC_DATA
    );
  }

  /**
   * Ruft die Lieferpositionsdaten für das Diagramm ab
   * @returns Promise mit den Lieferpositionsdaten
   */
  async getDeliveryPositionsData(): Promise<DeliveryPositionsDataPoint[]> {
    const cacheKey = CacheKeyGenerator.forDataType('delivery-positions');
    return await cachedApiCall(
      cacheKey,
      async () => (await fetchApi<DeliveryPositionsDataPoint[]>('/database/delivery-positions')) ?? [],
      CACHE_TTL.DYNAMIC_DATA
    );
  }

  /**
   * Ruft die Tagesleistungsdaten für das Diagramm ab
   * @returns Promise mit den Tagesleistungsdaten
   */
  async getTagesleistungData(): Promise<TagesleistungDataPoint[]> {
    const cacheKey = CacheKeyGenerator.forDataType('tagesleistung');
    return await cachedApiCall(
      cacheKey,
      async () => (await fetchApi<TagesleistungDataPoint[]>('/database/tagesleistung')) ?? [],
      CACHE_TTL.DYNAMIC_DATA
    );
  }

  // CSV-Import wurde entfernt, da nicht verwendet

  /**
   * Ruft die Daten aus der Ablaengerei-Tabelle ab
   * @returns Promise mit den Ablaengerei-Daten
   */
  async getAblaengereiData(): Promise<AblaengereiDataPoint[]> {
    const cacheKey = CacheKeyGenerator.forDataType('ablaengerei');
    return await cachedApiCall(
      cacheKey,
      async () => (await fetchApi<AblaengereiDataPoint[]>('/database/ablaengerei')) ?? [],
      CACHE_TTL.DYNAMIC_DATA
    );
  }

  /**
   * Ruft die WE-Daten (Wareneingang) für das Diagramm ab
   * @returns Promise mit den WE-Daten
   */
  async getSchnitteData(): Promise<any[]> {
    const cacheKey = CacheKeyGenerator.forDataType('schnitte-data');
    return await cachedApiCall(
      cacheKey,
      async () => (await fetchApi<any[]>('/database/schnitte-data')) ?? [],
      CACHE_TTL.REAL_TIME_DATA
    );
  }

  async getMaschinenEfficiency(): Promise<any[]> {
    const cacheKey = CacheKeyGenerator.forDataType('maschinen-efficiency');
    return await cachedApiCall(
      cacheKey,
      async () => (await fetchApi<any[]>('/database/maschinen-efficiency')) ?? [],
      CACHE_TTL.REAL_TIME_DATA
    );
  }

  async getCuttingChartData(): Promise<any[]> {
    const cacheKey = CacheKeyGenerator.forDataType('cutting-chart-data');
    return await cachedApiCall(
      cacheKey,
      async () => (await fetchApi<any[]>('/database/cutting-chart-data')) ?? [],
      CACHE_TTL.REAL_TIME_DATA
    );
  }

  async getLagerCutsChartData(): Promise<any[]> {
    const cacheKey = CacheKeyGenerator.forDataType('lager-cuts-chart-data');
    return await cachedApiCall(
      cacheKey,
      async () => (await fetchApi<any[]>('/database/lager-cuts-chart-data')) ?? [],
      CACHE_TTL.REAL_TIME_DATA
    );
  }

  async getAtrlData(startDate?: string, endDate?: string): Promise<any[]> {
    const params = new URLSearchParams();
    if (startDate) {
      params.append('startDate', startDate);
    }
    if (endDate) {
      params.append('endDate', endDate);
    }
    const url = `/database/atrl-data${params.toString() ? `?${params.toString()}` : ''}`;
    return (await fetchApi<any[]>(url)) ?? [];
  }

  async getArilData(startDate?: string, endDate?: string): Promise<any[]> {
    const params = new URLSearchParams();
    if (startDate) {
      params.append('startDate', startDate);
    }
    if (endDate) {
      params.append('endDate', endDate);
    }
    const url = `/database/aril-data${params.toString() ? `?${params.toString()}` : ''}`;
    return (await fetchApi<any[]>(url)) ?? [];
  }
  async getWEData(): Promise<WEDataPoint[]> {
    try {
      const data = await fetchApi<WEDataPoint[]>('/database/we');
      console.log('WE-Daten empfangen:', data);
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der WE-Daten:', error);
      throw error;
    }
  }

  /**
   * Ruft die Lagerauslastung200-Daten für das Diagramm ab
   * @param startDate - Optionales Startdatum im Format DD.MM.YY
   * @param endDate - Optionales Enddatum im Format DD.MM.YY
   * @returns Promise mit den Lagerauslastung200-Daten
   */
  async getLagerauslastung200Data(startDate?: string, endDate?: string): Promise<Lagerauslastung200DataPoint[]> {
    try {
      console.log('Starte Abruf der Lagerauslastung 200 Daten...');

      // Erstelle URL-Parameter für die Datumsfilterung
      const params = new URLSearchParams();

      if (startDate) {
        console.log('Startdatum:', startDate);
        params.append('startDate', startDate);
      } else {
        // Standardmäßig: letzte 30 Tage
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const defaultStartDate = thirtyDaysAgo.toISOString().split('T')[0];
        params.append('startDate', defaultStartDate);
      }

      if (endDate) {
        console.log('Enddatum:', endDate);
        params.append('endDate', endDate);
      }

      // URL mit Parametern erstellen
      const url = `/database/lagerauslastung200${params.toString() ? `?${params.toString()}` : ''}`;
      console.log('API-URL:', url);

      // Daten vom Backend abrufen
      const data = await fetchApi<Lagerauslastung200DataPoint[]>(url);
      console.log('Lagerauslastung 200 Daten empfangen:', data);

      // Stelle sicher, dass die Daten das erwartete Format haben
      if (!Array.isArray(data)) {
        console.error('Unerwartetes Datenformat:', data);
        throw new Error('Unerwartetes Antwortformat vom Server');
      }

      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Lagerauslastung 200:', error);
      throw error;
    }
  }

  /**
   * Ruft die Lagerauslastung240-Daten für das Diagramm ab
   * @param startDate - Optionales Startdatum im Format YYYY-MM-DD
   * @param endDate - Optionales Enddatum im Format YYYY-MM-DD
   * @returns Promise mit den Lagerauslastung240-Daten
   */
  async getLagerauslastung240Data(startDate?: string, endDate?: string): Promise<Lagerauslastung240DataPoint[]> {
    const params = { startDate, endDate };
    const cacheKey = CacheKeyGenerator.forDataType('lagerauslastung240', params);

    return await cachedApiCall(
      cacheKey,
      async () => {
        try {
          console.log('Starte Abruf der Lagerauslastung 240 Daten...');
          const urlParams = new URLSearchParams();

          if (startDate) {
            console.log('Startdatum:', startDate);
            urlParams.append('startDate', startDate);
          }

          if (endDate) {
            console.log('Enddatum:', endDate);
            urlParams.append('endDate', endDate);
          }

          // URL mit Parametern erstellen
          const url = `/database/lagerauslastung240${urlParams.toString() ? `?${urlParams.toString()}` : ''}`;
          console.log('API-URL:', url);

          const data = await fetchApi<Lagerauslastung240DataPoint[]>(url);
          console.log('Lagerauslastung 240 Daten empfangen:', data);
          return data;
        } catch (error) {
          console.error('Fehler beim Abrufen der Lagerauslastung 240:', error);
          throw error;
        }
      },
      CACHE_TTL.DYNAMIC_DATA
    );
  }

  /**
   * Berechnet System-Statistiken für den angegebenen Zeitraum
   * @param startDate Optionales Startdatum für die Statistik (Format: YYYY-MM-DD)
   * @param endDate Optionales Enddatum für die Statistik (Format: YYYY-MM-DD)
   * @returns Promise mit den System-Statistiken
   */
  async getSystemStats(startDate?: string, endDate?: string): Promise<any> {
    const params = { startDate, endDate };
    const cacheKey = CacheKeyGenerator.forDataType('system-stats', params);

    return await cachedApiCall(
      cacheKey,
      async () => {
        // Erstellen der Query-Parameter, falls Daten angegeben wurden
        const queryParams = [];
        if (startDate) queryParams.push(`startDate=${startDate}`);
        if (endDate) queryParams.push(`endDate=${endDate}`);

        const queryString = queryParams.length > 0 ? `?${queryParams.join('&')}` : '';

        return fetchApi<any>(`/database/system-stats${queryString}`);
      },
      CACHE_TTL.SYSTEM_STATS
    );
  }

  /**
   * System FTS Verfügbarkeitsdaten abrufen
   */
  async getSystemFTSData(startDate?: string, endDate?: string): Promise<SystemFTSDataPoint[]> {
    const params = { startDate, endDate };
    const cacheKey = CacheKeyGenerator.forDataType('system-fts-data', params);

    return await cachedApiCall(
      cacheKey,
      async () => {
        // Erstellen der Query-Parameter für Datums-Filterung
        const queryParams = [];
        if (startDate) queryParams.push(`startDate=${startDate}`);
        if (endDate) queryParams.push(`endDate=${endDate}`);

        const queryString = queryParams.length > 0 ? `?${queryParams.join('&')}` : '';

        return fetchApi<SystemFTSDataPoint[]>(`/database/system-fts-data${queryString}`);
      },
      CACHE_TTL.DYNAMIC_DATA // 5 Minuten Cache für System FTS Daten
    );
  }


  /**
   * Cache-Management Methoden
   */

  /**
   * Invalidiert Cache-Einträge basierend auf Datentyp
   * @param dataType Datentyp zum Invalidieren (z.B. 'service-level', 'picking')
   */
  invalidateCache(dataType: string): void {
    const prefix = CacheKeyGenerator.prefixFor('data') + dataType;
    const deletedCount = cache.deleteByPrefix(prefix);
    console.log(`Cache invalidiert für '${dataType}': ${deletedCount} Einträge gelöscht`);
  }

  /**
   * Invalidiert alle API-Cache-Einträge
   */
  invalidateAllCache(): void {
    cache.clear();
    console.log('Gesamter API-Cache wurde geleert');
  }

  /**
   * Gibt Cache-Statistiken zurück
   */
  // Rückgabetyp vermeiden, um Export-Namensproblem (CacheStats) zu umgehen
  getCacheStats(): unknown {
    return cache.getStats();
  }

  /**
   * Generic GET method for API calls
   * @param endpoint - The API endpoint to call
   * @returns Promise with response data
   */
  async get<T>(endpoint: string): Promise<T> {
    return await fetchApi<T>(endpoint);
  }

  /**
   * Generic PUT method for API calls
   * @param endpoint - The API endpoint to call
   * @param data - Optional data to send
   * @returns Promise with response data
   */
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return await fetchApi<T>(endpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: data ? JSON.stringify(data) : undefined
    });
  }

  /**
   * Generic POST method for API calls
   * @param endpoint - The API endpoint to call
   * @param data - Optional data to send
   * @returns Promise with response data
   */
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return await fetchApi<T>(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: data ? JSON.stringify(data) : undefined
    });
  }

  /**
   * Generic DELETE method for API calls
   * @param endpoint - The API endpoint to call
   * @returns Promise with response data
   */
  async delete<T>(endpoint: string): Promise<T> {
    return await fetchApi<T>(endpoint, {
      method: 'DELETE'
    });
  }
}

// Singleton-Instanz des API-Service
const apiService = new ApiService();
export default apiService;
export { apiService };

/**
 * Cache-Management Funktionen für globalen Zugriff
 */
export const cacheManager = {
  invalidate: (dataType: string) => apiService.invalidateCache(dataType),
  invalidateAll: () => apiService.invalidateAllCache(),
  // Rückgabetyp nicht benennen, um externen Typ CacheStats zu vermeiden
  getStats: (): unknown => apiService.getCacheStats()
};
