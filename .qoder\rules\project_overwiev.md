---
trigger: model_decision
description: Use this rule if you need a basic overview of the project
---

# Project Overview

## Project Overview

This project is a desktop application named "SFM Electron" designed for visualizing logistics and production data. It features a dashboard with various charts and statistics to provide insights into key performance indicators. The application is built with a modern technology stack, including:

*   **Core:** Electron, Vite, SWC
*   **UI:** React, Tailwind CSS, shadcn/ui, Recharts
*   **State Management:** React Query (TanStack)
*   **Routing:** TanStack Router
*   **Backend:** Node.js with Express.js
*   **Database:** Prisma with better-sqlite3
*   **Languages:** TypeScript
*   **Testing:** Vitest, Playwright

The application has a client-server architecture. The frontend is a single-page application built with React and Vite, while the backend is a Node.js server built with Express.js that provides a REST API for the frontend. The application is packaged as a desktop app using Electron Forge.

## Architecture

### Frontend

The frontend is a single-page application built with React and Vite. It is located in the `src` directory. The main entry point for the frontend is `src/main.ts`. The application uses TanStack Router for routing, and the routes are defined in the `src/routes` directory. The UI is built with React components, and the styling is done with Tailwind CSS and shadcn/ui.

### Backend

The backend is a Node.js server built with Express.js. It is located in the `backend` directory. The main entry point for the backend is `backend/src/server.ts`. The backend provides a REST API for the frontend, which is used to fetch data from the database and to perform other operations. The backend uses Prisma as an ORM to interact with the database.

### Database

The application uses a SQLite database to store its data. The database is located in the `backend/database` directory. The database schema is defined in `backend/prisma-sfm-dashboard/schema.prisma` and `backend/prisma-rag/schema.prisma`. Prisma is used to manage the database schema and to generate the database client.

## Important Files

*   `src/App.tsx`: The main application component.
*   `src/routes/router.tsx`: The router configuration.
*   `src/routes/__root.tsx`: The root layout of the application.
*   `backend/src/server.ts`: The main entry point for the backend server.
*   `backend/prisma-sfm-dashboard/schema.prisma`: The database schema for the main application.
*   `backend/prisma-rag/schema.prisma`: The database schema for the RAG feature.
*   `package.json`: The main `package.json` file for the frontend application.
*   `backend/package.json`: The `package.json` file for the backend server.
*   `forge.config.ts`: The configuration file for Electron Forge.

## Development Workflow

### Creating a New Module

To create a new module, you need to create a new directory in the `src/routes/modules` directory. The name of the directory will be the name of the module. Inside the module directory, you need to create a `index.tsx` file that exports the routes for the module. You can then add the module to the `allModuleRoutes` array in `src/routes/router.tsx`.

### Adding a New Route

To add a new route, you need to create a new file in the `src/pages` directory. The name of the file will be the name of the page. You can then add the route to the `src/routes/router.tsx` file.

### Working with the Database

To work with the database, you need to use the Prisma client. The Prisma client is generated automatically from the database schema. You can use the Prisma client to query the database, to create new records, and to update existing records. To regenerate the Prisma client, run `npm run prisma:generate` from the root directory.

## Building and Running

### Prerequisites

*   Node.js (version >=20.12 <21)
*   npm

### Installation

1.  Install the dependencies for the frontend and backend:

    ```bash
    npm install
    npm run backend:prepare
    ```

### Running the Application

To run the application in development mode, use the following command:

```bash
npm run dev
```

This will start both the frontend and backend servers in watch mode. The application will automatically reload when you make changes to the code.

### Building the Application

To build the application for production, use the following command:

```bash
npm run make
```

This will create a distributable package for your operating system in the `out` directory.

### Testing

The project includes unit tests and end-to-end tests.

*   To run the unit tests:

    ```bash
    npm run test:unit
    ```

*   To run the end-to-end tests:

    ```bash
    npm run test:e2e
    ```

## Development Conventions

*   **Code Style:** The project uses Prettier for code formatting and ESLint for linting. You can run the linter with `npm run lint`.
*   **Commits:** (No information on commit conventions was found in the analyzed files.)
*   **Branching:** (No information on branching strategy was found in the analyzed files.)
*   **Testing:** The project has a comprehensive test suite, including unit tests, integration tests, and end-to-end tests. All new features should be accompanied by tests.
*   **Backend Development:** The backend is located in the `backend` directory. It is a separate Node.js project with its own `package.json` file. When working on the backend, make sure to run `npm install` in the `backend` directory to install its dependencies.
*   **Database:** The project uses Prisma as an ORM. The database schema is defined in `backend/prisma-sfm-dashboard/schema.prisma` and `backend/prisma-rag/schema.prisma`. To generate the Prisma client, run `npm run prisma:generate` from the root directory.

---
