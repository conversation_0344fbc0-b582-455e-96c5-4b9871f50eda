"use strict";
/**
 * Chat-Routen für die direkte Kommunikation mit dem KI-Assistenten
 *
 * Diese Routen sind unter /api/chat verfügbar und leiten direkt an die KI-Dienste weiter.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const zod_1 = require("zod");
const validation_middleware_1 = require("../middleware/validation.middleware");
const rate_limiting_middleware_1 = require("../middleware/rate-limiting.middleware");
const openrouter_service_1 = __importDefault(require("../services/openrouter.service"));
const data_enrichment_service_1 = __importDefault(require("../services/data-enrichment.service"));
const performance_monitoring_service_1 = __importDefault(require("../services/performance-monitoring.service"));
const client_1 = require("@prisma-sfm-dashboard/client");
const router = express_1.default.Router();
// Initialize Prisma client and data enrichment service
const prisma = new client_1.PrismaClient();
const dataEnrichmentService = new data_enrichment_service_1.default(prisma);
// Validierungsschema für Chat-Anfragen
const chatRequestSchema = zod_1.z.object({
    message: zod_1.z.string().min(1, "Nachricht darf nicht leer sein").max(1000, "Nachricht zu lang"),
    includeInsights: zod_1.z.boolean().optional().default(false),
    includeAnomalies: zod_1.z.boolean().optional().default(false)
});
// Einfacher Chat-Endpunkt
router.post('/', rate_limiting_middleware_1.rateLimitConfig.general, (0, validation_middleware_1.createValidationMiddleware)({ body: chatRequestSchema }), async (req, res) => {
    const startTime = Date.now();
    try {
        const { message } = req.body;
        console.log(`📝 [CHAT] Anfrage erhalten: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
        // Anfrage an OpenRouter-Service weiterleiten
        const llmStartTime = Date.now();
        const response = await openrouter_service_1.default.generateResponse({
            message,
            includeInsights: false,
            includeAnomalies: false
        });
        const llmTime = Date.now() - llmStartTime;
        const totalTime = Date.now() - startTime;
        // Record performance metrics
        performance_monitoring_service_1.default.recordResponseTime('/api/chat', false, // not enriched
        totalTime, 0, // no enrichment time
        llmTime, JSON.stringify(response).length);
        console.log(`✅ [CHAT] Antwort gesendet für: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}" (${totalTime}ms)`);
        res.json(response);
    }
    catch (error) {
        const totalTime = Date.now() - startTime;
        // Record failed response time
        performance_monitoring_service_1.default.recordResponseTime('/api/chat', false, totalTime, 0, 0, 0);
        console.error('❌ [CHAT] Fehler:', error);
        res.status(500).json({
            success: false,
            error: 'Interner Serverfehler',
            message: 'Bei der Verarbeitung der Chat-Anfrage ist ein Fehler aufgetreten.'
        });
    }
});
// Erweiterter Chat-Endpunkt mit Insights und Anomalien
router.post('/enhanced', rate_limiting_middleware_1.rateLimitConfig.general, (0, validation_middleware_1.createValidationMiddleware)({ body: chatRequestSchema }), async (req, res) => {
    var _a;
    const startTime = Date.now();
    try {
        const { message, includeInsights, includeAnomalies } = req.body;
        console.log(`📝 [CHAT-ENHANCED] Anfrage erhalten: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}" (Insights: ${includeInsights}, Anomalien: ${includeAnomalies})`);
        let enrichedContext;
        let dataEnrichmentError = false;
        let enrichmentDetails = {};
        let enrichmentTime = 0;
        try {
            // Attempt to enrich context with database information
            console.log(`🔍 [CHAT-ENHANCED] Enriching context for message...`);
            const enrichmentStartTime = Date.now();
            enrichedContext = await dataEnrichmentService.enrichChatContext(message);
            enrichmentTime = Date.now() - enrichmentStartTime;
            // Collect enrichment details for response metadata
            enrichmentDetails = {
                requestId: enrichedContext.requestId,
                processingTime: enrichedContext.processingTime,
                fallbackReason: enrichedContext.fallbackReason,
                partialFailure: enrichedContext.partialFailure
            };
            if (enrichedContext.hasData) {
                console.log(`✅ [CHAT-ENHANCED] Context enriched with data types: ${enrichedContext.dataTypes.join(', ')} (${enrichedContext.processingTime}ms)`);
                if (enrichedContext.partialFailure) {
                    console.warn(`⚠️ [CHAT-ENHANCED] Partial failure in data enrichment - some repositories unavailable`);
                }
            }
            else {
                console.log(`📝 [CHAT-ENHANCED] No relevant database context found (${enrichedContext.fallbackReason || 'no intents detected'})`);
            }
        }
        catch (enrichmentError) {
            enrichmentTime = Date.now() - startTime;
            console.error('⚠️ [CHAT-ENHANCED] Data enrichment failed, continuing with basic response:', enrichmentError);
            dataEnrichmentError = true;
            enrichmentDetails = {
                error: enrichmentError instanceof Error ? enrichmentError.message : 'Unknown enrichment error',
                fallbackUsed: true
            };
            // Continue without enriched context - graceful fallback
        }
        // Anfrage an OpenRouter-Service weiterleiten
        const llmStartTime = Date.now();
        const response = await openrouter_service_1.default.generateResponse({
            message,
            includeInsights,
            includeAnomalies,
            enrichedContext: dataEnrichmentError ? undefined : enrichedContext
        });
        const llmTime = Date.now() - llmStartTime;
        const totalTime = Date.now() - startTime;
        // Add comprehensive metadata about data enrichment to response
        const enhancedResponse = {
            ...response,
            dataEnrichmentUsed: !dataEnrichmentError && (enrichedContext === null || enrichedContext === void 0 ? void 0 : enrichedContext.hasData),
            dataEnrichmentError: dataEnrichmentError,
            enrichmentDetails,
            detectedIntents: ((_a = enrichedContext === null || enrichedContext === void 0 ? void 0 : enrichedContext.detectedIntents) === null || _a === void 0 ? void 0 : _a.map(intent => ({
                type: intent.type,
                confidence: intent.confidence,
                keywords: intent.keywords
            }))) || [],
            dataQuality: {
                hasFullData: (enrichedContext === null || enrichedContext === void 0 ? void 0 : enrichedContext.hasData) && !(enrichedContext === null || enrichedContext === void 0 ? void 0 : enrichedContext.partialFailure),
                hasPartialData: (enrichedContext === null || enrichedContext === void 0 ? void 0 : enrichedContext.hasData) && (enrichedContext === null || enrichedContext === void 0 ? void 0 : enrichedContext.partialFailure),
                fallbackUsed: !!(enrichedContext === null || enrichedContext === void 0 ? void 0 : enrichedContext.fallbackReason) || dataEnrichmentError,
                availableDataTypes: (enrichedContext === null || enrichedContext === void 0 ? void 0 : enrichedContext.dataTypes) || []
            },
            performanceMetrics: {
                totalTime,
                enrichmentTime,
                llmTime,
                cacheUsed: (enrichedContext === null || enrichedContext === void 0 ? void 0 : enrichedContext.requestId) ? false : true // Simplified cache detection
            }
        };
        // Record performance metrics
        performance_monitoring_service_1.default.recordResponseTime('/api/chat/enhanced', true, // enriched
        totalTime, enrichmentTime, llmTime, JSON.stringify(enhancedResponse).length);
        console.log(`✅ [CHAT-ENHANCED] Antwort gesendet für: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}" (DB-Context: ${enhancedResponse.dataEnrichmentUsed}, ${totalTime}ms)`);
        res.json(enhancedResponse);
    }
    catch (error) {
        const totalTime = Date.now() - startTime;
        // Record failed response time
        performance_monitoring_service_1.default.recordResponseTime('/api/chat/enhanced', true, totalTime, 0, 0, 0);
        console.error('❌ [CHAT-ENHANCED] Fehler:', error);
        res.status(500).json({
            success: false,
            error: 'Interner Serverfehler',
            message: 'Bei der Verarbeitung der erweiterten Chat-Anfrage ist ein Fehler aufgetreten.'
        });
    }
});
// Cleanup handler for Prisma client
process.on('beforeExit', async () => {
    await prisma.$disconnect();
});
exports.default = router;
