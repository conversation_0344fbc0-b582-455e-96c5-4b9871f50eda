/**
 * Report Template Manager Component
 * 
 * Component for managing report templates - viewing, editing, and deleting
 */

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileText, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  Calendar,
  Users,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import type { ReportTemplate } from '@/types/reporting';

interface ReportTemplateManagerProps {
  templates: ReportTemplate[];
  loading: boolean;
  onSelectTemplate: (template: ReportTemplate | null) => void;
  onUpdateTemplate: (templateId: string, updates: Partial<ReportTemplate>) => Promise<void>;
  onDeleteTemplate: (templateId: string) => Promise<void>;
  onRefresh: () => Promise<void>;
}

/**
 * Report Template Manager Component
 */
export const ReportTemplateManager: React.FC<ReportTemplateManagerProps> = ({
  templates,
  loading,
  onSelectTemplate,
  onUpdateTemplate,
  onDeleteTemplate,
  onRefresh
}) => {
  // State for filtering and search
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterDepartment, setFilterDepartment] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [deletingTemplate, setDeletingTemplate] = useState<string | null>(null);

  /**
   * Filter templates based on search and filters
   */
  const filteredTemplates = templates.filter(template => {
    // Search filter
    if (searchTerm && !template.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !template.description.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }

    // Type filter
    if (filterType !== 'all' && template.type !== filterType) {
      return false;
    }

    // Department filter
    if (filterDepartment !== 'all' && template.department !== filterDepartment && template.department !== 'all') {
      return false;
    }

    // Status filter
    if (filterStatus === 'active' && !template.isActive) {
      return false;
    }
    if (filterStatus === 'inactive' && template.isActive) {
      return false;
    }
    if (filterStatus === 'scheduled' && (!template.schedule || !template.schedule.isActive)) {
      return false;
    }

    return true;
  });

  /**
   * Handle template selection
   */
  const handleSelectTemplate = useCallback((template: ReportTemplate) => {
    setSelectedTemplate(template);
    onSelectTemplate(template);
  }, [onSelectTemplate]);

  /**
   * Handle template activation toggle
   */
  const handleToggleActive = useCallback(async (template: ReportTemplate) => {
    try {
      await onUpdateTemplate(template.id, { isActive: !template.isActive });
    } catch (error) {
      console.error('Failed to toggle template status:', error);
    }
  }, [onUpdateTemplate]);

  /**
   * Handle template deletion
   */
  const handleDeleteTemplate = useCallback(async (template: ReportTemplate) => {
    if (!confirm(`Sind Sie sicher, dass Sie die Vorlage "${template.name}" löschen möchten?`)) {
      return;
    }

    try {
      setDeletingTemplate(template.id);
      await onDeleteTemplate(template.id);
      if (selectedTemplate?.id === template.id) {
        setSelectedTemplate(null);
        onSelectTemplate(null);
      }
    } catch (error) {
      console.error('Failed to delete template:', error);
    } finally {
      setDeletingTemplate(null);
    }
  }, [onDeleteTemplate, selectedTemplate, onSelectTemplate]);

  /**
   * Get template type display name
   */
  const getTypeDisplayName = (type: string) => {
    const types: Record<string, string> = {
      'kpi': 'KPI-Bericht',
      'performance': 'Leistungsbericht',
      'analysis': 'Analysebericht',
      'custom': 'Benutzerdefiniert'
    };
    return types[type] || type;
  };

  /**
   * Get department display name
   */
  const getDepartmentDisplayName = (department?: string) => {
    const departments: Record<string, string> = {
      'all': 'Alle Abteilungen',
      'dispatch': 'Versand',
      'cutting': 'Ablängerei',
      'incoming-goods': 'Wareneingang'
    };
    return departments[department || 'all'] || department;
  };

  /**
   * Get format display name
   */
  const getFormatDisplayName = (format: string) => {
    return format.toUpperCase();
  };

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filter und Suche
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Vorlagen durchsuchen..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger>
                  <SelectValue placeholder="Typ" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle Typen</SelectItem>
                  <SelectItem value="kpi">KPI-Bericht</SelectItem>
                  <SelectItem value="performance">Leistungsbericht</SelectItem>
                  <SelectItem value="analysis">Analysebericht</SelectItem>
                  <SelectItem value="custom">Benutzerdefiniert</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Select value={filterDepartment} onValueChange={setFilterDepartment}>
                <SelectTrigger>
                  <SelectValue placeholder="Abteilung" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle Abteilungen</SelectItem>
                  <SelectItem value="dispatch">Versand</SelectItem>
                  <SelectItem value="cutting">Ablängerei</SelectItem>
                  <SelectItem value="incoming-goods">Wareneingang</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle Status</SelectItem>
                  <SelectItem value="active">Aktiv</SelectItem>
                  <SelectItem value="inactive">Inaktiv</SelectItem>
                  <SelectItem value="scheduled">Geplant</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Templates List */}
      {loading ? (
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  <div className="flex space-x-2">
                    <div className="h-6 bg-gray-200 rounded w-16"></div>
                    <div className="h-6 bg-gray-200 rounded w-20"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredTemplates.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || filterType !== 'all' || filterDepartment !== 'all' || filterStatus !== 'all'
                ? 'Keine Vorlagen gefunden'
                : 'Keine Vorlagen vorhanden'
              }
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || filterType !== 'all' || filterDepartment !== 'all' || filterStatus !== 'all'
                ? 'Versuchen Sie, Ihre Suchkriterien zu ändern.'
                : 'Erstellen Sie Ihre erste Berichtsvorlage.'
              }
            </p>
            {(searchTerm || filterType !== 'all' || filterDepartment !== 'all' || filterStatus !== 'all') && (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('');
                  setFilterType('all');
                  setFilterDepartment('all');
                  setFilterStatus('all');
                }}
              >
                Filter zurücksetzen
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredTemplates.map((template) => (
            <Card 
              key={template.id}
              className={`cursor-pointer transition-colors ${
                selectedTemplate?.id === template.id ? 'ring-2 ring-blue-500' : 'hover:bg-gray-50'
              }`}
              onClick={() => handleSelectTemplate(template)}
            >
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <CardTitle className="text-lg flex items-center">
                      <FileText className="h-5 w-5 mr-2" />
                      {template.name}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {template.description}
                    </CardDescription>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <Badge variant={template.isActive ? "default" : "secondary"}>
                      {template.isActive ? (
                        <>
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Aktiv
                        </>
                      ) : (
                        <>
                          <XCircle className="h-3 w-3 mr-1" />
                          Inaktiv
                        </>
                      )}
                    </Badge>
                    
                    {template.schedule?.isActive && (
                      <Badge variant="outline">
                        <Clock className="h-3 w-3 mr-1" />
                        Geplant
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                  <div>
                    <p className="font-medium text-gray-600">Typ</p>
                    <p>{getTypeDisplayName(template.type)}</p>
                  </div>
                  
                  <div>
                    <p className="font-medium text-gray-600">Abteilung</p>
                    <p>{getDepartmentDisplayName(template.department)}</p>
                  </div>
                  
                  <div>
                    <p className="font-medium text-gray-600">Format</p>
                    <p>{getFormatDisplayName(template.format)}</p>
                  </div>
                  
                  <div>
                    <p className="font-medium text-gray-600">Abschnitte</p>
                    <p>{template.sections.length}</p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>Erstellt: {template.createdAt.toLocaleDateString('de-DE')}</span>
                    <span>Aktualisiert: {template.updatedAt.toLocaleDateString('de-DE')}</span>
                    {template.recipients && template.recipients.length > 0 && (
                      <span className="flex items-center">
                        <Users className="h-3 w-3 mr-1" />
                        {template.recipients.length} Empfänger
                      </span>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleToggleActive(template);
                      }}
                    >
                      {template.isActive ? 'Deaktivieren' : 'Aktivieren'}
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: Implement edit functionality
                        console.log('Edit template:', template.id);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteTemplate(template);
                      }}
                      disabled={deletingTemplate === template.id}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Schedule Information */}
                {template.schedule?.isActive && (
                  <Alert className="mt-4">
                    <Calendar className="h-4 w-4" />
                    <AlertDescription>
                      Automatische Generierung: {template.schedule.frequency === 'daily' ? 'Täglich' :
                        template.schedule.frequency === 'weekly' ? 'Wöchentlich' :
                        template.schedule.frequency === 'monthly' ? 'Monatlich' : 'Vierteljährlich'} 
                      um {template.schedule.time} Uhr
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Selected Template Details */}
      {selectedTemplate && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Vorlagen-Details: {selectedTemplate.name}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Abschnitte ({selectedTemplate.sections.length})</h4>
                <div className="grid gap-2">
                  {selectedTemplate.sections.map((section, index) => (
                    <div key={section.id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <span className="font-medium">{section.title}</span>
                        <span className="text-sm text-gray-600 ml-2">
                          ({section.type} - {section.dataSource})
                        </span>
                      </div>
                      <Badge variant="outline">{section.order + 1}</Badge>
                    </div>
                  ))}
                </div>
              </div>

              {selectedTemplate.recipients && selectedTemplate.recipients.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">E-Mail-Empfänger ({selectedTemplate.recipients.length})</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedTemplate.recipients.map((email) => (
                      <Badge key={email} variant="secondary">
                        {email}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ReportTemplateManager;