import React, { Suspense } from "react";
import DashboardNavigation from "@/modules/dashboard/components/DashboardNavigation";
import { StoerungNavigation } from "@/modules/stoerungen/components";
import { BackendNavigation } from "@/modules/backend/components";
import { AINavigation } from "@/modules/ai/components/navigation";
import { GlobalNavigation } from "@/components/navigation/GlobalNavigation";
import { BaseNavigation } from "@/components/navigation/BaseNavigation";
import { Outlet, useLocation } from "@tanstack/react-router";
import { Loader2 } from "lucide-react";
import { ErrorBoundaryWrapper } from "@/components/ErrorBoundary";
import { DialogProvider } from "@/components/ui/dialog";
import { settingsNavigationConfig } from "@/config/navigation/settings.config";
import { userNavigationConfig } from "@/config/navigation/user.config";
import { ChatBot } from "@/modules/ai/components";




/**
 * Dashboard-Layout für die Shopfloor-Management-App
 * 
 * Einfaches Layout mit:
 * - DragWindowRegion für Navigation
 * - Maximaler Platz für den Hauptinhalt
 * - Error Boundary und Suspense für bessere UX
 */
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Aktuelle Route ermitteln
  const location = useLocation();
  // Prüfen, ob wir auf der UserLandingPage sind
  const isUserLandingPage = location.pathname === '/';
  // Prüfen, welches Modul aktiv ist
  const isDashboardModule = location.pathname.startsWith('/modules/dashboard');
  const isStoerungenModule = location.pathname.startsWith('/modules/stoerungen');
  const isBackendModule = location.pathname.startsWith('/modules/backend');
  const isAIModule = location.pathname.startsWith('/modules/ai');
  // Prüfen, ob wir auf globalen Seiten sind (Settings, UserSettings)
  const isSettingsPage = location.pathname === '/settings';
  const isUserSettingsPage = location.pathname === '/user-settings';
  const isGlobalPage = isSettingsPage || isUserSettingsPage;

  // Debug-Ausgabe
  console.log('🔍 DashboardLayout - Aktuelle Route:', location.pathname);
  console.log('🔍 DashboardLayout - Ist UserLandingPage:', isUserLandingPage);
  console.log('🔍 DashboardLayout - Ist Dashboard-Modul:', isDashboardModule);
  console.log('🔍 DashboardLayout - Ist Störungen-Modul:', isStoerungenModule);
  console.log('🔍 DashboardLayout - Ist Backend-Modul:', isBackendModule);
  console.log('🔍 DashboardLayout - Ist AI-Modul:', isAIModule);
  console.log('🔍 DashboardLayout - Ist globale Seite:', isGlobalPage);

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Navigation - nur anzeigen, wenn wir nicht auf der UserLandingPage sind */}
      {!isUserLandingPage && (
        <>
          {/* Modul-spezifische Navigation für Modul-Seiten */}
          {isDashboardModule && <DashboardNavigation title="" />}
          {isStoerungenModule && <StoerungNavigation title="" />}
          {isBackendModule && <BackendNavigation title="" />}
          {isAIModule && <AINavigation title="" />}

          {/* Spezifische Navigation für Settings/UserSettings */}
          {isSettingsPage && <BaseNavigation title="" navigationConfig={settingsNavigationConfig} />}
          {isUserSettingsPage && <BaseNavigation title="" navigationConfig={userNavigationConfig} />}

          {/* Fallback für andere Seiten - Dashboard Navigation */}
          {!isDashboardModule && !isStoerungenModule && !isBackendModule && !isAIModule && !isGlobalPage && (
            <DashboardNavigation title="" />
          )}
        </>
      )}

      {/* Hauptinhalt ohne Sidebar */}
      <main className="flex-1 overflow-auto bg-background">
        <div className="w-full h-full">
          {/* Container für Seiteninhalte */}
          <div className="w-full h-full">
            {/* DialogProvider für Dialog-Funktionalität */}
            <DialogProvider>
              {/* Outlet für Seiteninhalte with Error Boundary and Suspense fallback */}
              <ErrorBoundaryWrapper>
                <Suspense fallback={
                  <div className="flex flex-col h-80 w-full items-center justify-center">
                    <Loader2 className="h-12 w-12 animate-spin" />
                    <p className="mt-4 font-bold">Lade Seite...</p>
                  </div>
                }>
                  {children || <Outlet />}
                </Suspense>
              </ErrorBoundaryWrapper>
            </DialogProvider>
          </div>
        </div>
      </main>
      
      {/* AI Chatbot - Floating Button */}
      <ChatBot />
    </div>
  );
}
