/**
 * Delivery Repository
 * 
 * Repository for managing delivery data and logistics information
 */

import { BaseRepository } from './base.repository';
import { DeliveryRequest, DeliveryRoute } from '@/types/supply-chain-optimization';

export interface DeliveryStats {
  totalDeliveries: number;
  onTimeDeliveries: number;
  averageDeliveryTime: number; // days
  totalDistance: number; // km
  totalCost: number; // euros
  activeRoutes: number;
}

export interface DeliveryHistoryRecord {
  deliveryId: string;
  supplierId: string;
  orderDate: Date;
  deliveryDate: Date;
  promisedTime: number; // days
  actualTime: number; // days
  wasLate: boolean;
  productType: string;
  urgency: 'low' | 'medium' | 'high';
  quantity: number;
  value: number; // euros
  distance?: number; // km
  route?: string;
  delayReason?: string;
}

/**
 * Repository for delivery and logistics data management
 */
export class DeliveryRepository extends BaseRepository {
  protected repositoryName = 'DeliveryRepository';
  protected logger = { 
    info: (msg: string, data?: any) => console.log(`[${this.repositoryName}] ${msg}`, data),
    error: (msg: string, data?: any) => console.error(`[${this.repositoryName}] ${msg}`, data)
  };

  constructor() {
    super();
  }

  /**
   * Get all delivery history (required by base repository)
   */
  async getAll(): Promise<DeliveryHistoryRecord[]> {
    return this.getDeliveryHistory({ days: 30 });
  }

  /**
   * Get overall delivery statistics
   */
  async getDeliveryStats(): Promise<DeliveryStats> {
    try {
      // Mock implementation - in real app, this would query the database
      const totalDeliveries = Math.floor(Math.random() * 1000) + 500;
      const onTimeDeliveries = Math.floor(totalDeliveries * (0.7 + Math.random() * 0.25));
      
      return {
        totalDeliveries,
        onTimeDeliveries,
        averageDeliveryTime: 5.5 + Math.random() * 3, // 5.5-8.5 days
        totalDistance: Math.floor(Math.random() * 50000) + 25000, // km
        totalCost: Math.floor(Math.random() * 100000) + 50000, // euros
        activeRoutes: Math.floor(Math.random() * 20) + 10
      };
    } catch (error) {
      this.logger.error('Failed to get delivery stats:', error);
      throw new Error(`Failed to get delivery stats: ${error}`);
    }
  }

  /**
   * Get delivery history for a specific supplier
   */
  async getSupplierDeliveryHistory(
    supplierId: string, 
    timeRange: { days: number }
  ): Promise<DeliveryHistoryRecord[]> {
    try {
      this.logger.info(`Getting delivery history for supplier ${supplierId} (${timeRange.days} days)`);
      
      // Mock implementation - generate sample delivery history
      const history: DeliveryHistoryRecord[] = [];
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeRange.days);
      
      // Generate 5-25 delivery records
      const recordCount = Math.floor(Math.random() * 20) + 5;
      
      const productTypes = ['Kabel', 'Stecker', 'Komponenten', 'Werkzeug', 'Zubehör'];
      const delayReasons = [
        'Wetterbedingungen',
        'Verkehrsstau',
        'Lieferantenprobleme',
        'Qualitätsprüfung',
        'Kapazitätsengpass',
        'Dokumentationsfehler'
      ];
      
      for (let i = 0; i < recordCount; i++) {
        const deliveryDate = new Date(startDate.getTime() + Math.random() * timeRange.days * 24 * 60 * 60 * 1000);
        const promisedTime = Math.floor(Math.random() * 10) + 3; // 3-12 days
        const actualTime = promisedTime + (Math.random() - 0.75) * 4; // Some variation, mostly on time
        const wasLate = actualTime > promisedTime;
        
        const record: DeliveryHistoryRecord = {
          deliveryId: `DEL_${supplierId}_${Date.now()}_${i}`,
          supplierId,
          orderDate: new Date(deliveryDate.getTime() - Math.max(actualTime, 1) * 24 * 60 * 60 * 1000),
          deliveryDate,
          promisedTime,
          actualTime: Math.max(actualTime, 1),
          wasLate,
          productType: productTypes[Math.floor(Math.random() * productTypes.length)],
          urgency: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',
          quantity: Math.floor(Math.random() * 1000) + 50,
          value: Math.floor(Math.random() * 10000) + 500,
          distance: Math.floor(Math.random() * 500) + 50,
          route: `Route_${Math.floor(Math.random() * 10) + 1}`,
          delayReason: wasLate ? delayReasons[Math.floor(Math.random() * delayReasons.length)] : undefined
        };
        
        history.push(record);
      }
      
      return history.sort((a, b) => b.deliveryDate.getTime() - a.deliveryDate.getTime());
    } catch (error) {
      this.logger.error(`Failed to get delivery history for supplier ${supplierId}:`, error);
      throw new Error(`Failed to get delivery history: ${error}`);
    }
  }

  /**
   * Get all delivery history within time range
   */
  async getDeliveryHistory(timeRange: { days: number }): Promise<DeliveryHistoryRecord[]> {
    try {
      this.logger.info(`Getting all delivery history (${timeRange.days} days)`);
      
      // Mock implementation - generate comprehensive delivery history
      const history: DeliveryHistoryRecord[] = [];
      const supplierIds = ['SUP_001', 'SUP_002', 'SUP_003', 'SUP_004', 'SUP_005'];
      
      for (const supplierId of supplierIds) {
        const supplierHistory = await this.getSupplierDeliveryHistory(supplierId, timeRange);
        history.push(...supplierHistory);
      }
      
      return history.sort((a, b) => b.deliveryDate.getTime() - a.deliveryDate.getTime());
    } catch (error) {
      this.logger.error('Failed to get delivery history:', error);
      throw new Error(`Failed to get delivery history: ${error}`);
    }
  }

  /**
   * Get active delivery routes
   */
  async getActiveRoutes(): Promise<DeliveryRoute[]> {
    try {
      this.logger.info('Getting active delivery routes');
      
      // Mock implementation - generate sample active routes
      const routes: DeliveryRoute[] = [];
      const routeCount = Math.floor(Math.random() * 10) + 5;
      
      for (let i = 0; i < routeCount; i++) {
        const deliveryCount = Math.floor(Math.random() * 5) + 1;
        const deliveries: DeliveryRequest[] = [];
        
        for (let j = 0; j < deliveryCount; j++) {
          deliveries.push({
            deliveryId: `DEL_${i}_${j}`,
            destination: `Destination ${i}-${j}`,
            items: [{
              itemId: `ITEM_${i}_${j}`,
              quantity: Math.floor(Math.random() * 100) + 10,
              weight: Math.random() * 50 + 5,
              dimensions: {
                length: Math.random() * 100 + 20,
                width: Math.random() * 50 + 10,
                height: Math.random() * 30 + 5,
                volume: 0 // Will be calculated
              }
            }],
            priority: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low'
          });
        }
        
        // Calculate volumes
        deliveries.forEach(delivery => {
          delivery.items.forEach(item => {
            item.dimensions.volume = item.dimensions.length * item.dimensions.width * item.dimensions.height;
          });
        });
        
        const route: DeliveryRoute = {
          routeId: `ROUTE_${i + 1}`,
          deliveries,
          totalDistance: Math.floor(Math.random() * 300) + 50,
          estimatedTime: Math.floor(Math.random() * 480) + 120, // 2-8 hours
          cost: Math.floor(Math.random() * 500) + 100
        };
        
        routes.push(route);
      }
      
      return routes;
    } catch (error) {
      this.logger.error('Failed to get active routes:', error);
      throw new Error(`Failed to get active routes: ${error}`);
    }
  }

  /**
   * Get delivery performance metrics
   */
  async getDeliveryPerformanceMetrics(timeRange: { days: number }): Promise<any> {
    try {
      const history = await this.getDeliveryHistory(timeRange);
      
      if (history.length === 0) {
        return {
          onTimeRate: 0.8,
          averageDeliveryTime: 7,
          totalDeliveries: 0,
          delayFrequency: 0.2,
          averageDelay: 2
        };
      }
      
      const onTimeDeliveries = history.filter(d => !d.wasLate).length;
      const onTimeRate = onTimeDeliveries / history.length;
      
      const totalDeliveryTime = history.reduce((sum, d) => sum + d.actualTime, 0);
      const averageDeliveryTime = totalDeliveryTime / history.length;
      
      const lateDeliveries = history.filter(d => d.wasLate);
      const delayFrequency = lateDeliveries.length / history.length;
      
      const totalDelay = lateDeliveries.reduce((sum, d) => sum + (d.actualTime - d.promisedTime), 0);
      const averageDelay = lateDeliveries.length > 0 ? totalDelay / lateDeliveries.length : 0;
      
      return {
        onTimeRate,
        averageDeliveryTime,
        totalDeliveries: history.length,
        delayFrequency,
        averageDelay
      };
    } catch (error) {
      this.logger.error('Failed to get delivery performance metrics:', error);
      throw new Error(`Failed to get delivery performance metrics: ${error}`);
    }
  }

  /**
   * Get delivery trends
   */
  async getDeliveryTrends(timeRange: { days: number }): Promise<any[]> {
    try {
      const history = await this.getDeliveryHistory(timeRange);
      
      // Group deliveries by week
      const weeklyData = new Map<string, any>();
      
      history.forEach(delivery => {
        const weekStart = new Date(delivery.deliveryDate);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        const weekKey = weekStart.toISOString().split('T')[0];
        
        if (!weeklyData.has(weekKey)) {
          weeklyData.set(weekKey, {
            week: weekStart,
            totalDeliveries: 0,
            onTimeDeliveries: 0,
            totalTime: 0,
            totalDistance: 0,
            totalCost: 0
          });
        }
        
        const weekData = weeklyData.get(weekKey)!;
        weekData.totalDeliveries++;
        if (!delivery.wasLate) weekData.onTimeDeliveries++;
        weekData.totalTime += delivery.actualTime;
        weekData.totalDistance += delivery.distance || 0;
        weekData.totalCost += delivery.value;
      });
      
      // Convert to array and calculate metrics
      const trends = Array.from(weeklyData.values()).map(week => ({
        week: week.week,
        onTimeRate: week.totalDeliveries > 0 ? week.onTimeDeliveries / week.totalDeliveries : 0,
        averageDeliveryTime: week.totalDeliveries > 0 ? week.totalTime / week.totalDeliveries : 0,
        totalDeliveries: week.totalDeliveries,
        totalDistance: week.totalDistance,
        totalCost: week.totalCost
      }));
      
      return trends.sort((a, b) => a.week.getTime() - b.week.getTime());
    } catch (error) {
      this.logger.error('Failed to get delivery trends:', error);
      throw new Error(`Failed to get delivery trends: ${error}`);
    }
  }

  /**
   * Get common delay reasons
   */
  async getDelayReasons(timeRange: { days: number }): Promise<any[]> {
    try {
      const history = await this.getDeliveryHistory(timeRange);
      const lateDeliveries = history.filter(d => d.wasLate && d.delayReason);
      
      const reasonCounts = new Map<string, { count: number; totalDelay: number }>();
      
      lateDeliveries.forEach(delivery => {
        if (delivery.delayReason) {
          const existing = reasonCounts.get(delivery.delayReason) || { count: 0, totalDelay: 0 };
          existing.count++;
          existing.totalDelay += (delivery.actualTime - delivery.promisedTime);
          reasonCounts.set(delivery.delayReason, existing);
        }
      });
      
      return Array.from(reasonCounts.entries()).map(([reason, data]) => ({
        reason,
        frequency: data.count,
        percentage: lateDeliveries.length > 0 ? (data.count / lateDeliveries.length) * 100 : 0,
        averageDelay: data.count > 0 ? data.totalDelay / data.count : 0,
        impact: data.count > 5 ? 'high' : data.count > 2 ? 'medium' : 'low'
      })).sort((a, b) => b.frequency - a.frequency);
    } catch (error) {
      this.logger.error('Failed to get delay reasons:', error);
      throw new Error(`Failed to get delay reasons: ${error}`);
    }
  }

  /**
   * Create new delivery record
   */
  async createDelivery(delivery: Partial<DeliveryHistoryRecord>): Promise<string> {
    try {
      const deliveryId = `DEL_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      this.logger.info(`Creating new delivery record: ${deliveryId}`);
      
      // Mock implementation - in real app, this would insert into database
      this.logger.info('Delivery record created:', { deliveryId, ...delivery });
      
      return deliveryId;
    } catch (error) {
      this.logger.error('Failed to create delivery record:', error);
      throw new Error(`Failed to create delivery record: ${error}`);
    }
  }

  /**
   * Update delivery status
   */
  async updateDeliveryStatus(deliveryId: string, status: string, actualDeliveryTime?: number): Promise<void> {
    try {
      this.logger.info(`Updating delivery status for ${deliveryId}: ${status}`);
      
      // Mock implementation - in real app, this would update the database
      this.logger.info('Delivery status updated:', { deliveryId, status, actualDeliveryTime });
    } catch (error) {
      this.logger.error(`Failed to update delivery status for ${deliveryId}:`, error);
      throw new Error(`Failed to update delivery status: ${error}`);
    }
  }

  /**
   * Get deliveries by route
   */
  async getDeliveriesByRoute(routeId: string): Promise<DeliveryHistoryRecord[]> {
    try {
      const allHistory = await this.getDeliveryHistory({ days: 30 });
      return allHistory.filter(d => d.route === routeId);
    } catch (error) {
      this.logger.error(`Failed to get deliveries for route ${routeId}:`, error);
      throw new Error(`Failed to get deliveries by route: ${error}`);
    }
  }

  /**
   * Get seasonal delivery patterns
   */
  async getSeasonalPatterns(): Promise<any[]> {
    try {
      // Mock implementation - generate seasonal patterns
      const seasons = [
        { season: 'Spring', months: [2, 3, 4], deliveryTimeFactor: 1.0, riskFactor: 0.9 },
        { season: 'Summer', months: [5, 6, 7], deliveryTimeFactor: 0.9, riskFactor: 0.8 },
        { season: 'Autumn', months: [8, 9, 10], deliveryTimeFactor: 1.1, riskFactor: 1.0 },
        { season: 'Winter', months: [11, 0, 1], deliveryTimeFactor: 1.3, riskFactor: 1.4 }
      ];
      
      return seasons.map(season => ({
        ...season,
        description: `${season.season}: ${season.deliveryTimeFactor > 1 ? 'Längere' : 'Kürzere'} Lieferzeiten, ${season.riskFactor > 1 ? 'höheres' : 'niedrigeres'} Risiko`
      }));
    } catch (error) {
      this.logger.error('Failed to get seasonal patterns:', error);
      throw new Error(`Failed to get seasonal patterns: ${error}`);
    }
  }
}

export default DeliveryRepository;