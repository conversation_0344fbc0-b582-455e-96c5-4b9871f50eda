'use client';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import { ReactNode } from 'react';

interface BentoSettingsCardProps {
  title: string;
  description: string;
  icon: ReactNode;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  children?: ReactNode;
  onClick?: () => void;
}

/**
 * Bento-Grid-Karte für Settings-Seiten
 * 
 * Moderne, animierte Karte im Bento-Stil mit Icon, Titel, Beschreibung
 * und optionalem Inhalt für Einstellungen.
 */
export const BentoSettingsCard = ({
  title,
  description,
  icon,
  className,
  size = 'small',
  children,
  onClick,
}: BentoSettingsCardProps) => {
  const variants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: 'spring' as const, damping: 25 },
    },
  };

  return (
    <motion.div
      variants={variants}
      onClick={onClick}
      className={cn(
        'group border-orange-200/20 bg-background hover:border-orange-300/40 relative flex h-full flex-col justify-between overflow-hidden rounded-xl border px-6 pt-6 pb-6 shadow-md transition-all duration-500 hover:shadow-lg',
        onClick && 'cursor-pointer',
        className,
      )}
    >
      {/* Hintergrund-Pattern */}
      <div className="absolute top-0 -right-1/2 z-0 size-full bg-[linear-gradient(to_right,#fb923320_1px,transparent_1px),linear-gradient(to_bottom,#fb923320_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]"></div>

      {/* Großes Hintergrund-Icon */}
      <div className="text-orange-100/10 group-hover:text-orange-200/20 absolute right-1 bottom-3 scale-[6] transition-all duration-700 group-hover:scale-[6.2]">
        {icon}
      </div>

      <div className="relative z-10 flex h-full flex-col justify-between">
        <div>
          {/* Icon-Badge */}
          <div className="bg-orange-100/20 text-orange-600 shadow-orange-100/20 group-hover:bg-orange-200/30 group-hover:shadow-orange-200/30 mb-4 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500">
            {icon}
          </div>
          
          {/* Titel und Beschreibung */}
          <h3 className="mb-2 text-xl font-semibold tracking-tight text-gray-900 dark:text-white">{title}</h3>
          <p className="text-muted-foreground text-sm mb-4">{description}</p>
          
          {/* Inhalt (Settings-Komponenten) */}
          {children && (
            <div className="space-y-4">
              {children}
            </div>
          )}
        </div>
        
        {/* Learn More Link (nur wenn onClick vorhanden) */}
        {onClick && (
          <div className="text-orange-600 mt-4 flex items-center text-sm font-medium">
            <span className="mr-1">Konfigurieren</span>
            <ArrowRight className="size-4 transition-all duration-500 group-hover:translate-x-2" />
          </div>
        )}
      </div>
      
      {/* Orange Gradient am unteren Rand */}
      <div className="from-orange-400 to-orange-600/30 absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-2xl transition-all duration-500 group-hover:blur-lg" />
    </motion.div>
  );
};

interface BentoSettingsGridProps {
  children: ReactNode;
  className?: string;
}

/**
 * Container für Bento-Settings-Cards
 * 
 * Responsive Grid-Layout mit Stagger-Animation für die Karten.
 */
export const BentoSettingsGrid = ({ children, className }: BentoSettingsGridProps) => {
  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.12,
        delayChildren: 0.1,
      },
    },
  };

  return (
    <motion.div
      className={cn(
        "grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
        className
      )}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {children}
    </motion.div>
  );
};
