# CRUSH Development Guide

## Build Commands
- `npm run dev` - Start full-stack development (frontend + backend)
- `npm run dev:app` - Start frontend only (Electron)
- `npm run dev:backend` - Start backend only
- `npm run build:portable` - Build portable Windows .exe
- `npm run test:unit` - Run unit tests (Vitest)
- `npm run test:e2e` - Run e2e tests (Playwright)
- `npm --prefix backend run test` - Run backend unit tests
- `npm --prefix backend run test:watch` - Run backend tests in watch mode

## Code Style
- **TypeScript only** - Use precise types, interfaces, utility types
- **React functional components** with hooks (useState, useEffect, etc.)
- **TailwindCSS** for all styling - NO inline styles or external CSS
- **Shadcn-UI** components as building blocks (Button, Dialog, Card, etc.)
- **NO comments** unless explicitly requested
- Use `@/` path alias for src imports
- Use `async/await` for all async operations
- Use meaningful names for variables, functions, components

## File Structure
- Components: Use PascalCase (e.g., `UserProfile.tsx`)
- Services: Use camelCase with .service.ts suffix
- Types: Use .types.ts suffix for type definitions
- Hooks: Use camelCase starting with "use" prefix

## Error Handling
- Use Zod for validation
- Implement proper error boundaries in React
- Use try/catch with async/await
- Return structured error objects with success/error flags

## Testing
- Use Vitest for unit tests
- Use Playwright for e2e tests
- Test components with @testing-library/react
- Follow AAA pattern (Arrange, Act, Assert)