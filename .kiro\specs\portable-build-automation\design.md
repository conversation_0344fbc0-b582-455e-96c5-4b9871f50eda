# Design Document

## Overview

The enhanced portable build automation system will extend the existing build infrastructure to provide a robust, reliable, and comprehensive solution for creating distribution-ready portable executables of the SFM Electron Dashboard. The system will build upon the current PowerShell automation while adding advanced error handling, validation, testing, and packaging capabilities.

The design leverages the existing Electron Forge + Vite setup and enhances the current `build-portable.ps1` script with modular components, configuration management, and comprehensive validation workflows.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Build Orchestrator] --> B[Prerequisites Validator]
    A --> C[Build Engine]
    A --> D[Asset Manager]
    A --> E[Validation Engine]
    A --> F[Package Generator]
    A --> G[Test Runner]
    
    B --> B1[Node.js Check]
    B --> B2[Dependencies Check]
    B --> B3[Database Check]
    
    C --> C1[Process Cleanup]
    C --> C2[Electron Forge Build]
    C --> C3[Vite Compilation]
    
    D --> D1[Database Copy]
    D --> D2[Assets Copy]
    D --> D3[Images Copy]
    
    E --> E1[File Validation]
    E --> E2[Integrity Check]
    E --> E3[Size Validation]
    
    F --> F1[Archive Creation]
    F --> F2[Metadata Generation]
    F --> F3[Checksum Generation]
    
    G --> G1[Smoke Tests]
    G --> G2[Component Tests]
    G --> G3[Integration Tests]
```

### Component Architecture

The system will be organized into modular PowerShell modules and configuration files:

```
scripts/
├── build-system/
│   ├── BuildOrchestrator.psm1      # Main build coordination
│   ├── PrerequisitesValidator.psm1  # System validation
│   ├── BuildEngine.psm1            # Core build logic
│   ├── AssetManager.psm1           # File and asset management
│   ├── ValidationEngine.psm1       # Build validation
│   ├── PackageGenerator.psm1       # Distribution packaging
│   ├── TestRunner.psm1             # Automated testing
│   └── Logger.psm1                 # Logging utilities
├── config/
│   ├── build-profiles.json         # Build configuration profiles
│   ├── validation-rules.json       # Validation criteria
│   └── package-templates.json      # Package metadata templates
└── build-portable-enhanced.ps1     # Enhanced main script
```

## Components and Interfaces

### 1. Build Orchestrator

**Purpose:** Central coordination of the entire build process

**Interface:**
```powershell
# Main entry point
Start-PortableBuild -Profile [string] -OutputPath [string] -Verbose [switch]

# Configuration management
Get-BuildConfiguration -Profile [string]
Set-BuildConfiguration -Profile [string] -Config [hashtable]
```

**Responsibilities:**
- Load and validate build configuration
- Coordinate execution of all build phases
- Handle global error recovery and cleanup
- Generate comprehensive build reports

### 2. Prerequisites Validator

**Purpose:** Validate system requirements before build starts

**Interface:**
```powershell
# System validation
Test-BuildPrerequisites -Config [hashtable]
Test-NodeJsInstallation
Test-DependenciesInstalled
Test-DatabaseAvailability
```

**Validation Checks:**
- Node.js version compatibility
- npm dependencies installation status
- Database file existence and accessibility
- Available disk space
- Process conflicts

### 3. Build Engine

**Purpose:** Execute the core Electron build process

**Interface:**
```powershell
# Build execution
Start-ElectronBuild -Config [hashtable]
Stop-ConflictingProcesses -ProcessNames [string[]]
Invoke-ForgePackage -OutputPath [string]
Copy-ViteArtifacts -SourcePath [string] -TargetPath [string]
```

**Features:**
- Intelligent process cleanup with retry logic
- Incremental build support
- Build artifact management
- Error recovery mechanisms

### 4. Asset Manager

**Purpose:** Handle database, images, and resource file management

**Interface:**
```powershell
# Asset management
Copy-DatabaseFiles -SourcePath [string] -TargetPath [string]
Copy-ApplicationAssets -Config [hashtable]
Validate-AssetIntegrity -AssetPath [string]
```

**Capabilities:**
- Automatic database detection and copying
- Asset integrity verification
- Directory structure preservation
- Selective asset inclusion based on configuration

### 5. Validation Engine

**Purpose:** Comprehensive validation of build outputs

**Interface:**
```powershell
# Validation operations
Test-BuildOutput -BuildPath [string] -Rules [hashtable]
Test-FileIntegrity -FilePath [string]
Test-ExecutableStartup -ExePath [string]
Generate-ValidationReport -Results [hashtable]
```

**Validation Types:**
- File existence and size validation
- Executable integrity checks
- Database connectivity tests
- Asset accessibility verification
- Performance benchmarks

### 6. Package Generator

**Purpose:** Create distribution-ready packages

**Interface:**
```powershell
# Package creation
New-DistributionPackage -BuildPath [string] -Config [hashtable]
New-CompressedArchive -SourcePath [string] -OutputPath [string]
Generate-PackageMetadata -BuildInfo [hashtable]
Generate-Checksums -PackagePath [string]
```

**Package Types:**
- Standalone executable directory
- Compressed ZIP archive
- Self-extracting archive (optional)
- Installer package (future enhancement)

### 7. Test Runner

**Purpose:** Automated testing of portable builds

**Interface:**
```powershell
# Test execution
Start-BuildTests -BuildPath [string] -TestSuite [string]
Test-ApplicationStartup -ExePath [string]
Test-DatabaseConnectivity -BuildPath [string]
Test-UIComponents -BuildPath [string]
```

**Test Categories:**
- Smoke tests (basic startup and shutdown)
- Component tests (database, UI, routing)
- Integration tests (end-to-end workflows)
- Performance tests (startup time, memory usage)

## Data Models

### Build Configuration Schema

```json
{
  "profile": {
    "name": "string",
    "description": "string",
    "version": "string"
  },
  "build": {
    "outputDirectory": "string",
    "cleanBeforeBuild": "boolean",
    "incrementalBuild": "boolean",
    "compressionLevel": "number"
  },
  "assets": {
    "includeDatabase": "boolean",
    "databasePath": "string",
    "includeImages": "boolean",
    "customAssets": ["string"]
  },
  "validation": {
    "performSmokeTests": "boolean",
    "validateIntegrity": "boolean",
    "checkFileSize": "boolean",
    "minimumSizeThreshold": "number"
  },
  "packaging": {
    "createArchive": "boolean",
    "generateChecksums": "boolean",
    "includeReadme": "boolean",
    "customMetadata": "object"
  }
}
```

### Build Report Schema

```json
{
  "buildInfo": {
    "timestamp": "datetime",
    "profile": "string",
    "version": "string",
    "duration": "number"
  },
  "results": {
    "success": "boolean",
    "warnings": ["string"],
    "errors": ["string"]
  },
  "artifacts": {
    "executable": {
      "path": "string",
      "size": "number",
      "checksum": "string"
    },
    "database": {
      "included": "boolean",
      "size": "number",
      "checksum": "string"
    },
    "assets": {
      "count": "number",
      "totalSize": "number"
    }
  },
  "validation": {
    "testsRun": "number",
    "testsPassed": "number",
    "testsFailed": "number",
    "details": ["object"]
  }
}
```

## Error Handling

### Error Categories and Recovery Strategies

1. **Prerequisites Errors**
   - Missing Node.js: Provide installation instructions
   - Missing dependencies: Suggest `npm install`
   - Database not found: Offer to continue without or create minimal DB

2. **Build Process Errors**
   - Process conflicts: Implement retry with exponential backoff
   - Disk space issues: Clean temporary files and retry
   - Permission errors: Suggest running as administrator

3. **Asset Management Errors**
   - File copy failures: Retry with different methods
   - Integrity check failures: Re-copy and validate
   - Missing assets: Continue with warnings or fail based on criticality

4. **Validation Errors**
   - Executable startup failures: Provide diagnostic information
   - Test failures: Generate detailed failure reports
   - Size validation failures: Check for missing components

### Error Recovery Mechanisms

```powershell
# Retry logic with exponential backoff
function Invoke-WithRetry {
    param(
        [scriptblock]$ScriptBlock,
        [int]$MaxRetries = 3,
        [int]$DelaySeconds = 1
    )
    
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            return & $ScriptBlock
        }
        catch {
            if ($i -eq $MaxRetries) { throw }
            Start-Sleep -Seconds ($DelaySeconds * [Math]::Pow(2, $i-1))
        }
    }
}
```

## Testing Strategy

### Test Levels

1. **Unit Tests**
   - Individual PowerShell module functions
   - Configuration validation logic
   - File operation utilities

2. **Integration Tests**
   - End-to-end build process
   - Asset management workflows
   - Package generation pipeline

3. **System Tests**
   - Portable executable functionality
   - Cross-machine compatibility
   - Performance benchmarks

### Test Automation

```powershell
# Automated test suite structure
Describe "Portable Build System" {
    Context "Prerequisites Validation" {
        It "Should detect Node.js installation" { }
        It "Should validate npm dependencies" { }
        It "Should check database availability" { }
    }
    
    Context "Build Process" {
        It "Should create portable executable" { }
        It "Should copy all required assets" { }
        It "Should generate valid build artifacts" { }
    }
    
    Context "Validation Engine" {
        It "Should validate executable integrity" { }
        It "Should perform smoke tests" { }
        It "Should generate validation reports" { }
    }
}
```

### Continuous Integration

The build system will support CI/CD integration through:
- Exit codes for success/failure indication
- JSON output for automated parsing
- Artifact generation for deployment pipelines
- Test result reporting in standard formats

## Performance Considerations

### Build Optimization

1. **Incremental Builds**
   - Track file modification times
   - Skip unchanged components
   - Selective asset copying

2. **Parallel Processing**
   - Concurrent asset copying
   - Parallel validation checks
   - Multi-threaded compression

3. **Caching Strategies**
   - Cache npm dependencies
   - Reuse Vite build artifacts
   - Store validation results

### Resource Management

- Memory usage monitoring during builds
- Disk space management and cleanup
- Process resource limits
- Temporary file lifecycle management

## Security Considerations

### Build Security

1. **Code Integrity**
   - Checksum validation of source files
   - Digital signature verification (future)
   - Build reproducibility

2. **Asset Security**
   - Database sanitization options
   - Sensitive data exclusion
   - Asset encryption (optional)

3. **Distribution Security**
   - Package integrity checksums
   - Secure distribution channels
   - Version authenticity verification

### Access Control

- Build environment isolation
- Credential management for signing
- Audit logging of build operations
- Secure temporary file handling