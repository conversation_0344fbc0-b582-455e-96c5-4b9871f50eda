/**
 * German Error Messages for AI Services
 * User-friendly error messages in German with fallbacks
 */

import { AIServiceErrorCode } from '../types/errors';

export const AI_ERROR_MESSAGES: Record<AIServiceErrorCode, { user: string; technical: string }> = {
  // General AI Service Errors
  [AIServiceErrorCode.SERVICE_UNAVAILABLE]: {
    user: 'Der KI-Service ist momentan nicht verfügbar. Bitte versuchen Sie es später erneut.',
    technical: 'AI service is currently unavailable due to system maintenance or overload'
  },
  [AIServiceErrorCode.INITIALIZATION_FAILED]: {
    user: 'Die KI-Dienste konnten nicht gestartet werden. Bitte kontaktieren Sie den Administrator.',
    technical: 'AI service initialization failed during startup sequence'
  },
  [AIServiceErrorCode.CONFIGURATION_ERROR]: {
    user: 'Fehler in der KI-Konfiguration. Bitte überprüfen Sie die Einstellungen.',
    technical: 'AI service configuration is invalid or missing required parameters'
  },

  // OpenRouter API Errors
  [AIServiceErrorCode.API_KEY_INVALID]: {
    user: 'Authentifizierungsfehler bei der KI-API. Bitte kontaktieren Sie den Administrator.',
    technical: 'OpenRouter API key is invalid or expired'
  },
  [AIServiceErrorCode.API_RATE_LIMIT_EXCEEDED]: {
    user: 'Zu viele Anfragen an die KI-API. Bitte warten Sie einen Moment und versuchen Sie es erneut.',
    technical: 'OpenRouter API rate limit exceeded, requests are being throttled'
  },
  [AIServiceErrorCode.API_REQUEST_FAILED]: {
    user: 'Die KI-Anfrage konnte nicht verarbeitet werden. Bitte versuchen Sie es erneut.',
    technical: 'OpenRouter API request failed with HTTP error'
  },
  [AIServiceErrorCode.API_TIMEOUT]: {
    user: 'Die KI-Anfrage hat zu lange gedauert. Bitte versuchen Sie es mit einer einfacheren Anfrage.',
    technical: 'OpenRouter API request timed out after configured timeout period'
  },
  [AIServiceErrorCode.MODEL_UNAVAILABLE]: {
    user: 'Das gewählte KI-Modell ist momentan nicht verfügbar. Ein alternatives Modell wird verwendet.',
    technical: 'Requested AI model is unavailable, falling back to alternative model'
  },

  // Vector Database Errors
  [AIServiceErrorCode.VECTOR_STORAGE_FAILED]: {
    user: 'Fehler beim Speichern der Wissensdaten. Die Suche funktioniert möglicherweise eingeschränkt.',
    technical: 'Vector storage operation failed, knowledge base may be incomplete'
  },
  [AIServiceErrorCode.VECTOR_SEARCH_FAILED]: {
    user: 'Die Suche in der Wissensdatenbank ist fehlgeschlagen. Standardantworten werden verwendet.',
    technical: 'Vector similarity search failed, falling back to non-contextual responses'
  },
  [AIServiceErrorCode.EMBEDDING_GENERATION_FAILED]: {
    user: 'Fehler bei der Textanalyse. Die KI-Funktionen sind möglicherweise eingeschränkt.',
    technical: 'Text embedding generation failed, semantic search unavailable'
  },
  [AIServiceErrorCode.INDEX_CREATION_FAILED]: {
    user: 'Fehler beim Aufbau der Suchindizes. Die Suchfunktion ist möglicherweise beeinträchtigt.',
    technical: 'Vector index creation or rebuild failed, search performance may be degraded'
  },

  // RAG Service Errors
  [AIServiceErrorCode.DOCUMENT_INDEXING_FAILED]: {
    user: 'Dokumente konnten nicht in die Wissensdatenbank aufgenommen werden.',
    technical: 'Document indexing process failed during knowledge base update'
  },
  [AIServiceErrorCode.CONTEXT_RETRIEVAL_FAILED]: {
    user: 'Relevante Informationen konnten nicht abgerufen werden. Allgemeine Antworten werden bereitgestellt.',
    technical: 'Context retrieval from knowledge base failed, using general responses'
  },
  [AIServiceErrorCode.KNOWLEDGE_BASE_ERROR]: {
    user: 'Fehler in der Wissensdatenbank. Die KI-Antworten sind möglicherweise unvollständig.',
    technical: 'Knowledge base is corrupted or inaccessible'
  },

  // Optimization Service Errors
  [AIServiceErrorCode.OPTIMIZATION_FAILED]: {
    user: 'Die Optimierung konnte nicht durchgeführt werden. Bitte überprüfen Sie die Eingabedaten.',
    technical: 'Optimization algorithm failed to find solution with given constraints'
  },
  [AIServiceErrorCode.INSUFFICIENT_DATA]: {
    user: 'Nicht genügend Daten für eine zuverlässige Analyse. Bitte fügen Sie mehr Daten hinzu.',
    technical: 'Insufficient data points for reliable analysis or prediction'
  },
  [AIServiceErrorCode.INVALID_PARAMETERS]: {
    user: 'Ungültige Parameter für die Berechnung. Bitte überprüfen Sie Ihre Eingaben.',
    technical: 'Invalid or out-of-range parameters provided to optimization service'
  },
  [AIServiceErrorCode.CALCULATION_ERROR]: {
    user: 'Fehler bei der Berechnung. Bitte versuchen Sie es mit anderen Parametern.',
    technical: 'Mathematical calculation error during optimization process'
  },

  // Prediction Service Errors
  [AIServiceErrorCode.PREDICTION_FAILED]: {
    user: 'Die Vorhersage konnte nicht erstellt werden. Historische Daten werden angezeigt.',
    technical: 'Prediction model failed to generate forecast'
  },
  [AIServiceErrorCode.MODEL_TRAINING_FAILED]: {
    user: 'Das Vorhersagemodell konnte nicht trainiert werden. Standardmodelle werden verwendet.',
    technical: 'Machine learning model training failed, using fallback models'
  },
  [AIServiceErrorCode.FORECAST_GENERATION_FAILED]: {
    user: 'Prognosen konnten nicht generiert werden. Aktuelle Trends werden angezeigt.',
    technical: 'Time series forecast generation failed'
  },
  [AIServiceErrorCode.ANOMALY_DETECTION_FAILED]: {
    user: 'Anomalieerkennung ist fehlgeschlagen. Manuelle Überprüfung wird empfohlen.',
    technical: 'Anomaly detection algorithm failed to process data'
  },

  // Data Access Errors
  [AIServiceErrorCode.DATABASE_CONNECTION_FAILED]: {
    user: 'Verbindung zur Datenbank fehlgeschlagen. Einige Funktionen sind nicht verfügbar.',
    technical: 'Database connection failed, AI services operating in limited mode'
  },
  [AIServiceErrorCode.DATA_VALIDATION_FAILED]: {
    user: 'Die Eingabedaten sind ungültig. Bitte überprüfen Sie das Format.',
    technical: 'Input data validation failed, data does not meet required schema'
  },
  [AIServiceErrorCode.REPOSITORY_ERROR]: {
    user: 'Fehler beim Datenzugriff. Bitte versuchen Sie es später erneut.',
    technical: 'Repository layer error during data access operation'
  },

  // Cache Errors
  [AIServiceErrorCode.CACHE_READ_FAILED]: {
    user: 'Zwischenspeicher konnte nicht gelesen werden. Die Antwortzeit kann sich verlängern.',
    technical: 'Cache read operation failed, performance may be degraded'
  },
  [AIServiceErrorCode.CACHE_WRITE_FAILED]: {
    user: 'Zwischenspeicher konnte nicht aktualisiert werden. Zukünftige Anfragen können langsamer sein.',
    technical: 'Cache write operation failed, future requests may be slower'
  },
  [AIServiceErrorCode.CACHE_INVALIDATION_FAILED]: {
    user: 'Zwischenspeicher konnte nicht aktualisiert werden. Möglicherweise werden veraltete Daten angezeigt.',
    technical: 'Cache invalidation failed, stale data may be served'
  }
};

export function getErrorMessage(code: AIServiceErrorCode, type: 'user' | 'technical' = 'user'): string {
  const messages = AI_ERROR_MESSAGES[code];
  if (!messages) {
    return type === 'user' 
      ? 'Ein unbekannter Fehler ist aufgetreten. Bitte kontaktieren Sie den Support.'
      : `Unknown error code: ${code}`;
  }
  return messages[type];
}

export function formatErrorForUser(error: any, context?: string): string {
  if (error?.code && Object.values(AIServiceErrorCode).includes(error.code)) {
    const baseMessage = getErrorMessage(error.code, 'user');
    return context ? `${context}: ${baseMessage}` : baseMessage;
  }
  
  return context 
    ? `${context}: Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.`
    : 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.';
}

export function formatErrorForTechnical(error: any): string {
  if (error?.code && Object.values(AIServiceErrorCode).includes(error.code)) {
    return getErrorMessage(error.code, 'technical');
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return error?.message || 'Unknown technical error occurred';
}