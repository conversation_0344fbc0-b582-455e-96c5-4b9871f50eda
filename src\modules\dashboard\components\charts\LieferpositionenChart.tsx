import React, { useEffect, useState, memo } from "react";
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Line,
  XAxis,
  YAxis,
} from "recharts";
import { useTranslation } from "react-i18next";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import apiService from "@/services/api.service";

/**
 * DailyPerformanceChart Komponente
 * 
 * Zeigt die täglichen Auslieferungszahlen als Balkendiagramm:
 * - Ausgeliefert LUP
 * - Rückständig
 * 
 * @param data Optional: Array von Datenpunkten mit den Auslieferungszahlen
 */

// Definition des Datentyps für den DateRange
interface DateRange {
  from?: Date;
  to?: Date;
}

// Definition des Datentyps für die Auslieferungs-Daten
interface DeliveryDataPoint {
  date: string;
  ausgeliefert_lup: number;
  rueckstaendig: number;
}

// Definition des Datentyps für Datenbankzeilen
interface DatabaseRow {
  date: string;
  ausgeliefert_lup: number;
  rueckstaendig: number;
}

interface DailyPerformanceChartProps {
  // Optional, da die Daten jetzt auch direkt geladen werden können
  data?: DeliveryDataPoint[];
  dateRange?: DateRange;
}

// Footer-Komponente für die DailyPerformanceChart - Verwendet nur Daten aus der Datenbank
export function DailyPerformanceChartFooter() {
  const { t } = useTranslation();
  
  return (
    <div className="w-full">
      <div className="flex justify-between w-full">
        <div className="text-muted-foreground text-xs">
          {t("updated")}: {new Date().toLocaleDateString()}
        </div>
      </div>
    </div>
  );
}

export const DailyPerformanceChart = memo(function DailyPerformanceChart({ data: propData, dateRange }: DailyPerformanceChartProps) {
  const { t } = useTranslation();
  const [chartData, setChartData] = useState<DeliveryDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Konfiguration für das Diagramm
  const chartConfig = {
    ausgeliefert_lup: {
      label: "Ausgeliefert",
      color: "var(--chart-1)",
    },
    rueckstaendig: {
      label: "Rückständig", 
      color: "var(--chart-3)",
    },
  };

  // Lade Daten aus der Datenbank, wenn keine Props übergeben wurden
  useEffect(() => {
    if (propData) {
      // Verwende die übergebenen Daten, wenn vorhanden
      setChartData(propData);
    } else {
      // Lade Daten aus der Datenbank
      const loadData = async () => {
        try {
          setLoading(true);
          setError(null);
          
          // Versuche Daten aus der Datenbank zu laden
          const result = await apiService.getDeliveryPositionsData();
          
          if (result && Array.isArray(result)) {

            
            // Konvertiere Datenbankdaten zu Chart-Format
            let processedData = result.map((row: unknown) => {
              const dbRow = row as DatabaseRow;
              return {
                date: dbRow.date,
                ausgeliefert_lup: Number(dbRow.ausgeliefert_lup) || 0,
                rueckstaendig: Number(dbRow.rueckstaendig) || 0
              };
            });
            
            // Filtere Daten basierend auf dem ausgewählten Datumsbereich
            if (dateRange?.from || dateRange?.to) {
              processedData = processedData.filter((item) => {
                const itemDate = new Date(item.date);
                
                if (dateRange.from && itemDate < dateRange.from) return false;
                if (dateRange.to && itemDate > dateRange.to) return false;
                
                return true;
              });
            }
            
            setChartData(processedData);
          } else {
            throw new Error('Keine gültigen Daten erhalten');
          }
        } catch (err) {
          // Keine Fallback-Daten mehr verwenden, stattdessen Fehler anzeigen
          setChartData([]);
          setError('Fehler beim Laden der Daten aus der Datenbank')
        } finally {
          setLoading(false);
        }
      };
      
      loadData();
    }
  }, [propData, dateRange]);

  // Berechne Durchschnittswerte für den Footer, wenn Daten vorhanden sind
  const avgDelivered = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.ausgeliefert_lup, 0) / chartData.length : 0;
  const avgOutstanding = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.rueckstaendig, 0) / chartData.length : 0;
  const totalDelivered = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.ausgeliefert_lup, 0) : 0;
  const totalOutstanding = chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.rueckstaendig, 0) : 0;
  const ratio = totalOutstanding > 0 ? (totalDelivered / totalOutstanding).toFixed(1) : "N/A";

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">{t("loading")}...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="text-red-500 text-4xl mb-2">⚠️</div>
        <p className="font-bold text-red-500">{error}</p>
      </div>
    );
  }

  // Zeige Hinweis an, wenn keine Daten vorhanden sind
  if (chartData.length === 0) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="font-bold">{t("noData")}</p>
      </div>
    );
  }

  return (
    <Card className="text-black w-full h-105 border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <CardTitle>LIEFERPOSITIONEN</CardTitle>
        <CardDescription>
          Tägliche Auslieferungs- und Rückstandszahlen
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="w-full h-60"
        >
              <ComposedChart data={chartData} margin={{ top: 5, right: 12, left: 12, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="date" 
              className="text-xs sm:text-sm"
              tickLine={false}
              axisLine={false}
              tickMargin={1}
              height={30}
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => {
                  try {
                    // Versuche erst, es als Datum zu parsen und als TT.MM zu formatieren
                    const date = new Date(value);
                    if (!isNaN(date.getTime())) {
                      return date.toLocaleDateString('de-DE', { 
                        day: '2-digit', 
                        month: '2-digit' 
                      });
                    }
                    // Falls es kein gültiges Datum ist, zeige den String direkt
                    return String(value);
                  } catch {
                  return value;
                }
              }}
            />
            <YAxis 
              className="text-xs font-bold"
              tick={{ fontSize: 12 }}
              tickLine={false}
              axisLine={false}
              tickMargin={6}
              width={35}
              domain={[0, 100]}
            />
            
            <ChartTooltip
              cursor={false}
              content={
                <ChartTooltipContent
                  indicator="dot"
                  labelClassName="font-bold"
                  labelFormatter={(label) => {
                    // Formatiere das Datum im Tooltip als DD.MM.YYYY
                    try {
                      const date = new Date(label);
                          return `Datum: ${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getFullYear()}`;
                    } catch {
                          return `Datum: ${label}`;
                    }
                  }}
                />
              }
            />
            <ChartLegend content={({ payload }) => (
              <ChartLegendContent 
                payload={payload} 
                className="p-2 rounded-md"
              />
            )} />
            
            {/* Ausgeliefert als Balken, Rückständig als Linie */}
            <Bar 
              dataKey="ausgeliefert_lup" 
              name="Ausgeliefert" 
              fill={chartConfig.ausgeliefert_lup.color}
              stroke="#000000"
              strokeWidth={2}
              radius={[4, 4, 0, 0]}
              className="neo-brutalism-bar" 
            />
            <Line 
              type="monotone"
              dataKey="rueckstaendig" 
              name="Rückständig" 
              fill={chartConfig.rueckstaendig.color}
              stroke={chartConfig.rueckstaendig.color}
              strokeWidth={3}
              dot={{ fill: chartConfig.rueckstaendig.color, strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: "#000000", strokeWidth: 2 }}
            />
          </ComposedChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex flex-col w-full gap-2 text-sm">
          <div className="font-medium mb-1">Durchschnitt über die ausgewählte Zeitperiode:</div>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-1">
              <div className="flex items-center gap-2 leading-none font-medium">
              <span className="text-black text-ml">Ausgeliefert: {avgDelivered.toFixed(0)}</span>
            </div>
            <div className="flex items-center gap-1">
            <span className="text-black text-ml">| Rückständig: {avgOutstanding.toFixed(0)}</span>
            </div>
            <div className="flex items-center gap-1">
            </div>
          </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
            <span className="text-black text-ml">Verhältnis: {ratio} | Gesamt: {(totalDelivered + totalOutstanding).toLocaleString()}</span>
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});
