import { PrismaClient } from '@prisma-sfm-dashboard/client';
import { startOfWeek, addWeeks, addDays, format } from 'date-fns';
import { de } from 'date-fns/locale';

const prisma = new PrismaClient();

export interface BereitschaftsPersonData {
  id?: number;
  name: string;
  telefon: string;
  email: string;
  abteilung: string;
  aktiv?: boolean;
  reihenfolge?: number;
}

export interface BereitschaftsWocheData {
  id?: number;
  personId: number;
  wochenStart: Date;
  wochenEnde: Date;
  von: Date;
  bis: Date;
  aktiv?: boolean;
  notiz?: string;
}

export interface BereitschaftsAusnahmeData {
  id?: number;
  personId: number;
  von: Date;
  bis: Date;
  grund: string;
  ersatzPersonId?: number;
  aktiv?: boolean;
}

export interface BereitschaftsKonfigurationData {
  id?: number;
  wechselTag?: number;
  wechselUhrzeit?: string;
  rotationAktiv?: boolean;
  benachrichtigungTage?: number;
  emailBenachrichtigung?: boolean;
}

export class BereitschaftsRepository {
  // Personen verwalten
  async getAllPersonen() {
    return await prisma.bereitschaftsPerson.findMany({
      where: { aktiv: true },
      orderBy: { reihenfolge: 'asc' },
      include: {
        bereitschaftsWochen: {
          where: { aktiv: true },
          orderBy: { wochenStart: 'desc' },
          take: 5
        }
      }
    });
  }

  async getPersonById(id: number) {
    return await prisma.bereitschaftsPerson.findUnique({
      where: { id },
      include: {
        bereitschaftsWochen: {
          where: { aktiv: true },
          orderBy: { wochenStart: 'desc' }
        },
        bereitschaftsAusnahmen: {
          where: { aktiv: true },
          orderBy: { von: 'desc' }
        }
      }
    });
  }

  async createPerson(data: BereitschaftsPersonData) {
    const maxReihenfolge = await prisma.bereitschaftsPerson.aggregate({
      _max: { reihenfolge: true }
    });

    return await prisma.bereitschaftsPerson.create({
      data: {
        ...data,
        reihenfolge: data.reihenfolge ?? (maxReihenfolge._max.reihenfolge ?? 0) + 1
      }
    });
  }

  async updatePerson(id: number, data: Partial<BereitschaftsPersonData>) {
    return await prisma.bereitschaftsPerson.update({
      where: { id },
      data
    });
  }

  async deletePerson(id: number) {
    return await prisma.bereitschaftsPerson.update({
      where: { id },
      data: { aktiv: false }
    });
  }

  async updatePersonenReihenfolge(personenIds: number[]) {
    const updates = personenIds.map((id, index) =>
      prisma.bereitschaftsPerson.update({
        where: { id },
        data: { reihenfolge: index + 1 }
      })
    );

    return await prisma.$transaction(updates);
  }

  // Wochen verwalten
  async getWochenPlan(startDate: Date, anzahlWochen: number) {
    const endDate = addWeeks(startDate, anzahlWochen);
    
    return await prisma.bereitschaftsWoche.findMany({
      where: {
        aktiv: true,
        wochenStart: {
          gte: startDate,
          lt: endDate
        }
      },
      include: {
        person: true
      },
      orderBy: { wochenStart: 'asc' }
    });
  }

  async getAktuelleBereitschaft() {
    const heute = new Date();
    
    return await prisma.bereitschaftsWoche.findFirst({
      where: {
        aktiv: true,
        von: { lte: heute },
        bis: { gt: heute }
      },
      include: {
        person: true
      }
    });
  }

  async createWoche(data: BereitschaftsWocheData) {
    return await prisma.bereitschaftsWoche.create({
      data,
      include: {
        person: true
      }
    });
  }

  async updateWoche(id: number, data: Partial<BereitschaftsWocheData>) {
    return await prisma.bereitschaftsWoche.update({
      where: { id },
      data,
      include: {
        person: true
      }
    });
  }

  async deleteWoche(id: number) {
    return await prisma.bereitschaftsWoche.update({
      where: { id },
      data: { aktiv: false }
    });
  }

  // Automatische Wochenplanung generieren
  async generiereWochenplan(startDate: Date, anzahlWochen: number) {
    const personen = await this.getAllPersonen();
    if (personen.length === 0) {
      throw new Error('Keine aktiven Bereitschaftspersonen gefunden');
    }

    const konfiguration = await this.getKonfiguration();
    const wechselTag = konfiguration?.wechselTag ?? 5; // Freitag
    const wechselUhrzeit = konfiguration?.wechselUhrzeit ?? '08:00';

    const wochen: BereitschaftsWocheData[] = [];

    for (let i = 0; i < anzahlWochen; i++) {
      const wochenStart = addWeeks(startOfWeek(startDate, { weekStartsOn: 1 }), i);
      const wochenEnde = addWeeks(wochenStart, 1);
      
      // Berechne Freitag der Woche
      const freitag = addDays(wochenStart, wechselTag - 1);
      const naechsterFreitag = addDays(freitag, 7);

      // Rotiere durch die Personen
      const personIndex = i % personen.length;
      const person = personen[personIndex];

      wochen.push({
        personId: person.id,
        wochenStart,
        wochenEnde,
        von: freitag,
        bis: naechsterFreitag,
        aktiv: true
      });
    }

    // Lösche bestehende Wochen im Zeitraum
    await prisma.bereitschaftsWoche.updateMany({
      where: {
        wochenStart: {
          gte: startOfWeek(startDate, { weekStartsOn: 1 }),
          lt: addWeeks(startOfWeek(startDate, { weekStartsOn: 1 }), anzahlWochen)
        }
      },
      data: { aktiv: false }
    });

    // Erstelle neue Wochen
    const erstellteWochen = await Promise.all(
      wochen.map(woche => this.createWoche(woche))
    );

    return erstellteWochen;
  }

  // Ausnahmen verwalten
  async getAllAusnahmen() {
    return await prisma.bereitschaftsAusnahme.findMany({
      where: { aktiv: true },
      include: {
        person: true
      },
      orderBy: { von: 'desc' }
    });
  }

  async createAusnahme(data: BereitschaftsAusnahmeData) {
    return await prisma.bereitschaftsAusnahme.create({
      data,
      include: {
        person: true
      }
    });
  }

  async updateAusnahme(id: number, data: Partial<BereitschaftsAusnahmeData>) {
    return await prisma.bereitschaftsAusnahme.update({
      where: { id },
      data,
      include: {
        person: true
      }
    });
  }

  async deleteAusnahme(id: number) {
    return await prisma.bereitschaftsAusnahme.update({
      where: { id },
      data: { aktiv: false }
    });
  }

  // Konfiguration verwalten
  async getKonfiguration() {
    const config = await prisma.bereitschaftsKonfiguration.findFirst({
      orderBy: { createdAt: 'desc' }
    });

    // Standardkonfiguration falls keine existiert
    if (!config) {
      return await this.createKonfiguration({});
    }

    // Map database fields to expected frontend fields
    return {
      id: config.id,
      wechselTag: config.wechsel_tag,
      wechselUhrzeit: config.wechselUhrzeit,
      rotationAktiv: config.rotationAktiv,
      benachrichtigungTage: config.benachrichtigungTage,
      emailBenachrichtigung: config.emailBenachrichtigung,
      createdAt: config.createdAt.toISOString(),
      updatedAt: config.updatedAt.toISOString()
    };
  }

  async createKonfiguration(data: BereitschaftsKonfigurationData) {
    const created = await prisma.bereitschaftsKonfiguration.create({
      data: {
        wechsel_tag: data.wechselTag ?? 5,
        wechselUhrzeit: data.wechselUhrzeit ?? '08:00',
        rotationAktiv: data.rotationAktiv ?? true,
        benachrichtigungTage: data.benachrichtigungTage ?? 2,
        emailBenachrichtigung: data.emailBenachrichtigung ?? true
      }
    });

    // Return mapped result
    return {
      id: created.id,
      wechselTag: created.wechsel_tag,
      wechselUhrzeit: created.wechselUhrzeit,
      rotationAktiv: created.rotationAktiv,
      benachrichtigungTage: created.benachrichtigungTage,
      emailBenachrichtigung: created.emailBenachrichtigung,
      createdAt: created.createdAt.toISOString(),
      updatedAt: created.updatedAt.toISOString()
    };
  }

  async updateKonfiguration(data: Partial<BereitschaftsKonfigurationData>) {
    const existingConfig = await prisma.bereitschaftsKonfiguration.findFirst({
      orderBy: { createdAt: 'desc' }
    });
    
    if (!existingConfig) {
      throw new Error('Keine Konfiguration gefunden');
    }

    // Map frontend fields to database fields
    const updateData: any = {};
    if (data.wechselTag !== undefined) updateData.wechsel_tag = data.wechselTag;
    if (data.wechselUhrzeit !== undefined) updateData.wechselUhrzeit = data.wechselUhrzeit;
    if (data.rotationAktiv !== undefined) updateData.rotationAktiv = data.rotationAktiv;
    if (data.benachrichtigungTage !== undefined) updateData.benachrichtigungTage = data.benachrichtigungTage;
    if (data.emailBenachrichtigung !== undefined) updateData.emailBenachrichtigung = data.emailBenachrichtigung;

    const updated = await prisma.bereitschaftsKonfiguration.update({
      where: { id: existingConfig.id },
      data: updateData
    });

    // Return mapped result
    return {
      id: updated.id,
      wechselTag: updated.wechsel_tag,
      wechselUhrzeit: updated.wechselUhrzeit,
      rotationAktiv: updated.rotationAktiv,
      benachrichtigungTage: updated.benachrichtigungTage,
      emailBenachrichtigung: updated.emailBenachrichtigung,
      createdAt: updated.createdAt.toISOString(),
      updatedAt: updated.updatedAt.toISOString()
    };
  }

  // Hilfsmethoden
  async getPersonenInZeitraum(von: Date, bis: Date) {
    return await prisma.bereitschaftsWoche.findMany({
      where: {
        aktiv: true,
        OR: [
          {
            von: { lte: bis },
            bis: { gte: von }
          }
        ]
      },
      include: {
        person: true
      },
      orderBy: { von: 'asc' }
    });
  }

  async validateWochenplan(wochen: BereitschaftsWocheData[]) {
    const errors: string[] = [];

    // Prüfe auf Überschneidungen
    for (let i = 0; i < wochen.length; i++) {
      for (let j = i + 1; j < wochen.length; j++) {
        const woche1 = wochen[i];
        const woche2 = wochen[j];

        if (woche1.von < woche2.bis && woche1.bis > woche2.von) {
          errors.push(`Überschneidung zwischen Woche ${i + 1} und ${j + 1}`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const bereitschaftsRepository = new BereitschaftsRepository();