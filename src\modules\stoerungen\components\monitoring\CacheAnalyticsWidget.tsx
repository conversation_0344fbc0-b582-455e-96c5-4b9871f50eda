import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Zap, 
  Database, 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  Activity,
  MemoryStick,
  Hash,
  Timer
} from "lucide-react";
import apiService from "@/services/api.service";

interface CacheAnalytics {
  hitRate: number;
  missRate: number;
  totalRequests: number;
  memoryUsage: number;
  memoryLimit: number;
  totalEntries: number;
  maxEntries: number;
  averageKeySize: number;
  oldestEntry: string;
  newestEntry: string;
  evictions: number;
  averageAccessTime: number;
  hotKeys: Array<{
    key: string;
    hits: number;
    lastAccessed: string;
  }>;
  sizeDistribution: {
    small: number;
    medium: number;
    large: number;
  };
}

interface CacheAnalyticsWidgetProps {
  compact?: boolean;
}

export const CacheAnalyticsWidget: React.FC<CacheAnalyticsWidgetProps> = ({ compact = false }) => {
  const [analytics, setAnalytics] = useState<CacheAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCacheAnalytics = async () => {
    try {
      setLoading(true);
      const response = await apiService.get<CacheAnalytics>("/performance/cache");
      setAnalytics(response);
    } catch (err) {
      setError("Fehler beim Laden der Cache-Analytics");
      console.error("Cache analytics error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCacheAnalytics();
    // Longer interval to reduce backend load when offline
    const interval = setInterval(fetchCacheAnalytics, 90000); // Update every 90 seconds
    return () => clearInterval(interval);
  }, []);

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return "< 1m ago";
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const getHitRateColor = (hitRate: number) => {
    if (hitRate >= 90) return "text-green-500";
    if (hitRate >= 75) return "text-yellow-500";
    return "text-red-500";
  };

  const getHitRateBadge = (hitRate: number) => {
    if (hitRate >= 90) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>;
    if (hitRate >= 75) return <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>;
    return <Badge className="bg-red-100 text-red-800">Poor</Badge>;
  };

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardHeader className="h-16 bg-gray-200"></CardHeader>
        <CardContent className="h-32 bg-gray-100"></CardContent>
      </Card>
    );
  }

  if (error || !analytics) {
    return (
      <Card className="border-red-200">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-red-600">
            <Zap className="h-4 w-4" />
            <span className="text-sm">{error || "Cache-Daten nicht verfügbar"}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Provide safe defaults for all safeAnalytics properties
  const safeAnalytics = {
    hitRate: analytics?.hitRate || 0,
    missRate: analytics?.missRate || 0,
    totalRequests: analytics?.totalRequests || 0,
    memoryUsage: analytics?.memoryUsage || 0,
    memoryLimit: analytics?.memoryLimit || 100000000, // 100MB default
    totalEntries: analytics?.totalEntries || 0,
    maxEntries: analytics?.maxEntries || 1000,
    averageKeySize: analytics?.averageKeySize || 0,
    oldestEntry: analytics?.oldestEntry || new Date().toISOString(),
    newestEntry: analytics?.newestEntry || new Date().toISOString(),
    evictions: analytics?.evictions || 0,
    averageAccessTime: analytics?.averageAccessTime || 0,
    hotKeys: analytics?.hotKeys || [],
    sizeDistribution: analytics?.sizeDistribution || { small: 0, medium: 0, large: 0 }
  };

  if (compact) {
    return (
      <Card className="border-l-4 border-l-purple-500">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Zap className="h-4 w-4 text-purple-500" />
            Cache Performance
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Hit Rate</span>
            <div className="flex items-center gap-2">
              <span className={`font-bold ${getHitRateColor(safeAnalytics.hitRate)}`}>
                {safeAnalytics.hitRate.toFixed(1)}%
              </span>
              {getHitRateBadge(safeAnalytics.hitRate)}
            </div>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Memory</span>
            <span className="font-medium text-purple-600">
              {formatBytes(safeAnalytics.memoryUsage)} / {formatBytes(safeAnalytics.memoryLimit)}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Entries</span>
            <span className="font-medium">
              {safeAnalytics.totalEntries.toLocaleString()} / {safeAnalytics.maxEntries.toLocaleString()}
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Cache Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
        <Card className="border-t-4 border-t-purple-500">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-purple-600 text-sm">
              <TrendingUp className="h-4 w-4" />
              Hit Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getHitRateColor(safeAnalytics?.hitRate || 0)}`}>
              {(safeAnalytics?.hitRate || 0).toFixed(1)}%
            </div>
            <div className="flex items-center justify-between mt-2">
              <span className="text-xs text-gray-500">Miss Rate: {(safeAnalytics?.missRate || 0).toFixed(1)}%</span>
              {getHitRateBadge(safeAnalytics?.hitRate || 0)}
            </div>
            <Progress value={safeAnalytics?.hitRate || 0} className="mt-2 h-2" />
          </CardContent>
        </Card>

        <Card className="border-t-4 border-t-blue-500">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-blue-600 text-sm">
              <MemoryStick className="h-4 w-4" />
              Memory Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {formatBytes(safeAnalytics.memoryUsage)}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              of {formatBytes(safeAnalytics.memoryLimit)} limit
            </div>
            <Progress 
              value={(safeAnalytics.memoryUsage / safeAnalytics.memoryLimit) * 100} 
              className="mt-2 h-2" 
            />
          </CardContent>
        </Card>

        <Card className="border-t-4 border-t-green-500">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-green-600 text-sm">
              <Hash className="h-4 w-4" />
              Entries
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {safeAnalytics.totalEntries.toLocaleString()}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              of {safeAnalytics.maxEntries.toLocaleString()} max
            </div>
            <Progress 
              value={(safeAnalytics.totalEntries / safeAnalytics.maxEntries) * 100} 
              className="mt-2 h-2" 
            />
          </CardContent>
        </Card>

        <Card className="border-t-4 border-t-orange-500">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-orange-600 text-sm">
              <Timer className="h-4 w-4" />
              Access Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {(safeAnalytics?.averageAccessTime || 0).toFixed(2)}ms
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Average access time
            </div>
            <div className="text-xs text-gray-500">
              {safeAnalytics.evictions} evictions
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Hot Keys */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-red-500" />
              Hot Keys
            </CardTitle>
            <CardDescription>Most frequently accessed cache entries</CardDescription>
          </CardHeader>
          <CardContent>
            {safeAnalytics.hotKeys.length === 0 ? (
              <p className="text-sm text-gray-500">No hot keys data available</p>
            ) : (
              <div className="space-y-3">
                {safeAnalytics.hotKeys.slice(0, 5).map((hotKey, index) => (
                  <div key={hotKey.key} className="flex justify-between items-center">
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium truncate">
                        {hotKey.key}
                      </div>
                      <div className="text-xs text-gray-500">
                        Last accessed: {formatTimeAgo(hotKey.lastAccessed)}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {hotKey.hits} hits
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Cache Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-blue-500" />
              Cache Statistics
            </CardTitle>
            <CardDescription>Detailed cache performance metrics</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Total Requests</span>
              <span className="font-bold">{safeAnalytics.totalRequests.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Average Key Size</span>
              <span className="font-bold">{formatBytes(safeAnalytics.averageKeySize)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Oldest Entry</span>
              <span className="text-sm">{formatTimeAgo(safeAnalytics.oldestEntry)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Newest Entry</span>
              <span className="text-sm">{formatTimeAgo(safeAnalytics.newestEntry)}</span>
            </div>
            
            {/* Size Distribution */}
            <div className="pt-2 border-t">
              <div className="text-sm font-medium mb-2">Size Distribution</div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Small (&lt; 1KB)</span>
                  <span>{safeAnalytics.sizeDistribution.small}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Medium (1-10KB)</span>
                  <span>{safeAnalytics.sizeDistribution.medium}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Large (&gt; 10KB)</span>
                  <span>{safeAnalytics.sizeDistribution.large}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};