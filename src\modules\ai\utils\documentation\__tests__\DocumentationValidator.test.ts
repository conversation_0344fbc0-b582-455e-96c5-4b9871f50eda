import { describe, it, expect, beforeEach } from 'vitest';
import { DocumentationValidator } from '../DocumentationValidator';
import { DocumentationSection, Tutorial } from '../../components/documentation/AIDocumentationProvider';

describe('DocumentationValidator', () => {
  let validator: DocumentationValidator;
  let validSection: DocumentationSection;
  let validTutorial: Tutorial;

  beforeEach(() => {
    validator = new DocumentationValidator();
    
    validSection = {
      id: 'test-section',
      title: 'Test Section',
      content: 'This is a test section with valid content.',
      category: 'overview',
      tags: ['test', 'validation'],
      lastUpdated: new Date(),
      difficulty: 'beginner'
    };

    validTutorial = {
      id: 'test-tutorial',
      title: 'Test Tutorial',
      description: 'This is a test tutorial',
      category: 'basics',
      estimatedTime: 15,
      difficulty: 'beginner',
      prerequisites: [],
      steps: [
        {
          id: 'step-1',
          title: 'First Step',
          content: 'This is the first step'
        }
      ]
    };
  });

  describe('section validation', () => {
    it('should validate a correct section', () => {
      const result = validator.validateSection(validSection);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      // The valid section might have warnings (like short content), so we don't check warnings length
    });

    it('should detect missing title', () => {
      const section = { ...validSection, title: '' };
      const result = validator.validateSection(section);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Section title is required');
    });

    it('should detect missing content', () => {
      const section = { ...validSection, content: '' };
      const result = validator.validateSection(section);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Section content is required');
    });

    it('should detect missing tags', () => {
      const section = { ...validSection, tags: [] };
      const result = validator.validateSection(section);
      
      expect(result.isValid).toBe(true); // Tags are not required, but should warn
      expect(result.warnings).toContain('Section has no tags');
    });

    it('should detect invalid category', () => {
      const section = { ...validSection, category: 'invalid' as any };
      const result = validator.validateSection(section);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid category: invalid');
    });

    it('should detect invalid difficulty', () => {
      const section = { ...validSection, difficulty: 'invalid' as any };
      const result = validator.validateSection(section);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid difficulty: invalid');
    });

    it('should warn about short content', () => {
      const section = { ...validSection, content: 'Short' };
      const result = validator.validateSection(section);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Section content is very short (less than 50 characters)');
    });

    it('should warn about old content', () => {
      const oldDate = new Date();
      oldDate.setFullYear(oldDate.getFullYear() - 2);
      const section = { ...validSection, lastUpdated: oldDate };
      const result = validator.validateSection(section);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Section content is older than 1 year');
    });
  });

  describe('tutorial validation', () => {
    it('should validate a correct tutorial', () => {
      const result = validator.validateTutorial(validTutorial);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
    });

    it('should detect missing title', () => {
      const tutorial = { ...validTutorial, title: '' };
      const result = validator.validateTutorial(tutorial);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Tutorial title is required');
    });

    it('should detect missing description', () => {
      const tutorial = { ...validTutorial, description: '' };
      const result = validator.validateTutorial(tutorial);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Tutorial description is required');
    });

    it('should detect no steps', () => {
      const tutorial = { ...validTutorial, steps: [] };
      const result = validator.validateTutorial(tutorial);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Tutorial must have at least one step');
    });

    it('should detect invalid estimated time', () => {
      const tutorial = { ...validTutorial, estimatedTime: 0 };
      const result = validator.validateTutorial(tutorial);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Tutorial estimated time must be greater than 0');
    });

    it('should detect invalid difficulty', () => {
      const tutorial = { ...validTutorial, difficulty: 'invalid' as any };
      const result = validator.validateTutorial(tutorial);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid difficulty: invalid');
    });

    it('should validate tutorial steps', () => {
      const tutorial = {
        ...validTutorial,
        steps: [
          {
            id: 'step-1',
            title: '',
            content: 'Content'
          }
        ]
      };
      const result = validator.validateTutorial(tutorial);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Step step-1 is missing a title');
    });

    it('should warn about long tutorials', () => {
      const tutorial = { ...validTutorial, estimatedTime: 120 };
      const result = validator.validateTutorial(tutorial);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Tutorial is quite long (over 60 minutes)');
    });

    it('should warn about missing prerequisites for advanced tutorials', () => {
      const tutorial = {
        ...validTutorial,
        difficulty: 'advanced' as const,
        prerequisites: []
      };
      const result = validator.validateTutorial(tutorial);
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Advanced tutorial should have prerequisites');
    });
  });

  describe('step validation', () => {
    it('should validate a correct step', () => {
      const step = validTutorial.steps[0];
      const result = validator.validateStep(step);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing step title', () => {
      const step = { ...validTutorial.steps[0], title: '' };
      const result = validator.validateStep(step);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(`Step ${step.id} is missing a title`);
    });

    it('should detect missing step content', () => {
      const step = { ...validTutorial.steps[0], content: '' };
      const result = validator.validateStep(step);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(`Step ${step.id} is missing content`);
    });

    it('should validate step actions', () => {
      const step = {
        ...validTutorial.steps[0],
        action: {
          type: 'click' as const,
          target: ''
        }
      };
      const result = validator.validateStep(step);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(`Step ${step.id} action is missing target`);
    });

    it('should validate step validation rules', () => {
      const step = {
        ...validTutorial.steps[0],
        validation: {
          type: 'element' as const,
          condition: ''
        }
      };
      const result = validator.validateStep(step);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(`Step ${step.id} validation is missing condition`);
    });
  });

  describe('batch validation', () => {
    it('should validate multiple sections', () => {
      const sections = [validSection, { ...validSection, id: 'section-2' }];
      const result = validator.validateSections(sections);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect duplicate section IDs', () => {
      const sections = [validSection, validSection];
      const result = validator.validateSections(sections);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Duplicate section ID: test-section');
    });

    it('should validate multiple tutorials', () => {
      const tutorials = [validTutorial, { ...validTutorial, id: 'tutorial-2' }];
      const result = validator.validateTutorials(tutorials);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect duplicate tutorial IDs', () => {
      const tutorials = [validTutorial, validTutorial];
      const result = validator.validateTutorials(tutorials);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Duplicate tutorial ID: test-tutorial');
    });
  });

  describe('cross-reference validation', () => {
    it('should detect broken internal links', () => {
      const section = {
        ...validSection,
        content: 'See [other section](#non-existent-section) for details.'
      };
      const result = validator.validateCrossReferences([section], []);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Broken link to non-existent-section in section test-section');
    });

    it('should validate tutorial prerequisites', () => {
      const tutorial = {
        ...validTutorial,
        prerequisites: ['non-existent-tutorial']
      };
      const result = validator.validateCrossReferences([], [tutorial]);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Tutorial test-tutorial references non-existent prerequisite: non-existent-tutorial');
    });

    it('should validate valid cross-references', () => {
      const section1 = validSection;
      const section2 = {
        ...validSection,
        id: 'section-2',
        content: 'See [test section](#test-section) for details.'
      };
      const result = validator.validateCrossReferences([section1, section2], []);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('comprehensive validation', () => {
    it('should perform comprehensive validation', () => {
      const result = validator.validateAll([validSection], [validTutorial]);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.summary).toBeDefined();
      expect(result.summary.totalSections).toBe(1);
      expect(result.summary.totalTutorials).toBe(1);
    });

    it('should provide validation summary', () => {
      const sections = [validSection, { ...validSection, id: 'section-2', title: '' }];
      const tutorials = [validTutorial];
      const result = validator.validateAll(sections, tutorials);
      
      expect(result.isValid).toBe(false);
      expect(result.summary.validSections).toBe(1);
      expect(result.summary.invalidSections).toBe(1);
      expect(result.summary.validTutorials).toBe(1);
      expect(result.summary.invalidTutorials).toBe(0);
    });

    it('should provide suggestions for improvement', () => {
      const result = validator.validateAll([validSection], []);
      
      expect(result.suggestions).toContain('Consider adding more tutorials for better user guidance');
    });
  });
});