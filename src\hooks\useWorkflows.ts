import { useState, useEffect, useCallback } from 'react';
import { workflowService } from '@/services/workflowService';
import { SAPWorkflowProcess, WorkflowExecution, WorkflowLog, WorkflowStats } from '@/types/workflow';

interface ProcessWithConfig extends SAPWorkflowProcess {
  isActive?: boolean;
}

export function useWorkflows() {
  const [processes, setProcesses] = useState<ProcessWithConfig[]>([]);
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [logs, setLogs] = useState<WorkflowLog[]>([]);
  const [stats, setStats] = useState<WorkflowStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [processesData, executionsData, logsData, statsData] = await Promise.all([
        workflowService.getProcesses(),
        workflowService.getExecutions(),
        workflowService.getLogs(),
        workflowService.getStats()
      ]);

      // Load configurations for each process to get isActive status
      const processesWithConfig = await Promise.all(
        processesData.map(async (process) => {
          try {
            const config = await workflowService.getWorkflowConfig(process.id);
            return {
              ...process,
              isActive: config?.isActive ?? true // Default to true if no config found
            };
          } catch (error) {
            console.warn(`Failed to load config for ${process.id}:`, error);
            return {
              ...process,
              isActive: true // Default to true on error
            };
          }
        })
      );

      setProcesses(processesWithConfig);
      setExecutions(executionsData);
      setLogs(logsData);
      setStats(statsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load workflow data');
      console.error('Failed to load workflow data:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const executeProcess = useCallback(async (processId: string) => {
    try {
      setError(null);
      
      // Update process status to running
      setProcesses(prev => prev.map(p => 
        p.id === processId ? { ...p, status: 'running' } : p
      ));

      const execution = await workflowService.executeProcess(processId);
      
      // Reload data to get updated status
      await loadData();
      
      return execution;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to execute process');
      
      // Update process status to error
      setProcesses(prev => prev.map(p => 
        p.id === processId ? { ...p, status: 'error' } : p
      ));
      
      throw err;
    }
  }, [loadData]);

  const executeAllProcesses = useCallback(async () => {
    try {
      setError(null);
      
      // Update all process statuses to running
      setProcesses(prev => prev.map(p => ({ ...p, status: 'running' as const })));

      const executions = await workflowService.executeAllProcesses();
      
      // Reload data to get updated statuses
      await loadData();
      
      return executions;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to execute all processes');
      throw err;
    }
  }, [loadData]);

  const getProcessLogs = useCallback((processId: string) => {
    return logs.filter(log => log.processId === processId);
  }, [logs]);

  const getProcessExecutions = useCallback((processId: string) => {
    return executions.filter(execution => execution.processId === processId);
  }, [executions]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    processes,
    executions,
    logs,
    stats,
    loading,
    error,
    executeProcess,
    executeAllProcesses,
    getProcessLogs,
    getProcessExecutions,
    refreshData: loadData
  };
}