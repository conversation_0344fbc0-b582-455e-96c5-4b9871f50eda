/**
 * Page Visibility Hook
 * 
 * Implements Page Visibility API for performance optimization.
 * Automatically manages resources when page visibility changes.
 * 
 * Requirements: 7.3, 7.4
 */

import { useState, useEffect, useCallback, useRef } from 'react';

export interface VisibilityState {
  isVisible: boolean;
  visibilityState: DocumentVisibilityState;
  lastVisibilityChange: number;
}

export interface VisibilityConfig {
  cleanupDelay: number; // ms to wait before cleanup when hidden
  enableLogging: boolean;
}

/**
 * Hook for managing page visibility and performance optimization
 */
export function usePageVisibility(config: Partial<VisibilityConfig> = {}) {
  const finalConfig: VisibilityConfig = {
    cleanupDelay: 30000, // 30 seconds
    enableLogging: true,
    ...config,
  };

  const [visibilityState, setVisibilityState] = useState<VisibilityState>(() => ({
    isVisible: !document.hidden,
    visibilityState: document.visibilityState,
    lastVisibilityChange: Date.now(),
  }));

  const cleanupTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hiddenStartTimeRef = useRef<number | null>(null);

  const callbacksRef = useRef<{
    onVisible: (() => void)[];
    onHidden: (() => void)[];
  }>({
    onVisible: [],
    onHidden: [],
  });

  /**
   * Register callback for when page becomes visible
   */
  const onVisible = useCallback((callback: () => void) => {
    callbacksRef.current.onVisible.push(callback);
    
    // Return cleanup function
    return () => {
      const index = callbacksRef.current.onVisible.indexOf(callback);
      if (index > -1) {
        callbacksRef.current.onVisible.splice(index, 1);
      }
    };
  }, []);

  /**
   * Register callback for when page becomes hidden
   */
  const onHidden = useCallback((callback: () => void) => {
    callbacksRef.current.onHidden.push(callback);
    
    // Return cleanup function
    return () => {
      const index = callbacksRef.current.onHidden.indexOf(callback);
      if (index > -1) {
        callbacksRef.current.onHidden.splice(index, 1);
      }
    };
  }, []);

  /**
   * Get time spent hidden (in milliseconds)
   */
  const getTimeHidden = useCallback((): number => {
    if (!hiddenStartTimeRef.current) return 0;
    return Date.now() - hiddenStartTimeRef.current;
  }, []);

  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;
      const newVisibilityState = document.visibilityState;
      const timestamp = Date.now();

      setVisibilityState({
        isVisible,
        visibilityState: newVisibilityState,
        lastVisibilityChange: timestamp,
      });

      if (finalConfig.enableLogging) {
        console.log(`Page visibility changed: ${isVisible ? 'visible' : 'hidden'}`);
      }

      if (isVisible) {
        // Page became visible
        hiddenStartTimeRef.current = null;
        
        // Clear any pending cleanup
        if (cleanupTimeoutRef.current) {
          clearTimeout(cleanupTimeoutRef.current);
          cleanupTimeoutRef.current = null;
        }

        // Execute visibility callbacks
        callbacksRef.current.onVisible.forEach(callback => {
          try {
            callback();
          } catch (error) {
            console.error('Error in onVisible callback:', error);
          }
        });
      } else {
        // Page became hidden
        hiddenStartTimeRef.current = timestamp;

        // Execute hidden callbacks
        callbacksRef.current.onHidden.forEach(callback => {
          try {
            callback();
          } catch (error) {
            console.error('Error in onHidden callback:', error);
          }
        });

        // Schedule cleanup after delay
        cleanupTimeoutRef.current = setTimeout(() => {
          if (finalConfig.enableLogging) {
            console.log('Performing cleanup after extended hidden period');
          }
          // Cleanup logic can be added here
        }, finalConfig.cleanupDelay);
      }
    };

    // Add event listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup on unmount
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      
      if (cleanupTimeoutRef.current) {
        clearTimeout(cleanupTimeoutRef.current);
      }
    };
  }, [finalConfig.cleanupDelay, finalConfig.enableLogging]);

  return {
    ...visibilityState,
    onVisible,
    onHidden,
    getTimeHidden,
  };
}

export default usePageVisibility;