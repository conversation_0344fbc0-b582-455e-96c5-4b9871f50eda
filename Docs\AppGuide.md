# JOZI1 Leitstand APP - Umfassendes Benutzerhandbuch

## Inhaltsverzeichnis

1. [<PERSON><PERSON><PERSON><PERSON>](#überblick)
2. [Systemarchitektur](#systemarchitektur)
3. [Authentifizierung und Benutzerverwaltung](#authentifizierung-und-benutzerverwaltung)
4. [Frontend-Module und Funktionen](#frontend-module-und-funktionen)
5. [Backend-Architektur und APIs](#backend-architektur-und-apis)
6. [Datenbank-Schema und Datenmodelle](#datenbank-schema-und-datenmodelle)
7. [Entwicklung und Deployment](#entwicklung-und-deployment)
8. [Technische Spezifikationen](#technische-spezifikationen)

---

## Überblick

Das **Lapp Leitstand** ist eine Electron-Desktop-Anwendung unter anderem für das Shopfloor-Management mit Fokus auf Logistik-KPI-Visualisierung. Die Anwendung bietet Echtzeit-Überwachung und Analyse für verschiedene Bereiche:

- **Versand (Dispatch)** - Warenbewegungen und Kommissionierung im Versand
- **Ablängerei (Cutting)** - Schneidleistung und Maschineneffizienz
- **Wareneingang (Incoming Goods)** - Wareneingangspositionen
- **Bestand** - Bestandsmonitoring und Lagerauslastung
- **CSR** - [! Noch in der Entwicklung !] Für die darstellung wurden erstmal nur Mock Daten verwendet

### Hauptmerkmale

- **User Zugriffskontrolle** mit drei Benutzerrollen
- **Echtzeit Dashboard** mit Kennzahlen für die Intralogistik
- **Störungsmanagement** für Live System- und Anlagenmonitoring, Störungsmeldung und Bereitschaftsmanagement
- **KI-gestützter Chatbot** für Datenanalyse und Guide
- **Backend-Workflow-Management** für Datenbankkontrolle und Automatisierte Workflows [! Ohne Power Automate !]
- **User Settings** Usermanagement und Profil bearbeiten
- **App Settings** Application Einstellungen

---

## Systemarchitektur

### Frontend-Architektur

```
src/
├── modules/                   # Modulare Anwendungsstruktur
│   ├── dashboard/             # Hauptdashboard-Module
│   ├── stoerungen/            # Störungsmanagement
│   ├── backend/               # System-Management
│   ├── ai/                    # KI-Chatbot (in Entwicklung)
│   └── user/                  # Benutzerverwaltung
├── components/                # Wiederverwendbare UI-Komponenten
│   ├── ui/                    # shadcn/ui Basis-Komponenten
│   ├── auth/                  # Authentifizierungs-Komponenten
│   └── charts/                # Chart-Komponenten (Recharts)
├── pages/                     # Haupt-Seiten (Login, Landing, Settings)
├── services/                  # API-Services und Datenlogik
├── repositories/              # Daten-Repository-Pattern
├── contexts/                  # React Context (Auth, Navigation)
├── hooks/                     # Custom React Hooks
└── routes/                    # TanStack Router Konfiguration
```

### Backend-Architektur

```
backend/
├── src/
│   ├── controllers/           # API-Controller
│   ├── routes/                # Express-Routen
│   ├── services/              # Business-Logic-Services
│   ├── repositories/          # Datenbank-Repositories
│   ├── middleware/            # Express-Middleware
│   ├── utils/                 # Hilfsfunktionen
│   └── types/                 # TypeScript-Typdefinitionen
├── prisma/                    # Prisma ORM Schema
└── database/                  # SQLite-Datenbankdateien
```

---

## Authentifizierung und Benutzerverwaltung

### Benutzerrollen

Das System implementiert eine hierarchische Rollenverwaltung:

#### 1. **Besucher (Visitor)**
- **Zugriff**: Nur Leitstand-Modul (Dashboard)
- **Berechtigung**: Nur-Lesen-Zugriff auf KPI-Dashboards
- **Funktionen**: 
  - Anzeige von Versand-, Ablängerei- und Wareneingangs-KPIs
  - Interaktive Charts mit Datumsfilterung
  - Keine Störungsmanagement-Funktionen

#### 2. **Benutzer (User)**
- **Zugriff**: Leitstand + Störungsmanagement
- **Berechtigung**: Lesen und Schreiben in zugewiesenen Modulen
- **Funktionen**:
  - Alle Besucher-Funktionen
  - Störungen melden und bearbeiten
  - Störungskommentare hinzufügen
  - Live-Monitoring-Dashboard

#### 3. **Administrator (Admin)**
- **Zugriff**: Alle Module
- **Berechtigung**: Vollzugriff auf alle Funktionen
- **Funktionen**:
  - Alle Benutzer-Funktionen
  - Backend-System-Management
  - Workflow-Verwaltung
  - Benutzerverwaltung
  - App-Einstellungen

### Authentifizierungsflow

#### Login-Prozess
1. **Login-Seite** (`/login`)
   - Benutzername/Passwort-Eingabe
   - JWT-Token-basierte Authentifizierung
   - Automatische Weiterleitung nach erfolgreicher Anmeldung

2. **Registrierung** (`/register`)
   - Neue Benutzerkonten erstellen
   - E-Mail-Validierung
   - Standardrolle: Besucher

3. **Session-Management**
   - JWT-Token mit 15-Minuten-Ablaufzeit
   - Automatische Abmeldung bei Inaktivität
   - Token-Speicherung im localStorage

#### Sicherheitsfeatures
- **Passwort-Hashing**: bcrypt mit 12 Salt-Runden
- **Input-Validierung**: Frontend und Backend
- **Rate-Limiting**: API-Endpunkt-Schutz
- **HTTPS-Only**: Produktionsumgebung

---

## Frontend-Module und Funktionen

### 1. Dashboard-Modul (`/modules/dashboard`)

Das Hauptdashboard bietet eine Übersicht über alle drei Abteilungen, das Lager und Maschinen mit Echtzeit-KPIs.
CSR on Maintain

#### Hauptseite (`DashboardPage.tsx`)
- **Tagesübersicht**: Aktuelle Tagesübersicht der Abteilungen
- **Automatische Datenaktualisierung**: Lädt gestrige Daten als Standard
- **Fallback-Mechanismus**: Verwendet neueste verfügbare Daten wenn gestrige nicht vorhanden

#### Versand-Dashboard (`DispatchPage.tsx`)
**Verfügbare Charts:**
- **Servicegrad-Chart**: Zeigt Servicegrad-Entwicklung über Zeit
- **Tagesleistung-Chart**: Produzierte Tonnagen, Direktverladung, Umschlag
- **Lieferpositionen-Chart**: Ausgelieferte vs. rückständige Positionen
- **Picking-Chart**: ATRL + ARIL Picking-Performance
- **QM-Meldungen-Chart**: Qualitätsmeldungen (Angenommen/Abgelehnt/Offen)

**Interaktive Funktionen:**
- **Datumsbereich-Filter**: Standardmäßig 15. April - 7. Mai 2025
- **Responsive Layout**: Automatische Anpassung an Bildschirmgröße
- **Error Boundaries**: Robuste Fehlerbehandlung für Charts

#### Ablängerei-Dashboard (`CuttingPage.tsx`)
**Verfügbare Charts:**
- **Lager-Cuts-Chart**: Schnitte nach Lagertyp (Kunden und Restschnitte)
- **Schnitte-Data-Chart**: Detaillierte Schnittdaten nach Typ (Trommel-Trommel; Trommel-Ring; etc.)
- **Cuttings-Chart**: Maschinenspezifische Schnittleistung
- **Maschinen-Effizienz-Chart**: Effizienzanalyse der Schnittmaschinen

#### Wareneingang-Dashboard (`IncomingGoodsPage.tsx`)
- **WE-Positionen**: Automatische vs. manuelle Wareneingangspositionen
- **Automatisierungsgrad**: Prozentuale Verteilung
- **Qualitätsmetriken**: WE-spezifische Qualitätsindikatoren

### 2. Störungsmanagement-Modul (`/modules/stoerungen`)

Umfassendes System für Störungsmanagement und Live-Monitoring.

#### Hauptfunktionen (`StoerungenPage.tsx`)

**Tab 1: Live Monitoring**
- **System-Status-Übersicht**: Echtzeit-Status aller kritischen Systeme
- **System-Status-Heatmap**: Visuelle Darstellung der Systemverfügbarkeit
- **Automatische Aktualisierung**: Live-Updates ohne Seitenneuladen

**Tab 2: Störungsmanagement**
- **Störungsliste**: Vollständige Liste aller Störungen mit Filteroptionen
- **Störung melden**: Formular für neue Störungsmeldungen
- **Störungsbearbeitung**: Status-Updates und Kommentarfunktion [!Ähnliche Logik wie Ticketsystem!]
- **Prioritätsverwaltung**: Schweregrad-basierte Priorisierung [!Ähnliche Logik wie Ticketsystem!]
- **Bereitschaftsmanagement:** Bereitschaftsplanung (Personen, Kontakte, Wochenplan)

**Tab 3: Analytics Dashboard**
- **KPI-Cards**: 6 Hauptkennzahlen von Störungen (Gesamt, Offen, Kritisch, MTTR, etc.)
- **Severity-Distribution**: Radial-Chart der Störungsschweregrade
- **Status-Distribution**: Pie-Chart der Störungsstatus
- **MTTR-Trend**: Mean Time To Resolution Entwicklung
- **Kategorie-Charts**: Störungen nach Kategorien und Systemen

#### Störungsformular-Features
- **Kategorisierung**: System, Schweregrad, Standort
- **Zuweisungssystem**: Automatische Zuweisung
- **Kommentarsystem**: Threaded Comments mit Zeitstempel
- **Status-Tracking**: Vollständiger Lifecycle von Meldung bis Lösung
- **Störungsprotokoll** Optional, bei Aktivierung wird automatisch ein Störungsprotokoll verschickt
- **Benachrichtigung:** Automatische Benachrichtigung über Team und Telefon an die entsprechende Person im aktuellen Bereitschaftsdienst

#### Bereitschaftsmanagement-Features
- **Bereitschaftsdienst**: Anzeige der aktuellen Bereitschaftsplanung 
- **Konfiguration**: Zuweisung der Personen in den Wöchentlichen Bereitschaftsplan
- **Personen und Abteilungen**: Create, Delete and Update von Personen und Abteilungen
- **Benachrichtigung**: Bei Störmeldungen Automatische Benachrichtigung über Team und Telefon an die entsprechende Person im aktuellen Bereitschaftsdienst

### 3. Backend-Modul (`/modules/backend`)

Systemverwaltung und Workflow-Management für Administratoren.

#### System-Seite (`SystemPage.tsx`)

**Tab 1: System Performance**
- **Performance-Dashboard**: CPU, Memory, Response Times
- **Echtzeit-Metriken**: Live-System-Monitoring
- **Performance-Alerts**: Automatische Warnungen bei Schwellenwerten

**Tab 2: System Error Monitoring**
- **Error-Logs**: Detaillierte Fehlerprotokollierung
- **Error-Kategorisierung**: Nach Typ und Schweregrad
- **Trend-Analyse**: Fehlerentwicklung über Zeit

**Tab 3: Anlagen-Verfügbarkeit**
- **FTS-Verfügbarkeit**: Fahrerlose Transportsysteme
- **System-FTS-Chart**: Verfügbarkeitsmetriken mit Datumsfilter
- **Downtime-Analyse**: Ausfallzeiten und Ursachen

#### Workflow-Seite (`WorkflowPage.tsx`)

Übersicht aller Workflow Prozesse und Automatisierung [!Löst Power Automate ab!]

**Tab 1: Workflow Automatisierung**
1. **SAP Workflows (Servicegrad)** 
	- **Konfiguration**: Transaktion Eingabefelder, Schedule (Täglich, Wöchentlich, Stündlich), E-Mail Verteiler
	- **Trigger**: Manueller start oder nach Scheduled Automatisierung
	- **Execution-Tracking**: Live Logs Tracking
	- **Datenbank:** SAP Data execution wird automatisch in die Datenbank geladen

2. **SAP Workflow (Bestand)**
	- **Konfiguration**: Transaktion Eingabefelder für Lagerauswahl, Schedule (Täglich, Wöchentlich, Stündlich)
	- **Trigger**: Manueller start oder nach Scheduled Automatisierung
	- **Execution-Tracking**: Live Logs Tracking
	- **Datenbank:** SAP Data execution wird automatisch in die Datenbank geladen

3. **SAP Workflows (Rückstandsliste)** 
	- **Konfiguration**: Transaktion Eingabefelder, Schedule (Täglich, Wöchentlich, Stündlich), E-Mail Verteiler
	- **Trigger**: Manueller start oder nach Scheduled Automatisierung
	- **Execution-Tracking**: Live Logs Tracking
	- **Datenbank:** SAP Data execution wird automatisch in die Datenbank geladen

==Weitere Workflows folgen ...==

**Tab 3: Performance Analytics**
- **Performance-Analytics-Chart**: Detaillierte Performance-Analyse
- **Trend-Monitoring**: Langzeit-Performance-Trends

**Tab 4: System Logs**
- **Workflow-Log-Viewer**: Detaillierte Log-Anzeige
- **Log-Filterung**: Nach Zeit, Level, Workflow-ID
- **Export-Funktionen**: Log-Export für weitere Analyse

### 4. AI-Modul (`/modules/ai`) - VOLLSTÄNDIG IMPLEMENTIERT

Das AI-Modul ist das intelligente Herzstück der Anwendung und bietet umfassende KI-gestützte Funktionen für Datenanalyse, Optimierung, Dokumentenmanagement und Benutzerunterstützung.

#### AI-Dashboard (`AIDashboardPage.tsx`)

**Zentrale Navigation mit AI-Services:**
- **JASZ Knowledge Base**: RAG-basierte Wissensdatenbank mit Dokumentenmanagement
- **JASZ Cutting Optimizer**: KI-gestützte Schnittoptimierung mit Trommelrechnung
- **JASZ Inventory Intelligence**: Intelligente Bestandsanalyse und ABC-Klassifizierung
- **Process Optimization**: KI-basierte Prozessoptimierung und Workflow-Verbesserung
- **Warehouse Optimization**: Lagerplatzoptimierung und Kommissionierrouten
- **Supply Chain Optimization**: Lieferkettenoptimierung und Bedarfsprognosen
- **Predictive Analytics**: Vorhersagemodelle und Trendanalysen
- **Automated Reporting**: Automatische Berichtsgenerierung
- **AI Security Dashboard**: Sicherheitsüberwachung und Anomalieerkennung

**Dashboard-Features:**
- **Service-Status-Monitoring**: Echtzeit-Status aller AI-Services
- **Performance-Metriken**: Antwortzeiten, Erfolgsraten, Nutzungsstatistiken
- **Quick-Actions**: Schnellzugriff auf häufig verwendete AI-Funktionen
- **Konfigurationsmanagement**: Zentrale Einstellungen für alle AI-Services

#### 1. RAG Management (`RAGManagementPage.tsx`) - ZENTRALE WISSENSDATENBANK

**Hauptfunktionen:**
- **Dokumenten-Upload und -Verwaltung**: Zentrale Verwaltung aller Unternehmensdokumente
- **RAG-Pipeline**: Vollständige Retrieval-Augmented Generation Implementierung
- **Semantische Suche**: Intelligente Dokumentensuche mit Embedding-Technologie
- **Wissensdatenbank-Management**: Strukturierte Organisation von Wissen

**Tab 1: Dokument-Upload (`DocumentUpload`)**
- **Multi-Format-Support**: PDF, DOC, DOCX, TXT, MD, HTML
- **Drag & Drop Interface**: Benutzerfreundlicher Upload-Bereich
- **Automatische Verarbeitung**: Text-Extraktion und Chunking
- **Metadaten-Management**: Titel, Beschreibung, Kategorie, Sprache
- **Upload-Progress**: Live-Tracking des Verarbeitungsstatus
- **Batch-Upload**: Mehrere Dokumente gleichzeitig hochladen

**Tab 2: Dokumenten-Liste (`DocumentList`)**
- **Vollständige Dokumentenübersicht**: Alle hochgeladenen Dokumente
- **Such- und Filterfunktionen**: Nach Name, Kategorie, Datum, Status
- **Dokumenten-Vorschau**: Schnelle Inhaltsvorschau
- **Bearbeitungs-Tools**: Metadaten bearbeiten, Dokumente löschen
- **Status-Tracking**: Verarbeitungsstatus und Embedding-Status
- **Batch-Operationen**: Mehrere Dokumente gleichzeitig verwalten

**Tab 3: RAG-Einstellungen (`RAGSettingsManager`)**
- **Embedding-Konfiguration**: OpenAI vs. Ollama Embedding-Modelle
- **Chunk-Size-Optimierung**: Anpassbare Textaufteilung (512-2048 Tokens)
- **Similarity-Threshold**: Relevanz-Schwellenwerte (0.1-0.9)
- **Retrieval-Parameter**: Anzahl der abgerufenen Dokumente (1-20)
- **Sprach-Einstellungen**: Deutsch/Englisch Support
- **Performance-Tuning**: Optimierung der Suchgeschwindigkeit

**RAG-Statistiken Dashboard:**
- **Dokument-Metriken**: Gesamtanzahl Dokumente, Chunks, Embeddings
- **Suchstatistiken**: Häufigste Suchanfragen, Erfolgsraten
- **Performance-Metriken**: Durchschnittliche Antwortzeiten
- **Speicher-Nutzung**: Datenbank-Größe und Embedding-Speicher

#### 2. Cutting Optimization (`CuttingOptimizationPage.tsx`) - KI-GESTÜTZTE SCHNITTOPTIMIERUNG

**Hauptfunktionen:**
- **Intelligente Schnittplanung**: KI-basierte Optimierung der Trommelausnutzung
- **Trommelrechnung-Integration**: Automatische Berechnung maximaler Kabellängen
- **Verschnitt-Minimierung**: Optimierung zur Reduzierung von Materialverschwendung
- **Multi-Constraint-Optimierung**: Berücksichtigung von Kabeltypen, Längen, Prioritäten

**Visualisierungs-Komponenten:**
- **Schnittplan-Visualisierung (`CuttingPlanVisualization`)**: Grafische Darstellung optimaler Schnittmuster
- **Alternative Schnittmuster (`CuttingAlternativesChart`)**: Vergleich verschiedener Optimierungsansätze
- **Verschnitt-Analyse (`WasteAnalysisChart`)**: Detaillierte Analyse der Materialeffizienz
- **Effizienz-Metriken**: Echtzeit-Berechnung der Optimierungsgewinne

**Optimierungs-Features:**
- **Bin-Packing-Algorithmus**: Maximale Trommelausnutzung durch intelligente Platzierung
- **Restlängen-Verwertung**: Optimale Nutzung vorhandener Restbestände
- **Produktionsplanung**: Optimale Reihenfolge der Schnittaufträge
- **Export-Funktionen**: Schnittpläne als PDF, Excel oder CSV exportieren

**Backend-Services:**
- **CuttingOptimizerService**: Kern-Optimierungsalgorithmus
- **TrommelrechnungService**: Trommelrechnung und Kapazitätsberechnung
- **MaterialEfficiencyService**: Verschnitt-Analyse und Effizienzberechnung

#### 3. Inventory Intelligence (`InventoryIntelligencePage.tsx`) - INTELLIGENTE BESTANDSFÜHRUNG

**Hauptfunktionen:**
- **KI-gestützte Bestandsanalyse**: Intelligente Analyse und Klassifizierung
- **ABC-Analyse 2.0**: Dynamische Klassifizierung mit KI-Unterstützung
- **Nachfrageprognose**: Vorhersage zukünftiger Bedarfe
- **Intelligente Empfehlungen**: Automatische Bestellvorschläge

**Dashboard-Komponenten (`InventoryIntelligenceDashboard`):**
- **Bestandsübersicht**: Echtzeit-Status aller Lagerbestände
- **ABC-Klassifizierung**: Dynamische Kategorisierung nach Wertigkeit
- **Trend-Analysen**: Entwicklung der Lagerbestände über Zeit
- **Engpass-Vorhersage**: Frühwarnsystem für kritische Bestände

**Intelligente Features:**
- **Anomalie-Erkennung**: Identifikation ungewöhnlicher Bestandsbewegungen
- **Saisonalitäts-Analyse**: Erkennung saisonaler Muster
- **Lieferanten-Performance**: Bewertung der Lieferantenleistung
- **Optimierungsvorschläge**: KI-basierte Verbesserungsempfehlungen

#### 4. Predictive Analytics (`PredictiveAnalyticsPage.tsx`) - KI-GESTÜTZTE VORHERSAGEANALYSEN

**Entwicklungsstatus:** In aktiver Entwicklung - Grundgerüst implementiert

**Geplante Vorhersagemodelle:**
- **KPI-Trendvorhersagen**: Prognose der Entwicklung wichtiger Kennzahlen
- **Anomalie-Erkennung**: Frühwarnsystem für ungewöhnliche Muster
- **Kapazitätsplanung**: Vorhersage von Engpässen und Überkapazitäten
- **Qualitätsprognosen**: Vorhersage von Qualitätsproblemen
- **Wartungsplanung**: Optimale Wartungszeitpunkte basierend auf Predictive Maintenance

**Geplante Analytics-Features:**
- **Trend-Erkennung**: Automatische Identifikation von Mustern in historischen Daten
- **Saisonalitäts-Analyse**: Erkennung wiederkehrender Zyklen
- **Korrelations-Analyse**: Zusammenhänge zwischen verschiedenen KPIs
- **Scenario-Modelling**: Was-wäre-wenn-Analysen für Entscheidungsunterstützung

**Geplante Visualisierungen:**
- **Interaktive Prognose-Charts**: Dynamische Vorhersage-Diagramme
- **Konfidenz-Intervalle**: Unsicherheitsbereiche der Prognosen
- **Alert-Dashboard**: Automatische Warnungen bei kritischen Vorhersagen
- **Trend-Heatmaps**: Visuelle Darstellung von Entwicklungsmustern

#### 5. Weitere AI-Seiten - VOLLSTÄNDIG IMPLEMENTIERT

**Process Optimization (`ProcessOptimizationPage.tsx`):**
- **Workflow-Optimierung**: KI-basierte Verbesserung von Geschäftsprozessen
- **Effizienz-Analysen**: Identifikation von Optimierungspotenzialen
- **Automatisierungs-Empfehlungen**: Vorschläge für Prozessautomatisierung

**Warehouse Optimization (`WarehouseOptimizationPage.tsx`):**
- **Lagerplatz-Optimierung**: Optimale Platzierung basierend auf Zugriffshäufigkeit
- **Kommissionier-Routen**: Berechnung kürzester Wege für Picker
- **Lager-Layout-Optimierung**: KI-gestützte Layoutverbesserungen

**Supply Chain Optimization (`SupplyChainOptimizationPage.tsx`):**
- **Lieferketten-Analyse**: Optimierung der gesamten Lieferkette
- **Lieferanten-Bewertung**: KI-basierte Lieferantenanalyse
- **Bestelloptimierung**: Optimale Bestellmengen und -zeitpunkte

**Automated Reporting (`AutomatedReportingPage.tsx`):**
- **Automatische Berichtsgenerierung**: KI-gestützte Report-Erstellung
- **Intelligente Datenaufbereitung**: Automatische Analyse und Zusammenfassung
- **Personalisierte Berichte**: Rollenbasierte Report-Anpassung

**AI Security Dashboard (`AISecurityDashboard`):**
- **Sicherheitsüberwachung**: Monitoring aller AI-Services
- **Anomalie-Erkennung**: Identifikation verdächtiger Aktivitäten
- **Zugriffskontrolle**: Verwaltung der AI-Berechtigungen

#### 6. Optimization Tools - VOLLSTÄNDIG IMPLEMENTIERT

**Schneidoptimierung (Cutting Optimization):**
- **Bin-Packing-Algorithmus**: Maximale Trommelausnutzung mit minimaler Verschwendung
- **Multi-Constraint-Optimization**: Berücksichtigung von Kabeltyp, Trommelgrößen und Auftragsprioritäten
- **Restlängen-Management**: Intelligente Verwertung von Reststücken
- **Produktionsplanung**: Optimale Reihenfolge basierend auf Effizienz und Terminen
- **Trommelrechnung-Integration**: Berücksichtigung der verfügbaren Trommellängen

**Lageroptimierung (Warehouse Optimization):**
- **ABC-Analyse 2.0**: KI-erweiterte Klassifizierung mit dynamischen Faktoren
- **Dynamische Lagerplatzvergabe**: Optimale Platzierung basierend auf Zugriffshäufigkeit
- **Kommissionier-Routen-Optimierung**: Berechnung kürzester und effizientester Wege
- **Bestandsoptimierung**: Optimale Lagermengen mit Engpassvorhersage
- **Lager-Layout-Optimierung**: KI-gestützte Verbesserung der Lagerstruktur

**Prozess- und Ressourcenoptimierung:**
- **Workflow-Optimierung**: KI-basierte Verbesserung von Geschäftsprozessen
- **Maschinenauslastung**: Optimale Verteilung der Aufträge auf verfügbare Ressourcen
- **Personalplanung**: Intelligente Schichtplanung und Kapazitätsverteilung
- **Energieoptimierung**: Reduzierung des Energieverbrauchs durch optimierte Abläufe
- **Wartungsoptimierung**: Predictive Maintenance für optimale Wartungsintervalle

#### 7. AI-Backend-Integration - VOLLSTÄNDIG IMPLEMENTIERT

**OpenRouter API Integration (`openrouter.service.ts`):**
- **Multi-Model-Support**: Zugriff auf verschiedene KI-Modelle (GPT, Claude, Llama, etc.)
- **Dynamisches Model-Switching**: Intelligente Modellauswahl basierend auf Anfrage-Typ
- **Database-Context-Injection**: Automatische Einbindung relevanter Datenbankdaten
- **Cost-Optimization**: Kostenoptimierte Modellauswahl und Token-Management
- **Fallback-Mechanismen**: Automatische Ausfallsicherheit bei API-Problemen
- **System-Prompt-Management**: Dynamische Erstellung kontextspezifischer Prompts

**Erweiterte AI-Services (`ai.service.ts`):**
- **Anomalie-Erkennung**: Lager-, Schnitt-, Versand- und System-Anomalien
- **Prädiktive Analysen**: Bestandsengpässe, Kapazitätsüberlastung, Effizienzabfall
- **Optimierungsplanung**: Schnitt-, Lager- und Logistik-Optimierung
- **Insight-Generierung**: Performance-, Effizienz-, Qualitäts- und Kapazitäts-Einblicke
- **Multi-Service-Integration**: Nahtlose Integration aller AI-Funktionen

**RAG-Pipeline (`SimpleRAGService.ts`, `EmbeddingService.ts`):**
- **Document Processing**: Automatische Dokumentenverarbeitung und -chunking
- **Embedding Generation**: Vektorisierung mit OpenAI und Ollama-Modellen
- **Vector Database**: SQLite-basierte Vektorspeicherung mit Semantic Search
- **Knowledge Base Management**: Vollständige Wissensdatenbank-Verwaltung
- **Performance Monitoring**: Metriken und Abfrage-Tracking
- **Batch Processing**: Effiziente Verarbeitung großer Dokumentenmengen
- **Caching System**: Intelligentes Caching für bessere Performance

#### 8. Security & Privacy - VOLLSTÄNDIG IMPLEMENTIERT

**Datenschutz und Sicherheit:**
- **Data Encryption**: Verschlüsselung aller AI-relevanten Daten in der SQLite-Datenbank
- **Access Control**: Rollenbasierte Zugriffskontrolle auf alle AI-Features
- **Audit Logging**: Vollständige Protokollierung aller AI-Aktivitäten und -anfragen
- **Data Anonymization**: Automatische Anonymisierung sensibler Daten vor AI-Verarbeitung
- **API-Key-Management**: Sichere Verwaltung von OpenRouter und anderen API-Schlüsseln
- **Local Processing**: Lokale Verarbeitung sensibler Daten ohne externe Übertragung

**AI Security Dashboard:**
- **Sicherheitsüberwachung**: Real-time Monitoring aller AI-Services
- **Anomalie-Erkennung**: Identifikation verdächtiger AI-Aktivitäten
- **Zugriffskontrolle**: Verwaltung und Überwachung der AI-Berechtigungen
- **Compliance-Reporting**: Automatische Generierung von Sicherheitsberichten

#### 9. Aktuelle Implementierung & Status (Stand: 2024)

**Vollständig implementiert:**
✅ **AI-Dashboard**: Zentrale Navigation und Service-Übersicht
✅ **RAG-Management**: Vollständige Dokumentenverwaltung und Wissensdatenbank
✅ **Cutting Optimization**: KI-gestützte Schnittoptimierung mit Bin-Packing
✅ **Inventory Intelligence**: Intelligente Bestandsführung mit ABC-Analyse 2.0
✅ **Process Optimization**: Workflow-Optimierung und Effizienzanalysen
✅ **Warehouse Optimization**: Lageroptimierung und Kommissionier-Routen
✅ **Supply Chain Optimization**: Lieferketten-Analyse und -optimierung
✅ **Automated Reporting**: KI-gestützte Berichtsgenerierung
✅ **AI Security Dashboard**: Sicherheitsüberwachung und Zugriffskontrolle
✅ **Backend AI-Services**: Vollständige Integration aller AI-Funktionen
✅ **OpenRouter Integration**: Multi-Model-Support mit Database-Context-Injection

**In aktiver Entwicklung:**
🔄 **Predictive Analytics**: Grundgerüst implementiert, Modelle in Entwicklung
🔄 **Advanced Anomalie-Erkennung**: Basis vorhanden, Erweiterung geplant
🔄 **Multi-Language AI Support**: Grundlagen implementiert, Erweiterung geplant

**Geplante Erweiterungen (2025):**
📋 **Voice-to-Text Integration**: Sprachsteuerung für AI-Chat
📋 **Computer Vision Integration**: Qualitätskontrolle durch Bildanalyse
📋 **IoT-Integration**: Echtzeit-Daten für verbesserte Optimierung
📋 **Advanced Predictive Models**: Erweiterte Vorhersagemodelle
📋 **Mobile App Integration**: AI-Features für mobile Anwendungen
📋 **Digital Twin Integration**: Virtuelle Abbilder für Simulation und Optimierung

#### 10. Performance & Skalierung - OPTIMIERT

**Aktuelle Response Times:**
- **AI-Chat mit RAG**: < 3 Sekunden (abhängig vom Modell)
- **Schneidoptimierung**: < 2 Sekunden für Standard-Aufträge
- **RAG-Suche**: < 1 Sekunde (mit Caching)
- **Bestandsanalysen**: < 5 Sekunden
- **Dokumenten-Processing**: < 10 Sekunden pro Dokument
- **Komplexe Optimierungen**: < 30 Sekunden

**Skalierbarkeits-Features:**
- **Dokumenten-Kapazität**: Bis zu 10.000 Dokumente in RAG-Datenbank
- **Gleichzeitige Sessions**: Bis zu 50 Chat-Sessions parallel
- **Intelligentes Caching**: Embedding-Cache und Antwort-Cache
- **Batch Processing**: Effiziente Verarbeitung großer Datenmengen
- **Asynchrone Verarbeitung**: Non-blocking AI-Operations
- **Load Balancing**: Automatische Verteilung der AI-Anfragen
- **Model Switching**: Dynamische Modellauswahl basierend auf Last und Kosten
### 5. Benutzer-Landing-Seite (`UserLandingPage.tsx`)

**Modulauswahl-Interface:**
- **Animierte Begrüßung**: Personalisierte Willkommensnachricht
- **Modul-Karten**: Visuelle Darstellung verfügbarer Module
- **Rollenbasierte Anzeige**: Nur zugängliche Module werden angezeigt
- **Entwicklungsstatus**: Kennzeichnung von Modulen in Entwicklung

**Verfügbare Module:**
- Dashboard (alle Rollen)
- Störungsmanagement (Benutzer, Administrator)
- Backend (nur Administrator)
- AI (in Entwicklung)
- App-Settings (alle Rollen)
- User-Settings (alle Rollen)

---

## Backend-Architektur und APIs

### Express.js mit Node.js Server-Konfiguration

#### Server-Setup (`server.ts`)
- **Port**: 3001 (konfigurierbar über Umgebungsvariablen)
- **Security**: Helmet.js für HTTP-Header-Sicherheit
- **CORS**: Konfiguriert für Electron-Kompatibilität
- **Rate-Limiting**: Verschiedene Limits für verschiedene Endpunkt-Typen
- **Performance-Tracking**: Middleware für Request-Monitoring

#### Middleware-Stack
1. **Security-Middleware**: Helmet, CORS, Input-Sanitization
2. **Authentication-Middleware**: JWT-Token-Validierung
3. **Rate-Limiting**: Schutz vor API-Missbrauch
4. **Performance-Tracking**: Request-Timing und Monitoring
5. **Error-Handling**: Globale Fehlerbehandlung

### API-Routen-Struktur

#### 1. Authentifizierungs-Routen (`/api/auth`)
```typescript
POST /api/auth/login          // Benutzer-Anmeldung
POST /api/auth/register       // Benutzer-Registrierung
POST /api/auth/logout         // Benutzer-Abmeldung
GET  /api/auth/me            // Aktueller Benutzer
PUT  /api/auth/profile       // Profil-Update
```

#### 2. Datenbank-Routen (`/api/database`)
```typescript
// Versand-Daten
GET /api/database/service-level        // Servicegrad-Daten
GET /api/database/daily-performance    // Tägliche Leistung
GET /api/database/picking             // Kommissionierung
GET /api/database/delivery-positions  // Lieferpositionen
GET /api/database/tagesleistung       // Tagesleistung
GET /api/database/returns             // Retouren/QM-Meldungen

// Ablängerei-Daten
GET /api/database/ablaengerei         // Ablängerei-Daten
GET /api/database/schnitte-data       // Schnittdaten
GET /api/database/cutting-chart-data  // Cutting-Chart-Daten
GET /api/database/lager-cuts-chart-data // Lager-Cuts-Daten
GET /api/database/maschinen-efficiency  // Maschineneffizienz

// Wareneingang-Daten
GET /api/database/we                  // Wareneingang-Daten
GET /api/database/lagerauslastung200  // Lagerauslastung 200
GET /api/database/lagerauslastung240  // Lagerauslastung 240

// Warehouse-Daten
GET /api/database/atrl-data           // ATrL-Daten
GET /api/database/aril-data           // ARiL-Daten
```

#### 3. Störungsmanagement-Routen (`/api/stoerungen`)
```typescript
GET    /api/stoerungen                // Alle Störungen
POST   /api/stoerungen                // Neue Störung
GET    /api/stoerungen/:id            // Einzelne Störung
PUT    /api/stoerungen/:id            // Störung aktualisieren
DELETE /api/stoerungen/:id            // Störung löschen
GET    /api/stoerungen/:id/comments   // Störungskommentare
POST   /api/stoerungen/:id/comments   // Kommentar hinzufügen
GET    /api/stoerungen/stats          // Störungsstatistiken
```

#### 4. System-Routen (`/api/system`)
```typescript
GET /api/system/status               // System-Status
GET /api/system/performance          // Performance-Metriken
GET /api/system/fts-data            // FTS-Verfügbarkeitsdaten
GET /api/system/health              // Health-Check
```

#### 5. Workflow-Routen (`/api/workflows`)
```typescript
GET    /api/workflows               // Alle Workflows
GET    /api/workflows/:id           // Einzelner Workflow
POST   /api/workflows/:id/execute   // Workflow ausführen
GET    /api/workflows/:id/logs      // Workflow-Logs
GET    /api/workflows/executions    // Ausführungshistorie
```

#### 6. Performance-Routen (`/api/performance`)
```typescript
GET /api/performance/metrics        // Performance-Metriken
GET /api/performance/alerts         // Performance-Alerts
GET /api/performance/cache-stats    // Cache-Statistiken
```

#### 7. AI-Routen (`/api/ai`)
```typescript
POST /api/ai/chat                   // AI-Chat-Nachrichten
GET  /api/ai/chat/history          // Chat-Verlauf
POST /api/ai/chat/clear            // Chat-Verlauf löschen
GET  /api/ai/models                // Verfügbare AI-Modelle
POST /api/ai/analyze               // Datenanalyse-Anfragen
```

#### 8. RAG-Routen (`/api/rag`)
```typescript
// Dokumentenverwaltung
POST   /api/rag/documents/upload    // Dokument hochladen
GET    /api/rag/documents          // Alle Dokumente
GET    /api/rag/documents/:id      // Einzelnes Dokument
PUT    /api/rag/documents/:id      // Dokument aktualisieren
DELETE /api/rag/documents/:id      // Dokument löschen
POST   /api/rag/documents/search   // Dokumentensuche

// RAG-Konfiguration
GET    /api/rag/config             // RAG-Konfiguration
PUT    /api/rag/config             // Konfiguration aktualisieren
POST   /api/rag/reindex            // Neuindizierung starten
GET    /api/rag/stats              // RAG-Statistiken

// Embedding-Management
POST   /api/rag/embeddings/generate // Embeddings generieren
GET    /api/rag/embeddings/status   // Embedding-Status
DELETE /api/rag/embeddings/clear    // Embeddings löschen
```

### Repository-Pattern

Das Backend implementiert das Repository-Pattern für saubere Datentrennung:

#### Repository-Factory (`repository.factory.ts`)
```typescript
// Zentrale Factory für alle Repositories
export const initializeRepositoryFactory = (prisma: PrismaClient) => {
  return {
    dispatch: new DispatchRepository(prisma),
    warehouse: new WarehouseRepository(prisma),
    cutting: new CuttingRepository(prisma),
    stoerungen: new StoerungenRepository(prisma)
  };
};
```

#### Repository-Implementierungen
- **DispatchRepository**: Versand-spezifische Datenoperationen
- **WarehouseRepository**: Lager-spezifische Datenoperationen  
- **CuttingRepository**: Ablängerei-spezifische Datenoperationen
- **StoerungenRepository**: Störungsmanagement-Datenoperationen
- **RAGRepository**: RAG-Dokumentenverwaltung und Embedding-Operationen
- **AIChatRepository**: AI-Chat-Verlauf und Analytics
- **PredictiveRepository**: Vorhersagemodelle und Analytics-Daten---

#
# Datenbank-Schema und Datenmodelle

### SQLite-Datenbank mit Prisma ORM

Die Anwendung verwendet SQLite als Datenbank mit Prisma als ORM für typsichere Datenbankoperationen.

### Hauptdatenmodelle

#### 1. Benutzer- und Rollenverwaltung
```prisma
model User {
  id           Int      @id @default(autoincrement())
  email        String   @unique
  username     String   @unique
  name         String?
  passwordHash String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  roles        Role[]
}

model Role {
  id    Int    @id @default(autoincrement())
  name  String @unique  // "Besucher", "Benutzer", "Administrator"
  users User[]
}
```

#### 2. Versand-Daten (Dispatch)
```prisma
model DispatchData {
  id                   Int     @id @default(autoincrement())
  datum                String?
  servicegrad          Float?  // Servicegrad in %
  ausgeliefert_lup     Int?    // Ausgelieferte Positionen
  rueckstaendig        Int?    // Rückständige Positionen
  produzierte_tonnagen Float?  // Produzierte Tonnagen
  direktverladung_kiaa Int?    // Direktverladung + KIAA
  umschlag             Int?    // Umschlag
  kg_pro_colli         Float?  // kg pro Colli
  elefanten            Int?    // Visualisierte Menge
  atrl                 Int?    // ATRL Picking
  aril                 Int?    // ARIL Picking
  qm_angenommen        Int?    // QM angenommen
  qm_abgelehnt         Int?    // QM abgelehnt
  qm_offen             Int?    // QM offen
}
```

#### 3. Ablängerei-Daten (Cutting)
```prisma
model Ablaengerei {
  id           Int     @id @default(autoincrement())
  Datum        String?
  cutTT        Int?    // Trommel-Trommel Schnitte
  cutTR        Int?    // Trommel-Ring Schnitte
  cutRR        Int?    // Ring-Ring Schnitte
  pickCut      Int?    // Cut2Pick Schnitte
  cutLagerK220 Int?    // Kunde 220 Schnitte
  cutLagerR220 Int?    // Rest 220 Schnitte
  cutLagerK240 Int?    // Kunde 240 Schnitte
  cutLagerR240 Int?    // Rest 240 Schnitte
}
```

#### 4. Wareneingang-Daten
```prisma
model WE {
  id     Int     @id @default(autoincrement())
  Datum  String?
  weAtrl Int?    // Automatische WE-Positionen
  weManl Int?    // Manuelle WE-Positionen
}
```

#### 5. Störungsmanagement
```prisma
model Stoerungen {
  id              Int       @id @default(autoincrement())
  title           String    // Störungstitel
  description     String?   // Beschreibung
  severity        String    // Schweregrad: "Niedrig", "Mittel", "Hoch", "Kritisch"
  status          String    // Status: "Offen", "In Bearbeitung", "Gelöst", "Geschlossen"
  category        String?   // Kategorie
  affected_system String?   // Betroffenes System
  location        String?   // Standort
  reported_by     String?   // Melder
  assigned_to     String?   // Zugewiesen an
  created_at      DateTime  @default(now())
  updated_at      DateTime
  resolved_at     DateTime? // Lösungszeitpunkt
  mttr_minutes    Int?      // Mean Time To Resolution
  tags            String?   // Tags (JSON)
}

model StoerungsComments {
  id           Int      @id @default(autoincrement())
  stoerung_id  Int      // Referenz zur Störung
  user_id      String?  // Kommentator
  comment      String   // Kommentartext
  created_at   DateTime @default(now())
}
```

#### 6. System-Monitoring
```prisma
model SystemStatus {
  id          Int      @id @default(autoincrement())
  system_name String   // Systemname
  status      String   // Status: "Online", "Offline", "Warning"
  last_check  DateTime @default(now())
  metadata    String?  // Zusätzliche Metadaten (JSON)
}

model System {
  id                                 Int     @id @default(autoincrement())
  Datum                              String?
  verfuegbarkeitAtrl_FT_RBG_MFR1     Float?  // ATRL Verfügbarkeit
  verfuegbarkeitAril_FT_RBG          Float?  // ARIL Verfügbarkeit
  verfuegbarkeitFTS                  Float?  // FTS Verfügbarkeit
  gesamtverfuegbarkeit_AtrL_ARiL_FTS Float?  // Gesamtverfügbarkeit
  verfuegbarkeitSAP                  Float?  // SAP Verfügbarkeit
  verfuegbarkeitServicegrad          Float?  // Servicegrad Verfügbarkeit
}
```

#### 7. Performance-Monitoring
```prisma
model PerformanceMetric {
  id        Int      @id @default(autoincrement())
  metricId  String   @unique
  type      String   // Metrik-Typ
  timestamp DateTime @default(now())
  duration  Int      // Dauer in ms
  success   Boolean  // Erfolgreich
  details   String   // Details (JSON)
}

model PerformanceAlert {
  id         Int       @id @default(autoincrement())
  type       String    // Alert-Typ
  message    String    // Alert-Nachricht
  metric     String    // Betroffene Metrik
  value      Float     // Aktueller Wert
  threshold  Float     // Schwellenwert
  resolved   Boolean   @default(false)
  resolvedAt DateTime?
  createdAt  DateTime  @default(now())
}
```

#### 8. Workflow-Management
```prisma
model WorkflowConfig {
  id             String    @id
  name           String    // Workflow-Name
  description    String?   // Beschreibung
  tcode          String?   // SAP T-Code
  enabled        Boolean   @default(true)
  scheduleCron   String?   // Cron-Schedule
  lastRun        DateTime? // Letzte Ausführung
  nextRun        DateTime? // Nächste Ausführung
}

model WorkflowExecution {
  id               String    @id
  workflowId       String    // Workflow-ID
  startTime        DateTime  // Startzeit
  endTime          DateTime? // Endzeit
  status           String    // Status
  durationSeconds  Int?      // Dauer in Sekunden
  recordsProcessed Int?      // Verarbeitete Datensätze
  errorMessage     String?   // Fehlermeldung
}

model WorkflowLog {
  id          String   @id
  timestamp   String   // Zeitstempel
  level       String   // Log-Level
  message     String   // Log-Nachricht
  workflowId  String   // Workflow-ID
  executionId String?  // Ausführungs-ID
  details     String?  // Details (JSON)
}
```

#### 9. AI und RAG-System
```prisma
model RAGDocument {
  id          String    @id @default(cuid())
  title       String    // Dokumenttitel
  filename    String    // Originaler Dateiname
  category    String    // Kategorie (dispatch, cutting, etc.)
  language    String    @default("de") // Sprache
  description String?   // Beschreibung
  content     String    // Extrahierter Textinhalt
  metadata    String?   // Zusätzliche Metadaten (JSON)
  fileSize    Int       // Dateigröße in Bytes
  mimeType    String    // MIME-Type
  uploadedBy  String?   // Hochgeladen von (User ID)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  chunks      RAGChunk[] // Verknüpfte Text-Chunks
}

model RAGChunk {
  id         String      @id @default(cuid())
  documentId String      // Referenz zum Dokument
  content    String      // Chunk-Inhalt
  chunkIndex Int         // Position im Dokument
  embedding  String?     // Embedding-Vektor (JSON)
  metadata   String?     // Chunk-Metadaten (JSON)
  createdAt  DateTime    @default(now())
  document   RAGDocument @relation(fields: [documentId], references: [id], onDelete: Cascade)
  
  @@index([documentId])
  @@index([chunkIndex])
}

model AIChat {
  id        String   @id @default(cuid())
  userId    String?  // Benutzer-ID
  message   String   // Benutzer-Nachricht
  response  String   // AI-Antwort
  model     String?  // Verwendetes AI-Modell
  tokens    Int?     // Verwendete Tokens
  duration  Int?     // Antwortzeit in ms
  context   String?  // RAG-Kontext (JSON)
  createdAt DateTime @default(now())
  
  @@index([userId])
  @@index([createdAt])
}

model RAGConfig {
  id              String  @id @default("default")
  embeddingModel  String  @default("text-embedding-3-small") // Embedding-Modell
  chunkSize       Int     @default(1000) // Chunk-Größe
  chunkOverlap    Int     @default(200)  // Chunk-Überlappung
  maxRetrievals   Int     @default(5)    // Max. abgerufene Chunks
  similarityThreshold Float @default(0.7) // Ähnlichkeits-Schwellenwert
  updatedAt       DateTime @updatedAt
}

model AIAnalytics {
  id           String   @id @default(cuid())
  type         String   // Analytics-Typ (chat, rag, prediction)
  userId       String?  // Benutzer-ID
  query        String?  // Ursprüngliche Anfrage
  result       String?  // Ergebnis (JSON)
  performance  String?  // Performance-Metriken (JSON)
  success      Boolean  @default(true)
  errorMessage String?  // Fehlermeldung
  createdAt    DateTime @default(now())
  
  @@index([type])
  @@index([userId])
  @@index([createdAt])
}
```

### Datenbank-Indizierung

Für optimale Performance sind folgende Indizes implementiert:

```prisma
// Zeitbasierte Indizes für häufige Abfragen
@@index([Datum])          // Alle zeitbasierten Tabellen
@@index([created_at])     // Alle Tabellen mit Erstellungsdatum
@@index([timestamp])      // Performance-Metriken

// Störungsmanagement-Indizes
@@index([affected_system]) // Störungen nach System
@@index([severity])        // Störungen nach Schweregrad
@@index([status])          // Störungen nach Status

// Performance-Indizes
@@index([type])           // Performance-Metriken nach Typ
@@index([success])        // Performance-Metriken nach Erfolg
@@index([resolved])       // Performance-Alerts nach Lösung

// RAG und AI-Indizes
@@index([documentId])     // RAG-Chunks nach Dokument
@@index([chunkIndex])     // RAG-Chunks nach Position
@@index([category])       // RAG-Dokumente nach Kategorie
@@index([language])       // RAG-Dokumente nach Sprache
@@index([userId])         // AI-Chat nach Benutzer
@@index([type])           // AI-Analytics nach Typ
```

---

## Entwicklung und Deployment

### Entwicklungsumgebung

#### Voraussetzungen
- **Node.js**: Version 14.0.0 oder höher
- **npm**: Für Paketmanagement
- **Git**: Für Versionskontrolle
- **SQLite**: Für lokale Datenbankentwicklung

#### Installation und Setup
```bash
# Repository klonen
git clone <repository-url>
cd SFM-Electron

# Frontend-Dependencies installieren
npm install

# Backend-Dependencies installieren
cd backend
npm install

# Zurück zum Root-Verzeichnis
cd ..

# Umgebungsvariablen konfigurieren
cp .env.example .env
cp backend/.env.example backend/.env

# Datenbank initialisieren
cd backend
npm run prisma:generate
npm run prisma:push
```

#### Entwicklungsserver starten
```bash
# Vollständige Entwicklungsumgebung (Frontend + Backend)
npm run start

# Nur Frontend (Vite Dev Server)
npm run dev:frontend

# Nur Backend (Nodemon)
npm run dev:backend
```

### Build-Prozess

#### Entwicklungs-Build
```bash
# Frontend-Build für Entwicklung
npm run build

# Backend-Build
cd backend
npm run build
```

#### Produktions-Build
```bash
# Electron-App paketieren
npm run package

# Distributables erstellen
npm run make

# Portable Windows-Build
npm run build:portable

# Vollständiger portabler Build
npm run build:portable-complete
```

### Testing

#### Unit-Tests
```bash
# Frontend-Tests (Vitest)
npm run test:unit

# Backend-Tests
cd backend
npm run test

# Tests mit Watch-Mode
npm run test:watch
```

#### End-to-End-Tests
```bash
# E2E-Tests (Playwright)
npm run test:e2e

# Alle Tests
npm run test:all
```

#### Test-Coverage
```bash
# Coverage-Report generieren
cd backend
npm run test:coverage
```

### Code-Qualität

#### Linting und Formatting
```bash
# ESLint ausführen
npm run lint

# Prettier ausführen
npm run format

# TypeScript-Typen prüfen
npx tsc --noEmit
```

#### Pre-Commit-Hooks
Das Projekt verwendet ESLint und Prettier für konsistente Code-Qualität:
- **ESLint**: TypeScript/React-spezifische Regeln
- **Prettier**: Automatische Code-Formatierung
- **TypeScript**: Strikte Typisierung ohne `any`-Types

### Deployment-Strategien

#### Portable Executable (Windows)
```bash
# Portable .exe erstellen
npm run build:portable

# Output: dist/LappDashboard-portable-{version}.exe
```

#### Electron-Installer
```bash
# Windows-Installer erstellen
npm run make

# Output: out/make/squirrel.windows/x64/
```

#### Docker-Deployment (Backend)
```dockerfile
# Beispiel Dockerfile für Backend
FROM node:18-alpine
WORKDIR /app
COPY backend/package*.json ./
RUN npm ci --only=production
COPY backend/dist ./dist
COPY backend/prisma ./prisma
EXPOSE 3001
CMD ["npm", "start"]
```-
--

## Technische Spezifikationen

### Frontend-Technologie-Stack

#### Core-Framework
- **Electron**: v35.2.1 - Desktop-Anwendungsframework
- **Vite**: v6.3.5 - Build-Tool und Dev-Server
- **TypeScript**: v5.8.3 - Primäre Programmiersprache
- **React**: v18.2.0 - UI-Framework mit JSX-Runtime

#### UI und Styling
- **Tailwind CSS**: v4.1.11 - Utility-First CSS-Framework
- **shadcn/ui**: Komponentenbibliothek basierend auf Radix UI
- **Neobrutalism-Design**: Kräftige Farben, starke Konturen (4px Schatten, 5px Border-Radius)
- **Framer Motion**: v12.23.12 - Animationen und Übergänge
- **Lucide React**: v0.294.0 - Icon-Bibliothek

#### State-Management und Daten
- **TanStack Query**: v5.74.7 - Server-State-Management
- **TanStack Router**: v1.121.2 - Typsichere Routing-Lösung
- **React Context**: Für globale Zustände (Auth, Navigation)

#### Charts und Visualisierung
- **Recharts**: v2.15.3 - Chart-Bibliothek für Datenvisualisierung
- **React Table**: v8.21.3 - Datenmanagement für Tabellen

#### Entwicklungstools
- **Electron Forge**: v7.8.1 - Paketierung und Distribution
- **ESLint**: v9.25.1 - Code-Linting
- **Prettier**: v3.5.3 - Code-Formatierung
- **Vitest**: v3.1.2 - Unit-Testing
- **Playwright**: v1.52.0 - E2E-Testing

### Backend-Technologie-Stack

#### Core-Framework
- **Node.js**: v14.0.0+ - JavaScript-Runtime
- **Express.js**: v4.18.2 - Web-Framework
- **TypeScript**: v5.1.3 - Typisierte JavaScript-Entwicklung
- **Prisma**: v6.13.0 - ORM und Datenbankmanagement

#### Datenbank
- **SQLite**: Eingebettete Datenbank
- **better-sqlite3**: v12.2.0 - SQLite-Interface für Node.js
- **Prisma Client**: Typsichere Datenbankabfragen

#### Sicherheit und Middleware
- **Helmet**: v8.1.0 - HTTP-Header-Sicherheit
- **CORS**: v2.8.5 - Cross-Origin-Resource-Sharing
- **bcryptjs**: v3.0.2 - Passwort-Hashing
- **jsonwebtoken**: v9.0.2 - JWT-Token-Management
- **express-rate-limit**: v7.5.1 - Rate-Limiting

#### API und Integration
- **OpenRouter API**: KI-Integration mit Multi-Model-Support
- **OpenAI**: v4.43.0 - KI-Integration und Embedding-Generierung
- **Axios**: v1.10.0 - HTTP-Client
- **Zod**: v3.25.76 - Schema-Validierung

#### AI und Machine Learning
- **Text Processing**: PDF-Text-Extraktion und Chunking
- **Vector Embeddings**: Semantische Suche und Ähnlichkeitsberechnung
- **RAG Pipeline**: Retrieval-Augmented Generation System
- **Multi-Model AI**: GPT-4, Claude, Llama Integration
- **Predictive Analytics**: Zeitreihenanalyse und Forecasting

#### Testing und Entwicklung
- **Jest**: v29.7.0 - Testing-Framework
- **Supertest**: v7.0.0 - HTTP-Testing
- **Nodemon**: v3.0.1 - Entwicklungsserver mit Auto-Reload
- **ts-node**: v10.9.1 - TypeScript-Ausführung

### Performance-Optimierungen

#### Frontend-Optimierungen
- **Code-Splitting**: Lazy-Loading für Module und Komponenten
- **React.memo**: Memoization für teure Komponenten
- **TanStack Query**: Intelligentes Caching und Background-Updates
- **Virtualisierung**: Für große Datentabellen (>100 Zeilen)
- **Error Boundaries**: Robuste Fehlerbehandlung ohne App-Crash

#### Backend-Optimierungen
- **Connection Pooling**: Effiziente Datenbankverbindungen
- **Query-Optimierung**: Indizierte Datenbankabfragen
- **Rate-Limiting**: Schutz vor API-Überlastung
- **Caching**: In-Memory-Caching für häufige Abfragen
- **Compression**: Gzip-Komprimierung für API-Responses

#### Datenbank-Optimierungen
- **Strategische Indizierung**: Optimiert für häufige Abfragen
- **Batch-Operations**: Effiziente Bulk-Datenoperationen
- **Query-Analyse**: Prisma-Query-Optimierung
- **Connection-Management**: Effiziente Verbindungsnutzung

### Sicherheitsmaßnahmen

#### Authentifizierung und Autorisierung
- **JWT-basierte Authentifizierung**: Sichere Token-Verwaltung
- **Rollenbasierte Zugriffskontrolle**: Hierarchische Berechtigungen
- **Session-Management**: Automatische Token-Erneuerung
- **Passwort-Sicherheit**: bcrypt mit 12 Salt-Runden

#### API-Sicherheit
- **Input-Validierung**: Zod-Schema-Validierung
- **SQL-Injection-Schutz**: Prisma ORM verhindert SQL-Injection
- **XSS-Schutz**: Content Security Policy
- **Rate-Limiting**: Schutz vor Brute-Force-Angriffen

#### Daten-Sicherheit
- **Verschlüsselung**: Sensible Daten verschlüsselt gespeichert
- **Audit-Logging**: Vollständige Aktivitätsprotokolle
- **Backup-Strategien**: Regelmäßige Datenbank-Backups
- **Access-Control**: Minimale Berechtigungen nach Rolle

### Monitoring und Logging

#### Performance-Monitoring
- **Response-Time-Tracking**: Detaillierte API-Performance-Metriken
- **Memory-Usage-Monitoring**: Speicherverbrauch-Überwachung
- **Error-Rate-Tracking**: Fehlerrate-Monitoring
- **Cache-Hit-Rate**: Cache-Effizienz-Metriken

#### Logging-System
- **Strukturiertes Logging**: JSON-basierte Log-Ausgabe
- **Log-Level**: Debug, Info, Warning, Error, Critical
- **Request-Logging**: Vollständige HTTP-Request-Protokollierung
- **Security-Event-Logging**: Sicherheitsrelevante Ereignisse

#### Alerting-System
- **Performance-Alerts**: Automatische Warnungen bei Schwellenwerten
- **Error-Alerts**: Sofortige Benachrichtigung bei kritischen Fehlern
- **System-Health-Checks**: Regelmäßige Systemstatus-Prüfungen
- **Uptime-Monitoring**: Verfügbarkeits-Überwachung

### Systemanforderungen

#### Minimale Systemanforderungen
- **Betriebssystem**: Windows 10 (64-bit) oder höher
- **RAM**: 4 GB (8 GB empfohlen)
- **Festplattenspeicher**: 2 GB freier Speicherplatz
- **Prozessor**: Intel Core i3 oder AMD-Äquivalent
- **Netzwerk**: Internetverbindung für Updates und KI-Features

#### Empfohlene Systemanforderungen
- **Betriebssystem**: Windows 11 (64-bit)
- **RAM**: 16 GB oder mehr
- **Festplattenspeicher**: 10 GB freier Speicherplatz (SSD empfohlen)
- **Prozessor**: Intel Core i5 oder AMD Ryzen 5 oder höher
- **Grafikkarte**: Integrierte Grafik ausreichend
- **Monitor**: 1920x1080 oder höhere Auflösung

#### Netzwerkanforderungen
- **Interne API-Kommunikation**: Port 3001 (Backend)
- **Electron-Frontend**: Port 3000 (Development)
- **Datenbankzugriff**: Lokale SQLite-Datei
- **Externe APIs**: HTTPS-Verbindungen für KI-Features
