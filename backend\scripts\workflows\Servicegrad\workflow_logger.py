#!/usr/bin/env python3
"""
Workflow Logger für SAP-Automatisierung
Strukturiertes Logging-System mit SQLite-Persistierung
"""

import json
import sqlite3
import uuid
import os
from datetime import datetime
from typing import Literal, Optional, Dict, Any
from pathlib import Path

LogLevel = Literal['info', 'warning', 'error', 'debug']

class WorkflowLogger:
    """
    Strukturiertes Logging-System für SAP Workflow-Prozesse
    Speichert Logs sowohl in SQLite als auch in JSON-Format für Frontend-Zugriff
    """
    
    def __init__(self, workflow_id: str, db_path: Optional[str] = None):
        self.workflow_id = workflow_id
        
        # Automatische DB-Pfad-Erkennung
        if db_path is None:
            # Pfad relativ zum Backend-Verzeichnis
            backend_dir = Path(__file__).parent.parent.parent
            self.db_path = str(backend_dir / "database" / "sfm_dashboard.db")
        else:
            self.db_path = db_path
            
        self.execution_id = str(uuid.uuid4())
        self.start_time = datetime.now()
        
        # <PERSON><PERSON> sicher, dass das Database-Verzeichnis existiert
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # Erstelle Logs-Tabelle falls nicht vorhanden
        self._init_logs_table()
        
    def _init_logs_table(self):
        """Erstellt die workflow_logs Tabelle falls sie nicht existiert"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS workflow_logs (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    level TEXT NOT NULL,
                    message TEXT NOT NULL,
                    workflow_id TEXT NOT NULL,
                    execution_id TEXT,
                    details TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Index für bessere Performance
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_workflow_logs_workflow_id 
                ON workflow_logs(workflow_id)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_workflow_logs_timestamp 
                ON workflow_logs(timestamp)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_workflow_logs_level 
                ON workflow_logs(level)
            ''')
            
            conn.commit()
            conn.close()
            
            print(f"[WorkflowLogger] Database initialized: {self.db_path}")
            
        except Exception as e:
            print(f"[WorkflowLogger] Fehler beim Initialisieren der Logs-Tabelle: {e}")
    
    def log(self, level: LogLevel, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Schreibt einen Log-Eintrag in die Datenbank
        
        Args:
            level: Log-Level (info, warning, error, debug)
            message: Log-Nachricht
            details: Zusätzliche Details als Dictionary
        """
        log_entry = {
            'id': str(uuid.uuid4()),
            'timestamp': datetime.now().isoformat(),
            'level': level,
            'message': message,
            'workflow_id': self.workflow_id,
            'execution_id': self.execution_id,
            'details': json.dumps(details) if details else None
        }
        
        try:
            # In Datenbank speichern
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO workflow_logs 
                (id, timestamp, level, message, workflow_id, execution_id, details)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                log_entry['id'],
                log_entry['timestamp'],
                log_entry['level'],
                log_entry['message'],
                log_entry['workflow_id'],
                log_entry['execution_id'],
                log_entry['details']
            ))
            
            conn.commit()
            conn.close()
            
            # Auch in Konsole ausgeben für Debugging
            timestamp_str = datetime.now().strftime("%H:%M:%S")
            level_str = level.upper().ljust(7)
            print(f"[{timestamp_str}] {level_str} [{self.workflow_id}] {message}")
            
        except Exception as e:
            print(f"[WorkflowLogger] Fehler beim Schreiben des Log-Eintrags: {e}")
            # Fallback: Nur Konsolen-Output
            timestamp_str = datetime.now().strftime("%H:%M:%S")
            level_str = level.upper().ljust(7)
            print(f"[{timestamp_str}] {level_str} [{self.workflow_id}] {message}")
    
    def info(self, message: str, details: Optional[Dict[str, Any]] = None):
        """Log Info-Nachricht"""
        self.log('info', message, details)
    
    def warning(self, message: str, details: Optional[Dict[str, Any]] = None):
        """Log Warning-Nachricht"""
        self.log('warning', message, details)
    
    def error(self, message: str, details: Optional[Dict[str, Any]] = None):
        """Log Error-Nachricht"""
        self.log('error', message, details)
    
    def debug(self, message: str, details: Optional[Dict[str, Any]] = None):
        """Log Debug-Nachricht"""
        self.log('debug', message, details)
    
    def log_process_start(self, process_name: str):
        """Loggt den Start eines Prozesses"""
        self.info(f"Starte Prozess: {process_name}", {
            'process_name': process_name,
            'execution_id': self.execution_id,
            'start_time': self.start_time.isoformat(),
            'event_type': 'process_start'
        })
    
    def log_process_complete(self, process_name: str, export_path: Optional[str] = None):
        """Loggt den erfolgreichen Abschluss eines Prozesses"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        self.info(f"Prozess abgeschlossen: {process_name}", {
            'process_name': process_name,
            'execution_id': self.execution_id,
            'duration_seconds': duration,
            'export_path': export_path,
            'end_time': end_time.isoformat(),
            'event_type': 'process_complete'
        })
    
    def log_process_error(self, process_name: str, error_message: str):
        """Loggt einen Prozess-Fehler"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        self.error(f"Prozess fehlgeschlagen: {process_name} - {error_message}", {
            'process_name': process_name,
            'execution_id': self.execution_id,
            'duration_seconds': duration,
            'error_message': error_message,
            'end_time': end_time.isoformat(),
            'event_type': 'process_error'
        })
    
    def log_sap_action(self, action: str, element_id: Optional[str] = None, success: bool = True):
        """Loggt SAP GUI-Aktionen"""
        level = 'info' if success else 'warning'
        message = f"SAP Aktion: {action}"
        if element_id:
            message += f" (Element: {element_id})"
        
        self.log(level, message, {
            'action': action,
            'element_id': element_id,
            'success': success,
            'event_type': 'sap_action'
        })
    
    def log_file_operation(self, operation: str, file_path: str, success: bool = True):
        """Loggt Datei-Operationen"""
        level = 'info' if success else 'error'
        message = f"Datei {operation}: {file_path}"
        
        self.log(level, message, {
            'operation': operation,
            'file_path': file_path,
            'success': success,
            'event_type': 'file_operation'
        })
    
    def log_database_operation(self, operation: str, table_name: str, record_count: Optional[int] = None, success: bool = True):
        """Loggt Datenbank-Operationen"""
        level = 'info' if success else 'error'
        message = f"Datenbank {operation}: {table_name}"
        if record_count is not None:
            message += f" ({record_count} Datensätze)"
        
        self.log(level, message, {
            'operation': operation,
            'table_name': table_name,
            'record_count': record_count,
            'success': success,
            'event_type': 'database_operation'
        })

    @classmethod
    def get_logs_for_workflow(cls, workflow_id: str, db_path: Optional[str] = None, limit: int = 100):
        """
        Lädt Logs für einen bestimmten Workflow aus der Datenbank
        """
        if db_path is None:
            backend_dir = Path(__file__).parent.parent.parent
            db_path = str(backend_dir / "database" / "sfm_dashboard.db")
            
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, timestamp, level, message, workflow_id, execution_id, details
                FROM workflow_logs
                WHERE workflow_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (workflow_id, limit))
            
            rows = cursor.fetchall()
            conn.close()
            
            logs = []
            for row in rows:
                log_entry = {
                    'id': row[0],
                    'timestamp': row[1],
                    'level': row[2],
                    'message': row[3],
                    'workflow_id': row[4],
                    'execution_id': row[5],
                    'details': json.loads(row[6]) if row[6] else None
                }
                logs.append(log_entry)
            
            return logs
            
        except Exception as e:
            print(f"[WorkflowLogger] Fehler beim Laden der Logs für Workflow {workflow_id}: {e}")
            return []
    
    @classmethod
    def get_all_logs(cls, db_path: Optional[str] = None, limit: int = 200):
        """
        Lädt alle Workflow-Logs aus der Datenbank
        """
        if db_path is None:
            backend_dir = Path(__file__).parent.parent.parent
            db_path = str(backend_dir / "database" / "sfm_dashboard.db")
            
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, timestamp, level, message, workflow_id, execution_id, details
                FROM workflow_logs
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (limit,))
            
            rows = cursor.fetchall()
            conn.close()
            
            logs = []
            for row in rows:
                log_entry = {
                    'id': row[0],
                    'timestamp': row[1],
                    'level': row[2],
                    'message': row[3],
                    'workflow_id': row[4],
                    'execution_id': row[5],
                    'details': json.loads(row[6]) if row[6] else None
                }
                logs.append(log_entry)
            
            return logs
            
        except Exception as e:
            print(f"[WorkflowLogger] Fehler beim Laden aller Logs: {e}")
            return []