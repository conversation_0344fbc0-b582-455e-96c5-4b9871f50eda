import React, { type ReactNode } from "react";
import { BaseNavigation } from "@/components/navigation";
import { stoerungenNavigationConfig } from "@/config/navigation";



interface StoerungNavigationProps {
    title?: ReactNode;
}

/**
 * Stoerungen Navigation Component
 * 
 * Spezifische Navigation für das Stoerungen-Modul mit:
 * - Logo und Titel
 * - Stoerungen-spezifische Navigation
 * - Health Indicator
 * - User Menu mit Settings-Link
 */
export default function StoerungNavigation({ title }: StoerungNavigationProps) {
    return (
        <BaseNavigation
            title={title}
            navigationConfig={stoerungenNavigationConfig}
            showUserMenu={true}
        />
    );
}