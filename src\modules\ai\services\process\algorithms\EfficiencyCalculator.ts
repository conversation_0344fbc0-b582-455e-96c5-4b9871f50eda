/**
 * Process Efficiency Calculator
 * Calculates various efficiency metrics for business processes
 */

import {
  ProcessData,
  ProcessHistoricalData,
  EfficiencyMetrics,
  EfficiencyTrend,
  EfficiencyBenchmark,
  ProcessOptimizationError
} from '../types';

export class EfficiencyCalculator {
  /**
   * Calculate comprehensive efficiency metrics for a process
   */
  public static calculateEfficiencyMetrics(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): EfficiencyMetrics {
    try {
      // Validate input data
      if (!processData.steps || processData.steps.length === 0) {
        throw new ProcessOptimizationError(
          'Process data must contain at least one step',
          'INVALID_PROCESS_DATA',
          processData.processId
        );
      }

      if (!processData.resources || processData.resources.length === 0) {
        throw new ProcessOptimizationError(
          'Process data must contain at least one resource',
          'INVALID_PROCESS_DATA',
          processData.processId
        );
      }

      return {
        overall: this.calculateOverallEfficiency(processData, historicalData),
        byStep: this.calculateStepEfficiencies(processData, historicalData),
        byResource: this.calculateResourceEfficiencies(processData, historicalData),
        trends: this.calculateEfficiencyTrends(historicalData),
        benchmarks: this.calculateEfficiencyBenchmarks(processData, historicalData)
      };
    } catch (error) {
      throw new ProcessOptimizationError(
        `Efficiency calculation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'EFFICIENCY_CALCULATION_FAILED',
        processData.processId
      );
    }
  }

  /**
   * Calculate overall process efficiency
   */
  private static calculateOverallEfficiency(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): number {
    if (historicalData.length === 0) {
      return processData.metrics.efficiency;
    }

    // Calculate efficiency based on multiple factors
    const throughputEfficiency = this.calculateThroughputEfficiency(processData, historicalData);
    const cycleTimeEfficiency = this.calculateCycleTimeEfficiency(processData, historicalData);
    const resourceEfficiency = this.calculateResourceUtilizationEfficiency(processData, historicalData);
    const qualityEfficiency = this.calculateQualityEfficiency(processData, historicalData);

    // Weighted average of different efficiency components
    const weights = {
      throughput: 0.3,
      cycleTime: 0.25,
      resource: 0.25,
      quality: 0.2
    };

    return (
      throughputEfficiency * weights.throughput +
      cycleTimeEfficiency * weights.cycleTime +
      resourceEfficiency * weights.resource +
      qualityEfficiency * weights.quality
    );
  }

  /**
   * Calculate efficiency for each process step
   */
  private static calculateStepEfficiencies(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): Record<string, number> {
    const stepEfficiencies: Record<string, number> = {};

    for (const step of processData.steps) {
      stepEfficiencies[step.stepId] = this.calculateStepEfficiency(step, historicalData);
    }

    return stepEfficiencies;
  }

  /**
   * Calculate efficiency for each resource
   */
  private static calculateResourceEfficiencies(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): Record<string, number> {
    const resourceEfficiencies: Record<string, number> = {};

    for (const resource of processData.resources) {
      resourceEfficiencies[resource.resourceId] = this.calculateResourceEfficiency(resource, historicalData);
    }

    return resourceEfficiencies;
  }

  /**
   * Calculate efficiency trends over time
   */
  private static calculateEfficiencyTrends(historicalData: ProcessHistoricalData[]): EfficiencyTrend[] {
    if (historicalData.length < 5) {
      return []; // Need sufficient data for trend analysis
    }

    const trends: EfficiencyTrend[] = [];

    // Sort historical data by timestamp
    const sortedData = [...historicalData].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    // Calculate trends for different metrics
    trends.push(this.calculateMetricTrend('throughput', sortedData, data => data.metrics.throughput));
    trends.push(this.calculateMetricTrend('cycleTime', sortedData, data => data.metrics.cycleTime));
    trends.push(this.calculateMetricTrend('efficiency', sortedData, data => data.metrics.efficiency));
    trends.push(this.calculateMetricTrend('utilization', sortedData, data => data.metrics.utilization));
    trends.push(this.calculateMetricTrend('qualityRate', sortedData, data => data.metrics.qualityRate));

    return trends;
  }

  /**
   * Calculate efficiency benchmarks
   */
  private static calculateEfficiencyBenchmarks(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): EfficiencyBenchmark[] {
    const benchmarks: EfficiencyBenchmark[] = [];

    // Current metrics
    const currentMetrics = historicalData.length > 0 
      ? historicalData[historicalData.length - 1].metrics 
      : processData.metrics;

    // Industry benchmarks (these would typically come from external data)
    const industryBenchmarks = this.getIndustryBenchmarks(processData);

    // Create benchmarks for key metrics
    benchmarks.push({
      metric: 'throughput',
      currentValue: currentMetrics.throughput,
      industryAverage: industryBenchmarks.throughput.average,
      bestPractice: industryBenchmarks.throughput.bestPractice,
      gap: this.calculateGap(currentMetrics.throughput, industryBenchmarks.throughput.bestPractice)
    });

    benchmarks.push({
      metric: 'cycleTime',
      currentValue: currentMetrics.cycleTime,
      industryAverage: industryBenchmarks.cycleTime.average,
      bestPractice: industryBenchmarks.cycleTime.bestPractice,
      gap: this.calculateGap(currentMetrics.cycleTime, industryBenchmarks.cycleTime.bestPractice, true)
    });

    benchmarks.push({
      metric: 'efficiency',
      currentValue: currentMetrics.efficiency,
      industryAverage: industryBenchmarks.efficiency.average,
      bestPractice: industryBenchmarks.efficiency.bestPractice,
      gap: this.calculateGap(currentMetrics.efficiency, industryBenchmarks.efficiency.bestPractice)
    });

    benchmarks.push({
      metric: 'qualityRate',
      currentValue: currentMetrics.qualityRate,
      industryAverage: industryBenchmarks.qualityRate.average,
      bestPractice: industryBenchmarks.qualityRate.bestPractice,
      gap: this.calculateGap(currentMetrics.qualityRate, industryBenchmarks.qualityRate.bestPractice)
    });

    return benchmarks;
  }

  /**
   * Calculate throughput efficiency
   */
  private static calculateThroughputEfficiency(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): number {
    if (historicalData.length === 0) return processData.metrics.efficiency;

    const recentData = historicalData.slice(-10); // Last 10 data points
    const avgThroughput = recentData.reduce((sum, data) => sum + data.metrics.throughput, 0) / recentData.length;
    const theoreticalMaxThroughput = this.calculateTheoreticalMaxThroughput(processData);

    return Math.min(1.0, avgThroughput / theoreticalMaxThroughput);
  }

  /**
   * Calculate cycle time efficiency
   */
  private static calculateCycleTimeEfficiency(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): number {
    if (historicalData.length === 0) return processData.metrics.efficiency;

    const recentData = historicalData.slice(-10);
    const avgCycleTime = recentData.reduce((sum, data) => sum + data.metrics.cycleTime, 0) / recentData.length;
    const theoreticalMinCycleTime = this.calculateTheoreticalMinCycleTime(processData);

    return Math.min(1.0, theoreticalMinCycleTime / avgCycleTime);
  }

  /**
   * Calculate resource utilization efficiency
   */
  private static calculateResourceUtilizationEfficiency(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): number {
    if (historicalData.length === 0) return processData.metrics.utilization;

    const recentData = historicalData.slice(-10);
    let totalUtilization = 0;
    let resourceCount = 0;

    for (const resource of processData.resources) {
      const avgUtilization = recentData.reduce((sum, data) => {
        return sum + (data.resourceUtilization[resource.resourceId] || 0);
      }, 0) / recentData.length;

      // Optimal utilization is around 80-85% to avoid bottlenecks
      const optimalUtilization = 0.82;
      const efficiency = avgUtilization <= optimalUtilization 
        ? avgUtilization / optimalUtilization
        : Math.max(0.5, 1 - (avgUtilization - optimalUtilization) * 2);

      totalUtilization += efficiency;
      resourceCount++;
    }

    return resourceCount > 0 ? totalUtilization / resourceCount : 0;
  }

  /**
   * Calculate quality efficiency
   */
  private static calculateQualityEfficiency(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): number {
    if (historicalData.length === 0) return processData.metrics.qualityRate;

    const recentData = historicalData.slice(-10);
    const avgQualityRate = recentData.reduce((sum, data) => sum + data.metrics.qualityRate, 0) / recentData.length;

    return avgQualityRate;
  }

  /**
   * Calculate efficiency for a specific step
   */
  private static calculateStepEfficiency(
    step: any,
    historicalData: ProcessHistoricalData[]
  ): number {
    if (historicalData.length === 0) {
      // Base efficiency on utilization with penalty for over-utilization
      if (step.currentUtilization <= 0.85) {
        return step.currentUtilization / 0.85;
      } else {
        return Math.max(0.5, 1 - (step.currentUtilization - 0.85) * 2);
      }
    }

    // Calculate step efficiency based on utilization and performance
    const stepEvents = historicalData.flatMap(data => 
      data.events.filter(event => event.stepId === step.stepId)
    );

    if (stepEvents.length === 0) {
      // Fallback to utilization-based calculation
      if (step.currentUtilization <= 0.85) {
        return step.currentUtilization / 0.85;
      } else {
        return Math.max(0.5, 1 - (step.currentUtilization - 0.85) * 2);
      }
    }

    // Calculate average processing time vs. standard time
    const processingTimes = stepEvents
      .filter(event => event.type === 'complete' && event.duration)
      .map(event => event.duration!);

    let timeEfficiency = 1.0;
    if (processingTimes.length > 0) {
      const avgProcessingTime = processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;
      const standardTime = step.duration;
      // Efficiency is inverse of time ratio (less time = more efficient)
      timeEfficiency = Math.min(1.0, standardTime / avgProcessingTime);
    }

    // Combine with utilization (but penalize over-utilization)
    const utilizationEfficiency = step.currentUtilization <= 0.85 
      ? step.currentUtilization / 0.85
      : Math.max(0.5, 1 - (step.currentUtilization - 0.85) * 2);

    return (timeEfficiency + utilizationEfficiency) / 2;
  }

  /**
   * Calculate efficiency for a specific resource
   */
  private static calculateResourceEfficiency(
    resource: any,
    historicalData: ProcessHistoricalData[]
  ): number {
    let utilization: number;
    
    if (historicalData.length === 0) {
      utilization = resource.currentLoad;
    } else {
      const recentData = historicalData.slice(-10);
      utilization = recentData.reduce((sum, data) => {
        return sum + (data.resourceUtilization[resource.resourceId] || resource.currentLoad);
      }, 0) / recentData.length;
    }

    // Optimal utilization for efficiency
    const optimalUtilization = 0.80;
    
    if (utilization <= optimalUtilization) {
      return utilization / optimalUtilization;
    } else {
      // Penalize over-utilization as it can lead to bottlenecks
      return Math.max(0.3, 1 - (utilization - optimalUtilization) * 1.5);
    }
  }

  /**
   * Calculate trend for a specific metric
   */
  private static calculateMetricTrend(
    metricName: string,
    sortedData: ProcessHistoricalData[],
    valueExtractor: (data: ProcessHistoricalData) => number
  ): EfficiencyTrend {
    const values = sortedData.map(data => ({
      timestamp: data.timestamp,
      value: valueExtractor(data)
    }));

    // Calculate linear regression to determine trend
    const n = values.length;
    if (n < 2) {
      return {
        metric: metricName,
        values,
        trend: 'stable',
        changeRate: 0
      };
    }

    const sumX = values.reduce((sum, _, index) => sum + index, 0);
    const sumY = values.reduce((sum, point) => sum + point.value, 0);
    const sumXY = values.reduce((sum, point, index) => sum + index * point.value, 0);
    const sumXX = values.reduce((sum, _, index) => sum + index * index, 0);

    const denominator = n * sumXX - sumX * sumX;
    const slope = denominator !== 0 ? (n * sumXY - sumX * sumY) / denominator : 0;
    const avgValue = sumY / n;

    // Determine trend direction and change rate
    let trendDirection: 'improving' | 'declining' | 'stable';
    const changeRate = avgValue !== 0 ? Math.abs((slope / avgValue) * 100) : 0; // Percentage change per period

    if (changeRate < 2) {
      trendDirection = 'stable';
    } else if (slope > 0) {
      trendDirection = metricName === 'cycleTime' ? 'declining' : 'improving';
    } else {
      trendDirection = metricName === 'cycleTime' ? 'improving' : 'declining';
    }

    return {
      metric: metricName,
      values,
      trend: trendDirection,
      changeRate
    };
  }

  /**
   * Get industry benchmarks (mock data - would come from external sources)
   */
  private static getIndustryBenchmarks(processData: ProcessData): any {
    // Mock industry benchmarks - in real implementation, this would come from external data
    const baseThroughput = Math.max(processData.metrics.throughput, 1);
    const baseCycleTime = Math.max(processData.metrics.cycleTime, 10);
    
    return {
      throughput: {
        average: baseThroughput * 1.1,
        bestPractice: baseThroughput * 1.3
      },
      cycleTime: {
        average: baseCycleTime * 0.95,
        bestPractice: baseCycleTime * 0.8
      },
      efficiency: {
        average: Math.max(0.75, processData.metrics.efficiency * 1.1),
        bestPractice: Math.max(0.85, processData.metrics.efficiency * 1.2)
      },
      qualityRate: {
        average: Math.max(0.90, processData.metrics.qualityRate * 1.02),
        bestPractice: Math.max(0.95, processData.metrics.qualityRate * 1.05)
      }
    };
  }

  /**
   * Calculate gap between current and benchmark values
   */
  private static calculateGap(current: number, benchmark: number, lowerIsBetter: boolean = false): number {
    if (lowerIsBetter) {
      return ((current - benchmark) / benchmark) * 100;
    } else {
      return ((benchmark - current) / benchmark) * 100;
    }
  }

  /**
   * Calculate theoretical maximum throughput
   */
  private static calculateTheoreticalMaxThroughput(processData: ProcessData): number {
    // Find the bottleneck step (longest duration)
    const bottleneckDuration = Math.max(...processData.steps.map(step => step.duration));
    return 60 / bottleneckDuration; // items per hour
  }

  /**
   * Calculate theoretical minimum cycle time
   */
  private static calculateTheoreticalMinCycleTime(processData: ProcessData): number {
    // Sum of all step durations (assuming no waiting time)
    return processData.steps.reduce((sum, step) => sum + step.duration, 0);
  }
}