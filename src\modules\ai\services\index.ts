/**
 * AI Module Services Index
 * 
 * Exports all AI services following the department organization pattern
 */

// Core AI Services
export { AIBaseService } from './base/AIBaseService';
export { VectorDatabaseService } from './vector/VectorDatabaseService';
export { EmbeddingService } from './embedding/EmbeddingService';
export { RAGService } from './rag/RAGService';

// Department-specific AI Services
export { CuttingOptimizerService } from './cutting/CuttingOptimizerService';
export { InventoryIntelligenceService } from './inventory/InventoryIntelligenceService';
export { ProcessOptimizerService } from './process/ProcessOptimizerService';
export { WarehouseOptimizerService } from './warehouse/WarehouseOptimizerService';
export { SupplyChainOptimizerService } from './supply-chain/SupplyChainOptimizerService';
export { ReportingService, reportingService } from './reporting/ReportingService';

// Service Orchestration
export { AIServiceCoordinator, aiServiceCoordinator } from './coordinator/AIServiceCoordinator';

// Types
export * from './types';