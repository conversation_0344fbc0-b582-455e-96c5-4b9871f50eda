/**
 * AI Error Handler Hook Tests
 * Tests for React hook error handling functionality
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useAIErrorHandler, useAIError } from '../useAIErrorHandler';
import { AIErrorHandler } from '../../services/error-handling/AIErrorHandler';
import { AIOperationLogger } from '../../services/error-handling/AIOperationLogger';
import { AIServiceErrorCode, AIServiceErrorSeverity } from '../../types/errors';

// Mock the error handler and logger
vi.mock('../../services/error-handling/AIErrorHandler');
vi.mock('../../services/error-handling/AIOperationLogger');

describe('useAIErrorHandler', () => {
  let mockErrorHandler: any;
  let mockLogger: any;

  beforeEach(() => {
    mockErrorHandler = {
      createError: vi.fn(),
      handleError: vi.fn(),
      formatUserError: vi.fn(),
      formatTechnicalError: vi.fn()
    };

    mockLogger = {
      startOperation: vi.fn().mockReturnValue('op123'),
      completeOperation: vi.fn()
    };

    vi.mocked(AIErrorHandler.getInstance).mockReturnValue(mockErrorHandler);
    vi.mocked(AIOperationLogger.getInstance).mockReturnValue(mockLogger);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should initialize with no error state', () => {
      const { result } = renderHook(() => 
        useAIErrorHandler({ serviceName: 'TestService' })
      );

      expect(result.current.error).toBeNull();
      expect(result.current.userMessage).toBeNull();
      expect(result.current.isRetrying).toBe(false);
      expect(result.current.retryCount).toBe(0);
      expect(result.current.hasRecovered).toBe(false);
      expect(result.current.hasError).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle errors and update state', async () => {
      const mockAIError = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'TestService',
        operation: 'testOp',
        recoverable: true,
        userMessage: 'User friendly error',
        technicalMessage: 'Technical error'
      };

      mockErrorHandler.createError.mockReturnValue(mockAIError);
      mockErrorHandler.handleError.mockResolvedValue(null);

      const { result } = renderHook(() => 
        useAIErrorHandler({ serviceName: 'TestService' })
      );

      await act(async () => {
        await result.current.handleError(
          new Error('Test error'),
          'testOperation',
          { input: 'data' }
        );
      });

      expect(result.current.error).toBe(mockAIError);
      expect(result.current.hasError).toBe(true);
      expect(result.current.isRecoverable).toBe(true);
      expect(mockErrorHandler.createError).toHaveBeenCalledWith(
        AIServiceErrorCode.SERVICE_UNAVAILABLE,
        'TestService',
        'testOperation',
        expect.any(Error),
        { input: 'data' }
      );
    });

    it('should handle successful recovery', async () => {
      const mockAIError = {
        code: AIServiceErrorCode.API_TIMEOUT,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'TestService',
        operation: 'testOp',
        recoverable: true
      };

      const mockOnRecovery = vi.fn();
      
      mockErrorHandler.createError.mockReturnValue(mockAIError);
      mockErrorHandler.handleError.mockResolvedValue('recovered result');

      const { result } = renderHook(() => 
        useAIErrorHandler({ 
          serviceName: 'TestService',
          enableRetry: true,
          onRecovery: mockOnRecovery
        })
      );

      const recoveredResult = await act(async () => {
        return await result.current.handleError(
          new Error('Test error'),
          'testOperation'
        );
      });

      expect(recoveredResult).toBe('recovered result');
      expect(result.current.hasRecovered).toBe(true);
      expect(result.current.error).toBeNull();
      expect(mockOnRecovery).toHaveBeenCalledWith(mockAIError, 'recovered result');
    });

    it('should call custom error handler', async () => {
      const mockOnError = vi.fn();
      const mockAIError = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'TestService',
        operation: 'testOp'
      };

      mockErrorHandler.createError.mockReturnValue(mockAIError);
      mockErrorHandler.handleError.mockResolvedValue(null);

      const { result } = renderHook(() => 
        useAIErrorHandler({ 
          serviceName: 'TestService',
          onError: mockOnError
        })
      );

      await act(async () => {
        await result.current.handleError(new Error('Test error'), 'testOperation');
      });

      expect(mockOnError).toHaveBeenCalledWith(mockAIError);
    });
  });

  describe('Operation Execution', () => {
    it('should execute operation successfully', async () => {
      const { result } = renderHook(() => 
        useAIErrorHandler({ serviceName: 'TestService' })
      );

      const mockOperation = vi.fn().mockResolvedValue('success result');

      const operationResult = await act(async () => {
        return await result.current.executeWithErrorHandling(
          mockOperation,
          'testOperation',
          'query',
          { context: 'data' }
        );
      });

      expect(operationResult).toBe('success result');
      expect(mockLogger.startOperation).toHaveBeenCalledWith(
        'TestService',
        'testOperation',
        'query',
        { context: 'data' }
      );
      expect(mockLogger.completeOperation).toHaveBeenCalledWith(
        'op123',
        true,
        { result: 'success result' }
      );
    });

    it('should handle operation failure with recovery', async () => {
      const mockAIError = {
        code: AIServiceErrorCode.API_TIMEOUT,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'TestService',
        operation: 'testOp'
      };

      mockErrorHandler.createError.mockReturnValue(mockAIError);
      mockErrorHandler.handleError.mockResolvedValue('recovered result');

      const { result } = renderHook(() => 
        useAIErrorHandler({ serviceName: 'TestService' })
      );

      const mockOperation = vi.fn().mockRejectedValue(new Error('Operation failed'));

      const operationResult = await act(async () => {
        return await result.current.executeWithErrorHandling(
          mockOperation,
          'testOperation'
        );
      });

      expect(operationResult).toBe('recovered result');
      expect(mockLogger.completeOperation).toHaveBeenCalledWith(
        'op123',
        true,
        { result: 'recovered result' },
        mockAIError
      );
    });

    it('should clear error state before new operation', async () => {
      const { result } = renderHook(() => 
        useAIErrorHandler({ serviceName: 'TestService' })
      );

      // First, set an error state
      const mockAIError = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'TestService',
        operation: 'testOp'
      };

      mockErrorHandler.createError.mockReturnValue(mockAIError);
      mockErrorHandler.handleError.mockResolvedValue(null);

      await act(async () => {
        await result.current.handleError(new Error('Test error'), 'testOperation');
      });

      expect(result.current.hasError).toBe(true);

      // Now execute a successful operation
      const mockOperation = vi.fn().mockResolvedValue('success');

      await act(async () => {
        await result.current.executeWithErrorHandling(mockOperation, 'newOperation');
      });

      expect(result.current.hasError).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('Retry Operations', () => {
    it('should retry failed operation', async () => {
      const { result } = renderHook(() => 
        useAIErrorHandler({ serviceName: 'TestService', maxRetries: 3 })
      );

      // Set up initial error state
      const mockAIError = {
        code: AIServiceErrorCode.API_TIMEOUT,
        severity: AIServiceErrorSeverity.MEDIUM,
        service: 'TestService',
        operation: 'testOp',
        recoverable: true
      };

      mockErrorHandler.createError.mockReturnValue(mockAIError);
      mockErrorHandler.handleError.mockResolvedValue(null);

      await act(async () => {
        await result.current.handleError(new Error('Test error'), 'testOperation');
      });

      expect(result.current.retryCount).toBe(0);
      expect(result.current.canRetry).toBe(true);

      // Now retry
      const mockRetryOperation = vi.fn().mockResolvedValue('retry success');

      const retryResult = await act(async () => {
        return await result.current.retryOperation(mockRetryOperation, 'retryOperation');
      });

      expect(retryResult).toBe('retry success');
      expect(result.current.hasRecovered).toBe(true);
      expect(result.current.error).toBeNull();
    });

    it('should not retry beyond max retries', async () => {
      const { result } = renderHook(() => 
        useAIErrorHandler({ serviceName: 'TestService', maxRetries: 2 })
      );

      // Set up error state with max retries reached
      await act(async () => {
        result.current.clearError();
        // Manually set retry count for testing
        (result.current as any).retryCount = 2;
      });

      const mockRetryOperation = vi.fn();

      const retryResult = await act(async () => {
        return await result.current.retryOperation(mockRetryOperation, 'retryOperation');
      });

      expect(retryResult).toBeNull();
      expect(mockRetryOperation).not.toHaveBeenCalled();
    });
  });

  describe('Utility Functions', () => {
    it('should create error from code', () => {
      const mockAIError = {
        code: AIServiceErrorCode.INVALID_PARAMETERS,
        service: 'TestService'
      };

      mockErrorHandler.createError.mockReturnValue(mockAIError);

      const { result } = renderHook(() => 
        useAIErrorHandler({ serviceName: 'TestService' })
      );

      const createdError = result.current.createErrorFromCode(
        AIServiceErrorCode.INVALID_PARAMETERS,
        'testOperation',
        new Error('Original'),
        { context: 'data' }
      );

      expect(createdError).toBe(mockAIError);
      expect(mockErrorHandler.createError).toHaveBeenCalledWith(
        AIServiceErrorCode.INVALID_PARAMETERS,
        'TestService',
        'testOperation',
        expect.any(Error),
        { context: 'data' }
      );
    });

    it('should format user errors', () => {
      mockErrorHandler.formatUserError.mockReturnValue('Formatted user error');

      const { result } = renderHook(() => 
        useAIErrorHandler({ serviceName: 'TestService' })
      );

      const formatted = result.current.formatUserError(new Error('Test'), 'Context');
      
      expect(formatted).toBe('Formatted user error');
      expect(mockErrorHandler.formatUserError).toHaveBeenCalledWith(
        expect.any(Error),
        'Context'
      );
    });

    it('should clear error state', () => {
      const { result } = renderHook(() => 
        useAIErrorHandler({ serviceName: 'TestService' })
      );

      // Set some error state first
      act(() => {
        (result.current as any).error = { code: 'TEST_ERROR' };
        (result.current as any).hasError = true;
      });

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
      expect(result.current.hasError).toBe(false);
      expect(result.current.retryCount).toBe(0);
      expect(result.current.hasRecovered).toBe(false);
    });
  });

  describe('Simplified Hooks', () => {
    it('should use default configuration with useAIError', () => {
      const { result } = renderHook(() => useAIError('TestService'));

      expect(result.current.error).toBeNull();
      expect(result.current.hasError).toBe(false);
      // Should have default retry and fallback enabled
    });
  });

  describe('Error State Properties', () => {
    it('should provide correct error state properties', async () => {
      const mockAIError = {
        code: AIServiceErrorCode.API_REQUEST_FAILED,
        severity: AIServiceErrorSeverity.HIGH,
        service: 'TestService',
        operation: 'testOp',
        recoverable: true
      };

      mockErrorHandler.createError.mockReturnValue(mockAIError);
      mockErrorHandler.handleError.mockResolvedValue(null);

      const { result } = renderHook(() => 
        useAIErrorHandler({ serviceName: 'TestService', maxRetries: 3 })
      );

      await act(async () => {
        await result.current.handleError(new Error('Test error'), 'testOperation');
      });

      expect(result.current.errorSeverity).toBe(AIServiceErrorSeverity.HIGH);
      expect(result.current.errorCode).toBe(AIServiceErrorCode.API_REQUEST_FAILED);
      expect(result.current.isRecoverable).toBe(true);
      expect(result.current.canRetry).toBe(true); // recoverable and under max retries
    });
  });
});