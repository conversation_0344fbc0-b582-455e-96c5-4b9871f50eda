import React from "react";
import DashboardLayout from "@/layouts/DashboardLayout";
import { Outlet, createRootRoute, useLocation } from "@tanstack/react-router";
import { AuthGuard, AuthenticatedChatBot } from "@/components/auth";
import { NavigationProvider } from "@/contexts/NavigationContext";

/**
 * Root-Route für die Shopfloor-Management-App
 * 
 * Verwendet das DashboardLayout als Basis-Layout für alle Seiten
 * mit der Sidebar für die Navigation zwischen den Abteilungen.
 * AuthGuard sorgt für die Authentifizierungsprüfung auf allen Routen.
 * 
 * Öffentliche Routen (Login/Register) werden ohne DashboardLayout gerendert.
 */
export const RootRoute = createRootRoute({
  component: Root,
});

function Root() {
  const location = useLocation();
  const isPublicRoute = location.pathname === '/login' || location.pathname === '/register';

  return (
    <AuthGuard>
      <NavigationProvider>
        {isPublicRoute ? (
          <Outlet />
        ) : (
          <DashboardLayout>
            <Outlet />
          </DashboardLayout>
        )}
        <AuthenticatedChatBot />
      </NavigationProvider>
    </AuthGuard>
  );
}
