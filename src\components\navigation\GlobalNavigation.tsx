import { type ReactNode } from "react";
import { useNavigationContext } from "@/contexts/NavigationContext";
import { BaseNavigation } from "./BaseNavigation";
import { dashboardNavigationConfig } from "@/config/navigation";

interface GlobalNavigationProps {
    title?: ReactNode;
    showUserMenu?: boolean;
}

/**
 * Global Navigation Component
 * 
 * Verwendet die Navigation des aktuell aktiven Moduls, auch wenn wir uns
 * auf globalen Seiten wie Settings oder UserSettings befinden.
 * Falls kein Modul aktiv ist, wird die Dashboard-Navigation als Fallback verwendet.
 */
export const GlobalNavigation = ({ 
    title, 
    showUserMenu = true 
}: GlobalNavigationProps) => {
    const { currentNavigationConfig } = useNavigationContext();
    
    // Fallback zur Dashboard-Navigation wenn kein Modul aktiv ist
    const navigationConfig = currentNavigationConfig || dashboardNavigationConfig;
    
    return (
        <BaseNavigation 
            title={title}
            navigationConfig={navigationConfig}
            showUserMenu={showUserMenu}
        />
    );
};