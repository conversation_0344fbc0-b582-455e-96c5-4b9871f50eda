/**
 * Supply Chain Optimizer Service Integration Tests
 * 
 * Integration tests for the supply chain optimization service with real repositories
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { SupplyChainOptimizerService } from '../SupplyChainOptimizerService';
import type {
  DeliveryPredictionRequest,
  SupplierEvaluationRequest,
  LogisticsOptimizationRequest,
  DisruptionAnalysisRequest
} from '@/types/supply-chain-optimization';

describe('SupplyChainOptimizerService Integration Tests', () => {
  let service: SupplyChainOptimizerService;

  beforeEach(async () => {
    service = new SupplyChainOptimizerService({
      predictionHorizon: 30,
      riskAssessmentDepth: 'detailed',
      enableRealTimeTracking: true,
      maxOptimizationRoutes: 3,
      disruptionSensitivity: 'medium'
    });

    await service.initialize();
  });

  afterEach(async () => {
    await service.destroy();
  });

  describe('End-to-End Delivery Prediction Workflow', () => {
    it('should complete full delivery prediction workflow', async () => {
      const request: DeliveryPredictionRequest = {
        supplierId: 'SUP_001',
        productType: 'Kabel',
        quantity: 500,
        urgency: 'high',
        destination: 'Hamburg'
      };

      const prediction = await service.predictDeliveryTimes(request);

      // Verify complete prediction structure
      expect(prediction).toBeDefined();
      expect(prediction.supplierId).toBe('SUP_001');
      expect(prediction.predictedDeliveryTime).toBeGreaterThan(0);
      expect(prediction.predictedDeliveryTime).toBeLessThan(30); // Reasonable range
      
      // Verify confidence intervals are logical
      expect(prediction.confidenceIntervals.lower95).toBeLessThan(prediction.predictedDeliveryTime);
      expect(prediction.confidenceIntervals.upper95).toBeGreaterThan(prediction.predictedDeliveryTime);
      expect(prediction.confidenceIntervals.lower68).toBeGreaterThan(prediction.confidenceIntervals.lower95);
      expect(prediction.confidenceIntervals.upper68).toBeLessThan(prediction.confidenceIntervals.upper95);
      
      // Verify risk factors are identified
      expect(prediction.delayRisks).toBeInstanceOf(Array);
      
      // Verify factors are provided
      expect(prediction.factors).toBeInstanceOf(Array);
      expect(prediction.factors.length).toBeGreaterThan(0);
      
      // Verify timestamps
      expect(prediction.lastUpdated).toBeInstanceOf(Date);
      expect(prediction.lastUpdated.getTime()).toBeLessThanOrEqual(Date.now());
    });

    it('should handle multiple concurrent prediction requests', async () => {
      const requests: DeliveryPredictionRequest[] = [
        { supplierId: 'SUP_001', productType: 'Kabel', urgency: 'high' },
        { supplierId: 'SUP_002', productType: 'Stecker', urgency: 'medium' },
        { supplierId: 'SUP_003', productType: 'Komponenten', urgency: 'low' }
      ];

      const predictions = await Promise.all(
        requests.map(request => service.predictDeliveryTimes(request))
      );

      expect(predictions).toHaveLength(3);
      predictions.forEach((prediction, index) => {
        expect(prediction.supplierId).toBe(requests[index].supplierId);
        expect(prediction.predictedDeliveryTime).toBeGreaterThan(0);
      });

      // Verify urgency affects delivery times
      const highUrgency = predictions.find(p => p.supplierId === 'SUP_001');
      const lowUrgency = predictions.find(p => p.supplierId === 'SUP_003');
      
      if (highUrgency && lowUrgency) {
        // High urgency should generally be faster (though not guaranteed due to other factors)
        expect(highUrgency.predictedDeliveryTime).toBeLessThanOrEqual(lowUrgency.predictedDeliveryTime * 1.2);
      }
    });
  });

  describe('End-to-End Risk Assessment Workflow', () => {
    it('should complete full risk assessment workflow', async () => {
      const request: SupplierEvaluationRequest = {
        supplierId: 'SUP_001',
        productCategories: ['Kabel', 'Stecker'],
        assessmentDate: new Date(),
        timeRange: { days: 180 }
      };

      const assessment = await service.assessSupplierRisk(request);

      // Verify complete assessment structure
      expect(assessment).toBeDefined();
      expect(assessment.supplierId).toBe('SUP_001');
      expect(assessment.overallRiskScore).toBeGreaterThan(0);
      expect(assessment.overallRiskScore).toBeLessThanOrEqual(10);
      
      // Verify risk category matches score
      const { riskCategory, overallRiskScore } = assessment;
      if (overallRiskScore <= 3) {
        expect(riskCategory).toBe('low');
      } else if (overallRiskScore <= 5) {
        expect(riskCategory).toBe('medium');
      } else if (overallRiskScore <= 7) {
        expect(riskCategory).toBe('high');
      } else {
        expect(riskCategory).toBe('critical');
      }
      
      // Verify risk factors are detailed
      expect(assessment.riskFactors).toBeInstanceOf(Array);
      assessment.riskFactors.forEach(factor => {
        expect(factor.type).toBeDefined();
        expect(['low', 'medium', 'high']).toContain(factor.severity);
        expect(factor.probability).toBeGreaterThanOrEqual(0);
        expect(factor.probability).toBeLessThanOrEqual(1);
        expect(factor.description).toBeDefined();
        expect(factor.impact).toBeGreaterThanOrEqual(0);
      });
      
      // Verify recommendations are provided
      expect(assessment.recommendations).toBeInstanceOf(Array);
      expect(assessment.recommendations.length).toBeGreaterThan(0);
      
      // Verify alternative suppliers are suggested
      expect(assessment.alternativeSuppliers).toBeInstanceOf(Array);
      
      // Verify validity period
      expect(assessment.assessmentDate).toBeInstanceOf(Date);
      expect(assessment.validUntil).toBeInstanceOf(Date);
      expect(assessment.validUntil.getTime()).toBeGreaterThan(assessment.assessmentDate.getTime());
    });

    it('should cache and reuse risk assessments', async () => {
      const request: SupplierEvaluationRequest = {
        supplierId: 'SUP_CACHE_TEST',
        assessmentDate: new Date()
      };

      // First assessment
      const startTime1 = Date.now();
      const assessment1 = await service.assessSupplierRisk(request);
      const duration1 = Date.now() - startTime1;

      // Second assessment (should be cached)
      const startTime2 = Date.now();
      const assessment2 = await service.assessSupplierRisk(request);
      const duration2 = Date.now() - startTime2;

      // Verify results are identical
      expect(assessment1).toEqual(assessment2);
      
      // Cached request should be significantly faster
      expect(duration2).toBeLessThan(duration1 * 0.5);
    });
  });

  describe('End-to-End Logistics Optimization Workflow', () => {
    it('should complete full logistics optimization workflow', async () => {
      const request: LogisticsOptimizationRequest = {
        requestId: 'INTEGRATION_TEST_001',
        deliveries: [
          {
            deliveryId: 'DEL_001',
            destination: 'Hamburg',
            items: [
              {
                itemId: 'KABEL_001',
                quantity: 100,
                weight: 50,
                dimensions: { length: 100, width: 10, height: 5, volume: 5000 }
              }
            ],
            priority: 'high',
            timeWindow: {
              earliest: new Date(Date.now() + 24 * 60 * 60 * 1000),
              latest: new Date(Date.now() + 72 * 60 * 60 * 1000)
            }
          },
          {
            deliveryId: 'DEL_002',
            destination: 'München',
            items: [
              {
                itemId: 'STECKER_001',
                quantity: 50,
                weight: 25,
                dimensions: { length: 50, width: 20, height: 10, volume: 10000 }
              }
            ],
            priority: 'medium'
          }
        ],
        constraints: {
          maxVehicleCapacity: 2000,
          maxRouteDistance: 800,
          maxRouteTime: 600,
          availableVehicles: 2,
          driverWorkingHours: 10,
          fuelCostPerKm: 0.18,
          laborCostPerHour: 28
        },
        optimizationGoals: [
          { type: 'minimize_cost', weight: 0.5 },
          { type: 'minimize_time', weight: 0.3 },
          { type: 'maximize_efficiency', weight: 0.2 }
        ]
      };

      const optimization = await service.optimizeLogistics(request);

      // Verify complete optimization structure
      expect(optimization).toBeDefined();
      expect(optimization.requestId).toBe('INTEGRATION_TEST_001');
      
      // Verify current routes analysis
      expect(optimization.currentRoutes).toBeInstanceOf(Array);
      expect(optimization.currentRoutes.length).toBeGreaterThan(0);
      optimization.currentRoutes.forEach(route => {
        expect(route.routeId).toBeDefined();
        expect(route.deliveries).toBeInstanceOf(Array);
        expect(route.totalDistance).toBeGreaterThan(0);
        expect(route.estimatedTime).toBeGreaterThan(0);
        expect(route.cost).toBeGreaterThan(0);
      });
      
      // Verify optimized routes
      expect(optimization.optimizedRoutes).toBeInstanceOf(Array);
      expect(optimization.optimizedRoutes.length).toBeGreaterThan(0);
      optimization.optimizedRoutes.forEach(route => {
        expect(route.optimizationMethod).toBeDefined();
        expect(route.confidence).toBeGreaterThan(0);
        expect(route.confidence).toBeLessThanOrEqual(1);
      });
      
      // Verify benefits calculation
      expect(optimization.benefits).toBeDefined();
      expect(optimization.benefits.distanceReduction).toBeGreaterThanOrEqual(0);
      expect(optimization.benefits.timeReduction).toBeGreaterThanOrEqual(0);
      expect(optimization.benefits.costSavings).toBeGreaterThanOrEqual(0);
      
      // Verify contingency plans
      expect(optimization.contingencyPlans).toBeInstanceOf(Array);
      
      // Verify implementation steps
      expect(optimization.implementationSteps).toBeInstanceOf(Array);
      expect(optimization.implementationSteps.length).toBeGreaterThan(0);
      
      // Verify timestamp
      expect(optimization.optimizationDate).toBeInstanceOf(Date);
    });

    it('should handle complex multi-delivery optimization', async () => {
      const deliveries = [];
      const destinations = ['Berlin', 'Hamburg', 'München', 'Köln', 'Frankfurt'];
      
      // Create 10 deliveries to different destinations
      for (let i = 0; i < 10; i++) {
        deliveries.push({
          deliveryId: `DEL_${i.toString().padStart(3, '0')}`,
          destination: destinations[i % destinations.length],
          items: [{
            itemId: `ITEM_${i}`,
            quantity: Math.floor(Math.random() * 100) + 10,
            weight: Math.random() * 50 + 5,
            dimensions: {
              length: Math.random() * 100 + 20,
              width: Math.random() * 50 + 10,
              height: Math.random() * 30 + 5,
              volume: 0 // Will be calculated
            }
          }],
          priority: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low'
        });
      }
      
      // Calculate volumes
      deliveries.forEach(delivery => {
        delivery.items.forEach(item => {
          item.dimensions.volume = item.dimensions.length * item.dimensions.width * item.dimensions.height;
        });
      });

      const request: LogisticsOptimizationRequest = {
        requestId: 'COMPLEX_OPTIMIZATION_001',
        deliveries,
        constraints: {
          maxVehicleCapacity: 3000,
          maxRouteDistance: 1000,
          maxRouteTime: 720,
          availableVehicles: 3,
          driverWorkingHours: 12,
          fuelCostPerKm: 0.20,
          laborCostPerHour: 30
        },
        optimizationGoals: [
          { type: 'minimize_cost', weight: 0.6 },
          { type: 'minimize_distance', weight: 0.4 }
        ]
      };

      const optimization = await service.optimizeLogistics(request);

      expect(optimization).toBeDefined();
      expect(optimization.optimizedRoutes.length).toBeLessThanOrEqual(3); // Max vehicles
      
      // Verify all deliveries are included
      const totalOptimizedDeliveries = optimization.optimizedRoutes.reduce(
        (sum, route) => sum + route.deliveries.length, 0
      );
      expect(totalOptimizedDeliveries).toBe(10);
      
      // Verify optimization provides benefits
      const hasImprovement = 
        optimization.benefits.distanceReduction > 0 ||
        optimization.benefits.timeReduction > 0 ||
        optimization.benefits.costSavings > 0;
      expect(hasImprovement).toBe(true);
    });
  });

  describe('End-to-End Disruption Analysis Workflow', () => {
    it('should complete full disruption analysis workflow', async () => {
      const request: DisruptionAnalysisRequest = {
        disruptionId: 'INTEGRATION_DISRUPTION_001',
        disruptionType: 'weather',
        affectedRegion: 'Norddeutschland',
        severity: 'high',
        estimatedDuration: 5,
        description: 'Schwerer Schneesturm blockiert Hauptverkehrsrouten'
      };

      const disruption = await service.analyzeSupplyChainDisruption(request);

      // Verify complete disruption analysis structure
      expect(disruption).toBeDefined();
      expect(disruption.disruptionId).toBe('INTEGRATION_DISRUPTION_001');
      expect(disruption.disruptionType).toBe('weather');
      expect(['low', 'medium', 'high', 'critical']).toContain(disruption.severity);
      
      // Verify impact assessment
      expect(disruption.impactAssessment).toBeDefined();
      expect(disruption.impactAssessment.affectedSuppliers).toBeGreaterThanOrEqual(0);
      expect(disruption.impactAssessment.affectedRoutes).toBeGreaterThanOrEqual(0);
      expect(disruption.impactAssessment.estimatedDuration).toBeGreaterThan(0);
      expect(['low', 'medium', 'high', 'critical']).toContain(disruption.impactAssessment.businessImpact);
      
      // Verify affected entities
      expect(disruption.affectedSuppliers).toBeInstanceOf(Array);
      expect(disruption.affectedRoutes).toBeInstanceOf(Array);
      
      // Verify mitigation strategies
      expect(disruption.mitigationStrategies).toBeInstanceOf(Array);
      expect(disruption.mitigationStrategies.length).toBeGreaterThan(0);
      disruption.mitigationStrategies.forEach(strategy => {
        expect(strategy.strategyId).toBeDefined();
        expect(strategy.description).toBeDefined();
        expect(['low', 'medium', 'high']).toContain(strategy.priority);
        expect(strategy.estimatedEffectiveness).toBeGreaterThan(0);
        expect(strategy.estimatedEffectiveness).toBeLessThanOrEqual(1);
        expect(strategy.implementationTime).toBeGreaterThan(0);
        expect(strategy.cost).toBeGreaterThanOrEqual(0);
      });
      
      // Verify recovery timeline
      expect(disruption.recoveryTimeline).toBeDefined();
      expect(disruption.recoveryTimeline.phases).toBeInstanceOf(Array);
      expect(disruption.recoveryTimeline.phases.length).toBeGreaterThan(0);
      disruption.recoveryTimeline.phases.forEach(phase => {
        expect(phase.phase).toBeDefined();
        expect(phase.duration).toBeGreaterThan(0);
        expect(phase.description).toBeDefined();
      });
      
      // Verify financial impact
      expect(disruption.financialImpact).toBeDefined();
      expect(disruption.financialImpact.estimatedCost).toBeGreaterThanOrEqual(0);
      expect(disruption.financialImpact.potentialRevenueLoss).toBeGreaterThanOrEqual(0);
      expect(disruption.financialImpact.mitigationCost).toBeGreaterThanOrEqual(0);
      
      // Verify timestamp
      expect(disruption.analysisDate).toBeInstanceOf(Date);
    });

    it('should analyze different disruption types appropriately', async () => {
      const disruptionTypes = ['weather', 'supplier_failure', 'transportation', 'demand_spike', 'quality_issue'];
      
      const analyses = await Promise.all(
        disruptionTypes.map(async (type, index) => {
          const request: DisruptionAnalysisRequest = {
            disruptionId: `MULTI_TYPE_${index}`,
            disruptionType: type as any,
            severity: 'medium'
          };
          
          return await service.analyzeSupplyChainDisruption(request);
        })
      );

      expect(analyses).toHaveLength(disruptionTypes.length);
      
      analyses.forEach((analysis, index) => {
        expect(analysis.disruptionType).toBe(disruptionTypes[index]);
        expect(analysis.mitigationStrategies.length).toBeGreaterThan(0);
        
        // Different disruption types should have different characteristics
        expect(analysis.severity).toBeDefined();
        expect(analysis.recoveryTimeline.phases.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Service Performance and Reliability', () => {
    it('should maintain performance under load', async () => {
      const requests = Array.from({ length: 20 }, (_, i) => ({
        supplierId: `SUP_${i.toString().padStart(3, '0')}`,
        productType: 'Test Product',
        urgency: 'medium' as const
      }));

      const startTime = Date.now();
      const predictions = await Promise.all(
        requests.map(request => service.predictDeliveryTimes(request))
      );
      const duration = Date.now() - startTime;

      // All requests should complete
      expect(predictions).toHaveLength(20);
      predictions.forEach(prediction => {
        expect(prediction).toBeDefined();
        expect(prediction.predictedDeliveryTime).toBeGreaterThan(0);
      });

      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(10000); // 10 seconds for 20 requests
    });

    it('should handle service restart gracefully', async () => {
      // Make initial request
      const request: DeliveryPredictionRequest = {
        supplierId: 'SUP_RESTART_TEST'
      };

      const prediction1 = await service.predictDeliveryTimes(request);
      expect(prediction1).toBeDefined();

      // Restart service
      await service.destroy();
      await service.initialize();

      // Make request after restart
      const prediction2 = await service.predictDeliveryTimes(request);
      expect(prediction2).toBeDefined();

      // Both predictions should be valid (though may differ due to cache clearing)
      expect(prediction1.supplierId).toBe(prediction2.supplierId);
    });

    it('should provide consistent health status', () => {
      const health1 = service.getHealthStatus();
      const health2 = service.getHealthStatus();

      expect(health1.status).toBe(health2.status);
      expect(health1.details.configuration).toEqual(health2.details.configuration);
    });
  });

  describe('Data Integration and Consistency', () => {
    it('should maintain data consistency across operations', async () => {
      const supplierId = 'SUP_CONSISTENCY_TEST';

      // Get risk assessment
      const riskAssessment = await service.assessSupplierRisk({
        supplierId,
        timeRange: { days: 90 }
      });

      // Get delivery prediction
      const deliveryPrediction = await service.predictDeliveryTimes({
        supplierId,
        urgency: 'medium'
      });

      // Both operations should reference the same supplier
      expect(riskAssessment.supplierId).toBe(supplierId);
      expect(deliveryPrediction.supplierId).toBe(supplierId);

      // Risk factors should be consistent with delivery risks
      const hasDeliveryRisk = riskAssessment.riskFactors.some(
        factor => factor.type === 'delivery_performance'
      );
      const hasDelayRisk = deliveryPrediction.delayRisks.some(
        risk => risk.type === 'supplier_reliability'
      );

      // If there's a delivery performance risk, there should be corresponding delay risks
      if (hasDeliveryRisk && riskAssessment.riskCategory !== 'low') {
        expect(deliveryPrediction.delayRisks.length).toBeGreaterThan(0);
      }
    });

    it('should handle cross-service data dependencies', async () => {
      // Create logistics optimization that might affect delivery predictions
      const logisticsRequest: LogisticsOptimizationRequest = {
        requestId: 'CROSS_SERVICE_TEST',
        deliveries: [{
          deliveryId: 'DEL_CROSS_TEST',
          destination: 'Test Location',
          items: [{
            itemId: 'ITEM_CROSS_TEST',
            quantity: 10,
            weight: 5,
            dimensions: { length: 10, width: 5, height: 2, volume: 100 }
          }],
          priority: 'medium'
        }],
        constraints: {
          maxVehicleCapacity: 1000,
          maxRouteDistance: 500,
          maxRouteTime: 480,
          availableVehicles: 1,
          driverWorkingHours: 8,
          fuelCostPerKm: 0.15,
          laborCostPerHour: 25
        },
        optimizationGoals: [{ type: 'minimize_cost', weight: 1.0 }]
      };

      const optimization = await service.optimizeLogistics(logisticsRequest);
      expect(optimization).toBeDefined();

      // The optimization should provide realistic benefits
      expect(optimization.benefits.distanceReduction).toBeGreaterThanOrEqual(0);
      expect(optimization.benefits.distanceReduction).toBeLessThan(100); // Shouldn't be more than 100%
    });
  });
});