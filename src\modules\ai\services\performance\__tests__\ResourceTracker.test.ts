import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { ResourceTracker } from '../ResourceTracker';

describe('ResourceTracker', () => {
  let resourceTracker: ResourceTracker;

  beforeEach(() => {
    resourceTracker = new ResourceTracker();
    vi.useFakeTimers();
  });

  afterEach(() => {
    resourceTracker.stopCollection();
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  describe('Resource Collection', () => {
    it('should collect current metrics', () => {
      const metrics = resourceTracker.getCurrentMetrics();

      expect(metrics).toHaveProperty('timestamp');
      expect(metrics).toHaveProperty('memoryUsage');
      expect(metrics).toHaveProperty('cpuUsage');
      expect(metrics).toHaveProperty('networkUsage');
      expect(metrics).toHaveProperty('diskUsage');
      expect(metrics).toHaveProperty('aiSpecificMetrics');

      expect(metrics.timestamp).toBeTypeOf('number');
      expect(metrics.memoryUsage).toHaveProperty('heapUsed');
      expect(metrics.cpuUsage).toHaveProperty('percent');
    });

    it('should start and stop collection', () => {
      resourceTracker.startCollection();
      
      // Advance time to trigger collection
      vi.advanceTimersByTime(30000);
      
      const history = resourceTracker.getHistory();
      expect(history.length).toBeGreaterThan(0);

      resourceTracker.stopCollection();
      
      const historyLengthAfterStop = history.length;
      vi.advanceTimersByTime(30000);
      
      // Should not collect more after stopping
      const historyAfterStop = resourceTracker.getHistory();
      expect(historyAfterStop.length).toBe(historyLengthAfterStop);
    });

    it('should maintain history within limits', () => {
      // Simulate many collection cycles
      for (let i = 0; i < 1200; i++) {
        vi.advanceTimersByTime(30000);
      }

      const history = resourceTracker.getHistory();
      expect(history.length).toBeLessThanOrEqual(1000); // Max history size
    });
  });

  describe('History Management', () => {
    beforeEach(() => {
      // Add some test data
      vi.advanceTimersByTime(30000); // First collection
      vi.advanceTimersByTime(30000); // Second collection
      vi.advanceTimersByTime(30000); // Third collection
    });

    it('should return full history when no time window specified', () => {
      const history = resourceTracker.getHistory();
      expect(history.length).toBeGreaterThan(0);
    });

    it('should filter history by time window', () => {
      const now = Date.now();
      vi.setSystemTime(now);

      // Get history for last minute
      const recentHistory = resourceTracker.getHistory(60000);
      const allHistory = resourceTracker.getHistory();

      expect(recentHistory.length).toBeLessThanOrEqual(allHistory.length);
      
      // All recent history entries should be within the time window
      recentHistory.forEach(entry => {
        expect(entry.timestamp).toBeGreaterThan(now - 60000);
      });
    });
  });

  describe('Trend Analysis', () => {
    beforeEach(() => {
      // Mock metrics with trends
      const mockMetrics = [
        {
          timestamp: Date.now() - 3600000,
          memoryUsage: { heapUsed: 100 * 1024 * 1024 },
          cpuUsage: { percent: 20 },
          networkUsage: { requestsPerSecond: 10 }
        },
        {
          timestamp: Date.now() - 1800000,
          memoryUsage: { heapUsed: 150 * 1024 * 1024 },
          cpuUsage: { percent: 30 },
          networkUsage: { requestsPerSecond: 15 }
        },
        {
          timestamp: Date.now(),
          memoryUsage: { heapUsed: 200 * 1024 * 1024 },
          cpuUsage: { percent: 40 },
          networkUsage: { requestsPerSecond: 20 }
        }
      ];

      // Inject mock data
      (resourceTracker as any).metrics = mockMetrics;
    });

    it('should detect increasing trends', () => {
      const trends = resourceTracker.getTrends(3600000);

      expect(trends.memory.trend).toBe('increasing');
      expect(trends.cpu.trend).toBe('increasing');
      expect(trends.network.trend).toBe('increasing');
    });

    it('should calculate trend rates', () => {
      const trends = resourceTracker.getTrends(3600000);

      expect(trends.memory.rate).toBeGreaterThan(0);
      expect(trends.cpu.rate).toBeGreaterThan(0);
      expect(trends.network.rate).toBeGreaterThan(0);
    });

    it('should detect stable trends with minimal change', () => {
      const stableMetrics = [
        {
          timestamp: Date.now() - 3600000,
          memoryUsage: { heapUsed: 100 * 1024 * 1024 },
          cpuUsage: { percent: 20 },
          networkUsage: { requestsPerSecond: 10 }
        },
        {
          timestamp: Date.now(),
          memoryUsage: { heapUsed: 101 * 1024 * 1024 },
          cpuUsage: { percent: 20.1 },
          networkUsage: { requestsPerSecond: 10.1 }
        }
      ];

      (resourceTracker as any).metrics = stableMetrics;

      const trends = resourceTracker.getTrends(3600000);

      expect(trends.memory.trend).toBe('stable');
      expect(trends.cpu.trend).toBe('stable');
      expect(trends.network.trend).toBe('stable');
    });
  });

  describe('Optimization Recommendations', () => {
    it('should generate memory optimization recommendations', () => {
      // Mock high memory usage
      const highMemoryMetrics = {
        timestamp: Date.now(),
        memoryUsage: { heapUsed: 150 * 1024 * 1024 }, // 150MB
        cpuUsage: { percent: 20 },
        networkUsage: { requestsPerSecond: 10 },
        diskUsage: { readBytes: 0, writeBytes: 0, readOperations: 0, writeOperations: 0 },
        aiSpecificMetrics: { activeEmbeddings: 10, vectorSearches: 5, cacheSize: 10 * 1024 * 1024, queueLength: 2 }
      };

      vi.spyOn(resourceTracker, 'getCurrentMetrics').mockReturnValue(highMemoryMetrics);

      const recommendations = resourceTracker.getOptimizationRecommendations();
      const memoryRecommendation = recommendations.find(r => r.type === 'memory');

      expect(memoryRecommendation).toBeDefined();
      expect(memoryRecommendation?.title).toContain('Speicherverbrauch');
    });

    it('should generate memory leak recommendations', () => {
      // Mock increasing memory trend
      const increasingTrend = {
        memory: { trend: 'increasing' as const, rate: 2000 }, // 2KB/ms increase
        cpu: { trend: 'stable' as const, rate: 0 },
        network: { trend: 'stable' as const, rate: 0 }
      };

      vi.spyOn(resourceTracker, 'getTrends').mockReturnValue(increasingTrend);

      const recommendations = resourceTracker.getOptimizationRecommendations();
      const leakRecommendation = recommendations.find(r => r.id === 'memory-leak-detection');

      expect(leakRecommendation).toBeDefined();
      expect(leakRecommendation?.title).toContain('Memory Leak');
    });

    it('should generate CPU optimization recommendations', () => {
      const highCpuMetrics = {
        timestamp: Date.now(),
        memoryUsage: { heapUsed: 50 * 1024 * 1024 },
        cpuUsage: { percent: 85 }, // High CPU usage
        networkUsage: { requestsPerSecond: 10 },
        diskUsage: { readBytes: 0, writeBytes: 0, readOperations: 0, writeOperations: 0 },
        aiSpecificMetrics: { activeEmbeddings: 10, vectorSearches: 5, cacheSize: 10 * 1024 * 1024, queueLength: 2 }
      };

      vi.spyOn(resourceTracker, 'getCurrentMetrics').mockReturnValue(highCpuMetrics);

      const recommendations = resourceTracker.getOptimizationRecommendations();
      const cpuRecommendation = recommendations.find(r => r.type === 'cpu');

      expect(cpuRecommendation).toBeDefined();
      expect(cpuRecommendation?.title).toContain('CPU-Auslastung');
    });

    it('should generate cache optimization recommendations', () => {
      const largeCacheMetrics = {
        timestamp: Date.now(),
        memoryUsage: { heapUsed: 50 * 1024 * 1024 },
        cpuUsage: { percent: 20 },
        networkUsage: { requestsPerSecond: 10 },
        diskUsage: { readBytes: 0, writeBytes: 0, readOperations: 0, writeOperations: 0 },
        aiSpecificMetrics: { 
          activeEmbeddings: 10, 
          vectorSearches: 5, 
          cacheSize: 60 * 1024 * 1024, // 60MB cache
          queueLength: 2 
        }
      };

      vi.spyOn(resourceTracker, 'getCurrentMetrics').mockReturnValue(largeCacheMetrics);

      const recommendations = resourceTracker.getOptimizationRecommendations();
      const cacheRecommendation = recommendations.find(r => r.type === 'cache');

      expect(cacheRecommendation).toBeDefined();
      expect(cacheRecommendation?.title).toContain('Cache-Größe');
    });

    it('should generate algorithm optimization recommendations', () => {
      const highVectorSearchMetrics = {
        timestamp: Date.now(),
        memoryUsage: { heapUsed: 50 * 1024 * 1024 },
        cpuUsage: { percent: 20 },
        networkUsage: { requestsPerSecond: 10 },
        diskUsage: { readBytes: 0, writeBytes: 0, readOperations: 0, writeOperations: 0 },
        aiSpecificMetrics: { 
          activeEmbeddings: 10, 
          vectorSearches: 150, // High vector searches
          cacheSize: 10 * 1024 * 1024, 
          queueLength: 2 
        }
      };

      vi.spyOn(resourceTracker, 'getCurrentMetrics').mockReturnValue(highVectorSearchMetrics);

      const recommendations = resourceTracker.getOptimizationRecommendations();
      const algorithmRecommendation = recommendations.find(r => r.type === 'algorithm');

      expect(algorithmRecommendation).toBeDefined();
      expect(algorithmRecommendation?.title).toContain('Vektor-Suche');
    });

    it('should generate network optimization recommendations', () => {
      const highNetworkMetrics = {
        timestamp: Date.now(),
        memoryUsage: { heapUsed: 50 * 1024 * 1024 },
        cpuUsage: { percent: 20 },
        networkUsage: { requestsPerSecond: 60 }, // High request rate
        diskUsage: { readBytes: 0, writeBytes: 0, readOperations: 0, writeOperations: 0 },
        aiSpecificMetrics: { activeEmbeddings: 10, vectorSearches: 5, cacheSize: 10 * 1024 * 1024, queueLength: 2 }
      };

      vi.spyOn(resourceTracker, 'getCurrentMetrics').mockReturnValue(highNetworkMetrics);

      const recommendations = resourceTracker.getOptimizationRecommendations();
      const networkRecommendation = recommendations.find(r => r.type === 'network');

      expect(networkRecommendation).toBeDefined();
      expect(networkRecommendation?.title).toContain('Netzwerk-Requests');
    });

    it('should prioritize recommendations by severity', () => {
      // Mock conditions that generate multiple recommendations
      const criticalMetrics = {
        timestamp: Date.now(),
        memoryUsage: { heapUsed: 600 * 1024 * 1024 }, // Very high memory
        cpuUsage: { percent: 95 }, // Very high CPU
        networkUsage: { requestsPerSecond: 100 },
        diskUsage: { readBytes: 0, writeBytes: 0, readOperations: 0, writeOperations: 0 },
        aiSpecificMetrics: { activeEmbeddings: 10, vectorSearches: 200, cacheSize: 100 * 1024 * 1024, queueLength: 2 }
      };

      vi.spyOn(resourceTracker, 'getCurrentMetrics').mockReturnValue(criticalMetrics);

      const recommendations = resourceTracker.getOptimizationRecommendations();
      
      // Should be sorted by priority (critical first)
      expect(recommendations.length).toBeGreaterThan(1);
      
      let lastPriority = 4; // critical = 4
      recommendations.forEach(rec => {
        const priorityValue = { critical: 4, high: 3, medium: 2, low: 1 }[rec.priority];
        expect(priorityValue).toBeLessThanOrEqual(lastPriority);
        lastPriority = priorityValue;
      });
    });
  });

  describe('Resource Alerts', () => {
    it('should generate memory alerts', () => {
      const highMemoryMetrics = {
        timestamp: Date.now(),
        memoryUsage: { heapUsed: 600 * 1024 * 1024 }, // 600MB - above critical threshold
        cpuUsage: { percent: 20 },
        networkUsage: { requestsPerSecond: 10 },
        diskUsage: { readBytes: 0, writeBytes: 0, readOperations: 0, writeOperations: 0 },
        aiSpecificMetrics: { activeEmbeddings: 10, vectorSearches: 5, cacheSize: 10 * 1024 * 1024, queueLength: 2 }
      };

      vi.spyOn(resourceTracker, 'getCurrentMetrics').mockReturnValue(highMemoryMetrics);

      const alerts = resourceTracker.getResourceAlerts();
      const memoryAlert = alerts.find(a => a.resource === 'memory');

      expect(memoryAlert).toBeDefined();
      expect(memoryAlert?.type).toBe('critical');
      expect(memoryAlert?.message).toContain('Speicherverbrauch');
    });

    it('should generate CPU alerts', () => {
      const highCpuMetrics = {
        timestamp: Date.now(),
        memoryUsage: { heapUsed: 50 * 1024 * 1024 },
        cpuUsage: { percent: 95 }, // Above critical threshold
        networkUsage: { requestsPerSecond: 10 },
        diskUsage: { readBytes: 0, writeBytes: 0, readOperations: 0, writeOperations: 0 },
        aiSpecificMetrics: { activeEmbeddings: 10, vectorSearches: 5, cacheSize: 10 * 1024 * 1024, queueLength: 2 }
      };

      vi.spyOn(resourceTracker, 'getCurrentMetrics').mockReturnValue(highCpuMetrics);

      const alerts = resourceTracker.getResourceAlerts();
      const cpuAlert = alerts.find(a => a.resource === 'cpu');

      expect(cpuAlert).toBeDefined();
      expect(cpuAlert?.type).toBe('critical');
      expect(cpuAlert?.message).toContain('CPU-Auslastung');
    });

    it('should generate warning alerts before critical', () => {
      const warningLevelMetrics = {
        timestamp: Date.now(),
        memoryUsage: { heapUsed: 150 * 1024 * 1024 }, // Above warning, below critical
        cpuUsage: { percent: 75 }, // Above warning, below critical
        networkUsage: { requestsPerSecond: 10 },
        diskUsage: { readBytes: 0, writeBytes: 0, readOperations: 0, writeOperations: 0 },
        aiSpecificMetrics: { activeEmbeddings: 10, vectorSearches: 5, cacheSize: 10 * 1024 * 1024, queueLength: 2 }
      };

      vi.spyOn(resourceTracker, 'getCurrentMetrics').mockReturnValue(warningLevelMetrics);

      const alerts = resourceTracker.getResourceAlerts();
      
      expect(alerts.some(a => a.type === 'warning')).toBe(true);
      expect(alerts.every(a => a.type !== 'critical')).toBe(true);
    });
  });

  describe('Threshold Management', () => {
    it('should allow setting custom thresholds', () => {
      const customThresholds = {
        memoryWarning: 50 * 1024 * 1024, // 50MB
        memoryCritical: 100 * 1024 * 1024, // 100MB
        cpuWarning: 50, // 50%
        cpuCritical: 80 // 80%
      };

      resourceTracker.setThresholds(customThresholds);

      const lowMemoryMetrics = {
        timestamp: Date.now(),
        memoryUsage: { heapUsed: 75 * 1024 * 1024 }, // Between warning and critical
        cpuUsage: { percent: 60 }, // Between warning and critical
        networkUsage: { requestsPerSecond: 10 },
        diskUsage: { readBytes: 0, writeBytes: 0, readOperations: 0, writeOperations: 0 },
        aiSpecificMetrics: { activeEmbeddings: 10, vectorSearches: 5, cacheSize: 10 * 1024 * 1024, queueLength: 2 }
      };

      vi.spyOn(resourceTracker, 'getCurrentMetrics').mockReturnValue(lowMemoryMetrics);

      const alerts = resourceTracker.getResourceAlerts();
      
      expect(alerts.some(a => a.resource === 'memory' && a.type === 'warning')).toBe(true);
      expect(alerts.some(a => a.resource === 'cpu' && a.type === 'warning')).toBe(true);
    });
  });
});