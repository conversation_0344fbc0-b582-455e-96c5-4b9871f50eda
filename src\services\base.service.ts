/**
 * Base Service Interface
 * 
 * Definiert die grundlegende Service-Schnittstelle für Dependency Injection
 * und einheitliche Service-Patterns.
 */

/**
 * Service-Konfiguration Interface
 */
export interface ServiceConfig {
  enableCaching?: boolean;
  enableLogging?: boolean;
  retryAttempts?: number;
  timeout?: number;
}

/**
 * Service-Status Interface
 */
export interface ServiceStatus {
  isInitialized: boolean;
  isHealthy: boolean;
  lastError?: Error;
  lastChecked: Date;
}

/**
 * Base Service Interface für alle Services
 */
export interface IService {
  /**
   * Service-Name für Logging und Debugging
   */
  readonly serviceName: string;
  
  /**
   * Service initialisieren
   */
  initialize?(config?: ServiceConfig): Promise<void>;
  
  /**
   * Service-Health-Check
   */
  healthCheck?(): Promise<ServiceStatus>;
  
  /**
   * Service-Ressourcen aufräumen
   */
  destroy?(): Promise<void>;
}

/**
 * Base Service Klasse mit gemeinsamen Funktionen
 */
export abstract class BaseService implements IService {
  protected config: ServiceConfig;
  protected initialized = false;
  protected lastError?: Error;
  
  abstract readonly serviceName: string;
  
  constructor(config: ServiceConfig = {}) {
    this.config = {
      enableCaching: true,
      enableLogging: false,
      retryAttempts: 3,
      timeout: 10000,
      ...config
    };
  }
  
  /**
   * Service initialisieren
   */
  async initialize(config?: ServiceConfig): Promise<void> {
    if (config) {
      this.config = { ...this.config, ...config };
    }
    
    try {
      await this.onInitialize();
      this.initialized = true;
      this.log('Service initialized successfully');
    } catch (error) {
      this.lastError = error instanceof Error ? error : new Error(String(error));
      this.log('Service initialization failed', error);
      throw error;
    }
  }
  
  /**
   * Service-spezifische Initialisierung (überschreibbar)
   */
  protected async onInitialize(): Promise<void> {
    // Override in subclasses
  }
  
  /**
   * Health Check durchführen
   */
  async healthCheck(): Promise<ServiceStatus> {
    const status: ServiceStatus = {
      isInitialized: this.initialized,
      isHealthy: true,
      lastError: this.lastError,
      lastChecked: new Date()
    };
    
    try {
      await this.onHealthCheck();
    } catch (error) {
      status.isHealthy = false;
      status.lastError = error instanceof Error ? error : new Error(String(error));
      this.lastError = status.lastError;
    }
    
    return status;
  }
  
  /**
   * Service-spezifischer Health Check (überschreibbar)
   */
  protected async onHealthCheck(): Promise<void> {
    // Override in subclasses
  }
  
  /**
   * Service-Ressourcen aufräumen
   */
  async destroy(): Promise<void> {
    try {
      await this.onDestroy();
      this.initialized = false;
      this.log('Service destroyed successfully');
    } catch (error) {
      this.lastError = error instanceof Error ? error : new Error(String(error));
      this.log('Service destruction failed', error);
      throw error;
    }
  }
  
  /**
   * Service-spezifische Cleanup-Logik (überschreibbar)
   */
  protected async onDestroy(): Promise<void> {
    // Override in subclasses
  }
  
  /**
   * Hilfsmethode für Retry-Logik
   */
  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxAttempts = this.config.retryAttempts || 3
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        this.log(`Attempt ${attempt}/${maxAttempts} failed`, lastError);
        
        if (attempt === maxAttempts) {
          throw lastError;
        }
        
        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }
  
  /**
   * Hilfsmethode für Timeout-Handling
   */
  protected async withTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs = this.config.timeout || 10000
  ): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => 
      setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
    );
    
    return Promise.race([operation(), timeoutPromise]);
  }
  
  /**
   * Logging-Hilfsmethode
   */
  protected log(message: string, data?: any): void {
    if (!this.config.enableLogging) return;
    
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${this.serviceName}] ${message}`;
    
    if (data) {
      if (data instanceof Error) {
        console.error(logMessage, data);
      } else {
        console.log(logMessage, data);
      }
    } else {
      console.log(logMessage);
    }
  }
  
  /**
   * Validierung der Service-Initialisierung
   */
  protected ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error(`Service ${this.serviceName} is not initialized. Call initialize() first.`);
    }
  }
}