import React, { useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { useAuthContext } from '@/contexts/AuthContext';

/**
 * AuthenticatedIndex Component
 * 
 * Behandelt die Weiterleitung für die Root-Route ("/") basierend auf dem Authentifizierungsstatus:
 * - Nicht authentifiziert: Weiterleitung zu /login
 * - Authentifiziert: Weiterleitung zu /userlanding
 * 
 * Requirements: 1.1, 1.3, 2.1
 * - WHEN die Anwendung gestartet wird AND kein gültiger JWT-Token im localStorage vorhanden ist 
 *   THEN soll die Anwendung automatisch zur Login-Seite (/login) weiterleiten
 * - WHEN ein authentifizierter Benutzer die Anwendung neu startet AND ein gültiger Token vorhanden ist 
 *   THEN soll er direkt zur UserLandingPage weitergeleitet werden
 * - WHEN ein Benutzer sich erfolgreich anmeldet THEN soll er automatisch zur UserLandingPage (/) weitergeleitet werden
 */
export const AuthenticatedIndex: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuthContext();
  const navigate = useNavigate();

  useEffect(() => {
    // Warte bis der Authentifizierungsstatus geladen ist
    if (isLoading) return;

    console.log('🏠 AuthenticatedIndex - Authentifizierungsstatus:', { isAuthenticated });

    if (!isAuthenticated) {
      // Nicht authentifiziert - Weiterleitung zur Login-Seite
      console.log('🔒 Nicht authentifiziert auf Root-Route - Weiterleitung zur Login-Seite');
      navigate({ to: '/login' });
    } else {
      // Authentifiziert - Weiterleitung zur Startseite (UserLandingPage)
      console.log('✅ Authentifiziert auf Root-Route - Weiterleitung zur Startseite');
      navigate({ to: '/' });
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Zeige Loading-Spinner während der Authentifizierungsprüfung
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-lg">Authentifizierung wird geprüft...</span>
      </div>
    );
  }

  // Während der Weiterleitung zeige einen kurzen Loading-Zustand
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span className="ml-3 text-sm">Weiterleitung...</span>
    </div>
  );
};