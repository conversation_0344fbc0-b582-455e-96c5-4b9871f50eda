# Ask JASZ Button Design Guide

## 🎨 Einheitliches Design

Alle Ask JASZ Buttons verwenden jetzt ein konsistentes, professionelles Design mit dem offiziellen JASZ Logo und durchgängigen Tooltips.

## 🖼️ JASZ Logo Integration

### Logo-Verwendung
- **Quelle**: `@/assets/jasz-logo.png`
- **Darstellung**: Goldenes Gehirn-Symbol mit Circuit-Patterns
- **Größen**: Automatische Anpassung je nach Button-Größe
- **Animation**: Subtile 12° Rotation bei Hover

### Visual Features
- **Hover-Effekte**: Sanft<PERSON> Glow und Ring-Effekt
- **Scale-Animation**: 5% Vergrößerung bei Hover/Focus
- **Moderne Shadows**: Dynamische Schatten-Effekte

## 🎯 Button Varianten

### 1. De<PERSON>ult (Haupt-Buttons)
```tsx
variant="default"
```
- **Verwendung**: <PERSON>upt-<PERSON>iten (Headers)
- **Style**: Gradient Amber-Orange mit weißem Text
- **Shadow**: <PERSON><PERSON><PERSON> Schat<PERSON> für Aufmerksamkeit
- **Mit Label**: <PERSON><PERSON>gt "Ask JASZ" Text

### 2. Minimal (Tab-Buttons)
```tsx
variant="minimal"
```
- **Verwendung**: Tab-Bereiche und Sektionen
- **Style**: Heller Hintergrund mit Orange-Akzent
- **Shadow**: Subtile Schatten
- **Dezent**: Stört nicht den Content-Flow

### 3. Compact (KPI-Cards)
```tsx
variant="compact"
```
- **Verwendung**: KPI-Karten und kleine Komponenten
- **Style**: Orange-Tint mit minimaler Hervorhebung
- **Size**: Hauptsächlich `sm` Größe
- **Hover-Only**: Erscheint bei Hover auf KPI-Cards

## 📏 Größen

### Small (`sm`)
- **Button**: 8x8 px (ohne Label), 8px Höhe + Padding (mit Label)
- **Logo**: 4x4 px
- **Verwendung**: KPI-Cards, kompakte Bereiche

### Medium (`md`) - Standard
- **Button**: 10x10 px (ohne Label), 10px Höhe + Padding (mit Label)
- **Logo**: 5x5 px
- **Verwendung**: Normale Buttons, Tab-Bereiche

### Large (`lg`)
- **Button**: 12x12 px (ohne Label), 12px Höhe + Padding (mit Label)
- **Logo**: 7x7 px
- **Verwendung**: Prominente Haupt-Buttons

## 🏷️ Konsistente Tooltips

### Alle Buttons haben Tooltips
- **Immer verfügbar**: Jeder Ask JASZ Button hat einen Tooltip
- **Konsistentes Design**: Dunkler Gradient mit JASZ Logo
- **Informativ**: Erklärt, was der Button macht

### Tooltip-Struktur
```
┌─────────────────────────────┐
│ 🧠 Ask JASZ                │
│ Kontext-spezifische Hilfe   │
└─────────────────────────────┘
```

### Standard-Tooltip-Texte
- **Seiten**: "Lass dir [Seitenname] erklären und erfahre, welche Funktionen verfügbar sind"
- **Tabs**: "Lass dir [Tabname] erklären und erfahre, was du hier machen kannst"
- **KPIs**: "Lass dir [KPI-Name] erklären - was bedeutet der Wert [Wert] und wie wird er berechnet?"

## 📍 Positionierung

### Inline (mit Label)
```tsx
position="inline"
showLabel={true}
```
- **Verwendung**: Header-Bereiche
- **Integration**: Teil des normalen UI-Flows
- **Sichtbarkeit**: Immer sichtbar

### Positioned (ohne Label)
```tsx
position="top-right" // oder bottom-right, top-left, bottom-left
```
- **Verwendung**: Floating über Content
- **Position**: Absolute Positionierung
- **Z-Index**: Hoch für Sichtbarkeit

### Action Button (in Cards)
```tsx
// In SubtlePatternCard als actionButton prop
```
- **Verwendung**: KPI-Cards
- **Verhalten**: Erscheint bei Hover
- **Smooth**: Opacity-Transition

## 🚀 Implementation Examples

### Header Button (Haupt-Seiten)
```tsx
<AskJaszButton
  context={createPageContext("Seitenname", [...funktionen])}
  template={PROMPT_TEMPLATES.PAGE_EXPLANATION}
  position="inline"
  variant="default"
  showLabel={true}
  size="md"
  tooltip="Lass dir die Seite erklären..."
/>
```

### Tab Button (Bereiche)
```tsx
<AskJaszButton
  context={getTabContext()}
  template={PROMPT_TEMPLATES.PAGE_EXPLANATION}
  position="top-right"
  variant="minimal"
  size="md"
  tooltip="Lass dir diesen Bereich erklären..."
/>
```

### KPI Button (Metriken)
```tsx
<AskJaszButton
  context={createKpiContext(label, value, description)}
  template={PROMPT_TEMPLATES.KPI_EXPLANATION}
  position="inline"
  variant="compact"
  size="sm"
  tooltip="Lass dir diese Metrik erklären..."
/>
```

## 🎯 Design Prinzipien

### Konsistenz
- **Einheitliches Logo**: Überall das gleiche JASZ Logo
- **Konsistente Tooltips**: Alle Buttons haben hilfreiche Tooltips
- **Vorhersagbares Verhalten**: Gleiche Interaktionen überall

### Accessibility
- **Hover States**: Klare visuele Feedback
- **Focus States**: Keyboard-Navigation unterstützt
- **Alt-Text**: Logo hat beschreibenden Alt-Text
- **Screen Reader**: Tooltips sind screen-reader-freundlich

### Performance
- **Optimierte Animationen**: 60fps Transitions
- **Lazy Loading**: Logo wird effizient geladen
- **Minimal Bundle**: Keine unnötigen Dependencies

### User Experience
- **Non-Intrusive**: Stört nicht den normalen Workflow
- **Progressive Disclosure**: KPI-Buttons nur bei Bedarf
- **Contextual**: Buttons passen zum Inhalt
- **Discoverable**: Leicht zu finden und zu verstehen

## 🔄 Migration Guide

### Alte Buttons aktualisieren
1. **Import**: `import { AskJaszButton } from "@/modules/ai/components"`
2. **Varianten**: Ersetze `outline`, `ghost` → `minimal` oder `compact`
3. **Tooltips**: Füge aussagekräftige `tooltip` Props hinzu
4. **Logo**: Entferne Custom `icon` Props (Logo wird automatisch verwendet)

### Best Practices
- **Sinnvolle Tooltips**: Erkläre, was passiert, nicht nur "Ask JASZ"
- **Passende Varianten**: Default für Haupt-Buttons, Minimal für Tabs, Compact für Cards
- **Kontext**: Verwende die richtigen Context-Builder-Funktionen
- **Responsive**: Teste auf verschiedenen Bildschirmgrößen

Das neue einheitliche Design sorgt für eine professionelle, konsistente Ask JASZ-Erfahrung in der gesamten Anwendung! 🎉