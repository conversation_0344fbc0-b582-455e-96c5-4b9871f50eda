"use strict";
/**
 * Debug-Test für spezifische Datumsabfrage
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const data_enrichment_service_1 = require("../services/data-enrichment.service");
const client_1 = require("@prisma/client");
async function debugSpecificDateQuery() {
    console.log('🔍 Debug: Specific Date Query for 17.04.2025\n');
    const prisma = new client_1.PrismaClient();
    const service = new data_enrichment_service_1.DataEnrichmentService(prisma);
    try {
        const query = 'Servicegrad vom 17.04.2025';
        console.log(`Query: "${query}"`);
        // Step 1: Test intent parsing
        console.log('\n1️⃣ INTENT PARSING:');
        const intents = service.parseIntent(query);
        console.log('Detected intents:', intents);
        // Step 2: Test time range parsing
        console.log('\n2️⃣ TIME RANGE PARSING:');
        const timeRange = service.parseTimeRange(query);
        console.log('Detected time range:', timeRange);
        // Step 3: Test full enrichment
        console.log('\n3️⃣ FULL ENRICHMENT:');
        const enrichedContext = await service.enrichChatContext(query);
        console.log('Enriched Context:');
        console.log('- Has Data:', enrichedContext.hasData);
        console.log('- Data Types:', enrichedContext.dataTypes);
        console.log('- Detected Intents:', enrichedContext.detectedIntents.length);
        if (enrichedContext.detectedIntents.length > 0) {
            const intent = enrichedContext.detectedIntents[0];
            console.log('- Intent Type:', intent.type);
            console.log('- Time Range:', intent.timeRange);
            console.log('- Keywords:', intent.keywords);
        }
        console.log('- Database Context Length:', enrichedContext.databaseContext.length);
        console.log('- Database Context Preview:', enrichedContext.databaseContext.substring(0, 200) + '...');
        if (enrichedContext.fallbackReason) {
            console.log('- Fallback Reason:', enrichedContext.fallbackReason);
        }
        // Step 4: Test direct repository query
        console.log('\n4️⃣ DIRECT REPOSITORY QUERY:');
        const dispatchRepo = new (await Promise.resolve().then(() => __importStar(require('../repositories/dispatch.repository')))).DispatchRepositoryImpl(prisma);
        const serviceLevel = await dispatchRepo.getServiceLevelData({
            startDate: '2025-04-17',
            endDate: '2025-04-17'
        });
        console.log('Direct Service Level Query Result:', serviceLevel);
        const performanceMetrics = await dispatchRepo.getPerformanceMetrics({
            startDate: '2025-04-17',
            endDate: '2025-04-17'
        });
        console.log('Direct Performance Metrics:', performanceMetrics);
    }
    catch (error) {
        console.error('❌ Error:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
// Run the debug
debugSpecificDateQuery().catch(console.error);
