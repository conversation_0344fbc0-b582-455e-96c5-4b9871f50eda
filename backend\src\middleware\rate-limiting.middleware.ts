/**
 * Rate-Limiting-Middleware
 * 
 * Schützt die API vor DoS-Angriffen und übermäßiger Nutzung
 * durch IP-basierte Ratenbegrenzung.
 */

import rateLimit from 'express-rate-limit';
import express, { Request, Response } from 'express';

/**
 * Erstellt eine benutzerdefinierte Fehlerantwort für Rate-Limiting
 */
function createRateLimitResponse(req: Request, res: Response): void {
  console.warn(`[RATE-LIMIT] IP ${req.ip} hat das Rate-Limit für ${req.originalUrl} überschritten`);
  
  res.status(429).json({
    success: false,
    error: 'Zu viele Anfragen',
    message: 'Sie haben zu viele Anfragen in kurzer Zeit gesendet. Bitte versuchen Sie es später erneut.',
    code: 'RATE_LIMIT_EXCEEDED',
    retryAfter: Math.ceil(res.getHeader('Retry-After') as number || 60)
  });
}

/**
 * Basis-Rate-Limiter für allgemeine API-Endpunkte
 * Development: 1000 Anfragen pro 15 Minuten, Production: 100 Anfragen pro 15 Minuten pro IP
 */
export const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 Minuten
  max: process.env.NODE_ENV === 'development' ? 1000 : 100, // Development: 1000, Production: 100
  handler: createRateLimitResponse,
  standardHeaders: true, // Rate-Limit-Informationen in `RateLimit-*` Headers
  legacyHeaders: false, // Deaktiviere `X-RateLimit-*` Headers
  // Erweiterte Konfiguration
  keyGenerator: (req: Request) => {
    // Verwende X-Forwarded-For falls verfügbar (für Proxy-Umgebungen)
    return req.headers['x-forwarded-for'] as string || req.ip || 'anonymous';
  },
  skip: (req: Request) => {
    // Überspringe Rate-Limiting für Health-Checks in der Entwicklung
    if (process.env.NODE_ENV === 'development' && req.path === '/health') {
      return true;
    }
    return false;
  }
});

/**
 * Rate-Limiter für Daten-intensive Endpunkte
 * Development: 500 Anfragen pro 15 Minuten, Production: 30 Anfragen pro 15 Minuten pro IP
 */
export const dataEndpointRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 Minuten
  max: process.env.NODE_ENV === 'development' ? 500 : 30, // Development: 500, Production: 30
  handler: createRateLimitResponse,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.headers['x-forwarded-for'] as string || req.ip || 'anonymous';
  }
});

/**
 * Sehr strenger Rate-Limiter für Import-/Schreiboperationen
 * 5 Anfragen pro 15 Minuten pro IP
 */
export const writeOperationRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 Minuten
  max: 5, // Maximum 5 Anfragen pro Fenster
  handler: createRateLimitResponse,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.headers['x-forwarded-for'] as string || req.ip || 'anonymous';
  }
});

/**
 * Lockerer Rate-Limiter für Health-Checks
 * 1000 Anfragen pro 15 Minuten pro IP
 */
export const healthCheckRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 Minuten
  max: 1000, // Maximum 1000 Anfragen pro Fenster
  handler: createRateLimitResponse,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.headers['x-forwarded-for'] as string || req.ip || 'anonymous';
  }
});

/**
 * Rate-Limiter für Performance-Metriken-Endpunkte
 * Development: 200 Anfragen pro 15 Minuten, Production: 50 Anfragen pro 15 Minuten pro IP
 */
export const metricsEndpointRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 Minuten
  max: process.env.NODE_ENV === 'development' ? 200 : 50, // Development: 200, Production: 50
  handler: createRateLimitResponse,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.headers['x-forwarded-for'] as string || req.ip || 'anonymous';
  }
});

/**
 * Sehr strenger Rate-Limiter für nicht-authentifizierte Anfragen
 * 10 Anfragen pro 15 Minuten pro IP
 */
export const unauthenticatedRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 Minuten
  max: 10, // Maximum 10 Anfragen pro Fenster
  handler: createRateLimitResponse,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req: Request) => {
    return req.headers['x-forwarded-for'] as string || req.ip || 'anonymous';
  }
});

/**
 * Adaptive Rate-Limiting basierend auf Authentifizierungsstatus
 * Authentifizierte Benutzer erhalten höhere Limits
 */
export function createAdaptiveRateLimit() {
  return (req: Request, res: Response, next: express.NextFunction) => {
    // Überprüfe Authentifizierungsstatus
    const isAuthenticated = !!(req as any).user?.authenticated;
    
    if (isAuthenticated) {
      // Verwende allgemeines Rate-Limit für authentifizierte Benutzer
      return generalRateLimit(req, res, next);
    } else {
      // Verwende strengeres Limit für nicht-authentifizierte Benutzer
      return unauthenticatedRateLimit(req, res, next);
    }
  };
}

/**
 * Rate-Limiting-Konfiguration für verschiedene Endpunkt-Typen
 */
export const rateLimitConfig = {
  general: generalRateLimit,
  dataEndpoint: dataEndpointRateLimit,
  writeOperation: writeOperationRateLimit,
  healthCheck: healthCheckRateLimit,
  metricsEndpoint: metricsEndpointRateLimit,
  unauthenticated: unauthenticatedRateLimit,
  adaptive: createAdaptiveRateLimit()
};