"use strict";
/**
 * RAG (Retrieval-Augmented Generation) Type Definitions
 *
 * Defines interfaces for vector embeddings, document management,
 * and similarity search functionality
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RAGErrorCode = void 0;
// Error Types
var RAGErrorCode;
(function (RAGErrorCode) {
    RAGErrorCode["DOCUMENT_NOT_FOUND"] = "DOCUMENT_NOT_FOUND";
    RAGErrorCode["KNOWLEDGE_BASE_NOT_FOUND"] = "KNOWLEDGE_BASE_NOT_FOUND";
    RAGErrorCode["EMBEDDING_GENERATION_FAILED"] = "EMBEDDING_GENERATION_FAILED";
    RAGErrorCode["VECTOR_STORAGE_FAILED"] = "VECTOR_STORAGE_FAILED";
    RAGErrorCode["SIMILARITY_SEARCH_FAILED"] = "SIMILARITY_SEARCH_FAILED";
    RAGErrorCode["CHUNKING_FAILED"] = "CHUNKING_FAILED";
    RAGErrorCode["INVALID_VECTOR_DIMENSIONS"] = "INVALID_VECTOR_DIMENSIONS";
    RAGErrorCode["DUPLICATE_DOCUMENT"] = "DUPLICATE_DOCUMENT";
    RAGErrorCode["INSUFFICIENT_CONTEXT"] = "INSUFFICIENT_CONTEXT";
})(RAGErrorCode || (exports.RAGErrorCode = RAGErrorCode = {}));
