/**
 * CuttingAnalysisSection
 *
 * Zeigt die Verschnitt-Analyse oder eine Hinweiskarte,
 * falls noch keine Analyse vorhanden ist. Präsentations-Component.
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';
import { ChartErrorBoundary } from '@/components/ErrorBoundary';
import { WasteAnalysisChart } from './WasteAnalysisChart';
import type { WasteAnalysis, CuttingPlan } from '../../services/types';

interface CuttingAnalysisSectionProps {
  wasteAnalysis: WasteAnalysis | null;
  plan: CuttingPlan | null;
}

export function CuttingAnalysisSection({ wasteAnalysis, plan }: CuttingAnalysisSectionProps) {
  return (
    <div className="space-y-6">
      {wasteAnalysis ? (
        <ChartErrorBoundary>
          <WasteAnalysisChart wasteAnalysis={wasteAnalysis} plan={plan} />
        </ChartErrorBoundary>
      ) : (
        <Card className="border-[#ff7a05]">
          <CardContent className="flex flex-col items-center justify-center h-64">
            <AlertTriangle className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-600">Führen Sie eine Optimierung durch, um die Analyse zu sehen</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default CuttingAnalysisSection;
