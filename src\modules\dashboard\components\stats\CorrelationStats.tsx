import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StatCard } from '@/components/ui/stat-card';

/**
 * Korrelationsanalyse-Statistiken Komponente
 * 
 * Zeigt wichtige Korrelationen zwischen verschiedenen Kennzahlen als Statistik-Karten an:
 * - Servicegrad vs. Rückstände
 * - Produktivität vs. Qualität
 * - Füllgrad ARIL vs. Servicegrad
 */
export function CorrelationStats() {
  const { t } = useTranslation();
  const [stats, setStats] = useState({
    serviceLevelBacklogCorrelation: 0,
    productivityQualityCorrelation: 0,
    fillLevelServiceCorrelation: 0,
  });
  
  useEffect(() => {
    const loadData = () => {
      try {
        // In einer echten Implementierung würden wir die Korrelationen aus den Daten berechnen
        // Hier verwenden wir Beispielwerte
        
        // Korrelation zwischen Servicegrad und Rückständen (-1 bis 1)
        // Negative Korrelation bedeutet: Je höher der Servicegrad, desto niedriger die Rückstände
        const serviceLevelBacklogCorrelation = -0.78;
        
        // Korrelation zwischen Produktivität und Qualität (-1 bis 1)
        // Negative Korrelation bedeutet: Je höher die Produktivität, desto niedriger die Qualität
        const productivityQualityCorrelation = -0.32;
        
        // Korrelation zwischen Füllgrad ARIL und Servicegrad (-1 bis 1)
        // Negative Korrelation bedeutet: Je höher der Füllgrad, desto niedriger der Servicegrad
        const fillLevelServiceCorrelation = -0.65;
        
        setStats({
          serviceLevelBacklogCorrelation,
          productivityQualityCorrelation,
          fillLevelServiceCorrelation,
        });
      } catch (err) {
        // Error handling without console logging
      }
    };
    
    loadData();
  }, []);
  
  // Funktion zur Bestimmung der Stärke der Korrelation
  const getCorrelationStrength = (correlation: number) => {
    const absCorrelation = Math.abs(correlation);
    if (absCorrelation >= 0.7) return t('strong');
    if (absCorrelation >= 0.3) return t('moderate');
    return t('weak');
  };
  
  // Funktion zur Bestimmung der Farbe basierend auf der Korrelation
  const getCorrelationClass = (correlation: number) => {
    if (correlation <= -0.7) return 'text-red-500';
    if (correlation <= -0.3) return 'text-orange-500';
    if (correlation < 0.3) return 'text-gray-500';
    if (correlation < 0.7) return 'text-blue-500';
    return 'text-green-500';
  };
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <StatCard
        title={t("serviceLevelBacklogCorrelation")}
        value={stats.serviceLevelBacklogCorrelation.toFixed(2)}
        description={t("serviceLevelBacklogCorrelationDesc")}
        footer={
          <span className={getCorrelationClass(stats.serviceLevelBacklogCorrelation)}>
            {getCorrelationStrength(stats.serviceLevelBacklogCorrelation)} {stats.serviceLevelBacklogCorrelation < 0 ? t('negativeCorrelation') : t('positiveCorrelation')}
          </span>
        }
      />
      <StatCard
        title={t("productivityQualityCorrelation")}
        value={stats.productivityQualityCorrelation.toFixed(2)}
        description={t("productivityQualityCorrelationDesc")}
        footer={
          <span className={getCorrelationClass(stats.productivityQualityCorrelation)}>
            {getCorrelationStrength(stats.productivityQualityCorrelation)} {stats.productivityQualityCorrelation < 0 ? t('negativeCorrelation') : t('positiveCorrelation')}
          </span>
        }
      />
      <StatCard
        title={t("fillLevelServiceCorrelation")}
        value={stats.fillLevelServiceCorrelation.toFixed(2)}
        description={t("fillLevelServiceCorrelationDesc")}
        footer={
          <span className={getCorrelationClass(stats.fillLevelServiceCorrelation)}>
            {getCorrelationStrength(stats.fillLevelServiceCorrelation)} {stats.fillLevelServiceCorrelation < 0 ? t('negativeCorrelation') : t('positiveCorrelation')}
          </span>
        }
      />
    </div>
  );
}
