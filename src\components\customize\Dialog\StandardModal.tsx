import React, { ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import './dialog.css';

export interface StandardDialogProps {
  open: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
  maxWidth?: string;
}

/**
 * Standard Dialog Component - MIT EIGENEN CSS-KLASSEN
 * 
 * Verwendung:
 * <StandardDialog open={isOpen} onClose={() => setIsOpen(false)} title="Titel">
 *   <div>Inhalt hier</div>
 * </StandardDialog>
 */
export const StandardDialog: React.FC<StandardDialogProps> = ({
  open,
  onClose,
  title,
  children,
  maxWidth = 'max-w-4xl'
}) => {
  if (!open) return null;

  return (
    <div className="dialog-overlay" onClick={onClose}>
      {/* Dialog */}
      <div 
        className={`dialog-content ${document.documentElement.classList.contains('dark') ? 'dark' : ''}`}
        onClick={(e) => e.stopPropagation()}
        style={{ maxWidth: maxWidth === 'max-w-4xl' ? '56rem' : maxWidth }}
      >
        {/* Header */}
        <div className={`dialog-header ${document.documentElement.classList.contains('dark') ? 'dark' : ''}`}>
          <h2 className={`dialog-title ${document.documentElement.classList.contains('dark') ? 'dark' : ''}`}>{title}</h2>
          <button 
            className={`dialog-close-button ${document.documentElement.classList.contains('dark') ? 'dark' : ''}`}
            onClick={onClose}
          >
            <X className={`dialog-close-icon ${document.documentElement.classList.contains('dark') ? 'dark' : ''}`} />
          </button>
        </div>

        {/* Content */}
        <div className="dialog-body">
          {children}
        </div>
      </div>
    </div>
  );
};

export default StandardDialog;