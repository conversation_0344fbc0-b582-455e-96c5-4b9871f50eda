"use client"

import { useEffect, useId, useRef } from "react"

import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// Props Interface für die TrommelRadioComp-Komponente
interface TrommelRadioCompProps {
  value?: string;
  onChange?: (value: string) => void;
  trommeln?: any[];
  selectedTrommel?: string;
  onTrommelChange?: (value: string) => void;
}

export default function TrommelRadioComp({ 
  value = "without-expansion", 
  onChange, 
  trommeln = [],
  selectedTrommel = "",
  onTrommelChange 
}: TrommelRadioCompProps) {
  const radioId = useId()
  const selectId = useId()

  // Verwende die übergebenen Props anstelle des lokalen State
  const selectedValue = value;
  const setSelectedValue = (newValue: string) => {
    onChange?.(newValue);
  };

  const handleValueChange = (value: string) => {
    setSelectedValue(value);
    onChange?.(value);
  };

  return (
    <div className="w-full">
      <RadioGroup value={selectedValue} onValueChange={handleValueChange} className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Linke Seite: Aus Trommelliste auswählen */}
        <div className="relative">
          <div 
            className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
              selectedValue === "with-expansion" 
                ? "border-primary bg-white shadow-sm" 
                : "border-border hover:border-primary/50"
            }`}
            onClick={() => handleValueChange("with-expansion")}
          >
            <div className="flex items-center space-x-2 mb-2">
              <RadioGroupItem value="with-expansion" id={`${radioId}-with-expansion`} />
              <Label htmlFor={`${radioId}-with-expansion`} className="text-sm font-medium cursor-pointer">
                Aus Stammdaten
              </Label>
            </div>
            <div className="text-xs text-muted-foreground mb-3">
              Trommeldaten aus den Stammdaten laden.
            </div>

            {/* Trommelliste - immer sichtbar */}
            <div className="mt-3">
              <div>
                <div className="pointer-events-none -m-2 overflow-hidden p-2">
                   <div className="pointer-events-auto">
                      <Select 
                        value={selectedTrommel} 
                        onValueChange={onTrommelChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Trommel auswählen" />
                        </SelectTrigger>
                        <SelectContent>
                          {trommeln.map((trommel) => (
                            <SelectItem key={trommel.Trommelname} value={trommel.Trommelname}>
                              {trommel.Trommelname} (Ø{trommel.Außendurchmesser}mm)
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                 </div>
               </div>
             </div>
           </div>
         </div>

        {/* Rechte Seite: Manuelle Eingabe */}
        <div 
          className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
            selectedValue === "without-expansion" 
              ? "border-primary bg-white shadow-sm" 
              : "border-border hover:border-primary/50"
          }`}
          onClick={() => handleValueChange("without-expansion")}
        >
          <div className="flex items-center space-x-2 mb-2">
            <RadioGroupItem value="without-expansion" id={`${radioId}-without-expansion`} />
            <Label htmlFor={`${radioId}-without-expansion`} className="text-sm font-medium cursor-pointer">
              Individuelle Eingabe
            </Label>
          </div>
          <div className="text-xs text-muted-foreground">
            Ermöglicht eine manuelle Eingabe der Trommeldaten.
          </div>
        </div>
      </RadioGroup>
    </div>
  )
}