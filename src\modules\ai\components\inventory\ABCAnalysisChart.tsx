import React, { memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, PieChart, Pie, Cell, ResponsiveContainer } from 'recharts';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, Package, DollarSign, AlertTriangle } from 'lucide-react';
import { ABCClassification, ABCAnalysisResult } from '../../services/inventory/types';

interface ABCAnalysisChartProps {
  classification: ABCClassification;
  analysisResults?: ABCAnalysisResult[];
  onItemSelect?: (itemId: string) => void;
}

/**
 * ABC Analysis Chart Component
 * 
 * Displays comprehensive ABC analysis visualization including:
 * - Class distribution pie chart
 * - Value contribution bar chart
 * - Classification summary cards
 * - Reclassification alerts
 */
export const ABCAnalysisChart = memo(function ABCAnalysisChart({
  classification,
  analysisResults = [],
  onItemSelect
}: ABCAnalysisChartProps) {

  // Prepare data for class distribution pie chart
  const classDistributionData = [
    {
      name: 'Klasse A',
      value: classification.classA.length,
      percentage: Math.round((classification.classA.length / (classification.classA.length + classification.classB.length + classification.classC.length)) * 100),
      color: 'var(--chart-1)'
    },
    {
      name: 'Klasse B',
      value: classification.classB.length,
      percentage: Math.round((classification.classB.length / (classification.classA.length + classification.classB.length + classification.classC.length)) * 100),
      color: 'var(--chart-2)'
    },
    {
      name: 'Klasse C',
      value: classification.classC.length,
      percentage: Math.round((classification.classC.length / (classification.classA.length + classification.classB.length + classification.classC.length)) * 100),
      color: 'var(--chart-3)'
    }
  ];

  // Prepare data for value contribution chart
  const valueContributionData = analysisResults.slice(0, 20).map(result => ({
    itemId: result.itemId.length > 15 ? result.itemId.substring(0, 15) + '...' : result.itemId,
    fullItemId: result.itemId,
    value: Math.round(result.value),
    percentage: Math.round(result.percentage * 100) / 100,
    cumulativePercentage: Math.round(result.cumulativePercentage * 100) / 100,
    classification: result.classification,
    color: result.classification === 'A' ? 'var(--chart-1)' :
      result.classification === 'B' ? 'var(--chart-2)' : 'var(--chart-3)'
  }));

  // Calculate summary metrics
  const totalItems = classification.classA.length + classification.classB.length + classification.classC.length;
  const totalValue = analysisResults.reduce((sum, result) => sum + result.value, 0);
  const classAValue = analysisResults.filter(r => r.classification === 'A').reduce((sum, result) => sum + result.value, 0);
  const classAValuePercentage = totalValue > 0 ? Math.round((classAValue / totalValue) * 100) : 0;

  // Chart configuration
  const chartConfig = {
    value: {
      label: "Wert (€)",
      color: "var(--chart-1)",
    },
    percentage: {
      label: "Anteil (%)",
      color: "var(--chart-2)",
    },
    cumulative: {
      label: "Kumulativ (%)",
      color: "var(--chart-3)",
    }
  };

  // Colors for pie chart
  const COLORS = ['var(--chart-1)', 'var(--chart-2)', 'var(--chart-3)'];

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-[#ff7a05]">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Package className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Gesamt Artikel</p>
                <p className="text-2xl font-bold text-blue-600">{totalItems}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#ff7a05]">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Klasse A Artikel</p>
                <p className="text-2xl font-bold text-green-600">
                  {classification.classA.length}
                </p>
                <p className="text-xs text-gray-500">
                  {classDistributionData[0].percentage}% der Artikel
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#ff7a05]">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Klasse A Wert</p>
                <p className="text-2xl font-bold text-purple-600">{classAValuePercentage}%</p>
                <p className="text-xs text-gray-500">
                  {Math.round(classAValue).toLocaleString()} €
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#ff7a05]">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Umklassifizierungen</p>
                <p className="text-2xl font-bold text-orange-600">
                  {classification.reclassifications.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Class Distribution Pie Chart */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle>Klassenverteilung</CardTitle>
            <CardDescription>
              Verteilung der Artikel nach ABC-Klassen
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={classDistributionData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    stroke="#000000"
                    strokeWidth={2}
                  >
                    {classDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <ChartTooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        const data = payload[0].payload;
                        return (
                          <div className="bg-white p-2 border border-gray-300 rounded shadow">
                            <p className="font-medium">{data.name}</p>
                            <p className="text-sm">Artikel: {data.value} ({data.percentage}%)</p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Value Contribution Bar Chart */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle>Wertbeitrag Top 20</CardTitle>
            <CardDescription>
              Artikel mit höchstem Wertbeitrag
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <BarChart data={valueContributionData} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis
                  dataKey="itemId"
                  className="text-xs font-bold"
                  tick={{ fontSize: 10 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis
                  className="text-xs font-bold"
                  tick={{ fill: "#000000" }}
                  label={{
                    value: "Wert (€)",
                    angle: -90,
                    position: "insideLeft",
                    style: { textAnchor: "middle", fontSize: 12 }
                  }}
                />
                <ChartTooltip
                  content={
                    <ChartTooltipContent
                      labelFormatter={(label, payload) => {
                        const item = payload?.[0]?.payload;
                        return `Artikel: ${item?.fullItemId || label}`;
                      }}
                      formatter={(value, name, props) => [
                        name === 'value' ? `${value.toLocaleString()} €` : `${value}%`,
                        name === 'value' ? 'Wert' :
                          name === 'percentage' ? 'Anteil' : 'Kumulativ'
                      ]}
                    />
                  }
                />
                <Bar
                  dataKey="value"
                  name="Wert"
                  stroke="#000000"
                  strokeWidth={1}
                  radius={[2, 2, 0, 0]}
                  onClick={(data) => onItemSelect?.(data.fullItemId)}
                  style={{ cursor: onItemSelect ? 'pointer' : 'default' }}
                >
                  {valueContributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Classification Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Class A Details */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Badge variant="default" className="bg-green-100 text-green-800">A</Badge>
              <span>Klasse A Artikel</span>
            </CardTitle>
            <CardDescription>
              Hochwertige Artikel ({classification.classA.length} Stück)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {classification.classA.slice(0, 10).map((item, index) => (
                <div
                  key={item.id}
                  className="flex justify-between items-center p-2 bg-green-50 rounded cursor-pointer hover:bg-green-100"
                  onClick={() => onItemSelect?.(item.id)}
                >
                  <div>
                    <p className="text-sm font-medium">{item.name}</p>
                    <p className="text-xs text-gray-600">{item.category}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold">{Math.round(item.currentStock * item.unitPrice).toLocaleString()} €</p>
                    <p className="text-xs text-gray-600">{item.currentStock} Stk.</p>
                  </div>
                </div>
              ))}
              {classification.classA.length > 10 && (
                <p className="text-xs text-gray-500 text-center">
                  ... und {classification.classA.length - 10} weitere
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Class B Details */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">B</Badge>
              <span>Klasse B Artikel</span>
            </CardTitle>
            <CardDescription>
              Mittelwertige Artikel ({classification.classB.length} Stück)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {classification.classB.slice(0, 10).map((item, index) => (
                <div
                  key={item.id}
                  className="flex justify-between items-center p-2 bg-yellow-50 rounded cursor-pointer hover:bg-yellow-100"
                  onClick={() => onItemSelect?.(item.id)}
                >
                  <div>
                    <p className="text-sm font-medium">{item.name}</p>
                    <p className="text-xs text-gray-600">{item.category}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold">{Math.round(item.currentStock * item.unitPrice).toLocaleString()} €</p>
                    <p className="text-xs text-gray-600">{item.currentStock} Stk.</p>
                  </div>
                </div>
              ))}
              {classification.classB.length > 10 && (
                <p className="text-xs text-gray-500 text-center">
                  ... und {classification.classB.length - 10} weitere
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Class C Details */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Badge variant="outline" className="bg-gray-100 text-gray-800">C</Badge>
              <span>Klasse C Artikel</span>
            </CardTitle>
            <CardDescription>
              Niederwertige Artikel ({classification.classC.length} Stück)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {classification.classC.slice(0, 10).map((item, index) => (
                <div
                  key={item.id}
                  className="flex justify-between items-center p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100"
                  onClick={() => onItemSelect?.(item.id)}
                >
                  <div>
                    <p className="text-sm font-medium">{item.name}</p>
                    <p className="text-xs text-gray-600">{item.category}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold">{Math.round(item.currentStock * item.unitPrice).toLocaleString()} €</p>
                    <p className="text-xs text-gray-600">{item.currentStock} Stk.</p>
                  </div>
                </div>
              ))}
              {classification.classC.length > 10 && (
                <p className="text-xs text-gray-500 text-center">
                  ... und {classification.classC.length - 10} weitere
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reclassifications Alert */}
      {classification.reclassifications.length > 0 && (
        <Card className="border-orange-500 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-orange-800">
              <AlertTriangle className="h-5 w-5" />
              <span>Umklassifizierungen</span>
            </CardTitle>
            <CardDescription className="text-orange-700">
              {classification.reclassifications.length} Artikel wurden umklassifiziert
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {classification.reclassifications.map((change, index) => (
                <div key={index} className="flex justify-between items-center p-2 bg-white rounded">
                  <div>
                    <p className="text-sm font-medium">{change.itemId}</p>
                    <p className="text-xs text-gray-600">{change.reason}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {change.previousClass} → {change.newClass}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {Math.round(change.confidence * 100)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
});