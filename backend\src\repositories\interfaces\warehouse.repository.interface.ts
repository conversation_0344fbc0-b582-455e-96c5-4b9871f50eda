/**
 * Warehouse Repository Interface
 * 
 * Repository-Schnittstelle für Lager-/Warehouse-Daten
 * einschließlich Auslastung und Bestandsdaten.
 */

import { BaseRepository } from './base.repository.interface';
import { DateRange } from './dispatch.repository.interface';
import {
  Lagerauslastung200DataPoint,
  Lagerauslastung240DataPoint,
  ArilDataPoint,
  AtrlDataPoint
} from '../../types/database.types';

import { RepositoryStats } from './base.repository.interface';

export interface WarehouseRepository {
  /**
   * Lagerauslastung 200 Daten
   */
  getLagerauslastung200Data(dateRange?: DateRange): Promise<Lagerauslastung200DataPoint[]>;

  /**
   * Lagerauslastung 240 Daten
   */
  getLagerauslastung240Data(dateRange?: DateRange): Promise<Lagerauslastung240DataPoint[]>;

  /**
   * ARiL <PERSON>daten
   */
  getArilData(dateRange?: DateRange): Promise<ArilDataPoint[]>;

  /**
   * ATrL <PERSON>daten
   */
  getAtrlData(dateRange?: DateRange): Promise<AtrlDataPoint[]>;

  /**
   * Bestandsübersicht für alle Lager
   */
  getWarehouseOverview(): Promise<WarehouseOverview>;

  /**
   * Auslastungstrends über Zeit
   */
  getCapacityTrends(
    warehouseType: '200' | '240',
    dateRange: DateRange
  ): Promise<CapacityTrend[]>;

  /**
   * Kritische Auslastungswerte ermitteln
   */
  getCriticalCapacityAlerts(threshold?: number): Promise<CapacityAlert[]>;

  /**
   * Repository-Statistiken abrufen
   */
  getStats(): Promise<RepositoryStats>;

  /**
   * Repository-Cache invalidieren
   */
  invalidateCache(key?: string): Promise<void>;
}

/**
 * Warehouse-Übersicht
 */
export interface WarehouseOverview {
  warehouse200: {
    currentCapacity: number;
    maxCapacity: number;
    utilizationRate: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  };
  warehouse240: {
    currentCapacity: number;
    maxCapacity: number;
    utilizationRate: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  };
  aril: {
    totalPositions: number;
    filledPositions: number;
    fillRate: number;
    lastUpdate: string;
  };
  atrl: {
    totalPositions: number;
    filledPositions: number;
    fillRate: number;
    lastUpdate: string;
  };
}

/**
 * Kapazitätstrend
 */
export interface CapacityTrend {
  date: string;
  capacity: number;
  utilizationRate: number;
  changeFromPrevious: number;
}

/**
 * Kapazitäts-Alert
 */
export interface CapacityAlert {
  warehouseType: string;
  currentUtilization: number;
  threshold: number;
  severity: 'warning' | 'critical';
  recommendation: string;
  date: string;
}