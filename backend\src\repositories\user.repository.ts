import { PrismaClient, User } from '@prisma-sfm-dashboard/client';

export class UserRepository {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async findByEmailOrUsername(email: string, username: string): Promise<User | null> {
    return await this.prisma.user.findFirst({
      where: {
        OR: [
          { email: email },
          { username: username }
        ]
      }
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    return await this.prisma.user.findUnique({
      where: { email }
    });
  }

  async findByUsername(username: string): Promise<User | null> {
    return await this.prisma.user.findUnique({
      where: { username },
      include: { roles: true } // Rollen mit laden
    });
  }

  async createUser(userData: {
    email: string;
    username: string;
    name?: string; // Optionales Feld für den Namen
    passwordHash: string;
  }): Promise<User> {
    return await this.prisma.user.create({
      data: userData
    });
  }

  async findById(id: number): Promise<User | null> {
    return await this.prisma.user.findUnique({
      where: { id },
      include: { roles: true } // Rollen mit laden
    });
  }
}