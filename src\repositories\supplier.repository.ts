/**
 * Supplier Repository
 * 
 * Repository for managing supplier data and performance metrics
 */

import { BaseRepository } from './base.repository';
import { Supplier, SupplierPerformanceMetrics } from '@/types/supply-chain-optimization';

export interface SupplierStats {
  totalSuppliers: number;
  activeSuppliers: number;
  averagePerformanceScore: number;
  topPerformers: number;
  riskSuppliers: number;
}

export interface SupplierPerformanceData {
  supplierId: string;
  onTimeDeliveryRate: number;
  qualityRate: number;
  responseTime: number;
  financialStabilityScore: number;
  reliabilityScore: number;
  costCompetitiveness: number;
  totalOrders: number;
  totalValue: number;
  lastOrderDate: Date;
  performanceTrend: 'improving' | 'stable' | 'declining';
}

/**
 * Repository for supplier data management
 */
export class SupplierRepository extends BaseRepository {
  protected repositoryName = 'SupplierRepository';
  protected logger = { 
    info: (msg: string, data?: any) => console.log(`[${this.repositoryName}] ${msg}`, data),
    error: (msg: string, data?: any) => console.error(`[${this.repositoryName}] ${msg}`, data)
  };

  constructor() {
    super();
  }

  /**
   * Get all suppliers (required by base repository)
   */
  async getAll(): Promise<Supplier[]> {
    return this.getAllSuppliers();
  }

  /**
   * Get overall supplier statistics
   */
  async getSupplierStats(): Promise<SupplierStats> {
    try {
      // Mock implementation - in real app, this would query the database
      // For now, return realistic mock data
      return {
        totalSuppliers: 45,
        activeSuppliers: 38,
        averagePerformanceScore: 7.2,
        topPerformers: 12,
        riskSuppliers: 3
      };
    } catch (error) {
      this.logger.error('Failed to get supplier stats:', error);
      throw new Error(`Failed to get supplier stats: ${error}`);
    }
  }

  /**
   * Get supplier performance data
   */
  async getSupplierPerformance(
    supplierId: string, 
    timeRange: { days: number }
  ): Promise<SupplierPerformanceData> {
    try {
      this.logger.info(`Getting performance data for supplier ${supplierId}`);
      
      // Mock implementation - generate realistic performance data
      const performanceData: SupplierPerformanceData = {
        supplierId,
        onTimeDeliveryRate: 0.75 + Math.random() * 0.25, // 75-100%
        qualityRate: 0.90 + Math.random() * 0.10, // 90-100%
        responseTime: Math.random() * 24 + 2, // 2-26 hours
        financialStabilityScore: Math.floor(Math.random() * 4) + 6, // 6-10
        reliabilityScore: Math.floor(Math.random() * 3) + 7, // 7-10
        costCompetitiveness: Math.floor(Math.random() * 4) + 6, // 6-10
        totalOrders: Math.floor(Math.random() * 100) + 20,
        totalValue: Math.floor(Math.random() * 500000) + 50000,
        lastOrderDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        performanceTrend: Math.random() > 0.6 ? 'improving' : Math.random() > 0.3 ? 'stable' : 'declining'
      };
      
      return performanceData;
    } catch (error) {
      this.logger.error(`Failed to get supplier performance for ${supplierId}:`, error);
      throw new Error(`Failed to get supplier performance: ${error}`);
    }
  }

  /**
   * Get all suppliers
   */
  async getAllSuppliers(): Promise<Supplier[]> {
    try {
      // Mock implementation - generate sample suppliers
      const suppliers: Supplier[] = [];
      const supplierNames = [
        'TechCorp GmbH', 'InnoSupply AG', 'QualityParts Ltd', 'ReliableTech Inc',
        'PrecisionComponents', 'GlobalSupplier Co', 'ElectronicParts GmbH'
      ];
      
      for (let i = 0; i < supplierNames.length; i++) {
        const supplier: Supplier = {
          id: `SUP_${(i + 1).toString().padStart(3, '0')}`,
          name: supplierNames[i],
          location: `Location ${i + 1}`,
          category: Math.random() > 0.5 ? 'Electronics' : 'Components',
          contactInfo: {
            email: `contact@${supplierNames[i].toLowerCase().replace(/\s+/g, '')}.com`,
            phone: `+49 ${Math.floor(Math.random() * 900) + 100} ${Math.floor(Math.random() * 9000) + 1000}`,
            address: `Address ${i + 1}, Germany`,
            contactPerson: `Contact Person ${i + 1}`
          },
          performanceMetrics: {
            onTimeDeliveryRate: 0.75 + Math.random() * 0.25,
            qualityRate: 0.90 + Math.random() * 0.10,
            responseTime: Math.random() * 24 + 2,
            financialStabilityScore: Math.floor(Math.random() * 4) + 6,
            reliabilityScore: Math.floor(Math.random() * 3) + 7,
            costCompetitiveness: Math.floor(Math.random() * 4) + 6
          }
        };
        suppliers.push(supplier);
      }
      
      return suppliers;
    } catch (error) {
      this.logger.error('Failed to get all suppliers:', error);
      throw new Error(`Failed to get suppliers: ${error}`);
    }
  }

  /**
   * Get supplier by ID
   */
  async getSupplierById(supplierId: string): Promise<Supplier | null> {
    try {
      const suppliers = await this.getAllSuppliers();
      return suppliers.find(s => s.id === supplierId) || null;
    } catch (error) {
      this.logger.error(`Failed to get supplier ${supplierId}:`, error);
      throw new Error(`Failed to get supplier: ${error}`);
    }
  }

  /**
   * Get suppliers by category
   */
  async getSuppliersByCategory(category: string): Promise<Supplier[]> {
    try {
      const suppliers = await this.getAllSuppliers();
      return suppliers.filter(s => s.category === category);
    } catch (error) {
      this.logger.error(`Failed to get suppliers by category ${category}:`, error);
      throw new Error(`Failed to get suppliers by category: ${error}`);
    }
  }

  /**
   * Get top performing suppliers
   */
  async getTopPerformingSuppliers(limit: number = 10): Promise<Supplier[]> {
    try {
      const suppliers = await this.getAllSuppliers();
      
      // Sort by overall performance score (combination of metrics)
      const sortedSuppliers = suppliers.sort((a, b) => {
        const scoreA = (a.performanceMetrics.onTimeDeliveryRate + 
                       a.performanceMetrics.qualityRate + 
                       a.performanceMetrics.reliabilityScore / 10) / 3;
        const scoreB = (b.performanceMetrics.onTimeDeliveryRate + 
                       b.performanceMetrics.qualityRate + 
                       b.performanceMetrics.reliabilityScore / 10) / 3;
        return scoreB - scoreA;
      });
      
      return sortedSuppliers.slice(0, limit);
    } catch (error) {
      this.logger.error('Failed to get top performing suppliers:', error);
      throw new Error(`Failed to get top performing suppliers: ${error}`);
    }
  }

  /**
   * Get suppliers with risk issues
   */
  async getRiskSuppliers(): Promise<Supplier[]> {
    try {
      const suppliers = await this.getAllSuppliers();
      
      // Filter suppliers with performance issues
      return suppliers.filter(s => 
        s.performanceMetrics.onTimeDeliveryRate < 0.8 ||
        s.performanceMetrics.qualityRate < 0.95 ||
        s.performanceMetrics.financialStabilityScore < 6
      );
    } catch (error) {
      this.logger.error('Failed to get risk suppliers:', error);
      throw new Error(`Failed to get risk suppliers: ${error}`);
    }
  }

  /**
   * Update supplier performance metrics
   */
  async updateSupplierPerformance(
    supplierId: string, 
    metrics: Partial<SupplierPerformanceMetrics>
  ): Promise<void> {
    try {
      this.logger.info(`Updating performance metrics for supplier ${supplierId}`);
      
      // Mock implementation - in real app, this would update the database
      // For now, just log the update
      this.logger.info(`Performance metrics updated for supplier ${supplierId}:`, metrics);
    } catch (error) {
      this.logger.error(`Failed to update supplier performance for ${supplierId}:`, error);
      throw new Error(`Failed to update supplier performance: ${error}`);
    }
  }

  /**
   * Search suppliers by name or category
   */
  async searchSuppliers(query: string): Promise<Supplier[]> {
    try {
      const suppliers = await this.getAllSuppliers();
      const lowerQuery = query.toLowerCase();
      
      return suppliers.filter(s => 
        s.name.toLowerCase().includes(lowerQuery) ||
        s.category.toLowerCase().includes(lowerQuery) ||
        s.location.toLowerCase().includes(lowerQuery)
      );
    } catch (error) {
      this.logger.error(`Failed to search suppliers with query "${query}":`, error);
      throw new Error(`Failed to search suppliers: ${error}`);
    }
  }

  /**
   * Get supplier delivery history summary
   */
  async getSupplierDeliveryHistory(supplierId: string, timeRange: { days: number }): Promise<any[]> {
    try {
      this.logger.info(`Getting delivery history for supplier ${supplierId} (${timeRange.days} days)`);
      
      // Mock implementation - generate sample delivery history
      const history = [];
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeRange.days);
      
      // Generate 10-30 delivery records
      const recordCount = Math.floor(Math.random() * 20) + 10;
      
      for (let i = 0; i < recordCount; i++) {
        const deliveryDate = new Date(startDate.getTime() + Math.random() * timeRange.days * 24 * 60 * 60 * 1000);
        const promisedTime = Math.floor(Math.random() * 10) + 3; // 3-12 days
        const actualTime = promisedTime + (Math.random() - 0.7) * 3; // Some variation
        
        history.push({
          deliveryId: `DEL_${supplierId}_${i + 1}`,
          supplierId,
          orderDate: new Date(deliveryDate.getTime() - actualTime * 24 * 60 * 60 * 1000),
          deliveryDate,
          promisedTime,
          deliveryTime: Math.max(actualTime, 1),
          wasLate: actualTime > promisedTime,
          productType: Math.random() > 0.5 ? 'Electronics' : 'Components',
          urgency: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',
          quantity: Math.floor(Math.random() * 1000) + 50,
          value: Math.floor(Math.random() * 10000) + 500
        });
      }
      
      return history.sort((a, b) => b.deliveryDate.getTime() - a.deliveryDate.getTime());
    } catch (error) {
      this.logger.error(`Failed to get delivery history for supplier ${supplierId}:`, error);
      throw new Error(`Failed to get delivery history: ${error}`);
    }
  }
}

export default SupplierRepository;