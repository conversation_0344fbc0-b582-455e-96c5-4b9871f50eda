import React, { memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { WasteAnalysis, CuttingPlan } from '../../services/types';
import { AlertTriangle, TrendingDown, Lightbulb, CheckCircle } from 'lucide-react';
import { SubtlePatternCard } from '@/components/ui/Card_SubtlePattern';

interface WasteAnalysisChartProps {
  wasteAnalysis: WasteAnalysis;
  plan: CuttingPlan | null;
}

/**
 * Waste Analysis Chart Component
 * 
 * Provides comprehensive waste analysis visualization including
 * waste distribution, recommendations, and efficiency metrics.
 */
export const WasteAnalysisChart = memo(function WasteAnalysisChart({
  wasteAnalysis,
  plan
}: WasteAnalysisChartProps) {

  // Prepare waste distribution data
  const wasteDistributionData = wasteAnalysis.wasteByDrum.map((drum, index) => ({
    drumId: drum.drumId.replace('drum-', 'Trommel '),
    waste: drum.waste,
    percentage: Math.round((drum.waste / wasteAnalysis.totalWaste) * 100)
  }));

  // Prepare efficiency comparison data
  const efficiencyData = plan ? plan.drumAllocations.map((allocation, index) => {
    const wasteItem = wasteAnalysis.wasteByDrum.find(w => w.drumId === allocation.drumId);
    const waste = wasteItem ? wasteItem.waste : 0;

    return {
      drumId: allocation.drumId.replace('drum-', 'T'),
      utilization: Math.round(allocation.utilization * 100),
      waste: waste,
      efficiency: Math.round((1 - (waste / 1000)) * 100), // Simplified efficiency calculation
      cuts: allocation.cuts.length
    };
  }) : [];

  // Create waste trend simulation (mock data for demonstration)
  const wasteTrendData = [
    { scenario: 'Aktuell', waste: wasteAnalysis.totalWaste, efficiency: plan?.efficiency ? plan.efficiency * 100 : 0 },
    { scenario: 'Optimiert', waste: wasteAnalysis.totalWaste * 0.8, efficiency: plan?.efficiency ? plan.efficiency * 100 * 1.1 : 0 },
    { scenario: 'Best Case', waste: wasteAnalysis.totalWaste * 0.6, efficiency: plan?.efficiency ? plan.efficiency * 100 * 1.2 : 0 }
  ];

  // Chart configuration
  const chartConfig = {
    waste: {
      label: "Verschnitt (m)",
      color: "var(--chart-3)",
    },
    utilization: {
      label: "Auslastung (%)",
      color: "var(--chart-1)",
    },
    efficiency: {
      label: "Effizienz (%)",
      color: "var(--chart-2)",
    }
  };

  // Colors for pie chart
  const COLORS = ['var(--chart-1)', 'var(--chart-2)', 'var(--chart-3)', 'var(--chart-4)', 'var(--chart-5)'];

  // Determine waste level severity
  const getWasteSeverity = (percentage: number) => {
    if (percentage <= 10) return { level: 'low', color: 'green', icon: CheckCircle };
    if (percentage <= 20) return { level: 'medium', color: 'yellow', icon: AlertTriangle };
    return { level: 'high', color: 'red', icon: AlertTriangle };
  };

  const wasteSeverity = getWasteSeverity(wasteAnalysis.wastePercentage);
  const WasteSeverityIcon = wasteSeverity.icon;

  return (
    <div className="space-y-6">
      {/* Waste Summary Cards mit SubtlePattern Design */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <SubtlePatternCard
          title="Gesamt-Verschnitt"
          value={`${parseFloat(wasteAnalysis.totalWaste.toFixed(1))} m`}
          subtitle={`${wasteAnalysis.wastePercentage.toFixed(1)}% der Gesamtlänge`}
          icon={<WasteSeverityIcon className={`h-5 w-5 text-${wasteSeverity.color}-600`} />}
          valueClassName={`text-${wasteSeverity.color}-600`}
        />

        <SubtlePatternCard
          title="Durchschn. Verschnitt"
          value={`${parseFloat((wasteAnalysis.totalWaste / wasteAnalysis.wasteByDrum.length).toFixed(1))} m`}
          subtitle="pro Trommel"
          icon={<TrendingDown className="h-5 w-5" />}
          valueClassName="text-blue-600"
        />

        <SubtlePatternCard
          title="Einsparpotential"
          value={`${parseFloat((wasteAnalysis.totalWaste * 0.3).toFixed(1))} m`}
          subtitle="geschätzt"
          icon={<Lightbulb className="h-5 w-5" />}
          valueClassName="text-purple-600"
        />
      </div>

      {/* Waste Level Alert */}
      <Alert className={`border-${wasteSeverity.color}-500`}>
        <WasteSeverityIcon className={`h-4 w-4 text-${wasteSeverity.color}-600`} />
        <AlertDescription>
          <strong>Verschnitt-Bewertung:</strong> {' '}
          {wasteSeverity.level === 'low' && 'Verschnitt liegt im optimalen Bereich (≤10%)'}
          {wasteSeverity.level === 'medium' && 'Verschnitt ist akzeptabel, aber verbesserungsfähig (10-20%)'}
          {wasteSeverity.level === 'high' && 'Hoher Verschnitt erkannt - Optimierung empfohlen (>20%)'}
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Waste Distribution by Drum */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle>Verschnitt-Verteilung</CardTitle>
            <CardDescription>
              Verschnitt pro Trommel in Zentimetern
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <BarChart data={wasteDistributionData} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis
                  dataKey="drumId"
                  className="text-xs font-bold"
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis
                  className="text-xs font-bold"
                  tick={{ fill: "#000000" }}
                  label={{
                    value: "Verschnitt (m)",
                    angle: -90,
                    position: "insideLeft",
                    style: { textAnchor: "middle", fontSize: 12 }
                  }}
                />
                <ChartTooltip
                  content={
                    <ChartTooltipContent
                      labelFormatter={(label) => `${label}`}
                      formatter={(value, name, props) => {
                        const drumData = wasteDistributionData.find(d => d.drumId === props.payload?.drumId);
                        return [
                          `${value} m (${drumData?.percentage || 0}%)`,
                          'Verschnitt'
                        ];
                      }}
                    />
                  }
                />
                <Bar
                  dataKey="waste"
                  name="Verschnitt"
                  fill={chartConfig.waste.color}
                  stroke="#000000"
                  strokeWidth={2}
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Waste Distribution Pie Chart */}
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle>Verschnitt-Anteile</CardTitle>
            <CardDescription>
              Prozentuale Verteilung des Verschnitts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={wasteDistributionData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="waste"
                    stroke="#000000"
                    strokeWidth={2}
                    label={({ percentage }) => `${percentage}%`}
                  >
                    {wasteDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <ChartTooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        const data = payload[0].payload;
                        return (
                          <div className="bg-white p-2 border border-gray-300 rounded shadow">
                            <p className="font-medium">{data.drumId}</p>
                            <p className="text-sm">Verschnitt: {data.waste} m ({data.percentage}%)</p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Efficiency vs Waste Comparison */}
      {efficiencyData.length > 0 && (
        <Card className="border-[#ff7a05]">
          <CardHeader>
            <CardTitle>Effizienz vs. Verschnitt</CardTitle>
            <CardDescription>
              Vergleich von Auslastung und Verschnitt pro Trommel
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className="h-64 w-full">
              <BarChart data={efficiencyData} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis
                  dataKey="drumId"
                  className="text-xs font-bold"
                  tick={{ fontSize: 12 }}
                />
                <YAxis
                  className="text-xs font-bold"
                  tick={{ fill: "#000000" }}
                  label={{
                    value: "Wert",
                    angle: -90,
                    position: "insideLeft",
                    style: { textAnchor: "middle", fontSize: 12 }
                  }}
                />
                <ChartTooltip
                  content={
                    <ChartTooltipContent
                      labelFormatter={(label) => `Trommel: ${label}`}
                      formatter={(value, name) => [
                        `${value}${name === 'utilization' ? '%' : name === 'waste' ? ' m' : ''}`,
                        name === 'utilization' ? 'Auslastung' :
                          name === 'waste' ? 'Verschnitt' : 'Schnitte'
                      ]}
                    />
                  }
                />
                <Bar
                  dataKey="utilization"
                  name="Auslastung"
                  fill={chartConfig.utilization.color}
                  stroke="#000000"
                  strokeWidth={2}
                  radius={[4, 4, 0, 0]}
                />
                <Bar
                  dataKey="waste"
                  name="Verschnitt"
                  fill={chartConfig.waste.color}
                  stroke="#000000"
                  strokeWidth={2}
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>
      )}

      {/* Optimization Potential */}
      <Card className="border-[#ff7a05]">
        <CardHeader>
          <CardTitle>Optimierungspotential</CardTitle>
          <CardDescription>
            Vergleich verschiedener Optimierungsszenarien
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-64 w-full">
            <AreaChart data={wasteTrendData} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis
                dataKey="scenario"
                className="text-xs font-bold"
                tick={{ fontSize: 12 }}
              />
              <YAxis
                className="text-xs font-bold"
                tick={{ fill: "#000000" }}
                label={{
                  value: "Verschnitt (m)",
                  angle: -90,
                  position: "insideLeft",
                  style: { textAnchor: "middle", fontSize: 12 }
                }}
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    labelFormatter={(label) => `Szenario: ${label}`}
                    formatter={(value, name) => [
                      `${parseFloat((value as number).toFixed(1))} ${name === 'waste' ? 'm' : '%'}`,
                      name === 'waste' ? 'Verschnitt' : 'Effizienz'
                    ]}
                  />
                }
              />
              <Area
                type="monotone"
                dataKey="waste"
                stroke={chartConfig.waste.color}
                fill={chartConfig.waste.color}
                fillOpacity={0.3}
                strokeWidth={2}
              />
            </AreaChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card className="border-[#ff7a05]">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Lightbulb className="h-5 w-5 text-yellow-600" />
            <span>Optimierungsempfehlungen</span>
          </CardTitle>
          <CardDescription>
            Automatisch generierte Verbesserungsvorschläge
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {wasteAnalysis.recommendations.map((recommendation, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                <Badge variant="outline" className="mt-0.5">
                  {index + 1}
                </Badge>
                <p className="text-sm text-blue-800">{recommendation}</p>
              </div>
            ))}

            {/* Additional context-aware recommendations */}
            {wasteAnalysis.wastePercentage > 20 && (
              <div className="flex items-start space-x-3 p-3 bg-red-50 rounded-lg">
                <Badge variant="destructive" className="mt-0.5">
                  !
                </Badge>
                <p className="text-sm text-red-800">
                  Kritischer Verschnitt erkannt. Erwägen Sie die Verwendung des genetischen Algorithmus
                  für bessere Optimierungsergebnisse.
                </p>
              </div>
            )}

            {wasteAnalysis.wastePercentage <= 10 && (
              <div className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg">
                <Badge variant="outline" className="mt-0.5 border-green-500 text-green-700">
                  ✓
                </Badge>
                <p className="text-sm text-green-800">
                  Ausgezeichnete Verschnitt-Optimierung erreicht!
                  Die aktuelle Konfiguration ist sehr effizient.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
});