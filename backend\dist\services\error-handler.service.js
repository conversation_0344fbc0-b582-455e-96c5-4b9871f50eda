"use strict";
/**
 * Unified Error Handling Service
 *
 * Standardisiert Error-Handling, Logging und Response-Formate
 * für alle Repository und Service-Klassen.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = exports.ErrorHandlerService = exports.ErrorSeverity = exports.ErrorType = void 0;
exports.withErrorHandling = withErrorHandling;
exports.toApiErrorResponse = toApiErrorResponse;
const library_1 = require("@prisma/client/runtime/library");
/**
 * Standard-Error-Typen für die Anwendung
 */
var ErrorType;
(function (ErrorType) {
    ErrorType["DATABASE_ERROR"] = "DATABASE_ERROR";
    ErrorType["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    ErrorType["NOT_FOUND_ERROR"] = "NOT_FOUND_ERROR";
    ErrorType["CACHE_ERROR"] = "CACHE_ERROR";
    ErrorType["NETWORK_ERROR"] = "NETWORK_ERROR";
    ErrorType["TIMEOUT_ERROR"] = "TIMEOUT_ERROR";
    ErrorType["PERMISSION_ERROR"] = "PERMISSION_ERROR";
    ErrorType["CONFIGURATION_ERROR"] = "CONFIGURATION_ERROR";
    ErrorType["BUSINESS_LOGIC_ERROR"] = "BUSINESS_LOGIC_ERROR";
    ErrorType["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
})(ErrorType || (exports.ErrorType = ErrorType = {}));
/**
 * Standard-Error-Severity für Logging und Monitoring
 */
var ErrorSeverity;
(function (ErrorSeverity) {
    ErrorSeverity["LOW"] = "low";
    ErrorSeverity["MEDIUM"] = "medium";
    ErrorSeverity["HIGH"] = "high";
    ErrorSeverity["CRITICAL"] = "critical";
})(ErrorSeverity || (exports.ErrorSeverity = ErrorSeverity = {}));
/**
 * Unified Error Handler Service
 */
class ErrorHandlerService {
    constructor(config = {}) {
        this.errorHistory = [];
        this.stats = {
            totalErrors: 0,
            errorsByType: {},
            errorsBySeverity: {},
            recentErrors: [],
            errorRate: 0,
            lastErrorTime: 0
        };
        this.config = {
            enableDetailedLogging: process.env.NODE_ENV === 'development',
            enableStackTraces: process.env.NODE_ENV === 'development',
            enableUserSuggestions: true,
            logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'error',
            maxErrorHistorySize: 100,
            ...config
        };
        // Initialisiere Error-Statistiken
        this.initializeStats();
    }
    static getInstance(config) {
        if (!ErrorHandlerService.instance) {
            ErrorHandlerService.instance = new ErrorHandlerService(config);
        }
        return ErrorHandlerService.instance;
    }
    /**
     * Hauptmethode zum Behandeln von Fehlern
     */
    handleError(error, context) {
        const standardError = this.standardizeError(error, context);
        // Error in History und Stats speichern
        this.recordError(standardError);
        // Error loggen
        this.logError(standardError);
        // Performance Monitoring benachrichtigen falls verfügbar
        this.notifyPerformanceMonitor(standardError);
        return standardError;
    }
    /**
     * Error zu StandardError umwandeln
     */
    standardizeError(error, context) {
        const errorId = this.generateErrorId();
        const timestamp = Date.now();
        // Error-Typ und Details bestimmen
        const { type, severity, code, userMessage, details, suggestions } = this.categorizeError(error);
        const standardError = {
            id: errorId,
            type,
            severity,
            message: (error === null || error === void 0 ? void 0 : error.message) || 'Unbekannter Fehler',
            userMessage,
            code,
            details,
            originalError: error instanceof Error ? error : undefined,
            context: {
                ...context,
                timestamp
            },
            stack: this.config.enableStackTraces ? error === null || error === void 0 ? void 0 : error.stack : undefined,
            suggestions: this.config.enableUserSuggestions ? suggestions : undefined
        };
        return standardError;
    }
    /**
     * Error kategorisieren und Details extrahieren
     */
    categorizeError(error) {
        // Prisma-Fehler
        if (error instanceof library_1.PrismaClientKnownRequestError) {
            return this.categorizePrismaError(error);
        }
        // Standard-Fehler-Kategorisierung
        const errorMessage = ((error === null || error === void 0 ? void 0 : error.message) || '').toLowerCase();
        // Timeout-Fehler
        if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
            return {
                type: ErrorType.TIMEOUT_ERROR,
                severity: ErrorSeverity.MEDIUM,
                code: 'TIMEOUT',
                userMessage: 'Die Operation hat zu lange gedauert. Bitte versuchen Sie es erneut.',
                suggestions: ['Überprüfen Sie Ihre Internetverbindung', 'Versuchen Sie es in wenigen Minuten erneut']
            };
        }
        // Netzwerk-Fehler
        if (errorMessage.includes('network') || errorMessage.includes('connection') || errorMessage.includes('fetch')) {
            return {
                type: ErrorType.NETWORK_ERROR,
                severity: ErrorSeverity.HIGH,
                code: 'NETWORK_ERROR',
                userMessage: 'Verbindungsfehler aufgetreten. Bitte überprüfen Sie Ihre Internetverbindung.',
                suggestions: ['Internetverbindung prüfen', 'Server-Status überprüfen']
            };
        }
        // Validierungs-Fehler
        if (errorMessage.includes('validation') || errorMessage.includes('invalid') || errorMessage.includes('required')) {
            return {
                type: ErrorType.VALIDATION_ERROR,
                severity: ErrorSeverity.LOW,
                code: 'VALIDATION_ERROR',
                userMessage: 'Ungültige Eingabedaten. Bitte überprüfen Sie Ihre Eingaben.',
                suggestions: ['Eingabedaten überprüfen', 'Pflichtfelder ausfüllen']
            };
        }
        // Not Found-Fehler
        if (errorMessage.includes('not found') || errorMessage.includes('404')) {
            return {
                type: ErrorType.NOT_FOUND_ERROR,
                severity: ErrorSeverity.LOW,
                code: 'NOT_FOUND',
                userMessage: 'Die angeforderten Daten wurden nicht gefunden.',
                suggestions: ['Überprüfen Sie die angeforderten Parameter', 'Aktualisieren Sie die Seite']
            };
        }
        // Cache-Fehler
        if (errorMessage.includes('cache') || errorMessage.includes('redis') || errorMessage.includes('memory')) {
            return {
                type: ErrorType.CACHE_ERROR,
                severity: ErrorSeverity.MEDIUM,
                code: 'CACHE_ERROR',
                userMessage: 'Cache-Fehler aufgetreten. Die Daten werden direkt aus der Datenbank geladen.',
                suggestions: ['Cache-Service neu starten', 'Memory-Verbrauch prüfen']
            };
        }
        // Permission-Fehler
        if (errorMessage.includes('permission') || errorMessage.includes('unauthorized') || errorMessage.includes('forbidden')) {
            return {
                type: ErrorType.PERMISSION_ERROR,
                severity: ErrorSeverity.HIGH,
                code: 'PERMISSION_DENIED',
                userMessage: 'Sie haben nicht die erforderlichen Berechtigungen für diese Operation.',
                suggestions: ['Anmeldedaten überprüfen', 'Administrator kontaktieren']
            };
        }
        // Standard-Unbekannter-Fehler
        return {
            type: ErrorType.UNKNOWN_ERROR,
            severity: ErrorSeverity.MEDIUM,
            code: 'UNKNOWN_ERROR',
            userMessage: 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.',
            details: { originalMessage: error === null || error === void 0 ? void 0 : error.message },
            suggestions: ['Seite neu laden', 'In wenigen Minuten erneut versuchen']
        };
    }
    /**
     * Prisma-spezifische Fehler kategorisieren
     */
    categorizePrismaError(error) {
        const code = error.code;
        const meta = error.meta;
        switch (code) {
            case 'P2002': // Unique constraint violation
                return {
                    type: ErrorType.VALIDATION_ERROR,
                    severity: ErrorSeverity.LOW,
                    code: 'DUPLICATE_ENTRY',
                    userMessage: 'Ein Eintrag mit diesen Daten existiert bereits.',
                    details: { fields: meta === null || meta === void 0 ? void 0 : meta.target, constraint: meta === null || meta === void 0 ? void 0 : meta.constraint },
                    suggestions: ['Eindeutige Werte verwenden', 'Bestehenden Eintrag aktualisieren']
                };
            case 'P2025': // Record not found
                return {
                    type: ErrorType.NOT_FOUND_ERROR,
                    severity: ErrorSeverity.LOW,
                    code: 'RECORD_NOT_FOUND',
                    userMessage: 'Der gesuchte Datensatz wurde nicht gefunden.',
                    details: { cause: meta === null || meta === void 0 ? void 0 : meta.cause },
                    suggestions: ['ID überprüfen', 'Datensatz-Existenz prüfen']
                };
            case 'P2003': // Foreign key constraint violation
                return {
                    type: ErrorType.VALIDATION_ERROR,
                    severity: ErrorSeverity.MEDIUM,
                    code: 'FOREIGN_KEY_VIOLATION',
                    userMessage: 'Die Operation verletzt referentielle Integrität.',
                    details: { field: meta === null || meta === void 0 ? void 0 : meta.field_name },
                    suggestions: ['Abhängigkeiten prüfen', 'Referenzierte Datensätze erstellen']
                };
            case 'P2021': // Table does not exist
                return {
                    type: ErrorType.CONFIGURATION_ERROR,
                    severity: ErrorSeverity.CRITICAL,
                    code: 'TABLE_NOT_EXISTS',
                    userMessage: 'Datenbankstruktur ist nicht korrekt konfiguriert.',
                    details: { table: meta === null || meta === void 0 ? void 0 : meta.table },
                    suggestions: ['Datenbank-Migration ausführen', 'Administrator kontaktieren']
                };
            case 'P2024': // Timeout
                return {
                    type: ErrorType.TIMEOUT_ERROR,
                    severity: ErrorSeverity.HIGH,
                    code: 'DATABASE_TIMEOUT',
                    userMessage: 'Datenbank-Operation hat zu lange gedauert.',
                    details: { timeout: meta === null || meta === void 0 ? void 0 : meta.timeout },
                    suggestions: ['Query-Performance optimieren', 'Datenbank-Load prüfen']
                };
            default:
                return {
                    type: ErrorType.DATABASE_ERROR,
                    severity: ErrorSeverity.HIGH,
                    code: `PRISMA_${code}`,
                    userMessage: 'Datenbankfehler aufgetreten. Bitte versuchen Sie es erneut.',
                    details: { prismaCode: code, meta },
                    suggestions: ['Erneut versuchen', 'Administrator benachrichtigen']
                };
        }
    }
    /**
     * Error in History und Stats erfassen
     */
    recordError(error) {
        // Error zu History hinzufügen
        this.errorHistory.push(error);
        // History-Größe begrenzen
        if (this.errorHistory.length > this.config.maxErrorHistorySize) {
            this.errorHistory = this.errorHistory.slice(-this.config.maxErrorHistorySize);
        }
        // Statistiken aktualisieren
        this.stats.totalErrors++;
        this.stats.errorsByType[error.type] = (this.stats.errorsByType[error.type] || 0) + 1;
        this.stats.errorsBySeverity[error.severity] = (this.stats.errorsBySeverity[error.severity] || 0) + 1;
        this.stats.lastErrorTime = error.context.timestamp;
        // Recent errors aktualisieren (letzte 10)
        this.stats.recentErrors = this.errorHistory.slice(-10);
        // Error-Rate berechnen (Errors pro Minute in den letzten 5 Minuten)
        const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
        const recentErrorCount = this.errorHistory.filter(e => e.context.timestamp >= fiveMinutesAgo).length;
        this.stats.errorRate = recentErrorCount / 5; // Errors per minute
    }
    /**
     * Error loggen
     */
    logError(error) {
        const logLevel = this.getLogLevel(error.severity);
        const logMessage = this.formatLogMessage(error);
        switch (logLevel) {
            case 'debug':
                console.debug(logMessage);
                break;
            case 'info':
                console.info(logMessage);
                break;
            case 'warn':
                console.warn(logMessage);
                break;
            case 'error':
                console.error(logMessage);
                break;
        }
        // Detailliertes Logging in Development
        if (this.config.enableDetailedLogging) {
            console.log('[ERROR-DETAILS]', {
                id: error.id,
                type: error.type,
                context: error.context,
                details: error.details,
                stack: error.stack
            });
        }
    }
    /**
     * Performance Monitor benachrichtigen
     */
    notifyPerformanceMonitor(error) {
        try {
            // Lazy-load um zirkuläre Abhängigkeiten zu vermeiden
            const { performanceMonitor } = require('./performance-monitor.service');
            performanceMonitor.recordQueryError(error.id, error.type);
        }
        catch (err) {
            // Ignore performance monitoring errors
            if (this.config.enableDetailedLogging) {
                console.debug('[ERROR-HANDLER] Performance monitor notification failed:', err);
            }
        }
    }
    /**
     * Error-Statistiken abrufen
     */
    getErrorStats() {
        return { ...this.stats };
    }
    /**
     * Error-History abrufen
     */
    getErrorHistory(limit) {
        return limit ? this.errorHistory.slice(-limit) : [...this.errorHistory];
    }
    /**
     * Repository-freundliche Error-Handler-Methoden
     */
    /**
     * Database-Error-Handler
     */
    handleDatabaseError(error, context) {
        return this.handleError(error, context);
    }
    /**
     * Cache-Error-Handler
     */
    handleCacheError(error, context) {
        return this.handleError(error, context);
    }
    /**
     * Validation-Error-Handler
     */
    handleValidationError(message, details, context) {
        const error = new Error(message);
        const standardError = this.handleError(error, context);
        standardError.type = ErrorType.VALIDATION_ERROR;
        standardError.details = details;
        return standardError;
    }
    /**
     * Not-Found-Error-Handler
     */
    handleNotFoundError(resource, id, context) {
        const message = `${resource} ${id ? `mit ID ${id}` : ''} nicht gefunden`;
        const error = new Error(message);
        const standardError = this.handleError(error, context);
        standardError.type = ErrorType.NOT_FOUND_ERROR;
        standardError.details = { resource, id };
        return standardError;
    }
    /**
     * Helper-Methoden
     */
    initializeStats() {
        // Initialisiere Error-Counters
        Object.values(ErrorType).forEach(type => {
            this.stats.errorsByType[type] = 0;
        });
        Object.values(ErrorSeverity).forEach(severity => {
            this.stats.errorsBySeverity[severity] = 0;
        });
    }
    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    getLogLevel(severity) {
        switch (severity) {
            case ErrorSeverity.LOW:
                return 'info';
            case ErrorSeverity.MEDIUM:
                return 'warn';
            case ErrorSeverity.HIGH:
            case ErrorSeverity.CRITICAL:
                return 'error';
            default:
                return 'error';
        }
    }
    formatLogMessage(error) {
        var _a, _b, _c;
        return [
            `[${error.severity.toUpperCase()}]`,
            `[${error.type}]`,
            `[${error.code}]`,
            `${error.message}`,
            ((_a = error.context) === null || _a === void 0 ? void 0 : _a.service) && `Service: ${error.context.service}`,
            ((_b = error.context) === null || _b === void 0 ? void 0 : _b.method) && `Method: ${error.context.method}`,
            ((_c = error.context) === null || _c === void 0 ? void 0 : _c.requestId) && `RequestID: ${error.context.requestId}`
        ].filter(Boolean).join(' | ');
    }
    /**
     * Service beenden und Cleanup
     */
    destroy() {
        this.errorHistory = [];
        this.initializeStats();
    }
}
exports.ErrorHandlerService = ErrorHandlerService;
/**
 * Error-Handler Decorator für Repository-Methoden
 */
function withErrorHandling(service) {
    return function (target, propertyName, descriptor) {
        const method = descriptor.value;
        descriptor.value = async function (...args) {
            try {
                return await method.apply(this, args);
            }
            catch (error) {
                const errorHandler = ErrorHandlerService.getInstance();
                const standardError = errorHandler.handleError(error, {
                    service,
                    method: propertyName
                });
                throw standardError;
            }
        };
        return descriptor;
    };
}
/**
 * Konvertiert StandardError zu API-Response-Format
 */
function toApiErrorResponse(error) {
    return {
        success: false,
        error: error.userMessage,
        code: error.code,
        details: error.details,
        suggestions: error.suggestions,
        errorId: error.id
    };
}
// Singleton instance
exports.errorHandler = ErrorHandlerService.getInstance();
