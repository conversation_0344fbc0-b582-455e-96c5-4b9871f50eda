/**
 * Predictive Analytics for Inventory Management
 * 
 * Advanced algorithms for seasonal pattern detection, reorder optimization,
 * anomaly detection, and consumption pattern analysis
 */

import {
  ConsumptionData,
  TimeSeriesData,
  SeasonalityPattern,
  SeasonalPattern,
  ReorderRecommendation,
  StockAnomaly,
  ConsumptionAnalysis,
  ConsumptionPattern,
  ConsumptionOutlier,
  StatisticalSummary,
  InventoryItem
} from '../types';

export interface SeasonalAnalysisResult {
  itemId: string;
  seasonalityPattern: SeasonalityPattern;
  seasonalStrength: number;
  dominantCycle: number;
  seasonalIndices: number[];
  confidence: number;
}

export interface ReorderOptimizationResult {
  itemId: string;
  currentReorderPoint: number;
  optimizedReorderPoint: number;
  improvement: number;
  reasoning: string[];
  riskAssessment: {
    stockoutProbability: number;
    overstockRisk: number;
    serviceLevel: number;
  };
}

export interface AnomalyDetectionResult {
  anomalies: StockAnomaly[];
  totalItemsAnalyzed: number;
  anomalyRate: number;
  severityDistribution: Record<string, number>;
}

export interface TrendAnalysisResult {
  itemId: string;
  trendDirection: 'increasing' | 'decreasing' | 'stable';
  trendStrength: number;
  changeRate: number;
  trendConfidence: number;
  projectedChange: number;
  timeToSignificantChange?: number;
}

export class PredictiveAnalyzer {
  /**
   * Advanced seasonal pattern detection using multiple methods
   */
  static detectSeasonalPatterns(
    itemId: string,
    consumptionData: ConsumptionData[],
    options: {
      minCycleLength?: number;
      maxCycleLength?: number;
      significanceThreshold?: number;
    } = {}
  ): SeasonalAnalysisResult {
    const {
      minCycleLength = 7,
      maxCycleLength = 365,
      significanceThreshold = 0.1
    } = options;

    if (consumptionData.length < minCycleLength * 2) {
      return {
        itemId,
        seasonalityPattern: {
          itemId,
          hasSeasonality: false,
          patterns: [],
          strength: 0,
          detectedAt: new Date()
        },
        seasonalStrength: 0,
        dominantCycle: 0,
        seasonalIndices: [],
        confidence: 0
      };
    }

    const timeSeriesData = this.convertToTimeSeries(consumptionData);
    const values = timeSeriesData.map(d => d.value);

    // Detect multiple seasonal patterns
    const detectedPatterns: SeasonalPattern[] = [];
    const cyclesToTest = [7, 14, 30, 90, 365].filter(c => 
      c >= minCycleLength && c <= maxCycleLength && values.length >= c * 2
    );

    let maxStrength = 0;
    let dominantCycle = 0;

    for (const cycle of cyclesToTest) {
      const autocorr = this.calculateAutocorrelation(values, cycle);
      const strength = Math.abs(autocorr);

      if (strength > significanceThreshold) {
        const pattern: SeasonalPattern = {
          type: this.getCycleType(cycle),
          cycle,
          amplitude: strength,
          phase: autocorr >= 0 ? 0 : Math.PI
        };
        detectedPatterns.push(pattern);

        if (strength > maxStrength) {
          maxStrength = strength;
          dominantCycle = cycle;
        }
      }
    }

    // Calculate seasonal indices for the dominant cycle
    const seasonalIndices = dominantCycle > 0 ? 
      this.calculateSeasonalIndices(values, dominantCycle) : [];

    // Calculate overall seasonal strength using spectral analysis
    const spectralStrength = this.calculateSpectralSeasonality(values);
    const finalStrength = Math.max(maxStrength, spectralStrength);

    // Calculate confidence based on data length and pattern consistency
    const confidence = this.calculateSeasonalConfidence(
      values.length,
      detectedPatterns,
      finalStrength
    );

    return {
      itemId,
      seasonalityPattern: {
        itemId,
        hasSeasonality: detectedPatterns.length > 0,
        patterns: detectedPatterns,
        strength: finalStrength,
        detectedAt: new Date()
      },
      seasonalStrength: finalStrength,
      dominantCycle,
      seasonalIndices,
      confidence
    };
  }

  /**
   * Optimize reorder points using advanced statistical methods
   */
  static optimizeReorderPoint(
    itemId: string,
    currentStock: number,
    consumptionData: ConsumptionData[],
    options: {
      targetServiceLevel?: number;
      leadTimeDays?: number;
      safetyStockMultiplier?: number;
      considerSeasonality?: boolean;
    } = {}
  ): ReorderOptimizationResult {
    const {
      targetServiceLevel = 0.95,
      leadTimeDays = 14,
      safetyStockMultiplier = 1.0,
      considerSeasonality = true
    } = options;

    if (consumptionData.length < 7) {
      throw new Error('Insufficient data for reorder point optimization');
    }

    const values = consumptionData.map(d => d.quantity);
    const stats = this.calculateStatistics(values);

    // Calculate demand during lead time
    const avgDailyDemand = stats.mean;
    const demandVariability = stats.standardDeviation;

    // Detect seasonality if requested
    let seasonalAdjustment = 1.0;
    if (considerSeasonality) {
      const seasonalAnalysis = this.detectSeasonalPatterns(itemId, consumptionData);
      if (seasonalAnalysis.seasonalityPattern.hasSeasonality) {
        // Adjust for current seasonal position
        const currentSeasonalIndex = this.getCurrentSeasonalIndex(
          seasonalAnalysis.seasonalIndices,
          seasonalAnalysis.dominantCycle
        );
        seasonalAdjustment = currentSeasonalIndex;
      }
    }

    // Calculate safety stock using normal distribution
    const zScore = this.getZScoreForServiceLevel(targetServiceLevel);
    const leadTimeDemandStdDev = demandVariability * Math.sqrt(leadTimeDays);
    const safetyStock = zScore * leadTimeDemandStdDev * safetyStockMultiplier;

    // Calculate optimized reorder point
    const expectedLeadTimeDemand = avgDailyDemand * leadTimeDays * seasonalAdjustment;
    const optimizedReorderPoint = Math.ceil(expectedLeadTimeDemand + safetyStock);

    // Calculate current reorder point (simple method for comparison)
    const currentReorderPoint = Math.ceil(avgDailyDemand * leadTimeDays * 1.2); // 20% safety margin

    // Calculate improvement
    const improvement = ((currentReorderPoint - optimizedReorderPoint) / currentReorderPoint) * 100;

    // Risk assessment
    const stockoutProbability = this.calculateStockoutProbability(
      currentStock,
      optimizedReorderPoint,
      avgDailyDemand,
      demandVariability,
      leadTimeDays
    );

    const overstockRisk = this.calculateOverstockRisk(
      currentStock,
      optimizedReorderPoint,
      avgDailyDemand
    );

    const actualServiceLevel = this.calculateServiceLevel(
      optimizedReorderPoint,
      avgDailyDemand,
      demandVariability,
      leadTimeDays
    );

    // Generate reasoning
    const reasoning = [
      `Based on ${consumptionData.length} days of consumption data`,
      `Average daily demand: ${avgDailyDemand.toFixed(2)} units`,
      `Demand variability (σ): ${demandVariability.toFixed(2)}`,
      `Lead time: ${leadTimeDays} days`,
      `Target service level: ${(targetServiceLevel * 100).toFixed(0)}%`,
      seasonalAdjustment !== 1.0 ? `Seasonal adjustment: ${(seasonalAdjustment * 100).toFixed(0)}%` : null,
      `Safety stock: ${safetyStock.toFixed(0)} units`
    ].filter(Boolean) as string[];

    return {
      itemId,
      currentReorderPoint,
      optimizedReorderPoint,
      improvement,
      reasoning,
      riskAssessment: {
        stockoutProbability,
        overstockRisk,
        serviceLevel: actualServiceLevel
      }
    };
  }

  /**
   * Advanced stock anomaly detection using multiple statistical methods
   */
  static detectStockAnomalies(
    inventoryItems: InventoryItem[],
    consumptionDataMap: Map<string, ConsumptionData[]>,
    options: {
      sensitivityLevel?: 'low' | 'medium' | 'high';
      lookbackDays?: number;
      minDataPoints?: number;
    } = {}
  ): AnomalyDetectionResult {
    const {
      sensitivityLevel = 'medium',
      lookbackDays = 30,
      minDataPoints = 7
    } = options;

    const anomalies: StockAnomaly[] = [];
    let totalItemsAnalyzed = 0;

    // Set thresholds based on sensitivity
    const thresholds = {
      low: { zScore: 3.0, changeThreshold: 0.5 },
      medium: { zScore: 2.5, changeThreshold: 0.3 },
      high: { zScore: 2.0, changeThreshold: 0.2 }
    };

    const { zScore: zThreshold, changeThreshold } = thresholds[sensitivityLevel];

    for (const item of inventoryItems) {
      const consumptionData = consumptionDataMap.get(item.id);
      if (!consumptionData || consumptionData.length < minDataPoints) {
        continue;
      }

      totalItemsAnalyzed++;

      // Filter recent data
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - lookbackDays);
      const recentData = consumptionData.filter(d => d.date >= cutoffDate);
      const historicalData = consumptionData.filter(d => d.date < cutoffDate);

      if (recentData.length === 0 || historicalData.length === 0) {
        continue;
      }

      // Detect various types of anomalies
      const detectedAnomalies = [
        ...this.detectConsumptionAnomalies(item, recentData, historicalData, zThreshold),
        ...this.detectStockLevelAnomalies(item, consumptionData, changeThreshold),
        ...this.detectPatternAnomalies(item, consumptionData, zThreshold),
        ...this.detectTrendAnomalies(item, consumptionData, changeThreshold)
      ];

      anomalies.push(...detectedAnomalies);
    }

    // Calculate statistics
    const anomalyRate = totalItemsAnalyzed > 0 ? anomalies.length / totalItemsAnalyzed : 0;
    const severityDistribution = anomalies.reduce((acc, anomaly) => {
      acc[anomaly.severity] = (acc[anomaly.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      anomalies,
      totalItemsAnalyzed,
      anomalyRate,
      severityDistribution
    };
  }

  /**
   * Advanced consumption pattern analysis with trend identification
   */
  static analyzeConsumptionPatterns(
    itemId: string,
    consumptionData: ConsumptionData[],
    options: {
      trendWindow?: number;
      outlierThreshold?: number;
      patternMinLength?: number;
    } = {}
  ): ConsumptionAnalysis {
    const {
      trendWindow = 14,
      outlierThreshold = 2.5,
      patternMinLength = 7
    } = options;

    if (consumptionData.length === 0) {
      throw new Error('No consumption data provided for analysis');
    }

    const values = consumptionData.map(d => d.quantity);
    const stats = this.calculateStatistics(values);

    // Trend analysis
    const trendAnalysis = this.analyzeTrend(consumptionData, trendWindow);

    // Pattern identification
    const patterns = this.identifyConsumptionPatterns(consumptionData, patternMinLength);

    // Outlier detection
    const outliers = this.detectOutliers(consumptionData, stats, outlierThreshold);

    // Volatility analysis
    const volatility = stats.standardDeviation / (stats.mean || 1);

    return {
      itemId,
      averageDailyConsumption: stats.mean,
      consumptionTrend: trendAnalysis.trendDirection,
      volatility,
      patterns,
      outliers,
      analysisDate: new Date()
    };
  }

  // Private helper methods

  private static convertToTimeSeries(data: ConsumptionData[]): TimeSeriesData[] {
    const sortedData = data.sort((a, b) => a.date.getTime() - b.date.getTime());
    const dailyData = new Map<string, number>();

    sortedData.forEach(item => {
      const dateKey = item.date.toISOString().split('T')[0];
      dailyData.set(dateKey, (dailyData.get(dateKey) || 0) + item.quantity);
    });

    return Array.from(dailyData.entries()).map(([dateStr, value]) => ({
      date: new Date(dateStr),
      value
    }));
  }

  private static calculateAutocorrelation(values: number[], lag: number): number {
    if (values.length <= lag) return 0;

    const n = values.length - lag;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;

    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < n; i++) {
      numerator += (values[i] - mean) * (values[i + lag] - mean);
    }

    for (let i = 0; i < values.length; i++) {
      denominator += Math.pow(values[i] - mean, 2);
    }

    return denominator > 0 ? numerator / denominator : 0;
  }

  private static calculateSpectralSeasonality(values: number[]): number {
    // Simplified spectral analysis using variance decomposition
    if (values.length < 14) return 0;

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const totalVariance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0);

    // Calculate seasonal variance for common periods
    const periods = [7, 14, 30].filter(p => values.length >= p * 2);
    let maxSeasonalVariance = 0;

    for (const period of periods) {
      const seasonalMeans = new Array(period).fill(0);
      const seasonalCounts = new Array(period).fill(0);

      for (let i = 0; i < values.length; i++) {
        const seasonalIndex = i % period;
        seasonalMeans[seasonalIndex] += values[i];
        seasonalCounts[seasonalIndex]++;
      }

      for (let i = 0; i < period; i++) {
        if (seasonalCounts[i] > 0) {
          seasonalMeans[i] /= seasonalCounts[i];
        }
      }

      const seasonalVariance = seasonalMeans.reduce((sum, seasonalMean) => 
        sum + Math.pow(seasonalMean - mean, 2), 0
      );

      maxSeasonalVariance = Math.max(maxSeasonalVariance, seasonalVariance);
    }

    return totalVariance > 0 ? maxSeasonalVariance / totalVariance : 0;
  }

  private static calculateSeasonalIndices(values: number[], period: number): number[] {
    const seasonalSums = new Array(period).fill(0);
    const seasonalCounts = new Array(period).fill(0);

    for (let i = 0; i < values.length; i++) {
      const seasonalPos = i % period;
      seasonalSums[seasonalPos] += values[i];
      seasonalCounts[seasonalPos]++;
    }

    const seasonalAverages = seasonalSums.map((sum, i) => 
      seasonalCounts[i] > 0 ? sum / seasonalCounts[i] : 1
    );

    const overallAverage = seasonalAverages.reduce((sum, avg) => sum + avg, 0) / period;

    return seasonalAverages.map(avg => overallAverage > 0 ? avg / overallAverage : 1);
  }

  private static getCycleType(cycle: number): SeasonalPattern['type'] {
    if (cycle <= 1) return 'daily';
    if (cycle <= 7) return 'weekly';
    if (cycle <= 31) return 'monthly';
    if (cycle <= 92) return 'quarterly';
    return 'yearly';
  }

  private static calculateSeasonalConfidence(
    dataLength: number,
    patterns: SeasonalPattern[],
    strength: number
  ): number {
    let confidence = 0;

    // Base confidence on data length
    const lengthConfidence = Math.min(1, dataLength / 90); // Full confidence with 90+ days

    // Pattern consistency confidence
    const patternConfidence = patterns.length > 0 ? 
      Math.min(1, patterns.reduce((sum, p) => sum + p.amplitude, 0) / patterns.length) : 0;

    // Strength confidence
    const strengthConfidence = Math.min(1, strength * 2);

    confidence = (lengthConfidence + patternConfidence + strengthConfidence) / 3;

    return Math.max(0, Math.min(1, confidence));
  }

  private static getCurrentSeasonalIndex(indices: number[], cycle: number): number {
    if (indices.length === 0) return 1;
    
    const now = new Date();
    const dayOfYear = Math.floor((now.getTime() - new Date(now.getFullYear(), 0, 0).getTime()) / (1000 * 60 * 60 * 24));
    const seasonalPosition = dayOfYear % cycle;
    
    return indices[seasonalPosition] || 1;
  }

  private static getZScoreForServiceLevel(serviceLevel: number): number {
    // Approximate z-scores for common service levels
    if (serviceLevel >= 0.999) return 3.09;
    if (serviceLevel >= 0.99) return 2.33;
    if (serviceLevel >= 0.95) return 1.65;
    if (serviceLevel >= 0.90) return 1.28;
    if (serviceLevel >= 0.85) return 1.04;
    if (serviceLevel >= 0.80) return 0.84;
    return 0.67; // ~75% service level
  }

  private static calculateStockoutProbability(
    currentStock: number,
    reorderPoint: number,
    avgDemand: number,
    demandStdDev: number,
    leadTime: number
  ): number {
    if (currentStock > reorderPoint) return 0;

    const expectedDemand = avgDemand * leadTime;
    const demandVariance = Math.pow(demandStdDev * Math.sqrt(leadTime), 2);
    
    if (demandVariance === 0) return currentStock < expectedDemand ? 1 : 0;

    // Use normal distribution approximation
    const zScore = (currentStock - expectedDemand) / Math.sqrt(demandVariance);
    return this.normalCDF(-zScore);
  }

  private static calculateOverstockRisk(
    currentStock: number,
    reorderPoint: number,
    avgDemand: number
  ): number {
    const daysOfStock = avgDemand > 0 ? currentStock / avgDemand : 0;
    const reorderDays = avgDemand > 0 ? reorderPoint / avgDemand : 0;
    
    if (daysOfStock <= reorderDays * 2) return 0;
    
    // Risk increases exponentially with excess stock
    const excessRatio = daysOfStock / (reorderDays || 1);
    return Math.min(1, Math.pow(excessRatio - 2, 2) / 10);
  }

  private static calculateServiceLevel(
    reorderPoint: number,
    avgDemand: number,
    demandStdDev: number,
    leadTime: number
  ): number {
    const expectedDemand = avgDemand * leadTime;
    const demandVariance = Math.pow(demandStdDev * Math.sqrt(leadTime), 2);
    
    if (demandVariance === 0) return reorderPoint >= expectedDemand ? 1 : 0;

    const zScore = (reorderPoint - expectedDemand) / Math.sqrt(demandVariance);
    return this.normalCDF(zScore);
  }

  private static normalCDF(z: number): number {
    // Approximation of the standard normal cumulative distribution function
    const t = 1 / (1 + 0.2316419 * Math.abs(z));
    const d = 0.3989423 * Math.exp(-z * z / 2);
    const prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
    
    return z > 0 ? 1 - prob : prob;
  }

  private static calculateStatistics(values: number[]): StatisticalSummary {
    if (values.length === 0) {
      return {
        mean: 0, median: 0, standardDeviation: 0, variance: 0,
        min: 0, max: 0, count: 0,
        percentiles: { p25: 0, p75: 0, p90: 0, p95: 0 }
      };
    }

    const sorted = [...values].sort((a, b) => a - b);
    const count = values.length;
    const mean = values.reduce((sum, val) => sum + val, 0) / count;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / count;
    const standardDeviation = Math.sqrt(variance);

    const getPercentile = (p: number) => {
      const index = Math.ceil(count * p / 100) - 1;
      return sorted[Math.max(0, Math.min(count - 1, index))];
    };

    return {
      mean,
      median: getPercentile(50),
      standardDeviation,
      variance,
      min: sorted[0],
      max: sorted[count - 1],
      count,
      percentiles: {
        p25: getPercentile(25),
        p75: getPercentile(75),
        p90: getPercentile(90),
        p95: getPercentile(95)
      }
    };
  }

  private static detectConsumptionAnomalies(
    item: InventoryItem,
    recentData: ConsumptionData[],
    historicalData: ConsumptionData[],
    zThreshold: number
  ): StockAnomaly[] {
    const anomalies: StockAnomaly[] = [];
    
    const recentValues = recentData.map(d => d.quantity);
    const historicalValues = historicalData.map(d => d.quantity);
    
    const recentStats = this.calculateStatistics(recentValues);
    const historicalStats = this.calculateStatistics(historicalValues);
    
    if (historicalStats.standardDeviation === 0) return anomalies;
    
    const zScore = Math.abs(recentStats.mean - historicalStats.mean) / historicalStats.standardDeviation;
    
    if (zScore > zThreshold) {
      let anomalyType: StockAnomaly['anomalyType'];
      let severity: StockAnomaly['severity'];
      
      if (recentStats.mean > historicalStats.mean * 1.5) {
        anomalyType = 'sudden_spike';
        severity = zScore > 4 ? 'critical' : zScore > 3 ? 'high' : 'medium';
      } else if (recentStats.mean < historicalStats.mean * 0.5) {
        anomalyType = 'sudden_drop';
        severity = zScore > 4 ? 'critical' : zScore > 3 ? 'high' : 'medium';
      } else {
        anomalyType = 'unusual_pattern';
        severity = 'medium';
      }
      
      anomalies.push({
        id: `${item.id}-consumption-${Date.now()}`,
        itemId: item.id,
        anomalyType,
        severity,
        detectedAt: new Date(),
        description: `Consumption ${anomalyType.replace('_', ' ')}: recent average ${recentStats.mean.toFixed(1)} vs historical ${historicalStats.mean.toFixed(1)}`,
        currentValue: recentStats.mean,
        expectedValue: historicalStats.mean,
        deviation: zScore,
        recommendations: this.getAnomalyRecommendations(anomalyType),
        confidence: Math.min(1, zScore / 4)
      });
    }
    
    return anomalies;
  }

  private static detectStockLevelAnomalies(
    item: InventoryItem,
    consumptionData: ConsumptionData[],
    changeThreshold: number
  ): StockAnomaly[] {
    const anomalies: StockAnomaly[] = [];
    
    if (consumptionData.length === 0) return anomalies;
    
    // Calculate expected stock based on consumption
    const recentConsumption = consumptionData.slice(-Math.min(7, consumptionData.length));
    if (recentConsumption.length === 0) return anomalies;
    
    const avgDailyConsumption = recentConsumption.reduce((sum, d) => sum + d.quantity, 0) / recentConsumption.length;
    const daysOfStock = avgDailyConsumption > 0 ? item.currentStock / avgDailyConsumption : Infinity;
    

    
    // Check for stockout risk
    if (daysOfStock < 7 && avgDailyConsumption > 0) {
      anomalies.push({
        id: `${item.id}-stockout-${Date.now()}`,
        itemId: item.id,
        anomalyType: 'stockout_risk',
        severity: daysOfStock < 3 ? 'critical' : daysOfStock < 5 ? 'high' : 'medium',
        detectedAt: new Date(),
        description: `Low stock: only ${daysOfStock.toFixed(1)} days remaining at current consumption rate`,
        currentValue: item.currentStock,
        expectedValue: avgDailyConsumption * 14, // 2 weeks stock
        deviation: Math.max(0, (14 - daysOfStock) / 14),
        recommendations: ['Place urgent order', 'Expedite delivery', 'Consider alternative suppliers'],
        confidence: 0.9
      });
    }
    
    // Check for overstock
    if (daysOfStock > 90 && avgDailyConsumption > 0) {
      anomalies.push({
        id: `${item.id}-overstock-${Date.now()}`,
        itemId: item.id,
        anomalyType: 'overstock',
        severity: daysOfStock > 180 ? 'high' : 'medium',
        detectedAt: new Date(),
        description: `Overstock detected: ${daysOfStock.toFixed(0)} days of stock available`,
        currentValue: item.currentStock,
        expectedValue: avgDailyConsumption * 30, // 1 month stock
        deviation: (daysOfStock - 30) / 30,
        recommendations: ['Reduce future orders', 'Consider promotional activities', 'Review demand forecasts'],
        confidence: 0.8
      });
    }
    
    return anomalies;
  }

  private static detectPatternAnomalies(
    item: InventoryItem,
    consumptionData: ConsumptionData[],
    zThreshold: number
  ): StockAnomaly[] {
    // Detect unusual patterns in consumption (e.g., missing expected seasonal patterns)
    const anomalies: StockAnomaly[] = [];
    
    if (consumptionData.length < 30) return anomalies;
    
    const values = consumptionData.map(d => d.quantity);
    const stats = this.calculateStatistics(values);
    
    // Check for unusual volatility
    const coefficientOfVariation = stats.standardDeviation / (stats.mean || 1);
    
    if (coefficientOfVariation > 2.0) {
      anomalies.push({
        id: `${item.id}-volatility-${Date.now()}`,
        itemId: item.id,
        anomalyType: 'unusual_pattern',
        severity: coefficientOfVariation > 3.0 ? 'high' : 'medium',
        detectedAt: new Date(),
        description: `Highly volatile consumption pattern detected (CV: ${coefficientOfVariation.toFixed(2)})`,
        currentValue: coefficientOfVariation,
        expectedValue: 0.5, // Expected CV
        deviation: coefficientOfVariation / 0.5,
        recommendations: ['Review demand drivers', 'Consider safety stock increase', 'Investigate consumption spikes'],
        confidence: 0.7
      });
    }
    
    return anomalies;
  }

  private static detectTrendAnomalies(
    item: InventoryItem,
    consumptionData: ConsumptionData[],
    changeThreshold: number
  ): StockAnomaly[] {
    const anomalies: StockAnomaly[] = [];
    
    if (consumptionData.length < 14) return anomalies;
    
    const trendAnalysis = this.analyzeTrend(consumptionData, 7);
    
    if (Math.abs(trendAnalysis.changeRate) > changeThreshold && trendAnalysis.trendConfidence > 0.7) {
      const anomalyType = trendAnalysis.changeRate > 0 ? 'sudden_spike' : 'sudden_drop';
      const severity = Math.abs(trendAnalysis.changeRate) > 0.5 ? 'high' : 'medium';
      
      anomalies.push({
        id: `${item.id}-trend-${Date.now()}`,
        itemId: item.id,
        anomalyType,
        severity,
        detectedAt: new Date(),
        description: `Significant trend change detected: ${(trendAnalysis.changeRate * 100).toFixed(1)}% change rate`,
        currentValue: trendAnalysis.changeRate,
        expectedValue: 0,
        deviation: Math.abs(trendAnalysis.changeRate),
        recommendations: trendAnalysis.changeRate > 0 ? 
          ['Investigate demand increase', 'Consider capacity expansion', 'Update forecasts'] :
          ['Investigate demand decrease', 'Review market conditions', 'Adjust inventory levels'],
        confidence: trendAnalysis.trendConfidence
      });
    }
    
    return anomalies;
  }

  private static analyzeTrend(
    consumptionData: ConsumptionData[],
    windowSize: number
  ): TrendAnalysisResult {
    const values = consumptionData.map(d => d.quantity);
    
    if (values.length < windowSize * 2) {
      return {
        itemId: '',
        trendDirection: 'stable',
        trendStrength: 0,
        changeRate: 0,
        trendConfidence: 0,
        projectedChange: 0
      };
    }
    
    // Calculate trend using linear regression
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;
    
    const xMean = x.reduce((sum, val) => sum + val, 0) / n;
    const yMean = y.reduce((sum, val) => sum + val, 0) / n;
    
    let numerator = 0;
    let denominator = 0;
    
    for (let i = 0; i < n; i++) {
      numerator += (x[i] - xMean) * (y[i] - yMean);
      denominator += Math.pow(x[i] - xMean, 2);
    }
    
    const slope = denominator > 0 ? numerator / denominator : 0;
    const intercept = yMean - slope * xMean;
    
    // Calculate R-squared for confidence
    let ssRes = 0;
    let ssTot = 0;
    
    for (let i = 0; i < n; i++) {
      const predicted = intercept + slope * x[i];
      ssRes += Math.pow(y[i] - predicted, 2);
      ssTot += Math.pow(y[i] - yMean, 2);
    }
    
    const r2 = ssTot > 0 ? 1 - (ssRes / ssTot) : 0;
    const trendConfidence = Math.max(0, r2);
    
    // Determine trend direction and strength
    const trendStrength = Math.abs(slope) / (yMean || 1);
    const changeRate = slope / (yMean || 1);
    
    let trendDirection: 'increasing' | 'decreasing' | 'stable';
    if (trendStrength > 0.01 && trendConfidence > 0.1) { // More sensitive thresholds
      trendDirection = slope > 0 ? 'increasing' : 'decreasing';
    } else {
      trendDirection = 'stable';
    }
    
    // Project change over next period
    const projectedChange = slope * windowSize;
    
    return {
      itemId: '',
      trendDirection,
      trendStrength,
      changeRate,
      trendConfidence,
      projectedChange
    };
  }

  private static identifyConsumptionPatterns(
    consumptionData: ConsumptionData[],
    minLength: number
  ): ConsumptionPattern[] {
    const patterns: ConsumptionPattern[] = [];
    const values = consumptionData.map(d => d.quantity);
    
    if (values.length < minLength) return patterns;
    
    const stats = this.calculateStatistics(values);
    const volatility = stats.standardDeviation / (stats.mean || 1);
    
    // Regular pattern detection
    if (volatility < 0.3) {
      patterns.push({
        type: 'regular',
        description: 'Consistent consumption with low variability',
        strength: 1 - volatility
      });
    }
    
    // Irregular pattern detection
    if (volatility > 0.8) { // More sensitive threshold
      patterns.push({
        type: 'irregular',
        description: 'Highly variable consumption pattern',
        strength: Math.min(1, volatility)
      });
    }
    
    // Seasonal pattern detection (simplified)
    const seasonalAnalysis = this.detectSeasonalPatterns('', consumptionData);
    if (seasonalAnalysis.seasonalityPattern.hasSeasonality) {
      patterns.push({
        type: 'seasonal',
        description: `Seasonal pattern detected with ${seasonalAnalysis.dominantCycle}-day cycle`,
        strength: seasonalAnalysis.seasonalStrength,
        frequency: seasonalAnalysis.dominantCycle
      });
    }
    
    // Trending pattern detection
    const trendAnalysis = this.analyzeTrend(consumptionData, 7);
    if (trendAnalysis.trendDirection !== 'stable' && trendAnalysis.trendConfidence > 0.5) {
      patterns.push({
        type: 'trending',
        description: `${trendAnalysis.trendDirection} consumption trend`,
        strength: trendAnalysis.trendStrength
      });
    }
    
    return patterns;
  }

  private static detectOutliers(
    consumptionData: ConsumptionData[],
    stats: StatisticalSummary,
    threshold: number
  ): ConsumptionOutlier[] {
    const outliers: ConsumptionOutlier[] = [];
    
    for (const item of consumptionData) {
      const zScore = Math.abs(item.quantity - stats.mean) / (stats.standardDeviation || 1);
      
      if (zScore > threshold) {
        outliers.push({
          date: item.date,
          value: item.quantity,
          expectedValue: stats.mean,
          deviation: zScore,
          possibleCause: item.quantity > stats.mean ? 
            'Unusually high demand period' : 
            'Unusually low demand period'
        });
      }
    }
    
    return outliers;
  }

  private static getAnomalyRecommendations(type: StockAnomaly['anomalyType']): string[] {
    switch (type) {
      case 'sudden_spike':
        return ['Investigate cause of increased demand', 'Consider increasing safety stock', 'Review supplier capacity'];
      case 'sudden_drop':
        return ['Investigate cause of decreased demand', 'Consider reducing orders', 'Review market conditions'];
      case 'stockout_risk':
        return ['Place urgent order', 'Expedite delivery if possible', 'Consider alternative suppliers'];
      case 'overstock':
        return ['Reduce future orders', 'Consider promotional activities', 'Review demand forecasts'];
      default:
        return ['Monitor closely', 'Review consumption patterns', 'Update forecasting models'];
    }
  }
}