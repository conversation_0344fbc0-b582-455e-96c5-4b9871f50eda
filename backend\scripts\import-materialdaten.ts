import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();

async function importMaterialdaten() {
  try {
    console.log('Starting import of Materialdaten...');
    
    // Read CSV file
    const csvPath = path.join(__dirname, '..', 'material.csv');
    const csvContent = fs.readFileSync(csvPath, 'utf-8');
    const lines = csvContent.split('\n').filter(line => line.trim() !== '');
    
    // Skip header line
    const dataLines = lines.slice(1);
    
    console.log(`Found ${dataLines.length} data rows to import`);
    
    let importedCount = 0;
    let skippedCount = 0;
    
    for (const line of dataLines) {
      const columns = line.split(';').map(col => col.trim());
      
      if (columns.length < 8) {
        console.warn(`Skipping invalid line: ${line}`);
        skippedCount++;
        continue;
      }
      
      const [
        matnr,
        materialkurz<PERSON>,
        ka<PERSON><PERSON><PERSON><PERSON><PERSON>,
        zuschlag<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        biegefaktor,
        ringauslieferung,
        kleinsterErlauberFreiraum,
        bruttogewicht
      ] = columns;
      
      try {
        // Check if record already exists
        const existing = await prisma.materialdaten.findUnique({
          where: { matnr }
        });
        
        if (existing) {
          console.log(`Material ${matnr} already exists, skipping...`);
          skippedCount++;
          continue;
        }
        
        // Create new record
        await prisma.materialdaten.create({
          data: {
            matnr,
            materialkurztext: materialkurztext || null,
            kabeldurchmesser: kabeldurchmesser ? parseFloat(kabeldurchmesser.replace(',', '.')) : null,
            zuschlagKabeldurchmesser: zuschlagKabeldurchmesser ? parseFloat(zuschlagKabeldurchmesser.replace(',', '.')) : null,
            biegefaktor: biegefaktor ? parseFloat(biegefaktor.replace(',', '.')) : null,
            ringauslieferung: ringauslieferung || null,
            kleinsterErlauberFreiraum: kleinsterErlauberFreiraum ? parseFloat(kleinsterErlauberFreiraum.replace(',', '.')) : null,
            bruttogewicht: bruttogewicht ? parseFloat(bruttogewicht.replace(',', '.')) : null
          }
        });
        
        importedCount++;
        
        if (importedCount % 100 === 0) {
          console.log(`Imported ${importedCount} records...`);
        }
        
      } catch (error) {
        console.error(`Error importing material ${matnr}:`, error);
        skippedCount++;
      }
    }
    
    console.log(`\nImport completed:`);
    console.log(`- Imported: ${importedCount} records`);
    console.log(`- Skipped: ${skippedCount} records`);
    
  } catch (error) {
    console.error('Error during import:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the import
importMaterialdaten()
  .then(() => {
    console.log('Import script finished.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Import script failed:', error);
    process.exit(1);
  });