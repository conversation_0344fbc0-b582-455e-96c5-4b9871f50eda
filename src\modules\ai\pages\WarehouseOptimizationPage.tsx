/**
 * Warehouse Optimization Page
 * 
 * Main page for AI-powered warehouse optimization features including
 * layout analysis, item placement optimization, and picking route optimization.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import SmoothTab from '@/components/Animation/kokonutui/smooth-tab';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    Warehouse, 
    MapPin, 
    Route, 
    TrendingUp, 
    BarChart3, 
    RefreshCw,
    AlertTriangle,
    CheckCircle,
    Zap,
    Settings
} from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

// Import warehouse optimization components
import { WarehouseLayoutVisualization } from '@/modules/ai/components/warehouse/WarehouseLayoutVisualization';
import { PickingRouteVisualization } from '@/modules/ai/components/warehouse/PickingRouteVisualization';

// Import service and types
import { WarehouseOptimizerService } from '@/modules/ai/services/warehouse/WarehouseOptimizerService';
import {
    WarehouseItem,
    WarehouseLayoutAnalysis,
    OptimalPlacement,
    WarehouseEfficiencyAnalysis,
    OptimizedPickingRoute,
    PickingRouteRequest,
    PickingItem
} from '@/types/warehouse-optimization';

/**
 * Warehouse Optimization Page Component
 */
export function WarehouseOptimizationPage() {
    const [selectedTab, setSelectedTab] = useState<'overview' | 'layout' | 'picking' | 'efficiency'>('overview');
    const [selectedItem, setSelectedItem] = useState<string | null>(null);
    const [optimizationInProgress, setOptimizationInProgress] = useState(false);
    
    const queryClient = useQueryClient();
    const warehouseService = new WarehouseOptimizerService();

    // Initialize service
    useEffect(() => {
        warehouseService.initialize().catch(error => {
            console.error('Failed to initialize warehouse service:', error);
            toast.error('Warehouse-Service konnte nicht initialisiert werden');
        });
    }, []);

    // Fetch warehouse layout analysis
    const { 
        data: layoutAnalysis, 
        isLoading: layoutLoading, 
        error: layoutError,
        refetch: refetchLayout
    } = useQuery({
        queryKey: ['warehouse-layout-analysis'],
        queryFn: () => warehouseService.analyzeWarehouseLayout(),
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2
    });

    // Fetch optimal placements
    const { 
        data: optimalPlacements, 
        isLoading: placementsLoading,
        refetch: refetchPlacements
    } = useQuery({
        queryKey: ['warehouse-optimal-placements'],
        queryFn: () => warehouseService.generateOptimalPlacements(),
        staleTime: 5 * 60 * 1000,
        retry: 2
    });

    // Fetch efficiency analysis
    const { 
        data: efficiencyAnalysis, 
        isLoading: efficiencyLoading,
        refetch: refetchEfficiency
    } = useQuery({
        queryKey: ['warehouse-efficiency-analysis'],
        queryFn: () => warehouseService.analyzeWarehouseEfficiency(),
        staleTime: 5 * 60 * 1000,
        retry: 2
    });

    // Mock warehouse items for demonstration
    const mockWarehouseItems: WarehouseItem[] = [
        {
            id: 'ITEM_001',
            name: 'Kabel Typ A',
            category: 'Kabel',
            weight: 2.5,
            dimensions: { length: 100, width: 10, height: 5, volume: 5000 },
            currentLocation: {
                id: 'A-1-1',
                zone: 'A',
                aisle: '1',
                shelf: '1',
                position: '1',
                coordinates: { x: 10, y: 5, z: 0 },
                capacity: 100,
                currentUtilization: 0.8,
                accessibilityScore: 9
            },
            accessFrequency: 15,
            lastAccessed: new Date(),
            relationships: [],
            pickingPriority: 'high'
        }
        // Add more mock items as needed
    ];

    // Mock picking route for demonstration
    const mockPickingRoute: OptimizedPickingRoute = {
        routeId: 'route_demo_001',
        orderId: 'ORDER_001',
        sequence: [
            {
                stepNumber: 1,
                itemId: 'ITEM_001',
                location: mockWarehouseItems[0].currentLocation,
                quantity: 2,
                distanceFromPrevious: 0,
                estimatedTime: 2,
                instructions: 'Artikel ITEM_001 (2 Stück) aus A-1-1 entnehmen'
            }
        ],
        totalDistance: 45.5,
        estimatedTime: 12.3,
        efficiency: 0.87,
        alternativeRoutes: []
    };

    // Mock picking items
    const mockPickingItems: PickingItem[] = [
        {
            itemId: 'ITEM_001',
            quantity: 2,
            location: mockWarehouseItems[0].currentLocation,
            priority: 'high',
            weight: 2.5,
            fragile: false
        }
    ];

    // Run full optimization
    const optimizationMutation = useMutation({
        mutationFn: async () => {
            setOptimizationInProgress(true);
            
            // Run all optimizations in parallel
            const [layout, placements, efficiency] = await Promise.all([
                warehouseService.analyzeWarehouseLayout(),
                warehouseService.generateOptimalPlacements(),
                warehouseService.analyzeWarehouseEfficiency()
            ]);
            
            return { layout, placements, efficiency };
        },
        onSuccess: () => {
            // Invalidate and refetch all queries
            queryClient.invalidateQueries({ queryKey: ['warehouse-layout-analysis'] });
            queryClient.invalidateQueries({ queryKey: ['warehouse-optimal-placements'] });
            queryClient.invalidateQueries({ queryKey: ['warehouse-efficiency-analysis'] });
            
            toast.success('Warehouse-Optimierung erfolgreich abgeschlossen');
            setOptimizationInProgress(false);
        },
        onError: (error) => {
            console.error('Optimization failed:', error);
            toast.error('Optimierung fehlgeschlagen');
            setOptimizationInProgress(false);
        }
    });

    // Handle item selection
    const handleItemSelect = (itemId: string) => {
        setSelectedItem(itemId);
    };

    // Handle location selection
    const handleLocationSelect = (locationId: string) => {
        console.log('Selected location:', locationId);
        // Could show location details or trigger actions
    };

    // Get efficiency color
    const getEfficiencyColor = (efficiency: number) => {
        if (efficiency >= 0.8) return 'text-green-600';
        if (efficiency >= 0.6) return 'text-yellow-600';
        return 'text-red-600';
    };

    // Get severity color
    const getSeverityColor = (severity: string) => {
        switch (severity) {
            case 'critical': return 'text-red-600';
            case 'high': return 'text-orange-600';
            case 'medium': return 'text-yellow-600';
            case 'low': return 'text-blue-600';
            default: return 'text-gray-600';
        }
    };

    return (
        <div className="w-full bg-bg min-h-screen p-8 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <Warehouse className="h-8 w-8 text-[#ff7a05]" />
                    <div>
                        <h1 className="text-2xl font-bold">Warehouse-Optimierung</h1>
                        <p className="text-muted-foreground">
                            KI-gestützte Lageroptimierung und Kommissionierungsanalyse
                        </p>
                    </div>
                </div>
                
                <div className="flex items-center gap-2">
                    <Button
                        onClick={() => optimizationMutation.mutate()}
                        disabled={optimizationInProgress}
                        className="flex items-center gap-2"
                    >
                        <RefreshCw className={`h-4 w-4 ${optimizationInProgress ? 'animate-spin' : ''}`} />
                        {optimizationInProgress ? 'Optimiere...' : 'Optimierung starten'}
                    </Button>
                    <Button variant="outline" size="icon">
                        <Settings className="h-4 w-4" />
                    </Button>
                </div>
            </div>

            {/* Error Alert */}
            {layoutError && (
                <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                        Fehler beim Laden der Warehouse-Daten: {layoutError.message}
                    </AlertDescription>
                </Alert>
            )}

            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center space-x-2">
                            <MapPin className="h-5 w-5 text-blue-600" />
                            <div>
                                <p className="text-sm font-medium text-gray-600">Artikel gesamt</p>
                                <p className="text-2xl font-bold text-blue-600">
                                    {layoutAnalysis?.totalItems || '--'}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center space-x-2">
                            <BarChart3 className="h-5 w-5 text-green-600" />
                            <div>
                                <p className="text-sm font-medium text-gray-600">Auslastung</p>
                                <p className="text-2xl font-bold text-green-600">
                                    {layoutAnalysis ? `${(layoutAnalysis.utilizationRate * 100).toFixed(1)}%` : '--'}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center space-x-2">
                            <TrendingUp className="h-5 w-5 text-orange-600" />
                            <div>
                                <p className="text-sm font-medium text-gray-600">Zugänglichkeit</p>
                                <p className="text-2xl font-bold text-orange-600">
                                    {layoutAnalysis ? `${layoutAnalysis.accessibilityScore.toFixed(1)}/10` : '--'}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center space-x-2">
                            <Zap className={`h-5 w-5 ${efficiencyAnalysis ? getEfficiencyColor(efficiencyAnalysis.overallEfficiency) : 'text-gray-400'}`} />
                            <div>
                                <p className="text-sm font-medium text-gray-600">Gesamteffizienz</p>
                                <p className={`text-2xl font-bold ${efficiencyAnalysis ? getEfficiencyColor(efficiencyAnalysis.overallEfficiency) : 'text-gray-400'}`}>
                                    {efficiencyAnalysis ? `${(efficiencyAnalysis.overallEfficiency * 100).toFixed(1)}%` : '--'}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Main Content Tabs */}
            <div className="space-y-6">
                <SmoothTab
                    items={[
                        {
                            id: 'overview',
                            title: 'Übersicht',
                            color: 'bg-blue-500 hover:bg-blue-600',
                            cardContent: (
                                <div className="relative h-full">
                                    <div className="absolute inset-0 overflow-hidden">
                                        <svg
                                            className="absolute bottom-0 w-full h-32"
                                            viewBox="0 0 420 100"
                                            preserveAspectRatio="none"
                                            aria-hidden="true"
                                            role="presentation"
                                        >
                                            <g className="fill-blue-500/15 stroke-blue-500" style={{ strokeWidth: 1 }}>
                                                <path d="M0 50 C 20 40, 40 30, 60 50 C 80 70, 100 60, 120 50 C 140 40, 160 30, 180 50 C 200 70, 220 60, 240 50 C 260 40, 280 30, 300 50 C 320 70, 340 60, 360 50 C 380 40, 400 30, 420 50 L 420 100 L 0 100 Z" />
                                            </g>
                                        </svg>
                                    </div>
                                    <div className="p-6 h-full relative flex flex-col">
                                        <div className="space-y-2">
                                            <h3 className="text-2xl font-semibold tracking-tight">
                                                Lager-Übersicht
                                            </h3>
                                            <p className="text-sm text-black/50 dark:text-white/50 leading-relaxed max-w-[90%]">
                                                Gesamtübersicht der Lageroptimierung
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )
                        },
                        {
                            id: 'layout',
                            title: 'Layout',
                            color: 'bg-green-500 hover:bg-green-600',
                            cardContent: (
                                <div className="relative h-full">
                                    <div className="absolute inset-0 overflow-hidden">
                                        <svg
                                            className="absolute bottom-0 w-full h-32"
                                            viewBox="0 0 420 100"
                                            preserveAspectRatio="none"
                                            aria-hidden="true"
                                            role="presentation"
                                        >
                                            <g className="fill-green-500/15 stroke-green-500" style={{ strokeWidth: 1 }}>
                                                <path d="M0 50 C 20 40, 40 30, 60 50 C 80 70, 100 60, 120 50 C 140 40, 160 30, 180 50 C 200 70, 220 60, 240 50 C 260 40, 280 30, 300 50 C 320 70, 340 60, 360 50 C 380 40, 400 30, 420 50 L 420 100 L 0 100 Z" />
                                            </g>
                                        </svg>
                                    </div>
                                    <div className="p-6 h-full relative flex flex-col">
                                        <div className="space-y-2">
                                            <h3 className="text-2xl font-semibold tracking-tight">
                                                Layout-Optimierung
                                            </h3>
                                            <p className="text-sm text-black/50 dark:text-white/50 leading-relaxed max-w-[90%]">
                                                Optimale Artikelplatzierung im Lager
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )
                        },
                        {
                            id: 'picking',
                            title: 'Kommissionierung',
                            color: 'bg-purple-500 hover:bg-purple-600',
                            cardContent: (
                                <div className="relative h-full">
                                    <div className="absolute inset-0 overflow-hidden">
                                        <svg
                                            className="absolute bottom-0 w-full h-32"
                                            viewBox="0 0 420 100"
                                            preserveAspectRatio="none"
                                            aria-hidden="true"
                                            role="presentation"
                                        >
                                            <g className="fill-purple-500/15 stroke-purple-500" style={{ strokeWidth: 1 }}>
                                                <path d="M0 50 C 20 40, 40 30, 60 50 C 80 70, 100 60, 120 50 C 140 40, 160 30, 180 50 C 200 70, 220 60, 240 50 C 260 40, 280 30, 300 50 C 320 70, 340 60, 360 50 C 380 40, 400 30, 420 50 L 420 100 L 0 100 Z" />
                                            </g>
                                        </svg>
                                    </div>
                                    <div className="p-6 h-full relative flex flex-col">
                                        <div className="space-y-2">
                                            <h3 className="text-2xl font-semibold tracking-tight">
                                                Kommissionier-Routen
                                            </h3>
                                            <p className="text-sm text-black/50 dark:text-white/50 leading-relaxed max-w-[90%]">
                                                Optimierte Wege für die Kommissionierung
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )
                        },
                        {
                            id: 'efficiency',
                            title: 'Effizienz',
                            color: 'bg-amber-500 hover:bg-amber-600',
                            cardContent: (
                                <div className="relative h-full">
                                    <div className="absolute inset-0 overflow-hidden">
                                        <svg
                                            className="absolute bottom-0 w-full h-32"
                                            viewBox="0 0 420 100"
                                            preserveAspectRatio="none"
                                            aria-hidden="true"
                                            role="presentation"
                                        >
                                            <g className="fill-amber-500/15 stroke-amber-500" style={{ strokeWidth: 1 }}>
                                                <path d="M0 50 C 20 40, 40 30, 60 50 C 80 70, 100 60, 120 50 C 140 40, 160 30, 180 50 C 200 70, 220 60, 240 50 C 260 40, 280 30, 300 50 C 320 70, 340 60, 360 50 C 380 40, 400 30, 420 50 L 420 100 L 0 100 Z" />
                                            </g>
                                        </svg>
                                    </div>
                                    <div className="p-6 h-full relative flex flex-col">
                                        <div className="space-y-2">
                                            <h3 className="text-2xl font-semibold tracking-tight">
                                                Effizienz-Analyse
                                            </h3>
                                            <p className="text-sm text-black/50 dark:text-white/50 leading-relaxed max-w-[90%]">
                                                Leistungsmetriken und Verbesserungen
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )
                        }
                    ]}
                    defaultTabId="overview"
                    onChange={(tabId) => setSelectedTab(tabId as any)}
                    className="mb-6"
                />

                {/* Tab Content */}
                {selectedTab === 'overview' && (
                    <div className="space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Inefficiencies */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertTriangle className="h-5 w-5 text-orange-500" />
                                    Erkannte Ineffizienzen
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                {layoutLoading ? (
                                    <div className="flex items-center justify-center h-32">
                                        <RefreshCw className="h-6 w-6 animate-spin" />
                                    </div>
                                ) : layoutAnalysis?.inefficiencies.length === 0 ? (
                                    <div className="flex items-center justify-center h-32">
                                        <div className="text-center">
                                            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
                                            <p className="text-muted-foreground">Keine Ineffizienzen erkannt</p>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="space-y-3">
                                        {layoutAnalysis?.inefficiencies.slice(0, 3).map((inefficiency, index) => (
                                            <div key={index} className="border rounded-lg p-3">
                                                <div className="flex items-center justify-between mb-2">
                                                    <span className="font-medium">
                                                        {inefficiency.type === 'misplaced_high_frequency' ? 'Falsch platzierte häufige Artikel' :
                                                         inefficiency.type === 'poor_accessibility' ? 'Schlechte Zugänglichkeit' :
                                                         inefficiency.type === 'suboptimal_grouping' ? 'Suboptimale Gruppierung' :
                                                         'Untergenutzte Fläche'}
                                                    </span>
                                                    <Badge className={getSeverityColor(inefficiency.severity)}>
                                                        {inefficiency.severity === 'critical' ? 'Kritisch' :
                                                         inefficiency.severity === 'high' ? 'Hoch' :
                                                         inefficiency.severity === 'medium' ? 'Mittel' : 'Niedrig'}
                                                    </Badge>
                                                </div>
                                                <p className="text-sm text-muted-foreground">
                                                    {inefficiency.description}
                                                </p>
                                                <p className="text-xs text-muted-foreground mt-1">
                                                    Zeitverlust: {inefficiency.estimatedImpact.toFixed(1)} Min/Tag
                                                </p>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Optimization Recommendations */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TrendingUp className="h-5 w-5 text-green-500" />
                                    Optimierungsempfehlungen
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                {placementsLoading ? (
                                    <div className="flex items-center justify-center h-32">
                                        <RefreshCw className="h-6 w-6 animate-spin" />
                                    </div>
                                ) : optimalPlacements?.length === 0 ? (
                                    <div className="flex items-center justify-center h-32">
                                        <div className="text-center">
                                            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
                                            <p className="text-muted-foreground">Keine Optimierungen erforderlich</p>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="space-y-3">
                                        {optimalPlacements?.slice(0, 3).map((placement, index) => (
                                            <div key={index} className="border rounded-lg p-3">
                                                <div className="flex items-center justify-between mb-2">
                                                    <span className="font-medium">Artikel {placement.itemId}</span>
                                                    <Badge variant="outline">
                                                        {(placement.confidence * 100).toFixed(0)}% Vertrauen
                                                    </Badge>
                                                </div>
                                                <p className="text-sm text-muted-foreground mb-2">
                                                    {placement.reason}
                                                </p>
                                                <div className="text-xs text-muted-foreground">
                                                    <span>Von: {placement.currentLocation.zone}-{placement.currentLocation.aisle}-{placement.currentLocation.shelf}</span>
                                                    <span className="mx-2">→</span>
                                                    <span>Nach: {placement.recommendedLocation.zone}-{placement.recommendedLocation.aisle}-{placement.recommendedLocation.shelf}</span>
                                                </div>
                                                <p className="text-xs text-green-600 mt-1">
                                                    Zeitersparnis: {placement.expectedBenefit.timeSavingsPerDay.toFixed(1)} Min/Tag
                                                </p>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                    </div>
                )}

                {selectedTab === 'layout' && (
                    <div className="space-y-6">
                    {layoutAnalysis && optimalPlacements && (
                        <WarehouseLayoutVisualization
                            items={mockWarehouseItems}
                            analysis={layoutAnalysis}
                            placements={optimalPlacements}
                            onItemSelect={handleItemSelect}
                            onLocationSelect={handleLocationSelect}
                        />
                    )}
                    </div>
                )}

                {selectedTab === 'picking' && (
                    <div className="space-y-6">
                    <PickingRouteVisualization
                        route={mockPickingRoute}
                        items={mockPickingItems}
                        onStepSelect={(stepNumber) => console.log('Selected step:', stepNumber)}
                        onAlternativeSelect={(routeId) => console.log('Selected alternative:', routeId)}
                    />
                    </div>
                )}

                {selectedTab === 'efficiency' && (
                    <div className="space-y-6">
                    {efficiencyLoading ? (
                        <div className="flex items-center justify-center h-64">
                            <RefreshCw className="h-8 w-8 animate-spin" />
                        </div>
                    ) : efficiencyAnalysis ? (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Efficiency Metrics */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Effizienz-Metriken</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <span>Gesamteffizienz</span>
                                            <span className={`font-bold ${getEfficiencyColor(efficiencyAnalysis.overallEfficiency)}`}>
                                                {(efficiencyAnalysis.overallEfficiency * 100).toFixed(1)}%
                                            </span>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span>Kommissioniereffizienz</span>
                                            <span className={`font-bold ${getEfficiencyColor(efficiencyAnalysis.pickingEfficiency)}`}>
                                                {(efficiencyAnalysis.pickingEfficiency * 100).toFixed(1)}%
                                            </span>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span>Lagereffizienz</span>
                                            <span className={`font-bold ${getEfficiencyColor(efficiencyAnalysis.storageEfficiency)}`}>
                                                {(efficiencyAnalysis.storageEfficiency * 100).toFixed(1)}%
                                            </span>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <span>Zugänglichkeitseffizienz</span>
                                            <span className={`font-bold ${getEfficiencyColor(efficiencyAnalysis.accessibilityEfficiency)}`}>
                                                {(efficiencyAnalysis.accessibilityEfficiency * 100).toFixed(1)}%
                                            </span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Bottlenecks */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Engpässe</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    {efficiencyAnalysis.bottlenecks.length === 0 ? (
                                        <div className="flex items-center justify-center h-32">
                                            <div className="text-center">
                                                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
                                                <p className="text-muted-foreground">Keine Engpässe erkannt</p>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="space-y-3">
                                            {efficiencyAnalysis.bottlenecks.map((bottleneck, index) => (
                                                <div key={index} className="border rounded-lg p-3">
                                                    <div className="flex items-center justify-between mb-2">
                                                        <span className="font-medium">{bottleneck.location}</span>
                                                        <Badge className={getSeverityColor(bottleneck.severity)}>
                                                            {bottleneck.severity === 'high' ? 'Hoch' :
                                                             bottleneck.severity === 'medium' ? 'Mittel' : 'Niedrig'}
                                                        </Badge>
                                                    </div>
                                                    <p className="text-sm text-muted-foreground">
                                                        {bottleneck.description}
                                                    </p>
                                                    <p className="text-xs text-muted-foreground mt-1">
                                                        Auswirkung: {bottleneck.impact.toFixed(1)} Min/Tag
                                                    </p>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>
                    ) : (
                        <div className="flex items-center justify-center h-64">
                            <p className="text-muted-foreground">Keine Effizienz-Daten verfügbar</p>
                        </div>
                    )}
                    </div>
                )}
            </div>
        </div>
    );
}

export default WarehouseOptimizationPage;