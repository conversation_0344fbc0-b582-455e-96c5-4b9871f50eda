/**
 * RAG (Retrieval-Augmented Generation) Service
 * 
 * Implements RAG functionality by combining vector search with LLM generation.
 * Provides context-aware AI responses using retrieved relevant information.
 */

import { AIBaseService, AIServiceConfig } from '../base/AIBaseService';
import { VectorDatabaseService } from '../vector/VectorDatabaseService';
import { EmbeddingService } from '../embedding/EmbeddingService';
import {
  EnhancedQuery,
  RAGResponse,
  Source,
  QueryContext,
  Document,
  DocumentChunk,
  VectorMetadata,
  AIServiceError
} from '../types';

/**
 * RAG Service Configuration
 */
export interface RAGServiceConfig extends AIServiceConfig {
  maxContextLength?: number;
  maxSources?: number;
  minSimilarityThreshold?: number;
  chunkSize?: number;
  chunkOverlap?: number;
  includeMetadata?: boolean;
}

/**
 * Document indexing request
 */
export interface DocumentIndexRequest {
  document: Document;
  chunkSize?: number;
  chunkOverlap?: number;
  metadata?: any;
}

/**
 * RAG Service for implementing Retrieval-Augmented Generation
 */
export class RAGService extends AIBaseService {
  readonly serviceName = 'RAGService';
  private ragConfig: RAGServiceConfig;
  private vectorService: VectorDatabaseService;
  private embeddingService: EmbeddingService;

  constructor(
    vectorService: VectorDatabaseService,
    embeddingService: EmbeddingService,
    config: RAGServiceConfig = {}
  ) {
    super(config);
    this.vectorService = vectorService;
    this.embeddingService = embeddingService;
    this.ragConfig = {
      maxContextLength: 8000,
      maxSources: 5,
      minSimilarityThreshold: 0.7,
      chunkSize: 1000,
      chunkOverlap: 200,
      includeMetadata: true,
      ...config
    };
  }

  /**
   * Initialize the RAG service
   */
  protected async onInitialize(): Promise<void> {
    // Ensure dependent services are initialized
    const vectorStatus = await this.vectorService.healthCheck();
    if (!vectorStatus.isInitialized) {
      await this.vectorService.initialize();
    }

    const embeddingStatus = await this.embeddingService.healthCheck();
    if (!embeddingStatus.isInitialized) {
      await this.embeddingService.initialize();
    }

    this.log('RAG service initialized successfully');
  }

  /**
   * Create embedding for text
   */
  async createEmbedding(text: string): Promise<number[]> {
    return this.handleAIError(
      async () => {
        return await this.embeddingService.createEmbedding(text);
      },
      async () => {
        // Fallback: return zero vector
        const dimensions = this.aiConfig.vectorDimensions || 1536;
        return new Array(dimensions).fill(0);
      },
      AIServiceError.EMBEDDING_FAILED
    );
  }

  /**
   * Store vector with content and metadata
   */
  async storeVector(id: string, content: string, metadata: VectorMetadata): Promise<void> {
    return this.handleAIError(
      async () => {
        // Generate embedding for content
        const embedding = await this.embeddingService.createEmbedding(content);

        // Store vector with metadata
        const vectorMetadata: VectorMetadata = {
          content,
          ...metadata
        };

        await this.vectorService.insertVector(id, embedding, vectorMetadata);
        this.log(`Vector stored successfully: ${id}`);
      },
      async () => {
        throw new Error('No fallback available for vector storage');
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Search for similar content
   */
  async searchSimilar(
    query: string,
    limit?: number,
    threshold?: number
  ): Promise<Source[]> {
    return this.handleAIError(
      async () => {
        // Generate embedding for query
        const queryEmbedding = await this.embeddingService.createEmbedding(query);

        // Search for similar vectors
        const matches = await this.vectorService.searchSimilar(
          queryEmbedding,
          limit || this.ragConfig.maxSources || 5,
          threshold || this.ragConfig.minSimilarityThreshold || 0.7
        );

        // Convert matches to sources
        const sources: Source[] = matches.map(match => ({
          id: match.id,
          title: match.metadata.title || 'Untitled',
          content: match.metadata.content || '',
          url: match.metadata.source,
          relevance: match.similarity
        }));

        this.log(`Found ${sources.length} similar sources for query`);
        return sources;
      },
      async () => {
        // Fallback: return empty sources
        this.log('Vector search failed, returning empty sources');
        return [];
      },
      AIServiceError.VECTOR_SEARCH_FAILED
    );
  }

  /**
   * Enhance query with context from vector search
   */
  async enhanceQuery(query: string, context?: QueryContext): Promise<EnhancedQuery> {
    return this.handleAIError(
      async () => {
        // Generate embedding for the query
        const embeddings = await this.embeddingService.createEmbedding(query);

        // Search for relevant context
        const sources = await this.searchSimilar(
          query,
          this.ragConfig.maxSources,
          this.ragConfig.minSimilarityThreshold
        );

        // Extract context from sources
        const contextStrings = sources.map(source => source.content);

        // Get relevant data based on context (if provided)
        const relevantData = context ? await this.getRelevantData(context) : [];

        const enhancedQuery: EnhancedQuery = {
          originalQuery: query,
          context: contextStrings,
          relevantData,
          embeddings
        };

        this.log(`Enhanced query with ${contextStrings.length} context sources`);
        return enhancedQuery;
      },
      async () => {
        // Fallback: return query without enhancement
        this.log('Query enhancement failed, returning original query');
        return {
          originalQuery: query,
          context: [],
          relevantData: [],
          embeddings: []
        };
      },
      AIServiceError.EMBEDDING_FAILED
    );
  }

  /**
   * Generate RAG response using enhanced query
   */
  async generateResponse(query: EnhancedQuery): Promise<RAGResponse> {
    return this.handleAIError(
      async () => {
        // Build context for LLM
        const contextText = this.buildContextText(query);

        // Generate response using OpenRouter (this would integrate with existing OpenRouter service)
        const response = await this.generateLLMResponse(query.originalQuery, contextText);

        // Extract sources used in the response
        const sources = await this.extractUsedSources(query);

        const ragResponse: RAGResponse = {
          response,
          sources,
          confidence: this.calculateConfidence(query),
          usedContext: query.context
        };

        this.log(`Generated RAG response with ${sources.length} sources`);
        return ragResponse;
      },
      async () => {
        // Fallback: return basic response without RAG
        this.log('RAG response generation failed, returning basic response');
        return {
          response: 'Entschuldigung, ich konnte keine kontextuelle Antwort generieren. Bitte versuchen Sie es erneut.',
          sources: [],
          confidence: 0,
          usedContext: []
        };
      },
      AIServiceError.MODEL_UNAVAILABLE
    );
  }

  /**
   * Index a document for RAG
   */
  async indexDocument(document: Document): Promise<void> {
    return this.handleAIError(
      async () => {
        // Split document into chunks
        const chunks = this.chunkDocument(
          document.content,
          this.ragConfig.chunkSize || 1000,
          this.ragConfig.chunkOverlap || 200
        );

        // Process chunks in batches
        const batchSize = 10;
        for (let i = 0; i < chunks.length; i += batchSize) {
          const batch = chunks.slice(i, i + batchSize);

          await Promise.all(batch.map(async (chunk, index) => {
            const chunkId = `${document.id}_chunk_${i + index}`;
            const metadata: VectorMetadata = {
              content: chunk,
              documentId: document.id,
              chunkIndex: i + index,
              title: document.title,
              source: document.sourceUrl,
              dataType: document.documentType,
              timestamp: document.createdAt.toISOString()
            };

            await this.storeVector(chunkId, chunk, metadata);
          }));
        }

        this.log(`Indexed document: ${document.id} (${chunks.length} chunks)`);
      },
      async () => {
        throw new Error('No fallback available for document indexing');
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Update knowledge base with multiple documents
   */
  async updateKnowledgeBase(documents: Document[]): Promise<void> {
    return this.handleAIError(
      async () => {
        for (const document of documents) {
          await this.indexDocument(document);
        }
        this.log(`Updated knowledge base with ${documents.length} documents`);
      },
      async () => {
        throw new Error('No fallback available for knowledge base update');
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Delete document from index
   */
  async deleteFromIndex(documentId: string): Promise<void> {
    return this.handleAIError(
      async () => {
        // Find all chunks for this document
        const stats = await this.vectorService.getIndexStats();

        // Delete all chunks (this is a simplified approach)
        // In a real implementation, you'd query for all chunk IDs first
        let chunkIndex = 0;
        let deletedCount = 0;

        while (chunkIndex < 1000) { // Reasonable upper limit
          const chunkId = `${documentId}_chunk_${chunkIndex}`;
          const deleted = await this.vectorService.deleteVector(chunkId);
          if (deleted) {
            deletedCount++;
          } else if (deletedCount === 0 && chunkIndex > 10) {
            // No chunks found and we've tried enough
            break;
          }
          chunkIndex++;
        }

        this.log(`Deleted ${deletedCount} chunks for document: ${documentId}`);
      },
      async () => {
        throw new Error('No fallback available for document deletion');
      },
      AIServiceError.DATABASE_ERROR
    );
  }

  /**
   * Chunk document into smaller pieces
   */
  private chunkDocument(content: string, chunkSize: number, overlap: number): string[] {
    const chunks: string[] = [];
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);

    let currentChunk = '';
    let currentSize = 0;

    for (const sentence of sentences) {
      const sentenceSize = sentence.length;

      if (currentSize + sentenceSize > chunkSize && currentChunk.length > 0) {
        chunks.push(currentChunk.trim());

        // Handle overlap
        if (overlap > 0) {
          const overlapText = currentChunk.slice(-overlap);
          currentChunk = overlapText + sentence;
          currentSize = overlapText.length + sentenceSize;
        } else {
          currentChunk = sentence;
          currentSize = sentenceSize;
        }
      } else {
        currentChunk += (currentChunk.length > 0 ? '. ' : '') + sentence;
        currentSize += sentenceSize + (currentChunk.length > sentence.length ? 2 : 0);
      }
    }

    if (currentChunk.trim().length > 0) {
      chunks.push(currentChunk.trim());
    }

    return chunks;
  }

  /**
   * Build context text for LLM
   */
  private buildContextText(query: EnhancedQuery): string {
    const maxLength = this.ragConfig.maxContextLength || 8000;
    let contextText = '';

    // Add retrieved context
    for (const context of query.context) {
      if (contextText.length + context.length > maxLength) {
        break;
      }
      contextText += context + '\n\n';
    }

    // Add relevant data if available
    if (query.relevantData.length > 0) {
      const dataContext = JSON.stringify(query.relevantData, null, 2);
      if (contextText.length + dataContext.length <= maxLength) {
        contextText += 'Relevante Daten:\n' + dataContext + '\n\n';
      }
    }

    return contextText.trim();
  }

  /**
   * Generate LLM response (placeholder - would integrate with OpenRouter service)
   */
  private async generateLLMResponse(query: string, context: string): Promise<string> {
    // This would integrate with the existing OpenRouter service
    // For now, return a placeholder response
    return `Basierend auf dem verfügbaren Kontext: ${query}`;
  }

  /**
   * Extract sources used in response
   */
  private async extractUsedSources(query: EnhancedQuery): Promise<Source[]> {
    // This would analyze which sources were actually used in the response
    // For now, return sources from the context
    return query.context.map((content, index) => ({
      id: `source_${index}`,
      title: `Quelle ${index + 1}`,
      content: content.substring(0, 200) + '...',
      relevance: 0.8 - (index * 0.1)
    }));
  }

  /**
   * Calculate confidence score for response
   */
  private calculateConfidence(query: EnhancedQuery): number {
    if (query.context.length === 0) {
      return 0.3; // Low confidence without context
    }

    // Base confidence on number and quality of sources
    const baseConfidence = Math.min(0.9, 0.5 + (query.context.length * 0.1));

    // Adjust based on embedding quality (if available)
    if (query.embeddings.length > 0) {
      const embeddingQuality = query.embeddings.reduce((sum, val) => sum + Math.abs(val), 0) / query.embeddings.length;
      return Math.min(0.95, baseConfidence * (1 + embeddingQuality * 0.1));
    }

    return baseConfidence;
  }

  /**
   * Get relevant data based on context (placeholder)
   */
  private async getRelevantData(context: QueryContext): Promise<any[]> {
    // This would integrate with existing repositories to get relevant data
    // For now, return empty array
    return [];
  }

  /**
   * Health check for RAG service
   */
  protected async onHealthCheck(): Promise<void> {
    // Check dependent services
    const vectorStatus = await this.vectorService.healthCheck();
    if (!vectorStatus.isHealthy) {
      throw new Error('Vector database service is unhealthy');
    }

    const embeddingStatus = await this.embeddingService.healthCheck();
    if (!embeddingStatus.isHealthy) {
      throw new Error('Embedding service is unhealthy');
    }

    // Test basic RAG functionality
    try {
      const testQuery = await this.enhanceQuery('test query');
      if (!testQuery.originalQuery) {
        throw new Error('Query enhancement test failed');
      }
    } catch (error) {
      throw new Error(`RAG functionality test failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get RAG service statistics
   */
  getRAGStats() {
    const performance = this.getPerformanceMetrics();
    return {
      ...performance,
      maxContextLength: this.ragConfig.maxContextLength,
      maxSources: this.ragConfig.maxSources,
      minSimilarityThreshold: this.ragConfig.minSimilarityThreshold,
      chunkSize: this.ragConfig.chunkSize
    };
  }
}