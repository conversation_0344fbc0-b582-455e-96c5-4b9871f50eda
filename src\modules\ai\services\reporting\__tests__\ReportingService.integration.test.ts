/**
 * Reporting Service Integration Tests
 * 
 * Tests the reporting service with real repository interactions
 * and end-to-end report generation workflows.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ReportingService, ReportTemplate, ReportGenerationRequest } from '../ReportingService';

// Mock all repositories for integration tests
vi.mock('@/repositories/delivery.repository', () => ({
  deliveryRepository: {
    getAll: vi.fn().mockResolvedValue([
      { id: '1', value: 95, date: '2024-01-01', department: 'dispatch' },
      { id: '2', value: 87, date: '2024-01-02', department: 'dispatch' },
      { id: '3', value: 92, date: '2024-01-03', department: 'dispatch' }
    ]),
    invalidateCache: vi.fn()
  }
}));

vi.mock('@/repositories/production.repository', () => ({
  productionRepository: {
    getAll: vi.fn().mockResolvedValue([
      { id: '1', value: 1000, date: '2024-01-01', department: 'cutting' },
      { id: '2', value: 1200, date: '2024-01-02', department: 'cutting' },
      { id: '3', value: 1100, date: '2024-01-03', department: 'cutting' }
    ]),
    invalidateCache: vi.fn()
  }
}));

vi.mock('@/repositories/warehouse.repository', () => ({
  warehouseRepository: {
    getAll: vi.fn().mockResolvedValue([
      { id: '1', value: 85, date: '2024-01-01', department: 'incoming-goods' },
      { id: '2', value: 90, date: '2024-01-02', department: 'incoming-goods' },
      { id: '3', value: 88, date: '2024-01-03', department: 'incoming-goods' }
    ]),
    invalidateCache: vi.fn()
  }
}));

vi.mock('@/repositories/supplier.repository', () => ({
  supplierRepository: {
    getAll: vi.fn().mockResolvedValue([
      { id: '1', value: 75, date: '2024-01-01', supplier: 'Supplier A' },
      { id: '2', value: 80, date: '2024-01-02', supplier: 'Supplier B' },
      { id: '3', value: 78, date: '2024-01-03', supplier: 'Supplier C' }
    ]),
    invalidateCache: vi.fn()
  }
}));

vi.mock('@/repositories/system.repository', () => ({
  systemRepository: {
    getAll: vi.fn().mockResolvedValue([
      { id: '1', value: 99, date: '2024-01-01', metric: 'uptime' },
      { id: '2', value: 98, date: '2024-01-02', metric: 'uptime' },
      { id: '3', value: 97, date: '2024-01-03', metric: 'uptime' }
    ]),
    invalidateCache: vi.fn()
  }
}));

describe('ReportingService Integration Tests', () => {
  let reportingService: ReportingService;

  beforeEach(async () => {
    reportingService = new ReportingService({
      enableLogging: false,
      enableVectorCache: false
    });
    
    await reportingService.initialize();
  });

  afterEach(async () => {
    await reportingService.destroy();
  });

  describe('End-to-End Report Generation', () => {
    it('should generate a complete KPI report with all sections', async () => {
      // Create a comprehensive template
      const template = await reportingService.createTemplate({
        name: 'Comprehensive KPI Report',
        description: 'Full KPI report with multiple sections and insights',
        type: 'kpi',
        department: 'all',
        sections: [
          {
            id: 'delivery-performance',
            title: 'Lieferleistung',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'avg',
            timeRange: 'month',
            order: 1
          },
          {
            id: 'production-metrics',
            title: 'Produktionsmetriken',
            type: 'chart',
            dataSource: 'production',
            chartType: 'line',
            aggregation: 'sum',
            timeRange: 'month',
            order: 2
          },
          {
            id: 'warehouse-efficiency',
            title: 'Lagereffizienz',
            type: 'table',
            dataSource: 'warehouse',
            aggregation: 'avg',
            timeRange: 'month',
            order: 3
          }
        ],
        format: 'pdf',
        isActive: true
      });

      // Generate report with full features
      const request: ReportGenerationRequest = {
        templateId: template.id,
        timeRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        },
        includeInsights: true,
        includeRecommendations: true,
        customFilters: {
          department: 'all'
        }
      };

      const report = await reportingService.generateReport(request);

      // Verify report structure
      expect(report).toBeDefined();
      expect(report.id).toBeDefined();
      expect(report.templateId).toBe(template.id);
      expect(report.title).toContain('Comprehensive KPI Report');
      expect(report.generatedAt).toBeInstanceOf(Date);
      
      // Verify time range
      expect(report.timeRange.start).toEqual(request.timeRange!.start);
      expect(report.timeRange.end).toEqual(request.timeRange!.end);
      
      // Verify sections
      expect(report.sections).toHaveLength(3);
      expect(report.sections[0].title).toBe('Lieferleistung');
      expect(report.sections[1].title).toBe('Produktionsmetriken');
      expect(report.sections[2].title).toBe('Lagereffizienz');
      
      // Verify chart configuration for chart section
      const chartSection = report.sections.find(s => s.type === 'chart');
      expect(chartSection).toBeDefined();
      expect(chartSection!.chartConfig).toBeDefined();
      expect(chartSection!.chartConfig.responsive).toBe(true);
      
      // Verify insights and recommendations are generated
      expect(report.insights).toBeDefined();
      expect(report.recommendations).toBeDefined();
      
      // Verify metadata
      expect(report.metadata.dataPoints).toBeGreaterThanOrEqual(0);
      expect(report.metadata.processingTime).toBeGreaterThan(0);
      expect(report.metadata.sources).toContain('delivery');
      expect(report.metadata.sources).toContain('production');
      expect(report.metadata.sources).toContain('warehouse');
      
      // Verify format
      expect(report.format).toBe('pdf');
    });

    it('should generate department-specific reports with targeted insights', async () => {
      // Create dispatch-specific template
      const dispatchTemplate = await reportingService.createTemplate({
        name: 'Versand Performance Report',
        description: 'Detailed dispatch department analysis',
        type: 'performance',
        department: 'dispatch',
        sections: [
          {
            id: 'delivery-times',
            title: 'Lieferzeiten',
            type: 'chart',
            dataSource: 'delivery',
            chartType: 'line',
            aggregation: 'avg',
            filters: { department: 'dispatch' },
            order: 1
          },
          {
            id: 'shipping-costs',
            title: 'Versandkosten',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'sum',
            filters: { department: 'dispatch' },
            order: 2
          }
        ],
        format: 'excel',
        isActive: true
      });

      const report = await reportingService.generateReport({
        templateId: dispatchTemplate.id,
        includeInsights: true,
        includeRecommendations: true
      });

      // Verify dispatch-specific content
      expect(report.sections).toHaveLength(2);
      expect(report.sections[0].title).toBe('Lieferzeiten');
      expect(report.sections[1].title).toBe('Versandkosten');
      
      // Verify dispatch-specific recommendations
      const dispatchRecommendations = report.recommendations.filter(rec => 
        rec.title.includes('Versand') || rec.kpiImpact.includes('Lieferzeit')
      );
      expect(dispatchRecommendations.length).toBeGreaterThan(0);
    });

    it('should handle multiple report formats correctly', async () => {
      const template = await reportingService.createTemplate({
        name: 'Multi-Format Test',
        description: 'Test different output formats',
        type: 'kpi',
        sections: [
          {
            id: 'test-section',
            title: 'Test Data',
            type: 'kpi',
            dataSource: 'system',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      // Test PDF format
      const pdfReport = await reportingService.generateReport({
        templateId: template.id,
        format: 'pdf'
      });
      expect(pdfReport.format).toBe('pdf');

      // Test Excel format
      const excelReport = await reportingService.generateReport({
        templateId: template.id,
        format: 'excel'
      });
      expect(excelReport.format).toBe('excel');

      // Test HTML format
      const htmlReport = await reportingService.generateReport({
        templateId: template.id,
        format: 'html'
      });
      expect(htmlReport.format).toBe('html');

      // Test JSON format (default from template)
      const jsonReport = await reportingService.generateReport({
        templateId: template.id
      });
      expect(jsonReport.format).toBe('json');
    });
  });

  describe('Template Lifecycle Management', () => {
    it('should manage complete template lifecycle', async () => {
      // Create template
      const template = await reportingService.createTemplate({
        name: 'Lifecycle Test Template',
        description: 'Testing complete lifecycle',
        type: 'analysis',
        department: 'cutting',
        sections: [
          {
            id: 'cutting-efficiency',
            title: 'Schnitteffizenz',
            type: 'kpi',
            dataSource: 'production',
            aggregation: 'avg',
            order: 1
          }
        ],
        format: 'pdf',
        schedule: {
          frequency: 'weekly',
          time: '09:00',
          dayOfWeek: 1, // Monday
          timezone: 'Europe/Berlin',
          isActive: false // Start inactive
        },
        recipients: ['<EMAIL>'],
        isActive: true
      });

      expect(template.id).toBeDefined();
      expect(template.schedule).toBeDefined();
      expect(template.recipients).toHaveLength(1);

      // Update template to activate scheduling
      const updatedTemplate = await reportingService.updateTemplate(template.id, {
        schedule: {
          ...template.schedule!,
          isActive: true
        }
      });

      expect(updatedTemplate.schedule!.isActive).toBe(true);
      expect(updatedTemplate.updatedAt.getTime()).toBeGreaterThan(template.updatedAt.getTime());

      // Generate report from template
      const report = await reportingService.generateReport({
        templateId: template.id
      });

      expect(report.templateId).toBe(template.id);
      expect(report.sections).toHaveLength(1);
      expect(report.sections[0].title).toBe('Schnitteffizenz');

      // Delete template
      const deleted = await reportingService.deleteTemplate(template.id);
      expect(deleted).toBe(true);

      // Verify template is gone
      const retrieved = await reportingService.getTemplate(template.id);
      expect(retrieved).toBeNull();
    });

    it('should handle template filtering and sorting', async () => {
      // Create multiple templates with different characteristics
      const templates = await Promise.all([
        reportingService.createTemplate({
          name: 'Alpha KPI Report',
          description: 'First alphabetically',
          type: 'kpi',
          department: 'dispatch',
          sections: [],
          format: 'pdf',
          isActive: true
        }),
        reportingService.createTemplate({
          name: 'Beta Performance Report',
          description: 'Second alphabetically',
          type: 'performance',
          department: 'cutting',
          sections: [],
          format: 'excel',
          isActive: false
        }),
        reportingService.createTemplate({
          name: 'Gamma Analysis Report',
          description: 'Third alphabetically',
          type: 'analysis',
          department: 'incoming-goods',
          sections: [],
          format: 'html',
          isActive: true
        })
      ]);

      // Test no filter (should include default template + 3 created = 4 total)
      const allTemplates = await reportingService.getTemplates();
      expect(allTemplates.length).toBeGreaterThanOrEqual(4);

      // Test department filter
      const dispatchTemplates = await reportingService.getTemplates({ department: 'dispatch' });
      expect(dispatchTemplates.length).toBeGreaterThanOrEqual(1);
      const alphaTemplate = dispatchTemplates.find(t => t.name === 'Alpha KPI Report');
      expect(alphaTemplate).toBeDefined();

      // Test type filter
      const kpiTemplates = await reportingService.getTemplates({ type: 'kpi' });
      expect(kpiTemplates.length).toBeGreaterThanOrEqual(2); // 1 created + 1 default

      // Test active filter
      const activeTemplates = await reportingService.getTemplates({ isActive: true });
      const inactiveTemplates = await reportingService.getTemplates({ isActive: false });
      
      expect(activeTemplates.length).toBeGreaterThan(0);
      expect(inactiveTemplates.length).toBeGreaterThan(0);
      expect(activeTemplates.length + inactiveTemplates.length).toBe(allTemplates.length);

      // Test combined filters
      const activeKpiTemplates = await reportingService.getTemplates({ 
        type: 'kpi', 
        isActive: true 
      });
      expect(activeKpiTemplates.length).toBeGreaterThanOrEqual(1);

      // Verify alphabetical sorting
      const sortedNames = allTemplates.map(t => t.name);
      const expectedSorted = [...sortedNames].sort();
      expect(sortedNames).toEqual(expectedSorted);
    });
  });

  describe('Advanced Insight Generation', () => {
    it('should generate comprehensive insights from complex data patterns', async () => {
      const template = await reportingService.createTemplate({
        name: 'Advanced Insights Test',
        description: 'Testing advanced insight generation',
        type: 'analysis',
        department: 'all',
        sections: [
          {
            id: 'trend-section',
            title: 'Trend Analysis',
            type: 'chart',
            dataSource: 'delivery',
            chartType: 'line',
            aggregation: 'avg',
            order: 1
          },
          {
            id: 'correlation-section',
            title: 'Correlation Analysis',
            type: 'chart',
            dataSource: 'production',
            chartType: 'scatter',
            aggregation: 'sum',
            order: 2
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({
        templateId: template.id,
        includeInsights: true,
        includeRecommendations: true
      });

      // Verify insights are generated
      expect(report.insights).toBeDefined();
      
      // Check for different types of insights
      const insightTypes = report.insights.map(insight => insight.type);
      const uniqueTypes = [...new Set(insightTypes)];
      
      // Should have at least trend analysis
      expect(uniqueTypes.length).toBeGreaterThan(0);
      
      // Verify insight structure
      for (const insight of report.insights) {
        expect(insight.id).toBeDefined();
        expect(insight.type).toMatch(/^(trend|anomaly|pattern|correlation)$/);
        expect(insight.title).toBeDefined();
        expect(insight.description).toBeDefined();
        expect(insight.severity).toMatch(/^(low|medium|high)$/);
        expect(insight.confidence).toBeGreaterThan(0);
        expect(insight.confidence).toBeLessThanOrEqual(1);
        expect(insight.dataPoints).toBeDefined();
        expect(Array.isArray(insight.dataPoints)).toBe(true);
      }
    });

    it('should generate actionable recommendations with proper prioritization', async () => {
      const template = await reportingService.createTemplate({
        name: 'Recommendations Test',
        description: 'Testing recommendation generation and prioritization',
        type: 'performance',
        department: 'dispatch',
        sections: [
          {
            id: 'performance-metrics',
            title: 'Performance Metrics',
            type: 'kpi',
            dataSource: 'delivery',
            aggregation: 'avg',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      const report = await reportingService.generateReport({
        templateId: template.id,
        includeRecommendations: true
      });

      // Verify recommendations are generated
      expect(report.recommendations).toBeDefined();
      expect(report.recommendations.length).toBeGreaterThan(0);

      // Verify recommendation structure
      for (const recommendation of report.recommendations) {
        expect(recommendation.id).toBeDefined();
        expect(recommendation.category).toMatch(/^(efficiency|cost|quality|process|resource)$/);
        expect(recommendation.title).toBeDefined();
        expect(recommendation.description).toBeDefined();
        expect(recommendation.priority).toMatch(/^(low|medium|high|critical)$/);
        expect(recommendation.impact).toMatch(/^(low|medium|high)$/);
        expect(recommendation.effort).toMatch(/^(low|medium|high)$/);
        expect(recommendation.expectedBenefit).toBeDefined();
        expect(Array.isArray(recommendation.actionItems)).toBe(true);
        expect(recommendation.actionItems.length).toBeGreaterThan(0);
        expect(Array.isArray(recommendation.kpiImpact)).toBe(true);
      }

      // Verify prioritization (critical > high > medium > low)
      const priorityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
      for (let i = 0; i < report.recommendations.length - 1; i++) {
        const current = report.recommendations[i];
        const next = report.recommendations[i + 1];
        expect(priorityOrder[current.priority]).toBeGreaterThanOrEqual(priorityOrder[next.priority]);
      }

      // Verify department-specific recommendations for dispatch
      const dispatchSpecificRecs = report.recommendations.filter(rec =>
        rec.title.includes('Versand') || 
        rec.kpiImpact.includes('Lieferzeit') ||
        rec.kpiImpact.includes('Transportkosten')
      );
      expect(dispatchSpecificRecs.length).toBeGreaterThan(0);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle large datasets efficiently', async () => {
      const template = await reportingService.createTemplate({
        name: 'Performance Test',
        description: 'Testing performance with large datasets',
        type: 'analysis',
        sections: [
          {
            id: 'large-dataset',
            title: 'Large Dataset Analysis',
            type: 'table',
            dataSource: 'delivery',
            aggregation: 'count',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      const startTime = Date.now();
      
      const report = await reportingService.generateReport({
        templateId: template.id,
        includeInsights: true,
        includeRecommendations: true
      });

      const processingTime = Date.now() - startTime;

      // Verify performance metrics
      expect(report.metadata.processingTime).toBeGreaterThan(0);
      expect(processingTime).toBeLessThan(10000); // Should complete within 10 seconds
      
      // Verify report was generated successfully
      expect(report.sections).toHaveLength(1);
      expect(report.metadata.dataPoints).toBeGreaterThanOrEqual(0);
    });

    it('should maintain service health under load', async () => {
      // Create multiple templates
      const templates = await Promise.all([
        reportingService.createTemplate({
          name: 'Load Test 1',
          description: 'First load test template',
          type: 'kpi',
          sections: [{ id: '1', title: 'Test 1', type: 'kpi', dataSource: 'delivery', order: 1 }],
          format: 'json',
          isActive: true
        }),
        reportingService.createTemplate({
          name: 'Load Test 2',
          description: 'Second load test template',
          type: 'kpi',
          sections: [{ id: '2', title: 'Test 2', type: 'kpi', dataSource: 'production', order: 1 }],
          format: 'json',
          isActive: true
        }),
        reportingService.createTemplate({
          name: 'Load Test 3',
          description: 'Third load test template',
          type: 'kpi',
          sections: [{ id: '3', title: 'Test 3', type: 'kpi', dataSource: 'warehouse', order: 1 }],
          format: 'json',
          isActive: true
        })
      ]);

      // Generate multiple reports concurrently
      const reportPromises = templates.map(template =>
        reportingService.generateReport({ templateId: template.id })
      );

      const reports = await Promise.all(reportPromises);

      // Verify all reports were generated successfully
      expect(reports).toHaveLength(3);
      for (const report of reports) {
        expect(report.id).toBeDefined();
        expect(report.sections).toHaveLength(1);
      }

      // Check service health after load
      const health = await reportingService.healthCheck();
      expect(health.isHealthy).toBe(true);
      expect(health.performance!.totalRequests).toBeGreaterThan(0);
      expect(health.performance!.successRate).toBeGreaterThan(0);
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should handle partial failures gracefully', async () => {
      // Create template with mixed valid and invalid data sources
      const template = await reportingService.createTemplate({
        name: 'Partial Failure Test',
        description: 'Testing partial failure handling',
        type: 'analysis',
        sections: [
          {
            id: 'valid-section',
            title: 'Valid Section',
            type: 'kpi',
            dataSource: 'delivery',
            order: 1
          },
          {
            id: 'invalid-section',
            title: 'Invalid Section',
            type: 'kpi',
            dataSource: 'nonexistent',
            order: 2
          }
        ],
        format: 'json',
        isActive: true
      });

      // Should fail due to invalid data source
      await expect(reportingService.generateReport({ templateId: template.id }))
        .rejects.toThrow('Unknown data source: nonexistent');

      // Service should remain healthy after error
      const health = await reportingService.healthCheck();
      expect(health.isInitialized).toBe(true);
    });

    it('should recover from temporary failures', async () => {
      const template = await reportingService.createTemplate({
        name: 'Recovery Test',
        description: 'Testing recovery from failures',
        type: 'kpi',
        sections: [
          {
            id: 'recovery-section',
            title: 'Recovery Section',
            type: 'kpi',
            dataSource: 'delivery',
            order: 1
          }
        ],
        format: 'json',
        isActive: true
      });

      // First attempt should succeed
      const report1 = await reportingService.generateReport({ templateId: template.id });
      expect(report1.id).toBeDefined();

      // Service should continue working after successful operations
      const report2 = await reportingService.generateReport({ templateId: template.id });
      expect(report2.id).toBeDefined();
      expect(report2.id).not.toBe(report1.id); // Different report instances

      // Verify service metrics
      const health = await reportingService.healthCheck();
      expect(health.performance!.totalRequests).toBeGreaterThanOrEqual(2);
      expect(health.performance!.successfulRequests).toBeGreaterThanOrEqual(2);
    });
  });
});