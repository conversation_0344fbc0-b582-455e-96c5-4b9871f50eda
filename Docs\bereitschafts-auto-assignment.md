# Automatische Zuweisung der Bereitschaftsperson bei Störungsmeldungen

## Übersicht

Diese Dokumentation beschreibt die Implementierung der automatischen Zuweisung der aktuell diensthabenden Bereitschaftsperson an neue Störungsmeldungen.

## Anforderungen

Wenn eine Störung über das `StoerungsForm.tsx` gemeldet und in die Datenbank geschrieben wird, soll automatisch die Person, die in der aktuellen Woche Bereitschaft hat, im Feld "assigned_to" eingetragen werden.

### Ablauf:
1. Ermittle die aktuell diensthabende Person aus der Tabelle "bereitschafts_wochen" anhand der "personen_id"
2. Hole den Namen und die E-Mail-Adresse der Person aus der Tabelle "bereitschafts_personen"
3. Trage den Namen der Person in das Feld "assigned_to" der Störungsmeldung ein
4. Erstelle eine E-Mail-Vorlage für die betroffene Person (ohne sie bereits zu versenden)

## Implementierung

### 1. Neuer Service: `bereitschafts-assignment.service.ts`

Ein neuer Service wurde erstellt, der folgende Funktionalitäten bereitstellt:

- **`getAktuelleBereitschaftsperson()`**: Ermittelt die aktuell diensthabende Person
- **`createEmailTemplate()`**: Erstellt eine E-Mail-Vorlage für die Bereitschaftsperson
- **`validateAutoAssignment()`**: Validiert, ob eine automatische Zuweisung möglich ist

#### Wichtige Interfaces:

```typescript
interface BereitschaftsAssignmentData {
  id: number;
  name: string;
  email: string;
  telefon?: string;
  abteilung?: string;
}

interface EmailTemplate {
  to: string;
  subject: string;
  body: string;
}

interface AssignmentValidation {
  isValid: boolean;
  message: string;
  assignmentData?: BereitschaftsAssignmentData;
}
```

### 2. Erweiterte StoerungsForm.tsx

Das Störungsformular wurde um folgende Funktionen erweitert:

#### Neue State-Variablen:
- `bereitschaftsperson`: Speichert die aktuelle Bereitschaftsperson
- `emailTemplate`: Speichert die generierte E-Mail-Vorlage
- `autoAssignmentEnabled`: Toggle für die automatische Zuweisung
- `bereitschaftsStatus`: Status-Anzeige für die Bereitschafts-Zuweisung

#### Automatische Zuweisung beim Laden:
- Beim Laden des Formulars wird automatisch die aktuelle Bereitschaftsperson ermittelt
- Das `assigned_to` Feld wird automatisch mit dem Namen der Bereitschaftsperson gefüllt
- Ein Status wird angezeigt, der den Erfolg oder Fehler der Zuweisung anzeigt

#### E-Mail-Template-Generierung:
- Vor dem Speichern der Störung wird automatisch eine E-Mail-Vorlage generiert
- Die Vorlage wird im Formular als Vorschau angezeigt
- Die E-Mail wird nicht automatisch versendet, sondern nur vorbereitet

### 3. UI-Verbesserungen

#### Bereitschafts-Status Anzeige:
- ✅ Erfolgreich zugewiesen: "Automatisch zugewiesen an: [Name]"
- ⚠️ Warnung: Bei Problemen mit der Zuweisung
- ❌ Fehler: Bei technischen Problemen

#### Toggle für automatische Zuweisung:
- Checkbox zum Ein-/Ausschalten der automatischen Zuweisung
- Bei Deaktivierung wird das `assigned_to` Feld wieder editierbar

#### E-Mail-Vorschau:
- Anzeige der generierten E-Mail-Vorlage mit:
  - Empfänger (E-Mail-Adresse der Bereitschaftsperson)
  - Betreff
  - Vollständiger E-Mail-Text

## Datenbankstruktur

### Tabelle: `bereitschafts_wochen`
- `id`: Eindeutige ID
- `personen_id`: Verweis auf die Bereitschaftsperson
- `wochen_start`: Startdatum der Bereitschaftswoche
- `wochen_ende`: Enddatum der Bereitschaftswoche
- `ist_aktiv`: Status der Bereitschaftswoche

### Tabelle: `bereitschafts_personen`
- `id`: Eindeutige ID
- `name`: Name der Person
- `email`: E-Mail-Adresse
- `telefon`: Telefonnummer (optional)
- `abteilung`: Abteilung (optional)
- `ist_aktiv`: Status der Person

## Fehlerbehandlung

### Mögliche Fehlerszenarien:
1. **Keine aktuelle Bereitschaftswoche gefunden**: Automatische Zuweisung wird deaktiviert
2. **Bereitschaftsperson nicht gefunden**: Warnung wird angezeigt
3. **Datenbankfehler**: Fehler wird geloggt, automatische Zuweisung wird deaktiviert
4. **Service nicht verfügbar**: Fallback auf manuelle Zuweisung

### Fallback-Verhalten:
- Bei Fehlern wird die automatische Zuweisung deaktiviert
- Das `assigned_to` Feld wird wieder editierbar
- Eine entsprechende Fehlermeldung wird angezeigt
- Die Störung kann trotzdem normal erstellt werden

## Testing

### Manuelle Tests:
1. **Erfolgreiche automatische Zuweisung**:
   - Störungsformular öffnen
   - Prüfen, ob automatisch eine Bereitschaftsperson zugewiesen wird
   - Prüfen, ob E-Mail-Vorlage generiert wird

2. **Deaktivierung der automatischen Zuweisung**:
   - Checkbox "Automatische Zuweisung" deaktivieren
   - Prüfen, ob `assigned_to` Feld editierbar wird
   - Prüfen, ob Status-Anzeige verschwindet

3. **Fehlerbehandlung**:
   - Service temporär deaktivieren
   - Prüfen, ob Fallback-Verhalten funktioniert

### Zu testende Szenarien:
- Normale Bereitschaftswoche aktiv
- Keine Bereitschaftswoche definiert
- Bereitschaftsperson nicht gefunden
- Service nicht erreichbar
- Datenbankfehler

## Konfiguration

### Umgebungsvariablen:
Keine zusätzlichen Umgebungsvariablen erforderlich.

### Abhängigkeiten:
- Bestehender `bereitschaftsService`
- Bestehender `stoerungenService`
- Prisma-Datenbankzugriff

## Wartung und Erweiterungen

### Mögliche Erweiterungen:
1. **Automatischer E-Mail-Versand**: E-Mail direkt nach Störungserstellung versenden
2. **Eskalation**: Bei nicht verfügbarer Bereitschaftsperson automatisch eskalieren
3. **Benachrichtigungen**: Push-Benachrichtigungen an die Bereitschaftsperson
4. **Statistiken**: Tracking der automatischen Zuweisungen

### Wartungsaufgaben:
1. **Regelmäßige Überprüfung** der Bereitschaftspläne
2. **Monitoring** der automatischen Zuweisungen
3. **Log-Analyse** bei Fehlern

## Changelog

### Version 1.0.0 (Initial Release)
- Implementierung der automatischen Bereitschaftsperson-Zuweisung
- E-Mail-Template-Generierung
- UI-Integration in StoerungsForm
- Fehlerbehandlung und Fallback-Mechanismen