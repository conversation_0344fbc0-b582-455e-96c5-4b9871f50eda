/**
 * Performance Patterns Chart Component
 * 
 * Displays detected performance patterns with frequency analysis and trend visualization.
 */

import React, { useState } from 'react';
import { Line, LineChart, Bar, BarChart, XAxis, YAxis, CartesianGrid, ResponsiveContainer, RadialBar, Radial<PERSON>ar<PERSON>hart, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import { TrendingUp, BarChart3, Clock, Calendar, Activity, Filter } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import type { PerformancePattern } from '@/types/predictive-analytics';
import { cn } from '@/lib/utils';

interface PerformancePatternsChartProps {
  patterns: PerformancePattern[];
  className?: string;
}

/**
 * Performance Patterns Chart Component
 */
export function PerformancePatternsChart({ patterns, className }: PerformancePatternsChartProps) {
  const [selectedPatternType, setSelectedPatternType] = useState<string>('all');
  const [selectedFrequency, setSelectedFrequency] = useState<string>('all');

  // Filter patterns
  const filteredPatterns = patterns.filter(pattern => 
    (selectedPatternType === 'all' || pattern.pattern_type === selectedPatternType) &&
    (selectedFrequency === 'all' || pattern.frequency === selectedFrequency)
  );

  // Prepare pattern type distribution data
  const patternTypeData = patterns.reduce((acc, pattern) => {
    const existing = acc.find(item => item.type === pattern.pattern_type);
    if (existing) {
      existing.count += 1;
      existing.avgStrength += pattern.strength;
    } else {
      acc.push({
        type: pattern.pattern_type,
        count: 1,
        avgStrength: pattern.strength,
        label: getPatternTypeLabel(pattern.pattern_type)
      });
    }
    return acc;
  }, [] as Array<{ type: string; count: number; avgStrength: number; label: string }>);

  // Calculate average strength for each type
  patternTypeData.forEach(item => {
    item.avgStrength = item.avgStrength / item.count;
  });

  // Prepare frequency distribution data
  const frequencyData = patterns.reduce((acc, pattern) => {
    const existing = acc.find(item => item.frequency === pattern.frequency);
    if (existing) {
      existing.count += 1;
      existing.totalStrength += pattern.strength;
    } else {
      acc.push({
        frequency: pattern.frequency,
        count: 1,
        totalStrength: pattern.strength,
        label: getFrequencyLabel(pattern.frequency)
      });
    }
    return acc;
  }, [] as Array<{ frequency: string; count: number; totalStrength: number; label: string }>);

  // Prepare strength distribution data
  const strengthData = filteredPatterns.map((pattern, index) => ({
    id: pattern.pattern_id,
    name: `Muster ${index + 1}`,
    strength: pattern.strength * 100,
    type: pattern.pattern_type,
    frequency: pattern.frequency,
    kpis: pattern.kpis_affected.length,
    description: pattern.description
  }));

  // Get pattern type label
  function getPatternTypeLabel(type: string): string {
    switch (type) {
      case 'seasonal': return 'Saisonal';
      case 'cyclical': return 'Zyklisch';
      case 'trend': return 'Trend';
      case 'irregular': return 'Unregelmäßig';
      default: return type;
    }
  }

  // Get frequency label
  function getFrequencyLabel(frequency: string): string {
    switch (frequency) {
      case 'hourly': return 'Stündlich';
      case 'daily': return 'Täglich';
      case 'weekly': return 'Wöchentlich';
      case 'monthly': return 'Monatlich';
      default: return frequency;
    }
  }

  // Get pattern type color
  const getPatternTypeColor = (type: string) => {
    switch (type) {
      case 'seasonal': return '#8884d8';
      case 'cyclical': return '#82ca9d';
      case 'trend': return '#ffc658';
      case 'irregular': return '#ff7c7c';
      default: return '#8dd1e1';
    }
  };

  // Get strength color
  const getStrengthColor = (strength: number) => {
    if (strength >= 0.8) return 'text-green-600 bg-green-100';
    if (strength >= 0.6) return 'text-blue-600 bg-blue-100';
    if (strength >= 0.4) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  // Chart configuration
  const chartConfig = {
    count: {
      label: 'Anzahl',
      color: 'var(--chart-1)',
    },
    strength: {
      label: 'Stärke (%)',
      color: 'var(--chart-2)',
    },
    avgStrength: {
      label: 'Durchschnittliche Stärke',
      color: 'var(--chart-3)',
    },
  };

  return (
    <Card className={cn("bg-secondary-background text-foreground", className)} data-testid="performance-patterns-chart">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">
            Performance-Muster ({filteredPatterns.length})
          </CardTitle>
          <div className="flex items-center gap-2">
            <select
              value={selectedPatternType}
              onChange={(e) => setSelectedPatternType(e.target.value)}
              className="px-2 py-1 border rounded text-sm"
            >
              <option value="all">Alle Typen</option>
              <option value="seasonal">Saisonal</option>
              <option value="cyclical">Zyklisch</option>
              <option value="trend">Trend</option>
              <option value="irregular">Unregelmäßig</option>
            </select>
            <select
              value={selectedFrequency}
              onChange={(e) => setSelectedFrequency(e.target.value)}
              className="px-2 py-1 border rounded text-sm"
            >
              <option value="all">Alle Frequenzen</option>
              <option value="hourly">Stündlich</option>
              <option value="daily">Täglich</option>
              <option value="weekly">Wöchentlich</option>
              <option value="monthly">Monatlich</option>
            </select>
          </div>
        </div>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>Erkannte Muster: <strong>{patterns.length}</strong></span>
          <span>Durchschnittliche Stärke: <strong>{(patterns.reduce((sum, p) => sum + p.strength, 0) / patterns.length * 100).toFixed(1)}%</strong></span>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Übersicht</TabsTrigger>
            <TabsTrigger value="distribution">Verteilung</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* Pattern Type Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Muster-Typen</CardTitle>
                </CardHeader>
                <CardContent>
                  <ChartContainer config={chartConfig} className="h-48">
                    <PieChart>
                      <Pie
                        data={patternTypeData}
                        cx="50%"
                        cy="50%"
                        outerRadius={60}
                        dataKey="count"
                        label={({ label, percent }) => `${label} ${(percent * 100).toFixed(0)}%`}
                      >
                        {patternTypeData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={getPatternTypeColor(entry.type)} />
                        ))}
                      </Pie>
                      <ChartTooltip
                        content={({ active, payload }) => {
                          if (!active || !payload?.length) return null;
                          const data = payload[0]?.payload;
                          return (
                            <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
                              <p className="font-medium">{data.label}</p>
                              <p className="text-sm text-muted-foreground">
                                Anzahl: {data.count}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                Ø Stärke: {(data.avgStrength * 100).toFixed(1)}%
                              </p>
                            </div>
                          );
                        }}
                      />
                    </PieChart>
                  </ChartContainer>
                </CardContent>
              </Card>

              {/* Frequency Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Frequenz-Verteilung</CardTitle>
                </CardHeader>
                <CardContent>
                  <ChartContainer config={chartConfig} className="h-48">
                    <BarChart data={frequencyData}>
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                      <XAxis
                        dataKey="label"
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis
                        tick={{ fontSize: 12 }}
                      />
                      <Bar
                        dataKey="count"
                        fill="var(--color-count)"
                        radius={[4, 4, 0, 0]}
                      />
                      <ChartTooltip
                        content={({ active, payload, label }) => {
                          if (!active || !payload?.length) return null;
                          const data = payload[0]?.payload;
                          return (
                            <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
                              <p className="font-medium">{label}</p>
                              <p className="text-sm text-muted-foreground">
                                Anzahl: {data.count}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                Gesamtstärke: {(data.totalStrength * 100).toFixed(1)}%
                              </p>
                            </div>
                          );
                        }}
                      />
                    </BarChart>
                  </ChartContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Distribution Tab */}
          <TabsContent value="distribution" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Muster-Stärke Verteilung</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="h-64">
                  <BarChart data={strengthData}>
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={60}
                    />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => `${value}%`}
                    />
                    <Bar
                      dataKey="strength"
                      fill="var(--color-strength)"
                      radius={[4, 4, 0, 0]}
                    />
                    <ChartTooltip
                      content={({ active, payload, label }) => {
                        if (!active || !payload?.length) return null;
                        const data = payload[0]?.payload;
                        return (
                          <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
                            <p className="font-medium">{label}</p>
                            <p className="text-sm text-muted-foreground">
                              Stärke: {data.strength.toFixed(1)}%
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Typ: {getPatternTypeLabel(data.type)}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Frequenz: {getFrequencyLabel(data.frequency)}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Betroffene KPIs: {data.kpis}
                            </p>
                          </div>
                        );
                      }}
                    />
                  </BarChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Details Tab */}
          <TabsContent value="details" className="space-y-4">
            <div className="h-64 overflow-y-auto">
              {filteredPatterns.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-blue-500 mx-auto mb-2" />
                    <p className="text-muted-foreground">
                      Keine Muster gefunden
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Passen Sie die Filter an
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredPatterns.map((pattern, index) => (
                    <div
                      key={pattern.pattern_id}
                      className="border rounded-lg p-3 hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Activity className="h-4 w-4 text-blue-500" />
                          <span className="font-medium text-sm">
                            Muster #{index + 1}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge style={{ backgroundColor: getPatternTypeColor(pattern.pattern_type), color: 'white' }}>
                            {getPatternTypeLabel(pattern.pattern_type)}
                          </Badge>
                          <Badge className={getStrengthColor(pattern.strength)}>
                            {Math.round(pattern.strength * 100)}% Stärke
                          </Badge>
                        </div>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-3">
                        {pattern.description}
                      </p>
                      
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-xs">
                        <div>
                          <span className="text-muted-foreground">Frequenz:</span>
                          <p className="font-medium flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {getFrequencyLabel(pattern.frequency)}
                          </p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Betroffene KPIs:</span>
                          <p className="font-medium">{pattern.kpis_affected.length}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Erkannt am:</span>
                          <p className="font-medium">
                            {new Date(pattern.detected_at).toLocaleDateString('de-DE')}
                          </p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Muster-ID:</span>
                          <p className="font-medium font-mono text-xs">
                            {pattern.pattern_id.slice(-8)}
                          </p>
                        </div>
                      </div>
                      
                      {pattern.kpis_affected.length > 0 && (
                        <div className="mt-3 pt-3 border-t">
                          <span className="text-xs text-muted-foreground">Betroffene KPIs:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {pattern.kpis_affected.slice(0, 5).map((kpi, kpiIndex) => (
                              <Badge key={kpiIndex} variant="outline" className="text-xs">
                                {kpi}
                              </Badge>
                            ))}
                            {pattern.kpis_affected.length > 5 && (
                              <Badge variant="outline" className="text-xs">
                                +{pattern.kpis_affected.length - 5} weitere
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

export default PerformancePatternsChart;