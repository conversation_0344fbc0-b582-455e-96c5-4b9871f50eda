/**
 * Unit Tests for RAG Chat Service
 * 
 * Tests the RAG-enhanced chat functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { RAGChatService, ChatRequest, ChatResponse } from '../RAGChatService';
import { RAGService } from '../../rag/RAGService';
import { apiService } from '@/services/api.service';
import { AIServiceError } from '../../types';

// Mock dependencies
vi.mock('@/services/api.service');
vi.mock('../../rag/RAGService');

describe('RAGChatService', () => {
  let ragChatService: RAGChatService;
  let mockRAGService: vi.Mocked<RAGService>;
  let mockApiService: vi.Mocked<typeof apiService>;

  beforeEach(() => {
    vi.clearAllMocks();

    // Create mock RAG service
    mockRAGService = {
      initialized: false,
      initialize: vi.fn().mockImplementation(() => {
        mockRAGService.initialized = true;
        return Promise.resolve();
      }),
      enhanceQuery: vi.fn().mockResolvedValue({
        originalQuery: 'test query',
        context: ['context1', 'context2'],
        relevantData: [],
        embeddings: [0.1, 0.2, 0.3]
      }),
      generateResponse: vi.fn().mockResolvedValue({
        response: 'RAG enhanced response',
        sources: [
          {
            id: 'source-1',
            title: 'Test Source',
            content: 'Test content',
            relevance: 0.9
          }
        ],
        confidence: 0.85,
        usedContext: ['context1', 'context2']
      }),
      healthCheck: vi.fn().mockResolvedValue({ isHealthy: true }),
      getRAGStats: vi.fn().mockReturnValue({
        maxContextLength: 8000,
        maxSources: 5
      })
    } as any;

    // Mock API service
    mockApiService = vi.mocked(apiService);
    mockApiService.post.mockResolvedValue({
      response: 'Backend response',
      metadata: {
        processingTime: 100,
        dataEnrichmentUsed: false,
        detectedIntents: [],
        performanceMetrics: {}
      }
    });

    ragChatService = new RAGChatService(mockRAGService);
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await ragChatService.initialize();
      
      expect(mockRAGService.initialize).toHaveBeenCalled();
      expect(ragChatService.initialized).toBe(true);
    });

    it('should handle initialization failure', async () => {
      mockRAGService.initialized = false;
      mockRAGService.initialize.mockRejectedValue(new Error('Init failed'));
      
      await expect(ragChatService.initialize()).rejects.toThrow('Init failed');
    });
  });

  describe('processChat', () => {
    beforeEach(async () => {
      await ragChatService.initialize();
    });

    it('should process basic chat when RAG is disabled', async () => {
      const request: ChatRequest = {
        message: 'Test message',
        useRAG: false
      };

      const response = await ragChatService.processChat(request);

      expect(response.ragEnhanced).toBe(false);
      expect(response.response).toBe('Backend response');
      expect(mockApiService.post).toHaveBeenCalledWith('/chat', {
        message: 'Test message',
        includeInsights: false,
        includeAnomalies: false
      });
    });

    it('should process RAG-enhanced chat when RAG is enabled', async () => {
      const request: ChatRequest = {
        message: 'Test message',
        useRAG: true
      };

      const response = await ragChatService.processChat(request);

      expect(response.ragEnhanced).toBe(true);
      expect(response.response).toBe('RAG enhanced response');
      expect(response.sources).toHaveLength(1);
      expect(response.confidence).toBe(0.85);
      expect(mockRAGService.enhanceQuery).toHaveBeenCalledWith('Test message', undefined);
      expect(mockRAGService.generateResponse).toHaveBeenCalled();
    });

    it('should include insights and anomalies when requested', async () => {
      const request: ChatRequest = {
        message: 'Test message',
        useRAG: true,
        includeInsights: true,
        includeAnomalies: true
      };

      await ragChatService.processChat(request);

      expect(mockApiService.post).toHaveBeenCalledWith('/chat/enhanced', {
        message: 'Test message',
        includeInsights: true,
        includeAnomalies: true,
        ragContext: {
          originalQuery: 'test query',
          contextSources: 2,
          relevantDataCount: 0
        }
      });
    });

    it('should fall back to basic chat when RAG confidence is too low', async () => {
      mockRAGService.generateResponse.mockResolvedValue({
        response: 'Low confidence response',
        sources: [],
        confidence: 0.2,
        usedContext: []
      });

      const request: ChatRequest = {
        message: 'Test message',
        useRAG: true
      };

      const response = await ragChatService.processChat(request);

      expect(response.ragEnhanced).toBe(false);
      expect(mockApiService.post).toHaveBeenCalled();
    });

    it('should fall back to basic chat when RAG service fails', async () => {
      mockRAGService.enhanceQuery.mockRejectedValue(new Error('RAG failed'));

      const request: ChatRequest = {
        message: 'Test message',
        useRAG: true
      };

      const response = await ragChatService.processChat(request);

      expect(response.ragEnhanced).toBe(false);
      expect(mockApiService.post).toHaveBeenCalled();
    });

    it('should generate contextual suggestions', async () => {
      const request: ChatRequest = {
        message: 'störung analysis',
        useRAG: true
      };

      const response = await ragChatService.processChat(request);

      expect(response.suggestions).toContain('Störungsanalyse vertiefen');
    });

    it('should handle conversation history', async () => {
      const conversationHistory = [
        {
          id: 'msg-1',
          text: 'Previous message',
          sender: 'user' as const,
          timestamp: new Date()
        },
        {
          id: 'msg-2',
          text: 'Previous response',
          sender: 'ai' as const,
          timestamp: new Date()
        }
      ];

      const request: ChatRequest = {
        message: 'Test message',
        useRAG: true,
        conversationHistory
      };

      await ragChatService.processChat(request);

      // Should pass conversation history to RAG service
      expect(mockRAGService.enhanceQuery).toHaveBeenCalled();
    });

    it('should provide ultimate fallback response on complete failure', async () => {
      mockRAGService.enhanceQuery.mockRejectedValue(new Error('RAG failed'));
      mockApiService.post.mockRejectedValue(new Error('API failed'));

      const request: ChatRequest = {
        message: 'Test message',
        useRAG: true
      };

      const response = await ragChatService.processChat(request);

      expect(response.response).toContain('Entschuldigung');
      expect(response.confidence).toBe(0.1);
      expect(response.ragEnhanced).toBe(false);
    });
  });

  describe('health check', () => {
    beforeEach(async () => {
      await ragChatService.initialize();
    });

    it('should pass health check when all services are healthy', async () => {
      const status = await ragChatService.healthCheck();
      
      expect(status.isHealthy).toBe(true);
      expect(mockRAGService.healthCheck).toHaveBeenCalled();
    });

    it('should fail health check when RAG service is unhealthy', async () => {
      mockRAGService.healthCheck.mockResolvedValue({ isHealthy: false });

      const status = await ragChatService.healthCheck();
      
      expect(status.isHealthy).toBe(false);
      expect(status.lastError?.message).toBe('RAG service is unhealthy');
    });

    it('should fail health check when basic chat test fails', async () => {
      // Mock API service to fail for the health check test
      mockApiService.post.mockRejectedValue(new Error('API failed'));

      const status = await ragChatService.healthCheck();
      
      expect(status.isHealthy).toBe(false);
      expect(status.lastError?.message).toContain('Chat functionality test failed');
    });
  });

  describe('statistics', () => {
    beforeEach(async () => {
      await ragChatService.initialize();
    });

    it('should return chat service statistics', () => {
      const stats = ragChatService.getChatStats();

      expect(stats).toHaveProperty('ragStats');
      expect(stats).toHaveProperty('config');
      expect(stats.config).toHaveProperty('maxConversationHistory');
      expect(stats.config).toHaveProperty('enableConversationContext');
      expect(stats.config).toHaveProperty('fallbackToBasicChat');
    });
  });

  describe('error handling', () => {
    beforeEach(async () => {
      await ragChatService.initialize();
    });

    it('should handle API service errors gracefully', async () => {
      mockApiService.post.mockRejectedValue(new Error('Network error'));

      const request: ChatRequest = {
        message: 'Test message',
        useRAG: false
      };

      const response = await ragChatService.processChat(request);

      expect(response.response).toContain('Entschuldigung');
      expect(response.confidence).toBe(0.1);
    });

    it('should handle RAG service errors with fallback', async () => {
      mockRAGService.enhanceQuery.mockRejectedValue(new Error('Vector search failed'));

      const request: ChatRequest = {
        message: 'Test message',
        useRAG: true
      };

      const response = await ragChatService.processChat(request);

      expect(response.ragEnhanced).toBe(false);
      expect(mockApiService.post).toHaveBeenCalled();
    });

    it('should handle configuration errors', () => {
      const invalidConfig = {
        maxConversationHistory: -1,
        fallbackToBasicChat: false
      };

      const service = new RAGChatService(mockRAGService, invalidConfig);
      const stats = service.getChatStats();

      expect(stats.config.maxConversationHistory).toBe(-1);
      expect(stats.config.fallbackToBasicChat).toBe(false);
    });
  });

  describe('suggestion generation', () => {
    beforeEach(async () => {
      await ragChatService.initialize();
    });

    it('should generate KPI-specific suggestions', async () => {
      const request: ChatRequest = {
        message: 'show me kpi data',
        useRAG: true
      };

      const response = await ragChatService.processChat(request);

      expect(response.suggestions).toContain('KPI-Trends anzeigen');
    });

    it('should generate optimization-specific suggestions', async () => {
      const request: ChatRequest = {
        message: 'optimierung recommendations',
        useRAG: true
      };

      const response = await ragChatService.processChat(request);

      expect(response.suggestions).toContain('Optimierungsmaßnahmen vorschlagen');
    });

    it('should limit suggestions to 4 items', async () => {
      const request: ChatRequest = {
        message: 'störung kpi optimization',
        useRAG: true
      };

      const response = await ragChatService.processChat(request);

      expect(response.suggestions).toHaveLength(4);
    });
  });
});