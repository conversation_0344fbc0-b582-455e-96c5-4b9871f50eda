// Debug-Script um die API-Anfrage zu testen
const fetch = require('node-fetch');

async function testAvailableDrumsAPI() {
    const url = 'http://localhost:3001/api/available-drums/filter';
    const requestBody = {
        aufnahmeDatum: '2025-07-11',
        material: '00100004',
        minGesamtbestand: 250
    };

    console.log('🔍 Teste API-Anfrage:');
    console.log('URL:', url);
    console.log('Request Body:', JSON.stringify(requestBody, null, 2));
    console.log('\n--- Sende Anfrage ---\n');

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        console.log('Response Status:', response.status);
        console.log('Response Headers:', Object.fromEntries(response.headers.entries()));

        const data = await response.json();
        console.log('\n--- Response Data ---');
        console.log('An<PERSON>hl Trommeln:', data.drums ? data.drums.length : 'Keine drums-Array');
        console.log('Response Structure:', Object.keys(data));
        
        if (data.drums && data.drums.length > 0) {
            console.log('\nErste 3 Trommeln:');
            data.drums.slice(0, 3).forEach((drum, index) => {
                console.log(`${index + 1}:`, {
                    material: drum.material,
                    charge: drum.charge,
                    gesamtbestand: drum.gesamtbestand
                });
            });
        }
        
        console.log('\n--- Vollständige Response ---');
        console.log(JSON.stringify(data, null, 2));
        
    } catch (error) {
        console.error('❌ Fehler bei API-Anfrage:', error.message);
    }
}

testAvailableDrumsAPI();