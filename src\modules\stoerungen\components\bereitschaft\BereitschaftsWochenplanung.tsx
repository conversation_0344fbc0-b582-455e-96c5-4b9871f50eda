import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Calendar, RefreshCw, Plus, Trash2 } from 'lucide-react';
import { BereitschaftGlareCard } from './BereitschaftGlareCard';
import { bereitschaftsService } from '@/services/bereitschaftsService';
import { BereitschaftsWoche, BereitschaftsPerson } from '@/types/bereitschafts';
import { format, startOfWeek } from 'date-fns';
import { de } from 'date-fns/locale';
import { toast } from 'sonner';

interface BereitschaftsWochenplanungProps {
  onPlanungChange?: () => void;
}

export const BereitschaftsWochenplanung: React.FC<BereitschaftsWochenplanungProps> = ({
  onPlanungChange
}) => {
  const [wochenplan, setWochenplan] = useState<BereitschaftsWoche[]>([]);
  const [personen, setPersonen] = useState<BereitschaftsPerson[]>([]);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [startDate, setStartDate] = useState(() => {
    const today = new Date();
    return format(startOfWeek(today, { weekStartsOn: 1 }), 'yyyy-MM-dd');
  });
  const [anzahlWochen, setAnzahlWochen] = useState(12);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [wochenplanData, personenData] = await Promise.all([
        bereitschaftsService.getWochenplan(startDate, anzahlWochen),
        bereitschaftsService.getAllPersonen()
      ]);
      setWochenplan(wochenplanData);
      setPersonen(personenData);
    } catch (error) {
      console.error('Fehler beim Laden der Daten:', error);
      toast.error('Daten konnten nicht geladen werden');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateWochenplan = async () => {
    if (personen.length === 0) {
      toast.error('Keine Personen vorhanden. Bitte fügen Sie zuerst Personen hinzu.');
      return;
    }

    if (!confirm(`Möchten Sie wirklich einen neuen Wochenplan für ${anzahlWochen} Wochen ab ${format(new Date(startDate), 'dd.MM.yyyy', { locale: de })} generieren? Bestehende Planungen werden überschrieben.`)) {
      return;
    }

    try {
      setGenerating(true);
      const neuerWochenplan = await bereitschaftsService.generiereWochenplan({
        startDate,
        anzahlWochen
      });
      setWochenplan(neuerWochenplan);
      toast.success(`Wochenplan für ${anzahlWochen} Wochen erfolgreich generiert`);
      onPlanungChange?.();
    } catch (error) {
      console.error('Fehler beim Generieren des Wochenplans:', error);
      toast.error(error instanceof Error ? error.message : 'Wochenplan konnte nicht generiert werden');
    } finally {
      setGenerating(false);
    }
  };

  const handleDeleteWoche = async (woche: BereitschaftsWoche) => {
    if (!confirm(`Möchten Sie die Bereitschaftswoche von ${woche.person.name} (KW ${format(new Date(woche.wochenStart), 'I', { locale: de })}) wirklich löschen?`)) {
      return;
    }

    try {
      await bereitschaftsService.deleteWoche(woche.id);
      setWochenplan(prev => prev.filter(w => w.id !== woche.id));
      toast.success('Bereitschaftswoche erfolgreich gelöscht');
      onPlanungChange?.();
    } catch (error) {
      console.error('Fehler beim Löschen der Woche:', error);
      toast.error('Woche konnte nicht gelöscht werden');
    }
  };

  const istVergangen = (woche: BereitschaftsWoche): boolean => {
    return new Date(woche.bis) < new Date();
  };

  const istAktuell = (woche: BereitschaftsWoche): boolean => {
    const heute = new Date();
    return new Date(woche.von) <= heute && heute < new Date(woche.bis);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Wochenplanung
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
            <span className="ml-2">Wochenplanung wird geladen...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6 max-h-screen overflow-y-auto scrollbar-thin scrollbar-track-slate-50 scrollbar-thumb-slate-300 hover:scrollbar-thumb-slate-400 scrollbar-thumb-rounded-full scrollbar-track-rounded-full">
      {/* Planungs-Steuerung */}
      <Card className="border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-blue-600" />
            Neuen Wochenplan generieren
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Startdatum</Label>
              <Input
                id="startDate"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="anzahlWochen">Anzahl Wochen</Label>
              <Input
                id="anzahlWochen"
                type="number"
                min="1"
                max="52"
                value={anzahlWochen}
                onChange={(e) => setAnzahlWochen(parseInt(e.target.value) || 1)}
              />
            </div>

            <div className="flex items-end">
              <Button
                onClick={handleGenerateWochenplan}
                disabled={generating || personen.length === 0}
                variant="accept"
              >
                {generating ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Plus className="h-4 w-4 mr-2" />
                )}
                {generating ? 'Generiere...' : 'Generieren'}
              </Button>
            </div>
          </div>

          {personen.length === 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <p className="text-sm text-yellow-800">
                <strong>Hinweis:</strong> Es sind keine Personen konfiguriert. 
                Bitte fügen Sie zuerst Personen im Tab "Personen" hinzu.
              </p>
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-800">
              <strong>Vorschau:</strong> Wochenplan wird für {anzahlWochen} Wochen ab {format(new Date(startDate), 'dd.MM.yyyy', { locale: de })} generiert.
              {personen.length > 0 && (
                <span> Rotation durch {personen.length} Personen.</span>
              )}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Aktueller Wochenplan - Kalender-Ansicht */}
      <Card className="border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              Aktueller Wochenplan
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={loadData}
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {wochenplan.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Kein Wochenplan vorhanden</h3>
              <p className="text-gray-600 mb-4">
                Generieren Sie einen neuen Wochenplan, um die Bereitschaften zu planen.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Kalender-Grid mit Scroll - Verbesserte Scrollbar */}
              <div className="max-h-[500px] overflow-y-auto pr-1 scrollbar-thin scrollbar-track-slate-100 scrollbar-thumb-blue-400 hover:scrollbar-thumb-blue-500 active:scrollbar-thumb-blue-600 scrollbar-thumb-rounded-full scrollbar-track-rounded-full transition-colors duration-200">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
                  {wochenplan.map((woche) => {
                    const kw = format(new Date(woche.wochenStart), 'I', { locale: de });
                    const jahr = format(new Date(woche.wochenStart), 'yyyy', { locale: de });
                    
                    return (
                      <BereitschaftGlareCard
                        key={woche.id}
                        className={`relative p-3 min-h-[120px] transition-all ${
                          istAktuell(woche)
                            ? 'bg-gradient-to-br from-green-900 to-green-800'
                            : istVergangen(woche)
                              ? 'bg-gradient-to-br from-gray-800 to-gray-700 opacity-80'
                              : 'bg-gradient-to-br from-blue-900 to-blue-800'
                        }`}
                      >
                        {/* KW Header */}
                        <div className="text-center mb-2">
                          <div className={`text-lg font-bold ${
                            istAktuell(woche) ? 'text-green-200' : 
                            istVergangen(woche) ? 'text-gray-400' : 'text-blue-200'
                          }`}>
                            KW {kw}
                          </div>
                          <div className="text-xs text-gray-400">
                            {jahr}
                          </div>
                        </div>

                        {/* Status Badge */}
                        <div className="flex justify-center mb-2">
                          {istAktuell(woche) && (
                            <Badge className="bg-green-500/80 text-white text-xs px-1 py-0.5 backdrop-blur-sm">
                              AKTIV
                            </Badge>
                          )}
                          {istVergangen(woche) && (
                            <Badge className="bg-gray-500/60 text-gray-200 border-gray-400/30 text-xs px-1 py-0.5 backdrop-blur-sm">
                              VERGANGEN
                            </Badge>
                          )}
                          {!istAktuell(woche) && !istVergangen(woche) && (
                            <Badge className="bg-blue-500/80 text-white border-blue-300/30 text-xs px-1 py-0.5 backdrop-blur-sm">
                              GEPLANT
                            </Badge>
                          )}
                        </div>

                        {/* Person Info */}
                        <div className="text-center space-y-1">
                          <div className={`font-semibold text-xs leading-tight ${
                            istAktuell(woche) ? 'text-green-100' : 
                            istVergangen(woche) ? 'text-gray-300' : 'text-blue-100'
                          }`}>
                            {woche.person.name}
                          </div>

                          {/* Zeitraum */}
                          <div className="text-xs text-gray-300 pt-1 border-t border-gray-600">
                            {format(new Date(woche.von), 'dd.MM', { locale: de })} - {format(new Date(woche.bis), 'dd.MM', { locale: de })}
                          </div>
                        </div>

                        {/* Ausnahme Indikator */}
                        {woche.hatAusnahme && (
                          <div className="absolute top-1 right-1">
                            <div className="w-2 h-2 bg-orange-400 rounded-full shadow-lg shadow-orange-400/50" title="Hat Ausnahme"></div>
                          </div>
                        )}

                        {/* Action Button */}
                        {!istVergangen(woche) && (
                          <div className="absolute top-1 left-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteWoche(woche)}
                              className="h-5 w-5 p-0 text-red-400 hover:text-red-200 hover:bg-red-500/20 backdrop-blur-sm"
                            >
                              <Trash2 className="h-2.5 w-2.5" />
                            </Button>
                          </div>
                        )}
                      </BereitschaftGlareCard>
                    );
                  })}
                </div>
              </div>

              {/* Legende */}
              <div className="flex flex-wrap justify-center gap-3 pt-3 border-t border-gray-200">
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-green-50 border border-green-400 rounded"></div>
                  <span className="text-xs text-gray-600">Aktiv</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-blue-50 border border-blue-300 rounded"></div>
                  <span className="text-xs text-gray-600">Geplant</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-gray-50 border border-gray-300 rounded"></div>
                  <span className="text-xs text-gray-600">Vergangen</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                  <span className="text-xs text-gray-600">Ausnahme</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Hinweise */}
      <Card className="bg-blue-50 border border-blue-200 rounded-lg">
        <CardContent className="pt-0">
          <div className="space-y-2 text-sm text-blue-800">
            <h4 className="font-semibold text-blue-800">Hinweise zur Wochenplanung:</h4>
            <ul className="list-disc list-inside space-y-1">
              <li>Die Generierung überschreibt bestehende Planungen im gewählten Zeitraum</li>
              <li>Vergangene Bereitschaften können nicht mehr bearbeitet werden</li>
              <li>Die Rotation erfolgt automatisch basierend auf der Personenreihenfolge</li>
              <li>Ausnahmen werden separat verwaltet und überschreiben die normale Rotation</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};