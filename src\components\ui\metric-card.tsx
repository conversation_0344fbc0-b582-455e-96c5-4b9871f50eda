import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "./card";

interface MetricCardProps {
  title: string;
  headerColor: "red" | "green" | "yellow" | "pink" | "purple" | "blue";
  chart?: React.ReactNode;
  stats?: React.ReactNode;
  children?: React.ReactNode;
}

export function MetricCard({ title, headerColor, chart, stats, children }: MetricCardProps) {
  const headerColorStyles = {
    red: "bg-neo-red text-white",
    green: "bg-neo-green text-white", 
    yellow: "bg-neo-yellow text-black",
    pink: "bg-neo-pink text-white",
    purple: "bg-neo-purple text-white",
    blue: "bg-neo-blue text-white"
  };

  return (
    <Card className={headerColorStyles[headerColor]}>
      <CardHeader className="border-b-3 border-black">
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-4 max-h-[500px] overflow-hidden">
        {chart}
        {stats}
        {children}
      </CardContent>
    </Card>
  );
} 