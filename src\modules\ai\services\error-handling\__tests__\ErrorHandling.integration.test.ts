/**
 * Error Handling Integration Tests
 * Tests for complete error handling workflow across all components
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { AIErrorHandler } from '../AIErrorHandler';
import { AIOperationLogger } from '../AIOperationLogger';
import { AIServiceErrorCode, AIServiceErrorSeverity } from '../../types/errors';
import { withErrorHandling, withAIServiceProtection } from '../../decorators/errorHandling';

describe('Error Handling Integration', () => {
  let errorHandler: AIErrorHandler;
  let logger: AIOperationLogger;

  beforeEach(() => {
    // Reset singletons
    (AIErrorHandler as any).instance = undefined;
    (AIOperationLogger as any).instance = undefined;
    
    errorHandler = AIErrorHandler.getInstance();
    logger = AIOperationLogger.getInstance();

    // Mock console methods
    vi.spyOn(console, 'info').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
    logger.clearLogs();
  });

  describe('End-to-End Error Handling Workflow', () => {
    it('should handle complete error workflow with recovery', async () => {
      // Register a recovery strategy
      errorHandler.registerRecoveryStrategy(AIServiceErrorCode.API_TIMEOUT, {
        canRecover: () => true,
        recover: async (error, context) => {
          return 'recovered result';
        },
        maxRetries: 2,
        retryDelay: 10
      });

      // Create an error
      const error = errorHandler.createError(
        AIServiceErrorCode.API_TIMEOUT,
        'TestService',
        'testOperation',
        new Error('Original timeout error'),
        { requestId: '123' }
      );

      // Handle the error
      const result = await errorHandler.handleError(error, 'original input');

      // Verify recovery worked
      expect(result).toBe('recovered result');

      // Verify logging
      const errorSummary = logger.getErrorSummary();
      expect(errorSummary.totalErrors).toBe(1);
      expect(errorSummary.errorsByCode[AIServiceErrorCode.API_TIMEOUT]).toBe(1);
    });

    it('should handle error workflow with fallback when recovery fails', async () => {
      // Register a failing recovery strategy
      errorHandler.registerRecoveryStrategy(AIServiceErrorCode.OPTIMIZATION_FAILED, {
        canRecover: () => true,
        recover: async () => {
          throw new Error('Recovery failed');
        },
        maxRetries: 1,
        retryDelay: 10
      });

      // Register a fallback strategy
      errorHandler.registerFallbackStrategy('TestService', {
        name: 'BasicFallback',
        canHandle: (error) => error.code === AIServiceErrorCode.OPTIMIZATION_FAILED,
        execute: async (input, error) => {
          return 'fallback result';
        },
        priority: 1
      });

      // Create an error
      const error = errorHandler.createError(
        AIServiceErrorCode.OPTIMIZATION_FAILED,
        'TestService',
        'optimizeData',
        new Error('Optimization failed'),
        { data: 'test' }
      );

      // Handle the error
      const result = await errorHandler.handleError(error, 'input data', 'TestService');

      // Verify fallback worked
      expect(result).toBe('fallback result');

      // Verify error was logged
      const errorSummary = logger.getErrorSummary();
      expect(errorSummary.totalErrors).toBe(1);
    });

    it('should handle complete failure when both recovery and fallback fail', async () => {
      // Register failing strategies
      errorHandler.registerRecoveryStrategy(AIServiceErrorCode.PREDICTION_FAILED, {
        canRecover: () => true,
        recover: async () => {
          throw new Error('Recovery failed');
        },
        maxRetries: 1,
        retryDelay: 10
      });

      errorHandler.registerFallbackStrategy('TestService', {
        name: 'FailingFallback',
        canHandle: () => true,
        execute: async () => {
          throw new Error('Fallback failed');
        },
        priority: 1
      });

      // Create an error
      const error = errorHandler.createError(
        AIServiceErrorCode.PREDICTION_FAILED,
        'TestService',
        'predict',
        new Error('Prediction failed')
      );

      // Handle the error
      const result = await errorHandler.handleError(error, 'input', 'TestService');

      // Should return null when all strategies fail
      expect(result).toBeNull();
    });
  });

  describe('Decorator Integration', () => {
    it('should integrate with error handling decorators', async () => {
      class TestService {
        @withErrorHandling('TestService', 'query', 'fallback value')
        async testMethod(input: string): Promise<string> {
          if (input === 'fail') {
            throw new Error('Method failed');
          }
          return `processed: ${input}`;
        }
      }

      const service = new TestService();

      // Test successful operation
      const successResult = await service.testMethod('success');
      expect(successResult).toBe('processed: success');

      // Test failed operation with fallback
      const failResult = await service.testMethod('fail');
      expect(failResult).toBe('fallback value');

      // Verify logging
      const logs = logger.getOperationLogs();
      expect(logs).toHaveLength(2);
      expect(logs[0].success).toBe(false); // Most recent first
      expect(logs[1].success).toBe(true);
    });

    it('should integrate with comprehensive service protection decorator', async () => {
      class TestService {
        @withAIServiceProtection('TestService', 'optimization', {
          maxRetries: 2,
          retryDelay: 10,
          fallbackValue: 'protected fallback'
        })
        async optimizeData(data: any): Promise<string> {
          if (data.shouldFail) {
            throw new Error('Optimization failed');
          }
          return 'optimized result';
        }
      }

      const service = new TestService();

      // Test successful operation
      const successResult = await service.optimizeData({ data: 'test' });
      expect(successResult).toBe('optimized result');

      // Test failed operation with protection
      const failResult = await service.optimizeData({ shouldFail: true });
      expect(failResult).toBe('protected fallback');

      // Verify comprehensive logging
      const logs = logger.getOperationLogs();
      expect(logs).toHaveLength(2);
      
      const performanceMetrics = logger.getPerformanceMetrics('TestService');
      expect(performanceMetrics.totalOperations).toBe(2);
    });
  });

  describe('Multi-Service Error Handling', () => {
    it('should handle errors across multiple services', async () => {
      // Create errors from different services
      const ragError = errorHandler.createError(
        AIServiceErrorCode.VECTOR_SEARCH_FAILED,
        'RAGService',
        'searchSimilar',
        new Error('Vector search failed')
      );

      const cuttingError = errorHandler.createError(
        AIServiceErrorCode.OPTIMIZATION_FAILED,
        'CuttingService',
        'optimizeCutting',
        new Error('Cutting optimization failed')
      );

      const inventoryError = errorHandler.createError(
        AIServiceErrorCode.PREDICTION_FAILED,
        'InventoryService',
        'forecastDemand',
        new Error('Demand forecast failed')
      );

      // Log all errors
      logger.logError(ragError);
      logger.logError(cuttingError);
      logger.logError(inventoryError);

      // Verify error summary
      const summary = logger.getErrorSummary();
      expect(summary.totalErrors).toBe(3);
      expect(summary.errorsByService['RAGService']).toBe(1);
      expect(summary.errorsByService['CuttingService']).toBe(1);
      expect(summary.errorsByService['InventoryService']).toBe(1);
    });

    it('should handle cascading errors with proper isolation', async () => {
      // Register fallback strategies for different services
      errorHandler.registerFallbackStrategy('RAGService', {
        name: 'RAGFallback',
        canHandle: () => true,
        execute: async () => 'rag fallback',
        priority: 1
      });

      errorHandler.registerFallbackStrategy('CuttingService', {
        name: 'CuttingFallback',
        canHandle: () => true,
        execute: async () => 'cutting fallback',
        priority: 1
      });

      // Create and handle errors from different services
      const ragError = errorHandler.createError(
        AIServiceErrorCode.CONTEXT_RETRIEVAL_FAILED,
        'RAGService',
        'getContext'
      );

      const cuttingError = errorHandler.createError(
        AIServiceErrorCode.OPTIMIZATION_FAILED,
        'CuttingService',
        'optimize'
      );

      // Handle errors independently
      const ragResult = await errorHandler.handleError(ragError, 'rag input', 'RAGService');
      const cuttingResult = await errorHandler.handleError(cuttingError, 'cutting input', 'CuttingService');

      // Each should use its own fallback
      expect(ragResult).toBe('rag fallback');
      expect(cuttingResult).toBe('cutting fallback');
    });
  });

  describe('Performance and Memory Management', () => {
    it('should handle high volume of errors without memory issues', async () => {
      const startMemory = process.memoryUsage().heapUsed;

      // Generate many errors
      for (let i = 0; i < 1000; i++) {
        const error = errorHandler.createError(
          AIServiceErrorCode.API_REQUEST_FAILED,
          'TestService',
          `operation${i}`,
          new Error(`Error ${i}`)
        );

        logger.logError(error);
      }

      const endMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = endMemory - startMemory;

      // Memory increase should be reasonable (less than 50MB for 1000 errors)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);

      // Verify error summary
      const summary = logger.getErrorSummary();
      expect(summary.totalErrors).toBe(1000);
    });

    it('should maintain performance with concurrent error handling', async () => {
      const startTime = Date.now();

      // Create multiple concurrent error handling operations
      const promises = Array.from({ length: 100 }, async (_, i) => {
        const error = errorHandler.createError(
          AIServiceErrorCode.API_TIMEOUT,
          'TestService',
          `concurrentOp${i}`,
          new Error(`Concurrent error ${i}`)
        );

        return errorHandler.handleError(error, `input${i}`);
      });

      await Promise.all(promises);

      const duration = Date.now() - startTime;

      // Should complete within reasonable time (less than 5 seconds)
      expect(duration).toBeLessThan(5000);

      // Verify all errors were logged
      const summary = logger.getErrorSummary();
      expect(summary.totalErrors).toBe(100);
    });
  });

  describe('Error Recovery Scenarios', () => {
    it('should handle network-related errors with appropriate strategies', async () => {
      // Register network error recovery
      errorHandler.registerRecoveryStrategy(AIServiceErrorCode.API_TIMEOUT, {
        canRecover: () => true,
        recover: async (error, context) => {
          // Simulate network recovery
          await new Promise(resolve => setTimeout(resolve, 50));
          return 'network recovered';
        },
        maxRetries: 3,
        retryDelay: 100
      });

      const networkError = errorHandler.createError(
        AIServiceErrorCode.API_TIMEOUT,
        'NetworkService',
        'apiCall',
        new Error('Network timeout')
      );

      const result = await errorHandler.handleError(networkError, 'api request');
      expect(result).toBe('network recovered');
    });

    it('should handle data-related errors with validation fallbacks', async () => {
      // Register data validation fallback
      errorHandler.registerFallbackStrategy('DataService', {
        name: 'ValidationFallback',
        canHandle: (error) => error.code === AIServiceErrorCode.DATA_VALIDATION_FAILED,
        execute: async (input, error) => {
          // Return sanitized/default data
          return { sanitized: true, original: input };
        },
        priority: 1
      });

      const dataError = errorHandler.createError(
        AIServiceErrorCode.DATA_VALIDATION_FAILED,
        'DataService',
        'validateInput',
        new Error('Invalid data format')
      );

      const result = await errorHandler.handleError(
        dataError, 
        { invalid: 'data' }, 
        'DataService'
      );

      expect(result).toEqual({ sanitized: true, original: { invalid: 'data' } });
    });
  });

  describe('Error Reporting and Analytics', () => {
    it('should provide comprehensive error analytics', async () => {
      // Generate diverse error scenarios
      const errorScenarios = [
        { code: AIServiceErrorCode.API_REQUEST_FAILED, severity: AIServiceErrorSeverity.MEDIUM, service: 'APIService' },
        { code: AIServiceErrorCode.VECTOR_SEARCH_FAILED, severity: AIServiceErrorSeverity.HIGH, service: 'VectorService' },
        { code: AIServiceErrorCode.OPTIMIZATION_FAILED, severity: AIServiceErrorSeverity.LOW, service: 'OptimizationService' },
        { code: AIServiceErrorCode.API_REQUEST_FAILED, severity: AIServiceErrorSeverity.MEDIUM, service: 'APIService' }, // Duplicate
        { code: AIServiceErrorCode.PREDICTION_FAILED, severity: AIServiceErrorSeverity.CRITICAL, service: 'PredictionService' }
      ];

      for (const scenario of errorScenarios) {
        const error = errorHandler.createError(
          scenario.code,
          scenario.service,
          'testOperation',
          new Error('Test error')
        );
        logger.logError(error);
      }

      const summary = logger.getErrorSummary();

      // Verify error counts
      expect(summary.totalErrors).toBe(5);
      expect(summary.errorsByCode[AIServiceErrorCode.API_REQUEST_FAILED]).toBe(2);
      expect(summary.errorsByService['APIService']).toBe(2);
      expect(summary.errorsBySeverity[AIServiceErrorSeverity.MEDIUM]).toBe(2);
      expect(summary.errorsBySeverity[AIServiceErrorSeverity.CRITICAL]).toBe(1);
    });

    it('should export error logs in multiple formats', () => {
      // Create test operation
      const opId = logger.startOperation('TestService', 'testOp', 'query', { input: 'test' });
      const error = errorHandler.createError(
        AIServiceErrorCode.API_REQUEST_FAILED,
        'TestService',
        'testOp'
      );
      logger.completeOperation(opId, false, undefined, error);

      // Test JSON export
      const jsonExport = logger.exportLogs('json');
      const parsedJson = JSON.parse(jsonExport);
      expect(Array.isArray(parsedJson)).toBe(true);
      expect(parsedJson[0].service).toBe('TestService');

      // Test CSV export
      const csvExport = logger.exportLogs('csv');
      const csvLines = csvExport.split('\n');
      expect(csvLines[0]).toContain('ID,Service,Operation'); // Header
      expect(csvLines[1]).toContain('TestService,testOp'); // Data
    });
  });
});