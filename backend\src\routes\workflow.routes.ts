/**
 * Workflow-API-Routen
 * 
 * API-Endpunkte für die Überwachung und Steuerung automatisierter Python-Workflows.
 * Folgt den etablierten Backend-Patterns mit Error-Handling und Rate-Limiting.
 */

import express from 'express';
import { ApiResponse } from '../types/database.types';
import {
  WorkflowListResponse,
  WorkflowDetailResponse,
  WorkflowHistoryResponse,
  Workflow,
  WorkflowLogEntry,
  WorkflowExecution,
  WorkflowStatus
} from '../types/workflow.types';
import { validators, sanitizationMiddleware } from '../middleware/validation.middleware';
import { rateLimitConfig } from '../middleware/rate-limiting.middleware';

const router = express.Router();

console.log('[ROUTES] Lade Workflow-Routen...');

/**
 * Helper function to map SAPWorkflowProcess status to WorkflowStatus
 */
function mapProcessStatusToWorkflowStatus(processStatus: 'idle' | 'running' | 'completed' | 'error'): WorkflowStatus {
  switch (processStatus) {
    case 'idle':
      return 'scheduled';
    case 'running':
      return 'running';
    case 'completed':
      return 'completed';
    case 'error':
      return 'error';
    default:
      return 'scheduled';
  }
}

// Router-Debugging
router.use((req, res, next) => {
  console.log(`[WORKFLOW-ROUTES] ${req.method} ${req.originalUrl}`);
  next();
});

// Basis-Middleware
router.use(sanitizationMiddleware);
router.use(rateLimitConfig.dataEndpoint);

/**
 * Hilfsfunktion für einheitliche Request-Verarbeitung
 */
async function handleRequest<T>(
  req: express.Request,
  res: express.Response,
  operation: () => Promise<T>
): Promise<void> {
  try {
    console.log(`[WORKFLOW-ROUTES] Verarbeite: ${req.method} ${req.originalUrl}`);
    const result = await operation();
    const response: ApiResponse<T> = {
      success: true,
      data: result
    };
    console.log(`[WORKFLOW-ROUTES] Erfolgreiche Antwort für ${req.originalUrl}`);
    res.json(response);
  } catch (error) {
    console.error(`[WORKFLOW-ROUTES] Fehler für ${req.originalUrl}:`, error);
    const response: ApiResponse<T> = {
      success: false,
      error: error instanceof Error ? error.message : 'Unbekannter Workflow-Fehler'
    };
    res.status(500).json(response);
  }
}

// Test-Route
router.get('/test', (req, res) => {
  console.log('[WORKFLOW-ROUTES] Test-Route aufgerufen!');
  res.json({ 
    message: 'Workflow-Routen funktionieren!', 
    timestamp: new Date().toISOString(),
    service: 'WorkflowService'
  });
});

/**
 * GET /api/workflows
 * Alle Workflows mit aktuellem Status abrufen
 */
router.get('/', async (req, res) => {
  await handleRequest<WorkflowListResponse>(req, res, async () => {
    // Use WorkflowService for consistency
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    const processes = await workflowServiceInstance.getProcesses();
    
    // Transform processes to workflow format
    const workflows: Workflow[] = processes.map(process => ({
      id: process.id,
      name: process.name,
      description: process.description,
      sourceType: 'SAP' as const,
      frequency: 'manual' as const,
      targetTables: [process.dbTable],
      scriptPath: `scripts/workflows/${process.id}`,
      isActive: true,
      status: mapProcessStatusToWorkflowStatus(process.status),
      lastExecution: process.lastRun || null,
      nextExecution: null,
      duration: process.duration || null,
      successRate: 95, // Default success rate
      errorCount: 0,
      totalRuns: 0
    }));
    
    return {
      workflows,
      totalCount: workflows.length,
      lastUpdated: new Date()
    };
  });
});

/**
 * GET /api/workflows/:id/status
 * Detaillierter Status für einzelnen Workflow
 */
router.get('/:id/status', async (req, res) => {
  const { id } = req.params;
  
  await handleRequest<WorkflowDetailResponse>(req, res, async () => {
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    const processes = await workflowServiceInstance.getProcesses();
    const process = processes.find(p => p.id === id);
    
    if (!process) {
      throw new Error(`Workflow mit ID ${id} nicht gefunden`);
    }
    
    // Transform process to workflow format
    const workflow: Workflow = {
      id: process.id,
      name: process.name,
      description: process.description,
      sourceType: 'SAP' as const,
      frequency: 'manual' as const,
      targetTables: [process.dbTable],
      scriptPath: `scripts/workflows/${process.id}`,
      isActive: true,
      status: mapProcessStatusToWorkflowStatus(process.status),
      lastExecution: process.lastRun || null,
      nextExecution: null,
      duration: process.duration || null,
      successRate: 95,
      errorCount: 0,
      totalRuns: 0
    };
    
    return {
      workflow,
      metrics: {
        workflowId: workflow.id,
        averageDuration: workflow.duration || 0,
        successRate: workflow.successRate,
        lastSuccessfulRun: workflow.lastExecution,
        totalExecutions: workflow.totalRuns,
        errorCount: workflow.errorCount,
        dataFreshness: 0
      },
      recentExecutions: [],
      recentLogs: []
    };
  });
});

/**
 * GET /api/workflows/logs
 * Log-Einträge für Workflows abrufen (kompatibel mit workflowRoutes.ts)
 */
router.get('/logs', async (req, res) => {
  await handleRequest<WorkflowLogEntry[]>(req, res, async () => {
    const { workflowId, limit = 50 } = req.query;
    
    // Use WorkflowService for consistency
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    
    // For now, return empty logs as the WorkflowService doesn't implement log storage yet
    // In a real implementation, this would read from log files or database
    return [];
  });
});

/**
 * GET /api/workflows/:id/logs
 * Log-Einträge für einen Workflow abrufen
 */
router.get('/:id/logs', async (req, res) => {
  const { id } = req.params;
  const limit = parseInt(req.query.limit as string) || 50;
  
  await handleRequest<WorkflowLogEntry[]>(req, res, async () => {
    // Use WorkflowService for consistency
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    
    // For now, return empty logs as the WorkflowService doesn't implement log storage yet
    // In a real implementation, this would read from log files or database
    return [];
  });
});

/**
 * PUT /api/workflows/:id/toggle
 * Workflow aktivieren/deaktivieren
 */
router.put('/:id/toggle', rateLimitConfig.writeOperation, async (req, res) => {
  const { id } = req.params;
  
  await handleRequest<{ isActive: boolean; message: string }>(req, res, async () => {
    // For now, just return a success response
    // In a real implementation, this would update the workflow configuration
    return {
      isActive: true,
      message: `Workflow ${id} toggle requested (not implemented yet)`
    };
  });
});

/**
 * GET /api/workflows/:id/running
 * Prüfen ob Workflow gerade läuft
 */
router.get('/:id/running', async (req, res) => {
  const { id } = req.params;
  
  await handleRequest<{ isRunning: boolean; checkedAt: Date }>(req, res, async () => {
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    const processes = await workflowServiceInstance.getProcesses();
    const process = processes.find(p => p.id === id);
    
    return {
      isRunning: process?.status === 'running' || false,
      checkedAt: new Date()
    };
  });
});

/**
 * GET /api/workflows/:id/config
 * Workflow-Konfiguration abrufen
 */
router.get('/:id/config', async (req, res) => {
  const { id } = req.params;
  
  await handleRequest(req, res, async () => {
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    
    const config = await workflowServiceInstance.getWorkflowConfig(id);
    return config;
  });
});

/**
 * PUT /api/workflows/:id/config
 * Workflow-Konfiguration aktualisieren
 */
router.put('/:id/config', rateLimitConfig.writeOperation, async (req, res) => {
  const { id } = req.params;
  const configData = req.body;
  
  await handleRequest(req, res, async () => {
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    
    const updatedConfig = await workflowServiceInstance.updateWorkflowConfig(id, configData);
    return updatedConfig;
  });
});

/**
 * POST /api/workflows/execute
 * SAP Workflow Prozess ausführen
 */
router.post('/execute', rateLimitConfig.writeOperation, async (req, res) => {
  await handleRequest<{ success: boolean; message: string; exportPath?: string }>(req, res, async () => {
    const { processId } = req.body;

    if (!processId) {
      throw new Error('Process ID is required');
    }

    console.log(`[WORKFLOW-ROUTES] Executing process: ${processId}`);
    
    // Use WorkflowService for consistent execution
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    
    const result = await workflowServiceInstance.executeProcess(processId);
    
    console.log(`[WORKFLOW-ROUTES] Process execution result:`, result);
    
    return result;
  });
});

/**
 * POST /api/workflows/:id/restart
 * Workflow neu starten (falls möglich)
 */
router.post('/:id/restart', rateLimitConfig.writeOperation, async (req, res) => {
  const { id } = req.params;
  
  await handleRequest<{ message: string; success: boolean }>(req, res, async () => {
    // Implementierung für Workflow-Neustart
    // In realer Implementierung würde hier das Python-Skript gestartet
    console.log(`[WORKFLOW-ROUTES] Neustart angefordert für Workflow: ${id}`);
    
    return {
      success: true,
      message: `Neustart für Workflow ${id} wurde eingeleitet`
    };
  });
});

/**
 * GET /api/workflows/:id/history
 * Ausführungshistorie für einen Workflow
 */
router.get('/:id/history', async (req, res) => {
  const { id } = req.params;
  const page = parseInt(req.query.page as string) || 1;
  const pageSize = parseInt(req.query.pageSize as string) || 20;
  
  await handleRequest<WorkflowHistoryResponse>(req, res, async () => {
    // Simulation der Historien-Daten
    // In realer Implementierung aus Datenbank oder Log-Files
    const executions: WorkflowExecution[] = [];
    const totalCount: number = 0;
    
    return {
      executions,
      totalCount,
      pageSize,
      currentPage: page
    };
  });
});

/**
 * GET /api/workflows/health
 * Gesundheitscheck für alle Workflows
 */
router.get('/health', async (req, res) => {
  await handleRequest<{
    totalWorkflows: number;
    activeWorkflows: number;
    runningWorkflows: number;
    failedWorkflows: number;
    overallHealth: 'healthy' | 'warning' | 'critical';
  }>(req, res, async () => {
    // Use WorkflowService instead of WorkflowMonitorService for consistency
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    const processes = await workflowServiceInstance.getProcesses();
    
    const totalWorkflows = processes.length;
    const activeWorkflows = processes.filter(p => p.status !== 'idle').length;
    const runningWorkflows = processes.filter(p => p.status === 'running').length;
    const failedWorkflows = processes.filter(p => p.status === 'error').length;
    
    let overallHealth: 'healthy' | 'warning' | 'critical' = 'healthy';
    
    if (failedWorkflows > 0) {
      overallHealth = failedWorkflows > totalWorkflows / 2 ? 'critical' : 'warning';
    }
    
    return {
      totalWorkflows,
      activeWorkflows,
      runningWorkflows,
      failedWorkflows,
      overallHealth
    };
  });
});

/**
 * Additional endpoints from workflowRoutes.ts for compatibility
 */

/**
 * GET /api/workflows/processes
 * Lädt alle verfügbaren Workflow-Prozesse
 */
router.get('/processes', async (req, res) => {
  await handleRequest(req, res, async () => {
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    const processes = await workflowServiceInstance.getProcesses();
    
    return {
      processes,
      count: processes.length
    };
  });
});

/**
 * GET /api/workflows/executions
 * Lädt Workflow-Ausführungen
 */
router.get('/executions', async (req, res) => {
  await handleRequest(req, res, async () => {
    const { workflowId, limit = 50 } = req.query;
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    
    const executions = await workflowServiceInstance.getExecutions(
      workflowId as string,
      Number(limit)
    );
    
    return {
      executions,
      count: executions.length
    };
  });
});

/**
 * GET /api/workflows/stats
 * Lädt Workflow-Statistiken
 */
router.get('/stats', async (req, res) => {
  await handleRequest(req, res, async () => {
    const { WorkflowService } = await import('../services/workflowService');
    const workflowServiceInstance = new WorkflowService();
    const stats = await workflowServiceInstance.getStats();
    
    return stats;
  });
});

export default router;