import React, { useEffect, useState } from 'react';
import { Play, Clock, CheckCircle, AlertTriangle, Database, FileSpreadsheet, Mail, Settings, Save, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Card } from '@/components/ui/card';
import AccordionWorkflow from './accordionWorkflow';
import { workflowService } from '@/services/workflowService';

type Schedule = {
  enabled: boolean;
  frequency: 'hourly' | 'daily' | 'weekly';
  time?: string;
  dayOfWeek?: number;
  interval?: number;
};

type BestandConfig = {
  id: 'bestand';
  name: string;
  description?: string;
  // generisch
  databasePath?: string;
  // Pfade für drei Teilprozesse
  exportDir240?: string;
  exportDir200?: string;
  exportDirRest?: string;
  // LGTYP Low/High Bereiche
  lgnum?: string;
  lgt240?: string;
  lgt200?: string;
  lgtRestLow?: string;
  lgtRestHigh?: string;
  // SAP Basis
  sapExecutablePath?: string;
  sapSystemId?: string;
  sapClient?: string;
  sapLanguage?: string;
  // E-Mail
  emailRecipients?: string[];
  schedule: Schedule;
  isActive?: boolean;
  lastModified?: Date;
};

const defaultBestandConfig: BestandConfig = {
  id: 'bestand',
  name: 'Bestand Workflow',
  description: 'Lagerspiegel Export aus verschiedenen Lägern mit Import in die Datenbank',
  databasePath: '',
  exportDir240: '',
  exportDir200: '',
  exportDirRest: '',
  lgnum: '512',
  lgt240: '240',
  lgt200: '200',
  lgtRestLow: '241',
  lgtRestHigh: '999',
  sapExecutablePath: 'C:\\\\Program Files (x86)\\\\SAP\\\\FrontEnd\\\\SapGui\\\\sapshcut.exe',
  sapSystemId: 'PS4',
  sapClient: '009',
  sapLanguage: 'DE',
  emailRecipients: [],
  schedule: {
    enabled: false,
    frequency: 'daily',
    time: '08:00',
    dayOfWeek: 1,
    interval: 1
  },
  isActive: true,
  lastModified: new Date()
};

interface BestandWorkflowCardProps {
  onExecute: (processId: string) => Promise<void>;
  isExecuting: boolean;
  initialTab?: 'overview' | 'settings';
}

export function BestandWorkflowCard({ onExecute, isExecuting, initialTab = 'settings' }: BestandWorkflowCardProps) {
  const [executionProgress, setExecutionProgress] = useState(0);
  const [config, setConfig] = useState<BestandConfig>(defaultBestandConfig);
  const [tempConfig, setTempConfig] = useState<BestandConfig>(defaultBestandConfig);
  const [activeSettingsTab, setActiveSettingsTab] = useState<string>('general');

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      const loaded = await workflowService.getWorkflowConfig('bestand');
      if (loaded) {
        const merged = { ...defaultBestandConfig, ...loaded };
        setConfig(merged);
        setTempConfig(merged);
      }
    } catch (e) {
      console.error('Fehler beim Laden der Bestand-Config:', e);
    }
  };

  const saveConfig = async () => {
    try {
      await workflowService.updateWorkflowConfig('bestand', tempConfig);
      setConfig(tempConfig);
      console.log('Bestand-Workflow-Konfiguration gespeichert:', tempConfig);
    } catch (e) {
      console.error('Fehler beim Speichern der Bestand-Config:', e);
    }
  };

  const handleExecute = async () => {
    if (!config.isActive) {
      console.warn('Workflow kann nicht gestartet werden - PowerSwitch ist ausgeschaltet');
      return;
    }

    setExecutionProgress(0);
    const progressInterval = setInterval(() => {
      setExecutionProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + Math.random() * 10;
      });
    }, 2000);

    try {
      await onExecute('bestand');
      setExecutionProgress(100);
    } catch (error) {
      console.error('Execution failed:', error);
    } finally {
      clearInterval(progressInterval);
      setTimeout(() => setExecutionProgress(0), 2000);
    }
  };

  const renderScheduleSettings = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label htmlFor="schedule-enabled" className="text-sm font-medium">
          Automatische Ausführung
        </Label>
        <Switch
          id="schedule-enabled"
          checked={!!tempConfig.schedule?.enabled}
          onCheckedChange={(checked) =>
            setTempConfig(prev => ({
              ...prev,
              schedule: { ...prev.schedule, enabled: checked }
            }))
          }
        />
      </div>

      {tempConfig.schedule?.enabled && (
        <>
          <div className="space-y-2">
            <Label htmlFor="frequency">Frequenz</Label>
            <Select
              value={tempConfig.schedule.frequency}
              onValueChange={(value: 'hourly' | 'daily' | 'weekly') =>
                setTempConfig(prev => ({
                  ...prev,
                  schedule: { ...prev.schedule, frequency: value }
                }))
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hourly">Stündlich</SelectItem>
                <SelectItem value="daily">Täglich</SelectItem>
                <SelectItem value="weekly">Wöchentlich</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {tempConfig.schedule.frequency === 'hourly' && (
            <div className="space-y-2">
              <Label htmlFor="interval">Alle X Stunden</Label>
              <Input
                id="interval"
                type="number"
                min="1"
                max="24"
                value={tempConfig.schedule.interval || 1}
                onChange={(e) =>
                  setTempConfig(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, interval: parseInt(e.target.value) || 1 }
                  }))
                }
              />
            </div>
          )}

          {(tempConfig.schedule.frequency === 'daily' || tempConfig.schedule.frequency === 'weekly') && (
            <div className="space-y-2">
              <Label htmlFor="time">Uhrzeit</Label>
              <Input
                id="time"
                type="time"
                value={tempConfig.schedule.time || '08:00'}
                onChange={(e) =>
                  setTempConfig(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, time: e.target.value }
                  }))
                }
              />
            </div>
          )}

          {tempConfig.schedule.frequency === 'weekly' && (
            <div className="space-y-2">
              <Label htmlFor="dayOfWeek">Wochentag</Label>
              <Select
                value={tempConfig.schedule.dayOfWeek?.toString() || '1'}
                onValueChange={(value) =>
                  setTempConfig(prev => ({
                    ...prev,
                    schedule: { ...prev.schedule, dayOfWeek: parseInt(value) }
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Montag</SelectItem>
                  <SelectItem value="2">Dienstag</SelectItem>
                  <SelectItem value="3">Mittwoch</SelectItem>
                  <SelectItem value="4">Donnerstag</SelectItem>
                  <SelectItem value="5">Freitag</SelectItem>
                  <SelectItem value="6">Samstag</SelectItem>
                  <SelectItem value="0">Sonntag</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </>
      )}
    </div>
  );

  return (
    <div className="border-0 transition-all duration-200">
      <div className="px-6 pb-6 space-y-6">
        <Tabs value={activeSettingsTab} onValueChange={setActiveSettingsTab} className="flex flex-col gap-3">
          <TabsList className="bg-white p-1 rounded-lg border shadow-sm flex justify-center">
            <div className="inline-flex gap-1">
              <TabsTrigger
                value="general"
                className="data-[state=active]:bg-blue-200 data-[state=active]:text-black data-[state=active]:shadow-sm"
              >
                SAP Prozess
              </TabsTrigger>
              <TabsTrigger
                value="schedule"
                className="data-[state=active]:bg-purple-200 data-[state=active]:text-black data-[state=active]:shadow-sm"
              >
                Zeitplan
              </TabsTrigger>
              <TabsTrigger
                value="email"
                className="data-[state=active]:bg-green-200 data-[state=active]:text-black data-[state=active]:shadow-sm"
              >
                E-Mail
              </TabsTrigger>
            </div>
          </TabsList>

          <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
            <TabsContent value="general">
              <AccordionWorkflow
                sections={[
                  {
                    id: 'lx03',
                    title: 'SAP LX03 Loop Nr.1 Eingabefelder',
                    content: (
                      <>
                        <div className="grid grid-cols-3 gap-4">
                          <div className="space-y-2 py-2">
                            <Label className="space-y-1" htmlFor="lgnum">Lagernummer</Label>
                            <Input
                              id="lgnum"
                              value={tempConfig.lgnum || ''}
                              onChange={(e) => setTempConfig(prev => ({ ...prev, lgnum: e.target.value }))}
                              placeholder="512"
                            />
                          </div>
                          <div className="space-y-2 py-2">
                            <Label className="space-y-1" htmlFor="lgt240">Lagertyp</Label>
                            <Input
                              id="lgt240"
                              value={tempConfig.lgt240 || ''}
                              onChange={(e) => setTempConfig(prev => ({ ...prev, lgt240: e.target.value }))}
                              placeholder="240"
                            />
                          </div>
                        </div>
                      </>
                    ),
                  },
                  {
                    id: 'lx03',
                    title: 'SAP LX03 Loop Nr.2 Eingabefelder',
                    content: (
                      <>
                        <div className="grid grid-cols-3 gap-4">
                          <div className="space-y-2 py-2">
                            <Label className="space-y-1" htmlFor="lgnum">Lagernummer</Label>
                            <Input
                              id="lgnum"
                              value={tempConfig.lgnum || ''}
                              onChange={(e) => setTempConfig(prev => ({ ...prev, lgnum: e.target.value }))}
                              placeholder="512"
                            />
                          </div>
                          <div className="space-y-2 py-2">
                            <Label className="space-y-1" htmlFor="lgt240">Lagertyp</Label>
                            <Input
                              id="lgt240"
                              value={tempConfig.lgt240 || ''}
                              onChange={(e) => setTempConfig(prev => ({ ...prev, lgt240: e.target.value }))}
                              placeholder="240"
                            />
                          </div>
                        </div>
                      </>
                    ),
                  },
                  {
                    id: 'lx03',
                    title: 'SAP LX03 Loop Nr.3 Eingabefelder',
                    content: (
                      <>
                        <div className="grid grid-cols-3 gap-4">
                          <div className="space-y-2 py-2">
                            <Label className="space-y-1" htmlFor="lgnum">Lagernummer</Label>
                            <Input
                              id="lgnum"
                              value={tempConfig.lgnum || ''}
                              onChange={(e) => setTempConfig(prev => ({ ...prev, lgnum: e.target.value }))}
                              placeholder="512"
                            />
                          </div>
                          <div className="space-y-2 py-2">
                            <Label className="space-y-1" htmlFor="lgt240">Lagertyp</Label>
                            <Input
                              id="lgt240"
                              value={tempConfig.lgt240 || ''}
                              onChange={(e) => setTempConfig(prev => ({ ...prev, lgt240: e.target.value }))}
                              placeholder="240"
                            />
                          </div>
                        </div>
                      </>
                    ),
                  },
                  {
                    id: 'excel',
                    title: 'SAP-Excel Export',
                    content: (
                      <div className="grid grid-cols-1 gap-4">
                        <div className="space-y-2 py-2">
                          <Label className="space-y-1" htmlFor="export-240">Verzeichnis Loop Nr.1</Label>
                          <Input
                            id="export-240"
                            value={tempConfig.exportDir240 || ''}
                            onChange={(e) => setTempConfig(prev => ({ ...prev, exportDir240: e.target.value }))}
                            placeholder="\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="space-y-1" htmlFor="export-200">Verzeichnis Loop Nr.2</Label>
                          <Input
                            id="export-200"
                            value={tempConfig.exportDir200 || ''}
                            onChange={(e) => setTempConfig(prev => ({ ...prev, exportDir200: e.target.value }))}
                            placeholder="\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="space-y-1" htmlFor="export-rest">Verzeichnis Loop Nr.3</Label>
                          <Input
                            id="export-rest"
                            value={tempConfig.exportDirRest || ''}
                            onChange={(e) => setTempConfig(prev => ({ ...prev, exportDirRest: e.target.value }))}
                            placeholder="\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST"
                          />
                        </div>
                      </div>
                    ),
                  },
                  {
                    id: 'sap',
                    title: 'SAP-Grundeinstellung',
                    content: (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div className="space-y-2 md:col-span-3 py-2">
                          <Label htmlFor="sap-exe">SAP Shortcut Pfad</Label>
                          <Input
                            id="sap-exe"
                            value={tempConfig.sapExecutablePath || ""}
                            onChange={(e) =>
                              setTempConfig((prev) => ({ ...prev, sapExecutablePath: e.target.value }))
                            }
                            placeholder="C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="sap-system">System-ID</Label>
                          <Input
                            id="sap-system"
                            value={tempConfig.sapSystemId || ""}
                            onChange={(e) =>
                              setTempConfig((prev) => ({ ...prev, sapSystemId: e.target.value }))
                            }
                            placeholder="PS4"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="sap-client">Client</Label>
                          <Input
                            id="sap-client"
                            value={tempConfig.sapClient || ""}
                            onChange={(e) =>
                              setTempConfig((prev) => ({ ...prev, sapClient: e.target.value }))
                            }
                            placeholder="009"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="sap-language">Sprache</Label>
                          <Input
                            id="sap-language"
                            value={tempConfig.sapLanguage || ""}
                            onChange={(e) =>
                              setTempConfig((prev) => ({ ...prev, sapLanguage: e.target.value }))
                            }
                            placeholder="DE"
                          />
                        </div>
                      </div>
                    ),
                  },
                ]}
              />
            </TabsContent>

            <TabsContent value="schedule">
              <div className="space-y-3">
                {renderScheduleSettings()}
              </div>
            </TabsContent>

            <TabsContent value="email">
              <div className="space-y-3">
                <Label htmlFor="email-recipients">E-Mail-Empfänger</Label>
                <Input
                  id="email-recipients"
                  value={tempConfig.emailRecipients?.join(', ') || ''}
                  onChange={(e) =>
                    setTempConfig(prev => ({
                      ...prev,
                      emailRecipients: e.target.value.split(',').map(x => x.trim()).filter(Boolean)
                    }))
                  }
                  placeholder="E-Mail-Adressen (kommagetrennt)"
                />

                {tempConfig.emailRecipients && tempConfig.emailRecipients.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {tempConfig.emailRecipients.map((email, i) => (
                      <Badge key={i} variant="secondary" className="text-xs">{email}</Badge>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>
          </Card>
        </Tabs>

        <div className="flex items-center justify-between pt-2">

        </div>
      </div>
    </div>
  );
}
