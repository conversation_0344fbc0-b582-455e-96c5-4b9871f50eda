/**
 * AI Module Types
 * 
 * Type definitions for AI services and operations
 */

// Vector Database Types
export interface VectorEntry {
  id: string;
  vector: number[];
  metadata: any;
}

export interface VectorMatch {
  id: string;
  similarity: number;
  metadata: any;
}

export interface VectorSearchResult {
  id: string;
  content: string;
  similarity: number;
  metadata: VectorMetadata;
}

export interface VectorMetadata {
  documentId?: string;
  chunkIndex?: number;
  title?: string;
  source?: string;
  timestamp?: string;
  department?: 'dispatch' | 'cutting' | 'incoming-goods';
  dataType?: string;
  content?: string;
}

export interface IndexStats {
  totalVectors: number;
  dimensions: number;
  indexSize: number;
  lastUpdated: Date;
}

// RAG Types
export interface EnhancedQuery {
  originalQuery: string;
  context: string[];
  relevantData: any[];
  embeddings: number[];
}

export interface RAGResponse {
  response: string;
  sources: Source[];
  confidence: number;
  usedContext: string[];
}

export interface Source {
  id: string;
  title: string;
  content: string;
  url?: string;
  relevance: number;
}

export interface QueryContext {
  department?: 'dispatch' | 'cutting' | 'incoming-goods';
  timeRange?: {
    start: Date;
    end: Date;
  };
  dataTypes?: string[];
  includeHistorical?: boolean;
}

// Document Management Types
export interface Document {
  id: string;
  title: string;
  content: string;
  documentType: string;
  sourceUrl?: string;
  metadata: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface DocumentChunk {
  id: string;
  documentId: string;
  content: string;
  chunkIndex: number;
  metadata: any;
  createdAt: Date;
}

// Optimization Types
export interface CuttingRequest {
  orders: CuttingOrder[];
  availableDrums: DrumInventory[];
  constraints: CuttingConstraints;
  priorities: OrderPriority[];
}

export interface CuttingOrder {
  id: string;
  cableType: string;
  requiredLength: number;
  quantity: number;
  priority: 'low' | 'medium' | 'high';
  deadline?: Date;
}

export interface DrumInventory {
  id: string;
  cableType: string;
  totalLength: number;
  availableLength?: number;
  remainingLength?: number; // Alternative to availableLength
  quality: 'A' | 'B' | 'C';
  diameter?: number;
  crossSection?: number;
}

export interface CuttingConstraints {
  maxWasteLength?: number;
  maxWastePercentage?: number;
  maxCutLength?: number;
  wasteThreshold?: number;
  allowMixedTypes?: boolean;
  allowedCableTypes?: string[];
  preferredDrums?: string[];
}

export interface OrderPriority {
  orderId: string;
  priority: number;
  weight: number;
}

export interface CuttingPlan {
  drumAllocations: DrumAllocation[];
  cuttingSequence: CuttingStep[];
  totalWaste: number;
  efficiency: number;
  estimatedTime: number;
}

export interface DrumAllocation {
  drumId: string;
  cuts: Cut[];
  remainingLength: number;
  utilization: number;
}

export interface Cut {
  orderId: string;
  length: number;
  startPosition: number;
  endPosition: number;
}

export interface CuttingStep {
  stepNumber: number;
  drumId: string;
  cuts: Cut[];
  estimatedTime: number;
}

export interface WasteAnalysis {
  totalWaste: number;
  wastePercentage: number;
  wasteByDrum: { drumId: string; waste: number }[];
  recommendations: string[];
}

export interface CuttingAlternative {
  id: string;
  plan: CuttingPlan;
  score: number;
  advantages: string[];
  disadvantages: string[];
}

export interface DrumAnalysis {
  drumId: string;
  utilizationHistory: { date: Date; utilization: number }[];
  averageUtilization: number;
  recommendations: string[];
}

export interface StockRecommendation {
  cableType: string;
  recommendedStock: number;
  currentStock: number;
  reasoning: string;
  confidence: number;
}

export interface CuttingHistory {
  date: Date;
  cableType: string;
  totalCuts: number;
  totalWaste: number;
  efficiency: number;
}

// AI Service Error Types
export enum AIServiceError {
  EMBEDDING_FAILED = 'EMBEDDING_FAILED',
  VECTOR_SEARCH_FAILED = 'VECTOR_SEARCH_FAILED',
  OPTIMIZATION_FAILED = 'OPTIMIZATION_FAILED',
  PREDICTION_FAILED = 'PREDICTION_FAILED',
  INSUFFICIENT_DATA = 'INSUFFICIENT_DATA',
  MODEL_UNAVAILABLE = 'MODEL_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  DATABASE_ERROR = 'DATABASE_ERROR',
  INVALID_INPUT = 'INVALID_INPUT'
}

// Service Configuration Types (basic version, extended in AIBaseService)
export interface BasicAIServiceConfig {
  enableCaching?: boolean;
  enableLogging?: boolean;
  retryAttempts?: number;
  timeout?: number;
  vectorDimensions?: number;
  embeddingModel?: string;
  maxContextLength?: number;
  similarityThreshold?: number;
}

// Service Status Types (basic version, extended in AIBaseService)
export interface BasicAIServiceStatus {
  isInitialized: boolean;
  isHealthy: boolean;
  lastError?: Error;
  lastChecked: Date;
  performance?: {
    averageResponseTime: number;
    successRate: number;
    totalRequests: number;
  };
}

// Knowledge Base Types
export interface DocumentProcessingStats {
  totalDocuments: number;
  totalChunks: number;
  totalVectors: number;
  averageChunksPerDocument: number;
  processingTime: number;
  lastProcessed: Date;
}

export interface KnowledgeBaseHealth {
  isHealthy: boolean;
  totalDocuments: number;
  totalChunks: number;
  indexedVectors: number;
  lastIndexUpdate: Date;
  storageSize: number;
  averageDocumentSize: number;
  processingQueue: number;
  errors: string[];
}

export interface IngestionResult {
  documentId: string;
  success: boolean;
  chunksCreated: number;
  vectorsIndexed: number;
  processingTime: number;
  errors: string[];
}

// Neue Interfaces für die verbesserte Lieferungslogik
export interface DeliveryItem {
  id: string;
  cableType: string;
  requiredLength: number;
  quantity: number;
  priority: 'low' | 'medium' | 'high';
}

export interface Delivery {
  id: string;
  items: DeliveryItem[];
  deadline?: Date;
  notes?: string;
}

// Import der neuen Interfaces aus cutting.ts
import type { 
  ManualDrumSelection, 
  DeliveryTracking, 
  DrumComparisonResult, 
  DrumMatch, 
  DrumDifference,
  AvailableDrum 
} from '../../types/cutting';

// Re-export für andere Module
export type { 
  ManualDrumSelection, 
  DeliveryTracking, 
  DrumComparisonResult, 
  DrumMatch, 
  DrumDifference,
  AvailableDrum 
} from '../../types/cutting';

// Erweiterte Delivery-Interface mit Trommelauswahl-Vergleich
export interface ExtendedDelivery extends Delivery {
  // Neue Felder für Trommelauswahl-Vergleich
  selectedDrums?: ManualDrumSelection[]; // Tatsächlich ausgewählte Trommeln
  selectionDate?: Date; // Datum der Trommelauswahl
  aiSuggestedDrums?: AvailableDrum[]; // Von der KI vorgeschlagene Trommeln
}

// Erweiterte Interface für Bestandsdaten aus der Datenbank
export interface InventoryDrum {
  id?: number;
  material?: string; // Material-Code
  charge?: string; // Eindeutige Charge-Nummer
  dauer?: number; // Lagerdauer
  lagereinheitentyp?: string; // Trommelgröße (z.B. L040, L090)
  lieferung?: string; // Liefernummer (null = verfügbar)
  gesamtbestand?: number; // Kabellänge in Metern
  aufnahmeDatum?: string; // Aufnahmedatum
  aufnahmeZeit?: string; // Aufnahmezeit
}