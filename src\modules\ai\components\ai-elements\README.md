# AI Elements Components

Diese AI Elements Komponenten sind für den JASZ Chatbot optimiert und bieten eine vollständige Suite von UI-Komponenten für AI-Interaktionen.

## Verfügbare Komponenten

### Conversation Components
- `Conversation` - Container für Chat-Nachrichten mit Auto-Scroll
- `ConversationContent` - Inhalt-Container für Nachrichten
- `ConversationScrollButton` - Button zum Scrollen nach unten

### Message Components
- `Message` - Container für einzelne Nachrichten
- `MessageContent` - Inhalt einer Nachricht
- `MessageAvatar` - Avatar für Benutzer/AI

### Response Components
- `Response` - Markdown-Renderer für AI-Antworten mit Syntax-Highlighting

### Input Components
- `PromptInput` - Container für Eingabe-Form
- `PromptInputTextarea` - Textarea mit Auto-Resize
- `PromptInputToolbar` - <PERSON><PERSON><PERSON> für Buttons
- `PromptInputTools` - Container für Tool-Buttons
- `PromptInputButton` - But<PERSON> für Tools
- `PromptInputSubmit` - Submit-Button mit Status-Anzeige

### Interactive Components
- `Actions` - Container für Aktions-Buttons
- `Action` - Einzelner Aktions-Button mit Tooltip
- `Suggestions` - Container für Vorschläge
- `Suggestion` - Einzelner Vorschlag-Button

### Information Components
- `Sources` - Collapsible Container für Quellen
- `SourcesTrigger` - Trigger für Quellen-Anzeige
- `SourcesContent` - Inhalt der Quellen
- `Source` - Einzelne Quelle mit Link

### Task Components
- `Task` - Container für Task-Anzeige
- `TaskTrigger` - Trigger für Task-Details
- `TaskContent` - Inhalt der Task-Details
- `TaskItem` - Einzelner Task-Schritt
- `TaskItemFile` - Datei-Badge für Tasks

### Tool Components
- `Tool` - Container für Tool-Ausführung
- `ToolHeader` - Header mit Tool-Status
- `ToolContent` - Inhalt der Tool-Ausführung
- `ToolInput` - Anzeige der Tool-Parameter
- `ToolOutput` - Anzeige der Tool-Ergebnisse

### Utility Components
- `Loader` - Animierter Loader mit verschiedenen Größen

## Verwendung

```tsx
import {
  Conversation,
  ConversationContent,
  Message,
  MessageContent,
  MessageAvatar,
  Response,
  Actions,
  Action,
  Suggestions,
  Suggestion
} from '@/modules/ai/components/ai-elements';

// Beispiel für eine vollständige Nachricht
<Message from="assistant">
  <MessageAvatar src={lappLogo} name="JASZ" />
  <MessageContent>
    <Response>Hier ist die AI-Antwort mit **Markdown** Support!</Response>
    
    <Actions>
      <Action tooltip="Kopieren">
        <Copy className="w-4 h-4" />
      </Action>
    </Actions>
    
    <Suggestions>
      <Suggestion 
        suggestion="Weitere Details" 
        onClick={handleSuggestionClick} 
      />
    </Suggestions>
  </MessageContent>
</Message>
```

## Features

### Markdown Support
- Vollständige Markdown-Unterstützung mit Syntax-Highlighting
- Code-Blöcke mit Copy-Button
- Mathematische Formeln (KaTeX)
- Sichere Link- und Bild-Behandlung

### Interaktivität
- Klickbare Vorschläge
- Aktions-Buttons mit Tooltips
- Collapsible Sections für Quellen und Tasks
- Auto-Scroll Funktionalität

### Accessibility
- Vollständige Keyboard-Navigation
- Screen-Reader Support
- ARIA-Labels und Rollen
- Fokus-Management

### Styling
- Konsistente Tailwind CSS Klassen
- Dark/Light Mode Support
- Responsive Design
- Neobrutalism Design-System Integration

## Demo

Besuchen Sie `/ai/demo` um alle Komponenten in Aktion zu sehen.

## Integration im ChatBot

Der ChatBot in `src/modules/ai/components/chat/ChatBot.tsx` verwendet alle diese Komponenten für eine vollständige AI-Chat-Erfahrung mit:

- Erweiterte Nachrichten mit Quellen, Tasks und Tools
- AI+ Modus mit zusätzlichen Features
- Interaktive Vorschläge
- Markdown-Rendering für AI-Antworten
- Floating Button Design für alle Seiten