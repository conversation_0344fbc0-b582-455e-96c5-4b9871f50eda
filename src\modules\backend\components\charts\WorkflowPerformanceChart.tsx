import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Clock, CheckCircle, AlertTriangle, Activity } from 'lucide-react';
import { useWorkflows } from '@/hooks/useWorkflows';
import { WorkflowStats, WorkflowExecution } from '@/types/workflow';
import { WorkflowExecutionChart } from './WorkflowExecutionChart';
import { WorkflowStatusChart } from './WorkflowStatusChart';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { AskJaszButton } from '@/modules/ai/components';
import { createKpiContext, PROMPT_TEMPLATES } from '@/contexts/AskJaszContext';

export function WorkflowPerformanceChart() {
  const { stats, executions, loading, error } = useWorkflows();

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const getExecutionData = () => {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    }).reverse();

    return last7Days.map(date => {
      const dayExecutions = executions.filter(e => 
        e.startTime.toISOString().split('T')[0] === date
      );
      
      return {
        date: new Date(date).toLocaleDateString('de-DE', { weekday: 'short', day: '2-digit' }),
        successful: dayExecutions.filter(e => e.status === 'completed').length,
        failed: dayExecutions.filter(e => e.status === 'error').length,
        total: dayExecutions.length
      };
    });
  };

  const getStatusDistribution = () => {
    if (!stats) return [];
    
    return [
      { name: 'Erfolgreich', value: stats.successfulExecutions, color: '#10b981' },
      { name: 'Fehlgeschlagen', value: stats.failedExecutions, color: '#ef4444' },
    ];
  };

  const getProcessPerformance = () => {
    const processStats = executions.reduce((acc, execution) => {
      if (!acc[execution.processId]) {
        acc[execution.processId] = {
          processId: execution.processId,
          total: 0,
          successful: 0,
          failed: 0,
          totalDuration: 0
        };
      }
      
      acc[execution.processId].total++;
      if (execution.status === 'completed') acc[execution.processId].successful++;
      if (execution.status === 'error') acc[execution.processId].failed++;
      
      if (execution.endTime) {
        acc[execution.processId].totalDuration += 
          execution.endTime.getTime() - execution.startTime.getTime();
      }
      
      return acc;
    }, {} as Record<string, any>);

    return Object.values(processStats).map((stat: any) => ({
      ...stat,
      avgDuration: stat.successful > 0 ? stat.totalDuration / stat.successful : 0,
      successRate: stat.total > 0 ? (stat.successful / stat.total) * 100 : 0
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  const executionData = getExecutionData();
  const statusDistribution = getStatusDistribution();
  const processPerformance = getProcessPerformance();

  // BentoCard: visuell angelehnt an BentoGridItem aus mvpblocks/bento-grid-1.tsx
  const BentoCard = ({
    title,
    value,
    icon,
    accent = 'primary',
    kpiType,
    description,
  }: {
    title: string;
    value: React.ReactNode;
    icon: React.ReactNode;
    accent?: 'primary' | 'green' | 'red' | 'blue';
    kpiType?: string;
    description?: string;
  }) => {
    const accentClasses =
      accent === 'green'
        ? {
            grid: 'bg-[linear-gradient(to_right,#16a34a30_1px,transparent_1px),linear-gradient(to_bottom,#16a34a30_1px,transparent_1px)]',
            icon: 'text-green-600',
            chipBg: 'bg-emerald-500/10',
            chipHover: 'group-hover:bg-emerald-500/20',
            glow: 'from-emerald-500 to-emerald-400/30',
            valueText: 'text-emerald-600',
          }
        : accent === 'red'
        ? {
            grid: 'bg-[linear-gradient(to_right,#ef444430_1px,transparent_1px),linear-gradient(to_bottom,#ef444430_1px,transparent_1px)]',
            icon: 'text-red-600',
            chipBg: 'bg-red-500/10',
            chipHover: 'group-hover:bg-red-500/20',
            glow: 'from-red-500 to-red-400/30',
            valueText: 'text-red-600',
          }
        : accent === 'blue'
        ? {
            grid: 'bg-[linear-gradient(to_right,#3b82f630_1px,transparent_1px),linear-gradient(to_bottom,#3b82f630_1px,transparent_1px)]',
            icon: 'text-blue-600',
            chipBg: 'bg-blue-500/10',
            chipHover: 'group-hover:bg-blue-500/20',
            glow: 'from-blue-500 to-blue-400/30',
            valueText: 'text-blue-600',
          }
        : {
            grid: 'bg-[linear-gradient(to_right,#6b728030_1px,transparent_1px),linear-gradient(to_bottom,#6b728030_1px,transparent_1px)]',
            icon: 'text-primary',
            chipBg: 'bg-primary/10',
            chipHover: 'group-hover:bg-primary/20',
            glow: 'from-primary to-primary/30',
            valueText: '',
          };

    return (
      <motion.div
        variants={{
          hidden: { opacity: 0, y: 20 },
          visible: { opacity: 1, y: 0, transition: { type: 'spring', damping: 25 } },
        }}
        className={cn(
          'group border-primary/10 bg-background hover:border-primary/30 relative flex h-full flex-col justify-between overflow-hidden rounded-xl border px-6 pt-6 pb-6 shadow-md transition-all duration-500'
        )}
      >
        {/* Ask JASZ Button - appears on hover */}
        {kpiType && (
          <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-20">
            <AskJaszButton
              context={createKpiContext(title, value, description || `Workflow-KPI für ${title}`)}
              template={PROMPT_TEMPLATES.KPI_EXPLANATION}
              position="floating"
              size="sm"
              tooltip={`Lass dir die KPI "${title}" erklären`}
            />
          </div>
        )}
        {/* Hintergrund-Raster wie Bento */}
        <div
          className={cn(
            'absolute top-0 -right-1/2 z-0 size-full cursor-pointer [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:24px_24px]',
            accentClasses.grid
          )}
        />
        {/* großes Icon im Hintergrund - stärker verblasst */}
        <div
          className={cn(
            'absolute right-2 bottom-2 scale-[5] transition-all duration-700 group-hover:scale-[5.05] opacity-10',
            accentClasses.icon
          )}
          aria-hidden
        >
          {icon}
        </div>

        <div className="relative z-10 flex h-full flex-col justify-between">
          <div>
            <div
              className={cn(
                'text-primary shadow-primary/10 mb-4 flex h-12 w-12 items-center justify-center rounded-full shadow transition-all duration-500',
                accentClasses.chipBg,
                accentClasses.chipHover
              )}
            >
              {icon}
            </div>
            <h3 className="mb-2 text-sm font-medium text-muted-foreground">{title}</h3>
            <div className={cn('text-2xl font-bold', accentClasses.valueText)}>{value}</div>
          </div>
        </div>
        {/* untere Glow-Linie wie Bento - dezenter */}
        <div
          className={cn(
            'absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r blur-xl transition-all duration-500 group-hover:blur-lg opacity-40',
            accentClasses.glow
          )}
        />
      </motion.div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards im Bento-Grid-Stil */}
      {/* Vier Karten in einer Zeile nebeneinander ab md:4 Spalten */}
      <motion.div
        className="grid grid-cols-1 gap-4 md:grid-cols-4"
        variants={{ hidden: {}, visible: { transition: { staggerChildren: 0.12, delayChildren: 0.1 } } }}
        initial="hidden"
        animate="visible"
      >
        <BentoCard
          title="Gesamt Ausführungen"
          value={stats?.totalExecutions || 0}
          icon={<Activity className="size-6" />}
          accent="blue"
          kpiType="total-executions"
          description="Gesamtanzahl aller Workflow-Ausführungen im System"
        />
        <BentoCard
          title="Erfolgreich"
          value={<span className="text-emerald-600">{stats?.successfulExecutions || 0}</span>}
          icon={<CheckCircle className="size-6" />}
          accent="green"
          kpiType="successful-executions"
          description="Anzahl der erfolgreich abgeschlossenen Workflow-Ausführungen"
        />
        <BentoCard
          title="Fehlgeschlagen"
          value={<span className="text-red-600">{stats?.failedExecutions || 0}</span>}
          icon={<AlertTriangle className="size-6" />}
          accent="red"
          kpiType="failed-executions"
          description="Anzahl der fehlgeschlagenen Workflow-Ausführungen mit Fehlerstatus"
        />
        <BentoCard
          title="Ø Dauer"
          value={formatDuration(stats?.averageDuration || 0)}
          icon={<Clock className="size-6" />}
          accent="blue"
          kpiType="average-duration"
          description="Durchschnittliche Ausführungsdauer aller Workflows"
        />
      </motion.div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Execution Trend */}
        <WorkflowExecutionChart data={executionData} />

        {/* Status Distribution */}
        <WorkflowStatusChart data={statusDistribution} />
      </div>

      {/* Process Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Prozess Performance</CardTitle>
          <CardDescription>Detaillierte Statistiken pro Prozess</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Prozess</th>
                  <th className="text-left p-2">Gesamt</th>
                  <th className="text-left p-2">Erfolgreich</th>
                  <th className="text-left p-2">Fehlgeschlagen</th>
                  <th className="text-left p-2">Erfolgsrate</th>
                  <th className="text-left p-2">Ø Dauer</th>
                </tr>
              </thead>
              <tbody>
                {processPerformance.map((process) => (
                  <tr key={process.processId} className="border-b">
                    <td className="p-2 font-medium">{process.processId}</td>
                    <td className="p-2">{process.total}</td>
                    <td className="p-2 text-green-600">{process.successful}</td>
                    <td className="p-2 text-red-600">{process.failed}</td>
                    <td className="p-2">
                      <Badge
                        variant={process.successRate >= 90 ? "secondary" : process.successRate >= 70 ? "outline" : "destructive"}
                        className={process.successRate >= 90 ? "bg-green-100 text-green-800" : ""}
                      >
                        {process.successRate.toFixed(1)}%
                      </Badge>
                    </td>
                    <td className="p-2">{formatDuration(process.avgDuration)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}