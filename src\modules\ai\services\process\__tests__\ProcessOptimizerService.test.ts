/**
 * Process Optimizer Service Tests
 * Unit tests for the main process optimization service
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ProcessOptimizerService } from '../ProcessOptimizerService';
import {
  ProcessData,
  ProcessStep,
  ProcessResource,
  ProcessMetrics,
  ProcessHistoricalData,
  ProcessChange,
  SimulationConfig
} from '../types';

describe('ProcessOptimizerService', () => {
  let service: ProcessOptimizerService;
  let mockProcessData: ProcessData;
  let mockHistoricalData: ProcessHistoricalData[];

  beforeEach(() => {
    service = new ProcessOptimizerService();

    // Mock process data
    mockProcessData = {
      processId: 'test-process-001',
      name: 'Test Manufacturing Process',
      steps: [
        {
          stepId: 'step-001',
          name: 'Material Preparation',
          duration: 30, // minutes
          resourceRequirements: [
            { resourceId: 'worker-001', quantity: 1, duration: 30 }
          ],
          dependencies: [],
          capacity: 2,
          currentUtilization: 0.8
        },
        {
          stepId: 'step-002',
          name: 'Assembly',
          duration: 45,
          resourceRequirements: [
            { resourceId: 'worker-002', quantity: 1, duration: 45 },
            { resourceId: 'machine-001', quantity: 1, duration: 45 }
          ],
          dependencies: ['step-001'],
          capacity: 1,
          currentUtilization: 0.95
        },
        {
          stepId: 'step-003',
          name: 'Quality Check',
          duration: 15,
          resourceRequirements: [
            { resourceId: 'worker-003', quantity: 1, duration: 15 }
          ],
          dependencies: ['step-002'],
          capacity: 3,
          currentUtilization: 0.6
        }
      ],
      resources: [
        {
          resourceId: 'worker-001',
          name: 'Preparation Worker',
          type: 'human',
          capacity: 2,
          currentLoad: 0.8,
          availability: [],
          costPerHour: 25
        },
        {
          resourceId: 'worker-002',
          name: 'Assembly Worker',
          type: 'human',
          capacity: 1,
          currentLoad: 0.95,
          availability: [],
          costPerHour: 30
        },
        {
          resourceId: 'worker-003',
          name: 'Quality Inspector',
          type: 'human',
          capacity: 3,
          currentLoad: 0.6,
          availability: [],
          costPerHour: 35
        },
        {
          resourceId: 'machine-001',
          name: 'Assembly Machine',
          type: 'machine',
          capacity: 1,
          currentLoad: 0.9,
          availability: [],
          costPerHour: 50
        }
      ],
      metrics: {
        throughput: 12, // items per hour
        cycleTime: 90, // minutes
        leadTime: 120, // minutes
        efficiency: 0.75,
        utilization: 0.82,
        qualityRate: 0.94,
        cost: 150
      },
      constraints: [],
      historicalData: []
    };

    // Mock historical data
    mockHistoricalData = [
      {
        timestamp: new Date('2024-01-01T08:00:00Z'),
        metrics: {
          throughput: 11,
          cycleTime: 95,
          leadTime: 125,
          efficiency: 0.72,
          utilization: 0.80,
          qualityRate: 0.92,
          cost: 155
        },
        events: [
          {
            eventId: 'event-001',
            timestamp: new Date('2024-01-01T08:30:00Z'),
            type: 'delay',
            stepId: 'step-002',
            duration: 10,
            description: 'Machine maintenance delay',
            impact: 'negative'
          }
        ],
        resourceUtilization: {
          'worker-001': 0.75,
          'worker-002': 0.90,
          'worker-003': 0.55,
          'machine-001': 0.85
        }
      },
      {
        timestamp: new Date('2024-01-01T16:00:00Z'),
        metrics: {
          throughput: 13,
          cycleTime: 85,
          leadTime: 115,
          efficiency: 0.78,
          utilization: 0.84,
          qualityRate: 0.96,
          cost: 145
        },
        events: [],
        resourceUtilization: {
          'worker-001': 0.80,
          'worker-002': 0.95,
          'worker-003': 0.65,
          'machine-001': 0.90
        }
      }
    ];
  });

  describe('analyzeProcess', () => {
    it('should analyze process and identify bottlenecks', async () => {
      const analysis = await service.analyzeProcess(mockProcessData, mockHistoricalData);

      expect(analysis).toBeDefined();
      expect(analysis.processId).toBe('test-process-001');
      expect(analysis.currentEfficiency).toBeGreaterThan(0);
      expect(analysis.bottlenecks).toBeInstanceOf(Array);
      expect(analysis.recommendations).toBeInstanceOf(Array);
      expect(analysis.improvementPotential).toBeGreaterThan(0);
      expect(analysis.analysisTimestamp).toBeInstanceOf(Date);
    });

    it('should identify high utilization bottleneck in assembly step', async () => {
      const analysis = await service.analyzeProcess(mockProcessData, mockHistoricalData);

      const assemblyBottleneck = analysis.bottlenecks.find(b => b.stepId === 'step-002');
      expect(assemblyBottleneck).toBeDefined();
      expect(['high', 'critical']).toContain(assemblyBottleneck?.severity);
    });

    it('should cache analysis results', async () => {
      const analysis1 = await service.analyzeProcess(mockProcessData, mockHistoricalData);
      const analysis2 = await service.analyzeProcess(mockProcessData, mockHistoricalData);

      expect(analysis1).toEqual(analysis2);
    });

    it('should handle process without historical data', async () => {
      const analysis = await service.analyzeProcess(mockProcessData, []);

      expect(analysis).toBeDefined();
      expect(analysis.bottlenecks).toBeInstanceOf(Array);
      expect(analysis.recommendations).toBeInstanceOf(Array);
    });
  });

  describe('identifyBottlenecks', () => {
    it('should identify bottlenecks correctly', async () => {
      const bottlenecks = await service.identifyBottlenecks(mockProcessData, mockHistoricalData);

      expect(bottlenecks).toBeInstanceOf(Array);
      expect(bottlenecks.length).toBeGreaterThan(0);

      // Should identify assembly step as bottleneck due to high utilization
      const assemblyBottleneck = bottlenecks.find(b => b.stepId === 'step-002');
      expect(assemblyBottleneck).toBeDefined();
      expect(assemblyBottleneck?.severity).toMatch(/high|critical/);
    });

    it('should rank bottlenecks by severity and impact', async () => {
      const bottlenecks = await service.identifyBottlenecks(mockProcessData, mockHistoricalData);

      if (bottlenecks.length > 1) {
        for (let i = 0; i < bottlenecks.length - 1; i++) {
          const current = bottlenecks[i];
          const next = bottlenecks[i + 1];
          
          const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          const currentScore = severityOrder[current.severity];
          const nextScore = severityOrder[next.severity];
          
          expect(currentScore).toBeGreaterThanOrEqual(nextScore);
        }
      }
    });
  });

  describe('simulateProcessChange', () => {
    it('should simulate process changes successfully', async () => {
      const changes: ProcessChange[] = [
        {
          changeId: 'change-001',
          type: 'capacity_change',
          description: 'Increase assembly capacity',
          affectedSteps: ['step-002'],
          affectedResources: [],
          parameters: { newCapacity: 2 },
          estimatedCost: 10000,
          estimatedTime: 14
        }
      ];

      const result = await service.simulateProcessChange(mockProcessData, changes);

      expect(result).toBeDefined();
      expect(result.scenarioName).toBe('Modified Process');
      expect(result.baselineMetrics).toBeDefined();
      expect(result.projectedMetrics).toBeDefined();
      expect(result.improvement).toBeDefined();
      expect(result.confidence).toBeGreaterThan(0);
    });

    it('should show improvement with capacity increase', async () => {
      const changes: ProcessChange[] = [
        {
          changeId: 'change-001',
          type: 'capacity_change',
          description: 'Increase assembly capacity',
          affectedSteps: ['step-002'],
          affectedResources: [],
          parameters: { newCapacity: 2 },
          estimatedCost: 10000,
          estimatedTime: 14
        }
      ];

      const result = await service.simulateProcessChange(mockProcessData, changes);

      expect(result.improvement.throughputImprovement).toBeGreaterThan(0);
      expect(result.improvement.efficiencyGain).toBeGreaterThan(0);
    });

    it('should cache simulation results', async () => {
      const changes: ProcessChange[] = [
        {
          changeId: 'change-001',
          type: 'automation',
          description: 'Automate quality check',
          affectedSteps: ['step-003'],
          affectedResources: [],
          parameters: { automationFactor: 0.3 },
          estimatedCost: 25000,
          estimatedTime: 30
        }
      ];

      const result1 = await service.simulateProcessChange(mockProcessData, changes);
      const result2 = await service.simulateProcessChange(mockProcessData, changes);

      expect(result1).toEqual(result2);
    });
  });

  describe('runDiscreteEventSimulation', () => {
    it('should run discrete event simulation successfully', async () => {
      const config: SimulationConfig = {
        processId: 'test-process-001',
        duration: 4, // 4 hours
        iterations: 50,
        changes: [],
        warmupPeriod: 0.5,
        confidenceLevel: 0.95
      };

      const output = await service.runDiscreteEventSimulation(mockProcessData, config);

      expect(output).toBeDefined();
      expect(output.results).toBeInstanceOf(Array);
      expect(output.bestScenario).toBeDefined();
      expect(output.worstScenario).toBeDefined();
      expect(output.averageScenario).toBeDefined();
      expect(output.recommendations).toBeInstanceOf(Array);
    });

    it('should compare baseline and modified scenarios', async () => {
      const changes: ProcessChange[] = [
        {
          changeId: 'change-001',
          type: 'step_modification',
          description: 'Reduce assembly time',
          affectedSteps: ['step-002'],
          affectedResources: [],
          parameters: { duration: 35 },
          estimatedCost: 5000,
          estimatedTime: 7
        }
      ];

      const config: SimulationConfig = {
        processId: 'test-process-001',
        duration: 2,
        iterations: 25,
        changes,
        warmupPeriod: 0.25,
        confidenceLevel: 0.90
      };

      const output = await service.runDiscreteEventSimulation(mockProcessData, config);

      expect(output.results.length).toBe(2); // Baseline + Modified
      expect(output.bestScenario.scenarioName).toMatch(/Modified|Baseline/);
    });
  });

  describe('generateOptimizationSuggestions', () => {
    it('should generate optimization suggestions', async () => {
      const analysis = await service.analyzeProcess(mockProcessData, mockHistoricalData);
      const suggestions = await service.generateOptimizationSuggestions(
        mockProcessData,
        analysis.bottlenecks,
        await service.calculateProcessEfficiency(mockProcessData, mockHistoricalData)
      );

      expect(suggestions).toBeInstanceOf(Array);
      expect(suggestions.length).toBeGreaterThan(0);

      suggestions.forEach(suggestion => {
        expect(suggestion.suggestionId).toBeDefined();
        expect(suggestion.category).toMatch(/bottleneck_removal|resource_optimization|process_redesign|automation|scheduling/);
        expect(suggestion.title).toBeDefined();
        expect(suggestion.description).toBeDefined();
        expect(suggestion.expectedBenefit).toBeDefined();
        expect(suggestion.priority).toBeGreaterThan(0);
      });
    });

    it('should prioritize suggestions correctly', async () => {
      const analysis = await service.analyzeProcess(mockProcessData, mockHistoricalData);
      const suggestions = await service.generateOptimizationSuggestions(
        mockProcessData,
        analysis.bottlenecks,
        await service.calculateProcessEfficiency(mockProcessData, mockHistoricalData)
      );

      if (suggestions.length > 1) {
        for (let i = 0; i < suggestions.length - 1; i++) {
          expect(suggestions[i].priority).toBeGreaterThanOrEqual(suggestions[i + 1].priority);
        }
      }
    });

    it('should limit suggestions to top 10', async () => {
      const analysis = await service.analyzeProcess(mockProcessData, mockHistoricalData);
      const suggestions = await service.generateOptimizationSuggestions(
        mockProcessData,
        analysis.bottlenecks,
        await service.calculateProcessEfficiency(mockProcessData, mockHistoricalData)
      );

      expect(suggestions.length).toBeLessThanOrEqual(10);
    });
  });

  describe('calculateProcessEfficiency', () => {
    it('should calculate efficiency metrics', async () => {
      const efficiency = await service.calculateProcessEfficiency(mockProcessData, mockHistoricalData);

      expect(efficiency).toBeDefined();
      expect(efficiency.overall).toBeGreaterThan(0);
      expect(efficiency.overall).toBeLessThanOrEqual(1);
      expect(efficiency.byStep).toBeDefined();
      expect(efficiency.byResource).toBeDefined();
      expect(efficiency.trends).toBeInstanceOf(Array);
      expect(efficiency.benchmarks).toBeInstanceOf(Array);
    });

    it('should calculate step-wise efficiency', async () => {
      const efficiency = await service.calculateProcessEfficiency(mockProcessData, mockHistoricalData);

      expect(Object.keys(efficiency.byStep)).toContain('step-001');
      expect(Object.keys(efficiency.byStep)).toContain('step-002');
      expect(Object.keys(efficiency.byStep)).toContain('step-003');

      Object.values(efficiency.byStep).forEach(stepEfficiency => {
        expect(stepEfficiency).toBeGreaterThan(0);
        expect(stepEfficiency).toBeLessThanOrEqual(1);
      });
    });

    it('should calculate resource-wise efficiency', async () => {
      const efficiency = await service.calculateProcessEfficiency(mockProcessData, mockHistoricalData);

      expect(Object.keys(efficiency.byResource)).toContain('worker-001');
      expect(Object.keys(efficiency.byResource)).toContain('worker-002');
      expect(Object.keys(efficiency.byResource)).toContain('machine-001');

      Object.values(efficiency.byResource).forEach(resourceEfficiency => {
        expect(resourceEfficiency).toBeGreaterThan(0);
        expect(resourceEfficiency).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('error handling', () => {
    it('should handle invalid process data', async () => {
      const invalidProcessData = { ...mockProcessData, steps: [] };

      await expect(service.analyzeProcess(invalidProcessData, mockHistoricalData))
        .rejects.toThrow();
    });

    it('should handle simulation errors gracefully', async () => {
      const invalidChanges: ProcessChange[] = [
        {
          changeId: 'invalid-change',
          type: 'step_modification',
          description: 'Invalid change',
          affectedSteps: ['non-existent-step'],
          affectedResources: [],
          parameters: {},
          estimatedCost: 0,
          estimatedTime: 0
        }
      ];

      // Should not throw, but handle gracefully
      const result = await service.simulateProcessChange(mockProcessData, invalidChanges);
      expect(result).toBeDefined();
    });
  });

  describe('performance', () => {
    it('should complete analysis within reasonable time', async () => {
      const startTime = Date.now();
      await service.analyzeProcess(mockProcessData, mockHistoricalData);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(5000); // 5 seconds
    });

    it('should complete simulation within reasonable time', async () => {
      const config: SimulationConfig = {
        processId: 'test-process-001',
        duration: 1,
        iterations: 10,
        changes: [],
        warmupPeriod: 0.1,
        confidenceLevel: 0.90
      };

      const startTime = Date.now();
      await service.runDiscreteEventSimulation(mockProcessData, config);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(10000); // 10 seconds
    });
  });
});