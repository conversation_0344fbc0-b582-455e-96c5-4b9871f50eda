"use strict";
/**
 * Database Performance Monitoring Validation Script
 *
 * Validiert die datenbankpersistente Version des Performance Monitoring Systems.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const performance_monitoring_factory_service_1 = __importDefault(require("../services/performance-monitoring-factory.service"));
async function validateDatabasePerformanceMonitoring() {
    console.log('🚀 Starting Database Performance Monitoring Validation...\n');
    const prisma = new client_1.PrismaClient();
    try {
        // Test 1: Factory Service
        console.log('🏭 Testing Performance Monitoring Factory...');
        const dbMonitor = performance_monitoring_factory_service_1.default.initialize(prisma, {
            useDatabasePersistence: true,
            databaseConfig: {
                enableInMemoryCache: true,
                inMemoryCacheSize: 100,
                batchSize: 5, // Small batch for testing
                flushInterval: 5 * 1000, // 5 seconds for testing
                retentionDays: 30,
                enableAggregation: true
            }
        });
        console.log(`✅ Factory initialized with database persistence: ${performance_monitoring_factory_service_1.default.isDatabasePersistenceEnabled()}`);
        // Test 2: Database Performance Monitoring Service
        console.log('\n📊 Testing Database Performance Monitoring Service...');
        const dbPerfMonitor = dbMonitor;
        // Record test metrics
        await dbPerfMonitor.recordQueryPerformance('stoerungen', 150, true, {
            dataSize: 1024,
            cacheHit: false,
            retryCount: 0
        });
        await dbPerfMonitor.recordIntentRecognition('Wie viele Störungen haben wir heute?', ['stoerungen'], 0.85, 50, ['störungen', 'heute'], 0.9);
        await dbPerfMonitor.recordEnrichmentPerformance('test_request_123', 800, true, {
            intentCount: 1,
            queryCount: 2,
            successfulQueries: 2,
            fallbackUsed: false,
            dataTypes: ['stoerungen']
        });
        await dbPerfMonitor.recordResponseTime('/api/chat/enhanced', true, 1500, 800, 700, 2048);
        console.log('✅ Test metrics recorded successfully');
        // Wait for batch flush
        console.log('⏳ Waiting for batch flush to database...');
        await new Promise(resolve => setTimeout(resolve, 6000));
        // Test 3: Database Queries
        console.log('\n🗄️ Testing Database Queries...');
        // Check if metrics were written to database
        const queryMetrics = await prisma.queryPerformanceMetric.findMany({
            take: 5,
            orderBy: { timestamp: 'desc' }
        });
        const responseMetrics = await prisma.responseTimeMetric.findMany({
            take: 5,
            orderBy: { timestamp: 'desc' }
        });
        const intentMetrics = await prisma.intentRecognitionMetric.findMany({
            take: 5,
            orderBy: { timestamp: 'desc' }
        });
        const enrichmentMetrics = await prisma.enrichmentPerformanceMetric.findMany({
            take: 5,
            orderBy: { timestamp: 'desc' }
        });
        console.log(`✅ Database contains:`);
        console.log(`   - Query metrics: ${queryMetrics.length}`);
        console.log(`   - Response metrics: ${responseMetrics.length}`);
        console.log(`   - Intent metrics: ${intentMetrics.length}`);
        console.log(`   - Enrichment metrics: ${enrichmentMetrics.length}`);
        // Test 4: Performance Statistics from Database
        console.log('\n📈 Testing Performance Statistics from Database...');
        const stats = await dbPerfMonitor.getPerformanceStats();
        console.log(`✅ Performance stats retrieved:`);
        console.log(`   - Total requests: ${stats.totalRequests}`);
        console.log(`   - Average response time: ${stats.averageResponseTime.toFixed(0)}ms`);
        console.log(`   - Success rate: ${(stats.successRate * 100).toFixed(1)}%`);
        console.log(`   - Cache hit rate: ${(stats.cacheHitRate * 100).toFixed(1)}%`);
        // Test 5: Performance Alerts
        console.log('\n🚨 Testing Performance Alerts...');
        await dbPerfMonitor.createPerformanceAlert('warning', 'Test alert for validation', 'response_time', 2500, 2000);
        const alerts = await dbPerfMonitor.getPerformanceAlerts();
        console.log(`✅ Performance alerts: ${alerts.length} alerts found`);
        // Test 6: Performance Trends
        console.log('\n📊 Testing Performance Trends...');
        // Add more test data for trends
        for (let i = 0; i < 10; i++) {
            await dbPerfMonitor.recordResponseTime('/api/chat/enhanced', true, 1000 + (i * 100), // Increasing response times
            500, 500, 1024);
        }
        // Wait for flush
        await new Promise(resolve => setTimeout(resolve, 6000));
        const trends = await dbPerfMonitor.getPerformanceTrends('response_time', 'hour', 12);
        console.log(`✅ Performance trends: ${trends.length} data points retrieved`);
        // Test 7: Data Export
        console.log('\n📤 Testing Data Export...');
        const jsonExport = await dbPerfMonitor.exportMetrics('json');
        const csvExport = await dbPerfMonitor.exportMetrics('csv');
        console.log(`✅ Data export successful:`);
        console.log(`   - JSON export: ${jsonExport.length} characters`);
        console.log(`   - CSV export: ${csvExport.split('\n').length} lines`);
        // Test 8: Database Cleanup
        console.log('\n🧹 Testing Database Cleanup...');
        // Create old test data
        const oldDate = new Date();
        oldDate.setDate(oldDate.getDate() - 100); // 100 days ago
        await prisma.queryPerformanceMetric.create({
            data: {
                metricId: 'old_test_metric',
                queryType: 'stoerungen',
                duration: 200,
                success: true,
                dataSize: 512,
                cacheHit: false,
                retryCount: 0,
                timestamp: oldDate
            }
        });
        const cleanupResult = await dbPerfMonitor.cleanupOldMetrics();
        console.log(`✅ Cleanup completed: ${cleanupResult.deleted} old metrics removed`);
        // Test 9: Memory and Performance
        console.log('\n🧠 Testing Memory and Performance...');
        const startTime = Date.now();
        const initialMemory = process.memoryUsage();
        // Record many metrics to test performance
        const promises = [];
        for (let i = 0; i < 100; i++) {
            promises.push(dbPerfMonitor.recordQueryPerformance('dispatch', 100 + i, true, {
                dataSize: 1024,
                cacheHit: i % 2 === 0
            }));
        }
        await Promise.all(promises);
        const endTime = Date.now();
        const finalMemory = process.memoryUsage();
        const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
        console.log(`✅ Performance test completed:`);
        console.log(`   - 100 metrics recorded in: ${endTime - startTime}ms`);
        console.log(`   - Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
        // Test 10: Concurrent Operations
        console.log('\n🔄 Testing Concurrent Operations...');
        const concurrentPromises = [
            dbPerfMonitor.recordQueryPerformance('stoerungen', 120, true),
            dbPerfMonitor.recordQueryPerformance('dispatch', 180, true),
            dbPerfMonitor.recordQueryPerformance('cutting', 160, true),
            dbPerfMonitor.getPerformanceStats(),
            dbPerfMonitor.getPerformanceAlerts()
        ];
        const concurrentResults = await Promise.all(concurrentPromises);
        console.log(`✅ Concurrent operations completed successfully`);
        // Final flush and verification
        console.log('\n🔍 Final Verification...');
        // Wait for final flush
        await new Promise(resolve => setTimeout(resolve, 6000));
        const finalCounts = await Promise.all([
            prisma.queryPerformanceMetric.count(),
            prisma.responseTimeMetric.count(),
            prisma.intentRecognitionMetric.count(),
            prisma.enrichmentPerformanceMetric.count(),
            prisma.performanceAlert.count()
        ]);
        console.log(`✅ Final database state:`);
        console.log(`   - Query metrics: ${finalCounts[0]}`);
        console.log(`   - Response metrics: ${finalCounts[1]}`);
        console.log(`   - Intent metrics: ${finalCounts[2]}`);
        console.log(`   - Enrichment metrics: ${finalCounts[3]}`);
        console.log(`   - Performance alerts: ${finalCounts[4]}`);
        console.log('\n🎉 Database Performance Monitoring Validation Complete!');
        console.log('✅ All tests passed successfully');
        return true;
    }
    catch (error) {
        console.error('\n❌ Validation failed:', error);
        return false;
    }
    finally {
        // Cleanup
        performance_monitoring_factory_service_1.default.destroy();
        await prisma.$disconnect();
    }
}
// Run validation if this file is executed directly
if (require.main === module) {
    validateDatabasePerformanceMonitoring()
        .then((success) => {
        if (success) {
            console.log('\n✅ Database Performance Monitoring validation completed successfully');
            process.exit(0);
        }
        else {
            console.log('\n❌ Database Performance Monitoring validation failed');
            process.exit(1);
        }
    })
        .catch((error) => {
        console.error('\n❌ Validation error:', error);
        process.exit(1);
    });
}
exports.default = validateDatabasePerformanceMonitoring;
