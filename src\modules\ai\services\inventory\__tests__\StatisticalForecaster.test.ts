/**
 * Statistical Forecaster Tests
 * 
 * Unit tests for statistical forecasting models
 */

import { StatisticalForecaster } from '../algorithms/StatisticalForecaster';

describe('StatisticalForecaster', () => {
  // Test data generators
  const generateStableData = (length: number, mean: number = 10, noise: number = 1): number[] => {
    return Array.from({ length }, () => mean + (Math.random() - 0.5) * noise * 2);
  };

  const generateTrendingData = (length: number, start: number = 5, slope: number = 0.1): number[] => {
    return Array.from({ length }, (_, i) => start + slope * i + (Math.random() - 0.5) * 2);
  };

  const generateSeasonalData = (length: number, period: number = 7, amplitude: number = 3): number[] => {
    return Array.from({ length }, (_, i) => 
      10 + amplitude * Math.sin((2 * Math.PI * i) / period) + (Math.random() - 0.5) * 2
    );
  };

  describe('simpleMovingAverage', () => {
    it('should generate correct predictions for stable data', () => {
      const data = generateStableData(20, 10, 1);
      const { predictions, accuracy } = StatisticalForecaster.simpleMovingAverage(data, 5, 3);

      expect(predictions).toHaveLength(3);
      expect(accuracy.mae).toBeGreaterThan(0);
      expect(accuracy.mse).toBeGreaterThan(0);
      expect(accuracy.rmse).toBeGreaterThan(0);
      expect(accuracy.mape).toBeGreaterThan(0);

      // Predictions should be non-negative
      predictions.forEach(pred => {
        expect(pred).toBeGreaterThanOrEqual(0);
      });
    });

    it('should handle insufficient data', () => {
      const data = [1, 2, 3];
      
      expect(() => {
        StatisticalForecaster.simpleMovingAverage(data, 5, 1);
      }).toThrow('Insufficient data');
    });

    it('should work with minimum required data', () => {
      const data = [1, 2, 3, 4, 5];
      const { predictions } = StatisticalForecaster.simpleMovingAverage(data, 5, 2);

      expect(predictions).toHaveLength(2);
      expect(predictions[0]).toBe(3); // Average of [1,2,3,4,5]
    });

    it('should handle different window sizes', () => {
      const data = generateStableData(20, 10, 1);
      
      const result3 = StatisticalForecaster.simpleMovingAverage(data, 3, 1);
      const result7 = StatisticalForecaster.simpleMovingAverage(data, 7, 1);

      expect(result3.predictions).toHaveLength(1);
      expect(result7.predictions).toHaveLength(1);
      
      // Both should produce reasonable predictions
      expect(result3.predictions[0]).toBeGreaterThan(0);
      expect(result7.predictions[0]).toBeGreaterThan(0);
    });
  });

  describe('exponentialSmoothing', () => {
    it('should generate predictions for stable data', () => {
      const data = generateStableData(15, 10, 1);
      const { predictions, accuracy } = StatisticalForecaster.exponentialSmoothing(data, 0.3, 3);

      expect(predictions).toHaveLength(3);
      expect(accuracy.mae).toBeGreaterThan(0);
      
      // For simple exponential smoothing, predictions should be constant
      expect(predictions[0]).toBeCloseTo(predictions[1], 1);
      expect(predictions[1]).toBeCloseTo(predictions[2], 1);
    });

    it('should handle different alpha values', () => {
      const data = generateStableData(15, 10, 1);
      
      const lowAlpha = StatisticalForecaster.exponentialSmoothing(data, 0.1, 1);
      const highAlpha = StatisticalForecaster.exponentialSmoothing(data, 0.9, 1);

      expect(lowAlpha.predictions).toHaveLength(1);
      expect(highAlpha.predictions).toHaveLength(1);
      
      // Both should produce valid predictions
      expect(lowAlpha.predictions[0]).toBeGreaterThan(0);
      expect(highAlpha.predictions[0]).toBeGreaterThan(0);
    });

    it('should require minimum data', () => {
      const data = [5];
      
      expect(() => {
        StatisticalForecaster.exponentialSmoothing(data, 0.3, 1);
      }).toThrow('Insufficient data');
    });
  });

  describe('doubleExponentialSmoothing', () => {
    it('should handle trending data', () => {
      const data = generateTrendingData(20, 5, 0.2);
      const { predictions, accuracy } = StatisticalForecaster.doubleExponentialSmoothing(data, 0.3, 0.1, 5);

      expect(predictions).toHaveLength(5);
      expect(accuracy.mae).toBeGreaterThan(0);

      // For trending data, predictions should generally increase
      const firstPred = predictions[0];
      const lastPred = predictions[predictions.length - 1];
      expect(lastPred).toBeGreaterThan(firstPred);
    });

    it('should require minimum data', () => {
      const data = [1, 2];
      
      expect(() => {
        StatisticalForecaster.doubleExponentialSmoothing(data, 0.3, 0.1, 1);
      }).toThrow('Insufficient data');
    });

    it('should handle different parameter combinations', () => {
      const data = generateTrendingData(15, 5, 0.1);
      
      const result1 = StatisticalForecaster.doubleExponentialSmoothing(data, 0.1, 0.1, 2);
      const result2 = StatisticalForecaster.doubleExponentialSmoothing(data, 0.9, 0.9, 2);

      expect(result1.predictions).toHaveLength(2);
      expect(result2.predictions).toHaveLength(2);
    });
  });

  describe('linearRegression', () => {
    it('should fit linear trends correctly', () => {
      const data = [1, 2, 3, 4, 5]; // Perfect linear trend
      const { predictions, slope, intercept, accuracy } = StatisticalForecaster.linearRegression(data, 3);

      expect(predictions).toHaveLength(3);
      expect(slope).toBeCloseTo(1, 1); // Slope should be close to 1
      expect(intercept).toBeCloseTo(1, 1); // Intercept should be close to 1
      expect(accuracy.r2).toBeGreaterThan(0.9); // Should have high R²
      
      // Predictions should continue the trend
      expect(predictions[0]).toBeCloseTo(6, 1);
      expect(predictions[1]).toBeCloseTo(7, 1);
      expect(predictions[2]).toBeCloseTo(8, 1);
    });

    it('should handle flat data', () => {
      const data = [5, 5, 5, 5, 5];
      const { predictions, slope, intercept } = StatisticalForecaster.linearRegression(data, 2);

      expect(predictions).toHaveLength(2);
      expect(slope).toBeCloseTo(0, 1); // Slope should be close to 0
      expect(intercept).toBeCloseTo(5, 1); // Intercept should be close to 5
      
      // Predictions should remain flat
      predictions.forEach(pred => {
        expect(pred).toBeCloseTo(5, 1);
      });
    });

    it('should require minimum data', () => {
      const data = [1];
      
      expect(() => {
        StatisticalForecaster.linearRegression(data, 1);
      }).toThrow('Insufficient data');
    });

    it('should handle noisy linear data', () => {
      const data = generateTrendingData(20, 0, 1); // Linear trend with noise
      const { predictions, accuracy } = StatisticalForecaster.linearRegression(data, 3);

      expect(predictions).toHaveLength(3);
      expect(accuracy.r2).toBeGreaterThan(0); // Should capture some of the trend
    });
  });

  describe('seasonalNaive', () => {
    it('should repeat seasonal patterns', () => {
      const data = [1, 2, 3, 1, 2, 3, 1, 2, 3]; // Clear 3-period pattern
      const { predictions, accuracy } = StatisticalForecaster.seasonalNaive(data, 3, 6);

      expect(predictions).toHaveLength(6);
      expect(accuracy.mae).toBeGreaterThanOrEqual(0); // Allow for perfect predictions (MAE = 0)
      
      // Should repeat the pattern: 1, 2, 3, 1, 2, 3
      expect(predictions[0]).toBe(1);
      expect(predictions[1]).toBe(2);
      expect(predictions[2]).toBe(3);
      expect(predictions[3]).toBe(1);
      expect(predictions[4]).toBe(2);
      expect(predictions[5]).toBe(3);
    });

    it('should handle weekly patterns', () => {
      const weeklyData = generateSeasonalData(21, 7, 2); // 3 weeks of data
      const { predictions } = StatisticalForecaster.seasonalNaive(weeklyData, 7, 7);

      expect(predictions).toHaveLength(7);
      
      // Should repeat the last week's pattern
      for (let i = 0; i < 7; i++) {
        expect(predictions[i]).toBe(weeklyData[weeklyData.length - 7 + i]);
      }
    });

    it('should require sufficient data', () => {
      const data = [1, 2];
      
      expect(() => {
        StatisticalForecaster.seasonalNaive(data, 5, 1);
      }).toThrow('Insufficient data');
    });
  });

  describe('weightedMovingAverage', () => {
    it('should apply weights correctly', () => {
      const data = [1, 2, 3, 4, 5];
      const weights = [0.1, 0.2, 0.3, 0.4]; // More weight on recent values
      const { predictions } = StatisticalForecaster.weightedMovingAverage(data, weights, 1);

      expect(predictions).toHaveLength(1);
      
      // The weighted average should use the last 4 values: [2, 3, 4, 5]
      // Manual calculation: (2*0.1 + 3*0.2 + 4*0.3 + 5*0.4) = 0.2 + 0.6 + 1.2 + 2.0 = 4.0
      // Actually, let me recalculate: 2*0.1 + 3*0.2 + 4*0.3 + 5*0.4 = 0.2 + 0.6 + 1.2 + 2.0 = 4.0
      expect(predictions[0]).toBeCloseTo(4.0, 1);
    });

    it('should normalize weights automatically', () => {
      const data = [1, 2, 3, 4, 5];
      const weights = [1, 2, 3, 4]; // Will be normalized to [0.1, 0.2, 0.3, 0.4]
      const { predictions } = StatisticalForecaster.weightedMovingAverage(data, weights, 1);

      expect(predictions).toHaveLength(1);
      expect(predictions[0]).toBeCloseTo(4.0, 1);
    });

    it('should handle equal weights (same as simple moving average)', () => {
      const data = [2, 4, 6, 8, 10];
      const weights = [1, 1, 1]; // Equal weights
      
      const weightedResult = StatisticalForecaster.weightedMovingAverage(data, weights, 1);
      const simpleResult = StatisticalForecaster.simpleMovingAverage(data, 3, 1);

      expect(weightedResult.predictions[0]).toBeCloseTo(simpleResult.predictions[0], 1);
    });

    it('should require sufficient data', () => {
      const data = [1, 2];
      const weights = [0.3, 0.7];
      
      expect(() => {
        StatisticalForecaster.weightedMovingAverage(data, [0.2, 0.3, 0.5], 1);
      }).toThrow('Insufficient data');
    });
  });

  describe('crossValidate', () => {
    it('should perform cross-validation', () => {
      const data = generateStableData(20, 10, 1);
      
      const modelFunction = (trainData: number[]) => {
        const { predictions } = StatisticalForecaster.simpleMovingAverage(trainData, 5, 1);
        return predictions;
      };

      const accuracy = StatisticalForecaster.crossValidate(data, modelFunction, 4);

      expect(accuracy.mae).toBeGreaterThan(0);
      expect(accuracy.mse).toBeGreaterThan(0);
      expect(accuracy.rmse).toBeGreaterThan(0);
      expect(accuracy.mape).toBeGreaterThan(0);
    });

    it('should handle insufficient data for cross-validation', () => {
      const data = [1, 2, 3];
      const modelFunction = (trainData: number[]) => [trainData[trainData.length - 1]];

      expect(() => {
        StatisticalForecaster.crossValidate(data, modelFunction, 5);
      }).toThrow('Insufficient data');
    });

    it('should handle model failures gracefully', () => {
      const data = generateStableData(20, 10, 1);
      
      const failingModelFunction = (trainData: number[]) => {
        throw new Error('Model failed');
      };

      const accuracy = StatisticalForecaster.crossValidate(data, failingModelFunction, 3);

      expect(accuracy.mae).toBe(Infinity);
      expect(accuracy.mse).toBe(Infinity);
      expect(accuracy.rmse).toBe(Infinity);
      expect(accuracy.mape).toBe(Infinity);
    });
  });

  describe('selectBestModel', () => {
    it('should select best model for stable data', () => {
      const data = generateStableData(30, 10, 1);
      const result = StatisticalForecaster.selectBestModel(data, 5);

      expect(result.modelName).toBeTruthy();
      expect(result.predictions).toHaveLength(5);
      expect(result.accuracy.mape).toBeGreaterThan(0);
      expect(result.accuracy.mape).toBeLessThan(1000); // Should be reasonable
    });

    it('should select best model for trending data', () => {
      const data = generateTrendingData(30, 5, 0.2);
      const result = StatisticalForecaster.selectBestModel(data, 3);

      expect(result.modelName).toBeTruthy();
      expect(result.predictions).toHaveLength(3);
      expect(result.accuracy.mape).toBeGreaterThan(0);
    });

    it('should handle small datasets', () => {
      const data = [1, 2, 3, 4, 5];
      const result = StatisticalForecaster.selectBestModel(data, 2);

      expect(result.modelName).toBeTruthy();
      expect(result.predictions).toHaveLength(2);
    });

    it('should return valid results even with poor data', () => {
      const data = [0, 1000, 0, 1000, 0]; // Very volatile data
      const result = StatisticalForecaster.selectBestModel(data, 1);

      expect(result.modelName).toBeTruthy();
      expect(result.predictions).toHaveLength(1);
      expect(result.predictions[0]).toBeGreaterThanOrEqual(0);
    });
  });

  describe('accuracy calculations', () => {
    it('should calculate accuracy metrics correctly', () => {
      const actual = [10, 12, 8, 15, 11];
      const predicted = [9, 13, 7, 14, 12];

      // Use a simple model to test accuracy calculation
      const data = [5, 6, 7, 8, 9, 10, 12, 8, 15, 11];
      const { accuracy } = StatisticalForecaster.simpleMovingAverage(data, 3, 1);

      expect(accuracy.mae).toBeGreaterThan(0);
      expect(accuracy.mse).toBeGreaterThan(0);
      expect(accuracy.rmse).toBeGreaterThan(0);
      expect(accuracy.mape).toBeGreaterThan(0);
      expect(accuracy.rmse).toBeCloseTo(Math.sqrt(accuracy.mse), 2);
    });

    it('should handle perfect predictions', () => {
      const data = [5, 5, 5, 5, 5, 5]; // Constant data
      const { accuracy } = StatisticalForecaster.simpleMovingAverage(data, 3, 1);

      // With constant data, moving average should be very accurate
      expect(accuracy.mae).toBeLessThan(1);
      expect(accuracy.mse).toBeLessThan(1);
    });
  });

  describe('edge cases and robustness', () => {
    it('should handle zero values', () => {
      const data = [0, 0, 1, 0, 2, 0, 1];
      const { predictions } = StatisticalForecaster.simpleMovingAverage(data, 3, 2);

      expect(predictions).toHaveLength(2);
      predictions.forEach(pred => {
        expect(pred).toBeGreaterThanOrEqual(0);
      });
    });

    it('should handle negative values', () => {
      const data = [-5, -3, -1, 1, 3, 5];
      const { predictions } = StatisticalForecaster.linearRegression(data, 2);

      expect(predictions).toHaveLength(2);
      // Should continue the positive trend
      expect(predictions[0]).toBeGreaterThan(5);
      expect(predictions[1]).toBeGreaterThan(predictions[0]);
    });

    it('should handle very large numbers', () => {
      const data = [1000000, 1000001, 1000002, 1000003, 1000004];
      const { predictions } = StatisticalForecaster.linearRegression(data, 1);

      expect(predictions).toHaveLength(1);
      expect(predictions[0]).toBeCloseTo(1000005, 0);
    });

    it('should handle very small numbers', () => {
      const data = [0.001, 0.002, 0.003, 0.004, 0.005];
      const { predictions } = StatisticalForecaster.linearRegression(data, 1);

      expect(predictions).toHaveLength(1);
      expect(predictions[0]).toBeCloseTo(0.006, 3);
    });

    it('should handle NaN and Infinity gracefully', () => {
      const data = [1, 2, 3, 4, 5];
      
      // Test with extreme alpha that might cause issues
      const { predictions } = StatisticalForecaster.exponentialSmoothing(data, 1.0, 1);
      
      expect(predictions).toHaveLength(1);
      expect(isFinite(predictions[0])).toBe(true);
      expect(isNaN(predictions[0])).toBe(false);
    });
  });
});