/**
 * Run RAG Database Migration
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

const dbPath = path.join(__dirname, '../database/rag_knowledge.db');
const migrationPath = path.join(__dirname, '../database/migrate-rag-schema.sql');

console.log('🚀 Running RAG database migration...');

try {
  const db = new Database(dbPath);
  const migration = fs.readFileSync(migrationPath, 'utf-8');
  
  // Split by semicolon and execute each statement
  const statements = migration.split(';').filter(stmt => stmt.trim().length > 0);
  
  for (const statement of statements) {
    try {
      db.exec(statement.trim());
      console.log('✅ Executed:', statement.trim().split('\n')[0]);
    } catch (error) {
      if (error.message.includes('duplicate column name')) {
        console.log('⏭️  Column already exists:', statement.trim().split('\n')[0]);
      } else {
        console.error('❌ Error executing statement:', statement.trim().split('\n')[0]);
        console.error('   Error:', error.message);
      }
    }
  }
  
  db.close();
  console.log('✅ Migration completed successfully');
} catch (error) {
  console.error('❌ Migration failed:', error.message);
  process.exit(1);
}