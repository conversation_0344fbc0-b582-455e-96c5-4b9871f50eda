#!/usr/bin/env node

import { validateAIModuleDeployment } from '../src/modules/ai/deployment/AIModuleDeploymentValidator';
import { aiModuleHealthCheck } from '../src/modules/ai/monitoring/AIModuleHealthCheck';

/**
 * AI Module Deployment Validation Script
 * 
 * This script validates that the AI module is properly deployed and integrated
 * with the existing application. It can be run as part of the build process
 * or CI/CD pipeline to ensure deployment quality.
 */

interface ValidationOptions {
  verbose?: boolean;
  exitOnFailure?: boolean;
  includeHealthCheck?: boolean;
  outputFormat?: 'console' | 'json';
}

class DeploymentValidator {
  private options: ValidationOptions;

  constructor(options: ValidationOptions = {}) {
    this.options = {
      verbose: false,
      exitOnFailure: true,
      includeHealthCheck: true,
      outputFormat: 'console',
      ...options
    };
  }

  /**
   * Run complete deployment validation
   */
  async validate(): Promise<void> {
    const startTime = Date.now();
    
    try {
      if (this.options.verbose) {
        console.log('🔍 Starting AI Module deployment validation...');
        console.log('=' .repeat(60));
      }

      // Step 1: Run deployment validation
      const validationReport = await this.runDeploymentValidation();
      
      // Step 2: Run health check if requested
      let healthStatus = null;
      if (this.options.includeHealthCheck) {
        healthStatus = await this.runHealthCheck();
      }

      // Step 3: Generate final report
      const finalReport = this.generateFinalReport(validationReport, healthStatus);
      
      // Step 4: Output results
      this.outputResults(finalReport);
      
      // Step 5: Handle exit conditions
      const duration = Date.now() - startTime;
      
      if (finalReport.success) {
        if (this.options.verbose) {
          console.log('=' .repeat(60));
          console.log(`✅ AI Module deployment validation completed successfully in ${duration}ms`);
        }
        process.exit(0);
      } else {
        if (this.options.verbose) {
          console.log('=' .repeat(60));
          console.log(`❌ AI Module deployment validation failed in ${duration}ms`);
        }
        
        if (this.options.exitOnFailure) {
          process.exit(1);
        }
      }
    } catch (error) {
      console.error('❌ Deployment validation failed with error:', error);
      
      if (this.options.exitOnFailure) {
        process.exit(1);
      }
    }
  }

  /**
   * Run deployment validation
   */
  private async runDeploymentValidation(): Promise<any> {
    if (this.options.verbose) {
      console.log('📋 Running deployment validation...');
    }
    
    const validationReport = await validateAIModuleDeployment();
    
    if (this.options.verbose) {
      console.log(`   Status: ${validationReport.overallStatus}`);
      console.log(`   Success Rate: ${validationReport.summary.successRate}%`);
      console.log(`   Tests: ${validationReport.summary.passed}/${validationReport.summary.total}`);
    }
    
    return validationReport;
  }

  /**
   * Run health check
   */
  private async runHealthCheck(): Promise<any> {
    if (this.options.verbose) {
      console.log('🏥 Running health check...');
    }
    
    const healthStatus = await aiModuleHealthCheck.performHealthCheck();
    
    if (this.options.verbose) {
      console.log(`   Health Status: ${healthStatus.overallStatus}`);
      console.log(`   Issues: ${healthStatus.issues.length}`);
    }
    
    return healthStatus;
  }

  /**
   * Generate final report
   */
  private generateFinalReport(validationReport: any, healthStatus: any): any {
    const success = validationReport.overallStatus === 'PASSED' && 
                   (!healthStatus || healthStatus.overallStatus !== 'CRITICAL');
    
    return {
      timestamp: new Date().toISOString(),
      success,
      validation: validationReport,
      health: healthStatus,
      summary: {
        validationPassed: validationReport.overallStatus === 'PASSED',
        healthCheckPassed: !healthStatus || healthStatus.overallStatus !== 'CRITICAL',
        totalIssues: validationReport.results.filter((r: any) => !r.success).length + 
                    (healthStatus ? healthStatus.issues.length : 0),
        recommendations: [
          ...validationReport.recommendations,
          ...(healthStatus ? healthStatus.recommendations : [])
        ]
      }
    };
  }

  /**
   * Output results
   */
  private outputResults(report: any): void {
    if (this.options.outputFormat === 'json') {
      console.log(JSON.stringify(report, null, 2));
      return;
    }

    // Console output
    if (this.options.verbose) {
      console.log('\n📊 Deployment Validation Report:');
      console.log(`Overall Success: ${report.success ? '✅' : '❌'}`);
      console.log(`Validation: ${report.summary.validationPassed ? '✅' : '❌'}`);
      console.log(`Health Check: ${report.summary.healthCheckPassed ? '✅' : '❌'}`);
      console.log(`Total Issues: ${report.summary.totalIssues}`);
      
      if (report.summary.recommendations.length > 0) {
        console.log('\n💡 Recommendations:');
        report.summary.recommendations.forEach((rec: string, index: number) => {
          console.log(`   ${index + 1}. ${rec}`);
        });
      }
    } else {
      // Minimal output
      const status = report.success ? 'PASSED' : 'FAILED';
      console.log(`AI Module Deployment Validation: ${status}`);
      
      if (!report.success) {
        console.log(`Issues found: ${report.summary.totalIssues}`);
      }
    }
  }
}

/**
 * Parse command line arguments
 */
function parseArguments(): ValidationOptions {
  const args = process.argv.slice(2);
  const options: ValidationOptions = {};

  args.forEach(arg => {
    switch (arg) {
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--no-exit':
        options.exitOnFailure = false;
        break;
      case '--no-health':
        options.includeHealthCheck = false;
        break;
      case '--json':
        options.outputFormat = 'json';
        break;
    }
  });

  return options;
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  const options = parseArguments();
  const validator = new DeploymentValidator(options);
  await validator.validate();
}

// Run if this script is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('Validation script failed:', error);
    process.exit(1);
  });
}

export { DeploymentValidator };