import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AICacheService } from '../caching/AICacheService';
import { AIPerformanceMonitor } from '../performance/AIPerformanceMonitor';
import { ResourceTracker } from '../performance/ResourceTracker';
import { AIPerformanceOptimizer } from '../performance/AIPerformanceOptimizer';
import { CacheService } from '@/services/cache.service';

// Mock dependencies
vi.mock('@/services/cache.service');

describe('AI Performance Benchmarks', () => {
  let cacheService: AICacheService;
  let performanceMonitor: AIPerformanceMonitor;
  let resourceTracker: ResourceTracker;
  let performanceOptimizer: AIPerformanceOptimizer;
  let mockCacheService: vi.Mocked<CacheService>;

  beforeEach(() => {
    mockCacheService = {
      get: vi.fn(),
      set: vi.fn(),
      delete: vi.fn(),
      clear: vi.fn(),
      has: vi.fn(),
      keys: vi.fn(),
      size: vi.fn()
    } as any;

    cacheService = new AICacheService(mockCacheService);
    performanceMonitor = new AIPerformanceMonitor();
    resourceTracker = new ResourceTracker();
    performanceOptimizer = new AIPerformanceOptimizer(
      performanceMonitor,
      resourceTracker,
      cacheService
    );
  });

  describe('Cache Performance Benchmarks', () => {
    it('should handle high-volume cache operations efficiently', async () => {
      const startTime = Date.now();
      const operationCount = 1000;

      // Benchmark cache set operations
      const setPromises = [];
      for (let i = 0; i < operationCount; i++) {
        setPromises.push(cacheService.set(`key-${i}`, `value-${i}`));
      }
      await Promise.all(setPromises);

      const setEndTime = Date.now();
      const setDuration = setEndTime - startTime;

      // Benchmark cache get operations
      const getStartTime = Date.now();
      const getPromises = [];
      for (let i = 0; i < operationCount; i++) {
        getPromises.push(cacheService.get(`key-${i}`));
      }
      await Promise.all(getPromises);

      const getEndTime = Date.now();
      const getDuration = getEndTime - getStartTime;

      // Performance assertions
      expect(setDuration).toBeLessThan(5000); // Should complete within 5 seconds
      expect(getDuration).toBeLessThan(2000); // Gets should be faster than sets
      
      const setThroughput = operationCount / (setDuration / 1000);
      const getThroughput = operationCount / (getDuration / 1000);
      
      expect(setThroughput).toBeGreaterThan(200); // At least 200 ops/sec
      expect(getThroughput).toBeGreaterThan(500); // At least 500 ops/sec

      console.log(`Cache Set Throughput: ${setThroughput.toFixed(2)} ops/sec`);
      console.log(`Cache Get Throughput: ${getThroughput.toFixed(2)} ops/sec`);
    });

    it('should maintain performance with large cache sizes', async () => {
      const largeDataSize = 10000; // 10KB
      const largeValue = 'x'.repeat(largeDataSize);
      const entryCount = 100;

      const startTime = Date.now();

      // Fill cache with large entries
      for (let i = 0; i < entryCount; i++) {
        await cacheService.set(`large-key-${i}`, largeValue);
      }

      // Test retrieval performance
      const retrievalStartTime = Date.now();
      for (let i = 0; i < entryCount; i++) {
        await cacheService.get(`large-key-${i}`);
      }
      const retrievalEndTime = Date.now();

      const totalDuration = retrievalEndTime - startTime;
      const retrievalDuration = retrievalEndTime - retrievalStartTime;

      expect(totalDuration).toBeLessThan(10000); // Should complete within 10 seconds
      expect(retrievalDuration).toBeLessThan(5000); // Retrieval should be fast

      const avgRetrievalTime = retrievalDuration / entryCount;
      expect(avgRetrievalTime).toBeLessThan(50); // Less than 50ms per retrieval

      console.log(`Average retrieval time for large entries: ${avgRetrievalTime.toFixed(2)}ms`);
    });

    it('should handle concurrent cache operations efficiently', async () => {
      const concurrentOperations = 50;
      const operationsPerWorker = 20;

      const startTime = Date.now();

      // Create concurrent workers
      const workers = [];
      for (let worker = 0; worker < concurrentOperations; worker++) {
        const workerPromise = (async () => {
          for (let op = 0; op < operationsPerWorker; op++) {
            const key = `worker-${worker}-op-${op}`;
            await cacheService.set(key, `value-${worker}-${op}`);
            await cacheService.get(key);
          }
        })();
        workers.push(workerPromise);
      }

      await Promise.all(workers);

      const endTime = Date.now();
      const duration = endTime - startTime;
      const totalOperations = concurrentOperations * operationsPerWorker * 2; // set + get

      expect(duration).toBeLessThan(15000); // Should complete within 15 seconds

      const throughput = totalOperations / (duration / 1000);
      expect(throughput).toBeGreaterThan(100); // At least 100 ops/sec under concurrency

      console.log(`Concurrent operations throughput: ${throughput.toFixed(2)} ops/sec`);
    });
  });

  describe('Performance Monitor Benchmarks', () => {
    it('should handle high-frequency operation tracking', () => {
      const operationCount = 10000;
      const startTime = Date.now();

      // Simulate high-frequency operations
      for (let i = 0; i < operationCount; i++) {
        const operationId = `op-${i}`;
        performanceMonitor.startOperation(operationId, 'benchmarkOperation');
        performanceMonitor.endOperation(operationId, true);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds

      const throughput = operationCount / (duration / 1000);
      expect(throughput).toBeGreaterThan(2000); // At least 2000 ops/sec

      // Verify statistics calculation performance
      const statsStartTime = Date.now();
      const stats = performanceMonitor.getOperationStats('benchmarkOperation');
      const statsEndTime = Date.now();
      const statsDuration = statsEndTime - statsStartTime;

      expect(statsDuration).toBeLessThan(100); // Stats calculation should be fast
      expect(stats.totalCalls).toBe(operationCount);

      console.log(`Operation tracking throughput: ${throughput.toFixed(2)} ops/sec`);
      console.log(`Stats calculation time: ${statsDuration}ms`);
    });

    it('should maintain performance with large operation history', () => {
      // Fill with historical data
      const historySize = 50000;
      for (let i = 0; i < historySize; i++) {
        performanceMonitor.recordOperation(
          `operation-${i % 10}`, // 10 different operation types
          Math.random() * 1000,
          Math.random() > 0.1 // 90% success rate
        );
      }

      // Benchmark statistics retrieval
      const startTime = Date.now();
      const allStats = performanceMonitor.getAllOperationStats();
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(1000); // Should complete within 1 second
      expect(allStats.length).toBe(10); // Should have 10 operation types

      // Benchmark individual operation stats
      const individualStartTime = Date.now();
      const individualStats = performanceMonitor.getOperationStats('operation-1');
      const individualEndTime = Date.now();
      const individualDuration = individualEndTime - individualStartTime;

      expect(individualDuration).toBeLessThan(100); // Should be very fast
      expect(individualStats.totalCalls).toBeGreaterThan(0);

      console.log(`All stats retrieval time: ${duration}ms`);
      console.log(`Individual stats retrieval time: ${individualDuration}ms`);
    });
  });

  describe('Resource Tracker Benchmarks', () => {
    it('should collect metrics efficiently', () => {
      const collectionCount = 1000;
      const startTime = Date.now();

      // Benchmark metric collection
      for (let i = 0; i < collectionCount; i++) {
        resourceTracker.getCurrentMetrics();
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds

      const throughput = collectionCount / (duration / 1000);
      expect(throughput).toBeGreaterThan(200); // At least 200 collections/sec

      console.log(`Metrics collection throughput: ${throughput.toFixed(2)} collections/sec`);
    });

    it('should analyze trends efficiently with large datasets', () => {
      // Generate large dataset
      const dataPoints = 10000;
      const mockMetrics = [];
      
      for (let i = 0; i < dataPoints; i++) {
        mockMetrics.push({
          timestamp: Date.now() - (dataPoints - i) * 1000,
          memoryUsage: { heapUsed: 100 * 1024 * 1024 + i * 1000 },
          cpuUsage: { percent: 20 + (i % 50) },
          networkUsage: { requestsPerSecond: 10 + (i % 20) }
        });
      }

      // Inject mock data
      (resourceTracker as any).metrics = mockMetrics;

      // Benchmark trend analysis
      const startTime = Date.now();
      const trends = resourceTracker.getTrends(3600000);
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(1000); // Should complete within 1 second
      expect(trends).toHaveProperty('memory');
      expect(trends).toHaveProperty('cpu');
      expect(trends).toHaveProperty('network');

      console.log(`Trend analysis time for ${dataPoints} data points: ${duration}ms`);
    });

    it('should generate recommendations efficiently', () => {
      const startTime = Date.now();
      const recommendations = resourceTracker.getOptimizationRecommendations();
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(500); // Should complete within 500ms
      expect(Array.isArray(recommendations)).toBe(true);

      console.log(`Recommendation generation time: ${duration}ms`);
    });
  });

  describe('Performance Optimizer Benchmarks', () => {
    it('should analyze performance efficiently', async () => {
      // Add some performance data
      for (let i = 0; i < 1000; i++) {
        performanceMonitor.recordOperation(
          `testOp${i % 5}`,
          Math.random() * 2000,
          Math.random() > 0.05
        );
      }

      const startTime = Date.now();
      const analysis = await performanceOptimizer.analyzePerformance();
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(2000); // Should complete within 2 seconds
      expect(analysis).toHaveProperty('recommendations');
      expect(analysis).toHaveProperty('currentMetrics');
      expect(analysis).toHaveProperty('trends');

      console.log(`Performance analysis time: ${duration}ms`);
    });

    it('should handle strategy application efficiently', async () => {
      const strategies = performanceOptimizer.getStrategies();
      expect(strategies.length).toBeGreaterThan(0);

      const strategy = strategies[0];
      const startTime = Date.now();
      
      try {
        await performanceOptimizer.applyOptimization(strategy.id);
      } catch (error) {
        // Expected for mock implementation
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds

      console.log(`Strategy application time: ${duration}ms`);
    });
  });

  describe('Memory Usage Benchmarks', () => {
    it('should maintain reasonable memory usage under load', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform intensive operations
      const operations = 5000;
      for (let i = 0; i < operations; i++) {
        await cacheService.set(`memory-test-${i}`, `value-${i}`);
        performanceMonitor.recordOperation(`memoryOp${i % 10}`, Math.random() * 1000, true);
        
        if (i % 100 === 0) {
          resourceTracker.getCurrentMetrics();
        }
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      const memoryIncreasePerOp = memoryIncrease / operations;

      // Memory increase should be reasonable
      expect(memoryIncreasePerOp).toBeLessThan(1000); // Less than 1KB per operation

      console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
      console.log(`Memory per operation: ${memoryIncreasePerOp.toFixed(2)} bytes`);

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
    });
  });

  describe('Scalability Benchmarks', () => {
    it('should scale linearly with data size', async () => {
      const dataSizes = [100, 500, 1000, 2000];
      const results = [];

      for (const size of dataSizes) {
        const startTime = Date.now();

        // Perform operations proportional to data size
        for (let i = 0; i < size; i++) {
          await cacheService.set(`scale-test-${size}-${i}`, `value-${i}`);
          performanceMonitor.recordOperation(`scaleOp-${size}`, Math.random() * 1000, true);
        }

        const endTime = Date.now();
        const duration = endTime - startTime;
        const throughput = size / (duration / 1000);

        results.push({ size, duration, throughput });

        console.log(`Size: ${size}, Duration: ${duration}ms, Throughput: ${throughput.toFixed(2)} ops/sec`);
      }

      // Check that performance doesn't degrade significantly with size
      const firstThroughput = results[0].throughput;
      const lastThroughput = results[results.length - 1].throughput;
      const degradation = (firstThroughput - lastThroughput) / firstThroughput;

      expect(degradation).toBeLessThan(0.5); // Less than 50% degradation

      console.log(`Performance degradation: ${(degradation * 100).toFixed(2)}%`);
    });
  });
});