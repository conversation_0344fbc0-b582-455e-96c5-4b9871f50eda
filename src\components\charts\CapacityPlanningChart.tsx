/**
 * Capacity Planning Chart Component
 * 
 * Displays capacity forecasts, utilization trends, and resource allocation recommendations.
 */

import React, { useState } from 'react';
import { Line, LineChart, Bar, BarChart, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Area, AreaChart, ComposedChart } from 'recharts';
import { TrendingUp, AlertTriangle, Users, Zap, Package, Settings } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import type { CapacityForecast } from '@/types/predictive-analytics';
import { cn } from '@/lib/utils';

interface CapacityPlanningChartProps {
  forecast: CapacityForecast;
  className?: string;
}

/**
 * Capacity Planning Chart Component
 */
export function CapacityPlanningChart({ forecast, className }: CapacityPlanningChartProps) {
  const [selectedView, setSelectedView] = useState<'forecast' | 'gaps' | 'recommendations'>('forecast');

  // Prepare forecast chart data
  const forecastChartData = forecast.predicted_demand.map((point, index) => ({
    timestamp: point.timestamp,
    demand: point.predicted_value,
    capacity: forecast.current_capacity,
    utilization: (point.predicted_value / forecast.current_capacity) * 100,
    confidence_lower: point.confidence_interval_lower,
    confidence_upper: point.confidence_interval_upper,
    gap: Math.max(0, point.predicted_value - forecast.current_capacity)
  }));

  // Prepare capacity gaps data
  const gapsData = forecast.capacity_gaps.map(gap => ({
    start_time: gap.start_time,
    end_time: gap.end_time,
    required: gap.required_capacity,
    available: gap.available_capacity,
    gap_percentage: gap.gap_percentage,
    impact: gap.impact_severity
  }));

  // Format timestamp for display
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('de-DE', {
      month: 'short',
      day: '2-digit',
      hour: '2-digit'
    });
  };

  // Get resource type icon
  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'personnel':
        return <Users className="h-4 w-4" />;
      case 'equipment':
        return <Settings className="h-4 w-4" />;
      case 'storage':
        return <Package className="h-4 w-4" />;
      default:
        return <Zap className="h-4 w-4" />;
    }
  };

  // Get impact severity color
  const getImpactColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // Get recommendation priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500 text-white';
      case 'medium':
        return 'bg-yellow-500 text-black';
      case 'low':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  // Chart configuration
  const chartConfig = {
    demand: {
      label: 'Nachfrage',
      color: 'var(--chart-1)',
    },
    capacity: {
      label: 'Kapazität',
      color: 'var(--chart-2)',
    },
    utilization: {
      label: 'Auslastung (%)',
      color: 'var(--chart-3)',
    },
    gap: {
      label: 'Kapazitätslücke',
      color: 'var(--chart-4)',
    },
  };

  return (
    <Card className={cn("bg-secondary-background text-foreground", className)} data-testid="capacity-planning-chart">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">
            Kapazitätsplanung - {forecast.department_id}
          </CardTitle>
          <div className="flex items-center gap-2">
            {getResourceIcon(forecast.resource_type)}
            <Badge variant="outline">
              {Math.round(forecast.current_utilization * 100)}% Auslastung
            </Badge>
          </div>
        </div>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>Aktuelle Kapazität: <strong>{forecast.current_capacity}</strong></span>
          <span>Kapazitätslücken: <strong>{forecast.capacity_gaps.length}</strong></span>
          <span>Empfehlungen: <strong>{forecast.recommendations.length}</strong></span>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={selectedView} onValueChange={(value) => setSelectedView(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="forecast">Prognose</TabsTrigger>
            <TabsTrigger value="gaps">Kapazitätslücken</TabsTrigger>
            <TabsTrigger value="recommendations">Empfehlungen</TabsTrigger>
          </TabsList>

          {/* Forecast View */}
          <TabsContent value="forecast" className="space-y-4">
            <ChartContainer config={chartConfig} className="h-64">
              <ComposedChart
                data={forecastChartData}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 20,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis
                  dataKey="timestamp"
                  tickFormatter={formatTimestamp}
                  tick={{ fontSize: 12 }}
                  interval="preserveStartEnd"
                />
                <YAxis
                  yAxisId="left"
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => value.toFixed(0)}
                />
                <YAxis
                  yAxisId="right"
                  orientation="right"
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => `${value}%`}
                />
                
                {/* Capacity line */}
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="capacity"
                  stroke="var(--color-capacity)"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={false}
                />
                
                {/* Demand area */}
                <Area
                  yAxisId="left"
                  type="monotone"
                  dataKey="demand"
                  stroke="var(--color-demand)"
                  fill="var(--color-demand)"
                  fillOpacity={0.3}
                />
                
                {/* Utilization bars */}
                <Bar
                  yAxisId="right"
                  dataKey="utilization"
                  fill="var(--color-utilization)"
                  fillOpacity={0.6}
                />
                
                {/* Capacity gaps */}
                <Bar
                  yAxisId="left"
                  dataKey="gap"
                  fill="var(--color-gap)"
                  fillOpacity={0.8}
                />
                
                <ChartTooltip
                  content={({ active, payload, label }) => {
                    if (!active || !payload?.length) return null;
                    
                    const data = payload[0]?.payload;
                    if (!data) return null;
                    
                    return (
                      <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
                        <p className="font-medium mb-2">
                          {formatTimestamp(label as string)}
                        </p>
                        <div className="space-y-1 text-sm">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-chart-1"></div>
                            <span>Nachfrage: {data.demand.toFixed(0)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-chart-2"></div>
                            <span>Kapazität: {data.capacity.toFixed(0)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-chart-3"></div>
                            <span>Auslastung: {data.utilization.toFixed(1)}%</span>
                          </div>
                          {data.gap > 0 && (
                            <div className="flex items-center gap-2">
                              <div className="w-3 h-3 rounded-full bg-chart-4"></div>
                              <span>Lücke: {data.gap.toFixed(0)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  }}
                />
              </ComposedChart>
            </ChartContainer>
          </TabsContent>

          {/* Capacity Gaps View */}
          <TabsContent value="gaps" className="space-y-4">
            <div className="h-64 overflow-y-auto">
              {forecast.capacity_gaps.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <TrendingUp className="h-12 w-12 text-green-500 mx-auto mb-2" />
                    <p className="text-muted-foreground">
                      Keine Kapazitätslücken prognostiziert
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {forecast.capacity_gaps.map((gap, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-3 hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-orange-500" />
                          <span className="font-medium text-sm">
                            Kapazitätslücke #{index + 1}
                          </span>
                        </div>
                        <Badge className={getImpactColor(gap.impact_severity)}>
                          {gap.impact_severity === 'critical' ? 'Kritisch' :
                           gap.impact_severity === 'high' ? 'Hoch' :
                           gap.impact_severity === 'medium' ? 'Mittel' : 'Niedrig'}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Zeitraum:</span>
                          <p className="font-medium">
                            {formatTimestamp(gap.start_time)} - {formatTimestamp(gap.end_time)}
                          </p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Lücke:</span>
                          <p className="font-medium">
                            {gap.gap_percentage.toFixed(1)}% ({gap.required_capacity - gap.available_capacity} Einheiten)
                          </p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Benötigt:</span>
                          <p className="font-medium">{gap.required_capacity}</p>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Verfügbar:</span>
                          <p className="font-medium">{gap.available_capacity}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          {/* Recommendations View */}
          <TabsContent value="recommendations" className="space-y-4">
            <div className="h-64 overflow-y-auto">
              {forecast.recommendations.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <Settings className="h-12 w-12 text-blue-500 mx-auto mb-2" />
                    <p className="text-muted-foreground">
                      Keine Empfehlungen verfügbar
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {forecast.recommendations.map((recommendation, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-3 hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <TrendingUp className="h-4 w-4 text-blue-500" />
                          <span className="font-medium text-sm">
                            {recommendation.type === 'increase_capacity' ? 'Kapazität erhöhen' :
                             recommendation.type === 'redistribute_load' ? 'Last umverteilen' :
                             'Zeitplan anpassen'}
                          </span>
                        </div>
                        <Badge className={getPriorityColor(recommendation.priority)}>
                          {recommendation.priority === 'high' ? 'Hoch' :
                           recommendation.priority === 'medium' ? 'Mittel' : 'Niedrig'}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-3">
                        {recommendation.description}
                      </p>
                      
                      <div className="grid grid-cols-3 gap-4 text-xs">
                        {recommendation.estimated_cost && (
                          <div>
                            <span className="text-muted-foreground">Kosten:</span>
                            <p className="font-medium">€{recommendation.estimated_cost.toLocaleString()}</p>
                          </div>
                        )}
                        {recommendation.estimated_benefit && (
                          <div>
                            <span className="text-muted-foreground">Nutzen:</span>
                            <p className="font-medium">€{recommendation.estimated_benefit.toLocaleString()}</p>
                          </div>
                        )}
                        {recommendation.implementation_time_days && (
                          <div>
                            <span className="text-muted-foreground">Umsetzung:</span>
                            <p className="font-medium">{recommendation.implementation_time_days} Tage</p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
        
        {/* Summary Footer */}
        <div className="mt-4 p-3 bg-muted rounded-lg">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-4">
              <span>
                <strong>Aktuelle Auslastung:</strong> {Math.round(forecast.current_utilization * 100)}%
              </span>
              <span>
                <strong>Kapazität:</strong> {forecast.current_capacity} {forecast.resource_type}
              </span>
            </div>
            <span className="text-muted-foreground">
              Generiert: {new Date(forecast.forecast_generated_at).toLocaleString('de-DE')}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default CapacityPlanningChart;