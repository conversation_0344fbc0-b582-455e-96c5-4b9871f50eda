import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Activity, 
  RefreshCw,
  TrendingUp,
  Clock
} from "lucide-react";
import { WorkflowHealthResponse } from "@/types/workflow.types";

interface WorkflowHealthBannerProps {
  healthData: WorkflowHealthResponse;
  lastUpdated: Date;
  onRefresh: () => void;
  isLoading: boolean;
}

export function WorkflowHealthBanner({ 
  healthData, 
  lastUpdated, 
  onRefresh, 
  isLoading 
}: WorkflowHealthBannerProps) {
  const getHealthIcon = () => {
    switch (healthData.overallHealth) {
      case 'healthy':
        return <CheckCircle className="h-6 w-6 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-6 w-6 text-yellow-600" />;
      case 'critical':
        return <XCircle className="h-6 w-6 text-red-600" />;
      default:
        return <Activity className="h-6 w-6 text-gray-600" />;
    }
  };

  const getHealthColor = () => {
    switch (healthData.overallHealth) {
      case 'healthy':
        return 'bg-green-50 border-green-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'critical':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getHealthTextColor = () => {
    switch (healthData.overallHealth) {
      case 'healthy':
        return 'text-green-800';
      case 'warning':
        return 'text-yellow-800';
      case 'critical':
        return 'text-red-800';
      default:
        return 'text-gray-800';
    }
  };

  const getHealthLabel = () => {
    switch (healthData.overallHealth) {
      case 'healthy':
        return 'Alle Systeme funktionieren';
      case 'warning':
        return 'Einige Workflows haben Probleme';
      case 'critical':
        return 'Kritische Workflow-Fehler';
      default:
        return 'Status unbekannt';
    }
  };

  const successRate = healthData.totalWorkflows > 0 
    ? ((healthData.totalWorkflows - healthData.failedWorkflows) / healthData.totalWorkflows * 100)
    : 100;

  return (
    <Card className={`border-2 ${getHealthColor()}`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Health Status */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              {getHealthIcon()}
              <div>
                <div className={`font-bold text-lg ${getHealthTextColor()}`}>
                  {getHealthLabel()}
                </div>
                <div className="text-sm text-gray-600">
                  System-Status: {healthData.overallHealth.toUpperCase()}
                </div>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="flex items-center gap-6">
            {/* Total Workflows */}
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {healthData.totalWorkflows}
              </div>
              <div className="text-xs text-gray-500">
                Workflows
              </div>
            </div>

            {/* Active Workflows */}
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {healthData.activeWorkflows}
              </div>
              <div className="text-xs text-gray-500">
                Aktiv
              </div>
            </div>

            {/* Running Workflows */}
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 flex items-center gap-1">
                {healthData.runningWorkflows > 0 && (
                  <div className="animate-pulse">
                    <Activity className="h-5 w-5" />
                  </div>
                )}
                {healthData.runningWorkflows}
              </div>
              <div className="text-xs text-gray-500">
                Laufend
              </div>
            </div>

            {/* Failed Workflows */}
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {healthData.failedWorkflows}
              </div>
              <div className="text-xs text-gray-500">
                Fehler
              </div>
            </div>

            {/* Success Rate */}
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {successRate.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">
                Erfolg
              </div>
            </div>
          </div>

          {/* Refresh Controls */}
          <div className="flex items-center gap-4">
            <div className="text-right text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {lastUpdated.toLocaleTimeString('de-DE')}
              </div>
              <div className="text-xs">
                Letztes Update
              </div>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Lädt...' : 'Aktualisieren'}
            </Button>
          </div>
        </div>

        {/* Additional Status Information */}
        {(healthData.failedWorkflows > 0 || healthData.runningWorkflows > 0) && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex flex-wrap gap-2">
              {healthData.runningWorkflows > 0 && (
                <Badge className="bg-orange-100 text-orange-800 border-orange-200">
                  <Activity className="h-3 w-3 mr-1 animate-pulse" />
                  {healthData.runningWorkflows} Workflow{healthData.runningWorkflows > 1 ? 's' : ''} läuft gerade
                </Badge>
              )}
              
              {healthData.failedWorkflows > 0 && (
                <Badge className="bg-red-100 text-red-800 border-red-200">
                  <XCircle className="h-3 w-3 mr-1" />
                  {healthData.failedWorkflows} fehlgeschlagen{healthData.failedWorkflows > 1 ? 'e' : 'er'} Workflow{healthData.failedWorkflows > 1 ? 's' : ''}
                </Badge>
              )}
              
              {healthData.overallHealth === 'healthy' && healthData.activeWorkflows > 0 && (
                <Badge className="bg-green-100 text-green-800 border-green-200">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Alle aktiven Workflows funktionieren einwandfrei
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}