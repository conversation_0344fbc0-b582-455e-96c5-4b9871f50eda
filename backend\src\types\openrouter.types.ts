/**
 * OpenRouter Service Types
 * 
 * Type definitions for OpenRouter API integration
 */

import { EnrichedContext } from './data-enrichment.types';

// Interface für die Chat-Anfrage
export interface ChatRequest {
  message: string;
  includeInsights?: boolean;
  includeAnomalies?: boolean;
  enrichedContext?: EnrichedContext;
}

// Interface für die OpenRouter-Anfrage
export interface OpenRouterRequest {
  model: string;
  messages: Array<{
    role: string;
    content: string;
  }>;
}

// Interface für die OpenRouter-Antwort
export interface OpenRouterResponse {
  id: string;
  choices: Array<{
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
}

// Interface für die Service-Antwort
export interface ChatResponse {
  response: string;
  timestamp: string;
  model: string;
  hasInsights: boolean;
  hasAnomalies: boolean;
  hasEnrichedContext: boolean;
  dataTypes: string[];
}