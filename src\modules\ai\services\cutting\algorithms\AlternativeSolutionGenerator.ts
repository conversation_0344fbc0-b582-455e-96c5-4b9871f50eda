/**
 * Alternative Solution Generation and Ranking
 * 
 * Generates multiple cutting solutions using different algorithms and strategies,
 * then ranks them based on multiple criteria for comparison and selection
 */

import {
  CuttingRequest,
  CuttingPlan,
  CuttingAlternative,
  Cut
} from '../../../types/cutting';
import { GeneticAlgorithm, GeneticAlgorithmConfig } from './GeneticAlgorithm';
import { MultiObjectiveOptimizer, MultiObjectiveConfig } from './MultiObjectiveOptimizer';

export interface AlternativeGenerationConfig {
  maxAlternatives: number;
  includeBasicAlgorithms: boolean;
  includeAdvancedAlgorithms: boolean;
  includeHeuristicVariations: boolean;
  timeLimit: number; // milliseconds
  diversityThreshold: number;
  qualityThreshold: number;
}

export interface SolutionStrategy {
  name: string;
  description: string;
  algorithm: 'first-fit' | 'best-fit' | 'worst-fit' | 'genetic' | 'multi-objective' | 'hybrid';
  parameters: any;
  expectedStrengths: string[];
  expectedWeaknesses: string[];
}

export interface RankedAlternative extends CuttingAlternative {
  strategy: SolutionStrategy;
  ranking: number;
  scores: {
    efficiency: number;
    wasteMinimization: number;
    timeOptimization: number;
    complexity: number;
    robustness: number;
    overall: number;
  };
  tradeoffs: {
    versus: string;
    gains: string[];
    losses: string[];
  }[];
  suitability: {
    scenario: string;
    score: number;
    reasoning: string;
  }[];
}

export interface AlternativeGenerationResult {
  alternatives: RankedAlternative[];
  generationTime: number;
  strategiesUsed: SolutionStrategy[];
  diversityMetrics: {
    averageDistance: number;
    minDistance: number;
    maxDistance: number;
    diversityIndex: number;
  };
  recommendedAlternative: RankedAlternative;
  scenarioRecommendations: {
    scenario: string;
    recommendedAlternative: RankedAlternative;
    reasoning: string;
  }[];
}

/**
 * Alternative solution generator and ranker
 */
export class AlternativeSolutionGenerator {
  private config: AlternativeGenerationConfig;
  private strategies: SolutionStrategy[];

  constructor(config: Partial<AlternativeGenerationConfig> = {}) {
    this.config = {
      maxAlternatives: 8,
      includeBasicAlgorithms: true,
      includeAdvancedAlgorithms: true,
      includeHeuristicVariations: true,
      timeLimit: 60000, // 1 minute
      diversityThreshold: 0.1,
      qualityThreshold: 0.6,
      ...config
    };

    this.strategies = this.initializeStrategies();
  }

  /**
   * Generate and rank alternative cutting solutions
   */
  async generateAlternatives(request: CuttingRequest): Promise<AlternativeGenerationResult> {
    const startTime = Date.now();
    const alternatives: RankedAlternative[] = [];
    const strategiesUsed: SolutionStrategy[] = [];

    // Generate solutions using different strategies
    for (const strategy of this.strategies) {
      if (Date.now() - startTime > this.config.timeLimit) {
        break; // Time limit reached
      }

      try {
        const plan = await this.generateSolutionWithStrategy(request, strategy);

        if (this.isQualitySufficient(plan)) {
          const alternative = await this.createRankedAlternative(plan, strategy, request);

          // Check diversity
          if (this.isDiverseEnough(alternative, alternatives)) {
            alternatives.push(alternative);
            strategiesUsed.push(strategy);
          }
        }
      } catch (error) {
        console.warn(`Strategy ${strategy.name} failed:`, error);
      }

      if (alternatives.length >= this.config.maxAlternatives) {
        break;
      }
    }

    // Rank alternatives
    const rankedAlternatives = this.rankAlternatives(alternatives);

    // Calculate diversity metrics
    const diversityMetrics = this.calculateDiversityMetrics(rankedAlternatives);

    // Find recommended alternative
    const recommendedAlternative = this.selectRecommendedAlternative(rankedAlternatives);

    // Generate scenario-based recommendations
    const scenarioRecommendations = this.generateScenarioRecommendations(rankedAlternatives);

    const generationTime = Date.now() - startTime;

    return {
      alternatives: rankedAlternatives,
      generationTime,
      strategiesUsed,
      diversityMetrics,
      recommendedAlternative,
      scenarioRecommendations
    };
  }

  /**
   * Initialize solution strategies
   */
  private initializeStrategies(): SolutionStrategy[] {
    const strategies: SolutionStrategy[] = [];

    if (this.config.includeBasicAlgorithms) {
      // First-Fit strategies
      strategies.push({
        name: 'First-Fit Standard',
        description: 'Standard first-fit bin packing algorithm',
        algorithm: 'first-fit',
        parameters: { sortOrder: 'none' },
        expectedStrengths: ['Fast execution', 'Simple implementation', 'Predictable results'],
        expectedWeaknesses: ['May not be optimal', 'Higher waste potential']
      });

      strategies.push({
        name: 'First-Fit Decreasing',
        description: 'First-fit with orders sorted by length (descending)',
        algorithm: 'first-fit',
        parameters: { sortOrder: 'decreasing' },
        expectedStrengths: ['Better packing than standard first-fit', 'Still fast'],
        expectedWeaknesses: ['May ignore priorities', 'Less flexible']
      });

      // Best-Fit strategies
      strategies.push({
        name: 'Best-Fit Standard',
        description: 'Standard best-fit bin packing algorithm',
        algorithm: 'best-fit',
        parameters: { tieBreaking: 'first' },
        expectedStrengths: ['Better space utilization', 'Reduced waste'],
        expectedWeaknesses: ['Slower than first-fit', 'May create small unusable spaces']
      });

      strategies.push({
        name: 'Best-Fit with Priority',
        description: 'Best-fit considering order priorities',
        algorithm: 'best-fit',
        parameters: { tieBreaking: 'priority', considerPriority: true },
        expectedStrengths: ['Respects order priorities', 'Good utilization'],
        expectedWeaknesses: ['May sacrifice some efficiency for priorities']
      });

      // Worst-Fit strategy
      strategies.push({
        name: 'Worst-Fit',
        description: 'Places items in bins with most remaining space',
        algorithm: 'worst-fit',
        parameters: {},
        expectedStrengths: ['Keeps large spaces available', 'Good for mixed sizes'],
        expectedWeaknesses: ['Generally less efficient', 'Higher waste']
      });
    }

    if (this.config.includeAdvancedAlgorithms) {
      // Genetic Algorithm strategies
      strategies.push({
        name: 'Genetic Algorithm - Balanced',
        description: 'Genetic algorithm with balanced objectives',
        algorithm: 'genetic',
        parameters: {
          populationSize: 50,
          generations: 100,
          mutationRate: 0.1,
          crossoverRate: 0.8
        } as GeneticAlgorithmConfig,
        expectedStrengths: ['Near-optimal solutions', 'Handles complex constraints'],
        expectedWeaknesses: ['Longer computation time', 'May not converge']
      });

      strategies.push({
        name: 'Genetic Algorithm - Fast',
        description: 'Genetic algorithm optimized for speed',
        algorithm: 'genetic',
        parameters: {
          populationSize: 30,
          generations: 50,
          mutationRate: 0.15,
          crossoverRate: 0.7
        } as GeneticAlgorithmConfig,
        expectedStrengths: ['Faster execution', 'Good solutions'],
        expectedWeaknesses: ['May not find optimal solution', 'Less exploration']
      });

      // Multi-objective strategies
      strategies.push({
        name: 'Multi-Objective NSGA-II',
        description: 'NSGA-II algorithm for multi-objective optimization',
        algorithm: 'multi-objective',
        parameters: {
          populationSize: 80,
          generations: 120,
          objectives: [
            { name: 'wasteMinimization', weight: 1.0, minimize: false },
            { name: 'efficiency', weight: 1.0, minimize: false },
            { name: 'timeOptimization', weight: 0.8, minimize: false }
          ]
        } as MultiObjectiveConfig,
        expectedStrengths: ['Pareto-optimal solutions', 'Multiple trade-offs'],
        expectedWeaknesses: ['Complex to interpret', 'Longer computation']
      });
    }

    if (this.config.includeHeuristicVariations) {
      // Hybrid strategies
      strategies.push({
        name: 'Hybrid Best-First',
        description: 'Combines best-fit and first-fit strategies',
        algorithm: 'hybrid',
        parameters: {
          primary: 'best-fit',
          fallback: 'first-fit',
          switchThreshold: 0.8
        },
        expectedStrengths: ['Adaptive approach', 'Balances speed and quality'],
        expectedWeaknesses: ['More complex logic', 'Unpredictable behavior']
      });
    }

    return strategies;
  }

  /**
   * Generate solution using specific strategy
   */
  private async generateSolutionWithStrategy(
    request: CuttingRequest,
    strategy: SolutionStrategy
  ): Promise<CuttingPlan> {
    switch (strategy.algorithm) {
      case 'first-fit':
        return await this.generateFirstFitSolution(request, strategy.parameters);

      case 'best-fit':
        return await this.generateBestFitSolution(request, strategy.parameters);

      case 'worst-fit':
        return await this.generateWorstFitSolution(request, strategy.parameters);

      case 'genetic':
        return await this.generateGeneticSolution(request, strategy.parameters);

      case 'multi-objective':
        return await this.generateMultiObjectiveSolution(request, strategy.parameters);

      case 'hybrid':
        return await this.generateHybridSolution(request, strategy.parameters);

      default:
        throw new Error(`Unknown algorithm: ${strategy.algorithm}`);
    }
  }

  /**
   * Generate first-fit solution with parameters
   */
  private async generateFirstFitSolution(
    request: CuttingRequest,
    parameters: any
  ): Promise<CuttingPlan> {
    // Modify request based on parameters
    const modifiedRequest = this.applyParameters(request, parameters);

    // Use existing first-fit implementation (simplified)
    return await this.basicFirstFit(modifiedRequest);
  }

  /**
   * Generate best-fit solution with parameters
   */
  private async generateBestFitSolution(
    request: CuttingRequest,
    parameters: any
  ): Promise<CuttingPlan> {
    const modifiedRequest = this.applyParameters(request, parameters);
    return await this.basicBestFit(modifiedRequest);
  }

  /**
   * Generate worst-fit solution
   */
  private async generateWorstFitSolution(
    request: CuttingRequest,
    parameters: any
  ): Promise<CuttingPlan> {
    const modifiedRequest = this.applyParameters(request, parameters);
    return await this.basicWorstFit(modifiedRequest);
  }

  /**
   * Generate genetic algorithm solution
   */
  private async generateGeneticSolution(
    request: CuttingRequest,
    parameters: GeneticAlgorithmConfig
  ): Promise<CuttingPlan> {
    const ga = new GeneticAlgorithm(parameters);
    const result = await ga.optimize(request);
    return result.bestSolution.plan!;
  }

  /**
   * Generate multi-objective solution
   */
  private async generateMultiObjectiveSolution(
    request: CuttingRequest,
    parameters: MultiObjectiveConfig
  ): Promise<CuttingPlan> {
    const mo = new MultiObjectiveOptimizer(parameters);
    const result = await mo.optimize(request);
    return result.paretoFront[0].individual.plan!;
  }

  /**
   * Generate hybrid solution
   */
  private async generateHybridSolution(
    request: CuttingRequest,
    parameters: any
  ): Promise<CuttingPlan> {
    // Try primary algorithm first
    try {
      const primaryPlan = await this.generateSolutionWithStrategy(request, {
        name: 'Primary',
        description: 'Primary algorithm',
        algorithm: parameters.primary,
        parameters: {},
        expectedStrengths: [],
        expectedWeaknesses: []
      });

      // Check if quality meets threshold
      if (primaryPlan.efficiency >= parameters.switchThreshold) {
        return primaryPlan;
      }
    } catch (error) {
      // Primary failed, continue to fallback
    }

    // Use fallback algorithm
    return await this.generateSolutionWithStrategy(request, {
      name: 'Fallback',
      description: 'Fallback algorithm',
      algorithm: parameters.fallback,
      parameters: {},
      expectedStrengths: [],
      expectedWeaknesses: []
    });
  }

  /**
   * Apply parameters to request (sorting, filtering, etc.)
   */
  private applyParameters(request: CuttingRequest, parameters: any): CuttingRequest {
    let modifiedOrders = [...request.orders];

    // Apply sorting
    if (parameters.sortOrder === 'decreasing') {
      modifiedOrders.sort((a, b) => b.requiredLength - a.requiredLength);
    } else if (parameters.sortOrder === 'increasing') {
      modifiedOrders.sort((a, b) => a.requiredLength - b.requiredLength);
    } else if (parameters.sortOrder === 'priority') {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      modifiedOrders.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
    }

    return {
      ...request,
      orders: modifiedOrders
    };
  }

  /**
   * Basic first-fit implementation
   */
  private async basicFirstFit(request: CuttingRequest): Promise<CuttingPlan> {
    // Simplified implementation - in practice would use the full CuttingOptimizerService
    const drumAllocations = request.availableDrums.map(drum => ({
      drumId: drum.id,
      cuts: [],
      remainingLength: drum.availableLength || drum.remainingLength || 0,
      utilization: 0
    }));

    const cuttingSequence = [];
    let stepNumber = 1;

    for (const order of request.orders) {
      for (let i = 0; i < order.quantity; i++) {
        for (const allocation of drumAllocations) {
          if (allocation.remainingLength >= order.requiredLength) {
            const cut: Cut = {
              orderId: order.id,
              length: order.requiredLength,
              startPosition: 0,
              endPosition: order.requiredLength
            };

            (allocation.cuts as Cut[]).push(cut);
            allocation.remainingLength -= order.requiredLength;

            cuttingSequence.push({
              stepNumber: stepNumber++,
              drumId: allocation.drumId,
              cuts: [cut],
              estimatedTime: 60
            });

            break;
          }
        }
      }
    }

    // Update utilizations
    drumAllocations.forEach(allocation => {
      const drum = request.availableDrums.find(d => d.id === allocation.drumId);
      const totalLength = drum ? (drum.availableLength || drum.remainingLength || 0) : 0;
      allocation.utilization = totalLength > 0 ? 1 - (allocation.remainingLength / totalLength) : 0;
    });

    const totalWaste = drumAllocations.reduce((sum, allocation) => sum + allocation.remainingLength, 0);
    const totalCapacity = request.availableDrums.reduce((sum, drum) =>
      sum + (drum.availableLength || drum.remainingLength || 0), 0);
    const efficiency = totalCapacity > 0 ? 1 - (totalWaste / totalCapacity) : 0;

    return {
      drumAllocations,
      cuttingSequence,
      totalWaste,
      efficiency,
      estimatedTime: cuttingSequence.length * 60
    };
  }

  /**
   * Basic best-fit implementation
   */
  private async basicBestFit(request: CuttingRequest): Promise<CuttingPlan> {
    // Similar to first-fit but chooses best fitting drum
    // Implementation would be similar to basicFirstFit but with best-fit logic
    return await this.basicFirstFit(request); // Simplified for now
  }

  /**
   * Basic worst-fit implementation
   */
  private async basicWorstFit(request: CuttingRequest): Promise<CuttingPlan> {
    // Similar to first-fit but chooses drum with most remaining space
    // Implementation would be similar to basicFirstFit but with worst-fit logic
    return await this.basicFirstFit(request); // Simplified for now
  }

  /**
   * Check if solution quality is sufficient
   */
  private isQualitySufficient(plan: CuttingPlan): boolean {
    return plan.efficiency >= this.config.qualityThreshold;
  }

  /**
   * Check if alternative is diverse enough from existing alternatives
   */
  private isDiverseEnough(
    alternative: RankedAlternative,
    existingAlternatives: RankedAlternative[]
  ): boolean {
    if (existingAlternatives.length === 0) return true;

    for (const existing of existingAlternatives) {
      const distance = this.calculateAlternativeDistance(alternative, existing);
      if (distance < this.config.diversityThreshold) {
        return false; // Too similar to existing alternative
      }
    }

    return true;
  }

  /**
   * Calculate distance between two alternatives
   */
  private calculateAlternativeDistance(
    alt1: RankedAlternative,
    alt2: RankedAlternative
  ): number {
    const efficiencyDiff = Math.abs(alt1.plan.efficiency - alt2.plan.efficiency);
    const wasteDiff = Math.abs(alt1.plan.totalWaste - alt2.plan.totalWaste);
    const timeDiff = Math.abs(alt1.plan.estimatedTime - alt2.plan.estimatedTime);

    // Normalize and combine
    return Math.sqrt(
      Math.pow(efficiencyDiff, 2) +
      Math.pow(wasteDiff / 1000, 2) +
      Math.pow(timeDiff / 3600, 2)
    );
  }

  /**
   * Create ranked alternative from plan and strategy
   */
  private async createRankedAlternative(
    plan: CuttingPlan,
    strategy: SolutionStrategy,
    request: CuttingRequest
  ): Promise<RankedAlternative> {
    const scores = this.calculateScores(plan, request);
    const tradeoffs = this.analyzeTradeoffs(plan, strategy);
    const suitability = this.analyzeSuitability(plan, strategy);

    return {
      id: `${strategy.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`,
      plan,
      score: scores.overall,
      advantages: strategy.expectedStrengths,
      disadvantages: strategy.expectedWeaknesses,
      strategy,
      ranking: 0, // Will be set during ranking
      scores,
      tradeoffs,
      suitability
    };
  }

  /**
   * Calculate comprehensive scores for an alternative
   */
  private calculateScores(plan: CuttingPlan, request: CuttingRequest): RankedAlternative['scores'] {
    const totalCapacity = request.availableDrums.reduce((sum, drum) =>
      sum + (drum.availableLength || drum.remainingLength || 0), 0);

    const efficiency = plan.efficiency;
    const wasteMinimization = totalCapacity > 0 ? 1 - (plan.totalWaste / totalCapacity) : 0;

    const avgTimePerCut = plan.cuttingSequence.length > 0 ?
      plan.estimatedTime / plan.cuttingSequence.length : 0;
    const timeOptimization = Math.max(0, 1 - (avgTimePerCut - 60) / 120); // Normalize around 1-3 minutes

    const complexity = this.calculateComplexityScore(plan, request);
    const robustness = this.calculateRobustnessScore(plan, request);

    const overall = (
      efficiency * 0.3 +
      wasteMinimization * 0.3 +
      timeOptimization * 0.2 +
      (1 - complexity) * 0.1 +
      robustness * 0.1
    );

    return {
      efficiency,
      wasteMinimization,
      timeOptimization,
      complexity,
      robustness,
      overall
    };
  }

  /**
   * Calculate complexity score
   */
  private calculateComplexityScore(plan: CuttingPlan, request: CuttingRequest): number {
    // Number of drums used
    const drumsUsed = plan.drumAllocations.filter(alloc => alloc.cuts.length > 0).length;
    const drumComplexity = drumsUsed / request.availableDrums.length;

    // Variation in cuts per drum
    const cutsPerDrum = plan.drumAllocations.map(alloc => alloc.cuts.length);
    const avgCuts = cutsPerDrum.reduce((sum, cuts) => sum + cuts, 0) / cutsPerDrum.length;
    const cutVariation = cutsPerDrum.reduce((sum, cuts) => sum + Math.abs(cuts - avgCuts), 0) / cutsPerDrum.length;
    const cutComplexity = Math.min(cutVariation / avgCuts, 1);

    return (drumComplexity + cutComplexity) / 2;
  }

  /**
   * Calculate robustness score (how well solution handles variations)
   */
  private calculateRobustnessScore(plan: CuttingPlan, request: CuttingRequest): number {
    // Check if solution uses drums efficiently
    const utilizationVariance = this.calculateUtilizationVariance(plan.drumAllocations);
    const robustness = Math.max(0, 1 - utilizationVariance);

    return robustness;
  }

  /**
   * Calculate utilization variance
   */
  private calculateUtilizationVariance(allocations: any[]): number {
    const utilizations = allocations.map(alloc => alloc.utilization);
    const avgUtilization = utilizations.reduce((sum, util) => sum + util, 0) / utilizations.length;
    const variance = utilizations.reduce((sum, util) => sum + Math.pow(util - avgUtilization, 2), 0) / utilizations.length;

    return Math.sqrt(variance);
  }

  /**
   * Analyze tradeoffs between alternatives
   */
  private analyzeTradeoffs(plan: CuttingPlan, strategy: SolutionStrategy): RankedAlternative['tradeoffs'] {
    // This would compare against other known strategies
    // Simplified implementation
    return [
      {
        versus: 'Standard algorithms',
        gains: strategy.expectedStrengths,
        losses: strategy.expectedWeaknesses
      }
    ];
  }

  /**
   * Analyze suitability for different scenarios
   */
  private analyzeSuitability(plan: CuttingPlan, strategy: SolutionStrategy): RankedAlternative['suitability'] {
    const suitability: RankedAlternative['suitability'] = [];

    // Time-critical scenario
    if (strategy.algorithm === 'first-fit' || strategy.name.includes('Fast')) {
      suitability.push({
        scenario: 'Time-critical operations',
        score: 0.9,
        reasoning: 'Fast execution with acceptable quality'
      });
    }

    // Quality-focused scenario
    if (strategy.algorithm === 'genetic' || strategy.algorithm === 'multi-objective') {
      suitability.push({
        scenario: 'Quality-focused operations',
        score: 0.9,
        reasoning: 'High-quality solutions with optimal resource usage'
      });
    }

    // Balanced scenario
    if (strategy.algorithm === 'best-fit' || strategy.algorithm === 'hybrid') {
      suitability.push({
        scenario: 'Balanced operations',
        score: 0.8,
        reasoning: 'Good balance between speed and quality'
      });
    }

    return suitability;
  }

  /**
   * Rank alternatives based on scores and criteria
   */
  private rankAlternatives(alternatives: RankedAlternative[]): RankedAlternative[] {
    // Sort by overall score
    const ranked = alternatives.sort((a, b) => b.scores.overall - a.scores.overall);

    // Assign rankings
    ranked.forEach((alternative, index) => {
      alternative.ranking = index + 1;
    });

    return ranked;
  }

  /**
   * Calculate diversity metrics for the set of alternatives
   */
  private calculateDiversityMetrics(alternatives: RankedAlternative[]): AlternativeGenerationResult['diversityMetrics'] {
    if (alternatives.length < 2) {
      return {
        averageDistance: 0,
        minDistance: 0,
        maxDistance: 0,
        diversityIndex: 0
      };
    }

    const distances: number[] = [];

    for (let i = 0; i < alternatives.length; i++) {
      for (let j = i + 1; j < alternatives.length; j++) {
        const distance = this.calculateAlternativeDistance(alternatives[i], alternatives[j]);
        distances.push(distance);
      }
    }

    const averageDistance = distances.reduce((sum, d) => sum + d, 0) / distances.length;
    const minDistance = Math.min(...distances);
    const maxDistance = Math.max(...distances);
    const diversityIndex = maxDistance > 0 ? averageDistance / maxDistance : 0;

    return {
      averageDistance,
      minDistance,
      maxDistance,
      diversityIndex
    };
  }

  /**
   * Select the recommended alternative
   */
  private selectRecommendedAlternative(alternatives: RankedAlternative[]): RankedAlternative {
    // Return the highest-ranked alternative
    return alternatives[0];
  }

  /**
   * Generate scenario-based recommendations
   */
  private generateScenarioRecommendations(
    alternatives: RankedAlternative[]
  ): AlternativeGenerationResult['scenarioRecommendations'] {
    const scenarios = [
      'Time-critical operations',
      'Quality-focused operations',
      'Balanced operations',
      'High-volume production',
      'Complex requirements'
    ];

    return scenarios.map(scenario => {
      // Find best alternative for this scenario
      let bestAlternative = alternatives[0];
      let bestScore = 0;

      for (const alternative of alternatives) {
        if (alternative.suitability) {
          const suitabilityEntry = alternative.suitability.find(s => s.scenario === scenario);
          if (suitabilityEntry && suitabilityEntry.score > bestScore) {
            bestScore = suitabilityEntry.score;
            bestAlternative = alternative;
          }
        }
      }

      return {
        scenario,
        recommendedAlternative: bestAlternative,
        reasoning: bestAlternative.suitability?.find(s => s.scenario === scenario)?.reasoning ||
          'Best overall alternative for this scenario'
      };
    });
  }
}