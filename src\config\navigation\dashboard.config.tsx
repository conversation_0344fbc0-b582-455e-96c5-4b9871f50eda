import { Home, Truck, Scissors, Drum, Shell, Warehouse, Factory, PackageCheck } from "lucide-react";
import { NavigationConfig } from "./types";
import iconDashboardBlack from '../../assets/iconDashboardBlack.png';
import iconDeliveryBlack from '../../assets/iconDeliveryBlack.png';
import iconWE from '../../assets/iconWE.png';
import iconSupplyChainBlack from '../../assets/supply-chainBlack.png';
import iconCutterBlack from '../../assets/iconCutterBlack.png';
import iconHome from '../../assets/iconHome.png';
import iconWarehouseBlack from '../../assets/iconWarehouseBlack.png';
import cableMachineOrange from '../../assets/cableMachineOrange.png';

export const dashboardNavigationConfig: NavigationConfig = {
    menu: [
        {
            title: "Home",
            icon: <img src={iconHome} alt="Home" className="h-5 w-5" />,
            to: "/"
        },
        {
            title: "Dashboard",
            icon: <img src={iconDashboardBlack} alt="Dashboard" className="h-5 w-5" />,
            to: "/modules/dashboard"
        },
        {
            title: "Versand",
            icon: <img src={iconDeliveryBlack} alt="Dispatch" className="h-6 w-6" />,
            to: "/modules/dashboard/dispatch",
        },
        {
            title: "Wareneingang",
            // Korrigiertes Icon und Größe
            icon: <img src={iconWE} alt="Incoming Goods" className="h-5 w-5" />,
            // Konsistente, kleingeschriebene URL wie gefordert
            to: "/modules/dashboard/incoming-goods",
        },
        {
            title: "CSR",
            icon: <img src={iconSupplyChainBlack} alt="CSR" className="h-5 w-5" />,
            to: "/modules/dashboard/csr",
        },
        {
            title: "Ablängerei",
            icon: <img src={iconCutterBlack} alt="Cutting" className="h-5 w-5" />,
            to: "#",
            items: [
                {
                    title: "Schnitte",
                    description: "Kennzahlen über Schnitte und Schnittarten",
                    icon: <Scissors className="size-5 shrink-0" />,
                    to: "/modules/dashboard/cutting",
                },
                {
                    title: "Maschinen",
                    description: "Effizienz der Maschinen",
                    icon: <img src={cableMachineOrange} alt="Cable Machine" className="size-5 shrink-0" />,
                    to: "/modules/dashboard/machines",
                },
            ],
        },
        {
            title: "Lagerauslastung",
            icon: <img src={iconWarehouseBlack} alt="Warehouse" className="h-5 w-5" />,
            to: "#",
            items: [
                {
                    title: "Automatisches Trommellager",
                    description: "Bestände und Bewegungen im Atomatischen Trommellager",
                    icon: <Drum className="size-5 shrink-0" />,
                    to: "/modules/dashboard/atrl",
                },
                {
                    title: "Automatisches Ringlager",
                    description: "Bestände und Bewegungen im Automatischen Ringlager",
                    icon: <Shell className="size-5 shrink-0" />,
                    to: "/modules/dashboard/aril",
                },
            ],
        },
    ]
};