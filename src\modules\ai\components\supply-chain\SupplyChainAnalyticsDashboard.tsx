/**
 * Supply Chain Analytics Dashboard Component
 * 
 * Main dashboard for displaying supply chain analytics, performance metrics,
 * alerts, and recommendations.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Truck,
  Users,
  BarChart3,
  AlertCircle,
  Lightbulb,
  RefreshCw
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell } from 'recharts';
import type {
  SupplyChainAnalytics,
  SupplierPerformanceAnalytics,
  SupplyChainAlert,
  SupplyChainRecommendation
} from '@/types/supply-chain-optimization';

interface SupplyChainAnalyticsDashboardProps {
  analytics: SupplyChainAnalytics | null;
  alerts: SupplyChainAlert[];
  recommendations: SupplyChainRecommendation[];
  loading: boolean;
  onRefresh: () => void;
  onAcknowledgeAlert: (alertId: string) => void;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export const SupplyChainAnalyticsDashboard: React.FC<SupplyChainAnalyticsDashboardProps> = ({
  analytics,
  alerts,
  recommendations,
  loading,
  onRefresh,
  onAcknowledgeAlert
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Lade Supply Chain Analytics...</span>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-yellow-500" />
        <h3 className="text-lg font-semibold mb-2">Keine Daten verfügbar</h3>
        <p className="text-gray-600 mb-4">Supply Chain Analytics konnten nicht geladen werden.</p>
        <Button onClick={onRefresh}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Erneut versuchen
        </Button>
      </div>
    );
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <BarChart3 className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatPercentage = (value: number) => `${(value * 100).toFixed(1)}%`;
  const formatCurrency = (value: number) => `€${value.toLocaleString()}`;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Supply Chain Analytics</h1>
          <p className="text-gray-600">Überwachung und Analyse der Lieferkette</p>
        </div>
        <Button onClick={onRefresh} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Aktualisieren
        </Button>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pünktlichkeitsrate</p>
                <p className="text-2xl font-bold">{formatPercentage(analytics.overallPerformance.onTimeDeliveryRate)}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
            <Progress
              value={analytics.overallPerformance.onTimeDeliveryRate * 100}
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Ø Lieferzeit</p>
                <p className="text-2xl font-bold">{analytics.overallPerformance.averageDeliveryTime.toFixed(1)} Tage</p>
              </div>
              <Truck className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Kosteneffizienz</p>
                <p className="text-2xl font-bold">{formatPercentage(analytics.overallPerformance.costEfficiency)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-yellow-500" />
            </div>
            <Progress
              value={analytics.overallPerformance.costEfficiency * 100}
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Lieferantenzuverlässigkeit</p>
                <p className="text-2xl font-bold">{formatPercentage(analytics.overallPerformance.supplierReliability)}</p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
            <Progress
              value={analytics.overallPerformance.supplierReliability * 100}
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Risikoscore</p>
                <p className="text-2xl font-bold">{analytics.overallPerformance.overallRiskScore.toFixed(1)}/10</p>
              </div>
              <AlertTriangle className={`h-8 w-8 ${analytics.overallPerformance.overallRiskScore > 7 ? 'text-red-500' :
                analytics.overallPerformance.overallRiskScore > 5 ? 'text-yellow-500' : 'text-green-500'
                }`} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts Section */}
      {alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              Aktuelle Warnungen ({alerts.filter(a => !a.acknowledged).length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {alerts.slice(0, 5).map((alert) => (
                <Alert key={alert.alertId} className={`border-l-4 ${getSeverityColor(alert.severity)}`}>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <AlertTitle className="flex items-center">
                        {alert.message}
                        <Badge variant="outline" className="ml-2">
                          {alert.severity}
                        </Badge>
                        {alert.supplierName && (
                          <Badge variant="secondary" className="ml-2">
                            {alert.supplierName}
                          </Badge>
                        )}
                      </AlertTitle>
                      <AlertDescription className="mt-1">
                        {alert.details}
                      </AlertDescription>
                      {alert.recommendations.length > 0 && (
                        <div className="mt-2">
                          <p className="text-sm font-medium">Empfohlene Maßnahmen:</p>
                          <ul className="text-sm text-gray-600 list-disc list-inside">
                            {alert.recommendations.slice(0, 2).map((rec, index) => (
                              <li key={index}>{rec}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                    {!alert.acknowledged && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onAcknowledgeAlert(alert.alertId)}
                      >
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Bestätigen
                      </Button>
                    )}
                  </div>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Übersicht</TabsTrigger>
          <TabsTrigger value="suppliers">Lieferanten</TabsTrigger>
          <TabsTrigger value="performance">Leistung</TabsTrigger>
          <TabsTrigger value="recommendations">Empfehlungen</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Trends Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Leistungstrends</CardTitle>
              <CardDescription>Entwicklung der wichtigsten KPIs über die Zeit</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analytics.trends[0]?.dataPoints || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => new Date(value).toLocaleDateString('de-DE')}
                  />
                  <YAxis />
                  <Tooltip
                    labelFormatter={(value) => new Date(value).toLocaleDateString('de-DE')}
                    formatter={(value: number) => [`${value.toFixed(1)}%`, 'Wert']}
                  />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#8884d8"
                    strokeWidth={2}
                    dot={{ fill: '#8884d8' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Risk Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Risikoanalyse</CardTitle>
                <CardDescription>Aktuelle Risikobewertung der Lieferkette</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Gesamtrisiko</span>
                    <Badge className={
                      analytics.riskAnalysis.overallRiskLevel === 'critical' ? 'bg-red-500' :
                        analytics.riskAnalysis.overallRiskLevel === 'high' ? 'bg-orange-500' :
                          analytics.riskAnalysis.overallRiskLevel === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                    }>
                      {analytics.riskAnalysis.overallRiskLevel}
                    </Badge>
                  </div>

                  {analytics.riskAnalysis.topRiskFactors.slice(0, 3).map((factor, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{factor.description}</span>
                        <Badge variant="outline">{factor.severity}</Badge>
                      </div>
                      <Progress value={factor.probability * 100} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Lieferleistung</CardTitle>
                <CardDescription>Analyse der Lieferperformance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Pünktlichkeitsrate</span>
                    <span className="font-semibold">
                      {formatPercentage(analytics.deliveryPerformance.onTimeRate)}
                    </span>
                  </div>
                  <Progress value={analytics.deliveryPerformance.onTimeRate * 100} />

                  <div className="flex justify-between items-center">
                    <span>Ø Lieferzeit</span>
                    <span className="font-semibold">
                      {analytics.deliveryPerformance.averageDeliveryTime.toFixed(1)} Tage
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span>Verspätungsrate</span>
                    <span className="font-semibold">
                      {formatPercentage(analytics.deliveryPerformance.delayFrequency)}
                    </span>
                  </div>
                  <Progress value={analytics.deliveryPerformance.delayFrequency * 100} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="suppliers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Lieferanten-Performance Ranking</CardTitle>
              <CardDescription>Bewertung und Ranking aller Lieferanten</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.supplierPerformance.slice(0, 10).map((supplier, index) => (
                  <div key={supplier.supplierId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                        <span className="text-sm font-bold text-blue-600">#{index + 1}</span>
                      </div>
                      <div>
                        <h4 className="font-semibold">{supplier.supplierName}</h4>
                        <p className="text-sm text-gray-600">ID: {supplier.supplierId}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Performance</p>
                        <p className="font-semibold">{supplier.performanceScore.toFixed(1)}/10</p>
                      </div>

                      <div className="text-center">
                        <p className="text-sm text-gray-600">Risiko</p>
                        <p className="font-semibold">{supplier.riskScore.toFixed(1)}/10</p>
                      </div>

                      <div className="text-center">
                        <p className="text-sm text-gray-600">Pünktlichkeit</p>
                        <p className="font-semibold">{formatPercentage(supplier.deliveryReliability)}</p>
                      </div>

                      <div className="text-center">
                        <p className="text-sm text-gray-600">Trend</p>
                        <div className="flex items-center justify-center">
                          {getTrendIcon(supplier.trends.performance)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {/* Delivery Performance Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Lieferperformance Trends</CardTitle>
              <CardDescription>Entwicklung der Lieferleistung über die Zeit</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={analytics.trends.find(t => t.metric === 'Pünktlichkeitsrate')?.dataPoints || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => new Date(value).toLocaleDateString('de-DE')}
                  />
                  <YAxis />
                  <Tooltip
                    labelFormatter={(value) => new Date(value).toLocaleDateString('de-DE')}
                    formatter={(value: number) => [`${value.toFixed(1)}%`, 'Pünktlichkeitsrate']}
                  />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Common Delay Reasons */}
          <Card>
            <CardHeader>
              <CardTitle>Häufige Verspätungsgründe</CardTitle>
              <CardDescription>Analyse der Hauptursachen für Lieferverzögerungen</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={analytics.deliveryPerformance.commonDelayReasons}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="reason" />
                  <YAxis />
                  <Tooltip formatter={(value: number) => [value, 'Häufigkeit']} />
                  <Bar dataKey="frequency" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="h-5 w-5 mr-2" />
                KI-Empfehlungen ({recommendations.length})
              </CardTitle>
              <CardDescription>Automatisch generierte Verbesserungsvorschläge</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recommendations.map((recommendation) => (
                  <div key={recommendation.recommendationId} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-semibold text-lg">{recommendation.title}</h4>
                        <p className="text-gray-600">{recommendation.description}</p>
                      </div>
                      <Badge className={getPriorityColor(recommendation.priority)}>
                        {recommendation.priority}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Erwarteter Nutzen</p>
                        <p className="text-sm">{recommendation.expectedBenefit}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Zeitrahmen</p>
                        <p className="text-sm">{recommendation.timeframe}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">ROI</p>
                        <p className="text-sm text-green-600">
                          {formatCurrency(recommendation.estimatedSavings - recommendation.estimatedCost)}
                        </p>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-gray-600 mb-2">Umsetzungsschritte:</p>
                      <ol className="text-sm text-gray-600 list-decimal list-inside space-y-1">
                        {recommendation.implementationSteps.map((step, index) => (
                          <li key={index}>{step}</li>
                        ))}
                      </ol>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SupplyChainAnalyticsDashboard;