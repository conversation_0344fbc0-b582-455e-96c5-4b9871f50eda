import { useState, useEffect } from 'react';

/**
 * Motion preferences interface based on design specifications
 */
export interface MotionPreferences {
  prefersReducedMotion: boolean;
  allowTransitions: boolean;
  fallbackMode: 'static' | 'minimal-animation';
}

/**
 * Fallback mode options for reduced motion scenarios
 */
export type FallbackMode = 'static' | 'minimal-animation';

/**
 * Custom hook to detect and manage motion preferences
 * Implements requirements 4.1, 4.2, and 4.4 for motion sensitivity support
 */
export function useMotionPreference() {
  const [motionPreferences, setMotionPreferences] = useState<MotionPreferences>(() => {
    // Initialize with current system preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    return {
      prefersReducedMotion,
      allowTransitions: !prefersReducedMotion,
      fallbackMode: prefersReducedMotion ? 'static' : 'minimal-animation'
    };
  });

  useEffect(() => {
    // Create media query matcher for prefers-reduced-motion
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    // Handler for motion preference changes
    const handleMotionPreferenceChange = (event: MediaQueryListEvent) => {
      const prefersReducedMotion = event.matches;
      
      setMotionPreferences(prev => ({
        ...prev,
        prefersReducedMotion,
        allowTransitions: !prefersReducedMotion,
        fallbackMode: prefersReducedMotion ? 'static' : 'minimal-animation'
      }));
    };

    // Add event listener for changes
    mediaQuery.addEventListener('change', handleMotionPreferenceChange);

    // Cleanup event listener on unmount
    return () => {
      mediaQuery.removeEventListener('change', handleMotionPreferenceChange);
    };
  }, []);

  /**
   * Manually override motion preferences (for testing or user settings)
   */
  const updateMotionPreferences = (updates: Partial<MotionPreferences>) => {
    setMotionPreferences(prev => ({
      ...prev,
      ...updates
    }));
  };

  /**
   * Reset to system defaults
   */
  const resetToSystemDefaults = () => {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    setMotionPreferences({
      prefersReducedMotion,
      allowTransitions: !prefersReducedMotion,
      fallbackMode: prefersReducedMotion ? 'static' : 'minimal-animation'
    });
  };

  /**
   * Get fallback mode selection logic based on user preferences
   */
  const getFallbackMode = (): FallbackMode => {
    if (motionPreferences.prefersReducedMotion) {
      return 'static';
    }
    return motionPreferences.fallbackMode;
  };



  /**
   * Check if transitions should be enabled
   */
  const shouldAllowTransitions = (): boolean => {
    return motionPreferences.allowTransitions && !motionPreferences.prefersReducedMotion;
  };

  return {
    motionPreferences,
    updateMotionPreferences,
    resetToSystemDefaults,
    getFallbackMode,
    shouldAllowTransitions,
    // Convenience getters for common checks
    prefersReducedMotion: motionPreferences.prefersReducedMotion,
    allowTransitions: motionPreferences.allowTransitions,
    fallbackMode: motionPreferences.fallbackMode
  };
}

export default useMotionPreference;