import React from 'react';

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  footer?: React.ReactNode;
  icon?: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  className?: string;
}

/**
 * StatCard Komponente
 * 
 * Zeigt eine Statistik mit Titel, Wert und optionaler Beschreibung an.
 * Kann auch einen Trend (positiv/negativ/neutral) und einen Fußbereich anzeigen.
 */
export function StatCard({
  title,
  value,
  description,
  footer,
  icon,
  trend,
  trendValue,
  className = '',
}: StatCardProps) {
  return (
    <div className={`overflow-hidden h-full w-full max-w-xs border-2 border-black bg-white rounded-md p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] ${className}`}>
      <div className="pb-2">
        <div className="text-sm font-medium">{title}</div>
        {description && <div className="text-xs text-gray-500">{description}</div>}
      </div>
      <div className="py-2">
        <div className="flex items-center">
          {icon && <div className="mr-2">{icon}</div>}
          <div>
            <div className="text-2xl font-bold">{value}</div>
            {trend && (
              <p className={`text-xs ${
                trend === 'up' ? 'text-green-500' : 
                trend === 'down' ? 'text-red-500' : 
                'text-gray-500'
              } flex items-center`}>
                {trend === 'up' ? '↑' : trend === 'down' ? '↓' : '→'} {trendValue}
              </p>
            )}
          </div>
        </div>
      </div>
      {footer && <div className="pt-1 text-xs text-gray-500">{footer}</div>}
    </div>
  );
}
