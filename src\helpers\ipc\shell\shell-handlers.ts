/**
 * Shell-Handler für Main-Process
 * 
 * Behandelt Shell-bezogene IPC-Anfragen und stellt
 * sichere Shell-Funktionalitäten zur Verfügung.
 */

import { ipcMain, shell } from 'electron';

/**
 * Registriert IPC-Handler für Shell-Funktionen
 */
export function registerShellHandlers(): void {
  console.log('🐚 Registriere Shell-Handler...');

  // Handler für das Öffnen externer URLs
  ipcMain.handle('shell:open-external', async (_event, url: string): Promise<void> => {
    try {
      console.log('[SHELL] Öffne externe URL:', url);
      
      // Validiere die URL für Sicherheit
      if (!isValidUrl(url)) {
        throw new Error('Ungültige URL');
      }
      
      // Öffne die URL mit der Standard-Anwendung
      await shell.openExternal(url);
      
      console.log('[SHELL] URL erfolgreich geöffnet');
    } catch (error) {
      console.error('[SHELL] Fehler beim Öffnen der URL:', error);
      throw error;
    }
  });

  console.log('✅ Shell-Handler registriert');
}

/**
 * Validiert eine URL für Sicherheitszwecke
 * @param url Die zu validierende URL
 * @returns true wenn die URL sicher ist
 */
function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    
    // Erlaube nur bestimmte Protokolle
    const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:'];
    
    if (!allowedProtocols.includes(urlObj.protocol)) {
      console.warn('[SHELL] Nicht erlaubtes Protokoll:', urlObj.protocol);
      return false;
    }
    
    return true;
  } catch (error) {
    console.warn('[SHELL] Ungültige URL-Format:', url);
    return false;
  }
}