import React from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Table as TableIcon } from "lucide-react";

interface TableDataDialogProps {
  isOpen: boolean;
  onClose: () => void;
  tableName: string;
  tableData: {
    columns: string[];
    rows: any[];
  } | null;
}

/**
 * Dialog-Komponente zur Anzeige von Tabellendaten in einem Popup-Fenster
 * 
 * Zeigt die Daten einer ausgewählten Tabelle in einem übersichtlichen Dialog an.
 */
const TableDataDialog: React.FC<TableDataDialogProps> = ({ 
  isOpen, 
  onClose, 
  tableName, 
  tableData 
}) => {
  if (!isOpen || !tableData) return null;

  // Dialog-Inhalt
  const dialogContent = (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="mb-4">
        <div className="flex items-center gap-2 mb-1">
          <TableIcon className="h-5 w-5" />
          <h2 className="text-xl font-semibold">Tabelle: {tableName}</h2>
        </div>
        <p className="text-muted-foreground">
          Daten aus der Tabelle {tableName} ({tableData.rows.length} Einträge)
        </p>
      </div>
      
      {/* Tabellendaten */}
      <div className="flex-1 overflow-auto">
        <div className="rounded-md border overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                {tableData.columns.map((column) => (
                  <TableHead key={column} className="bg-muted/50 font-medium">
                    {column}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {tableData.rows.length > 0 ? (
                tableData.rows.map((row, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {tableData.columns.map((column) => (
                      <TableCell key={`${rowIndex}-${column}`}>
                        {row[column] !== null ? String(row[column]) : ''}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={tableData.columns.length} className="text-center py-4">
                    Keine Daten vorhanden
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );

  // Manuelles Rendering des Dialogs
  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
      onClick={onClose}
    >
      <div 
        className="relative max-w-6xl max-h-[95vh] w-[95%] bg-background rounded-lg shadow-2xl border overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <button 
          className="absolute top-4 right-4 z-10 p-2 rounded-full bg-background border hover:bg-muted transition-colors"
          onClick={onClose}
          aria-label="Schließen"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
        <div className="p-6 overflow-y-auto max-h-[95vh]">
          {dialogContent}
        </div>
      </div>
    </div>
  );
};

export default TableDataDialog;
