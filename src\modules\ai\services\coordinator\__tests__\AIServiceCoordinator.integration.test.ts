/**
 * AI Service Coordinator Integration Tests
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { AIServiceCoordinator, AIOperation } from '../AIServiceCoordinator';
import { AIBaseService } from '../../base/AIBaseService';

// Mock AI service for testing
class MockAIService extends AIBaseService {
  private shouldFail: boolean = false;
  private executionDelay: number = 100;

  constructor(shouldFail = false, executionDelay = 100) {
    super();
    this.shouldFail = shouldFail;
    this.executionDelay = executionDelay;
  }

  async execute(payload: any): Promise<any> {
    await new Promise(resolve => setTimeout(resolve, this.executionDelay));
    
    if (this.shouldFail) {
      throw new Error('Mock service execution failed');
    }
    
    return { result: 'success', payload };
  }

  async healthCheck() {
    return {
      isHealthy: !this.shouldFail,
      status: this.shouldFail ? 'error' : 'healthy',
      lastCheck: new Date(),
      uptime: 3600000,
      version: '1.0.0'
    };
  }
}

describe('AIServiceCoordinator Integration Tests', () => {
  let coordinator: AIServiceCoordinator;
  let mockService1: MockAIService;
  let mockService2: MockAIService;
  let mockFailingService: MockAIService;

  beforeEach(async () => {
    coordinator = new AIServiceCoordinator({
      maxConcurrentOperations: 3,
      defaultTimeout: 2000, // Shorter timeout for tests
      defaultRetries: 1, // Fewer retries for tests
      enableQueueing: true,
      queueMaxSize: 10,
      enableResourceMonitoring: false // Disable for testing
    });

    mockService1 = new MockAIService(false, 100);
    mockService2 = new MockAIService(false, 200);
    mockFailingService = new MockAIService(true, 50);

    await coordinator.initialize();

    // Register services
    coordinator.registerService('service1', mockService1);
    coordinator.registerService('service2', mockService2);
    coordinator.registerService('failingService', mockFailingService);
  });

  afterEach(async () => {
    await coordinator.destroy();
  });

  describe('Service Registration and Management', () => {
    it('registers and unregisters services correctly', async () => {
      const newService = new MockAIService();
      coordinator.registerService('newService', newService);

      const status = await coordinator.healthCheck();
      expect(status.services).toHaveProperty('newService');

      coordinator.unregisterService('newService');
      const updatedStatus = await coordinator.healthCheck();
      expect(updatedStatus.services).not.toHaveProperty('newService');
    });

    it('registers services with dependencies', () => {
      const dependentService = new MockAIService();
      coordinator.registerService('dependentService', dependentService, {
        serviceId: 'dependentService',
        dependsOn: ['service1', 'service2'],
        optional: false
      });

      // Should not throw error
      expect(() => coordinator.registerService('dependentService', dependentService)).not.toThrow();
    });
  });

  describe('Single Operation Execution', () => {
    it('executes single operation successfully', async () => {
      const operation: AIOperation = {
        id: 'test-op-1',
        type: 'execute',
        serviceId: 'service1',
        priority: 'medium',
        payload: { data: 'test' }
      };

      const result = await coordinator.executeOperation(operation);

      expect(result.success).toBe(true);
      expect(result.operationId).toBe('test-op-1');
      expect(result.serviceUsed).toBe('service1');
      expect(result.result).toEqual({ result: 'success', payload: { data: 'test' } });
      expect(result.executionTime).toBeGreaterThan(0);
    });

    it('handles operation failure gracefully', async () => {
      const operation: AIOperation = {
        id: 'test-op-fail',
        type: 'execute',
        serviceId: 'failingService',
        priority: 'medium',
        payload: { data: 'test' }
      };

      const result = await coordinator.executeOperation(operation);

      expect(result.success).toBe(false);
      expect(result.operationId).toBe('test-op-fail');
      expect(result.error).toContain('Mock service execution failed');
      expect(result.executionTime).toBeGreaterThan(0);
    });

    it('validates operation before execution', async () => {
      const invalidOperation: AIOperation = {
        id: '',
        type: 'execute',
        serviceId: 'service1',
        priority: 'medium',
        payload: {}
      };

      const result = await coordinator.executeOperation(invalidOperation);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Operation ID is required');
    });

    it('handles non-existent service', async () => {
      const operation: AIOperation = {
        id: 'test-op-nonexistent',
        type: 'execute',
        serviceId: 'nonExistentService',
        priority: 'medium',
        payload: {}
      };

      const result = await coordinator.executeOperation(operation);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Service not registered');
    });
  });

  describe('Multiple Operations Execution', () => {
    it('executes multiple operations in priority order', async () => {
      const operations: AIOperation[] = [
        {
          id: 'low-priority',
          type: 'execute',
          serviceId: 'service1',
          priority: 'low',
          payload: { order: 3 }
        },
        {
          id: 'high-priority',
          type: 'execute',
          serviceId: 'service2',
          priority: 'high',
          payload: { order: 1 }
        },
        {
          id: 'medium-priority',
          type: 'execute',
          serviceId: 'service1',
          priority: 'medium',
          payload: { order: 2 }
        }
      ];

      const results = await coordinator.executeOperations(operations);

      expect(results).toHaveLength(3);
      expect(results.every(r => r.success)).toBe(true);

      // Results should be in priority order (high, medium, low)
      const highPriorityResult = results.find(r => r.operationId === 'high-priority');
      const mediumPriorityResult = results.find(r => r.operationId === 'medium-priority');
      const lowPriorityResult = results.find(r => r.operationId === 'low-priority');

      expect(highPriorityResult).toBeDefined();
      expect(mediumPriorityResult).toBeDefined();
      expect(lowPriorityResult).toBeDefined();
    });

    it('respects concurrency limits', async () => {
      const startTime = Date.now();
      
      // Create 5 operations (more than maxConcurrentOperations = 3)
      const operations: AIOperation[] = Array.from({ length: 5 }, (_, i) => ({
        id: `concurrent-op-${i}`,
        type: 'execute',
        serviceId: 'service1',
        priority: 'medium',
        payload: { index: i }
      }));

      const results = await coordinator.executeOperations(operations);
      const executionTime = Date.now() - startTime;

      expect(results).toHaveLength(5);
      expect(results.every(r => r.success)).toBe(true);
      
      // Should take longer than a single batch due to concurrency limits
      expect(executionTime).toBeGreaterThan(150); // At least two batches
    });

    it('handles mixed success and failure operations', async () => {
      const operations: AIOperation[] = [
        {
          id: 'success-op',
          type: 'execute',
          serviceId: 'service1',
          priority: 'medium',
          payload: {}
        },
        {
          id: 'failure-op',
          type: 'execute',
          serviceId: 'failingService',
          priority: 'medium',
          payload: {}
        }
      ];

      const results = await coordinator.executeOperations(operations);

      expect(results).toHaveLength(2);
      
      const successResult = results.find(r => r.operationId === 'success-op');
      const failureResult = results.find(r => r.operationId === 'failure-op');

      expect(successResult?.success).toBe(true);
      expect(failureResult?.success).toBe(false);
    });
  });

  describe('Operation Queue Management', () => {
    it('queues operations correctly', () => {
      const operation: AIOperation = {
        id: 'queued-op',
        type: 'execute',
        serviceId: 'service1',
        priority: 'medium',
        payload: {}
      };

      coordinator.queueOperation(operation);
      
      const queueStatus = coordinator.getQueueStatus();
      expect(queueStatus.queueLength).toBe(1);
    });

    it('processes queued operations', async () => {
      const operations: AIOperation[] = [
        {
          id: 'queued-op-1',
          type: 'execute',
          serviceId: 'service1',
          priority: 'high',
          payload: {}
        },
        {
          id: 'queued-op-2',
          type: 'execute',
          serviceId: 'service2',
          priority: 'low',
          payload: {}
        }
      ];

      operations.forEach(op => coordinator.queueOperation(op));
      
      const results = await coordinator.processQueue();
      
      expect(results).toHaveLength(2);
      expect(results.every(r => r.success)).toBe(true);
      
      const queueStatus = coordinator.getQueueStatus();
      expect(queueStatus.queueLength).toBe(0);
    });

    it('respects queue size limits', () => {
      // Fill queue to max size
      for (let i = 0; i < 10; i++) {
        coordinator.queueOperation({
          id: `queue-op-${i}`,
          type: 'execute',
          serviceId: 'service1',
          priority: 'medium',
          payload: {}
        });
      }

      // Adding one more should throw error
      expect(() => {
        coordinator.queueOperation({
          id: 'overflow-op',
          type: 'execute',
          serviceId: 'service1',
          priority: 'medium',
          payload: {}
        });
      }).toThrow('Operation queue is full');
    });

    it('clears queue correctly', () => {
      coordinator.queueOperation({
        id: 'clear-test-op',
        type: 'execute',
        serviceId: 'service1',
        priority: 'medium',
        payload: {}
      });

      coordinator.clearQueue();
      
      const queueStatus = coordinator.getQueueStatus();
      expect(queueStatus.queueLength).toBe(0);
    });
  });

  describe('Health Monitoring', () => {
    it('provides comprehensive health status', async () => {
      const status = await coordinator.healthCheck();

      expect(status.isHealthy).toBe(true);
      expect(status.services).toBeDefined();
      expect(status.services).toHaveProperty('service1');
      expect(status.services).toHaveProperty('service2');
      expect(status.services).toHaveProperty('failingService');

      expect(status.services.service1.isHealthy).toBe(true);
      expect(status.services.service2.isHealthy).toBe(true);
      expect(status.services.failingService.isHealthy).toBe(false);
      
      // Check new coordinator status properties
      expect(status.totalOperations).toBeDefined();
      expect(status.successfulOperations).toBeDefined();
      expect(status.failedOperations).toBeDefined();
      expect(status.averageExecutionTime).toBeDefined();
      expect(status.queueLength).toBeDefined();
      expect(status.runningOperations).toBeDefined();
      expect(status.resourceUsage).toBeDefined();
      expect(status.performanceHistory).toBeDefined();
    });

    it('provides queue status information', () => {
      coordinator.queueOperation({
        id: 'status-test-op',
        type: 'execute',
        serviceId: 'service1',
        priority: 'medium',
        payload: {}
      });

      const queueStatus = coordinator.getQueueStatus();
      
      expect(queueStatus.queueLength).toBe(1);
      expect(queueStatus.runningOperations).toBe(0);
      expect(queueStatus.maxConcurrent).toBe(3);
      expect(queueStatus.queueEnabled).toBe(true);
    });
  });

  describe('Resource Monitoring and Performance Tracking', () => {
    it('tracks operation performance metrics', async () => {
      const operation: AIOperation = {
        id: 'perf-test-op',
        type: 'execute',
        serviceId: 'service1',
        priority: 'medium',
        payload: { data: 'test' }
      };

      const result = await coordinator.executeOperation(operation);

      expect(result.resourceUsage).toBeDefined();
      expect(result.resourceUsage?.memoryUsed).toBeGreaterThan(0);
      expect(result.resourceUsage?.cpuUsage).toBeGreaterThanOrEqual(0);
      expect(result.resourceUsage?.startTime).toBeDefined();
      expect(result.resourceUsage?.endTime).toBeDefined();
      expect(result.queueTime).toBeDefined();
    });

    it('provides performance analytics', async () => {
      // Execute a few operations to generate performance data
      const operations: AIOperation[] = [
        {
          id: 'analytics-op-1',
          type: 'execute',
          serviceId: 'service1',
          priority: 'medium',
          payload: {}
        },
        {
          id: 'analytics-op-2',
          type: 'execute',
          serviceId: 'service2',
          priority: 'medium',
          payload: {}
        }
      ];

      await coordinator.executeOperations(operations);

      const analytics = coordinator.getPerformanceAnalytics();

      expect(analytics.averageExecutionTime).toBeGreaterThan(0);
      expect(analytics.successRate).toBeGreaterThan(0);
      expect(analytics.operationsPerMinute).toBeGreaterThanOrEqual(0);
      expect(analytics.resourceEfficiency).toBeGreaterThanOrEqual(0);
      expect(analytics.servicePerformance).toBeDefined();
      expect(analytics.servicePerformance.service1).toBeDefined();
      expect(analytics.servicePerformance.service2).toBeDefined();
    });

    it('provides orchestration metrics', async () => {
      const operation: AIOperation = {
        id: 'orchestration-test-op',
        type: 'execute',
        serviceId: 'service1',
        priority: 'medium',
        payload: {}
      };

      await coordinator.executeOperation(operation);

      const metrics = coordinator.getOrchestrationMetrics();

      expect(metrics.coordinator).toBeDefined();
      expect(metrics.coordinator.totalOperations).toBeGreaterThan(0);
      expect(metrics.coordinator.resourceUsage).toBeDefined();
      expect(metrics.services).toBeDefined();
      expect(metrics.performance).toBeDefined();
      expect(metrics.performance.bottlenecks).toBeDefined();
      expect(Array.isArray(metrics.performance.bottlenecks)).toBe(true);
    });

    it('tracks queue time for operations', async () => {
      const operation: AIOperation = {
        id: 'queue-time-test-op',
        type: 'execute',
        serviceId: 'service1',
        priority: 'medium',
        payload: {}
      };

      coordinator.queueOperation(operation);
      
      // Wait a bit to simulate queue time
      await new Promise(resolve => setTimeout(resolve, 50));
      
      const results = await coordinator.processQueue();
      
      expect(results).toHaveLength(1);
      expect(results[0].queueTime).toBeGreaterThan(0);
    });

    it('handles resource availability checks', async () => {
      // Create coordinator with very low resource limits
      const restrictedCoordinator = new AIServiceCoordinator({
        maxMemoryUsage: 1, // 1MB limit (very low)
        maxCpuUsage: 1, // 1% CPU limit (very low)
        enableResourceMonitoring: true
      });

      await restrictedCoordinator.initialize();
      restrictedCoordinator.registerService('service1', mockService1);

      const operation: AIOperation = {
        id: 'resource-test-op',
        type: 'execute',
        serviceId: 'service1',
        priority: 'medium',
        payload: {}
      };

      const result = await restrictedCoordinator.executeOperation(operation);
      
      // Operation might fail due to resource constraints
      // But should handle gracefully
      expect(result.operationId).toBe('resource-test-op');
      expect(typeof result.success).toBe('boolean');

      await restrictedCoordinator.destroy();
    });
  });

  describe('Retry and Timeout Handling', () => {
    it('handles operation timeout', async () => {
      const slowService = new MockAIService(false, 6000); // 6 second delay
      coordinator.registerService('slowService', slowService);

      const operation: AIOperation = {
        id: 'timeout-op',
        type: 'execute',
        serviceId: 'slowService',
        priority: 'medium',
        payload: {},
        timeout: 1000 // 1 second timeout
      };

      const result = await coordinator.executeOperation(operation);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('timeout');
    });

    it('retries failed operations', async () => {
      const flakyService = new MockAIService(true, 50);
      let attemptCount = 0;
      
      // Mock the service to succeed on third attempt
      const originalExecute = flakyService.execute.bind(flakyService);
      flakyService.execute = async (payload: any) => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error('Temporary failure');
        }
        return { result: 'success', payload, attempts: attemptCount };
      };

      coordinator.registerService('flakyService', flakyService);

      const operation: AIOperation = {
        id: 'retry-op',
        type: 'execute',
        serviceId: 'flakyService',
        priority: 'medium',
        payload: {},
        retries: 3
      };

      const result = await coordinator.executeOperation(operation);
      
      expect(result.success).toBe(true);
      expect(attemptCount).toBe(3);
    });
  });

  describe('Dependency Management', () => {
    it('checks service dependencies before execution', async () => {
      // Register a service with dependencies
      const dependentService = new MockAIService();
      coordinator.registerService('dependentService', dependentService, {
        serviceId: 'dependentService',
        dependsOn: ['service1'],
        optional: false
      });

      const operation: AIOperation = {
        id: 'dependent-op',
        type: 'execute',
        serviceId: 'dependentService',
        priority: 'medium',
        payload: {}
      };

      const result = await coordinator.executeOperation(operation);
      expect(result.success).toBe(true);
    });

    it('handles missing required dependencies', async () => {
      const dependentService = new MockAIService();
      coordinator.registerService('dependentService', dependentService, {
        serviceId: 'dependentService',
        dependsOn: ['nonExistentService'],
        optional: false
      });

      const operation: AIOperation = {
        id: 'missing-dep-op',
        type: 'execute',
        serviceId: 'dependentService',
        priority: 'medium',
        payload: {}
      };

      const result = await coordinator.executeOperation(operation);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Required dependency not available');
    });

    it('handles optional dependencies gracefully', async () => {
      const dependentService = new MockAIService();
      coordinator.registerService('dependentService', dependentService, {
        serviceId: 'dependentService',
        dependsOn: ['nonExistentService'],
        optional: true
      });

      const operation: AIOperation = {
        id: 'optional-dep-op',
        type: 'execute',
        serviceId: 'dependentService',
        priority: 'medium',
        payload: {}
      };

      const result = await coordinator.executeOperation(operation);
      expect(result.success).toBe(true);
    });
  });
});