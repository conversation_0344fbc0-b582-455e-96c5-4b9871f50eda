/**
 * Label Authentication Styling Utilities
 * Provides helper functions and constants for applying label-style aesthetics
 */

// CSS class name constants for type safety
export const LABEL_CLASSES = {
  // Typography
  typography: 'label-typography',
  textPrimary: 'label-text-primary',
  textSecondary: 'label-text-secondary',
  textMuted: 'label-text-muted',
  
  // Form containers
  formContainer: 'label-form-container',
  formBackground: 'label-form-background',
  
  // Form elements
  input: 'label-input',
  button: 'label-button',
  buttonPrimary: 'label-button-primary',
  formLabel: 'label-form-label',
  
  // Messages
  error: 'label-error',
  success: 'label-success',
  
  // States
  loading: 'label-loading',
  focusVisible: 'label-focus-visible',
  
  // Layout utilities
  stack: 'label-stack',
  row: 'label-row',
  center: 'label-center',
  fullWidth: 'label-full-width',
  
  // Accessibility
  srOnly: 'label-sr-only'
} as const;

// Type for label class names
export type LabelClassName = typeof LABEL_CLASSES[keyof typeof LABEL_CLASSES];

/**
 * Combines multiple label classes with optional additional classes
 */
export function combineClasses(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

/**
 * Creates a label form container class string
 */
export function getLabelFormClasses(additional?: string): string {
  return combineClasses(
    LABEL_CLASSES.formContainer,
    LABEL_CLASSES.formBackground,
    LABEL_CLASSES.typography,
    additional
  );
}

/**
 * Creates a label input class string
 */
export function getLabelInputClasses(additional?: string): string {
  return combineClasses(
    LABEL_CLASSES.input,
    additional
  );
}

/**
 * Creates a label button class string
 */
export function getLabelButtonClasses(variant: 'primary' | 'secondary' = 'secondary', additional?: string): string {
  return combineClasses(
    LABEL_CLASSES.button,
    variant === 'primary' ? LABEL_CLASSES.buttonPrimary : null,
    additional
  );
}

/**
 * Creates a label text class string based on hierarchy
 */
export function getLabelTextClasses(variant: 'primary' | 'secondary' | 'muted' = 'primary', additional?: string): string {
  const textClass = {
    primary: LABEL_CLASSES.textPrimary,
    secondary: LABEL_CLASSES.textSecondary,
    muted: LABEL_CLASSES.textMuted
  }[variant];
  
  return combineClasses(
    LABEL_CLASSES.typography,
    textClass,
    additional
  );
}

/**
 * Creates a label message class string
 */
export function getLabelMessageClasses(type: 'error' | 'success', additional?: string): string {
  const messageClass = type === 'error' ? LABEL_CLASSES.error : LABEL_CLASSES.success;
  return combineClasses(messageClass, additional);
}

/**
 * Responsive breakpoints for label styling
 */
export const LABEL_BREAKPOINTS = {
  mobile: 480,
  tablet: 768,
  desktop: 1024
} as const;

/**
 * Checks if current viewport matches a breakpoint
 */
export function matchesBreakpoint(breakpoint: keyof typeof LABEL_BREAKPOINTS): boolean {
  if (typeof window === 'undefined') return false;
  return window.innerWidth <= LABEL_BREAKPOINTS[breakpoint];
}

/**
 * Gets the current breakpoint name
 */
export function getCurrentBreakpoint(): keyof typeof LABEL_BREAKPOINTS | 'large' {
  if (typeof window === 'undefined') return 'desktop';
  
  const width = window.innerWidth;
  if (width <= LABEL_BREAKPOINTS.mobile) return 'mobile';
  if (width <= LABEL_BREAKPOINTS.tablet) return 'tablet';
  if (width <= LABEL_BREAKPOINTS.desktop) return 'desktop';
  return 'large';
}

/**
 * Accessibility helpers
 */
export const LABEL_ACCESSIBILITY = {
  /**
   * Creates ARIA attributes for form elements
   */
  getFormAriaAttributes: (id: string, hasError?: boolean, errorId?: string) => ({
    id,
    'aria-invalid': hasError ? 'true' : 'false',
    'aria-describedby': hasError && errorId ? errorId : undefined
  }),
  
  /**
   * Creates ARIA attributes for buttons
   */
  getButtonAriaAttributes: (loading?: boolean, disabled?: boolean) => ({
    'aria-busy': loading ? 'true' : 'false',
    'aria-disabled': disabled ? 'true' : 'false'
  }),
  
  /**
   * Creates screen reader text for loading states
   */
  getLoadingText: (action: string) => `${action} in progress, please wait`,
  
  /**
   * Creates screen reader text for form validation
   */
  getValidationText: (field: string, error?: string) => 
    error ? `${field} has error: ${error}` : `${field} is valid`
} as const;

/**
 * Motion preference detection
 */
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * High contrast preference detection
 */
export function prefersHighContrast(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-contrast: high)').matches;
}

/**
 * Creates CSS custom properties for dynamic theming
 */
export function createLabelTheme(overrides?: Partial<{
  primaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
}>): Record<string, string> {
  const defaults = {
    primaryColor: '#000000',
    backgroundColor: '#ffffff',
    textColor: '#000000',
    borderColor: '#000000'
  };
  
  const theme = { ...defaults, ...overrides };
  
  return {
    '--label-primary': theme.primaryColor,
    '--label-background': theme.backgroundColor,
    '--label-text': theme.textColor,
    '--label-border': theme.borderColor
  };
}

/**
 * Validates that required label styles are loaded
 */
export function validateLabelStyles(): boolean {
  if (typeof document === 'undefined') return false;
  
  // Check if CSS custom properties are available
  const testElement = document.createElement('div');
  testElement.style.setProperty('--test-prop', 'test');
  const hasCustomProperties = testElement.style.getPropertyValue('--test-prop') === 'test';
  
  // Check if label classes exist in stylesheets
  const hasLabelStyles = Array.from(document.styleSheets).some(sheet => {
    try {
      return Array.from(sheet.cssRules || []).some(rule => 
        rule instanceof CSSStyleRule && rule.selectorText?.includes('label-')
      );
    } catch {
      return false;
    }
  });
  
  return hasCustomProperties && hasLabelStyles;
}

/**
 * Development helper to log current label styling state
 */
export function debugLabelStyles(): void {
  if (process.env.NODE_ENV !== 'development') return;
  
  console.group('Label Styles Debug');
  console.log('Breakpoint:', getCurrentBreakpoint());
  console.log('Reduced Motion:', prefersReducedMotion());
  console.log('High Contrast:', prefersHighContrast());
  console.log('Styles Loaded:', validateLabelStyles());
  console.groupEnd();
}