import { Router } from 'express';
import { RunbookController } from '../controllers/runbookController';

const router = Router();
const runbookController = new RunbookController();

/**
 * GET /api/runbooks
 * Holt alle Runbooks
 */
router.get('/', (req, res) => runbookController.getAllRunbooks(req, res));

/**
 * GET /api/runbooks/search
 * Sucht nach Runbooks
 * Query-Parameter:
 * - q: Suchbegriff (erforderlich)
 */
router.get('/search', (req, res) => runbookController.searchRunbooks(req, res));

/**
 * GET /api/runbooks/:id
 * Holt ein spezifisches Runbook
 */
router.get('/:id', (req, res) => runbookController.getRunbookById(req, res));

/**
 * POST /api/runbooks
 * Erstellt ein neues Runbook
 * Body: { title: string, content: string, affected_systems?: string[], tags?: string[] }
 */
router.post('/', (req, res) => runbookController.createRunbook(req, res));

/**
 * PUT /api/runbooks/:id
 * Aktualisiert ein bestehendes Runbook
 * Body: { title?: string, content?: string, affected_systems?: string[], tags?: string[] }
 */
router.put('/:id', (req, res) => runbookController.updateRunbook(req, res));

/**
 * DELETE /api/runbooks/:id
 * Löscht ein Runbook
 */
router.delete('/:id', (req, res) => runbookController.deleteRunbook(req, res));

export default router;