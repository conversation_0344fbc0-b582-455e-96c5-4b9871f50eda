"use strict";
/**
 * AI Security Middleware
 *
 * Provides additional security layers specifically for AI module endpoints.
 * Includes rate limiting, input validation, and audit logging.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAIRateLimiter = createAIRateLimiter;
exports.validateAIFeatureAccess = validateAIFeatureAccess;
exports.validateAIInput = validateAIInput;
exports.logAISecurityEvent = logAISecurityEvent;
exports.createAISecurityMiddleware = createAISecurityMiddleware;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
// Rate limiting configurations for different AI features
const AI_RATE_LIMITS = {
    rag_query: { windowMs: 15 * 60 * 1000, max: 100 }, // 100 requests per 15 minutes
    cutting_optimization: { windowMs: 60 * 60 * 1000, max: 50 }, // 50 requests per hour
    inventory_intelligence: { windowMs: 60 * 60 * 1000, max: 30 }, // 30 requests per hour
    process_optimization: { windowMs: 60 * 60 * 1000, max: 20 }, // 20 requests per hour
    predictive_analytics: { windowMs: 60 * 60 * 1000, max: 40 }, // 40 requests per hour
    warehouse_optimization: { windowMs: 60 * 60 * 1000, max: 25 }, // 25 requests per hour
    supply_chain_optimization: { windowMs: 60 * 60 * 1000, max: 15 }, // 15 requests per hour
    automated_reporting: { windowMs: 60 * 60 * 1000, max: 10 }, // 10 requests per hour
    default: { windowMs: 15 * 60 * 1000, max: 50 } // Default limit
};
// AI Feature permissions mapping
const AI_FEATURE_PERMISSIONS = {
    rag_query: ['Besucher', 'Benutzer', 'Administrator'],
    cutting_optimization: ['Benutzer', 'Administrator'],
    inventory_intelligence: ['Benutzer', 'Administrator'],
    process_optimization: ['Benutzer', 'Administrator'],
    predictive_analytics: ['Benutzer', 'Administrator'],
    warehouse_optimization: ['Benutzer', 'Administrator'],
    supply_chain_optimization: ['Administrator'],
    automated_reporting: ['Benutzer', 'Administrator'],
    ai_configuration: ['Administrator']
};
// Input validation patterns
const SECURITY_PATTERNS = [
    {
        name: 'sql_injection',
        pattern: /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)|(['";])/i,
        riskLevel: 'high'
    },
    {
        name: 'script_injection',
        pattern: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        riskLevel: 'high'
    },
    {
        name: 'prompt_injection',
        pattern: /(\bignore\b.*\binstructions?\b)|(\bsystem\b.*\bprompt\b)|(\brole\b.*\bassistant\b)/i,
        riskLevel: 'medium'
    },
    {
        name: 'excessive_length',
        pattern: /^.{10000,}$/,
        riskLevel: 'medium'
    }
];
/**
 * Creates rate limiter for specific AI feature
 */
function createAIRateLimiter(feature) {
    const config = AI_RATE_LIMITS[feature] || AI_RATE_LIMITS.default;
    return (0, express_rate_limit_1.default)({
        windowMs: config.windowMs,
        max: config.max,
        message: {
            success: false,
            error: 'Rate limit exceeded',
            message: `Zu viele Anfragen für ${feature}. Bitte versuchen Sie es später erneut.`,
            code: 'RATE_LIMIT_EXCEEDED'
        },
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req) => {
            var _a;
            // Use user ID if available, otherwise fall back to IP
            const authReq = req;
            return ((_a = authReq.user) === null || _a === void 0 ? void 0 : _a.apiKey) || req.ip || 'anonymous';
        }
    });
}
/**
 * Validates AI feature access based on user roles
 */
function validateAIFeatureAccess(feature) {
    return (req, res, next) => {
        try {
            // Extract user information from JWT token
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                return res.status(401).json({
                    success: false,
                    error: 'Authentication required',
                    message: 'JWT Token erforderlich für KI-Funktionen',
                    code: 'MISSING_JWT_TOKEN'
                });
            }
            const token = authHeader.substring(7);
            // Decode JWT token (simplified - in production use proper JWT verification)
            let userInfo;
            try {
                const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
                userInfo = {
                    userId: payload.userId || payload.id,
                    username: payload.username,
                    roles: payload.roles || []
                };
            }
            catch (error) {
                return res.status(401).json({
                    success: false,
                    error: 'Invalid token',
                    message: 'Ungültiger JWT Token',
                    code: 'INVALID_JWT_TOKEN'
                });
            }
            // Check if user has required role for this feature
            const requiredRoles = AI_FEATURE_PERMISSIONS[feature];
            if (!requiredRoles) {
                return res.status(400).json({
                    success: false,
                    error: 'Unknown feature',
                    message: `Unbekannte KI-Funktion: ${feature}`,
                    code: 'UNKNOWN_AI_FEATURE'
                });
            }
            const hasAccess = requiredRoles.some(role => userInfo.roles.includes(role));
            if (!hasAccess) {
                // Log access denial
                console.warn(`[AI Security] Access denied for user ${userInfo.username} to feature ${feature}. Required roles: ${requiredRoles.join(', ')}, User roles: ${userInfo.roles.join(', ')}`);
                return res.status(403).json({
                    success: false,
                    error: 'Insufficient permissions',
                    message: `Keine Berechtigung für KI-Funktion: ${feature}. Benötigte Rollen: ${requiredRoles.join(', ')}`,
                    code: 'INSUFFICIENT_AI_PERMISSIONS'
                });
            }
            // Add AI security context to request
            req.aiSecurity = {
                userId: userInfo.userId,
                username: userInfo.username,
                roles: userInfo.roles,
                feature,
                riskLevel: 'low'
            };
            next();
        }
        catch (error) {
            console.error('[AI Security] Error validating feature access:', error);
            return res.status(500).json({
                success: false,
                error: 'Security validation error',
                message: 'Fehler bei der Sicherheitsvalidierung',
                code: 'AI_SECURITY_ERROR'
            });
        }
    };
}
/**
 * Validates and sanitizes AI input
 */
function validateAIInput(req, res, next) {
    var _a, _b;
    try {
        const { input, query, prompt } = req.body;
        const textInput = input || query || prompt;
        if (!textInput || typeof textInput !== 'string') {
            return res.status(400).json({
                success: false,
                error: 'Invalid input',
                message: 'Gültige Texteingabe erforderlich',
                code: 'INVALID_AI_INPUT'
            });
        }
        // Check input against security patterns
        const violations = [];
        let riskLevel = 'low';
        for (const pattern of SECURITY_PATTERNS) {
            if (pattern.pattern.test(textInput)) {
                violations.push(pattern.name);
                if (pattern.riskLevel === 'high') {
                    riskLevel = 'high';
                }
                else if (pattern.riskLevel === 'medium' && riskLevel === 'low') {
                    riskLevel = 'medium';
                }
            }
        }
        // Block high-risk inputs
        if (riskLevel === 'high') {
            console.warn(`[AI Security] High-risk input blocked from user ${(_a = req.aiSecurity) === null || _a === void 0 ? void 0 : _a.username}: ${violations.join(', ')}`);
            return res.status(400).json({
                success: false,
                error: 'Dangerous input detected',
                message: `Eingabe blockiert: ${violations.join(', ')}`,
                code: 'DANGEROUS_AI_INPUT',
                violations
            });
        }
        // Update risk level in security context
        if (req.aiSecurity) {
            req.aiSecurity.riskLevel = riskLevel;
        }
        // Log medium-risk inputs for monitoring
        if (riskLevel === 'medium') {
            console.warn(`[AI Security] Medium-risk input from user ${(_b = req.aiSecurity) === null || _b === void 0 ? void 0 : _b.username}: ${violations.join(', ')}`);
        }
        // Sanitize input
        const sanitizedInput = textInput
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/[<>]/g, '')
            .replace(/['";]/g, '')
            .trim();
        // Update request body with sanitized input
        if (req.body.input)
            req.body.input = sanitizedInput;
        if (req.body.query)
            req.body.query = sanitizedInput;
        if (req.body.prompt)
            req.body.prompt = sanitizedInput;
        next();
    }
    catch (error) {
        console.error('[AI Security] Error validating input:', error);
        return res.status(500).json({
            success: false,
            error: 'Input validation error',
            message: 'Fehler bei der Eingabevalidierung',
            code: 'AI_INPUT_VALIDATION_ERROR'
        });
    }
}
/**
 * Logs AI security events for audit purposes
 */
function logAISecurityEvent(req, res, next) {
    // Store original end function
    const originalEnd = res.end;
    // Override end function to log after response
    res.end = function (chunk, encoding, cb) {
        var _a, _b, _c, _d;
        // Log the security event
        const logEntry = {
            timestamp: new Date().toISOString(),
            userId: (_a = req.aiSecurity) === null || _a === void 0 ? void 0 : _a.userId,
            username: (_b = req.aiSecurity) === null || _b === void 0 ? void 0 : _b.username,
            feature: (_c = req.aiSecurity) === null || _c === void 0 ? void 0 : _c.feature,
            method: req.method,
            path: req.path,
            statusCode: res.statusCode,
            riskLevel: (_d = req.aiSecurity) === null || _d === void 0 ? void 0 : _d.riskLevel,
            userAgent: req.headers['user-agent'],
            ip: req.ip
        };
        console.log('[AI Security Audit]', JSON.stringify(logEntry));
        // Call original end function
        return originalEnd.call(this, chunk, encoding, cb);
    };
    next();
}
/**
 * Complete AI security middleware chain
 */
function createAISecurityMiddleware(feature) {
    return [
        createAIRateLimiter(feature),
        validateAIFeatureAccess(feature),
        validateAIInput,
        logAISecurityEvent
    ];
}
