# Qwen Code Context: SFM Electron Dashboard

## Project Overview

This project is an **Electron-based desktop application** called "JOZI1 Lapp Dashboard". Its primary function is to visualize logistics and production data through interactive dashboards and tables. It is built with a modern web technology stack, combining Electron for the desktop wrapper, React for the frontend UI, and Express for the backend API.

### Core Technologies

- **Desktop Framework**: Electron
- **Build Tool**: Vite with TypeScript
- **Frontend**: React, Tailwind CSS, shadcn/ui, Recharts
- **Backend**: Express.js
- **Databases**: SQLite (better-sqlite3) managed by Prisma
- **State Management**: React Query (TanStack)
- **Routing**: TanStack Router
- **Languages**: TypeScript

## Project Structure

The project is organized into two main parts:

1.  **Frontend (`src/`)**: Contains all the React application code, including components, pages, routing, UI elements (shadcn/ui), and internationalization.
2.  **Backend (`backend/`)**: A separate Express.js server that provides the API endpoints, interacts with the databases, and handles business logic. It also uses Prisma for database access.

Key directories:
- `src/app/`, `src/components/`, `src/pages/`, `src/routes/`, etc.: Frontend source code.
- `src/main.ts`: Electron main process entry point, responsible for creating the window and starting the backend server.
- `backend/src/`: Backend source code.
- `backend/src/server.ts`: Express server entry point.
- `backend/prisma-sfm-dashboard/`, `backend/prisma-rag/`: Prisma schemas for the main dashboard database and a separate RAG (Retrieval-Augmented Generation) knowledge database.

## Building and Running

### Prerequisites

- Node.js (version >= 20.12, as specified in `package.json`)

### Development

1.  **Install Dependencies**:
    ```bash
    npm install
    ```
    This also automatically prepares the backend (installs its dependencies and generates Prisma client code).

2.  **Start Development Server**:
    ```bash
    npm run start
    ```
    or
    ```bash
    npm run dev
    ```
    This command starts both the Electron frontend and the Express backend in development mode. The backend uses `nodemon` for hot-reloading.

### Production

1.  **Package Application**: Creates a platform-specific executable bundle.
    ```bash
    npm run package
    ```

2.  **Make Distributables**: Creates platform-specific distribution packages (e.g., `.exe`, `.dmg`).
    ```bash
    npm run make
    ```

3.  **Build Portable Executable**: Creates a portable Windows executable.
    ```bash
    npm run build:portable
    ```

### Testing

- **Unit Tests (Vitest)**:
    ```bash
    npm run test
    ```
- **End-to-End Tests (Playwright)**:
    ```bash
    npm run test:e2e
    ```
- **All Tests**:
    ```bash
    npm run test:all
    ```

## Development Conventions

- **Language**: Primarily German for UI and logs, but English is used for code structure, comments, and some technical terms.
- **Architecture**:
  - The Electron main process (`src/main.ts`) is responsible for spawning and managing the backend server process.
  - The frontend communicates with the backend via HTTP API calls.
  - The backend is structured with Express routes, middleware for security and validation, and Prisma for database interactions.
  - Frontend routing is handled by TanStack Router.
- **Security**:
  - API endpoints are protected by an authentication middleware using a secret API key in production.
  - Rate limiting is applied.
  - Helmet.js is used for HTTP security headers (configured for Electron compatibility).
- **Configuration**:
  - Environment variables are used for configuration (API keys, ports, etc.). See `.env.example` and `backend/.env.example`.
  - The main process configures portable paths when the app is not in development mode.
- **Data**:
  - SQLite databases are used for persistence.
  - Prisma ORM is used for database schema definition and data access.
  - There are separate databases for the main dashboard data and for RAG (Retrieval-Augmented Generation) knowledge.
