"use strict";
/**
 * Prüft welche Daten in der Datenbank verfügbar sind
 */
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
async function checkAvailableData() {
    console.log('🔍 Checking available data in database...\n');
    const prisma = new client_1.PrismaClient();
    try {
        // Check Dispatch Data
        console.log('📊 DISPATCH DATA:');
        const dispatchData = await prisma.dispatchData.findMany({
            select: {
                datum: true,
                servicegrad: true,
                produzierte_tonnagen: true
            },
            where: {
                datum: { not: null },
                servicegrad: { not: null }
            },
            orderBy: { datum: 'desc' },
            take: 10
        });
        if (dispatchData.length > 0) {
            console.log(`  Found ${dispatchData.length} dispatch records:`);
            dispatchData.forEach((record, index) => {
                console.log(`    ${index + 1}. ${record.datum} - Servicegrad: ${record.servicegrad}, Tonnage: ${record.produzierte_tonnagen}`);
            });
            const dateRange = await prisma.dispatchData.aggregate({
                where: { datum: { not: null } },
                _min: { datum: true },
                _max: { datum: true },
                _count: { datum: true }
            });
            console.log(`  Date range: ${dateRange._min.datum} to ${dateRange._max.datum}`);
            console.log(`  Total records: ${dateRange._count.datum}`);
        }
        else {
            console.log('  ❌ No dispatch data found');
        }
        console.log('\n📊 STÖRUNGEN DATA:');
        const stoerungenData = await prisma.stoerungen.findMany({
            select: {
                created_at: true,
                status: true,
                severity: true,
                title: true
            },
            orderBy: { created_at: 'desc' },
            take: 10
        });
        if (stoerungenData.length > 0) {
            console.log(`  Found ${stoerungenData.length} störungen records:`);
            stoerungenData.forEach((record, index) => {
                console.log(`    ${index + 1}. ${record.created_at} - ${record.title} - Status: ${record.status}, Severity: ${record.severity}`);
            });
        }
        else {
            console.log('  ❌ No störungen data found');
        }
        console.log('\n📊 CUTTING DATA:');
        const cuttingData = await prisma.ablaengerei.findMany({
            select: {
                Datum: true,
                cutGesamt: true,
                cutTT: true
            },
            where: {
                Datum: { not: null }
            },
            orderBy: { Datum: 'desc' },
            take: 10
        });
        if (cuttingData.length > 0) {
            console.log(`  Found ${cuttingData.length} cutting records:`);
            cuttingData.forEach((record, index) => {
                console.log(`    ${index + 1}. ${record.Datum} - Gesamt: ${record.cutGesamt}, TT: ${record.cutTT}`);
            });
        }
        else {
            console.log('  ❌ No cutting data found');
        }
        // Check for specific date 17.04.2025
        console.log('\n🎯 CHECKING SPECIFIC DATE: 2025-04-17');
        const specificDispatch = await prisma.dispatchData.findMany({
            where: {
                datum: '2025-04-17'
            },
            select: {
                datum: true,
                servicegrad: true,
                produzierte_tonnagen: true
            }
        });
        if (specificDispatch.length > 0) {
            console.log(`  ✅ Found data for 2025-04-17:`);
            specificDispatch.forEach(record => {
                console.log(`    Servicegrad: ${record.servicegrad}, Tonnage: ${record.produzierte_tonnagen}`);
            });
        }
        else {
            console.log(`  ❌ No data found for 2025-04-17`);
        }
        // Check for dates around April 2025
        console.log('\n🗓️ CHECKING APRIL 2025 RANGE:');
        const aprilData = await prisma.dispatchData.findMany({
            where: {
                datum: {
                    gte: '2025-04-01',
                    lte: '2025-04-30'
                }
            },
            select: {
                datum: true,
                servicegrad: true
            },
            orderBy: { datum: 'asc' }
        });
        if (aprilData.length > 0) {
            console.log(`  ✅ Found ${aprilData.length} records in April 2025:`);
            aprilData.forEach(record => {
                console.log(`    ${record.datum}: ${record.servicegrad}`);
            });
        }
        else {
            console.log(`  ❌ No data found for April 2025`);
        }
    }
    catch (error) {
        console.error('❌ Error checking data:', error);
    }
    finally {
        await prisma.$disconnect();
    }
    console.log('\n✅ Data check completed!');
}
// Run the check
checkAvailableData().catch(console.error);
