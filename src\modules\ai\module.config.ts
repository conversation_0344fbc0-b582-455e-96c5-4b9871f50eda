import { ModuleConfig } from '@/types/module';

export const aiModuleConfig: ModuleConfig = {
  id: 'ai',
  name: 'ai',
  displayName: 'KI-Assistent',
  description: 'KI-gestützter Chatbot und intelligente Datenanalyse',
  icon: 'Bot',
  baseRoute: '/modules/ai',
  requiredRoles: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Administrator'],
  isEnabled: true,
  pages: [
    {
      id: 'rag',
      name: 'JASZ AI Wissensdatenbank',
      route: '/modules/ai/rag-management',
      component: 'RAGManagementPage'
    },
    {
      id: 'analysis',
      name: 'Datenanalyse',
      route: '/modules/ai/analysis',
      component: 'AnalysisPage'
    },
    {
      id: 'cutting-optimization',
      name: 'Schnittoptimierung',
      route: '/modules/ai/cutting-optimization',
      component: 'CuttingOptimizationPage',
      requiredRoles: ['<PERSON><PERSON><PERSON>', 'Administrator']
    },
    {
      id: 'inventory-intelligence',
      name: 'Lager-Intelligenz',
      route: '/modules/ai/inventory-intelligence',
      component: 'InventoryIntelligencePage',
      requiredRoles: ['<PERSON><PERSON><PERSON>', 'Administrator']
    },
    {
      id: 'process-optimization',
      name: 'Prozessoptimierung',
      route: '/modules/ai/process-optimization',
      component: 'ProcessOptimizationPage',
      requiredRoles: ['Benutzer', 'Administrator']
    },
    {
      id: 'predictive-analytics',
      name: 'Vorhersageanalyse',
      route: '/modules/ai/predictive-analytics',
      component: 'PredictiveAnalyticsPage',
      requiredRoles: ['Benutzer', 'Administrator']
    },
    {
      id: 'warehouse-optimization',
      name: 'Lageroptimierung',
      route: '/modules/ai/warehouse-optimization',
      component: 'WarehouseOptimizationPage',
      requiredRoles: ['Benutzer', 'Administrator']
    },
    {
      id: 'supply-chain-optimization',
      name: 'Lieferkettenoptimierung',
      route: '/modules/ai/supply-chain-optimization',
      component: 'SupplyChainOptimizationPage',
      requiredRoles: ['Administrator']
    },
    {
      id: 'automated-reporting',
      name: 'Automatisierte Berichte',
      route: '/modules/ai/automated-reporting',
      component: 'AutomatedReportingPage',
      requiredRoles: ['Benutzer', 'Administrator']
    },
    {
      id: 'security-dashboard',
      name: 'Sicherheitsdashboard',
      route: '/modules/ai/security',
      component: 'AISecurityDashboard',
      requiredRoles: ['Administrator']
    }
  ],
  security: {
    requiresAuthentication: true,
    enableInputValidation: true,
    enableAuditLogging: true,
    rateLimiting: {
      enabled: true,
      defaultLimit: 50,
      windowMs: 15 * 60 * 1000 // 15 minutes
    },
    apiKeyValidation: {
      enabled: true,
      requiredServices: ['openrouter']
    }
  }
};