"use strict";
/**
 * Test für spezifische Datumsverarbeitung
 *
 * Testet die erweiterte parseTimeRange Funktionalität für spezifische Datumsangaben
 */
Object.defineProperty(exports, "__esModule", { value: true });
const data_enrichment_service_1 = require("../services/data-enrichment.service");
const client_1 = require("@prisma/client");
describe('Specific Date Parsing Tests', () => {
    let dataEnrichmentService;
    let prisma;
    beforeAll(async () => {
        prisma = new client_1.PrismaClient();
        dataEnrichmentService = new data_enrichment_service_1.DataEnrichmentService(prisma);
    });
    afterAll(async () => {
        await prisma.$disconnect();
    });
    describe('parseTimeRange - Specific Dates', () => {
        test('should parse German date format DD.MM.YYYY', () => {
            const result = dataEnrichmentService.parseTimeRange('Servicegrad vom 17.04.2025');
            expect(result).toBeDefined();
            expect(result === null || result === void 0 ? void 0 : result.startDate).toBe('2025-04-17');
            expect(result === null || result === void 0 ? void 0 : result.endDate).toBe('2025-04-17');
        });
        test('should parse German date format DD.MM.YY', () => {
            const result = dataEnrichmentService.parseTimeRange('Daten vom 17.04.25');
            expect(result).toBeDefined();
            expect(result === null || result === void 0 ? void 0 : result.startDate).toBe('2025-04-17');
            expect(result === null || result === void 0 ? void 0 : result.endDate).toBe('2025-04-17');
        });
        test('should parse ISO date format YYYY-MM-DD', () => {
            const result = dataEnrichmentService.parseTimeRange('Show data from 2025-04-17');
            expect(result).toBeDefined();
            expect(result === null || result === void 0 ? void 0 : result.startDate).toBe('2025-04-17');
            expect(result === null || result === void 0 ? void 0 : result.endDate).toBe('2025-04-17');
        });
        test('should parse slash date format DD/MM/YYYY', () => {
            const result = dataEnrichmentService.parseTimeRange('Data from 17/04/2025');
            expect(result).toBeDefined();
            expect(result === null || result === void 0 ? void 0 : result.startDate).toBe('2025-04-17');
            expect(result === null || result === void 0 ? void 0 : result.endDate).toBe('2025-04-17');
        });
        test('should parse month and year', () => {
            const result = dataEnrichmentService.parseTimeRange('Daten für April 2025');
            expect(result).toBeDefined();
            expect(result === null || result === void 0 ? void 0 : result.startDate).toBe('2025-04-01');
            expect(result === null || result === void 0 ? void 0 : result.endDate).toBe('2025-04-30');
        });
        test('should parse German month names', () => {
            const result = dataEnrichmentService.parseTimeRange('Störungen im März 2025');
            expect(result).toBeDefined();
            expect(result === null || result === void 0 ? void 0 : result.startDate).toBe('2025-03-01');
            expect(result === null || result === void 0 ? void 0 : result.endDate).toBe('2025-03-31');
        });
        test('should parse date ranges', () => {
            const result = dataEnrichmentService.parseTimeRange('von 15.04.2025 bis 20.04.2025');
            expect(result).toBeDefined();
            expect(result === null || result === void 0 ? void 0 : result.startDate).toBe('2025-04-15');
            expect(result === null || result === void 0 ? void 0 : result.endDate).toBe('2025-04-20');
        });
        test('should parse "last X days" pattern', () => {
            const today = new Date();
            const expectedStart = new Date(today);
            expectedStart.setDate(expectedStart.getDate() - 7);
            const result = dataEnrichmentService.parseTimeRange('letzte 7 tage');
            expect(result).toBeDefined();
            expect(result === null || result === void 0 ? void 0 : result.startDate).toBe(expectedStart.toISOString().split('T')[0]);
            expect(result === null || result === void 0 ? void 0 : result.endDate).toBe(today.toISOString().split('T')[0]);
        });
        test('should handle invalid dates gracefully', () => {
            const result = dataEnrichmentService.parseTimeRange('32.13.2025'); // Invalid date
            expect(result).toBeUndefined();
        });
        test('should prioritize specific dates over relative terms', () => {
            const result = dataEnrichmentService.parseTimeRange('heute zeige mir Daten vom 17.04.2025');
            expect(result).toBeDefined();
            expect(result === null || result === void 0 ? void 0 : result.startDate).toBe('2025-04-17');
            expect(result === null || result === void 0 ? void 0 : result.endDate).toBe('2025-04-17');
        });
    });
    describe('Intent Recognition with Specific Dates', () => {
        test('should recognize dispatch intent with specific date', () => {
            var _a;
            const intents = dataEnrichmentService.parseIntent('Servicegrad vom 17.04.2025');
            expect(intents).toHaveLength(1);
            expect(intents[0].type).toBe('dispatch');
            expect(intents[0].timeRange).toBeDefined();
            expect((_a = intents[0].timeRange) === null || _a === void 0 ? void 0 : _a.startDate).toBe('2025-04-17');
            expect(intents[0].keywords).toContain('servicegrad');
        });
        test('should recognize störungen intent with specific date', () => {
            var _a;
            const intents = dataEnrichmentService.parseIntent('Störungen am 17.04.2025');
            expect(intents).toHaveLength(1);
            expect(intents[0].type).toBe('stoerungen');
            expect(intents[0].timeRange).toBeDefined();
            expect((_a = intents[0].timeRange) === null || _a === void 0 ? void 0 : _a.startDate).toBe('2025-04-17');
        });
        test('should recognize cutting intent with date range', () => {
            var _a, _b;
            const intents = dataEnrichmentService.parseIntent('Schnittdaten von 15.04.2025 bis 20.04.2025');
            expect(intents).toHaveLength(1);
            expect(intents[0].type).toBe('cutting');
            expect(intents[0].timeRange).toBeDefined();
            expect((_a = intents[0].timeRange) === null || _a === void 0 ? void 0 : _a.startDate).toBe('2025-04-15');
            expect((_b = intents[0].timeRange) === null || _b === void 0 ? void 0 : _b.endDate).toBe('2025-04-20');
        });
    });
    describe('End-to-End Date Processing', () => {
        test('should enrich context with specific date query', async () => {
            var _a;
            const result = await dataEnrichmentService.enrichChatContext('Wie war der Servicegrad am 17.04.2025?');
            expect(result).toBeDefined();
            expect(result.detectedIntents).toHaveLength(1);
            expect(result.detectedIntents[0].type).toBe('dispatch');
            expect((_a = result.detectedIntents[0].timeRange) === null || _a === void 0 ? void 0 : _a.startDate).toBe('2025-04-17');
            // Should attempt to query database with specific date
            // Note: Actual data availability depends on database content
        });
        test('should handle multiple date formats in same query', async () => {
            var _a;
            const result = await dataEnrichmentService.enrichChatContext('Vergleiche Servicegrad vom 17.04.2025 mit 2025-04-18');
            expect(result).toBeDefined();
            expect(result.detectedIntents).toHaveLength(1);
            // Should detect the first date format found
            expect((_a = result.detectedIntents[0].timeRange) === null || _a === void 0 ? void 0 : _a.startDate).toBe('2025-04-17');
        });
    });
    describe('Error Handling', () => {
        test('should handle malformed dates', () => {
            const result = dataEnrichmentService.parseTimeRange('Daten vom 99.99.9999');
            expect(result).toBeUndefined();
        });
        test('should handle empty date strings', () => {
            const result = dataEnrichmentService.parseTimeRange('Daten vom ');
            expect(result).toBeUndefined();
        });
        test('should handle ambiguous date formats', () => {
            // This could be 01/02/2025 (Jan 2 or Feb 1 depending on locale)
            const result = dataEnrichmentService.parseTimeRange('01/02/25');
            // Should either parse correctly or return undefined, but not crash
            expect(typeof result === 'object' || result === undefined).toBe(true);
        });
    });
});
/**
 * Integration test to verify the complete flow works
 */
describe('Date Parsing Integration', () => {
    let dataEnrichmentService;
    let prisma;
    beforeAll(async () => {
        prisma = new client_1.PrismaClient();
        dataEnrichmentService = new data_enrichment_service_1.DataEnrichmentService(prisma);
    });
    afterAll(async () => {
        await prisma.$disconnect();
    });
    test('should process real-world date queries', async () => {
        var _a, _b;
        const testQueries = [
            'Servicegrad vom 17.04.2025',
            'Störungen am 2025-04-17',
            'Schnittdaten für April 2025',
            'Performance von 15.04.2025 bis 20.04.2025',
            'Daten der letzten 7 Tage',
            'Heute zeige mir den Status'
        ];
        for (const query of testQueries) {
            console.log(`Testing query: "${query}"`);
            const result = await dataEnrichmentService.enrichChatContext(query);
            expect(result).toBeDefined();
            expect(result.originalMessage).toBe(query);
            if (result.detectedIntents.length > 0) {
                const intent = result.detectedIntents[0];
                console.log(`  - Intent: ${intent.type}`);
                console.log(`  - Time range: ${(_a = intent.timeRange) === null || _a === void 0 ? void 0 : _a.startDate} to ${(_b = intent.timeRange) === null || _b === void 0 ? void 0 : _b.endDate}`);
                console.log(`  - Keywords: ${intent.keywords.join(', ')}`);
            }
        }
    });
});
