/**
 * Report Scheduler Component
 * 
 * Component for managing scheduled report generation and automation
 */

import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar, 
  Clock, 
  Play,
  Pause,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Users,
  FileText,
  Info
} from 'lucide-react';
import type { ReportTemplate } from '@/types/reporting';

interface ReportSchedulerProps {
  templates: ReportTemplate[];
  onUpdateTemplate: (templateId: string, updates: Partial<ReportTemplate>) => Promise<void>;
  loading: boolean;
}

/**
 * Report Scheduler Component
 */
export const ReportScheduler: React.FC<ReportSchedulerProps> = ({
  templates,
  onUpdateTemplate,
  loading
}) => {
  const [updatingTemplate, setUpdatingTemplate] = useState<string | null>(null);

  /**
   * Get scheduled templates
   */
  const scheduledTemplates = useMemo(() => {
    return templates.filter(template => template.schedule);
  }, [templates]);

  /**
   * Get active scheduled templates
   */
  const activeScheduledTemplates = useMemo(() => {
    return templates.filter(template => template.schedule?.isActive);
  }, [templates]);

  /**
   * Handle schedule toggle
   */
  const handleToggleSchedule = useCallback(async (template: ReportTemplate) => {
    if (!template.schedule) return;

    try {
      setUpdatingTemplate(template.id);
      await onUpdateTemplate(template.id, {
        schedule: {
          ...template.schedule,
          isActive: !template.schedule.isActive
        }
      });
    } catch (error) {
      console.error('Failed to toggle schedule:', error);
    } finally {
      setUpdatingTemplate(null);
    }
  }, [onUpdateTemplate]);

  /**
   * Get frequency display name
   */
  const getFrequencyDisplayName = (frequency: string) => {
    const frequencies: Record<string, string> = {
      'daily': 'Täglich',
      'weekly': 'Wöchentlich',
      'monthly': 'Monatlich',
      'quarterly': 'Vierteljährlich'
    };
    return frequencies[frequency] || frequency;
  };

  /**
   * Get day of week name
   */
  const getDayOfWeekName = (day: number) => {
    const days = ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag'];
    return days[day] || `Tag ${day}`;
  };

  /**
   * Calculate next execution time
   */
  const getNextExecutionTime = (template: ReportTemplate) => {
    if (!template.schedule || !template.schedule.isActive) return null;

    const now = new Date();
    let nextExecution = new Date();

    switch (template.schedule.frequency) {
      case 'daily':
        nextExecution.setDate(now.getDate() + 1);
        break;
      case 'weekly':
        const daysUntilNext = (template.schedule.dayOfWeek || 1) - now.getDay();
        nextExecution.setDate(now.getDate() + (daysUntilNext <= 0 ? daysUntilNext + 7 : daysUntilNext));
        break;
      case 'monthly':
        nextExecution.setMonth(now.getMonth() + 1);
        nextExecution.setDate(template.schedule.dayOfMonth || 1);
        break;
      case 'quarterly':
        nextExecution.setMonth(now.getMonth() + 3);
        nextExecution.setDate(1);
        break;
    }

    // Set time
    const [hours, minutes] = template.schedule.time.split(':').map(Number);
    nextExecution.setHours(hours, minutes, 0, 0);

    return nextExecution;
  };

  /**
   * Get schedule status color
   */
  const getScheduleStatusColor = (template: ReportTemplate) => {
    if (!template.schedule) return 'gray';
    if (!template.isActive) return 'gray';
    if (!template.schedule.isActive) return 'yellow';
    return 'green';
  };

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Geplante Vorlagen</p>
                <p className="text-2xl font-bold text-blue-600">
                  {scheduledTemplates.length}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Aktive Zeitpläne</p>
                <p className="text-2xl font-bold text-green-600">
                  {activeScheduledTemplates.length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Inaktive Zeitpläne</p>
                <p className="text-2xl font-bold text-gray-600">
                  {scheduledTemplates.length - activeScheduledTemplates.length}
                </p>
              </div>
              <XCircle className="h-8 w-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Scheduled Templates */}
      {loading ? (
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  <div className="flex space-x-2">
                    <div className="h-6 bg-gray-200 rounded w-16"></div>
                    <div className="h-6 bg-gray-200 rounded w-20"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : scheduledTemplates.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Keine geplanten Berichte
            </h3>
            <p className="text-gray-600 mb-4">
              Konfigurieren Sie Zeitpläne für Ihre Berichtsvorlagen, um automatische Generierung zu aktivieren.
            </p>
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Gehen Sie zu den Vorlagen und bearbeiten Sie eine Vorlage, um einen Zeitplan hinzuzufügen.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {scheduledTemplates.map((template) => {
            const nextExecution = getNextExecutionTime(template);
            const statusColor = getScheduleStatusColor(template);
            
            return (
              <Card key={template.id}>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-lg flex items-center">
                        <FileText className="h-5 w-5 mr-2" />
                        {template.name}
                      </CardTitle>
                      <CardDescription className="mt-1">
                        {template.description}
                      </CardDescription>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <Badge 
                        variant={template.isActive ? "default" : "secondary"}
                      >
                        {template.isActive ? 'Vorlage Aktiv' : 'Vorlage Inaktiv'}
                      </Badge>
                      
                      <Badge 
                        variant={
                          statusColor === 'green' ? "default" : 
                          statusColor === 'yellow' ? "secondary" : "outline"
                        }
                      >
                        {template.schedule?.isActive ? (
                          <>
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Zeitplan Aktiv
                          </>
                        ) : (
                          <>
                            <XCircle className="h-3 w-3 mr-1" />
                            Zeitplan Inaktiv
                          </>
                        )}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  {template.schedule && (
                    <div className="space-y-4">
                      {/* Schedule Details */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="font-medium text-gray-600">Häufigkeit</p>
                          <p>{getFrequencyDisplayName(template.schedule.frequency)}</p>
                        </div>
                        
                        <div>
                          <p className="font-medium text-gray-600">Uhrzeit</p>
                          <p>{template.schedule.time} Uhr</p>
                        </div>
                        
                        {template.schedule.frequency === 'weekly' && template.schedule.dayOfWeek !== undefined && (
                          <div>
                            <p className="font-medium text-gray-600">Wochentag</p>
                            <p>{getDayOfWeekName(template.schedule.dayOfWeek)}</p>
                          </div>
                        )}
                        
                        {template.schedule.frequency === 'monthly' && template.schedule.dayOfMonth && (
                          <div>
                            <p className="font-medium text-gray-600">Tag des Monats</p>
                            <p>{template.schedule.dayOfMonth}.</p>
                          </div>
                        )}
                        
                        <div>
                          <p className="font-medium text-gray-600">Zeitzone</p>
                          <p>{template.schedule.timezone}</p>
                        </div>
                      </div>

                      {/* Next Execution */}
                      {nextExecution && template.schedule.isActive && template.isActive && (
                        <Alert>
                          <Clock className="h-4 w-4" />
                          <AlertDescription>
                            <strong>Nächste Ausführung:</strong> {nextExecution.toLocaleString('de-DE')}
                          </AlertDescription>
                        </Alert>
                      )}

                      {/* Recipients */}
                      {template.recipients && template.recipients.length > 0 && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 mb-2">
                            E-Mail-Empfänger ({template.recipients.length})
                          </p>
                          <div className="flex flex-wrap gap-2">
                            {template.recipients.slice(0, 3).map((email) => (
                              <Badge key={email} variant="outline" className="text-xs">
                                <Users className="h-3 w-3 mr-1" />
                                {email}
                              </Badge>
                            ))}
                            {template.recipients.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{template.recipients.length - 3} weitere
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Controls */}
                      <div className="flex items-center justify-between pt-2 border-t">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <Switch
                              id={`schedule-${template.id}`}
                              checked={template.schedule.isActive}
                              onCheckedChange={() => handleToggleSchedule(template)}
                              disabled={updatingTemplate === template.id || !template.isActive}
                            />
                            <label htmlFor={`schedule-${template.id}`} className="text-sm">
                              Zeitplan {template.schedule.isActive ? 'deaktivieren' : 'aktivieren'}
                            </label>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          {!template.isActive && (
                            <Alert className="inline-flex items-center p-2">
                              <AlertTriangle className="h-4 w-4 mr-2" />
                              <span className="text-xs">Vorlage ist inaktiv</span>
                            </Alert>
                          )}
                          
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={updatingTemplate === template.id}
                          >
                            <Settings className="h-4 w-4 mr-2" />
                            Bearbeiten
                          </Button>
                        </div>
                      </div>

                      {/* Warnings */}
                      {template.schedule.isActive && !template.isActive && (
                        <Alert className="border-yellow-200 bg-yellow-50">
                          <AlertTriangle className="h-4 w-4" />
                          <AlertDescription>
                            Der Zeitplan ist aktiv, aber die Vorlage ist deaktiviert. 
                            Aktivieren Sie die Vorlage, um automatische Berichte zu generieren.
                          </AlertDescription>
                        </Alert>
                      )}

                      {template.schedule.isActive && (!template.recipients || template.recipients.length === 0) && (
                        <Alert className="border-blue-200 bg-blue-50">
                          <Info className="h-4 w-4" />
                          <AlertDescription>
                            Keine E-Mail-Empfänger konfiguriert. Berichte werden generiert, aber nicht automatisch versendet.
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Schedule Summary */}
      {activeScheduledTemplates.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Zeitplan-Übersicht
            </CardTitle>
            <CardDescription>
              Übersicht über alle aktiven Berichtszeitpläne
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {activeScheduledTemplates.map((template) => {
                const nextExecution = getNextExecutionTime(template);
                return (
                  <div key={template.id} className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium">{template.name}</p>
                      <p className="text-sm text-gray-600">
                        {getFrequencyDisplayName(template.schedule!.frequency)} um {template.schedule!.time} Uhr
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {nextExecution ? nextExecution.toLocaleDateString('de-DE') : 'Unbekannt'}
                      </p>
                      <p className="text-xs text-gray-600">
                        {nextExecution ? nextExecution.toLocaleTimeString('de-DE', { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        }) : ''}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ReportScheduler;