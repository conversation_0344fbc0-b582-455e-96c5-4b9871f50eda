# SAP Workflow Integration

This module integrates the Python SAP automation workflow (`workflowSAP.py`) into the JOZI1 Lapp Dashboard.

## Overview

The SAP workflow automation handles five main processes:
1. **Servicegrad** - Service level data export
2. **Rückstandsliste** - Backlog list export  
3. **LX03 Lagertyp 240** - Storage type 240 bin status
4. **LX03 Lagertyp 200** - Storage type 200 bin status
5. **LX03 Lagertyp Rest** - Storage types 241-999 bin status

## Architecture

### Frontend Components

- **WorkflowPage.tsx** - Main page with tabs for different workflow views
- **WorkflowGrid.tsx** - Grid view of all SAP processes with execution controls
- **WorkflowPerformanceChart.tsx** - Performance analytics and charts
- **WorkflowLogViewer.tsx** - Log viewer with filtering capabilities

### Services & Hooks

- **workflowService.ts** - Frontend service for workflow operations
- **useWorkflows.ts** - React hook for workflow state management

### Backend Integration

- **workflow.routes.ts** - API endpoints for workflow execution
- **workflowSAP.py** - Python script for SAP automation

## Features

### Process Management
- View all SAP processes with current status
- Execute individual processes or all processes at once
- Real-time status updates during execution
- Process restart capabilities

### Performance Analytics
- Execution statistics and success rates
- Performance charts and trends
- Process duration tracking
- Historical data visualization

### Logging & Monitoring
- Comprehensive log viewer with filtering
- Real-time log streaming during execution
- Export logs to CSV
- Error tracking and reporting

## Usage

### Starting Workflows

1. Navigate to the Workflows page
2. Select the "SAP Workflows" tab
3. Click "Alle Prozesse ausführen" to run all processes
4. Or click individual "Prozess starten" buttons for specific processes

### Monitoring Progress

- Process cards show real-time status (Bereit, Läuft, Abgeschlossen, Fehler)
- Performance tab shows execution statistics
- Logs tab provides detailed execution logs

### Configuration

The Python script uses these configuration variables:
- `SAP_EXECUTABLE_PATH` - Path to SAP GUI executable
- `SAP_SYSTEM_ID` - SAP system identifier
- `SAP_CLIENT` - SAP client number
- `SAP_LANGUAGE` - SAP language setting

## API Endpoints

### POST /api/workflows/execute
Execute a specific SAP workflow process.

**Request:**
```json
{
  "processId": "servicegrad"
}
```

**Response:**
```json
{
  "success": true,
  "processId": "servicegrad",
  "exportPath": "\\\\path\\to\\exported\\file.xlsx",
  "logs": ["Process started...", "Export completed..."]
}
```

### GET /api/workflows/status
Get current status of all workflows.

### GET /api/workflows/config
Get workflow configuration settings.

## Error Handling

- Process timeouts after 30 minutes
- Comprehensive error logging
- Graceful failure handling
- User-friendly error messages in German

## Security

- Rate limiting on API endpoints
- Input validation and sanitization
- Secure process execution
- Log sanitization to prevent information leakage

## Dependencies

### Python Requirements
- `win32com.client` - SAP GUI automation
- `pandas` - Data processing
- `sqlite3` - Database operations
- `subprocess` - Process management

### Node.js Requirements
- Express.js backend
- React frontend with TypeScript
- TanStack Query for state management
- Recharts for data visualization

## Troubleshooting

### Common Issues

1. **SAP GUI not found**
   - Verify `SAP_EXECUTABLE_PATH` configuration
   - Ensure SAP GUI is installed and accessible

2. **Process timeout**
   - Check SAP system connectivity
   - Verify network access to export directories

3. **Export path not accessible**
   - Ensure network drive permissions
   - Verify export directory exists

4. **Python script errors**
   - Check Python environment and dependencies
   - Verify script permissions and file paths

### Debugging

- Enable detailed logging in development mode
- Check browser console for frontend errors
- Monitor backend logs for API issues
- Use the log viewer for process-specific debugging