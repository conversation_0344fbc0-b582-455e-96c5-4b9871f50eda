# SAP Workflow Scripts

## Übersicht

Dieses Verzeichnis enthält die Python-Skripte für die SAP-Workflow-Automatisierung. Die Skripte sind optimiert für die Backend-Integration und folgen modernen Architektur-Prinzipien.

## 📁 Dateistruktur

```
backend/scripts/workflows/
├── workflowSAP.py          # Hauptskript für SAP-Automatisierung
├── workflow_logger.py      # Strukturiertes Logging-System
├── test_logger.py          # Test-Skript für das Logging
├── config.json             # Konfigurationsdatei
└── README.md               # Diese Dokumentation
```

## 🚀 Verwendung

### Hauptskript ausführen
```bash
cd backend/scripts/workflows
python workflowSAP.py
```

### Logging-System testen
```bash
cd backend/scripts/workflows
python test_logger.py
```

## ⚙️ Konfiguration

Die Konfiguration erfolgt über `config.json`:

```json
{
  "sap": {
    "executable_path": "C:\\Program Files (x86)\\SAP\\FrontEnd\\SapGui\\sapshcut.exe",
    "system_id": "PS4",
    "client": "009",
    "language": "DE"
  },
  "database": {
    "path": "../../database/sap_datenbank.db"
  }
}
```

## 🔧 Features

### WorkflowLogger
- **Automatische DB-Pfad-Erkennung**: Keine manuellen Pfad-Konfigurationen nötig
- **Strukturierte Logs**: JSON-basierte Details für bessere Analyse
- **Performance-Indizes**: Optimierte Datenbankabfragen
- **Event-Typen**: Kategorisierte Log-Events für bessere Filterung

### SAP Workflow
- **Robuste Fehlerbehandlung**: Umfassende Try-Catch-Blöcke
- **Datei-Validierung**: Überprüfung der Export-Dateien
- **Automatische Pfade**: Backend-relative Pfad-Erkennung
- **Detailliertes Logging**: Jeder Schritt wird protokolliert

## 📊 Log-Kategorien

### Event-Typen
- `process_start` - Prozess-Start
- `process_complete` - Prozess-Abschluss
- `process_error` - Prozess-Fehler
- `sap_action` - SAP GUI-Aktionen
- `file_operation` - Datei-Operationen
- `database_operation` - Datenbank-Operationen

### Log-Level
- `info` - Allgemeine Informationen
- `warning` - Warnungen
- `error` - Fehler
- `debug` - Debug-Informationen

## 🔍 Monitoring

### Logs anzeigen
```python
from workflow_logger import WorkflowLogger

# Alle Logs
logs = WorkflowLogger.get_all_logs(limit=100)

# Workflow-spezifische Logs
logs = WorkflowLogger.get_logs_for_workflow("servicegrad", limit=50)
```

### Log-Archivierung
```python
# Logs werden NIEMALS gelöscht - nur archiviert und komprimiert
# Alle Logs bleiben dauerhaft erhalten für Audit-Zwecke
WorkflowLogger.get_all_logs(limit=1000)  # Lade ältere Logs
```

## 🛠️ Entwicklung

### Neuen Workflow hinzufügen
1. Erweitere `config.json` um neuen Prozess
2. Implementiere Prozess-Funktion in `workflowSAP.py`
3. Füge entsprechende Logging-Aufrufe hinzu
4. Teste mit `test_logger.py`

### Debugging
- Verwende `logger.debug()` für detaillierte Informationen
- Überprüfe die Konsolen-Ausgabe für sofortiges Feedback
- Analysiere die Datenbank-Logs für historische Daten

## 🔒 Sicherheit

- **Pfad-Validierung**: Alle Pfade werden validiert
- **SQL-Injection-Schutz**: Parametrisierte Queries
- **Fehler-Isolation**: Einzelne Prozess-Fehler stoppen nicht den gesamten Workflow
- **Logging-Fallback**: Bei DB-Fehlern wird auf Konsolen-Output zurückgegriffen

## 📈 Performance

- **Indizierte Datenbank**: Optimierte Abfragen durch Indizes
- **Batch-Operationen**: Effiziente Datenbank-Schreibvorgänge
- **Memory-Management**: Automatische Verbindungsschließung
- **Lazy-Loading**: Logs werden nur bei Bedarf geladen

## 🚨 Troubleshooting

### Häufige Probleme

#### SAP GUI nicht gefunden
```
Lösung: Überprüfe SAP_EXECUTABLE_PATH in config.json
```

#### Datenbank-Fehler
```
Lösung: Stelle sicher, dass backend/database/ Verzeichnis existiert
```

#### Python-Module fehlen
```bash
pip install pandas openpyxl pywin32
```

#### Berechtigungsfehler
```
Lösung: Führe als Administrator aus oder überprüfe Netzwerk-Berechtigungen
```

## 📞 Support

Bei Problemen:
1. Überprüfe die Konsolen-Ausgabe
2. Analysiere die Datenbank-Logs
3. Teste mit `test_logger.py`
4. Überprüfe die Konfiguration in `config.json`

---

*Diese Skripte sind Teil der JOZI1 Lapp Dashboard Backend-Infrastruktur.*