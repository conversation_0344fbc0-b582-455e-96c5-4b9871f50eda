{"name": "Documentation Sync", "description": "Updates documentation when source files change", "trigger": {"type": "fileChange", "patterns": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "backend/src/**/*.ts", "backend/src/**/*.js", "backend/prisma/**/*.prisma", "*.config.ts", "*.config.js", "package.json", "backend/package.json", "tsconfig.json", "backend/tsconfig.json", "vite.config.ts", "tailwind.config.ts", "forge.config.ts"], "excludePatterns": ["node_modules/**", "dist/**", ".vite/**", "backend/dist/**", "backend/node_modules/**", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}, "action": {"type": "agent", "prompt": "Source files have been modified in this TypeScript/React Electron project. Please analyze the changes and update the project documentation accordingly. Focus on:\n\n1. Update the README.md if there are significant architectural changes, new features, or changes to the technology stack\n2. Update documentation in the /documentation folder if it exists, particularly CLAUDE.md or other relevant files\n3. If there are new components, services, or major functionality changes, ensure they are properly documented\n4. Update any API documentation if backend routes or services have changed\n5. Ensure the documentation reflects the current state of the codebase\n\nPlease review the changed files and update documentation to keep it synchronized with the current implementation. Focus on user-facing changes and architectural updates that would be important for developers working on this project.", "includeContext": ["README.md", "documentation/**/*.md", "docs/**/*.md"]}, "enabled": true}