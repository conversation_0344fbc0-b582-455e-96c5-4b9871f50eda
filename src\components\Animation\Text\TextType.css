/*
  Styles for TextType component
  - Minimal, clear styles matching class names used in TextType.tsx
  - Keep simple and unobtrusive; animation (blink) is handled by GSAP in the component
  - Documented for maintainability
*/

/* Container for typed text + cursor */
.text-type {
  /* Keep text inline with surrounding content */
  display: inline-flex;
  align-items: baseline;
  position: relative;
}

/* Visible typed content */
.text-type__content {
  /* Preserve spaces/newlines if provided */
  white-space: pre-wrap;
}

/* Cursor element (blinking handled in code via GSAP) */
.text-type__cursor {
  display: inline-block;
  margin-left: 2px; /* small gap from text */
  line-height: 1;
  font-weight: inherit; /* match surrounding text weight */
  /* Improve performance during opacity changes */
  will-change: opacity, transform;
}

/* Hidden cursor state while typing when configured */
.text-type__cursor--hidden {
  visibility: hidden;
}
