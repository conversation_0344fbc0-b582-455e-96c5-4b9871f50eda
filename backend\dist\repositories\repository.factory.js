"use strict";
/**
 * Repository Factory
 *
 * Zentrale Factory-Klasse für die Erstellung und Verwaltung
 * aller Repository-Instanzen mit Singleton-Pattern.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RepositoryFactoryImpl = void 0;
exports.initializeRepositoryFactory = initializeRepositoryFactory;
exports.getRepositoryFactory = getRepositoryFactory;
exports.getDispatchRepository = getDispatchRepository;
exports.getWarehouseRepository = getWarehouseRepository;
exports.getCuttingRepository = getCuttingRepository;
const dispatch_repository_1 = require("./dispatch.repository");
const warehouse_repository_1 = require("./warehouse.repository");
const cutting_repository_1 = require("./cutting.repository");
/**
 * Repository Factory Implementation
 */
class RepositoryFactoryImpl {
    constructor(prisma) {
        this.prisma = prisma;
    }
    /**
     * Singleton Instance abrufen
     */
    static getInstance(prisma) {
        if (!RepositoryFactoryImpl.instance) {
            if (!prisma) {
                throw new Error('Prisma client is required for first initialization');
            }
            RepositoryFactoryImpl.instance = new RepositoryFactoryImpl(prisma);
        }
        return RepositoryFactoryImpl.instance;
    }
    /**
     * Dispatch Repository abrufen
     */
    dispatch() {
        if (!this._dispatchRepository) {
            this._dispatchRepository = new dispatch_repository_1.DispatchRepositoryImpl(this.prisma);
        }
        return this._dispatchRepository;
    }
    /**
     * Warehouse Repository abrufen
     */
    warehouse() {
        if (!this._warehouseRepository) {
            this._warehouseRepository = new warehouse_repository_1.WarehouseRepositoryImpl(this.prisma);
        }
        return this._warehouseRepository;
    }
    /**
     * Cutting Repository abrufen
     */
    cutting() {
        if (!this._cuttingRepository) {
            this._cuttingRepository = new cutting_repository_1.CuttingRepositoryImpl(this.prisma);
        }
        return this._cuttingRepository;
    }
    /**
     * Repository Statistics für Monitoring
     */
    async getAllRepositoryStats() {
        const stats = {
            timestamp: new Date().toISOString(),
            repositories: {}
        };
        // Dispatch Repository Stats
        if (this._dispatchRepository) {
            stats.repositories.dispatch = await this._dispatchRepository.getStats();
        }
        // Warehouse Repository Stats
        if (this._warehouseRepository) {
            stats.repositories.warehouse = await this._warehouseRepository.getStats();
        }
        // Cutting Repository Stats
        if (this._cuttingRepository) {
            stats.repositories.cutting = await this._cuttingRepository.getStats();
        }
        return stats;
    }
    /**
     * Alle Repository-Caches invalidieren
     */
    async invalidateAllCaches() {
        const invalidationPromises = [];
        if (this._dispatchRepository) {
            invalidationPromises.push(this._dispatchRepository.invalidateCache());
        }
        if (this._warehouseRepository) {
            invalidationPromises.push(this._warehouseRepository.invalidateCache());
        }
        if (this._cuttingRepository) {
            invalidationPromises.push(this._cuttingRepository.invalidateCache());
        }
        await Promise.all(invalidationPromises);
    }
    /**
     * Factory zurücksetzen (für Testing)
     */
    static reset() {
        RepositoryFactoryImpl.instance = undefined;
    }
}
exports.RepositoryFactoryImpl = RepositoryFactoryImpl;
/**
 * Globale Repository Factory Instance
 */
let repositoryFactory;
/**
 * Repository Factory initialisieren
 */
function initializeRepositoryFactory(prisma) {
    repositoryFactory = RepositoryFactoryImpl.getInstance(prisma);
    return repositoryFactory;
}
/**
 * Repository Factory abrufen
 */
function getRepositoryFactory() {
    if (!repositoryFactory) {
        throw new Error('Repository factory not initialized. Call initializeRepositoryFactory first.');
    }
    return repositoryFactory;
}
/**
 * Direkte Repository-Zugriffe (Convenience Functions)
 */
function getDispatchRepository() {
    return getRepositoryFactory().dispatch();
}
function getWarehouseRepository() {
    return getRepositoryFactory().warehouse();
}
function getCuttingRepository() {
    return getRepositoryFactory().cutting();
}
