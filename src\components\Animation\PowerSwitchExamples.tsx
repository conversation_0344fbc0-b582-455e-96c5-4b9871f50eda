import React, { useState } from 'react';
import { PowerSwitch } from '@/components/ui/power-switch';

export const PowerSwitchExamples: React.FC = () => {
  const [isSystemActive, setIsSystemActive] = useState(false);
  const [isServiceEnabled, setIsServiceEnabled] = useState(true);
  const [isMaintenanceMode, setIsMaintenanceMode] = useState(false);

  return (
    <div className="p-8 space-y-8">
      <h2 className="text-2xl font-bold mb-6">Power Switch Examples</h2>

      {/* Basic Usage */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">System Status</h3>
        <div className="flex items-center gap-4">
          <PowerSwitch
            checked={isSystemActive}
            onChange={setIsSystemActive}
            id="system-switch"
          />
          <span className="text-sm">
            System ist {isSystemActive ? 'aktiv' : 'inaktiv'}
          </span>
        </div>
      </div>

      {/* Service Control */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Service Control</h3>
        <div className="flex items-center gap-4">
          <PowerSwitch
            checked={isServiceEnabled}
            onChange={setIsServiceEnabled}
            id="service-switch"
          />
          <span className="text-sm">
            Service ist {isServiceEnabled ? 'eingeschaltet' : 'ausgeschaltet'}
          </span>
        </div>
      </div>

      {/* Maintenance Mode */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Wartungsmodus</h3>
        <div className="flex items-center gap-4">
          <PowerSwitch
            checked={isMaintenanceMode}
            onChange={setIsMaintenanceMode}
            id="maintenance-switch"
          />
          <span className="text-sm">
            Wartungsmodus {isMaintenanceMode ? 'aktiviert' : 'deaktiviert'}
          </span>
        </div>
      </div>

      {/* Disabled State */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Disabled Switch</h3>
        <div className="flex items-center gap-4">
          <PowerSwitch
            checked={false}
            disabled={true}
            id="disabled-switch"
          />
          <span className="text-sm text-gray-500">
            Dieser Switch ist deaktiviert
          </span>
        </div>
      </div>

      {/* Multiple Switches in Grid */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Department Controls</h3>
        <div className="grid grid-cols-3 gap-6">
          <div className="text-center space-y-2">
            <PowerSwitch
              checked={true}
              onChange={() => { }}
              id="dispatch-switch"
            />
            <p className="text-sm">Versand</p>
          </div>
          <div className="text-center space-y-2">
            <PowerSwitch
              checked={false}
              onChange={() => { }}
              id="cutting-switch"
            />
            <p className="text-sm">Ablängerei</p>
          </div>
          <div className="text-center space-y-2">
            <PowerSwitch
              checked={true}
              onChange={() => { }}
              id="incoming-switch"
            />
            <p className="text-sm">Wareneingang</p>
          </div>
        </div>
      </div>
    </div>
  );
};