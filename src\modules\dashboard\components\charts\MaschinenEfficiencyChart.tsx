import { useEffect, useState, memo } from "react";
import apiService from "@/services/api.service";
import { DateRange } from "react-day-picker";
import { isWithinInterval } from "date-fns";
import { Bar, Bar<PERSON>hart, XAxis, YAxis, CartesianGrid } from "recharts";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useDialog } from "@/components/ui/dialog";
import { MaschinenDataTable } from "./MaschinenDataTable";

// Definition des Datentyps für die Effizienz-Daten
interface EfficiencyDataPoint {
  Datum: string;
  Machine: string;
  sollSchnitte: number;
  tagesSchnitte: number;
  istSchnitteProStunde: number;
  effizienzProzent: number;
}





/**
 * MaschinenEfficiencyChart Komponente
 * 
 * Zeigt die Effizienz aller Maschinen an (Ist vs. Soll Leistung in Prozent).
 * Berechnet basierend auf Soll-Schnitten pro Stunde vs. tatsächlichen Schnitten pro Stunde.
 */

interface ChartDataPoint {
  name: string;
  date: string;
  averageEfficiency: number;
  h1Efficiency: number;
  h3Efficiency: number;
  totalMachines: number;
  h1TagesSchnitte: number;
  h1SollSchnitteProTag: number;
  h3TagesSchnitte: number;
  h3SollSchnitteProTag: number;
  totalTagesSchnitte: number;
  totalSollSchnitteProTag: number;
}

interface MaschinenEfficiencyChartProps {
  data?: EfficiencyDataPoint[];
  dateRange?: DateRange;
}

const filterDataByDateRange = (data: EfficiencyDataPoint[], dateRange: DateRange): EfficiencyDataPoint[] => {
  if (!dateRange.from || !dateRange.to) return data;
  
  return data.filter(item => {
    try {
      const itemDate = new Date(item.Datum);
      return isWithinInterval(itemDate, {
        start: dateRange.from as Date,
        end: dateRange.to as Date
      });
    } catch {
      return true;
    }
  });
};

export const MaschinenEfficiencyChart = memo(function MaschinenEfficiencyChart({ data: propData, dateRange }: MaschinenEfficiencyChartProps) {
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [averageEfficiency, setAverageEfficiency] = useState<number>(0);
  
  // Dialog Hook für das Öffnen der Datentabelle
  const { openDialog } = useDialog();

  // Funktion zum Öffnen der Datentabelle im Dialog
  const handleOpenDetailsDialog = () => {
    openDialog(
      <div className="text-center space-y-2">
        <h2 className="text-xl font-bold text-black">MASCHINEN-TABELLE</h2>
        <p className="text-gray-600">
          Detailansicht der Effizienz-Kennzahlen pro Maschine
        </p>
        <MaschinenDataTable dateRange={dateRange} />
      </div>
    );
  };

  // Konfiguration für das Diagramm im Neobrutalism-Stil
  const chartConfig = {
    h1Efficiency: {
      label: "H1-Maschinen Zielerreichung (%)",
      color: "var(--chart-1)", // Blau für H1
    },
    h3Efficiency: {
      label: "H3-Maschinen Zielerreichung (%)",
      color: "var(--chart-2)", // Rot für H3
    },
    averageEfficiency: {
      label: "Gesamt-Zielerreichung (%)",
      color: "var(--chart-3)", // Grün für Gesamt
    }
  };

  // Farben für die verschiedenen Balken im Neobrutalism-Stil
  const colors = ["var(--chart-1)", "var(--chart-2)", "var(--chart-3)"];

  useEffect(() => {
    if (propData) {
      const filteredData = dateRange ? filterDataByDateRange(propData, dateRange) : propData;
      processEfficiencyData(filteredData);
    } else {
      loadData();
    }
  }, [propData, dateRange]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await apiService.getMaschinenEfficiency();
      
      if (!result) {
        setError('Keine Effizienz-Daten gefunden');
        setChartData([]);
        return;
      }
      
      // Filtere nach Datumsbereich
      let finalData = result as EfficiencyDataPoint[];
      if (dateRange && dateRange.from && dateRange.to) {
        finalData = (result as EfficiencyDataPoint[]).filter(item => {
          try {
            const itemDate = new Date(item.Datum);
            return isWithinInterval(itemDate, {
              start: dateRange.from as Date,
              end: dateRange.to as Date
            });
          } catch {
            return true;
          }
        });
      }
      
      processEfficiencyData(finalData || []);
    } catch (err) {
      setChartData([]);
      setError('Fehler beim Laden der Daten: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setLoading(false);
    }
  };

  const processEfficiencyData = (data: EfficiencyDataPoint[]) => {
    if (!data || data.length === 0) {
      setChartData([]);
      setAverageEfficiency(0);
      return;
    }

    // Gruppiere Daten nach Datum
    const groupedByDate = data.reduce((acc, item) => {
      if (!acc[item.Datum]) {
        acc[item.Datum] = [];
      }
      acc[item.Datum].push(item);
      return acc;
    }, {} as Record<string, EfficiencyDataPoint[]>);

    // Berechne aggregierte Werte pro Tag
    const processedData: ChartDataPoint[] = Object.entries(groupedByDate).map(([date, dayData]) => {
      const validData = dayData.filter(d => d.sollSchnitte !== null && d.sollSchnitte !== undefined && d.sollSchnitte > 0);
      
      const h1Data = validData.filter(d => d.Machine.includes('H1'));
      const h3Data = validData.filter(d => d.Machine.includes('H3'));
      
      const h1TagesSchnitte = h1Data.reduce((sum, d) => sum + d.tagesSchnitte, 0);
      const h1SollSchnitteProTag = h1Data.reduce((sum, d) => sum + (d.sollSchnitte * 21.5), 0);
      const h1Efficiency = h1SollSchnitteProTag > 0 ? (h1TagesSchnitte / h1SollSchnitteProTag) * 100 : 0;
      
      const h3TagesSchnitte = h3Data.reduce((sum, d) => sum + d.tagesSchnitte, 0);
      const h3SollSchnitteProTag = h3Data.reduce((sum, d) => sum + (d.sollSchnitte * 21.5), 0);
      const h3Efficiency = h3SollSchnitteProTag > 0 ? (h3TagesSchnitte / h3SollSchnitteProTag) * 100 : 0;
      
      const totalTagesSchnitte = validData.reduce((sum, d) => sum + d.tagesSchnitte, 0);
      const totalSollSchnitteProTag = validData.reduce((sum, d) => sum + (d.sollSchnitte * 21.5), 0);
      const averageEfficiency = totalSollSchnitteProTag > 0 ? (totalTagesSchnitte / totalSollSchnitteProTag) * 100 : 0;

      return {
        name: new Date(date).toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' }),
        date,
        averageEfficiency: Math.round(averageEfficiency),
        h1Efficiency: Math.round(h1Efficiency),
        h3Efficiency: Math.round(h3Efficiency),
        totalMachines: validData.length,
        h1TagesSchnitte,
        h1SollSchnitteProTag,
        h3TagesSchnitte,
        h3SollSchnitteProTag,
        totalTagesSchnitte,
        totalSollSchnitteProTag,
      };
    });

    // Sortiere nach Datum
    processedData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
    setChartData(processedData);
    
    // Berechne Gesamt-Durchschnitts-Effizienz
    const grandTotalTagesSchnitte = processedData.reduce((sum, d) => sum + d.totalTagesSchnitte, 0);
    const grandTotalSollSchnitteProTag = processedData.reduce((sum, d) => sum + d.totalSollSchnitteProTag, 0);
    const overallAverage = grandTotalSollSchnitteProTag > 0
        ? (grandTotalTagesSchnitte / grandTotalSollSchnitteProTag) * 100
        : 0;
    setAverageEfficiency(Math.round(overallAverage));
  };

  // Berechne Gesamtwerte und Durchschnitte für den Footer
  const totalH1TagesSchnitte = chartData.reduce((sum, item) => sum + item.h1TagesSchnitte, 0);
  const totalH1SollSchnitte = chartData.reduce((sum, item) => sum + item.h1SollSchnitteProTag, 0);
  const avgH1 = totalH1SollSchnitte > 0 ? (totalH1TagesSchnitte / totalH1SollSchnitte) * 100 : 0;

  const totalH3TagesSchnitte = chartData.reduce((sum, item) => sum + item.h3TagesSchnitte, 0);
  const totalH3SollSchnitte = chartData.reduce((sum, item) => sum + item.h3SollSchnitteProTag, 0);
  const avgH3 = totalH3SollSchnitte > 0 ? (totalH3TagesSchnitte / totalH3SollSchnitte) * 100 : 0;

  // Zeige Ladezustand oder Fehler an
  if (loading) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
        <p className="mt-4 font-bold">Lädt Effizienz-Daten...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-80 w-full items-center justify-center neo-brutalism-chart">
        <p className="text-red-500 font-bold">{error}</p>
        <p className="mt-2">Datenbank-Verbindung prüfen</p>
      </div>
    );
  }

  return (
    <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>MASCHINEN EFFIZIENZ</CardTitle>
            <CardDescription>
              Vergleich der täglichen Ist-Schnitte mit den Soll-Schnitten pro Tag.
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
          <ChartContainer
            config={chartConfig}
            className="h-60 w-full"
          >
            <BarChart data={chartData} margin={{ top: 5, right: 20, left: 20, bottom: 1 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis 
                dataKey="name" 
                className="text-xs font-bold"
                tickLine={false}
                axisLine={false}
                tickMargin={1}
                height={30}
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => {
                  try {
                    // Formatiere das Datum als TT.MM
                    return String(value);
                  } catch {
                    return value;
                  }
                }}
              />
              <YAxis 
                className="text-xs font-bold"
                tick={{ fill: "#000000" }}
                axisLine={{ stroke: "#000000", strokeWidth: 2 }}
                // Beschriftung für die Y-Achse
                label={{ 
                  value: "Zielerreichung (%)",
                  angle: -90, 
                  position: "insideLeft",
                  style: { textAnchor: "middle", fontSize: 12, fill: "#000000" },
                  offset: -5
                }}
                domain={[0, 'dataMax + 10']}
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    labelClassName="font-bold"
                    labelFormatter={(label) => `Datum: ${label}`}                   
                  />
                }
              />
              <ChartLegend content={<ChartLegendContent />} />
              <Bar
                dataKey="h1Efficiency"
                name="H1-Maschinen Effizienz: "
                fill={colors[0]}
                stroke="#000000"
                strokeWidth={2}
                radius={[4, 4, 0, 0]}
                className="neo-brutalism-bar"
              />
              <Bar
                dataKey="h3Efficiency"
                name="H3-Maschinen Effizienz: "
                fill={colors[1]}
                stroke="#000000"
                strokeWidth={2}
                radius={[4, 4, 0, 0]}
                className="neo-brutalism-bar"
              />
              <Bar
                dataKey="averageEfficiency"
                name="Gesamt-Effizienz: "
                fill={colors[2]}
                stroke="#000000"
                strokeWidth={2}
                radius={[4, 4, 0, 0]}
                className="neo-brutalism-bar"
              />
            </BarChart>
          </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex flex-col w-full text-sm">
          <div className="font-medium mb-1">Durchschnittliche Maschinen-Effizienz über die ausgewählte Zeitperiode:</div>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center">
              <span className="text-black">H1: {chartData.length > 0 ? avgH1.toFixed(1) : 'N/A'}% | </span>
            </div>
            <div className="flex items-center">
              <span className="text-black">H3: {chartData.length > 0 ? avgH3.toFixed(1) : 'N/A'}% | </span>
            </div>
            <div className="flex items-center">
              <span className="text-black">Gesamt: {averageEfficiency}% </span>
            </div>
          </div>
      </div>
    </CardFooter>
  </Card>
);
});