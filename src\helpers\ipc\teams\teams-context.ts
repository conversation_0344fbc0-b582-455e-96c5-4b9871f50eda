import { ipc<PERSON><PERSON><PERSON> } from 'electron';

export interface TeamsAPI {
  sendStoerung: (data: StoerungTeamsData) => Promise<{ success: boolean; error?: string }>;
  testConnection: () => Promise<{ success: boolean; error?: string }>;
}

export interface StoerungTeamsData {
  title: string;
  description?: string;
  severity: string;
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
  status: string;
  send_protocol: boolean;
}

/**
 * Stellt die Teams API im Renderer-Prozess zur Verfügung
 */
export function exposeTeamsContext(): TeamsAPI {
  return {
    /**
     * Sendet eine Störungsmeldung an Microsoft Teams
     */
    sendStoerung: async (data: StoerungTeamsData): Promise<{ success: boolean; error?: string }> => {
      try {
        return await ipcRenderer.invoke('teams:send-stoerung', data);
      } catch (error) {
        console.error('Fehler beim Senden an Teams:', error);
        return { 
          success: false, 
          error: error instanceof Error ? error.message : 'Unbekann<PERSON>hler' 
        };
      }
    },

    /**
     * Testet die Verbindung zu Microsoft Teams
     */
    testConnection: async (): Promise<{ success: boolean; error?: string }> => {
      try {
        return await ipcRenderer.invoke('teams:test-connection');
      } catch (error) {
        console.error('Fehler beim Testen der Teams-Verbindung:', error);
        return { 
          success: false, 
          error: error instanceof Error ? error.message : 'Verbindungstest fehlgeschlagen' 
        };
      }
    }
  };
}