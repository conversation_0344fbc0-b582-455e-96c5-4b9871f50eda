/**
 * Picking Route Visualization Component
 * 
 * Displays optimized picking routes with step-by-step navigation,
 * efficiency metrics, and alternative route options.
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
    Navigation, 
    Route, 
    Clock, 
    MapPin, 
    Package, 
    TrendingUp,
    AlertTriangle,
    CheckCircle,
    ArrowRight,
    BarChart3,
    Zap
} from 'lucide-react';
import {
    OptimizedPickingRoute,
    PickingStep,
    AlternativeRoute,
    PickingItem
} from '@/types/warehouse-optimization';
import { cn } from '@/lib/utils';

interface PickingRouteVisualizationProps {
    route: OptimizedPickingRoute;
    items: PickingItem[];
    onStepSelect?: (stepNumber: number) => void;
    onAlternativeSelect?: (routeId: string) => void;
    className?: string;
}

/**
 * Picking Route Visualization Component
 */
export function PickingRouteVisualization({
    route,
    items,
    onStepSelect,
    onAlternativeSelect,
    className
}: PickingRouteVisualizationProps) {
    const [selectedView, setSelectedView] = useState<'route' | 'metrics' | 'alternatives'>('route');
    const [selectedStep, setSelectedStep] = useState<number | null>(null);
    const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());

    // Calculate route metrics
    const routeMetrics = useMemo(() => {
        const totalItems = route.sequence.reduce((sum, step) => sum + step.quantity, 0);
        const avgTimePerStep = route.estimatedTime / route.sequence.length;
        const avgDistancePerStep = route.totalDistance / route.sequence.length;
        
        return {
            totalItems,
            avgTimePerStep,
            avgDistancePerStep,
            stepsCompleted: completedSteps.size,
            progressPercentage: (completedSteps.size / route.sequence.length) * 100
        };
    }, [route, completedSteps]);

    // Get step status
    const getStepStatus = (stepNumber: number) => {
        if (completedSteps.has(stepNumber)) return 'completed';
        if (selectedStep === stepNumber) return 'current';
        if (completedSteps.size + 1 === stepNumber) return 'next';
        return 'pending';
    };

    // Get step status color
    const getStepStatusColor = (status: string) => {
        switch (status) {
            case 'completed': return 'bg-green-500 text-white';
            case 'current': return 'bg-[#ff7a05] text-white';
            case 'next': return 'bg-blue-500 text-white';
            default: return 'bg-gray-300 text-gray-600';
        }
    };

    // Get priority color
    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'high': return 'text-red-600 bg-red-100';
            case 'medium': return 'text-yellow-600 bg-yellow-100';
            case 'low': return 'text-green-600 bg-green-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    // Handle step completion
    const handleStepComplete = (stepNumber: number) => {
        setCompletedSteps(prev => new Set([...prev, stepNumber]));
        
        // Auto-advance to next step
        if (stepNumber < route.sequence.length) {
            setSelectedStep(stepNumber + 1);
            onStepSelect?.(stepNumber + 1);
        }
    };

    // Handle step selection
    const handleStepSelect = (stepNumber: number) => {
        setSelectedStep(stepNumber);
        onStepSelect?.(stepNumber);
    };

    // Format time
    const formatTime = (minutes: number) => {
        const hours = Math.floor(minutes / 60);
        const mins = Math.round(minutes % 60);
        return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
    };

    // Get efficiency color
    const getEfficiencyColor = (efficiency: number) => {
        if (efficiency >= 0.8) return 'text-green-600';
        if (efficiency >= 0.6) return 'text-yellow-600';
        return 'text-red-600';
    };

    return (
        <Card className={cn("bg-secondary-background text-foreground", className)} data-testid="picking-route-visualization">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="text-lg font-semibold flex items-center gap-2">
                        <Navigation className="h-5 w-5 text-[#ff7a05]" />
                        Picking Route - {route.orderId}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        <Badge variant="outline">
                            {route.sequence.length} Schritte
                        </Badge>
                        <Badge variant="outline">
                            {route.totalDistance.toFixed(1)}m
                        </Badge>
                        <Badge variant="outline">
                            {formatTime(route.estimatedTime)}
                        </Badge>
                        <Badge className={getEfficiencyColor(route.efficiency)}>
                            {(route.efficiency * 100).toFixed(1)}% Effizienz
                        </Badge>
                    </div>
                </div>
                
                {/* Progress Bar */}
                <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                        <span>Fortschritt: {routeMetrics.stepsCompleted}/{route.sequence.length} Schritte</span>
                        <span>{routeMetrics.progressPercentage.toFixed(1)}%</span>
                    </div>
                    <Progress value={routeMetrics.progressPercentage} className="h-2" />
                </div>
            </CardHeader>
            <CardContent>
                <Tabs value={selectedView} onValueChange={(value) => setSelectedView(value as any)}>
                    <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="route">Route</TabsTrigger>
                        <TabsTrigger value="metrics">Metriken</TabsTrigger>
                        <TabsTrigger value="alternatives">Alternativen</TabsTrigger>
                    </TabsList>

                    {/* Route View */}
                    <TabsContent value="route" className="space-y-4">
                        <div className="max-h-96 overflow-y-auto space-y-3">
                            {route.sequence.map((step, index) => {
                                const status = getStepStatus(step.stepNumber);
                                const item = items.find(i => i.itemId === step.itemId);
                                
                                return (
                                    <div
                                        key={step.stepNumber}
                                        className={cn(
                                            "border rounded-lg p-3 cursor-pointer transition-all hover:shadow-md",
                                            selectedStep === step.stepNumber ? "ring-2 ring-[#ff7a05]" : "",
                                            status === 'completed' ? "bg-green-50 border-green-200" : ""
                                        )}
                                        onClick={() => handleStepSelect(step.stepNumber)}
                                    >
                                        <div className="flex items-center justify-between mb-2">
                                            <div className="flex items-center gap-3">
                                                <div className={cn(
                                                    "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                                                    getStepStatusColor(status)
                                                )}>
                                                    {status === 'completed' ? (
                                                        <CheckCircle className="h-4 w-4" />
                                                    ) : (
                                                        step.stepNumber
                                                    )}
                                                </div>
                                                <div>
                                                    <p className="font-medium">
                                                        {step.itemId} ({step.quantity} Stück)
                                                    </p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {step.location.zone}-{step.location.aisle}-{step.location.shelf}
                                                    </p>
                                                </div>
                                            </div>
                                            
                                            <div className="flex items-center gap-2">
                                                {item && (
                                                    <Badge className={getPriorityColor(item.priority)}>
                                                        {item.priority === 'high' ? 'Hoch' :
                                                         item.priority === 'medium' ? 'Mittel' : 'Niedrig'}
                                                    </Badge>
                                                )}
                                                {item?.fragile && (
                                                    <Badge variant="destructive">
                                                        <AlertTriangle className="h-3 w-3 mr-1" />
                                                        Zerbrechlich
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>
                                        
                                        <div className="grid grid-cols-3 gap-4 text-sm">
                                            <div className="flex items-center gap-1">
                                                <Route className="h-4 w-4 text-muted-foreground" />
                                                <span>{step.distanceFromPrevious.toFixed(1)}m</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Clock className="h-4 w-4 text-muted-foreground" />
                                                <span>{formatTime(step.estimatedTime)}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Package className="h-4 w-4 text-muted-foreground" />
                                                <span>{item?.weight ? `${item.weight}kg` : 'N/A'}</span>
                                            </div>
                                        </div>
                                        
                                        {step.instructions && (
                                            <div className="mt-2 p-2 bg-muted rounded text-sm">
                                                <p className="text-muted-foreground">{step.instructions}</p>
                                            </div>
                                        )}
                                        
                                        {status === 'current' && (
                                            <div className="mt-3 flex gap-2">
                                                <Button
                                                    size="sm"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleStepComplete(step.stepNumber);
                                                    }}
                                                    className="flex items-center gap-1"
                                                >
                                                    <CheckCircle className="h-4 w-4" />
                                                    Abgeschlossen
                                                </Button>
                                                {index < route.sequence.length - 1 && (
                                                    <Button
                                                        size="sm"
                                                        variant="outline"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleStepSelect(step.stepNumber + 1);
                                                        }}
                                                        className="flex items-center gap-1"
                                                    >
                                                        <ArrowRight className="h-4 w-4" />
                                                        Weiter
                                                    </Button>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    </TabsContent>

                    {/* Metrics View */}
                    <TabsContent value="metrics" className="space-y-4">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <Card>
                                <CardContent className="p-4">
                                    <div className="flex items-center space-x-2">
                                        <Package className="h-5 w-5 text-blue-600" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-600">Artikel gesamt</p>
                                            <p className="text-2xl font-bold text-blue-600">
                                                {routeMetrics.totalItems}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-4">
                                    <div className="flex items-center space-x-2">
                                        <Route className="h-5 w-5 text-green-600" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-600">Gesamtstrecke</p>
                                            <p className="text-2xl font-bold text-green-600">
                                                {route.totalDistance.toFixed(1)}m
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-4">
                                    <div className="flex items-center space-x-2">
                                        <Clock className="h-5 w-5 text-orange-600" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-600">Geschätzte Zeit</p>
                                            <p className="text-2xl font-bold text-orange-600">
                                                {formatTime(route.estimatedTime)}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-4">
                                    <div className="flex items-center space-x-2">
                                        <TrendingUp className={cn("h-5 w-5", getEfficiencyColor(route.efficiency))} />
                                        <div>
                                            <p className="text-sm font-medium text-gray-600">Effizienz</p>
                                            <p className={cn("text-2xl font-bold", getEfficiencyColor(route.efficiency))}>
                                                {(route.efficiency * 100).toFixed(1)}%
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Detailed Metrics */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-base">Detaillierte Metriken</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                                    <div>
                                        <span className="text-muted-foreground">Ø Zeit pro Schritt:</span>
                                        <p className="font-medium">{formatTime(routeMetrics.avgTimePerStep)}</p>
                                    </div>
                                    <div>
                                        <span className="text-muted-foreground">Ø Distanz pro Schritt:</span>
                                        <p className="font-medium">{routeMetrics.avgDistancePerStep.toFixed(1)}m</p>
                                    </div>
                                    <div>
                                        <span className="text-muted-foreground">Schritte abgeschlossen:</span>
                                        <p className="font-medium">{routeMetrics.stepsCompleted}/{route.sequence.length}</p>
                                    </div>
                                    <div>
                                        <span className="text-muted-foreground">Fortschritt:</span>
                                        <p className="font-medium">{routeMetrics.progressPercentage.toFixed(1)}%</p>
                                    </div>
                                    <div>
                                        <span className="text-muted-foreground">Route ID:</span>
                                        <p className="font-medium">{route.routeId}</p>
                                    </div>
                                    <div>
                                        <span className="text-muted-foreground">Bestellung:</span>
                                        <p className="font-medium">{route.orderId}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Performance Analysis */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-base flex items-center gap-2">
                                    <BarChart3 className="h-4 w-4" />
                                    Performance-Analyse
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm">Routeneffizienz</span>
                                        <div className="flex items-center gap-2">
                                            <Progress value={route.efficiency * 100} className="w-24 h-2" />
                                            <span className="text-sm font-medium">{(route.efficiency * 100).toFixed(1)}%</span>
                                        </div>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm">Zeitoptimierung</span>
                                        <div className="flex items-center gap-2">
                                            <Progress value={85} className="w-24 h-2" />
                                            <span className="text-sm font-medium">85%</span>
                                        </div>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm">Distanzoptimierung</span>
                                        <div className="flex items-center gap-2">
                                            <Progress value={78} className="w-24 h-2" />
                                            <span className="text-sm font-medium">78%</span>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Alternatives View */}
                    <TabsContent value="alternatives" className="space-y-4">
                        {route.alternativeRoutes.length === 0 ? (
                            <div className="flex items-center justify-center h-32">
                                <div className="text-center">
                                    <Route className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                                    <p className="text-muted-foreground">
                                        Keine alternativen Routen verfügbar
                                    </p>
                                </div>
                            </div>
                        ) : (
                            <div className="space-y-3">
                                {route.alternativeRoutes.map((alternative, index) => (
                                    <Card
                                        key={alternative.routeId}
                                        className="cursor-pointer hover:shadow-md transition-shadow"
                                        onClick={() => onAlternativeSelect?.(alternative.routeId)}
                                    >
                                        <CardContent className="p-4">
                                            <div className="flex items-center justify-between mb-2">
                                                <div className="flex items-center gap-2">
                                                    <Zap className="h-4 w-4 text-blue-500" />
                                                    <span className="font-medium">Alternative Route #{index + 1}</span>
                                                </div>
                                                <Badge className={getEfficiencyColor(alternative.efficiency)}>
                                                    {(alternative.efficiency * 100).toFixed(1)}% Effizienz
                                                </Badge>
                                            </div>
                                            
                                            <p className="text-sm text-muted-foreground mb-3">
                                                {alternative.description}
                                            </p>
                                            
                                            <div className="grid grid-cols-3 gap-4 text-sm mb-3">
                                                <div>
                                                    <span className="text-muted-foreground">Distanz:</span>
                                                    <p className="font-medium">{alternative.totalDistance.toFixed(1)}m</p>
                                                </div>
                                                <div>
                                                    <span className="text-muted-foreground">Zeit:</span>
                                                    <p className="font-medium">{formatTime(alternative.estimatedTime)}</p>
                                                </div>
                                                <div>
                                                    <span className="text-muted-foreground">Effizienz:</span>
                                                    <p className={cn("font-medium", getEfficiencyColor(alternative.efficiency))}>
                                                        {(alternative.efficiency * 100).toFixed(1)}%
                                                    </p>
                                                </div>
                                            </div>
                                            
                                            {/* Tradeoffs */}
                                            <div className="space-y-1">
                                                <span className="text-xs font-medium text-muted-foreground">Kompromisse:</span>
                                                <div className="flex flex-wrap gap-1">
                                                    {alternative.tradeoffs.map((tradeoff, tradeoffIndex) => (
                                                        <Badge
                                                            key={tradeoffIndex}
                                                            variant="secondary"
                                                            className="text-xs"
                                                        >
                                                            {tradeoff}
                                                        </Badge>
                                                    ))}
                                                </div>
                                            </div>
                                            
                                            {/* Comparison with current route */}
                                            <div className="mt-3 pt-3 border-t">
                                                <div className="grid grid-cols-3 gap-2 text-xs">
                                                    <div className={cn(
                                                        "text-center p-1 rounded",
                                                        alternative.totalDistance < route.totalDistance ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"
                                                    )}>
                                                        {alternative.totalDistance < route.totalDistance ? '↓' : '↑'} Distanz
                                                    </div>
                                                    <div className={cn(
                                                        "text-center p-1 rounded",
                                                        alternative.estimatedTime < route.estimatedTime ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"
                                                    )}>
                                                        {alternative.estimatedTime < route.estimatedTime ? '↓' : '↑'} Zeit
                                                    </div>
                                                    <div className={cn(
                                                        "text-center p-1 rounded",
                                                        alternative.efficiency > route.efficiency ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"
                                                    )}>
                                                        {alternative.efficiency > route.efficiency ? '↑' : '↓'} Effizienz
                                                    </div>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        )}
                    </TabsContent>
                </Tabs>
            </CardContent>
        </Card>
    );
}

export default PickingRouteVisualization;