import sqlite3 from 'sqlite3';
import { open, Database } from 'sqlite';
import path from 'path';
import { app } from 'electron';
import { promisify } from 'util';
import fs from 'fs';

const mkdir = promisify(fs.mkdir);
const exists = promisify(fs.exists);

// Typen für die Tabellen
export interface DispatchData {
  id: number;
  datum: string;
  tag: number;
  monat: number;
  kw: number;
  jahr: number;
  servicegrad: number;
  ausgeliefert_lup: number;
  rueckstaendig: number;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
  atrl: number;
  aril: number;
  fuellgrad_aril: number;
  qm_angenommen: number;
  qm_abgelehnt: number;
  qm_offen: number;
  mitarbeiter_std: number;
}

// Datenbankverzeichnis erstellen, falls nicht vorhanden
const getDbPath = async (): Promise<string> => {
  // In Entwicklung: ./database/sfm_dashboard.db
  // In Produktion: Benutzerdatenverzeichnis/AppData/Roaming/YourApp/database/sfm_dashboard.db
  const dbDir = app?.isPackaged 
    ? path.join(app.getPath('userData'), 'database')
    : path.join(process.cwd(), 'database');
    
  if (!await exists(dbDir)) {
    await mkdir(dbDir, { recursive: true });
  }
  
  return path.join(dbDir, 'sfm_dashboard.db');
};

// Datenbankverbindung initialisieren
let _db: Database | null = null;

export async function getDb(): Promise<Database> {
  if (!_db) {
    const dbPath = await getDbPath();
    console.log('Datenbankpfad:', dbPath);
    
    try {
      _db = await open({
        filename: dbPath,
        driver: sqlite3.Database
      });
      
      // Datenbank initialisieren, falls sie neu erstellt wurde
      await _db.exec(`
        CREATE TABLE IF NOT EXISTS dispatch_data (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          datum TEXT NOT NULL,
          tag INTEGER,
          monat INTEGER,
          kw INTEGER,
          jahr INTEGER,
          servicegrad REAL,
          ausgeliefert_lup INTEGER,
          rueckstaendig INTEGER,
          produzierte_tonnagen REAL,
          direktverladung_kiaa REAL,
          umschlag REAL,
          kg_pro_colli REAL,
          elefanten INTEGER,
          atrl INTEGER,
          aril INTEGER,
          fuellgrad_aril REAL,
          qm_angenommen REAL,
          qm_abgelehnt REAL,
          qm_offen REAL,
          mitarbeiter_std REAL
        )
      `);
      
      console.log('Datenbank erfolgreich initialisiert');
    } catch (error) {
      console.error('Fehler beim Initialisieren der Datenbank:', error);
      throw error;
    }
  }
  return _db;
}
