"use strict";
/**
 * Unit Tests for Repository Query Coordinator <PERSON><PERSON><PERSON>
 *
 * Tests timeout handling, retry logic, and fallback mechanisms
 * for database query coordination.
 */
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const client_1 = require("@prisma/client");
const repository_query_coordinator_service_1 = require("../services/repository-query-coordinator.service");
// Mock dependencies
vitest_1.vi.mock('@prisma/client');
vitest_1.vi.mock('../repositories/stoerungen.repository');
vitest_1.vi.mock('../repositories/dispatch.repository');
vitest_1.vi.mock('../repositories/cutting.repository');
vitest_1.vi.mock('../services/stoerungen-data-integration.service');
(0, vitest_1.describe)('RepositoryQueryCoordinator Error Handling', () => {
    let queryCoordinator;
    let mockPrisma;
    let mockStoerungenRepo;
    let mockDispatchRepo;
    let mockCuttingRepo;
    const testIntent = {
        type: 'stoerungen',
        keywords: ['störungen', 'incidents'],
        confidence: 0.8,
        timeRange: {
            startDate: '2024-01-01',
            endDate: '2024-01-31'
        }
    };
    (0, vitest_1.beforeEach)(() => {
        vitest_1.vi.clearAllMocks();
        mockPrisma = new client_1.PrismaClient();
        // Create query coordinator with test configuration
        queryCoordinator = new repository_query_coordinator_service_1.RepositoryQueryCoordinator(mockPrisma, {
            maxConcurrentQueries: 2,
            queryTimeout: 1000, // 1 second for faster tests
            retryAttempts: 2,
            retryDelay: 100, // 100ms for faster tests
            enableMetrics: true,
            enableCaching: false
        });
        // Mock repositories
        mockStoerungenRepo = {
            getStoerungen: vitest_1.vi.fn(),
            getInstance: vitest_1.vi.fn()
        };
        mockDispatchRepo = {
            getServiceLevelData: vitest_1.vi.fn(),
            getDailyPerformanceData: vitest_1.vi.fn(),
            getPickingData: vitest_1.vi.fn(),
            getReturnsData: vitest_1.vi.fn(),
            getPerformanceMetrics: vitest_1.vi.fn()
        };
        mockCuttingRepo = {
            getCuttingChartData: vitest_1.vi.fn(),
            getMaschinenEfficiency: vitest_1.vi.fn(),
            getCuttingPerformanceOverview: vitest_1.vi.fn(),
            getTopPerformingMachines: vitest_1.vi.fn()
        };
        // Replace internal repositories with mocks
        queryCoordinator.stoerungenRepo = mockStoerungenRepo;
        queryCoordinator.dispatchRepo = mockDispatchRepo;
        queryCoordinator.cuttingRepo = mockCuttingRepo;
        queryCoordinator.stoerungenIntegration = {
            getStoerungenDataForAI: vitest_1.vi.fn()
        };
    });
    (0, vitest_1.afterEach)(() => {
        vitest_1.vi.restoreAllMocks();
    });
    (0, vitest_1.describe)('Query Timeout Handling', () => {
        (0, vitest_1.it)('should timeout individual queries after configured time', async () => {
            // Mock a slow query that exceeds timeout
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockImplementation(() => new Promise(resolve => setTimeout(resolve, 2000)) // 2 seconds, longer than 1 second timeout
            );
            const results = await queryCoordinator.executeQueries([testIntent]);
            (0, vitest_1.expect)(results).toHaveLength(1);
            (0, vitest_1.expect)(results[0].success).toBe(false);
            (0, vitest_1.expect)(results[0].error).toContain('timeout');
            (0, vitest_1.expect)(results[0].fallback).toBe(true);
        });
        (0, vitest_1.it)('should handle mixed timeout scenarios', async () => {
            const intents = [
                { ...testIntent, type: 'stoerungen' },
                { ...testIntent, type: 'dispatch' }
            ];
            // Mock störungen to timeout, dispatch to succeed
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockImplementation(() => new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 1500)));
            mockDispatchRepo.getServiceLevelData.mockResolvedValue([]);
            mockDispatchRepo.getDailyPerformanceData.mockResolvedValue([]);
            mockDispatchRepo.getPickingData.mockResolvedValue([]);
            mockDispatchRepo.getReturnsData.mockResolvedValue([]);
            mockDispatchRepo.getPerformanceMetrics.mockResolvedValue({});
            const results = await queryCoordinator.executeQueries(intents);
            (0, vitest_1.expect)(results).toHaveLength(2);
            // Störungen should fail
            const stoerungenResult = results.find(r => r.dataType === 'stoerungen');
            (0, vitest_1.expect)(stoerungenResult === null || stoerungenResult === void 0 ? void 0 : stoerungenResult.success).toBe(false);
            (0, vitest_1.expect)(stoerungenResult === null || stoerungenResult === void 0 ? void 0 : stoerungenResult.fallback).toBe(true);
            // Dispatch should succeed
            const dispatchResult = results.find(r => r.dataType === 'dispatch');
            (0, vitest_1.expect)(dispatchResult === null || dispatchResult === void 0 ? void 0 : dispatchResult.success).toBe(true);
        });
    });
    (0, vitest_1.describe)('Retry Logic', () => {
        (0, vitest_1.it)('should retry failed queries up to configured attempts', async () => {
            let attemptCount = 0;
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockImplementation(() => {
                attemptCount++;
                if (attemptCount < 3) { // Fail first 2 attempts, succeed on 3rd
                    throw new Error('Temporary failure');
                }
                return Promise.resolve({
                    statistics: { total: 5 },
                    summary: 'Success after retry'
                });
            });
            const results = await queryCoordinator.executeQueries([testIntent]);
            (0, vitest_1.expect)(attemptCount).toBe(2); // Should retry once (2 total attempts)
            (0, vitest_1.expect)(results[0].success).toBe(false); // Still fails after max retries
            (0, vitest_1.expect)(results[0].fallback).toBe(true);
        });
        (0, vitest_1.it)('should succeed on retry if query recovers', async () => {
            let attemptCount = 0;
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockImplementation(() => {
                attemptCount++;
                if (attemptCount === 1) {
                    throw new Error('First attempt fails');
                }
                return Promise.resolve({
                    statistics: { total: 5 },
                    summary: 'Success on retry'
                });
            });
            const results = await queryCoordinator.executeQueries([testIntent]);
            (0, vitest_1.expect)(attemptCount).toBe(2);
            (0, vitest_1.expect)(results[0].success).toBe(true);
            (0, vitest_1.expect)(results[0].summary).toBe('Success on retry');
        });
        (0, vitest_1.it)('should not retry timeout errors', async () => {
            let attemptCount = 0;
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockImplementation(() => {
                attemptCount++;
                return new Promise((_, reject) => setTimeout(() => reject(new Error('Query timeout')), 1500));
            });
            const results = await queryCoordinator.executeQueries([testIntent]);
            (0, vitest_1.expect)(attemptCount).toBe(1); // Should not retry timeout errors
            (0, vitest_1.expect)(results[0].success).toBe(false);
            (0, vitest_1.expect)(results[0].error).toContain('timeout');
        });
    });
    (0, vitest_1.describe)('Repository Unavailability', () => {
        (0, vitest_1.it)('should handle repository being null/undefined', async () => {
            // Set repository to null to simulate unavailability
            queryCoordinator.stoerungenRepo = null;
            const results = await queryCoordinator.executeQueries([testIntent]);
            (0, vitest_1.expect)(results[0].success).toBe(false);
            (0, vitest_1.expect)(results[0].error).toContain('not available');
            (0, vitest_1.expect)(results[0].fallback).toBe(true);
        });
        (0, vitest_1.it)('should provide fallback data when repository fails', async () => {
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockRejectedValue(new Error('Repository connection failed'));
            const results = await queryCoordinator.executeQueries([testIntent]);
            (0, vitest_1.expect)(results[0].success).toBe(false);
            (0, vitest_1.expect)(results[0].data).toBeDefined();
            (0, vitest_1.expect)(results[0].data.message).toContain('nicht verfügbar');
            (0, vitest_1.expect)(results[0].fallback).toBe(true);
        });
    });
    (0, vitest_1.describe)('Partial Query Failures', () => {
        (0, vitest_1.it)('should handle partial failures in dispatch queries', async () => {
            const dispatchIntent = {
                type: 'dispatch',
                keywords: ['versand'],
                confidence: 0.7
            };
            // Mock some dispatch queries to fail
            mockDispatchRepo.getServiceLevelData.mockResolvedValue([]);
            mockDispatchRepo.getDailyPerformanceData.mockRejectedValue(new Error('Performance data unavailable'));
            mockDispatchRepo.getPickingData.mockResolvedValue([]);
            mockDispatchRepo.getReturnsData.mockRejectedValue(new Error('Returns data unavailable'));
            mockDispatchRepo.getPerformanceMetrics.mockResolvedValue({});
            const results = await queryCoordinator.executeQueries([dispatchIntent]);
            (0, vitest_1.expect)(results[0].success).toBe(true); // Should succeed with partial data
            (0, vitest_1.expect)(results[0].partialFailure).toBe(true);
            (0, vitest_1.expect)(results[0].summary).toContain('Einige Daten eingeschränkt verfügbar');
        });
        (0, vitest_1.it)('should handle partial failures in cutting queries', async () => {
            const cuttingIntent = {
                type: 'cutting',
                keywords: ['ablängerei'],
                confidence: 0.8
            };
            // Mock some cutting queries to fail
            mockCuttingRepo.getCuttingChartData.mockResolvedValue([]);
            mockCuttingRepo.getMaschinenEfficiency.mockRejectedValue(new Error('Efficiency data unavailable'));
            mockCuttingRepo.getCuttingPerformanceOverview.mockResolvedValue({ totalCuts: { cutTT: 0, cutTR: 0, cutRR: 0, pickCut: 0 } });
            mockCuttingRepo.getTopPerformingMachines.mockRejectedValue(new Error('Machine data unavailable'));
            const results = await queryCoordinator.executeQueries([cuttingIntent]);
            (0, vitest_1.expect)(results[0].success).toBe(true); // Should succeed with partial data
            (0, vitest_1.expect)(results[0].partialFailure).toBe(true);
            (0, vitest_1.expect)(results[0].summary).toContain('Einige Maschinendaten nicht verfügbar');
        });
    });
    (0, vitest_1.describe)('Fallback Data Quality', () => {
        (0, vitest_1.it)('should provide appropriate fallback data for störungen', async () => {
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockRejectedValue(new Error('Service unavailable'));
            const results = await queryCoordinator.executeQueries([testIntent]);
            (0, vitest_1.expect)(results[0].data.statistics).toBeDefined();
            (0, vitest_1.expect)(results[0].data.statistics.total).toBe(0);
            (0, vitest_1.expect)(results[0].data.systemStatus).toEqual([]);
            (0, vitest_1.expect)(results[0].data.message).toContain('nicht verfügbar');
        });
        (0, vitest_1.it)('should provide appropriate fallback data for dispatch', async () => {
            const dispatchIntent = {
                type: 'dispatch',
                keywords: ['versand'],
                confidence: 0.7
            };
            // Mock all dispatch queries to fail
            mockDispatchRepo.getServiceLevelData.mockRejectedValue(new Error('Service unavailable'));
            mockDispatchRepo.getDailyPerformanceData.mockRejectedValue(new Error('Service unavailable'));
            mockDispatchRepo.getPickingData.mockRejectedValue(new Error('Service unavailable'));
            mockDispatchRepo.getReturnsData.mockRejectedValue(new Error('Service unavailable'));
            mockDispatchRepo.getPerformanceMetrics.mockRejectedValue(new Error('Service unavailable'));
            const results = await queryCoordinator.executeQueries([dispatchIntent]);
            (0, vitest_1.expect)(results[0].data.metrics.totalDeliveries).toBe(0);
            (0, vitest_1.expect)(results[0].data.serviceLevel).toEqual([]);
            (0, vitest_1.expect)(results[0].data.message).toContain('nicht verfügbar');
        });
    });
    (0, vitest_1.describe)('Error Classification', () => {
        (0, vitest_1.it)('should classify timeout errors correctly', async () => {
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockRejectedValue(new Error('Query timeout after 1000ms'));
            const results = await queryCoordinator.executeQueries([testIntent]);
            (0, vitest_1.expect)(results[0].summary).toContain('dauerte zu lange');
            (0, vitest_1.expect)(results[0].summary).toContain('überlastet');
        });
        (0, vitest_1.it)('should classify network errors correctly', async () => {
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockRejectedValue(new Error('Network connection failed'));
            const results = await queryCoordinator.executeQueries([testIntent]);
            (0, vitest_1.expect)(results[0].summary).toContain('nicht erreichbar');
            (0, vitest_1.expect)(results[0].summary).toContain('wiederhergestellt');
        });
        (0, vitest_1.it)('should classify generic errors correctly', async () => {
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockRejectedValue(new Error('Unknown database error'));
            const results = await queryCoordinator.executeQueries([testIntent]);
            (0, vitest_1.expect)(results[0].summary).toContain('Fehler beim Abrufen');
            (0, vitest_1.expect)(results[0].summary).toContain('erneut');
        });
    });
    (0, vitest_1.describe)('Metrics and Monitoring', () => {
        (0, vitest_1.it)('should track query metrics including failures', async () => {
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockRejectedValue(new Error('Test failure'));
            await queryCoordinator.executeQueries([testIntent]);
            const metrics = queryCoordinator.getMetrics();
            (0, vitest_1.expect)(metrics.totalQueries).toBe(1);
            (0, vitest_1.expect)(metrics.failedQueries).toBe(1);
            (0, vitest_1.expect)(metrics.successfulQueries).toBe(0);
            (0, vitest_1.expect)(metrics.lastExecutionTime).toBeDefined();
        });
        (0, vitest_1.it)('should track average query times', async () => {
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockResolvedValue({
                statistics: { total: 1 },
                summary: 'Test data'
            });
            await queryCoordinator.executeQueries([testIntent]);
            const metrics = queryCoordinator.getMetrics();
            (0, vitest_1.expect)(metrics.averageQueryTime).toBeGreaterThan(0);
            (0, vitest_1.expect)(metrics.successfulQueries).toBe(1);
        });
        (0, vitest_1.it)('should warn about high failure rates', async () => {
            const consoleSpy = vitest_1.vi.spyOn(console, 'warn').mockImplementation(() => { });
            // Execute multiple failing queries to trigger high failure rate warning
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockRejectedValue(new Error('Consistent failure'));
            for (let i = 0; i < 5; i++) {
                await queryCoordinator.executeQueries([testIntent]);
            }
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalledWith(vitest_1.expect.stringContaining('High failure rate detected'));
            consoleSpy.mockRestore();
        });
    });
    (0, vitest_1.describe)('Concurrent Query Handling', () => {
        (0, vitest_1.it)('should handle concurrent query failures gracefully', async () => {
            const intents = [
                { ...testIntent, type: 'stoerungen' },
                { ...testIntent, type: 'dispatch' },
                { ...testIntent, type: 'cutting' }
            ];
            // Mock all queries to fail
            queryCoordinator.stoerungenIntegration.getStoerungenDataForAI
                .mockRejectedValue(new Error('Störungen failed'));
            mockDispatchRepo.getServiceLevelData.mockRejectedValue(new Error('Dispatch failed'));
            mockCuttingRepo.getCuttingChartData.mockRejectedValue(new Error('Cutting failed'));
            const results = await queryCoordinator.executeQueries(intents);
            (0, vitest_1.expect)(results).toHaveLength(3);
            (0, vitest_1.expect)(results.every(r => !r.success)).toBe(true);
            (0, vitest_1.expect)(results.every(r => r.fallback)).toBe(true);
        });
    });
});
