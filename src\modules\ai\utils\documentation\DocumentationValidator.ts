import { DocumentationSection, Tutorial, TutorialStep } from '../../components/documentation/AIDocumentationProvider';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface ComprehensiveValidationResult extends ValidationResult {
  summary: ValidationSummary;
}

export interface ValidationSummary {
  totalSections: number;
  validSections: number;
  invalidSections: number;
  totalTutorials: number;
  validTutorials: number;
  invalidTutorials: number;
  totalSteps: number;
  validSteps: number;
  invalidSteps: number;
}

export class DocumentationValidator {
  private readonly validCategories = ['overview', 'features', 'tutorials', 'api', 'troubleshooting'];
  private readonly validDifficulties = ['beginner', 'intermediate', 'advanced'];
  private readonly validActionTypes = ['click', 'input', 'navigate', 'wait'];
  private readonly validValidationTypes = ['element', 'value', 'state'];

  // Validate a single documentation section
  validateSection(section: DocumentationSection): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Required fields validation
    if (!section.id || section.id.trim() === '') {
      errors.push('Section ID is required');
    }

    if (!section.title || section.title.trim() === '') {
      errors.push('Section title is required');
    }

    if (!section.content || section.content.trim() === '') {
      errors.push('Section content is required');
    }

    // Category validation
    if (!this.validCategories.includes(section.category)) {
      errors.push(`Invalid category: ${section.category}`);
    }

    // Difficulty validation
    if (!this.validDifficulties.includes(section.difficulty)) {
      errors.push(`Invalid difficulty: ${section.difficulty}`);
    }

    // Content quality checks
    if (section.content && section.content.length < 50) {
      warnings.push('Section content is very short (less than 50 characters)');
    }

    if (section.content && section.content.length > 5000) {
      warnings.push('Section content is very long (over 5000 characters). Consider breaking it down.');
    }

    // Tags validation
    if (!section.tags || section.tags.length === 0) {
      warnings.push('Section has no tags');
    }

    if (section.tags && section.tags.length > 10) {
      warnings.push('Section has too many tags (over 10). Consider reducing for better organization.');
    }

    // Date validation
    if (section.lastUpdated) {
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
      
      if (section.lastUpdated < oneYearAgo) {
        warnings.push('Section content is older than 1 year');
      }
    }

    // Content structure suggestions
    if (section.content && !section.content.includes('\n')) {
      suggestions.push('Consider adding line breaks to improve content readability');
    }

    if (section.content && !section.content.includes('```')) {
      if (section.category === 'api' || section.tags.includes('code')) {
        suggestions.push('Consider adding code examples for better understanding');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  // Validate a single tutorial
  validateTutorial(tutorial: Tutorial): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Required fields validation
    if (!tutorial.id || tutorial.id.trim() === '') {
      errors.push('Tutorial ID is required');
    }

    if (!tutorial.title || tutorial.title.trim() === '') {
      errors.push('Tutorial title is required');
    }

    if (!tutorial.description || tutorial.description.trim() === '') {
      errors.push('Tutorial description is required');
    }

    if (!tutorial.category || tutorial.category.trim() === '') {
      errors.push('Tutorial category is required');
    }

    // Steps validation
    if (!tutorial.steps || tutorial.steps.length === 0) {
      errors.push('Tutorial must have at least one step');
    }

    // Estimated time validation
    if (tutorial.estimatedTime <= 0) {
      errors.push('Tutorial estimated time must be greater than 0');
    }

    if (tutorial.estimatedTime > 60) {
      warnings.push('Tutorial is quite long (over 60 minutes)');
    }

    // Difficulty validation
    if (!this.validDifficulties.includes(tutorial.difficulty)) {
      errors.push(`Invalid difficulty: ${tutorial.difficulty}`);
    }

    // Prerequisites validation
    if (tutorial.difficulty === 'advanced' && (!tutorial.prerequisites || tutorial.prerequisites.length === 0)) {
      warnings.push('Advanced tutorial should have prerequisites');
    }

    if (tutorial.prerequisites && tutorial.prerequisites.length > 5) {
      warnings.push('Tutorial has many prerequisites. Consider simplifying or breaking it down.');
    }

    // Validate each step
    if (tutorial.steps) {
      tutorial.steps.forEach(step => {
        const stepValidation = this.validateStep(step);
        errors.push(...stepValidation.errors);
        warnings.push(...stepValidation.warnings);
        suggestions.push(...stepValidation.suggestions);
      });
    }

    // Tutorial structure suggestions
    if (tutorial.steps && tutorial.steps.length > 20) {
      suggestions.push('Consider breaking down this tutorial into smaller, focused tutorials');
    }

    if (tutorial.steps && tutorial.steps.length < 3) {
      suggestions.push('Consider adding more detailed steps for better guidance');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  // Validate a single tutorial step
  validateStep(step: TutorialStep): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Required fields validation
    if (!step.id || step.id.trim() === '') {
      errors.push('Step ID is required');
    }

    if (!step.title || step.title.trim() === '') {
      errors.push(`Step ${step.id} is missing a title`);
    }

    if (!step.content || step.content.trim() === '') {
      errors.push(`Step ${step.id} is missing content`);
    }

    // Action validation
    if (step.action) {
      if (!this.validActionTypes.includes(step.action.type)) {
        errors.push(`Step ${step.id} has invalid action type: ${step.action.type}`);
      }

      if (step.action.type !== 'wait' && (!step.action.target || step.action.target.trim() === '')) {
        errors.push(`Step ${step.id} action is missing target`);
      }

      if (step.action.type === 'input' && (!step.action.value || step.action.value.trim() === '')) {
        warnings.push(`Step ${step.id} input action has no value`);
      }
    }

    // Validation rules validation
    if (step.validation) {
      if (!this.validValidationTypes.includes(step.validation.type)) {
        errors.push(`Step ${step.id} has invalid validation type: ${step.validation.type}`);
      }

      if (!step.validation.condition || step.validation.condition.trim() === '') {
        errors.push(`Step ${step.id} validation is missing condition`);
      }
    }

    // Content quality checks
    if (step.content && step.content.length < 10) {
      warnings.push(`Step ${step.id} has very short content`);
    }

    if (step.content && step.content.length > 500) {
      warnings.push(`Step ${step.id} has very long content. Consider breaking it down.`);
    }

    // Suggestions
    if (step.action && !step.validation) {
      suggestions.push(`Step ${step.id} has an action but no validation. Consider adding validation for better user guidance.`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  // Validate multiple sections
  validateSections(sections: DocumentationSection[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Check for duplicate IDs
    const ids = new Set<string>();
    sections.forEach(section => {
      if (ids.has(section.id)) {
        errors.push(`Duplicate section ID: ${section.id}`);
      }
      ids.add(section.id);
    });

    // Validate each section
    sections.forEach(section => {
      const validation = this.validateSection(section);
      errors.push(...validation.errors);
      warnings.push(...validation.warnings);
      suggestions.push(...validation.suggestions);
    });

    // Collection-level suggestions
    const categories = new Set(sections.map(s => s.category));
    if (categories.size < 3) {
      suggestions.push('Consider adding sections in more categories for comprehensive coverage');
    }

    const difficulties = new Set(sections.map(s => s.difficulty));
    if (!difficulties.has('beginner')) {
      suggestions.push('Consider adding beginner-level documentation');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  // Validate multiple tutorials
  validateTutorials(tutorials: Tutorial[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Check for duplicate IDs
    const ids = new Set<string>();
    tutorials.forEach(tutorial => {
      if (ids.has(tutorial.id)) {
        errors.push(`Duplicate tutorial ID: ${tutorial.id}`);
      }
      ids.add(tutorial.id);
    });

    // Validate each tutorial
    tutorials.forEach(tutorial => {
      const validation = this.validateTutorial(tutorial);
      errors.push(...validation.errors);
      warnings.push(...validation.warnings);
      suggestions.push(...validation.suggestions);
    });

    // Collection-level suggestions
    const difficulties = new Set(tutorials.map(t => t.difficulty));
    if (!difficulties.has('beginner')) {
      suggestions.push('Consider adding beginner-level tutorials');
    }

    if (tutorials.length < 3) {
      suggestions.push('Consider adding more tutorials for better user guidance');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  // Validate cross-references between sections and tutorials
  validateCrossReferences(sections: DocumentationSection[], tutorials: Tutorial[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    const allIds = new Set([
      ...sections.map(s => s.id),
      ...tutorials.map(t => t.id)
    ]);

    // Check internal links in sections
    sections.forEach(section => {
      const linkMatches = section.content.match(/\[([^\]]+)\]\(#([^)]+)\)/g);
      if (linkMatches) {
        linkMatches.forEach(match => {
          const linkId = match.match(/\(#([^)]+)\)/)?.[1];
          if (linkId && !allIds.has(linkId)) {
            errors.push(`Broken link to ${linkId} in section ${section.id}`);
          }
        });
      }
    });

    // Check tutorial prerequisites
    tutorials.forEach(tutorial => {
      if (tutorial.prerequisites) {
        tutorial.prerequisites.forEach(prereq => {
          if (!allIds.has(prereq)) {
            errors.push(`Tutorial ${tutorial.id} references non-existent prerequisite: ${prereq}`);
          }
        });
      }
    });

    // Check for circular dependencies in prerequisites
    tutorials.forEach(tutorial => {
      if (this.hasCircularDependency(tutorial, tutorials)) {
        errors.push(`Tutorial ${tutorial.id} has circular dependency in prerequisites`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  // Comprehensive validation of all documentation
  validateAll(sections: DocumentationSection[], tutorials: Tutorial[]): ComprehensiveValidationResult {
    const sectionValidation = this.validateSections(sections);
    const tutorialValidation = this.validateTutorials(tutorials);
    const crossRefValidation = this.validateCrossReferences(sections, tutorials);

    const allErrors = [
      ...sectionValidation.errors,
      ...tutorialValidation.errors,
      ...crossRefValidation.errors
    ];

    const allWarnings = [
      ...sectionValidation.warnings,
      ...tutorialValidation.warnings,
      ...crossRefValidation.warnings
    ];

    const allSuggestions = [
      ...sectionValidation.suggestions,
      ...tutorialValidation.suggestions,
      ...crossRefValidation.suggestions
    ];

    // Calculate summary
    const validSections = sections.filter(s => this.validateSection(s).isValid).length;
    const validTutorials = tutorials.filter(t => this.validateTutorial(t).isValid).length;
    const totalSteps = tutorials.reduce((sum, t) => sum + t.steps.length, 0);
    const validSteps = tutorials.reduce((sum, t) => {
      return sum + t.steps.filter(s => this.validateStep(s).isValid).length;
    }, 0);

    const summary: ValidationSummary = {
      totalSections: sections.length,
      validSections,
      invalidSections: sections.length - validSections,
      totalTutorials: tutorials.length,
      validTutorials,
      invalidTutorials: tutorials.length - validTutorials,
      totalSteps,
      validSteps,
      invalidSteps: totalSteps - validSteps
    };

    // Add comprehensive suggestions
    if (sections.length < 5) {
      allSuggestions.push('Consider adding more documentation sections for comprehensive coverage');
    }

    if (tutorials.length < 3) {
      allSuggestions.push('Consider adding more tutorials for better user guidance');
    }

    const avgTutorialLength = tutorials.length > 0 
      ? tutorials.reduce((sum, t) => sum + t.estimatedTime, 0) / tutorials.length 
      : 0;
    
    if (avgTutorialLength > 45) {
      allSuggestions.push('Consider breaking down long tutorials into shorter, focused ones');
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
      suggestions: [...new Set(allSuggestions)], // Remove duplicates
      summary
    };
  }

  // Helper method to detect circular dependencies
  private hasCircularDependency(tutorial: Tutorial, allTutorials: Tutorial[], visited: Set<string> = new Set()): boolean {
    if (visited.has(tutorial.id)) {
      return true;
    }

    visited.add(tutorial.id);

    if (tutorial.prerequisites) {
      for (const prereqId of tutorial.prerequisites) {
        const prereqTutorial = allTutorials.find(t => t.id === prereqId);
        if (prereqTutorial && this.hasCircularDependency(prereqTutorial, allTutorials, new Set(visited))) {
          return true;
        }
      }
    }

    return false;
  }

  // Generate validation report
  generateReport(result: ComprehensiveValidationResult): string {
    const { summary, errors, warnings, suggestions } = result;
    
    let report = '# Documentation Validation Report\n\n';
    
    report += '## Summary\n\n';
    report += `- **Total Sections**: ${summary.totalSections} (${summary.validSections} valid, ${summary.invalidSections} invalid)\n`;
    report += `- **Total Tutorials**: ${summary.totalTutorials} (${summary.validTutorials} valid, ${summary.invalidTutorials} invalid)\n`;
    report += `- **Total Steps**: ${summary.totalSteps} (${summary.validSteps} valid, ${summary.invalidSteps} invalid)\n`;
    report += `- **Overall Status**: ${result.isValid ? '✅ Valid' : '❌ Invalid'}\n\n`;

    if (errors.length > 0) {
      report += '## Errors\n\n';
      errors.forEach(error => {
        report += `- ❌ ${error}\n`;
      });
      report += '\n';
    }

    if (warnings.length > 0) {
      report += '## Warnings\n\n';
      warnings.forEach(warning => {
        report += `- ⚠️ ${warning}\n`;
      });
      report += '\n';
    }

    if (suggestions.length > 0) {
      report += '## Suggestions\n\n';
      suggestions.forEach(suggestion => {
        report += `- 💡 ${suggestion}\n`;
      });
      report += '\n';
    }

    return report;
  }
}