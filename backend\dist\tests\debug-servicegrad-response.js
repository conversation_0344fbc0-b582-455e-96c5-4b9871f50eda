"use strict";
/**
 * Debug Test für Servicegrad-Antwort
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const data_enrichment_service_1 = require("../services/data-enrichment.service");
const client_1 = require("@prisma/client");
async function debugServicegradResponse() {
    var _a, _b;
    console.log('🔍 Debugging Servicegrad Response...\n');
    const prisma = new client_1.PrismaClient();
    const service = new data_enrichment_service_1.DataEnrichmentService(prisma);
    try {
        // Test the exact query from the logs
        const result = await service.enrichChatContext('hallo, kannst du mir sagen wie der Servicegrad am 17.04.2025 war?');
        console.log('📊 ENRICHMENT RESULT:');
        console.log(`  - Has Data: ${result.hasData}`);
        console.log(`  - Data Types: ${result.dataTypes.join(', ')}`);
        console.log(`  - Fallback Reason: ${result.fallbackReason || 'None'}`);
        if (result.detectedIntents.length > 0) {
            const intent = result.detectedIntents[0];
            console.log(`  - Intent: ${intent.type} (confidence: ${intent.confidence})`);
            console.log(`  - Time Range: ${(_a = intent.timeRange) === null || _a === void 0 ? void 0 : _a.startDate} to ${(_b = intent.timeRange) === null || _b === void 0 ? void 0 : _b.endDate}`);
        }
        console.log('\n📝 DATABASE CONTEXT:');
        console.log(result.databaseContext);
        console.log('\n🔍 DETAILED ANALYSIS:');
        // Let's also test the dispatch repository directly
        const { DispatchRepositoryImpl } = await Promise.resolve().then(() => __importStar(require('../repositories/dispatch.repository')));
        const dispatchRepo = new DispatchRepositoryImpl(prisma);
        const serviceLevelData = await dispatchRepo.getServiceLevelData({
            startDate: '2025-04-17',
            endDate: '2025-04-17'
        });
        console.log('\n🎯 DIRECT REPOSITORY RESULT:');
        console.log(`  - Records found: ${serviceLevelData.length}`);
        if (serviceLevelData.length > 0) {
            serviceLevelData.forEach(record => {
                console.log(`  - ${record.datum}: Servicegrad ${record.servicegrad} (raw value)`);
            });
        }
        const performanceMetrics = await dispatchRepo.getPerformanceMetrics({
            startDate: '2025-04-17',
            endDate: '2025-04-17'
        });
        console.log('\n📊 PERFORMANCE METRICS:');
        console.log(`  - Average Service Level: ${performanceMetrics.averageServiceLevel}%`);
        console.log(`  - Total Tonnage: ${performanceMetrics.totalTonnage}`);
        console.log(`  - Total Deliveries: ${performanceMetrics.totalDeliveries}`);
    }
    catch (error) {
        console.error('❌ Error:', error);
    }
    finally {
        await prisma.$disconnect();
    }
}
// Run the debug
debugServicegradResponse().catch(console.error);
