/**
 * AvailableDrumsTable
 * 
 * Komponente zur Anzeige verfügbarer Trommeln in einer Tabelle mit Auswahlmöglichkeit.
 * Filtert Trommeln basierend auf aufnahmeDatum, Material und Mindestbestand.
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
// Calendar und Popover nicht mehr benötigt, da Datum schreibgeschützt ist
// import { Calendar } from '@/components/ui/calendar';
// import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  Search, 
  Calendar as CalendarIcon, 
  Package, 
  CheckCircle, 
  AlertCircle,
  Loader2
} from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { 
  availableDrumsService, 
  type AvailableDrum, 
  type AvailableDrumFilter 
} from '../../services/cutting/available-drums/AvailableDrumsService';

interface AvailableDrumsTableProps {
  // Filter-Eigenschaften
  selectedDate?: Date | string;
  selectedMaterial?: string;
  minLength?: number;
  
  // Callback für ausgewählte Trommeln
  onDrumsSelected: (drums: AvailableDrum[]) => void;
  
  // Bereits ausgewählte Trommeln (für Checkbox-Status)
  selectedDrums?: AvailableDrum[];
  
  // Optionale Klassen
  className?: string;
}

export default function AvailableDrumsTable({
  selectedDate,
  selectedMaterial,
  minLength = 0,
  onDrumsSelected,
  selectedDrums = [],
  className = ''
}: AvailableDrumsTableProps) {
  // State für verfügbare Trommeln und Filter
  const [availableDrums, setAvailableDrums] = useState<AvailableDrum[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // State für lokale Filter (schreibgeschützt, aus Props übernommen)
  const [filterDate, setFilterDate] = useState<Date | undefined>(
    selectedDate ? (typeof selectedDate === 'string' ? new Date(selectedDate) : selectedDate) : new Date()
  );
  const [filterMaterial, setFilterMaterial] = useState(selectedMaterial || '');
  const [filterMinLength, setFilterMinLength] = useState(minLength);
  // isCalendarOpen nicht mehr benötigt, da Datum schreibgeschützt ist
  
  // State für Trommelauswahl
  const [localSelectedDrums, setLocalSelectedDrums] = useState<AvailableDrum[]>(selectedDrums);

  /**
   * Lädt verfügbare Trommeln basierend auf den Filterkriterien
   */
  const loadAvailableDrums = async () => {
    // Nur laden wenn Datum vorhanden ist - Material kann auch leer sein
    if (!filterDate) {
      setAvailableDrums([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const filter: AvailableDrumFilter = {
        aufnahmeDatum: availableDrumsService.formatDateForAPI(filterDate),
        material: filterMaterial || '',
        minGesamtbestand: filterMinLength
      };

      // Validierung
      const validationErrors = availableDrumsService.validateFilter(filter);
      if (validationErrors.length > 0) {
        setError(`Ungültige Filter: ${validationErrors.join(', ')}`);
        return;
      }

      const response = await availableDrumsService.getAvailableDrums(filter);
      setAvailableDrums(response.drums);
      
      if (response.drums.length === 0) {
        // Keine Trommeln gefunden, aber das ist in Ordnung - Tabelle wird trotzdem angezeigt
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Fehler beim Laden der Trommeln');
      setAvailableDrums([]);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Behandelt die Auswahl/Abwahl einer Trommel
   */
  const handleDrumSelection = (drum: AvailableDrum, selected: boolean) => {
    let updatedSelection: AvailableDrum[];
    
    if (selected) {
      // Trommel hinzufügen, falls noch nicht ausgewählt
      if (!localSelectedDrums.find(d => d.charge === drum.charge)) {
        updatedSelection = [...localSelectedDrums, drum];
      } else {
        updatedSelection = localSelectedDrums;
      }
    } else {
      // Trommel entfernen
      updatedSelection = localSelectedDrums.filter(d => d.charge !== drum.charge);
    }
    
    setLocalSelectedDrums(updatedSelection);
    onDrumsSelected(updatedSelection);
  };

  /**
   * Prüft, ob eine Trommel ausgewählt ist
   */
  const isDrumSelected = (drum: AvailableDrum): boolean => {
    return localSelectedDrums.some(d => d.charge === drum.charge);
  };

  /**
   * Initialisierung der Filter basierend auf Props
   */
  useEffect(() => {
    // Datum verarbeiten - sowohl Date-Objekt als auch String unterstützen
    if (selectedDate) {
      if (typeof selectedDate === 'string') {
        // String-Format: YYYY-MM-DD zu Date konvertieren
        const dateObj = new Date(selectedDate + 'T00:00:00');
        setFilterDate(dateObj);
      } else {
        setFilterDate(selectedDate);
      }
    } else {
      const today = new Date();
      setFilterDate(today);
    }

    // Material setzen - auch leere Strings sind gültig
    setFilterMaterial(selectedMaterial || '');
    
    // Mindestlänge setzen
    setFilterMinLength(minLength || 0);
  }, [selectedDate, selectedMaterial, minLength]);



  /**
   * Automatisches Laden bei Änderung der Filter
   */
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadAvailableDrums();
    }, 500); // Debounce für 500ms

    return () => clearTimeout(timeoutId);
  }, [filterDate, filterMaterial, filterMinLength]);

  return (
    <div className="space-y-2">
      {/* Filter-Bereich */}
      <div className="space-y-2 grid grid-cols-1 md:grid-cols-3 gap-4 py-0 bg-gray-50 rounded-lg">
        {/* Datum-Anzeige (aus Lieferung übernommen) */}
        <div className="space-y-2">
          <Label>Aufnahmedatum</Label>
          <div className="flex items-center gap-2 p-2 bg-gray-100 border rounded-md">
            <CalendarIcon className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium">
              {filterDate && filterDate instanceof Date ? format(filterDate, 'dd.MM.yyyy', { locale: de }) : 'Kein Datum ausgewählt'}
            </span>
          </div>
        </div>

        {/* Material-Anzeige (aus Lieferung übernommen) */}
        <div className="space-y-2">
          <Label>Material</Label>
          <div className="flex items-center gap-2 p-2 bg-gray-100 border rounded-md">
            <Package className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium">
              {filterMaterial || 'Kein Material ausgewählt'}
            </span>
          </div>
        </div>

        {/* Mindestlänge-Anzeige (aus Lieferung übernommen) */}
        <div className="space-y-2">
          <Label>Mindestlänge</Label>
          <div className="flex items-center gap-2 p-2 bg-gray-100 border rounded-md">
            <span className="text-sm font-medium">
              {filterMinLength > 0 ? `${filterMinLength} m` : 'Keine Mindestlänge'}
            </span>
          </div>
        </div>
      </div>

        {/* Lade-Indikator */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Lade verfügbare Trommeln...</span>
          </div>
        )}

        {/* Fehler-Anzeige */}
        {error && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-800">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">{error}</span>
          </div>
        )}

        {/* Trommeln-Tabelle */}
        {!loading && !error && availableDrums.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {availableDrums.length} Trommel(n) gefunden
              </span>
              <Badge variant="secondary">
                {localSelectedDrums.length} ausgewählt
              </Badge>
            </div>

            <div className="border rounded-lg overflow-hidden shadow-sm scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400">
              <div className="max-h-96 overflow-y-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gradient-to-r from-gray-50 to-gray-100 border-b sticky top-0 shadow-sm z-10">
                    <tr>
                      <th className="text-left p-4 font-semibold text-gray-700 tracking-wide uppercase text-xs">Ausw.</th>
                      <th className="text-left p-4 font-semibold text-gray-700 tracking-wide uppercase text-xs">MatNr.</th>
                      <th className="text-left p-4 font-semibold text-gray-700 tracking-wide uppercase text-xs">Charge</th>
                      <th className="text-left p-4 font-semibold text-gray-700 tracking-wide uppercase text-xs">Trommel</th>
                      <th className="text-left p-4 font-semibold text-gray-700 tracking-wide uppercase text-xs">Länge</th>
                      <th className="text-left p-4 font-semibold text-gray-700 tracking-wide uppercase text-xs">Kennz.</th>
                      <th className="text-left p-4 font-semibold text-gray-700 tracking-wide uppercase text-xs">Dauer</th>
                      <th className="text-left p-4 font-semibold text-gray-700 tracking-wide uppercase text-xs">Datum</th>
                    </tr>
                  </thead>
                  <tbody>
                    {availableDrums.map((drum, index) => {
                      // Berechne Dauer seit Aufnahmedatum
                      const aufnahmeDatum = drum.aufnahmeDatum ? new Date(drum.aufnahmeDatum) : null;
                      const heute = new Date();
                      const dauerTage = aufnahmeDatum ? Math.floor((heute.getTime() - aufnahmeDatum.getTime()) / (1000 * 60 * 60 * 24)) : null;
                      
                      return (
                        <tr 
                          key={drum.charge} 
                          className={`border-b transition-colors duration-150 hover:bg-gray-50 ${
                            isDrumSelected(drum) ? 'bg-blue-50 hover:bg-blue-100' : 'hover:bg-gray-50'
                          }`}
                        >
                          <td className="p-4 align-middle">
                            <Checkbox
                              checked={isDrumSelected(drum)}
                              onCheckedChange={(checked) => 
                                handleDrumSelection(drum, checked as boolean)
                              }
                              className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                            />
                          </td>
                          <td className="p-4 align-middle font-medium text-gray-900">{drum.material || '-'}</td>
                          <td className="p-4 align-middle font-mono text-sm bg-gray-50 rounded">{drum.charge}</td>
                          <td className="p-4 align-middle">
                            <Badge variant="outline" className="font-medium">{drum.lagereinheitentyp}</Badge>
                          </td>
                          <td className="p-4 align-middle font-bold text-green-700">{drum.gesamtbestand.toFixed(1)} m</td>
                          <td className="p-4 align-middle text-gray-600">{drum.lagerbereich || '-'}</td>
                          <td className="p-4 align-middle text-gray-600 font-medium">
                            {dauerTage !== null ? `${dauerTage} Tage` : '-'}
                          </td>
                          <td className="p-4 align-middle text-gray-600 font-medium">
                            {aufnahmeDatum ? format(aufnahmeDatum, 'dd.MM.yyyy', { locale: de }) : '-'}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Keine Trommeln gefunden */}
        {!loading && !error && availableDrums.length === 0 && filterDate && (
          <div className="text-center py-8 text-gray-500">
            <Package className="h-12 w-12 mx-auto mb-3 opacity-30" />
            <p>Keine Trommeln für die gewählten Filter gefunden.</p>
            <p className="text-xs mt-1">Versuchen Sie andere Filterkriterien.</p>
          </div>
        )}

        {/* Ausgewählte Trommeln Zusammenfassung */}
        {localSelectedDrums.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800">
                {localSelectedDrums.length} Trommel(n) ausgewählt
              </span>
            </div>
            <div className="text-sm text-blue-700">
              Gesamtlänge: {localSelectedDrums.reduce((sum, drum) => sum + drum.gesamtbestand, 0).toFixed(1)} m
            </div>
          </div>
        )}
    </div>
  );
}