/**
 * Performance Monitoring Factory Service
 * 
 * Factory-Service zur Erstellung der richtigen Performance Monitoring Instanz
 * basierend auf der Konfiguration (In-Memory oder Database-Persistent).
 */

import { PrismaClient } from '@prisma-sfm-dashboard/client';
import { PerformanceMonitoringService } from './performance-monitoring.service';
import DatabasePerformanceMonitoringService from './performance-monitoring-db.service';

export interface PerformanceMonitoringConfig {
  useDatabasePersistence: boolean;
  inMemoryConfig?: {
    maxMetrics: number;
    cleanupInterval: number;
  };
  databaseConfig?: {
    enableInMemoryCache: boolean;
    inMemoryCacheSize: number;
    batchSize: number;
    flushInterval: number;
    retentionDays: number;
    enableAggregation: boolean;
  };
}

const DEFAULT_CONFIG: PerformanceMonitoringConfig = {
  useDatabasePersistence: true, // Default to database persistence for production
  inMemoryConfig: {
    maxMetrics: 10000,
    cleanupInterval: 60 * 60 * 1000 // 1 hour
  },
  databaseConfig: {
    enableInMemoryCache: true,
    inMemoryCacheSize: 1000,
    batchSize: 50,
    flushInterval: 30 * 1000, // 30 seconds
    retentionDays: 90, // 3 months
    enableAggregation: true
  }
};

export class PerformanceMonitoringFactory {
  private static instance: PerformanceMonitoringService | DatabasePerformanceMonitoringService | null = null;
  private static config: PerformanceMonitoringConfig;

  /**
   * Initialize the performance monitoring service
   */
  static initialize(
    prisma?: PrismaClient, 
    config: Partial<PerformanceMonitoringConfig> = {}
  ): PerformanceMonitoringService | DatabasePerformanceMonitoringService {
    
    this.config = { ...DEFAULT_CONFIG, ...config };

    if (this.instance) {
      console.warn('⚠️ [PERF-FACTORY] Performance monitoring service already initialized');
      return this.instance;
    }

    if (this.config.useDatabasePersistence) {
      if (!prisma) {
        throw new Error('Prisma client is required for database-persistent performance monitoring');
      }
      
      console.log('📊 [PERF-FACTORY] Initializing database-persistent performance monitoring');
      this.instance = new DatabasePerformanceMonitoringService(prisma, this.config.databaseConfig);
    } else {
      console.log('📊 [PERF-FACTORY] Initializing in-memory performance monitoring');
      this.instance = new PerformanceMonitoringService();
    }

    return this.instance;
  }

  /**
   * Get the current performance monitoring instance
   */
  static getInstance(): PerformanceMonitoringService | DatabasePerformanceMonitoringService {
    if (!this.instance) {
      throw new Error('Performance monitoring service not initialized. Call initialize() first.');
    }
    return this.instance;
  }

  /**
   * Check if database persistence is enabled
   */
  static isDatabasePersistenceEnabled(): boolean {
    return this.config?.useDatabasePersistence || false;
  }

  /**
   * Destroy the current instance
   */
  static destroy(): void {
    if (this.instance) {
      this.instance.destroy();
      this.instance = null;
    }
  }

  /**
   * Reinitialize with new configuration
   */
  static reinitialize(
    prisma?: PrismaClient, 
    config: Partial<PerformanceMonitoringConfig> = {}
  ): PerformanceMonitoringService | DatabasePerformanceMonitoringService {
    this.destroy();
    return this.initialize(prisma, config);
  }
}

// Convenience functions for common operations
export const initializePerformanceMonitoring = (
  prisma?: PrismaClient, 
  config?: Partial<PerformanceMonitoringConfig>
) => PerformanceMonitoringFactory.initialize(prisma, config);

export const getPerformanceMonitor = () => PerformanceMonitoringFactory.getInstance();

export const isUsingDatabasePersistence = () => PerformanceMonitoringFactory.isDatabasePersistenceEnabled();

export default PerformanceMonitoringFactory;