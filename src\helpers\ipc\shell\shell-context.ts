/**
 * Shell-Context für IPC
 * 
 * Stellt Shell-Funktionalitäten vom Main-Process
 * an den Renderer-Process bereit.
 */

import { ipcRenderer } from 'electron';

/**
 * Shell API für das Frontend
 */
export interface ShellAPI {
  /**
   * Öffnet eine URL in der Standard-Anwendung des Systems
   * @param url Die zu öffnende URL
   */
  openExternal: (url: string) => Promise<void>;
}

/**
 * Context-Bridge API für Shell-Funktionen
 */
export function exposeShellContext(): ShellAPI {
  return {
    /**
     * Öffnet eine URL extern (z.B. im Standard-Browser oder E-Mail-Client)
     */
    openExternal: async (url: string): Promise<void> => {
      try {
        await ipcRenderer.invoke('shell:open-external', url);
      } catch (error) {
        console.error('Fehler beim Öffnen der externen URL:', error);
        throw error;
      }
    }
  };
}