/**
 * Datenbank-Typdefinitionen für das Backend
 * 
 * Diese Definitionen werden sowohl vom Backend als auch vom Frontend verwendet.
 * Das Frontend importiert diese Typen über die API-Schnittstelle.
 */

// Basis-Typen für Datenpunkte
export interface ServiceLevelDataPoint {
  datum: string;
  servicegrad: number;
}

export interface DailyPerformanceDataPoint {
  datum: string;
  value: number;
}

export interface PickingDataPoint {
  date: string;
  atrl: number;
  aril: number;
  fuellgrad_aril: number;
}

export interface ReturnsDataPoint {
  name: string;
  value: number;
}

export interface DeliveryPositionsDataPoint {
  date: string;
  ausgeliefert_lup: number;
  rueckstaendig: number;
}

export interface TagesleistungDataPoint {
  date: string;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
}

export interface AblaengereiDataPoint {
  id?: number;
  datum: string;
  cutTT: number | null;
  cutTR: number | null;
  cutRR: number | null;
  pickCut: number | null;
  lagerCut220: number | null;
  lagerCut240: number | null;
  lagerCut200: number | null;
  cutLagerK200: number | null;
  cutLagerK240: number | null;
  cutLagerK220: number | null;
  cutLager200: number | null;
  cutLagerR240: number | null;
  cutLagerR220: number | null;
}

export interface WEDataPoint {
  id?: number;
  datum: string;
  weAtrl: number | null;  // Entspricht atrl in dispatch_data
  weManl: number | null;  // Entspricht aril in dispatch_data
}

export interface Lagerauslastung200DataPoint {
  date: string;
  auslastungA: number;
  auslastungB: number;
  auslastungC: number;
  gesamt?: number;
}

export interface Lagerauslastung240DataPoint {
  date: string;
  auslastungA: number;
  auslastungB: number;
  auslastungC: number;
  gesamt?: number;
}

// Weitere Typen aus der ursprünglichen database.types.ts
export interface DatabaseRow {
  [key: string]: any;
}

export interface AblaengereiRecord {
  id?: number;
  datum: string;
  cutTT: number | null;
  cutTR: number | null;
  cutRR: number | null;
  pickCut: number | null;
  lagerCut220: number | null;
  lagerCut240: number | null;
  lagerCut200: number | null;
  cutLagerK200: number | null;
  cutLagerK240: number | null;
  cutLagerK220: number | null;
  cutLager200: number | null;
  cutLagerR240: number | null;
  cutLagerR220: number | null;
}

export interface SchnitteDataPoint {
  id?: number;
  datum: string;
  schnitte: number;
  maschinentyp: string;
}

export interface AtrlDataPoint {
  id?: number;
  Datum: string;
  weAtrl: number | null;
  EinlagerungAblKunde: number | null;
  EinlagerungAblRest: number | null;
  umlagerungen: number | null;
  waTaPositionen: number | null;
  AuslagerungAbl: number | null;
}


export interface ArilDataPoint {
  id?: number;
  Datum: string;
  waTaPositionen: number;
  cuttingLagerKunde: number;
  cuttingLagerRest: number;
  Umlagerungen: number;
  lagerCutting: number;
}

export interface SystemAtrlDataPoint {
  id?: number;
  datum: string;
  atrl: number;
}

export interface SystemArilDataPoint {
  id?: number;
  datum: string;
  aril: number;
}

export interface QMDataPoint {
  id?: number;
  datum: string;
  qm: number;
}

export interface DeliveryDataPoint {
  id?: number;
  datum: string;
  ausgeliefert_lup: number;
  rueckstaendig: number;
}

export interface CuttingDataPoint {
  datum: string;
  cutTT: number;
  cutTR: number;
  cutRR: number;
  pickCut: number;
}

export interface LagerCutsDataPoint {
  datum: string;
  lagerSumme: number;
  cutLagerKSumme: number;
  cutLagerRSumme: number;
}

// System FTS Verfügbarkeitsdaten
export interface SystemFTSDataPoint {
  id: number;
  Datum: string;
  verfuegbarkeitFTS: number;
}

// Konfigurationstypen
export interface DatabaseConfig {
  path: string;
  tables: {
    [key: string]: {
      name: string;
      columns: string[];
    };
  };
}

// API-Typen für die Backend-Frontend-Kommunikation
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}
