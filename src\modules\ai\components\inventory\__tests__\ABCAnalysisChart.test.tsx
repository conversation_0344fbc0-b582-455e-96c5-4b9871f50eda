import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ABCAnalysisChart } from '../ABCAnalysisChart';
import { ABCClassification, ABCAnalysisResult } from '../../../services/inventory/types';

// Mock recharts components
vi.mock('recharts', () => ({
  BarChart: ({ children, data }: any) => (
    <div data-testid="bar-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Bar: ({ dataKey, onClick }: any) => (
    <div data-testid="bar" data-key={dataKey} onClick={onClick} />
  ),
  XAxis: ({ dataKey }: any) => <div data-testid="x-axis" data-key={dataKey} />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: ({ data }: any) => (
    <div data-testid="pie" data-pie-data={JSON.stringify(data)} />
  ),
  Cell: () => <div data-testid="cell" />,
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="responsive-container">{children}</div>
  )
}));

// Mock chart components
vi.mock('@/components/ui/chart', () => ({
  ChartContainer: ({ children }: any) => (
    <div data-testid="chart-container">{children}</div>
  ),
  ChartTooltip: ({ children }: any) => (
    <div data-testid="chart-tooltip">{children}</div>
  ),
  ChartTooltipContent: ({ children }: any) => (
    <div data-testid="chart-tooltip-content">{children}</div>
  )
}));

describe('ABCAnalysisChart', () => {
  const mockClassification: ABCClassification = {
    classA: [
      {
        id: 'item-1',
        name: 'High Value Item',
        category: 'Category A',
        currentStock: 100,
        unitPrice: 50,
        lastUpdated: new Date()
      }
    ],
    classB: [
      {
        id: 'item-2',
        name: 'Medium Value Item',
        category: 'Category B',
        currentStock: 200,
        unitPrice: 25,
        lastUpdated: new Date()
      }
    ],
    classC: [
      {
        id: 'item-3',
        name: 'Low Value Item',
        category: 'Category C',
        currentStock: 500,
        unitPrice: 5,
        lastUpdated: new Date()
      }
    ],
    reclassifications: [
      {
        itemId: 'item-1',
        previousClass: 'B',
        newClass: 'A',
        reason: 'Increased consumption',
        confidence: 0.85
      }
    ],
    analysisDate: new Date(),
    criteria: {
      method: 'value',
      classAThreshold: 80,
      classBThreshold: 95,
      timeWindow: 90
    }
  };

  const mockAnalysisResults: ABCAnalysisResult[] = [
    {
      itemId: 'item-1',
      classification: 'A',
      value: 5000,
      percentage: 50,
      cumulativePercentage: 50,
      rank: 1,
      confidence: 0.9
    },
    {
      itemId: 'item-2',
      classification: 'B',
      value: 3000,
      percentage: 30,
      cumulativePercentage: 80,
      rank: 2,
      confidence: 0.8
    },
    {
      itemId: 'item-3',
      classification: 'C',
      value: 2000,
      percentage: 20,
      cumulativePercentage: 100,
      rank: 3,
      confidence: 0.7
    }
  ];

  const mockOnItemSelect = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders ABC analysis chart with classification data', () => {
    render(
      <ABCAnalysisChart
        classification={mockClassification}
        analysisResults={mockAnalysisResults}
        onItemSelect={mockOnItemSelect}
      />
    );

    // Check if summary cards are rendered
    expect(screen.getByText('Gesamt Artikel')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument(); // Total items
    expect(screen.getByText('Klasse A Artikel')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // Class A items

    // Check if charts are rendered
    expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
  });

  it('displays class distribution correctly', () => {
    render(
      <ABCAnalysisChart
        classification={mockClassification}
        analysisResults={mockAnalysisResults}
        onItemSelect={mockOnItemSelect}
      />
    );

    // Check class distribution percentages
    expect(screen.getByText('33% der Artikel')).toBeInTheDocument(); // Class A percentage
    expect(screen.getByText('Klassenverteilung')).toBeInTheDocument();
  });

  it('shows reclassification alerts when present', () => {
    render(
      <ABCAnalysisChart
        classification={mockClassification}
        analysisResults={mockAnalysisResults}
        onItemSelect={mockOnItemSelect}
      />
    );

    // Check reclassification alert
    expect(screen.getByText('Umklassifizierungen')).toBeInTheDocument();
    expect(screen.getByText('1 Artikel wurden umklassifiziert')).toBeInTheDocument();
    expect(screen.getByText('Increased consumption')).toBeInTheDocument();
  });

  it('displays class details with item information', () => {
    render(
      <ABCAnalysisChart
        classification={mockClassification}
        analysisResults={mockAnalysisResults}
        onItemSelect={mockOnItemSelect}
      />
    );

    // Check class A details
    expect(screen.getByText('Klasse A Artikel')).toBeInTheDocument();
    expect(screen.getByText('High Value Item')).toBeInTheDocument();
    expect(screen.getByText('Category A')).toBeInTheDocument();

    // Check class B details
    expect(screen.getByText('Klasse B Artikel')).toBeInTheDocument();
    expect(screen.getByText('Medium Value Item')).toBeInTheDocument();

    // Check class C details
    expect(screen.getByText('Klasse C Artikel')).toBeInTheDocument();
    expect(screen.getByText('Low Value Item')).toBeInTheDocument();
  });

  it('calls onItemSelect when item is clicked', async () => {
    render(
      <ABCAnalysisChart
        classification={mockClassification}
        analysisResults={mockAnalysisResults}
        onItemSelect={mockOnItemSelect}
      />
    );

    // Click on an item
    const itemElement = screen.getByText('High Value Item');
    fireEvent.click(itemElement);

    await waitFor(() => {
      expect(mockOnItemSelect).toHaveBeenCalledWith('item-1');
    });
  });

  it('handles empty classification data gracefully', () => {
    const emptyClassification: ABCClassification = {
      classA: [],
      classB: [],
      classC: [],
      reclassifications: [],
      analysisDate: new Date(),
      criteria: {
        method: 'value',
        classAThreshold: 80,
        classBThreshold: 95,
        timeWindow: 90
      }
    };

    render(
      <ABCAnalysisChart
        classification={emptyClassification}
        analysisResults={[]}
        onItemSelect={mockOnItemSelect}
      />
    );

    // Should still render structure without errors
    expect(screen.getByText('Gesamt Artikel')).toBeInTheDocument();
    expect(screen.getByText('0')).toBeInTheDocument();
  });

  it('displays value contribution chart with correct data', () => {
    render(
      <ABCAnalysisChart
        classification={mockClassification}
        analysisResults={mockAnalysisResults}
        onItemSelect={mockOnItemSelect}
      />
    );

    // Check value contribution chart
    expect(screen.getByText('Wertbeitrag Top 20')).toBeInTheDocument();
    
    // Check if chart data is passed correctly
    const barChart = screen.getByTestId('bar-chart');
    const chartData = JSON.parse(barChart.getAttribute('data-chart-data') || '[]');
    expect(chartData).toHaveLength(3);
    expect(chartData[0].itemId).toBe('item-1');
    expect(chartData[0].value).toBe(5000);
  });

  it('shows correct summary metrics', () => {
    render(
      <ABCAnalysisChart
        classification={mockClassification}
        analysisResults={mockAnalysisResults}
        onItemSelect={mockOnItemSelect}
      />
    );

    // Check summary metrics
    expect(screen.getByText('50%')).toBeInTheDocument(); // Class A value percentage
    expect(screen.getByText('5.000 €')).toBeInTheDocument(); // Class A value
  });

  it('handles missing onItemSelect prop gracefully', () => {
    render(
      <ABCAnalysisChart
        classification={mockClassification}
        analysisResults={mockAnalysisResults}
      />
    );

    // Should render without errors even without onItemSelect
    expect(screen.getByText('High Value Item')).toBeInTheDocument();
  });

  it('displays confidence levels in reclassifications', () => {
    render(
      <ABCAnalysisChart
        classification={mockClassification}
        analysisResults={mockAnalysisResults}
        onItemSelect={mockOnItemSelect}
      />
    );

    // Check confidence display in reclassifications
    expect(screen.getByText('85%')).toBeInTheDocument(); // Confidence percentage
    expect(screen.getByText('B → A')).toBeInTheDocument(); // Classification change
  });

  it('truncates long item lists correctly', () => {
    // Create classification with many items
    const manyItemsClassification: ABCClassification = {
      ...mockClassification,
      classA: Array.from({ length: 15 }, (_, i) => ({
        id: `item-${i}`,
        name: `Item ${i}`,
        category: 'Category',
        currentStock: 100,
        unitPrice: 10,
        lastUpdated: new Date()
      }))
    };

    render(
      <ABCAnalysisChart
        classification={manyItemsClassification}
        analysisResults={mockAnalysisResults}
        onItemSelect={mockOnItemSelect}
      />
    );

    // Should show truncation message
    expect(screen.getByText('... und 5 weitere')).toBeInTheDocument();
  });
});