"use strict";
/**
 * Performance Tracking Middleware
 *
 * Automatisches Tracking aller API-Requests für Performance-Monitoring
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.routePerformanceTracker = exports.performanceTrackingMiddleware = void 0;
const performance_monitoring_service_1 = __importDefault(require("../services/performance-monitoring.service"));
/**
 * Middleware zum automatischen Tracking aller API-Requests
 */
const performanceTrackingMiddleware = (req, res, next) => {
    // Nur API-Requests tracken (nicht statische Assets)
    if (!req.path.startsWith('/api/')) {
        return next();
    }
    // Startzeit für Request-Dauer-Messung
    req.startTime = Date.now();
    // Original res.end überschreiben um Response-Zeit zu messen
    const originalEnd = res.end;
    res.end = function (chunk, encoding) {
        const duration = Date.now() - (req.startTime || Date.now());
        const success = res.statusCode < 400;
        // Performance-Metriken aufzeichnen
        try {
            // Query-Performance basierend auf Route
            const queryType = getQueryTypeFromPath(req.path);
            if (queryType) {
                performance_monitoring_service_1.default.recordQueryPerformance(queryType, duration, success, {
                    dataSize: getResponseSize(chunk),
                    cacheHit: false // TODO: Cache-Status ermitteln
                });
            }
            // Response-Zeit aufzeichnen
            performance_monitoring_service_1.default.recordResponseTime(req.path, false, // enriched - für normale API-Calls false
            duration, 0, // enrichmentTime - nicht verfügbar
            0, // llmTime - nicht verfügbar  
            getResponseSize(chunk));
        }
        catch (error) {
            console.warn('Performance tracking error:', error);
        }
        // Original end-Funktion aufrufen
        return originalEnd.call(this, chunk, encoding);
    };
    next();
};
exports.performanceTrackingMiddleware = performanceTrackingMiddleware;
/**
 * Ermittelt Query-Type basierend auf API-Pfad
 */
function getQueryTypeFromPath(path) {
    if (path.includes('/stoerungen') || path.includes('/errors')) {
        return 'stoerungen';
    }
    if (path.includes('/dispatch') || path.includes('/versand')) {
        return 'dispatch';
    }
    if (path.includes('/cutting') || path.includes('/ablaengerei')) {
        return 'cutting';
    }
    // Für allgemeine API-Calls wie /database, /system etc.
    if (path.includes('/database') || path.includes('/system')) {
        return 'dispatch'; // Default zu dispatch für allgemeine Queries
    }
    return null;
}
/**
 * Schätzt Response-Größe
 */
function getResponseSize(chunk) {
    if (!chunk)
        return 0;
    if (typeof chunk === 'string') {
        return Buffer.byteLength(chunk, 'utf8');
    }
    if (Buffer.isBuffer(chunk)) {
        return chunk.length;
    }
    // Für Objekte: JSON-Serialisierung schätzen
    try {
        return Buffer.byteLength(JSON.stringify(chunk), 'utf8');
    }
    catch (_a) {
        return 0;
    }
}
/**
 * Middleware für spezifische Route-Performance-Tracking
 */
const routePerformanceTracker = (routeName) => {
    return (req, res, next) => {
        const startTime = Date.now();
        res.on('finish', () => {
            const duration = Date.now() - startTime;
            const success = res.statusCode < 400;
            performance_monitoring_service_1.default.recordResponseTime(routeName, false, // enriched - für normale API-Calls false
            duration, 0, // enrichmentTime - nicht verfügbar
            0, // llmTime - nicht verfügbar
            0 // dataSize - nicht verfügbar bei finish event
            );
        });
        next();
    };
};
exports.routePerformanceTracker = routePerformanceTracker;
