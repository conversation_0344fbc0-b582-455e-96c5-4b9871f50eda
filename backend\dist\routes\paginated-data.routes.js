"use strict";
/**
 * Paginated Data API Routes
 *
 * Demonstriert Cursor- und Offset-basierte Paginierung
 * für große Datasets wie alle_daten und Ablaengerei.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const client_1 = require("@prisma-sfm-dashboard/client");
const pagination_service_1 = require("../services/pagination.service");
const rate_limiting_middleware_1 = require("../middleware/rate-limiting.middleware");
const router = express_1.default.Router();
const prisma = new client_1.PrismaClient();
/**
 * Hilfsfunktion für Error-Handling
 */
async function handleRequest(req, res, operation) {
    try {
        const result = await operation();
        const response = {
            success: true,
            data: result
        };
        res.json(response);
    }
    catch (error) {
        console.error(`[PAGINATED-DATA-API] <PERSON><PERSON> für ${req.originalUrl}:`, error);
        const response = {
            success: false,
            error: error instanceof Error ? error.message : 'Unbekannter Fehler'
        };
        res.status(500).json(response);
    }
}
// Rate-Limiting für Daten-Endpunkte
router.use(rate_limiting_middleware_1.rateLimitConfig.dataEndpoint);
/**
 * Paginierte alle_daten mit Cursor-Pagination
 */
router.get('/alle-daten/cursor', async (req, res) => {
    await handleRequest(req, res, async () => {
        const paginationParams = pagination_service_1.paginationService.parseRequestParams(req.query);
        paginationParams.strategy = pagination_service_1.PaginationStrategy.CURSOR;
        if (!paginationParams.cursor) {
            paginationParams.cursor = pagination_service_1.paginationService.parseCursorParams(req.query);
        }
        const { dateRange } = req.query;
        let whereClause = {};
        // Datum-Filter falls angegeben
        if (dateRange) {
            try {
                const { startDate, endDate } = JSON.parse(dateRange);
                whereClause.Datum = {
                    gte: startDate,
                    lte: endDate
                };
            }
            catch (error) {
                // Ignoriere ungültigen dateRange
            }
        }
        const query = {
            where: whereClause,
            findMany: (config) => prisma.alle_daten.findMany(config),
            findFirst: (config) => prisma.alle_daten.findFirst(config),
            count: (config) => prisma.alle_daten.count(config)
        };
        const result = await pagination_service_1.paginationService.applyCursorPagination(query, paginationParams.cursor, 'Datum' // Standard-Sortierung nach Datum
        );
        return {
            ...result,
            meta: {
                strategy: 'cursor',
                totalCount: result.totalCount,
                recommendations: pagination_service_1.paginationService.getPerformanceRecommendations(result.totalCount || 0, paginationParams.cursor.first || paginationParams.cursor.last || 50)
            }
        };
    });
});
/**
 * Paginierte alle_daten mit Offset-Pagination
 */
router.get('/alle-daten/offset', async (req, res) => {
    await handleRequest(req, res, async () => {
        const paginationParams = pagination_service_1.paginationService.parseRequestParams(req.query);
        paginationParams.strategy = pagination_service_1.PaginationStrategy.OFFSET;
        if (!paginationParams.offset) {
            paginationParams.offset = pagination_service_1.paginationService.parseOffsetParams(req.query);
        }
        const { dateRange } = req.query;
        let whereClause = {};
        // Datum-Filter falls angegeben
        if (dateRange) {
            try {
                const { startDate, endDate } = JSON.parse(dateRange);
                whereClause.Datum = {
                    gte: startDate,
                    lte: endDate
                };
            }
            catch (error) {
                // Ignoriere ungültigen dateRange
            }
        }
        const query = {
            where: whereClause,
            findMany: (config) => prisma.alle_daten.findMany(config),
            count: (config) => prisma.alle_daten.count(config)
        };
        const result = await pagination_service_1.paginationService.applyOffsetPagination(query, paginationParams.offset, 'Datum');
        return {
            ...result,
            meta: {
                ...result.meta,
                strategy: 'offset',
                recommendations: pagination_service_1.paginationService.getPerformanceRecommendations(result.meta.totalCount, result.meta.limit)
            }
        };
    });
});
/**
 * Auto-Pagination für alle_daten (automatische Strategie-Erkennung)
 */
router.get('/alle-daten', async (req, res) => {
    await handleRequest(req, res, async () => {
        const paginationParams = pagination_service_1.paginationService.parseRequestParams(req.query);
        const { dateRange } = req.query;
        let whereClause = {};
        if (dateRange) {
            try {
                const { startDate, endDate } = JSON.parse(dateRange);
                whereClause.Datum = {
                    gte: startDate,
                    lte: endDate
                };
            }
            catch (error) {
                // Ignoriere ungültigen dateRange
            }
        }
        const query = {
            where: whereClause,
            findMany: (config) => prisma.alle_daten.findMany(config),
            findFirst: (config) => prisma.alle_daten.findFirst(config),
            count: (config) => prisma.alle_daten.count(config)
        };
        const result = await pagination_service_1.paginationService.applyPagination(query, paginationParams, 'Datum');
        return {
            ...result,
            meta: {
                ...result.meta,
                strategy: paginationParams.strategy,
                autoDetected: true
            }
        };
    });
});
/**
 * Paginierte Ablaengerei-Daten mit Cursor-Pagination
 */
router.get('/ablaengerei/cursor', async (req, res) => {
    await handleRequest(req, res, async () => {
        const paginationParams = pagination_service_1.paginationService.parseRequestParams(req.query);
        paginationParams.strategy = pagination_service_1.PaginationStrategy.CURSOR;
        if (!paginationParams.cursor) {
            paginationParams.cursor = pagination_service_1.paginationService.parseCursorParams(req.query);
        }
        const { dateRange, maschine } = req.query;
        let whereClause = {};
        if (dateRange) {
            try {
                const { startDate, endDate } = JSON.parse(dateRange);
                whereClause.Datum = {
                    gte: startDate,
                    lte: endDate
                };
            }
            catch (error) {
                // Ignoriere ungültigen dateRange
            }
        }
        if (maschine) {
            whereClause.Maschine = {
                contains: maschine,
                mode: 'insensitive'
            };
        }
        const query = {
            where: whereClause,
            findMany: (config) => prisma.ablaengerei.findMany(config),
            findFirst: (config) => prisma.ablaengerei.findFirst(config),
            count: (config) => prisma.ablaengerei.count(config)
        };
        const result = await pagination_service_1.paginationService.applyCursorPagination(query, paginationParams.cursor, 'Datum');
        return {
            ...result,
            meta: {
                strategy: 'cursor',
                filters: { dateRange, maschine },
                totalCount: result.totalCount
            }
        };
    });
});
/**
 * Paginierte Ablaengerei-Daten mit Offset-Pagination
 */
router.get('/ablaengerei/offset', async (req, res) => {
    await handleRequest(req, res, async () => {
        const paginationParams = pagination_service_1.paginationService.parseRequestParams(req.query);
        paginationParams.strategy = pagination_service_1.PaginationStrategy.OFFSET;
        if (!paginationParams.offset) {
            paginationParams.offset = pagination_service_1.paginationService.parseOffsetParams(req.query);
        }
        const { dateRange, maschine } = req.query;
        let whereClause = {};
        if (dateRange) {
            try {
                const { startDate, endDate } = JSON.parse(dateRange);
                whereClause.Datum = {
                    gte: startDate,
                    lte: endDate
                };
            }
            catch (error) {
                // Ignoriere ungültigen dateRange
            }
        }
        if (maschine) {
            whereClause.Maschine = {
                contains: maschine,
                mode: 'insensitive'
            };
        }
        const query = {
            where: whereClause,
            findMany: (config) => prisma.ablaengerei.findMany(config),
            count: (config) => prisma.ablaengerei.count(config)
        };
        const result = await pagination_service_1.paginationService.applyOffsetPagination(query, paginationParams.offset, 'Datum');
        return {
            ...result,
            meta: {
                ...result.meta,
                strategy: 'offset',
                filters: { dateRange, maschine }
            }
        };
    });
});
/**
 * Pagination-Performance-Vergleich
 * TODO: Fix TypeScript union type issues
 */
router.get('/performance-comparison', async (req, res) => {
    // Temporarily disabled due to TypeScript union type issues
    await handleRequest(req, res, async () => {
        return {
            message: "Performance comparison temporarily disabled",
            reason: "TypeScript union type compatibility issues",
            status: "maintenance"
        };
    });
    return;
    /*
    await handleRequest(req, res, async () => {
      const { table, limit = '100' } = req.query as {
        table?: 'alle_daten' | 'ablaengerei';
        limit?: string;
      };
  
      if (!table || !['alle_daten', 'ablaengerei'].includes(table)) {
        throw new Error('Invalid table parameter. Must be "alle_daten" or "ablaengerei"');
      }
  
      const limitNum = parseInt(limit, 10);
      // Handle table-specific queries to avoid TypeScript union type issues
  
      // Teste beide Pagination-Strategien
      const [cursorResult, offsetResult] = await Promise.all([
        measureExecutionTime(async () => {
          const query = {
            where: {},
            findMany: (config: any) => prismaTable.findMany(config),
            findFirst: (config: any) => prismaTable.findFirst(config),
            count: (config: any) => prismaTable.count(config)
          };
  
          return paginationService.applyCursorPagination(
            query,
            { first: limitNum, orderBy: 'Datum' },
            'Datum'
          );
        }),
        measureExecutionTime(async () => {
          const query = {
            where: {},
            findMany: (config: any) => prismaTable.findMany(config),
            count: (config: any) => prismaTable.count(config)
          };
  
          return paginationService.applyOffsetPagination(
            query,
            { limit: limitNum, page: 1, orderBy: 'Datum' },
            'Datum'
          );
        })
      ]);
  
      return {
        table,
        limit: limitNum,
        cursor: {
          executionTime: cursorResult.executionTime,
          resultCount: cursorResult.result.edges.length,
          hasNextPage: cursorResult.result.pageInfo.hasNextPage
        },
        offset: {
          executionTime: offsetResult.executionTime,
          resultCount: offsetResult.result.data.length,
          totalCount: offsetResult.result.meta.totalCount,
          totalPages: offsetResult.result.meta.totalPages
        },
        recommendation: cursorResult.executionTime < offsetResult.executionTime
          ? 'cursor'
          : 'offset',
        analysis: {
          cursorAdvantages: [
            'Konsistente Performance bei tiefen Offsets',
            'Echte Echtzeit-Paginierung',
            'Keine doppelten/fehlenden Einträge bei Änderungen'
          ],
          offsetAdvantages: [
            'Direkter Sprung zu beliebiger Seite',
            'Einfacher zu implementieren',
            'Gesamtanzahl verfügbar'
          ]
        }
      };
    */
});
/**
 * Pagination-Schema-Informationen
 */
router.get('/schema-info', async (req, res) => {
    await handleRequest(req, res, async () => {
        const tableInfo = {
            alle_daten: {
                description: 'Aggregierte Daten aus allen Bereichen',
                recommendedStrategy: 'cursor',
                reasons: [
                    'Sehr große Datenmenge (1M+ Datensätze)',
                    'Häufige Einfügungen neuer Daten',
                    'Zeitbasierte Abfragen dominieren'
                ],
                sortableFields: ['Datum', 'id'],
                defaultOrderBy: 'Datum'
            },
            ablaengerei: {
                description: 'Schneid- und Bearbeitungsoperationen',
                recommendedStrategy: 'both',
                reasons: [
                    'Moderate Datenmenge (10K-100K Datensätze)',
                    'Sowohl sequentielle als auch Random-Access-Patterns',
                    'Filteroptionen nach Maschine häufig genutzt'
                ],
                sortableFields: ['Datum', 'Maschine', 'id'],
                defaultOrderBy: 'Datum'
            }
        };
        const paginationGuide = {
            cursorPagination: {
                when: 'Große Datasets, zeitbasierte Daten, Echtzeitanwendungen',
                parameters: {
                    first: 'Anzahl Datensätze nach Cursor',
                    after: 'Cursor für Vorwärtspaginierung',
                    last: 'Anzahl Datensätze vor Cursor',
                    before: 'Cursor für Rückwärtspaginierung'
                },
                example: '/api/paginated/alle-daten/cursor?first=50&after=eyJmaWVsZCI6IkRhdHVtI...'
            },
            offsetPagination: {
                when: 'Kleinere Datasets, Random Access, Seitennummerierung',
                parameters: {
                    page: 'Seitennummer (1-basiert)',
                    limit: 'Anzahl Datensätze pro Seite',
                    offset: 'Direkter Offset (alternativ zu page)'
                },
                example: '/api/paginated/alle-daten/offset?page=1&limit=50'
            }
        };
        return {
            tableInfo,
            paginationGuide,
            bestPractices: [
                'Verwenden Sie Cursor-Pagination für große, zeitbasierte Datasets',
                'Verwenden Sie Offset-Pagination für kleinere Datasets mit Random Access',
                'Limitieren Sie die Seitengröße auf maximal 1000 Datensätze',
                'Implementieren Sie angemessene Indizierung für Sortierfelder',
                'Kombinieren Sie Pagination mit Filteroptionen für bessere Performance'
            ]
        };
    });
});
/**
 * Helper-Funktion für Performance-Messung
 */
async function measureExecutionTime(operation) {
    const startTime = Date.now();
    const result = await operation();
    const executionTime = Date.now() - startTime;
    return { result, executionTime };
}
exports.default = router;
