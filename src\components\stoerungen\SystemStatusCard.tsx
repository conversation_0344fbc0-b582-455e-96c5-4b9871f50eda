import React, { useState } from 'react';
import { GlareCard } from '@/components/Animation/Cards/glare-card';
import { cn } from '@/lib/utils';
import { SystemStatus } from '@/types/stoerungen.types';
import { Activity, AlertTriangle, CheckCircle, XCircle, Power } from 'lucide-react';

interface SystemStatusCardProps {
  system: SystemStatus;
  backgroundStyle: any;
  formatLastCheck: (timestamp: string) => string;
  getStatusColor: (status: string) => string;
}

export const SystemStatusCard: React.FC<SystemStatusCardProps> = ({
  system,
  backgroundStyle,
  formatLastCheck,
  getStatusColor
}) => {
  const [isFlipped, setIsFlipped] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OK':
        return <CheckCircle className="h-6 w-6 text-white" />;
      case 'WARNING':
        return <AlertTriangle className="h-6 w-6 text-white" />;
      case 'ERROR':
        return <XCircle className="h-6 w-6 text-white" />;
      case 'OFF':
        return <Power className="h-6 w-6 text-white" />;
      default:
        return <Activity className="h-6 w-6 text-white" />;
    }
  };

  const handleCardClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsFlipped(!isFlipped);
  };

  return (
    <div 
      className="h-full min-h-[40px] cursor-pointer relative"
      style={{ perspective: '1000px' }}
      onClick={handleCardClick}
    >
      <div 
        className={cn(
          "w-full h-full transition-transform duration-500",
          isFlipped ? "" : ""
        )}
        style={{
          transformStyle: 'preserve-3d',
          transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)'
        }}
      >
        {/* Front Side */}
        <div 
          className="w-full h-full"
          style={{ 
            backfaceVisibility: 'hidden',
            position: isFlipped ? 'absolute' : 'relative',
            top: 0,
            left: 0
          }}
        >
          <GlareCard
            className={cn(
              "relative h-full min-h-[40px] transition-all duration-200",
              getStatusColor(system.status),
              system.status === 'ERROR' ? 'animate-pulse' : ''
            )}
          >
            <div 
              className="flex flex-col items-start justify-center text-black min-h-[40px] h-full p-1"
              style={backgroundStyle}
              title={`${system.system_name}: ${system.status} (${formatLastCheck(system.last_check)})`}
            >
              {getStatusIcon(system.status)}
              <span className="text-sm font-medium mt-0.5 text-left leading-tight">
                {system.system_name}
              </span>
              <span className="text-xs opacity-80 mt-0.5 text-left">
                {formatLastCheck(system.last_check)}
              </span>
            </div>
          </GlareCard>
        </div>

        {/* Back Side */}
        <div 
          className="w-full h-full"
          style={{ 
            backfaceVisibility: 'hidden',
            transform: 'rotateY(180deg)',
            position: isFlipped ? 'relative' : 'absolute',
            top: 0,
            left: 0
          }}
        >
          <GlareCard
            className={cn(
              "relative h-full min-h-[40px] transition-all duration-200",
              getStatusColor(system.status),
              system.status === 'ERROR' ? 'animate-pulse' : ''
            )}
          >
            <div 
              className="flex flex-col items-start justify-center text-black min-h-[40px] h-full p-1"
            >
              <div className="flex items-center mb-1">
                {getStatusIcon(system.status)}
                <span className="text-xs font-semibold ml-1">{system.status}</span>
              </div>
              
              {system.statusMessages && system.statusMessages.length > 0 ? (
                <div className="space-y-1">
                  <div className="text-sm font-medium text-left leading-tight">
                    {system.statusMessages[0].title}
                  </div>
                  <div className="text-xs text-left leading-tight opacity-90">
                    {system.statusMessages[0].description}
                  </div>
                </div>
              ) : (
                <div className="space-y-1">
                  <div className="text-sm font-medium text-left">
                    Status: {system.status}
                  </div>
                  <div className="text-xs text-left opacity-90">
                    Keine detaillierte Beschreibung verfügbar
                  </div>
                </div>
              )}
            </div>
          </GlareCard>
        </div>
      </div>
    </div>
  );
};