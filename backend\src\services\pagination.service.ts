/**
 * Pagination Service
 * 
 * Implementiert cursor-basierte und offset-basierte Paginierung
 * für große Datasets mit Performance-Optimierungen.
 */

/**
 * Pagination-Strategien
 */
export enum PaginationStrategy {
  CURSOR = 'cursor',
  OFFSET = 'offset'
}

/**
 * Sortierungs-Richtungen
 */
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc'
}

/**
 * Cursor-Pagination-Parameter
 */
export interface CursorPaginationParams {
  first?: number; // Anzahl der Datensätze nach dem Cursor
  after?: string; // Cursor-Position (base64-kodiert)
  last?: number; // Anzahl der Datensätze vor dem Cursor
  before?: string; // Cursor-Position (base64-kodiert)
  orderBy?: string; // Sortierung-Feld
  orderDirection?: SortDirection; // Sortierung-Richtung
}

/**
 * Offset-Pagination-Parameter
 */
export interface OffsetPaginationParams {
  page?: number; // Seiten-Nummer (1-basiert)
  limit?: number; // Anzahl der Datensätze pro Seite
  offset?: number; // Direkte Offset-Angabe
  orderBy?: string; // Sortierung-Feld
  orderDirection?: SortDirection; // Sortierung-Richtung
}

/**
 * Generische Pagination-Parameter
 */
export interface PaginationParams {
  strategy: PaginationStrategy;
  cursor?: CursorPaginationParams;
  offset?: OffsetPaginationParams;
}

/**
 * Page Info für Cursor-Pagination
 */
export interface PageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startCursor?: string;
  endCursor?: string;
  totalCount?: number;
}

/**
 * Pagination-Metadaten für Offset-Pagination
 */
export interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  limit: number;
  offset: number;
}

/**
 * Paginiertes Ergebnis für Cursor-Pagination
 */
export interface CursorPaginatedResult<T> {
  edges: Array<{
    cursor: string;
    node: T;
  }>;
  pageInfo: PageInfo;
  totalCount?: number;
}

/**
 * Paginiertes Ergebnis für Offset-Pagination
 */
export interface OffsetPaginatedResult<T> {
  data: T[];
  meta: PaginationMeta;
}

/**
 * Cursor-Informationen
 */
interface CursorInfo {
  field: string;
  value: any;
  id: string | number;
  direction: SortDirection;
}

/**
 * Pagination Service
 */
export class PaginationService {
  private static instance: PaginationService;
  
  private readonly DEFAULT_LIMIT = 50;
  private readonly MAX_LIMIT = 1000;
  private readonly DEFAULT_ORDER_BY = 'id';

  private constructor() {}

  static getInstance(): PaginationService {
    if (!PaginationService.instance) {
      PaginationService.instance = new PaginationService();
    }
    return PaginationService.instance;
  }

  /**
   * Cursor-basierte Paginierung implementieren
   */
  async applyCursorPagination<T extends Record<string, any>>(
    query: any, // Prisma-Query-Builder
    params: CursorPaginationParams,
    defaultOrderBy: string = this.DEFAULT_ORDER_BY
  ): Promise<CursorPaginatedResult<T>> {
    const orderBy = params.orderBy || defaultOrderBy;
    const orderDirection = params.orderDirection || SortDirection.ASC;
    
    // Validiere und normalisiere Parameter
    const first = this.validateLimit(params.first);
    const last = this.validateLimit(params.last);
    
    if (first && last) {
      throw new Error('Cannot specify both "first" and "last" parameters');
    }
    
    if (params.after && params.before) {
      throw new Error('Cannot specify both "after" and "before" cursors');
    }

    let limit = first || last || this.DEFAULT_LIMIT;
    const isReversed = !!last;
    
    // Cursor dekodieren
    let cursorCondition: any = {};
    if (params.after) {
      const cursor = this.decodeCursor(params.after);
      cursorCondition = this.buildCursorCondition(cursor, false, orderDirection);
    } else if (params.before) {
      const cursor = this.decodeCursor(params.before);
      cursorCondition = this.buildCursorCondition(cursor, true, orderDirection);
    }

    // Query konfigurieren
    const queryConfig: any = {
      where: {
        ...query.where,
        ...cursorCondition
      },
      orderBy: {
        [orderBy]: isReversed ? this.reverseDirection(orderDirection) : orderDirection,
        // Sekundäre Sortierung nach ID für Eindeutigkeit
        ...(orderBy !== 'id' && { id: orderDirection })
      },
      take: limit + 1 // +1 um hasNextPage zu bestimmen
    };

    // Query ausführen
    const results = await query.findMany(queryConfig);
    
    // Prüfe ob mehr Daten verfügbar sind
    const hasMore = results.length > limit;
    if (hasMore) {
      results.pop(); // Entferne den extra Datensatz
    }

    // Bei Rückwärts-Paginierung die Reihenfolge umkehren
    if (isReversed) {
      results.reverse();
    }

    // Edges und Cursors erstellen
    const edges = results.map((node: T) => ({
      cursor: this.encodeCursor(orderBy, node[orderBy], node.id, orderDirection),
      node
    }));

    // PageInfo erstellen
    const pageInfo: PageInfo = {
      hasNextPage: isReversed ? false : hasMore,
      hasPreviousPage: isReversed ? hasMore : false,
      startCursor: edges.length > 0 ? edges[0].cursor : undefined,
      endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : undefined
    };

    // Bei Bedarf auch Rückwärts-Navigation prüfen
    if (!isReversed && params.after) {
      // Prüfe ob vorherige Seite existiert
      const cursor = this.decodeCursor(params.after);
      const previousCondition = this.buildCursorCondition(cursor, true, orderDirection);
      
      const previousQuery = {
        ...query,
        where: {
          ...query.where,
          ...previousCondition
        },
        take: 1
      };
      
      const previousExists = await query.findFirst(previousQuery);
      pageInfo.hasPreviousPage = !!previousExists;
    }

    return {
      edges,
      pageInfo
    };
  }

  /**
   * Offset-basierte Paginierung implementieren
   */
  async applyOffsetPagination<T>(
    query: any, // Prisma-Query-Builder
    params: OffsetPaginationParams,
    defaultOrderBy: string = this.DEFAULT_ORDER_BY
  ): Promise<OffsetPaginatedResult<T>> {
    const orderBy = params.orderBy || defaultOrderBy;
    const orderDirection = params.orderDirection || SortDirection.ASC;
    const limit = this.validateLimit(params.limit);
    
    // Berechne Offset
    let offset = 0;
    if (params.offset !== undefined) {
      offset = Math.max(0, params.offset);
    } else if (params.page !== undefined) {
      const page = Math.max(1, params.page);
      offset = (page - 1) * limit;
    }

    // Query konfigurieren
    const queryConfig: any = {
      where: query.where,
      orderBy: {
        [orderBy]: orderDirection,
        // Sekundäre Sortierung nach ID für Konsistenz
        ...(orderBy !== 'id' && { id: orderDirection })
      },
      skip: offset,
      take: limit
    };

    // Parallel: Daten und Gesamtanzahl abrufen
    const [data, totalCount] = await Promise.all([
      query.findMany(queryConfig),
      query.count({ where: query.where })
    ]);

    // Pagination-Metadaten berechnen
    const totalPages = Math.ceil(totalCount / limit);
    const currentPage = Math.floor(offset / limit) + 1;

    const meta: PaginationMeta = {
      currentPage,
      totalPages,
      totalCount,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1,
      limit,
      offset
    };

    return {
      data,
      meta
    };
  }

  /**
   * Intelligente Pagination-Strategie-Auswahl
   */
  async applyPagination<T extends Record<string, any>>(
    query: any,
    params: PaginationParams,
    defaultOrderBy: string = this.DEFAULT_ORDER_BY
  ): Promise<CursorPaginatedResult<T> | OffsetPaginatedResult<T>> {
    switch (params.strategy) {
      case PaginationStrategy.CURSOR:
        if (!params.cursor) {
          throw new Error('Cursor parameters required for cursor pagination');
        }
        return this.applyCursorPagination(query, params.cursor, defaultOrderBy);
      
      case PaginationStrategy.OFFSET:
        if (!params.offset) {
          throw new Error('Offset parameters required for offset pagination');
        }
        return this.applyOffsetPagination(query, params.offset, defaultOrderBy);
      
      default:
        throw new Error(`Unknown pagination strategy: ${params.strategy}`);
    }
  }

  /**
   * Cursor-Funktionen
   */

  /**
   * Cursor kodieren
   */
  private encodeCursor(
    field: string,
    value: any,
    id: string | number,
    direction: SortDirection
  ): string {
    const cursorInfo: CursorInfo = {
      field,
      value,
      id,
      direction
    };
    
    const encoded = Buffer.from(JSON.stringify(cursorInfo)).toString('base64');
    return encoded;
  }

  /**
   * Cursor dekodieren
   */
  private decodeCursor(cursor: string): CursorInfo {
    try {
      const decoded = Buffer.from(cursor, 'base64').toString('utf-8');
      const cursorInfo = JSON.parse(decoded);
      
      if (!cursorInfo.field || cursorInfo.value === undefined || !cursorInfo.id) {
        throw new Error('Invalid cursor format');
      }
      
      return cursorInfo;
    } catch (error) {
      throw new Error('Invalid cursor format');
    }
  }

  /**
   * Cursor-Condition für Prisma-Query erstellen
   */
  private buildCursorCondition(
    cursor: CursorInfo,
    isBefore: boolean,
    orderDirection: SortDirection
  ): any {
    const { field, value, id } = cursor;
    
    // Bestimme Vergleichsoperator basierend auf Richtung
    const isAscending = orderDirection === SortDirection.ASC;
    const operator = isBefore
      ? (isAscending ? 'lt' : 'gt')
      : (isAscending ? 'gt' : 'lt');

    if (field === 'id') {
      return {
        id: {
          [operator]: id
        }
      };
    }

    // Für Nicht-ID-Felder: Kombiniere Feld-Wert und ID für eindeutige Sortierung
    return {
      OR: [
        {
          [field]: {
            [operator]: value
          }
        },
        {
          [field]: value,
          id: {
            [operator]: id
          }
        }
      ]
    };
  }

  /**
   * Hilfsfunktionen
   */

  private validateLimit(limit?: number): number {
    if (limit === undefined) {
      return this.DEFAULT_LIMIT;
    }
    
    if (limit < 1) {
      throw new Error('Limit must be positive');
    }
    
    if (limit > this.MAX_LIMIT) {
      throw new Error(`Limit cannot exceed ${this.MAX_LIMIT}`);
    }
    
    return limit;
  }

  private reverseDirection(direction: SortDirection): SortDirection {
    return direction === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC;
  }

  /**
   * Utility-Funktionen für API-Handler
   */

  /**
   * Parse Cursor-Parameter aus Request-Query
   */
  parseCursorParams(query: any): CursorPaginationParams {
    return {
      first: query.first ? parseInt(query.first, 10) : undefined,
      after: query.after || undefined,
      last: query.last ? parseInt(query.last, 10) : undefined,
      before: query.before || undefined,
      orderBy: query.orderBy || undefined,
      orderDirection: query.orderDirection === 'desc' ? SortDirection.DESC : SortDirection.ASC
    };
  }

  /**
   * Parse Offset-Parameter aus Request-Query
   */
  parseOffsetParams(query: any): OffsetPaginationParams {
    return {
      page: query.page ? parseInt(query.page, 10) : undefined,
      limit: query.limit ? parseInt(query.limit, 10) : undefined,
      offset: query.offset ? parseInt(query.offset, 10) : undefined,
      orderBy: query.orderBy || undefined,
      orderDirection: query.orderDirection === 'desc' ? SortDirection.DESC : SortDirection.ASC
    };
  }

  /**
   * Automatische Strategie-Erkennung basierend auf Request-Parametern
   */
  detectPaginationStrategy(query: any): PaginationStrategy {
    const hasCursorParams = !!(query.first || query.last || query.after || query.before);
    const hasOffsetParams = !!(query.page || query.offset || query.limit);
    
    if (hasCursorParams && hasOffsetParams) {
      throw new Error('Cannot mix cursor and offset pagination parameters');
    }
    
    if (hasCursorParams) {
      return PaginationStrategy.CURSOR;
    }
    
    // Default zu Offset-Pagination
    return PaginationStrategy.OFFSET;
  }

  /**
   * Vollständige Parameter-Parsing aus Request
   */
  parseRequestParams(query: any): PaginationParams {
    const strategy = this.detectPaginationStrategy(query);
    
    return {
      strategy,
      cursor: strategy === PaginationStrategy.CURSOR ? this.parseCursorParams(query) : undefined,
      offset: strategy === PaginationStrategy.OFFSET ? this.parseOffsetParams(query) : undefined
    };
  }

  /**
   * Performance-Empfehlungen für große Datasets
   */
  getPerformanceRecommendations(totalCount: number, currentLimit: number): string[] {
    const recommendations: string[] = [];
    
    if (totalCount > 10000 && currentLimit > 100) {
      recommendations.push('Verwenden Sie kleinere Seitengrößen für bessere Performance bei großen Datasets');
    }
    
    if (totalCount > 100000) {
      recommendations.push('Erwägen Sie Cursor-Pagination für sehr große Datasets');
      recommendations.push('Implementieren Sie Filteroptionen um die Datenmenge zu reduzieren');
    }
    
    if (currentLimit > 500) {
      recommendations.push('Limit über 500 kann zu Performance-Problemen führen');
    }
    
    return recommendations;
  }
}

/**
 * Pagination-Helper für Repository-Integration
 */
export class RepositoryPaginationHelper {
  private paginationService: PaginationService;

  constructor() {
    this.paginationService = PaginationService.getInstance();
  }

  /**
   * Erweitere Repository findAll-Methode mit Pagination
   */
  async paginatedFindAll<T extends Record<string, any>>(
    prismaModel: any,
    baseWhere: any = {},
    paginationParams: PaginationParams,
    defaultOrderBy: string = 'id'
  ): Promise<CursorPaginatedResult<T> | OffsetPaginatedResult<T>> {
    const query = {
      where: baseWhere,
      findMany: (config: any) => prismaModel.findMany(config),
      findFirst: (config: any) => prismaModel.findFirst(config),
      count: (config: any) => prismaModel.count(config)
    };

    return this.paginationService.applyPagination(query, paginationParams, defaultOrderBy);
  }

  /**
   * Cache-Key für paginierte Queries
   */
  generatePaginationCacheKey(
    baseKey: string,
    paginationParams: PaginationParams
  ): string {
    const strategy = paginationParams.strategy;
    let paramString = '';

    if (strategy === PaginationStrategy.CURSOR && paginationParams.cursor) {
      const { first, after, last, before, orderBy, orderDirection } = paginationParams.cursor;
      paramString = `cursor:${first || ''}:${after || ''}:${last || ''}:${before || ''}:${orderBy || ''}:${orderDirection || ''}`;
    } else if (strategy === PaginationStrategy.OFFSET && paginationParams.offset) {
      const { page, limit, offset, orderBy, orderDirection } = paginationParams.offset;
      paramString = `offset:${page || ''}:${limit || ''}:${offset || ''}:${orderBy || ''}:${orderDirection || ''}`;
    }

    return `${baseKey}:paginated:${paramString}`;
  }
}

// Singleton instances
export const paginationService = PaginationService.getInstance();
export const repositoryPaginationHelper = new RepositoryPaginationHelper();