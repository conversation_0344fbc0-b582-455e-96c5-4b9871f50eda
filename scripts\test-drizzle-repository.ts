#!/usr/bin/env tsx

/**
 * Direkter Test für das Drizzle Stoerungen Repository
 * 
 * Dieser Test validiert die Migration von Prisma zu Drizzle
 */

import { StoerungenRepository } from '../src/repositories/stoerungen.repository';

async function testDrizzleRepository() {
  console.log('🧪 Testing Drizzle Stoerungen Repository...\n');

  try {
    const repository = StoerungenRepository.getInstance();

    // Test 1: Störungen-Statistiken abrufen
    console.log('📊 Testing getStoerungsStats()...');
    const stats = await repository.getStoerungsStats();
    console.log(`   ✅ Statistics retrieved successfully:`);
    console.log(`      - Total: ${stats.total}`);
    console.log(`      - Active: ${stats.active}`);
    console.log(`      - Resolved: ${stats.resolved}`);
    console.log(`      - Recent: ${stats.recent}`);
    console.log(`      - Avg MTTR: ${stats.avgMttr} minutes`);
    console.log(`      - By Severity:`, JSON.stringify(stats.bySeverity, null, 8));

    // Test 2: Störungen auflisten (begrenzt)
    console.log('\n📋 Testing getStoerungen() with filters...');
    const stoerungen = await repository.getStoerungen({
      limit: 5,
      offset: 0,
    });
    console.log(`   ✅ Retrieved ${stoerungen.length} störungen`);
    if (stoerungen.length > 0) {
      const firstStoerung = stoerungen[0];
      console.log(`      - First störung: "${firstStoerung.title}" (ID: ${firstStoerung.id})`);
      console.log(`      - Status: ${firstStoerung.status}, Severity: ${firstStoerung.severity}`);
      console.log(`      - Tags: ${JSON.stringify(firstStoerung.tags)}`);
      console.log(`      - Comments: ${firstStoerung.comments?.length || 0}`);

      // Test 3: Spezifische Störung abrufen
      console.log('\n🔍 Testing getStoerungById()...');
      const specificStoerung = await repository.getStoerungById(firstStoerung.id);
      if (specificStoerung) {
        console.log(`   ✅ Retrieved specific störung: "${specificStoerung.title}"`);
        console.log(`      - Description: ${specificStoerung.description || 'N/A'}`);
        console.log(`      - Created: ${specificStoerung.created_at}`);
        console.log(`      - Updated: ${specificStoerung.updated_at}`);
      } else {
        console.log('   ❌ Failed to retrieve specific störung');
      }
    }

    // Test 4: Erweiterte Filterung
    console.log('\n🔍 Testing getStoerungen() with specific filters...');
    const filteredStoerungen = await repository.getStoerungen({
      status: 'RESOLVED',
      limit: 3,
    });
    console.log(`   ✅ Retrieved ${filteredStoerungen.length} resolved störungen`);

    // Test 5: Cache-Test
    console.log('\n⚡ Testing caching performance...');
    const startTime = Date.now();
    await repository.getStoerungsStats();
    const firstCallTime = Date.now() - startTime;

    const startTime2 = Date.now();
    await repository.getStoerungsStats();
    const secondCallTime = Date.now() - startTime2;

    console.log(`   ✅ First call: ${firstCallTime}ms, Second call: ${secondCallTime}ms`);
    console.log(`      Cache effectiveness: ${secondCallTime < firstCallTime ? 'Working' : 'Not noticeable'}`);

    console.log('\n🎉 All Drizzle Repository tests passed successfully!');

    // Vergleiche mit direkter DB-Abfrage
    console.log('\n🔧 Performing integrity check...');
    const { db } = await import('../src/db');
    const { stoerungen: stoerungenTable } = await import('../src/db/schema');
    const { count } = await import('drizzle-orm');

    const directCount = await db.select({ count: count() }).from(stoerungenTable);
    console.log(`   ✅ Direct DB count: ${directCount[0].count}`);
    console.log(`   ✅ Repository stats count: ${stats.total}`);
    
    if (directCount[0].count === stats.total) {
      console.log('   🎯 Data integrity check: PASSED');
    } else {
      console.log('   ⚠️ Data integrity check: MISMATCH (cache issue?)');
    }

  } catch (error) {
    console.error('❌ Drizzle Repository test failed:', error);
    process.exit(1);
  }
}

// Run the test
testDrizzleRepository();
