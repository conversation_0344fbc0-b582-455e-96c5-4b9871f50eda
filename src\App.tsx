import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { RouterProvider } from "@tanstack/react-router";
import { updateAppLanguage } from "@/helpers/language_helpers";
import { router } from "@/routes/router";
import Toaster from "@/components/ui/toaster";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/components/ui/toast";
import { AskJaszProvider } from "@/contexts/AskJaszContext";
import { useChatBot } from "@/hooks/useChatBot";
import ChatBot from "@/modules/ai/components/chat/ChatBot";
import "./localization/i18n";

/**
 * Hauptkomponente der Anwendung
 * 
 * Verantwortlich für:
 * - Initialisierung der Sprache
 * - Bereitstellung des Routers
 */
function App() {
  const { i18n } = useTranslation();
  const { isChatOpen, chatMessage, openChatWithMessage, setChatOpen } = useChatBot();

  useEffect(() => {
    console.log("🚀 App-Initialisierung gestartet");
    
    // Initialisiere Sprache beim Start
    updateAppLanguage(i18n);
    
    console.log("✅ App vollständig initialisiert und funktional!");
  }, [i18n]);

  const handleOpenChat = (prompt: string) => {
    openChatWithMessage(prompt);
  };

  return (
    <React.StrictMode>
      <AuthProvider>
        <ToastProvider>
          <AskJaszProvider onOpenChat={handleOpenChat}>
            <RouterProvider router={router} />
            <Toaster />
            <ChatBot 
              isOpen={isChatOpen}
              onOpenChange={setChatOpen}
              initialMessage={chatMessage}
              autoOpen={true}
            />
          </AskJaszProvider>
        </ToastProvider>
      </AuthProvider>
    </React.StrictMode>
  );
}

export default App;
