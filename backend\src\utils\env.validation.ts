/**
 * Umgebungsvariablen-Validierung
 * 
 * <PERSON><PERSON>t sicher, dass alle erforderlichen Umgebungsvariablen vorhanden 
 * und gültig sind, bevor der Server gestartet wird.
 */

import dotenv from 'dotenv';

// Lade Umgebungsvariablen
dotenv.config();

/**
 * Interface für validierte Umgebungsvariablen
 */
export interface ValidatedEnv {
  OPENROUTER_API_KEY: string;
  OPENROUTER_PRESET_MODEL: string;
  API_SECRET_KEY: string;
  NODE_ENV: 'development' | 'production' | 'test';
  API_PORT: number;
}

/**
 * Erforderliche Umgebungsvariablen mit Validierungsregeln
 */
const ENV_REQUIREMENTS = {
  OPENROUTER_API_KEY: {
    required: true,
    validate: (value: string) => value.startsWith('sk-or-v1-') && value.length > 20,
    error: 'OPENROUTER_API_KEY muss mit "sk-or-v1-" beginnen und mindestens 20 Zeichen lang sein'
  },
  OPENROUTER_PRESET_MODEL: {
    required: true,
    validate: (value: string) => value.startsWith('@preset/'),
    error: 'OPENROUTER_PRESET_MODEL muss mit "@preset/" beginnen'
  },
  API_SECRET_KEY: {
    required: true,
    validate: (value: string) => value.length >= 32,
    error: 'API_SECRET_KEY muss mindestens 32 Zeichen lang sein'
  },
  NODE_ENV: {
    required: false,
    default: 'development',
    validate: (value: string) => ['development', 'production', 'test'].includes(value),
    error: 'NODE_ENV muss "development", "production" oder "test" sein'
  },
  API_PORT: {
    required: false,
    default: '3001',
    validate: (value: string) => {
      const port = parseInt(value, 10);
      return !isNaN(port) && port > 0 && port < 65536;
    },
    error: 'API_PORT muss eine gültige Port-Nummer zwischen 1 und 65535 sein'
  }
} as const;

/**
 * Validiert alle Umgebungsvariablen
 * @returns Validierte Umgebungsvariablen
 * @throws Error bei ungültigen oder fehlenden Variablen
 */
export function validateEnvironment(): ValidatedEnv {
  // Skip validation in portable builds
  if (process.env.SKIP_ENV_VALIDATION === 'true') {
    console.log('⚠️ Umgebungsvariablen-Validierung übersprungen (Portable Build)');
    return {
      OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || 'sk-or-v1-fallback',
      OPENROUTER_PRESET_MODEL: process.env.OPENROUTER_PRESET_MODEL || '@preset/lapp',
      API_SECRET_KEY: process.env.API_SECRET_KEY || 'fallback-secret-************************************',
      NODE_ENV: (process.env.NODE_ENV as any) || 'production',
      API_PORT: parseInt(process.env.API_PORT || '3001', 10)
    };
  }

  const errors: string[] = [];
  const validated: Partial<ValidatedEnv> = {};

  // Überprüfe jede erforderliche Umgebungsvariable
  for (const [key, config] of Object.entries(ENV_REQUIREMENTS)) {
    let value = process.env[key];

    // Verwende Standardwert falls vorhanden und nicht gesetzt
    if (!value && 'default' in config) {
      value = config.default;
    }

    // Überprüfe ob erforderlich
    if (config.required && !value) {
      errors.push(`Umgebungsvariable ${key} ist erforderlich aber nicht gesetzt`);
      continue;
    }

    // Validiere Wert falls vorhanden
    if (value && !config.validate(value)) {
      errors.push(`${key}: ${config.error}`);
      continue;
    }

    // Spezielle Behandlung für PORT (String zu Number)
    if (key === 'API_PORT' && value) {
      (validated as any)[key] = parseInt(value, 10);
    } else if (value) {
      (validated as any)[key] = value;
    }
  }

  // Fehler werfen falls Validierung fehlgeschlagen
  if (errors.length > 0) {
    console.error('❌ Umgebungsvariablen-Validierung fehlgeschlagen:');
    errors.forEach(error => console.error(`  - ${error}`));
    console.error('\n💡 Tipp: Überprüfen Sie Ihre .env-Datei oder verwenden Sie .env.example als Vorlage');
    throw new Error('Ungültige Umgebungskonfiguration');
  }

  console.log('✅ Alle Umgebungsvariablen erfolgreich validiert');
  return validated as ValidatedEnv;
}

/**
 * Hilfsfunktion zur sicheren Anzeige von Umgebungsvariablen (ohne Geheimnisse)
 * @param env Validierte Umgebungsvariablen
 */
export function logEnvironmentInfo(env: ValidatedEnv): void {
  console.log('🔧 Umgebungskonfiguration:');
  console.log(`  - NODE_ENV: ${env.NODE_ENV}`);
  console.log(`  - API_PORT: ${env.API_PORT}`);
  console.log(`  - OPENROUTER_PRESET_MODEL: ${env.OPENROUTER_PRESET_MODEL}`);
  console.log(`  - OPENROUTER_API_KEY: ${env.OPENROUTER_API_KEY.substring(0, 12)}...`);
  console.log(`  - API_SECRET_KEY: ${env.API_SECRET_KEY.substring(0, 8)}...`);
}

/**
 * Generiert einen neuen sicheren API-Schlüssel
 * @returns Neuer API-Schlüssel
 */
export function generateApiKey(): string {
  const crypto = require('crypto');
  return 'sfm_api_' + crypto.randomBytes(32).toString('hex');
}