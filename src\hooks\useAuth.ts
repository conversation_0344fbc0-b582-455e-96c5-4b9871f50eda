import { useState, useEffect, useCallback } from 'react';
import { authService } from '@/services/auth.service';

export interface User {
  id: number;
  username: string;
  email: string;
  roles: string[];
  name?: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

interface AuthHook extends AuthState {
  login: (username: string, password: string) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  trackActivity: () => void;
}

export const useAuth = (): AuthHook => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const logout = useCallback(() => {
    console.log('🚪 Logging out user');
    authService.logout();
    setUser(null);
    setToken(null);
  }, []);

  const login = async (username: string, password: string): Promise<{ success: boolean; message: string }> => {
    try {
      setIsLoading(true);
      const response = await authService.login({ username, password });

      if (response.success && response.data) {
        setUser(response.data.user);
        setToken(response.data.token);
        return { success: true, message: response.message };
      } else {
        return { success: false, message: response.message };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: 'Ein Fehler ist aufgetreten' };
    } finally {
      setIsLoading(false);
    }
  };

  const trackActivity = useCallback(() => {
    // Simplified activity tracking - just a placeholder for now
    console.log('Activity tracked');
  }, []);

  // Initialize auth state - very simple version
  useEffect(() => {
    const initializeAuth = () => {
      try {
        console.log('🔄 Initializing auth state...');
        const storedToken = authService.getToken();
        const storedUser = authService.getUser();

        if (storedToken && storedUser) {
          console.log('✅ Found stored auth data');
          setToken(storedToken);
          setUser(storedUser);
        } else {
          console.log('ℹ️ No stored auth data found');
        }
      } catch (error) {
        console.error('❌ Auth initialization error:', error);
      } finally {
        setIsLoading(false);
        console.log('✅ Auth initialization complete');
      }
    };

    initializeAuth();
  }, []);

  return {
    user,
    token,
    isAuthenticated: !!(user && token),
    isLoading,
    login,
    logout,
    trackActivity
  };
};