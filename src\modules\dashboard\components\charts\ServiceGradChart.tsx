"use client"

import React, { useEffect, useState, memo } from "react"
import { TrendingUp } from "lucide-react"
import { Area, AreaChart, CartesianGrid, XAxis, YAxis, Bar } from "recharts"
import { DateRange } from "react-day-picker"
import apiService from "@/services/api.service"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent
} from "@/components/ui/chart"

// Interface für die ServiceLevel-Daten aus der Datenbank
interface ServiceLevelDbRow {
  datum: string;
  servicegrad: number;
}

// Props für die AreaChartStacked Komponente
interface AreaChartStackedProps {
  dateRange?: DateRange;
}

// Definition des Datentyps für Chart-Daten
interface ChartDataPoint {
  date: string;
  servicegrad: number;
}

const chartConfig = {
  servicegrad: {
    label: "Servicegrad",
    color: "#f54a00",
  },
} satisfies ChartConfig

const AreaChartStacked = memo(function AreaChartStacked({ dateRange }: AreaChartStackedProps) {
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Lade Daten aus der Datenbank
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const result = await apiService.getServiceLevelData();
        
        if (result && Array.isArray(result)) {
          
          // Konvertiere Datenbankdaten zu Chart-Format
          let processedData = result.map((row) => {
            // Verwende servicegrad oder csr je nachdem, was in den Daten vorhanden ist
            let servicegradValue = Number((row as any).servicegrad || (row as any).csr) || 0;
            
            // Wenn der Wert zwischen 0 und 1 liegt, handelt es sich um einen Dezimalwert (z.B. 0.95 = 95%)
            if (servicegradValue > 0 && servicegradValue <= 1) {
              servicegradValue = servicegradValue * 100;
            }
            
            return {
              date: row.datum,
              servicegrad: servicegradValue
            };
          });
          
          // Filtere Daten basierend auf dem ausgewählten Datumsbereich
          if (dateRange?.from || dateRange?.to) {
            processedData = processedData.filter((item) => {
              const itemDate = new Date(item.date);
              
              // Prüfe ob das Datum im ausgewählten Bereich liegt
              if (dateRange.from && itemDate < dateRange.from) return false;
              if (dateRange.to && itemDate > dateRange.to) return false;
              
              return true;
            });
          }
          
          setChartData(processedData);
        } else {
          throw new Error('Keine gültigen Daten erhalten');
        }
      } catch (err) {
        setError('Fehler beim Laden der Daten aus der Datenbank.');
        setChartData([]);
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [dateRange]); // Abhängigkeit hinzugefügt für dateRange

  // Zeige Ladezustand an
  if (loading) {
    return (
      <Card className="text-black">
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Zeige Fehler an - nur wenn kein Fallback vorhanden ist
  if (error && chartData.length === 0) {
    return (
      <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <p className="font-bold text-red-500">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Berechne Statistiken
  const avgServicegrad = chartData.length > 0 
    ? chartData.reduce((sum, item) => sum + item.servicegrad, 0) / chartData.length 
    : 0;

  return (
    <Card className="text-black  w-full h-105 border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
      <CardHeader>
        <CardTitle>SERVICEGRAD</CardTitle>
        <CardDescription>
          Verlauf des Servicegrads
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="w-full h-60">
          <AreaChart
            accessibilityLayer
            data={chartData}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} stroke="#000" strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              height={40}
              tickFormatter={(value) => {
                try {
                  const date = new Date(value);
                  return date.toLocaleDateString('de-DE', { day: '2-digit', month: '2-digit' });
                } catch {
                  return value;
                }
              }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tick={{ fontSize: 12, fontWeight: 600, fill: '#000' }}
              domain={[80, 100]}
              tickFormatter={(value) => `${value}%`}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="dot" 
                formatter={(value) => [`${Number(value).toFixed(1)}%`, "Servicegrad"]}
                labelFormatter={(label) => `Datum: ${label}`}
              />}
            />
            <Area
              dataKey="servicegrad"
              type="monotone"
              fill="var(--chart-1)"
              stroke="var(--chart-1)"
              strokeWidth={3}
              fillOpacity={0.3}
              activeDot={{
                fill: "var(--chart-1)",
                stroke: "#000",
                strokeWidth: 2,
                r: 6,
              }}
            />
            <ChartLegend content={({ payload }) => (
              <ChartLegendContent 
                payload={payload} 
                className="p-2 rounded-md"
              />
            )} />
            <Bar 
              dataKey="Servicegrad" 
              name="Servicegrad" 
              fill="var(--color-servicegrad)"
              radius={4} 
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Durchschnitt: {avgServicegrad.toFixed(1)}% <TrendingUp className="h-4 w-4" />
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              {error ? (
                <span className="text-red-500">Datenbank nicht verfügbar</span>
              ) : (
                <span>Letzte Aktualisierung: {new Date().toLocaleDateString()}</span>
              )}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
});

export default AreaChartStacked;
