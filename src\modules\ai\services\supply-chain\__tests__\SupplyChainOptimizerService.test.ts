/**
 * Supply Chain Optimizer Service Tests
 * 
 * Unit tests for the supply chain optimization service
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SupplyChainOptimizerService } from '../SupplyChainOptimizerService';
import { SupplierRepository } from '@/repositories/supplier.repository';
import { DeliveryRepository } from '@/repositories/delivery.repository';
import { AIServiceError } from '../../types';
import type {
  DeliveryPredictionRequest,
  SupplierEvaluationRequest,
  LogisticsOptimizationRequest,
  DisruptionAnalysisRequest
} from '@/types/supply-chain-optimization';

// Mock repositories
vi.mock('@/repositories/supplier.repository');
vi.mock('@/repositories/delivery.repository');

describe('SupplyChainOptimizerService', () => {
  let service: SupplyChainOptimizerService;
  let mockSupplierRepository: vi.Mocked<SupplierRepository>;
  let mockDeliveryRepository: vi.Mocked<DeliveryRepository>;

  beforeEach(async () => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Create service instance
    service = new SupplyChainOptimizerService({
      predictionHorizon: 30,
      riskAssessmentDepth: 'detailed',
      enableRealTimeTracking: true
    });

    // Setup mock repositories
    mockSupplierRepository = vi.mocked(new SupplierRepository());
    mockDeliveryRepository = vi.mocked(new DeliveryRepository());

    // Mock repository methods
    mockSupplierRepository.getSupplierStats.mockResolvedValue({
      totalSuppliers: 10,
      activeSuppliers: 8,
      averagePerformanceScore: 7.5,
      topPerformers: 3,
      riskSuppliers: 1
    });

    mockDeliveryRepository.getDeliveryStats.mockResolvedValue({
      totalDeliveries: 100,
      onTimeDeliveries: 85,
      averageDeliveryTime: 6.5,
      totalDistance: 10000,
      totalCost: 25000,
      activeRoutes: 5
    });

    // Initialize service
    await service.initialize();
  });

  afterEach(async () => {
    await service.destroy();
  });

  describe('Service Initialization', () => {
    it('should initialize successfully', async () => {
      const newService = new SupplyChainOptimizerService();
      await expect(newService.initialize()).resolves.not.toThrow();
      await newService.destroy();
    });

    it('should handle initialization failure', async () => {
      mockSupplierRepository.getSupplierStats.mockRejectedValue(new Error('Database error'));
      
      const newService = new SupplyChainOptimizerService();
      await expect(newService.initialize()).rejects.toThrow('Supply chain optimizer initialization failed');
      await newService.destroy();
    });

    it('should validate configuration', async () => {
      const config = {
        predictionHorizon: -5, // Invalid
        maxOptimizationRoutes: 0 // Invalid
      };

      const newService = new SupplyChainOptimizerService(config);
      // Service should handle invalid config gracefully
      await expect(newService.initialize()).resolves.not.toThrow();
      await newService.destroy();
    });
  });

  describe('Delivery Time Prediction', () => {
    it('should predict delivery times successfully', async () => {
      const request: DeliveryPredictionRequest = {
        supplierId: 'SUP_001',
        productType: 'Electronics',
        quantity: 100,
        urgency: 'medium'
      };

      // Mock delivery history
      mockDeliveryRepository.getSupplierDeliveryHistory.mockResolvedValue([
        {
          deliveryId: 'DEL_001',
          supplierId: 'SUP_001',
          orderDate: new Date('2024-01-01'),
          deliveryDate: new Date('2024-01-08'),
          promisedTime: 7,
          actualTime: 7,
          wasLate: false,
          productType: 'Electronics',
          urgency: 'medium',
          quantity: 100,
          value: 1000
        }
      ]);

      const prediction = await service.predictDeliveryTimes(request);

      expect(prediction).toBeDefined();
      expect(prediction.supplierId).toBe('SUP_001');
      expect(prediction.predictedDeliveryTime).toBeGreaterThan(0);
      expect(prediction.confidenceLevel).toBeGreaterThan(0);
      expect(prediction.confidenceLevel).toBeLessThanOrEqual(100);
      expect(prediction.confidenceIntervals).toBeDefined();
      expect(prediction.delayRisks).toBeInstanceOf(Array);
      expect(prediction.factors).toBeInstanceOf(Array);
      expect(prediction.lastUpdated).toBeInstanceOf(Date);
    });

    it('should handle prediction with no historical data', async () => {
      const request: DeliveryPredictionRequest = {
        supplierId: 'SUP_NEW',
        productType: 'Components'
      };

      mockDeliveryRepository.getSupplierDeliveryHistory.mockResolvedValue([]);

      const prediction = await service.predictDeliveryTimes(request);

      expect(prediction).toBeDefined();
      expect(prediction.predictedDeliveryTime).toBeGreaterThan(0);
      expect(prediction.confidenceLevel).toBeLessThan(60); // Lower confidence with no data
    });

    it('should apply urgency factors correctly', async () => {
      const baseRequest: DeliveryPredictionRequest = {
        supplierId: 'SUP_001',
        productType: 'Electronics',
        urgency: 'medium'
      };

      const urgentRequest: DeliveryPredictionRequest = {
        ...baseRequest,
        urgency: 'high'
      };

      mockDeliveryRepository.getSupplierDeliveryHistory.mockResolvedValue([
        {
          deliveryId: 'DEL_001',
          supplierId: 'SUP_001',
          orderDate: new Date('2024-01-01'),
          deliveryDate: new Date('2024-01-08'),
          promisedTime: 7,
          actualTime: 7,
          wasLate: false,
          productType: 'Electronics',
          urgency: 'medium',
          quantity: 100,
          value: 1000
        }
      ]);

      const basePrediction = await service.predictDeliveryTimes(baseRequest);
      const urgentPrediction = await service.predictDeliveryTimes(urgentRequest);

      // Urgent deliveries should be faster
      expect(urgentPrediction.predictedDeliveryTime).toBeLessThan(basePrediction.predictedDeliveryTime);
    });

    it('should handle prediction errors gracefully', async () => {
      const request: DeliveryPredictionRequest = {
        supplierId: 'SUP_ERROR'
      };

      mockDeliveryRepository.getSupplierDeliveryHistory.mockRejectedValue(new Error('Database error'));

      const prediction = await service.predictDeliveryTimes(request);

      // Should return fallback prediction
      expect(prediction).toBeDefined();
      expect(prediction.predictedDeliveryTime).toBe(7); // Default fallback
      expect(prediction.confidenceLevel).toBe(50); // Low confidence
    });
  });

  describe('Supplier Risk Assessment', () => {
    it('should assess supplier risk successfully', async () => {
      const request: SupplierEvaluationRequest = {
        supplierId: 'SUP_001',
        productCategories: ['Electronics']
      };

      mockSupplierRepository.getSupplierPerformance.mockResolvedValue({
        supplierId: 'SUP_001',
        onTimeDeliveryRate: 0.85,
        qualityRate: 0.95,
        responseTime: 12,
        financialStabilityScore: 8,
        reliabilityScore: 9,
        costCompetitiveness: 7,
        totalOrders: 50,
        totalValue: 100000,
        lastOrderDate: new Date(),
        performanceTrend: 'stable'
      });

      const assessment = await service.assessSupplierRisk(request);

      expect(assessment).toBeDefined();
      expect(assessment.supplierId).toBe('SUP_001');
      expect(assessment.overallRiskScore).toBeGreaterThan(0);
      expect(assessment.overallRiskScore).toBeLessThanOrEqual(10);
      expect(['low', 'medium', 'high', 'critical']).toContain(assessment.riskCategory);
      expect(assessment.riskFactors).toBeInstanceOf(Array);
      expect(assessment.recommendations).toBeInstanceOf(Array);
      expect(assessment.alternativeSuppliers).toBeInstanceOf(Array);
      expect(assessment.assessmentDate).toBeInstanceOf(Date);
      expect(assessment.validUntil).toBeInstanceOf(Date);
    });

    it('should identify high-risk suppliers', async () => {
      const request: SupplierEvaluationRequest = {
        supplierId: 'SUP_RISK'
      };

      mockSupplierRepository.getSupplierPerformance.mockResolvedValue({
        supplierId: 'SUP_RISK',
        onTimeDeliveryRate: 0.60, // Poor performance
        qualityRate: 0.85, // Below standard
        responseTime: 48, // Slow response
        financialStabilityScore: 4, // Poor financial stability
        reliabilityScore: 5,
        costCompetitiveness: 6,
        totalOrders: 20,
        totalValue: 25000,
        lastOrderDate: new Date(),
        performanceTrend: 'declining'
      });

      const assessment = await service.assessSupplierRisk(request);

      expect(assessment.riskCategory).toBe('high');
      expect(assessment.overallRiskScore).toBeGreaterThan(6);
      expect(assessment.riskFactors.length).toBeGreaterThan(0);
      expect(assessment.recommendations.length).toBeGreaterThan(0);
    });

    it('should cache risk assessments', async () => {
      const request: SupplierEvaluationRequest = {
        supplierId: 'SUP_CACHE',
        assessmentDate: new Date()
      };

      mockSupplierRepository.getSupplierPerformance.mockResolvedValue({
        supplierId: 'SUP_CACHE',
        onTimeDeliveryRate: 0.90,
        qualityRate: 0.98,
        responseTime: 8,
        financialStabilityScore: 9,
        reliabilityScore: 9,
        costCompetitiveness: 8,
        totalOrders: 75,
        totalValue: 150000,
        lastOrderDate: new Date(),
        performanceTrend: 'improving'
      });

      // First call
      const assessment1 = await service.assessSupplierRisk(request);
      
      // Second call with same parameters should use cache
      const assessment2 = await service.assessSupplierRisk(request);

      expect(assessment1).toEqual(assessment2);
      expect(mockSupplierRepository.getSupplierPerformance).toHaveBeenCalledTimes(1);
    });

    it('should handle risk assessment errors gracefully', async () => {
      const request: SupplierEvaluationRequest = {
        supplierId: 'SUP_ERROR'
      };

      mockSupplierRepository.getSupplierPerformance.mockRejectedValue(new Error('Database error'));

      const assessment = await service.assessSupplierRisk(request);

      // Should return fallback assessment
      expect(assessment).toBeDefined();
      expect(assessment.riskCategory).toBe('medium');
      expect(assessment.overallRiskScore).toBe(5);
      expect(assessment.riskFactors.length).toBeGreaterThan(0);
      expect(assessment.riskFactors[0].type).toBe('insufficient_data');
    });
  });

  describe('Logistics Optimization', () => {
    it('should optimize logistics successfully', async () => {
      const request: LogisticsOptimizationRequest = {
        requestId: 'REQ_001',
        deliveries: [
          {
            deliveryId: 'DEL_001',
            destination: 'Location A',
            items: [{
              itemId: 'ITEM_001',
              quantity: 10,
              weight: 5,
              dimensions: { length: 10, width: 5, height: 2, volume: 100 }
            }],
            priority: 'medium'
          }
        ],
        constraints: {
          maxVehicleCapacity: 1000,
          maxRouteDistance: 500,
          maxRouteTime: 480,
          availableVehicles: 3,
          driverWorkingHours: 8,
          fuelCostPerKm: 0.15,
          laborCostPerHour: 25
        },
        optimizationGoals: [
          { type: 'minimize_cost', weight: 0.6 },
          { type: 'minimize_time', weight: 0.4 }
        ]
      };

      const optimization = await service.optimizeLogistics(request);

      expect(optimization).toBeDefined();
      expect(optimization.requestId).toBe('REQ_001');
      expect(optimization.currentRoutes).toBeInstanceOf(Array);
      expect(optimization.optimizedRoutes).toBeInstanceOf(Array);
      expect(optimization.benefits).toBeDefined();
      expect(optimization.contingencyPlans).toBeInstanceOf(Array);
      expect(optimization.implementationSteps).toBeInstanceOf(Array);
      expect(optimization.optimizationDate).toBeInstanceOf(Date);
    });

    it('should calculate optimization benefits', async () => {
      const request: LogisticsOptimizationRequest = {
        requestId: 'REQ_BENEFITS',
        deliveries: [
          {
            deliveryId: 'DEL_001',
            destination: 'Location A',
            items: [{
              itemId: 'ITEM_001',
              quantity: 10,
              weight: 5,
              dimensions: { length: 10, width: 5, height: 2, volume: 100 }
            }],
            priority: 'high'
          },
          {
            deliveryId: 'DEL_002',
            destination: 'Location B',
            items: [{
              itemId: 'ITEM_002',
              quantity: 5,
              weight: 3,
              dimensions: { length: 8, width: 4, height: 2, volume: 64 }
            }],
            priority: 'medium'
          }
        ],
        constraints: {
          maxVehicleCapacity: 1000,
          maxRouteDistance: 500,
          maxRouteTime: 480,
          availableVehicles: 2,
          driverWorkingHours: 8,
          fuelCostPerKm: 0.15,
          laborCostPerHour: 25
        },
        optimizationGoals: [
          { type: 'minimize_cost', weight: 1.0 }
        ]
      };

      const optimization = await service.optimizeLogistics(request);

      expect(optimization.benefits.distanceReduction).toBeGreaterThanOrEqual(0);
      expect(optimization.benefits.timeReduction).toBeGreaterThanOrEqual(0);
      expect(optimization.benefits.costSavings).toBeGreaterThanOrEqual(0);
    });

    it('should handle logistics optimization errors gracefully', async () => {
      const request: LogisticsOptimizationRequest = {
        requestId: 'REQ_ERROR',
        deliveries: [],
        constraints: {
          maxVehicleCapacity: 1000,
          maxRouteDistance: 500,
          maxRouteTime: 480,
          availableVehicles: 1,
          driverWorkingHours: 8,
          fuelCostPerKm: 0.15,
          laborCostPerHour: 25
        },
        optimizationGoals: []
      };

      const optimization = await service.optimizeLogistics(request);

      // Should return fallback optimization
      expect(optimization).toBeDefined();
      expect(optimization.benefits.distanceReduction).toBe(0);
      expect(optimization.benefits.timeReduction).toBe(0);
      expect(optimization.benefits.costSavings).toBe(0);
    });
  });

  describe('Supply Chain Disruption Analysis', () => {
    it('should analyze disruptions successfully', async () => {
      const request: DisruptionAnalysisRequest = {
        disruptionId: 'DISR_001',
        disruptionType: 'weather',
        affectedRegion: 'Northern Germany',
        severity: 'medium',
        estimatedDuration: 3,
        description: 'Heavy snowfall affecting transportation'
      };

      const disruption = await service.analyzeSupplyChainDisruption(request);

      expect(disruption).toBeDefined();
      expect(disruption.disruptionId).toBe('DISR_001');
      expect(disruption.disruptionType).toBe('weather');
      expect(['low', 'medium', 'high', 'critical']).toContain(disruption.severity);
      expect(disruption.impactAssessment).toBeDefined();
      expect(disruption.affectedSuppliers).toBeInstanceOf(Array);
      expect(disruption.affectedRoutes).toBeInstanceOf(Array);
      expect(disruption.mitigationStrategies).toBeInstanceOf(Array);
      expect(disruption.recoveryTimeline).toBeDefined();
      expect(disruption.financialImpact).toBeDefined();
      expect(disruption.analysisDate).toBeInstanceOf(Date);
    });

    it('should categorize disruption severity correctly', async () => {
      const criticalRequest: DisruptionAnalysisRequest = {
        disruptionId: 'DISR_CRITICAL',
        disruptionType: 'natural_disaster',
        severity: 'critical',
        estimatedDuration: 14
      };

      const disruption = await service.analyzeSupplyChainDisruption(criticalRequest);

      expect(disruption.severity).toBe('critical');
      expect(disruption.mitigationStrategies.length).toBeGreaterThan(0);
      expect(disruption.recoveryTimeline.phases.length).toBeGreaterThan(0);
    });

    it('should generate appropriate mitigation strategies', async () => {
      const request: DisruptionAnalysisRequest = {
        disruptionId: 'DISR_MITIGATION',
        disruptionType: 'supplier_failure',
        severity: 'high'
      };

      const disruption = await service.analyzeSupplyChainDisruption(request);

      expect(disruption.mitigationStrategies.length).toBeGreaterThan(0);
      
      const strategy = disruption.mitigationStrategies[0];
      expect(strategy.strategyId).toBeDefined();
      expect(strategy.description).toBeDefined();
      expect(['low', 'medium', 'high']).toContain(strategy.priority);
      expect(strategy.estimatedEffectiveness).toBeGreaterThan(0);
      expect(strategy.estimatedEffectiveness).toBeLessThanOrEqual(1);
      expect(strategy.implementationTime).toBeGreaterThan(0);
      expect(strategy.cost).toBeGreaterThanOrEqual(0);
    });

    it('should handle disruption analysis errors gracefully', async () => {
      const request: DisruptionAnalysisRequest = {
        disruptionId: 'DISR_ERROR',
        disruptionType: 'pandemic'
      };

      const disruption = await service.analyzeSupplyChainDisruption(request);

      // Should return fallback analysis
      expect(disruption).toBeDefined();
      expect(disruption.severity).toBe('medium');
      expect(disruption.mitigationStrategies.length).toBeGreaterThan(0);
      expect(disruption.mitigationStrategies[0].description).toContain('Manuelle Bewertung');
    });
  });

  describe('Service Health and Management', () => {
    it('should provide health status', () => {
      const health = service.getHealthStatus();

      expect(health).toBeDefined();
      expect(health.status).toBeDefined();
      expect(health.details).toBeDefined();
      expect(health.details.configuration).toBeDefined();
    });

    it('should clear caches', () => {
      expect(() => service.clearCache()).not.toThrow();
    });

    it('should handle service destruction', async () => {
      await expect(service.destroy()).resolves.not.toThrow();
    });
  });

  describe('Configuration Validation', () => {
    it('should use default configuration values', () => {
      const defaultService = new SupplyChainOptimizerService();
      const health = defaultService.getHealthStatus();
      
      expect(health.details.configuration.predictionHorizon).toBe(30);
      expect(health.details.configuration.riskAssessmentDepth).toBe('detailed');
      expect(health.details.configuration.enableRealTimeTracking).toBe(true);
    });

    it('should override default configuration', () => {
      const customService = new SupplyChainOptimizerService({
        predictionHorizon: 60,
        riskAssessmentDepth: 'comprehensive',
        enableRealTimeTracking: false
      });
      
      const health = customService.getHealthStatus();
      
      expect(health.details.configuration.predictionHorizon).toBe(60);
      expect(health.details.configuration.riskAssessmentDepth).toBe('comprehensive');
      expect(health.details.configuration.enableRealTimeTracking).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle AI service errors with fallbacks', async () => {
      // Test with invalid supplier ID that would cause errors
      const request: DeliveryPredictionRequest = {
        supplierId: 'INVALID_SUPPLIER'
      };

      mockDeliveryRepository.getSupplierDeliveryHistory.mockRejectedValue(
        new Error('Supplier not found')
      );

      // Should not throw, but return fallback
      const prediction = await service.predictDeliveryTimes(request);
      expect(prediction).toBeDefined();
      expect(prediction.predictedDeliveryTime).toBe(7); // Fallback value
    });

    it('should log errors appropriately', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      const request: SupplierEvaluationRequest = {
        supplierId: 'ERROR_SUPPLIER'
      };

      mockSupplierRepository.getSupplierPerformance.mockRejectedValue(
        new Error('Database connection failed')
      );

      await service.assessSupplierRisk(request);

      // Verify error was logged (implementation detail may vary)
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });
});