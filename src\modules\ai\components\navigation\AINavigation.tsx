import React from 'react';
import { BaseNavigation } from '@/components/navigation/BaseNavigation';
import { aiNavigationConfig } from '@/config/navigation/ai.config';

interface AINavigationProps {
  title?: string;
}

/**
 * AI Module Navigation Component
 * 
 * Provides navigation for the AI module using the BaseNavigation component
 * with AI-specific configuration and role-based access control.
 */
export const AINavigation: React.FC<AINavigationProps> = ({ title = "" }) => {
  return (
    <BaseNavigation 
      title={title} 
      navigationConfig={aiNavigationConfig} 
    />
  );
};

export default AINavigation;