import { SAPWorkflowProcess, WorkflowExecution, WorkflowLog, WorkflowConfig, WorkflowStats } from '@/types/workflow';
import { apiService } from '@/services/api.service';

interface WorkflowExecutionResult {
  success?: boolean;
  exportPath?: string;
  error?: string;
  message?: string;
}

class WorkflowService {
  private processes: SAPWorkflowProcess[] = [
    {
      id: 'servicegrad',
      name: 'Servicegrad Automatisierung',
      description: 'SAP Servicegrad Export mit automatischer Excel-Verarbeitung, E-Mail-Versand und Datenbank-Speicherung',
      tcode: '/n/LSGIT/VS_DLV_CHECK',
      exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\SG',
      exportBasename: 'SG',
      dbTable: 'dispatch_data',
      status: 'idle'
    },
    {
      id: 'bestand',
      name: 'Bestand Workflow',
      description: 'Lagerspiegel Export aus verschiedenen Lägern mit Import in die Datenbank',
      tcode: '/nlx03',
      exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST',
      exportBasename: 'BST',
      dbTable: 'lagerspiegel',
      status: 'idle'
    },
    {
      id: 'rueckstandsliste',
      name: 'Rückstandsliste',
      description: 'SAP Rückstandsliste Datenexport',
      tcode: '/n/LSGIT/VS_DLV_CHECK',
      exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\RSL',
      exportBasename: 'RSL',
      dbTable: 'rsl_variant_data',
      status: 'idle'
    },
    {
      id: 'lx03_240',
      name: 'LX03 Lagertyp 240',
      description: 'SAP LX03 Lagertyp 240 Datenexport',
      tcode: '/nlx03',
      exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST',
      exportBasename: 'BST_240',
      dbTable: 'lx03_bin_status',
      status: 'idle'
    },
    {
      id: 'lx03_200',
      name: 'LX03 Lagertyp 200',
      description: 'SAP LX03 Lagertyp 200 Datenexport',
      tcode: '/nlx03',
      exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST',
      exportBasename: 'BST_200',
      dbTable: 'lx03_bin_status',
      status: 'idle'
    },
    {
      id: 'lx03_rest',
      name: 'LX03 Lagertyp Rest',
      description: 'SAP LX03 Lagertyp Rest (241-999) Datenexport',
      tcode: '/nlx03',
      exportDir: '\\\\adsgroup\\Group\\UIL-CL-Zentral\\10 Dashboard-App\\SourceData\\BST',
      exportBasename: 'BST_REST',
      dbTable: 'lx03_bin_status',
      status: 'idle'
    }
  ];

  private executions: WorkflowExecution[] = [];
  private logs: WorkflowLog[] = [];

  async getProcesses(): Promise<SAPWorkflowProcess[]> {
    return this.processes;
  }

  async getProcess(id: string): Promise<SAPWorkflowProcess | undefined> {
    return this.processes.find(p => p.id === id);
  }

  async executeProcess(processId: string): Promise<WorkflowExecution> {
    const process = this.processes.find(p => p.id === processId);
    if (!process) {
      throw new Error(`Process ${processId} not found`);
    }

    const execution: WorkflowExecution = {
      id: `exec_${Date.now()}`,
      processId,
      startTime: new Date(),
      status: 'running',
      logs: []
    };

    this.executions.push(execution);
    process.status = 'running';

    try {
      // Call Python script via backend API using existing API service
      const result = await apiService.post('/workflows/execute', { processId }) as WorkflowExecutionResult;

      if (result.success !== false) {
        execution.endTime = new Date();
        execution.status = 'completed';
        execution.exportPath = result.exportPath;
        process.status = 'completed';
        process.lastRun = new Date();
        process.exportPath = result.exportPath;

        this.addLog(processId, 'info', `Process ${process.name} completed successfully`);
      } else {
        throw new Error(result.error || result.message || 'Process execution failed');
      }

    } catch (error) {
      // Process execution failed
      execution.endTime = new Date();
      execution.status = 'error';
      execution.errorMessage = error instanceof Error ? error.message : 'Unknown error';
      process.status = 'error';

      this.addLog(processId, 'error', `Process ${process.name} failed: ${execution.errorMessage}`);
    }

    return execution;
  }

  async executeAllProcesses(): Promise<WorkflowExecution[]> {
    const executions: WorkflowExecution[] = [];

    for (const process of this.processes) {
      try {
        const execution = await this.executeProcess(process.id);
        executions.push(execution);
      } catch (error) {
        this.addLog(process.id, 'error', `Failed to execute ${process.name}: ${error}`);
      }
    }

    return executions;
  }

  async getExecutions(): Promise<WorkflowExecution[]> {
    return this.executions;
  }

  async getLogs(processId?: string): Promise<WorkflowLog[]> {
    try {
      const endpoint = processId
        ? `/workflows/logs?workflowId=${processId}`
        : '/workflows/logs';

      const result = await apiService.get(endpoint);

      if (Array.isArray(result)) {
        // Transform backend log format to frontend format
        return result.map((log: any) => ({
          id: log.id || `log_${Date.now()}_${Math.random()}`,
          timestamp: new Date(log.timestamp),
          level: log.level,
          message: log.message,
          processId: log.workflowId
        }));
      }

      return [];
    } catch (error) {
      // Failed to fetch logs from backend
      // Fallback to local logs
      if (processId) {
        return this.logs.filter(log => log.processId === processId);
      }
      return this.logs;
    }
  }

  async getStats(): Promise<WorkflowStats> {
    const totalExecutions = this.executions.length;
    const successfulExecutions = this.executions.filter(e => e.status === 'completed').length;
    const failedExecutions = this.executions.filter(e => e.status === 'error').length;

    const completedExecutions = this.executions.filter(e => e.endTime);
    const averageDuration = completedExecutions.length > 0
      ? completedExecutions.reduce((sum, e) => {
        const duration = e.endTime!.getTime() - e.startTime.getTime();
        return sum + duration;
      }, 0) / completedExecutions.length
      : 0;

    const lastExecution = this.executions.length > 0
      ? this.executions[this.executions.length - 1].startTime
      : undefined;

    return {
      totalExecutions,
      successfulExecutions,
      failedExecutions,
      averageDuration,
      lastExecution
    };
  }

  private addLog(processId: string, level: 'info' | 'warning' | 'error', message: string): void {
    const log: WorkflowLog = {
      id: `log_${Date.now()}_${Math.random()}`,
      timestamp: new Date(),
      level,
      message,
      processId
    };
    this.logs.push(log);
  }

  async updateProcessStatus(processId: string, status: SAPWorkflowProcess['status']): Promise<void> {
    const process = this.processes.find(p => p.id === processId);
    if (process) {
      process.status = status;
    }
  }

  async getWorkflowConfig(workflowId: string): Promise<any> {
    try {
      return await apiService.get(`/workflows/${workflowId}/config`);
    } catch (error) {
      // Failed to load config for workflow
      // Fallback zu localStorage
      const savedConfig = localStorage.getItem(`workflow_config_${workflowId}`);
      return savedConfig ? JSON.parse(savedConfig) : null;
    }
  }

  async updateWorkflowConfig(workflowId: string, config: any): Promise<any> {
    try {
      const result = await apiService.put(`/workflows/${workflowId}/config`, config);
      // Auch lokal speichern als Backup
      localStorage.setItem(`workflow_config_${workflowId}`, JSON.stringify(config));
      return result;
    } catch (error) {
      // Failed to save config for workflow
      // Fallback zu localStorage
      localStorage.setItem(`workflow_config_${workflowId}`, JSON.stringify(config));
      return config;
    }
  }
}

export const workflowService = new WorkflowService();