import React, { useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { useAuthContext } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * ProtectedRoute Component
 * 
 * Schützt Routen vor nicht-authentifizierten Benutzern.
 * Leitet automatisch zur Login-Seite weiter, wenn der Benutzer nicht angemeldet ist.
 * 
 * Requirements: 1.2, 2.1
 * - WHEN ein Benutzer versucht, eine geschützte Route aufzurufen AND nicht authentifiziert ist 
 *   THEN soll er zur Login-Seite weitergeleitet werden
 * - WHEN ein authentifizierter Benutzer die Anwendung neu startet AND ein gültiger Token vorhanden ist 
 *   THEN soll er direkt zum Dashboard weitergeleitet werden
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthContext();
  const navigate = useNavigate();

  useEffect(() => {
    // Warte bis der Authentifizierungsstatus geladen ist
    if (!isLoading && !isAuthenticated) {
      console.log('🔒 Nicht authentifiziert - Weiterleitung zur Login-Seite');
      navigate({ to: '/login' });
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Zeige Loading-Spinner während der Authentifizierungsprüfung
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-lg">Authentifizierung wird geprüft...</span>
      </div>
    );
  }

  // Rendere Kinder nur wenn authentifiziert
  return isAuthenticated ? <>{children}</> : null;
};