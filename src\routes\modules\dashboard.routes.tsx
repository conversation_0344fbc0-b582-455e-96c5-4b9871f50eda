import { createRoute } from "@tanstack/react-router";
import { ModuleGuard } from '@/components/auth';
import { dashboardModuleConfig } from '@/modules/dashboard/module.config';
import { RootRoute as rootRoute } from '@/routes/__root';
import { 
  LazyDashboardPage,
  LazyDispatchPage,
  LazyCuttingPage,
  LazyMachinesPage,
  LazyIncomingGoodsPage,
  LazyCSRPage,
  LazyAtrlPage,
  LazyArilPage,
  ModuleWrapper
} from '@/modules/lazy-loading';

/**
 * Dashboard Module Routes
 * 
 * Definiert alle Routen für das Dashboard-Modul mit rollenbasierter Zugriffskontrolle.
 * Alle Routen sind unter /modules/dashboard/* organisiert.
 */

// Leitstand Module Base Route (Dashboard)
export const DashboardModuleRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/modules/dashboard",
  component: () => (
    <ModuleGuard 
      moduleId={dashboardModuleConfig.id}
      requiredRoles={dashboardModuleConfig.requiredRoles}
    >
      <ModuleWrapper>
        <LazyDashboardPage />
      </ModuleWrapper>
    </ModuleGuard>
  ),
});

// Dispatch-Route
export const DashboardDispatchRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/modules/dashboard/dispatch",
  component: () => (
    <ModuleGuard 
      moduleId={dashboardModuleConfig.id}
      requiredRoles={dashboardModuleConfig.requiredRoles}
    >
      <ModuleWrapper>
        <LazyDispatchPage />
      </ModuleWrapper>
    </ModuleGuard>
  ),
});

// Cutting-Route
export const DashboardCuttingRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/modules/dashboard/cutting",
  component: () => (
    <ModuleGuard 
      moduleId={dashboardModuleConfig.id}
      requiredRoles={dashboardModuleConfig.requiredRoles}
    >
      <ModuleWrapper>
        <LazyCuttingPage />
      </ModuleWrapper>
    </ModuleGuard>
  ),
});

// IncomingGoods-Route
export const DashboardIncomingGoodsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/modules/dashboard/incoming-goods",
  component: () => (
    <ModuleGuard 
      moduleId={dashboardModuleConfig.id}
      requiredRoles={dashboardModuleConfig.requiredRoles}
    >
      <ModuleWrapper>
      <LazyIncomingGoodsPage />
    </ModuleWrapper>
  </ModuleGuard>
  )
});

// CSR-Route
export const DashboardCSRRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/modules/dashboard/CSR",
  component: () => (
    <ModuleGuard 
      moduleId={dashboardModuleConfig.id}
      requiredRoles={dashboardModuleConfig.requiredRoles}
    >
      <ModuleWrapper>
      <LazyCSRPage />
    </ModuleWrapper>
  </ModuleGuard>
  )
});

// Aril-Route
export const DashboardArilRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/modules/dashboard/aril",
  component: () => (
    <ModuleGuard 
      moduleId={dashboardModuleConfig.id}
      requiredRoles={dashboardModuleConfig.requiredRoles}
    >
      <ModuleWrapper>
        <LazyArilPage />
      </ModuleWrapper>
    </ModuleGuard>
  ),
});

// ATrL Page Route
export const DashboardAtrlRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/modules/dashboard/atrl",
  component: () => (
    <ModuleGuard 
      moduleId={dashboardModuleConfig.id}
      requiredRoles={dashboardModuleConfig.requiredRoles}
    >
      <ModuleWrapper>
        <LazyAtrlPage />
      </ModuleWrapper>
    </ModuleGuard>
  ),
});

// Machines Page Route
export const DashboardMachinesRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/modules/dashboard/machines",
  component: () => (
    <ModuleGuard 
      moduleId={dashboardModuleConfig.id}
      requiredRoles={dashboardModuleConfig.requiredRoles}
    >
      <ModuleWrapper>
        <LazyMachinesPage />
      </ModuleWrapper>
    </ModuleGuard>
  ),
});

// Exportiere alle Dashboard-Routen als Array
export const dashboardRoutes = [
  DashboardModuleRoute,
  DashboardDispatchRoute,
  DashboardCuttingRoute,
  DashboardIncomingGoodsRoute,
  DashboardCSRRoute,
  DashboardArilRoute,
  DashboardAtrlRoute,
  DashboardMachinesRoute,
];