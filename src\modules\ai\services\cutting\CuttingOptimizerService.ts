/**
 * Cutting Optimizer Service
 * 
 * Implements bin-packing algorithms for optimal cable cutting patterns.
 * Provides cutting plan optimization, waste analysis, and drum utilization.
 */

import { AIBaseService, AIServiceConfig } from '../base/AIBaseService';
import {
  CuttingRequest,
  CuttingPlan,
  CuttingOrder,
  AvailableDrum,
  DrumAllocation,
  Cut,
  CuttingStep,
  CuttingAlternative,
  CuttingConstraints
} from '../../types/cutting';
import {
  WasteAnalysis,
  DrumAnalysis,
  StockRecommendation,
  CuttingHistory,
  AIServiceError,
  InventoryDrum
} from '../types';
import {
  ManualDrumSelection,
  DrumComparisonResult,
  DeliveryTracking
} from '../../types/cutting';
import type { MultiObjectiveConfig, ParetoSolution } from './algorithms/MultiObjectiveOptimizer';
import type { Individual } from './algorithms/GeneticAlgorithm';
import { InventoryService } from '../inventory/InventoryService';
import { DrumComparisonService } from '../comparison/DrumComparisonService';
import TrommelrechnungService, { TrommelEmpfehlung, KabelMaterialData } from './TrommelrechnungService';

/**
 * Configuration for cutting optimization service
 */
export interface CuttingOptimizerConfig extends AIServiceConfig {
  defaultMinCutLength?: number;
  defaultMaxWastePercentage?: number;
  defaultAlgorithm?: 'first-fit' | 'best-fit' | 'genetic';
  enableAdvancedOptimization?: boolean;
  maxOptimizationTime?: number; // milliseconds
}

/**
 * Interface für KI-Trommelauswahl pro Kabeltyp
 */
export interface AITrommelSelection {
  kabeltyp: string; // MATNR des Kabels
  empfohlene_trommel: string; // Name der empfohlenen Trommel
  grund: string; // Begründung für die Auswahl
  maximale_länge: number; // Maximale Kabellänge auf dieser Trommel
  confidence: number; // KI-Vertrauen 0-1
  material_daten?: KabelMaterialData; // Geladene Materialdaten
}

/**
 * Ergebnis der KI-Trommelauswahl
 */
export interface AITrommelauswahlResult {
  selections: AITrommelSelection[]; // Auswahl pro Kabeltyp
  verfügbare_trommeln: AvailableDrum[]; // Alle empfohlenen Trommeln für die Optimierung
  gesamt_confidence: number; // Durchschnittliches Vertrauen
  warnungen: string[]; // Warnungen und Hinweise
  erfolg: boolean; // Ob die Auswahl erfolgreich war
}

/**
 * Cutting optimization algorithms implementation
 */
export class CuttingOptimizerService extends AIBaseService {
  protected config: CuttingOptimizerConfig;
  readonly serviceName = 'CuttingOptimizer';
  private inventoryService?: InventoryService;
  private comparisonService?: DrumComparisonService;

  constructor(config: CuttingOptimizerConfig = {}) {
    super(config);
    this.config = {
      defaultMinCutLength: 50, // 50cm minimum cut length
      defaultMaxWastePercentage: 15, // 15% maximum waste
      defaultAlgorithm: 'best-fit',
      enableAdvancedOptimization: true,
      maxOptimizationTime: 30000, // 30 seconds
      ...config
    };
  }

  /**
   * Initialisiert die Services für Vergleichslogik
   */
  setComparisonServices(inventoryService: InventoryService, comparisonService: DrumComparisonService): void {
    this.inventoryService = inventoryService;
    this.comparisonService = comparisonService;
  }

  /**
   * Initialize the cutting optimizer service
   */
  async initialize(config?: CuttingOptimizerConfig): Promise<void> {
    if (config) {
      this.config = { ...this.config, ...config };
    }

    await super.initialize(this.config);
    console.log('Cutting Optimizer Service initialized');
  }

  /**
   * Optimize cutting plan using bin-packing algorithms
   */
  async optimizeCuttingPlan(request: CuttingRequest): Promise<CuttingPlan> {
    return await this.handleAIError(
      async () => {
        this.validateCuttingRequest(request);

        const startTime = Date.now();

        // KI-Trommelauswahl: Falls keine manuellen Trommeln ausgewählt wurden,
        // verwende die KI-basierte automatische Auswahl
        let processedRequest = request;
        if (!request.availableDrums || request.availableDrums.length === 0) {
          console.log('Keine manuellen Trommeln ausgewählt - verwende KI-Trommelauswahl');
          
          try {
            const aiSelection = await this.selectOptimalDrumsAI(request.orders, request.constraints);
            
            if (aiSelection.erfolg && aiSelection.verfügbare_trommeln.length > 0) {
              processedRequest = {
                ...request,
                availableDrums: aiSelection.verfügbare_trommeln
              };
              
              console.log(`KI-Trommelauswahl erfolgreich: ${aiSelection.selections.length} Empfehlungen mit Confidence ${(aiSelection.gesamt_confidence * 100).toFixed(1)}%`);
              
              // Warnungen ausgeben falls vorhanden
              if (aiSelection.warnungen.length > 0) {
                console.warn('KI-Trommelauswahl Warnungen:', aiSelection.warnungen);
              }
            } else {
              console.warn('KI-Trommelauswahl fehlgeschlagen - verwende ursprüngliche Anfrage');
            }
          } catch (error) {
            console.error('Fehler bei KI-Trommelauswahl:', error);
            // Verwende ursprüngliche Anfrage bei Fehler
          }
        } else {
          console.log(`Verwende ${request.availableDrums.length} manuell ausgewählte Trommeln`);
        }

        // Apply constraints and priorities
        const finalProcessedRequest = this.preprocessRequest(processedRequest);

        // Run optimization algorithm
        const plan = await this.runOptimization(finalProcessedRequest);

        // Validate and enhance the plan
        const validatedPlan = this.validateAndEnhancePlan(plan, processedRequest);

        const processingTime = Date.now() - startTime;
        console.log(`Cutting optimization completed in ${processingTime}ms`);

        return validatedPlan;
      },
      async () => {
        // Fallback to simple first-fit algorithm
        console.log('Falling back to simple first-fit algorithm');
        return await this.simpleFirstFitOptimization(request);
      },
      AIServiceError.OPTIMIZATION_FAILED
    );
  }

  /**
   * Calculate waste analysis for a cutting plan
   */
  async calculateWaste(plan: CuttingPlan): Promise<WasteAnalysis> {
    const wasteByDrum = plan.drumAllocations.map(allocation => ({
      drumId: allocation.drumId,
      waste: allocation.remainingLength
    }));

    const totalWaste = wasteByDrum.reduce((sum, drum) => sum + drum.waste, 0);
    const totalUsed = plan.drumAllocations.reduce((sum, allocation) => {
      const drumTotalLength = allocation.cuts.reduce((cutSum, cut) => cutSum + cut.length, 0) + allocation.remainingLength;
      return sum + drumTotalLength;
    }, 0);

    const wastePercentage = totalUsed > 0 ? (totalWaste / totalUsed) * 100 : 0;

    const recommendations = this.generateWasteRecommendations(wasteByDrum, wastePercentage);

    return {
      totalWaste,
      wastePercentage,
      wasteByDrum,
      recommendations
    };
  }

  /**
   * Generate alternative cutting solutions using advanced algorithms
   */
  async simulateAlternatives(request: CuttingRequest): Promise<CuttingAlternative[]> {
    return await this.handleAIError(
      async () => {
        try {
          const { AlternativeSolutionGenerator } = await import('./algorithms/AlternativeSolutionGenerator');

          const generator = new AlternativeSolutionGenerator({
            maxAlternatives: 6,
            includeBasicAlgorithms: true,
            includeAdvancedAlgorithms: this.config.enableAdvancedOptimization,
            includeHeuristicVariations: true,
            timeLimit: this.config.maxOptimizationTime || 30000,
            diversityThreshold: 0.1,
            qualityThreshold: 0.6
          });

          const result = await generator.generateAlternatives(request);

          // Convert RankedAlternative to CuttingAlternative
          return result.alternatives.map(alt => ({
            id: alt.id,
            plan: alt.plan,
            score: alt.score,
            advantages: alt.advantages,
            disadvantages: alt.disadvantages
          }));
        } catch (error) {
          console.error(`Advanced alternative generation failed: ${error}, using basic alternatives`);
          return await this.generateBasicAlternatives(request);
        }
      },
      async () => {
        // Fallback: return single alternative
        const plan = await this.simpleFirstFitOptimization(request);
        return [{
          id: 'fallback',
          plan,
          score: this.calculatePlanScore(plan),
          advantages: ['Simple and reliable'],
          disadvantages: ['May not be optimal']
        }];
      },
      AIServiceError.OPTIMIZATION_FAILED
    );
  }

  /**
   * Generate basic alternatives (fallback method)
   */
  private async generateBasicAlternatives(request: CuttingRequest): Promise<CuttingAlternative[]> {
    const alternatives: CuttingAlternative[] = [];

    // Generate alternatives using different algorithms
    const algorithms: Array<'first-fit' | 'best-fit'> = ['first-fit', 'best-fit'];

    for (const algorithm of algorithms) {
      const altConfig = { ...this.config, defaultAlgorithm: algorithm };
      const altService = new CuttingOptimizerService(altConfig);
      await altService.initialize();

      const plan = await altService.optimizeCuttingPlan(request);
      const score = this.calculatePlanScore(plan);

      alternatives.push({
        id: `${algorithm}-${Date.now()}`,
        plan,
        score,
        advantages: this.getAlgorithmAdvantages(algorithm),
        disadvantages: this.getAlgorithmDisadvantages(algorithm)
      });
    }

    // Sort by score (higher is better)
    return alternatives.sort((a, b) => b.score - a.score);
  }

  /**
   * Run multi-objective optimization
   */
  async optimizeMultiObjective(request: CuttingRequest): Promise<{
    paretoFront: any[];
    alternatives: CuttingAlternative[];
    metrics: any;
  }> {
    return await this.handleAIError(
      async () => {
        const { MultiObjectiveOptimizer } = await import('./algorithms/MultiObjectiveOptimizer');

        const moConfig: MultiObjectiveConfig = {
          populationSize: 80,
          generations: 120,
          mutationRate: 0.1,
          crossoverRate: 0.9,
          elitismRate: 0.2,
          tournamentSize: 5,
          convergenceThreshold: 0.001,
          maxStagnantGenerations: 20,
          objectives: [
            { name: 'wasteMinimization', weight: 1.0, minimize: false, normalize: true },
            { name: 'efficiency', weight: 1.0, minimize: false, normalize: true },
            { name: 'timeOptimization', weight: 0.8, minimize: false, normalize: true }
          ],
          paretoFrontSize: 10
        };

        const optimizer = new MultiObjectiveOptimizer(moConfig);
        const result = await optimizer.optimize(request);

        console.log(`Multi-objective optimization completed in ${result.executionTime}ms`);

        return {
          paretoFront: result.paretoFront,
          alternatives: result.alternatives,
          metrics: {
            hypervolume: result.hypervolume,
            convergenceMetrics: result.convergenceMetrics,
            executionTime: result.executionTime
          }
        };
      },
      async () => {
        // Fallback: return single solution as Pareto front
        const plan = await this.bestFitOptimization(request);
        const alternative: CuttingAlternative = {
          id: 'fallback-mo',
          plan,
          score: this.calculatePlanScore(plan),
          advantages: ['Reliable solution'],
          disadvantages: ['Single objective optimization']
        };

        // Create a proper ParetoSolution object
        const paretoSolution: ParetoSolution = {
          individual: {
            chromosome: [],
            fitness: this.calculatePlanScore(plan),
            objectives: {
              wasteMinimization: plan.efficiency,
              efficiency: plan.efficiency,
              timeOptimization: 1 - (plan.estimatedTime / 3600)
            },
            plan
          },
          rank: 0,
          crowdingDistance: 0,
          dominationCount: 0,
          dominatedSolutions: new Set<number>()
        };

        return {
          paretoFront: [paretoSolution],
          alternatives: [alternative],
          metrics: {
            hypervolume: 0,
            convergenceMetrics: [{
              generation: 0,
              paretoFrontSize: 1,
              hypervolume: 0,
              spacing: 0
            }],
            executionTime: 0
          }
        };
      },
      AIServiceError.OPTIMIZATION_FAILED
    );
  }

  /**
   * Generate cutting pattern visualization data
   */
  async generateVisualization(request: CuttingRequest, plan: CuttingPlan): Promise<any> {
    return await this.handleAIError(
      async () => {
        // For now, skip the advanced analyzer due to type mismatch and return basic visualization
        console.log('Generating basic cutting pattern visualization');

        return {
          drumVisualizations: plan.drumAllocations.map(allocation => ({
            drumId: allocation.drumId,
            cuts: allocation.cuts,
            utilization: allocation.utilization,
            efficiency: allocation.utilization
          })),
          wasteDistribution: {
            totalWaste: plan.totalWaste,
            wastePercentage: plan.drumAllocations.length > 0 ? (plan.totalWaste / plan.drumAllocations.reduce((sum, alloc) => {
              const drum = request.availableDrums.find(d => d.id === alloc.drumId);
              return sum + (drum ? (drum.availableLength || drum.remainingLength || 0) : 0);
            }, 0)) * 100 : 0
          },
          recommendations: ['Consider using advanced optimization algorithms'],
          efficiencyMetrics: {
            overall: plan.efficiency,
            byDrum: plan.drumAllocations.map(alloc => ({
              drumId: alloc.drumId,
              efficiency: alloc.utilization
            }))
          },
          patternAnalysis: {
            totalCuts: plan.drumAllocations.reduce((sum, alloc) => sum + alloc.cuts.length, 0),
            averageCutLength: plan.drumAllocations.reduce((sum, alloc) =>
              sum + alloc.cuts.reduce((cutSum, cut) => cutSum + cut.length, 0), 0) /
              Math.max(1, plan.drumAllocations.reduce((sum, alloc) => sum + alloc.cuts.length, 0))
          }
        };
      },
      async () => {
        // Fallback: return basic visualization data
        return {
          drumVisualizations: plan.drumAllocations.map(allocation => ({
            drumId: allocation.drumId,
            cuts: allocation.cuts,
            utilization: allocation.utilization,
            efficiency: allocation.utilization
          })),
          wasteDistribution: {
            totalWaste: plan.totalWaste,
            wastePercentage: (plan.totalWaste / 1000) * 100 // Simplified
          },
          recommendations: ['Consider using advanced optimization algorithms'],
          efficiencyMetrics: {
            overall: plan.efficiency,
            byDrum: []
          },
          patternAnalysis: {
            totalCuts: 0,
            averageCutLength: 0
          }
        };
      },
      AIServiceError.OPTIMIZATION_FAILED
    );
  }

  /**
   * Analyze drum utilization
   */
  async analyzeDrumUtilization(drumId: string): Promise<DrumAnalysis> {
    // This would typically query historical data
    // For now, return mock analysis
    return {
      drumId,
      utilizationHistory: [
        { date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), utilization: 0.85 },
        { date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), utilization: 0.78 },
        { date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), utilization: 0.92 },
        { date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), utilization: 0.88 },
        { date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), utilization: 0.75 },
        { date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), utilization: 0.89 },
        { date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), utilization: 0.83 }
      ],
      averageUtilization: 0.84,
      recommendations: [
        'Drum shows good utilization patterns',
        'Consider using for high-priority orders',
        'Monitor for quality degradation'
      ]
    };
  }

  /**
   * Predict optimal stock levels
   */
  async predictOptimalStock(historicalData: CuttingHistory[]): Promise<StockRecommendation> {
    if (historicalData.length === 0) {
      throw new Error('Insufficient historical data for stock prediction');
    }

    // Simple analysis - in production this would use ML models
    const avgEfficiency = historicalData.reduce((sum, record) => sum + record.efficiency, 0) / historicalData.length;
    const avgCuts = historicalData.reduce((sum, record) => sum + record.totalCuts, 0) / historicalData.length;

    const cableType = historicalData[0].cableType;
    const currentStock = 1000; // This would come from inventory system
    const recommendedStock = Math.ceil(avgCuts * 1.2); // 20% buffer

    return {
      cableType,
      recommendedStock,
      currentStock,
      reasoning: `Based on ${historicalData.length} days of data, average daily cuts: ${avgCuts.toFixed(0)}, efficiency: ${(avgEfficiency * 100).toFixed(1)}%`,
      confidence: Math.min(0.95, historicalData.length / 30) // Higher confidence with more data
    };
  }

  /**
   * Validate cutting request
   */
  private validateCuttingRequest(request: CuttingRequest): void {
    if (!request.orders || request.orders.length === 0) {
      throw new Error('No cutting orders provided');
    }

    if (!request.availableDrums || request.availableDrums.length === 0) {
      throw new Error('No available drums provided');
    }

    // Validate each order
    for (const order of request.orders) {
      if (order.requiredLength <= 0) {
        throw new Error(`Invalid required length for order ${order.id}`);
      }
      if (order.quantity <= 0) {
        throw new Error(`Invalid quantity for order ${order.id}`);
      }
    }

    // Validate each drum
    for (const drum of request.availableDrums) {
      const drumLength = drum.availableLength || drum.remainingLength || 0;
      if (drumLength <= 0) {
        throw new Error(`Invalid available length for drum ${drum.id}`);
      }
    }
  }

  /**
   * Preprocess request by applying constraints and priorities
   */
  private preprocessRequest(request: CuttingRequest): CuttingRequest {
    // Sort orders by priority and deadline
    const sortedOrders = [...request.orders].sort((a, b) => {
      const priorityWeight = { high: 3, medium: 2, low: 1 };
      const aPriority = priorityWeight[a.priority];
      const bPriority = priorityWeight[b.priority];

      if (aPriority !== bPriority) {
        return bPriority - aPriority; // Higher priority first
      }

      // If same priority, maintain original order (no deadline property in new type)

      return 0;
    });

    // Use all available drums (no filtering by preferred drums in this constraint type)
    const filteredDrums = request.availableDrums;

    return {
      ...request,
      orders: sortedOrders,
      availableDrums: filteredDrums
    };
  }

  /**
   * Run the main optimization algorithm
   */
  private async runOptimization(request: CuttingRequest): Promise<CuttingPlan> {
    switch (this.config.defaultAlgorithm) {
      case 'first-fit':
        return await this.firstFitOptimization(request);
      case 'best-fit':
        return await this.bestFitOptimization(request);
      case 'genetic':
        return await this.geneticOptimization(request);
      default:
        return await this.bestFitOptimization(request);
    }
  }

  /**
   * First-fit bin packing algorithm
   */
  private async firstFitOptimization(request: CuttingRequest): Promise<CuttingPlan> {
    const drumAllocations: DrumAllocation[] = [];
    const cuttingSequence: CuttingStep[] = [];
    let stepNumber = 1;

    // Initialize drum allocations
    for (const drum of request.availableDrums) {
      drumAllocations.push({
        drumId: drum.id,
        cuts: [],
        remainingLength: drum.availableLength || drum.remainingLength || 0,
        utilization: 0
      });
    }

    // Process each order
    for (const order of request.orders) {
      for (let i = 0; i < order.quantity; i++) {
        let placed = false;

        // Try to place in first available drum
        for (const allocation of drumAllocations) {
          const drum = request.availableDrums.find(d => d.id === allocation.drumId);
          if (!drum) continue;

          // Check if cut fits and meets constraints
          if (this.canPlaceCut(allocation, order, drum, request.constraints)) {
            const cut = this.createCut(order, allocation);
            allocation.cuts.push(cut);
            allocation.remainingLength -= order.requiredLength;
            allocation.utilization = this.calculateUtilization(allocation, drum);

            // Add to cutting sequence
            cuttingSequence.push({
              stepNumber: stepNumber++,
              drumId: drum.id,
              cuts: [cut],
              estimatedTime: this.estimateCuttingTime(order.requiredLength)
            });

            placed = true;
            break;
          }
        }

        if (!placed) {
          console.warn(`Warning: Could not place cut for order ${order.id}, quantity ${i + 1}`);
        }
      }
    }

    const totalWaste = drumAllocations.reduce((sum, allocation) => sum + allocation.remainingLength, 0);
    const efficiency = this.calculateEfficiency(drumAllocations, request.availableDrums);
    const estimatedTime = cuttingSequence.reduce((sum, step) => sum + step.estimatedTime, 0);

    return {
      drumAllocations,
      cuttingSequence,
      totalWaste,
      efficiency,
      estimatedTime
    };
  }

  /**
   * Best-fit bin packing algorithm
   */
  private async bestFitOptimization(request: CuttingRequest): Promise<CuttingPlan> {
    const drumAllocations: DrumAllocation[] = [];
    const cuttingSequence: CuttingStep[] = [];
    let stepNumber = 1;

    // Initialize drum allocations
    for (const drum of request.availableDrums) {
      drumAllocations.push({
        drumId: drum.id,
        cuts: [],
        remainingLength: drum.availableLength || drum.remainingLength || 0,
        utilization: 0
      });
    }

    // Process each order
    for (const order of request.orders) {
      for (let i = 0; i < order.quantity; i++) {
        let bestFit: { allocation: DrumAllocation; drum: AvailableDrum; wasteAfter: number } | null = null;

        // Find best fit (minimum waste after placement)
        for (const allocation of drumAllocations) {
          const drum = request.availableDrums.find(d => d.id === allocation.drumId);
          if (!drum) continue;

          if (this.canPlaceCut(allocation, order, drum, request.constraints)) {
            const wasteAfter = allocation.remainingLength - order.requiredLength;

            if (!bestFit || wasteAfter < bestFit.wasteAfter) {
              bestFit = { allocation, drum, wasteAfter };
            }
          }
        }

        if (bestFit) {
          const cut = this.createCut(order, bestFit.allocation);
          bestFit.allocation.cuts.push(cut);
          bestFit.allocation.remainingLength -= order.requiredLength;
          bestFit.allocation.utilization = this.calculateUtilization(bestFit.allocation, bestFit.drum);

          // Add to cutting sequence
          cuttingSequence.push({
            stepNumber: stepNumber++,
            drumId: bestFit.drum.id,
            cuts: [cut],
            estimatedTime: this.estimateCuttingTime(order.requiredLength)
          });
        } else {
          console.warn(`Warning: Could not place cut for order ${order.id}, quantity ${i + 1}`);
        }
      }
    }

    const totalWaste = drumAllocations.reduce((sum, allocation) => sum + allocation.remainingLength, 0);
    const efficiency = this.calculateEfficiency(drumAllocations, request.availableDrums);
    const estimatedTime = cuttingSequence.reduce((sum, step) => sum + step.estimatedTime, 0);

    return {
      drumAllocations,
      cuttingSequence,
      totalWaste,
      efficiency,
      estimatedTime
    };
  }

  /**
   * Genetic algorithm optimization
   */
  private async geneticOptimization(request: CuttingRequest): Promise<CuttingPlan> {
    try {
      const { GeneticAlgorithm } = await import('./algorithms/GeneticAlgorithm');

      const gaConfig = {
        populationSize: 50,
        generations: 100,
        mutationRate: 0.1,
        crossoverRate: 0.8,
        elitismRate: 0.2,
        tournamentSize: 5,
        convergenceThreshold: 0.001,
        maxStagnantGenerations: 20
      };

      const ga = new GeneticAlgorithm(gaConfig);
      const result = await ga.optimize(request);

      if (result.bestSolution.plan) {
        console.log(`Genetic algorithm completed in ${result.executionTime}ms, converged: ${result.converged}`);
        return result.bestSolution.plan;
      } else {
        throw new Error('Genetic algorithm failed to generate a valid plan');
      }
    } catch (error) {
      console.error(`Genetic algorithm failed: ${error}, falling back to best-fit`);
      return await this.bestFitOptimization(request);
    }
  }

  /**
   * Simple first-fit optimization for fallback
   */
  private async simpleFirstFitOptimization(request: CuttingRequest): Promise<CuttingPlan> {
    // Simplified version with minimal error checking
    const drumAllocations: DrumAllocation[] = request.availableDrums.map(drum => ({
      drumId: drum.id,
      cuts: [],
      remainingLength: drum.availableLength || drum.remainingLength || 0,
      utilization: 0
    }));

    const cuttingSequence: CuttingStep[] = [];
    let stepNumber = 1;

    for (const order of request.orders) {
      for (let i = 0; i < order.quantity; i++) {
        for (const allocation of drumAllocations) {
          if (allocation.remainingLength >= order.requiredLength) {
            const cut: Cut = {
              orderId: order.id,
              length: order.requiredLength,
              startPosition: 0, // Simplified
              endPosition: order.requiredLength
            };

            allocation.cuts.push(cut);
            allocation.remainingLength -= order.requiredLength;
            const drum = request.availableDrums.find(d => d.id === allocation.drumId)!;
            const totalLength = drum.availableLength || drum.remainingLength || 0;
            allocation.utilization = totalLength > 0 ? 1 - (allocation.remainingLength / totalLength) : 0;

            cuttingSequence.push({
              stepNumber: stepNumber++,
              drumId: allocation.drumId,
              cuts: [cut],
              estimatedTime: 60 // 1 minute per cut
            });

            break;
          }
        }
      }
    }

    return {
      drumAllocations,
      cuttingSequence,
      totalWaste: drumAllocations.reduce((sum, allocation) => sum + allocation.remainingLength, 0),
      efficiency: 0.8, // Simplified
      estimatedTime: cuttingSequence.length * 60
    };
  }

  /**
   * Check if a cut can be placed in a drum allocation
   */
  private canPlaceCut(
    allocation: DrumAllocation,
    order: CuttingOrder,
    drum: AvailableDrum,
    constraints?: CuttingConstraints
  ): boolean {
    // Check if there's enough length
    if (allocation.remainingLength < order.requiredLength) {
      return false;
    }

    // Check maximum waste length if specified
    if (constraints?.maxWasteLength && allocation.remainingLength - order.requiredLength > constraints.maxWasteLength) {
      return false;
    }

    // Check maximum cuts per drum if specified
    if (constraints?.maxCutsPerDrum && allocation.cuts.length >= constraints.maxCutsPerDrum) {
      return false;
    }

    // Check if drum material matches order (if both have material specified)
    if (drum.material && order.material && drum.material !== order.material) {
      return false;
    }

    return true;
  }

  /**
   * Create a cut object
   */
  private createCut(order: CuttingOrder, allocation: DrumAllocation): Cut {
    const startPosition = allocation.cuts.reduce((sum, cut) => sum + cut.length, 0);

    return {
      orderId: order.id,
      length: order.requiredLength,
      startPosition,
      endPosition: startPosition + order.requiredLength
    };
  }

  /**
   * Calculate drum utilization
   */
  private calculateUtilization(allocation: DrumAllocation, drum: AvailableDrum): number {
    const totalLength = drum.availableLength || drum.remainingLength || 0;
    const usedLength = totalLength - allocation.remainingLength;
    return totalLength > 0 ? usedLength / totalLength : 0;
  }

  /**
   * Calculate overall efficiency
   */
  private calculateEfficiency(allocations: DrumAllocation[], drums: AvailableDrum[]): number {
    const totalCapacity = drums.reduce((sum, drum) => sum + (drum.availableLength || drum.remainingLength || 0), 0);
    const totalUsed = allocations.reduce((sum, allocation) => {
      const drum = drums.find(d => d.id === allocation.drumId);
      const drumLength = drum ? (drum.availableLength || drum.remainingLength || 0) : 0;
      return sum + (drumLength - allocation.remainingLength);
    }, 0);

    return totalCapacity > 0 ? totalUsed / totalCapacity : 0;
  }

  /**
   * Estimate cutting time for a given length
   */
  private estimateCuttingTime(length: number): number {
    // Base time + length-dependent time
    return 30 + (length / 100) * 10; // 30 seconds base + 10 seconds per meter
  }

  /**
   * Get cable type from existing cuts
   */
  private getCableTypeFromCuts(cuts: Cut[]): string | null {
    // This would need to be enhanced to track cable types per cut
    // For now, return null to allow mixed types
    return null;
  }

  /**
   * Validate and enhance cutting plan
   */
  private validateAndEnhancePlan(plan: CuttingPlan, request: CuttingRequest): CuttingPlan {
    // Validate that all cuts are within drum bounds
    for (const allocation of plan.drumAllocations) {
      const drum = request.availableDrums.find(d => d.id === allocation.drumId);
      if (!drum) continue;

      const totalCutLength = allocation.cuts.reduce((sum, cut) => sum + cut.length, 0);
      const drumLength = drum.availableLength || drum.remainingLength || 0;
      if (totalCutLength + allocation.remainingLength !== drumLength) {
        console.warn(`Warning: Length mismatch for drum ${drum.id}`);
      }
    }

    return plan;
  }

  /**
   * Calculate plan score for comparison
   */
  private calculatePlanScore(plan: CuttingPlan): number {
    // Higher efficiency and lower waste = higher score
    const efficiencyScore = plan.efficiency * 100;
    const wasteScore = Math.max(0, 100 - plan.totalWaste);
    const timeScore = Math.max(0, 100 - (plan.estimatedTime / 3600)); // Penalty for long times

    return (efficiencyScore * 0.5) + (wasteScore * 0.3) + (timeScore * 0.2);
  }

  /**
   * Get algorithm advantages
   */
  private getAlgorithmAdvantages(algorithm: string): string[] {
    switch (algorithm) {
      case 'first-fit':
        return ['Fast execution', 'Simple and reliable', 'Good for time-critical operations'];
      case 'best-fit':
        return ['Better space utilization', 'Reduced waste', 'Good balance of speed and efficiency'];
      case 'genetic':
        return ['Optimal solutions', 'Handles complex constraints', 'Learns from patterns'];
      default:
        return ['Standard approach'];
    }
  }

  /**
   * Get algorithm disadvantages
   */
  private getAlgorithmDisadvantages(algorithm: string): string[] {
    switch (algorithm) {
      case 'first-fit':
        return ['May not be optimal', 'Higher waste potential', 'Less efficient space usage'];
      case 'best-fit':
        return ['Slower than first-fit', 'May create small unusable spaces', 'More complex logic'];
      case 'genetic':
        return ['Longer computation time', 'Complex to tune', 'May not converge'];
      default:
        return ['May have limitations'];
    }
  }

  /**
   * Generate waste reduction recommendations
   */
  private generateWasteRecommendations(wasteByDrum: { drumId: string; waste: number }[], wastePercentage: number): string[] {
    const recommendations: string[] = [];

    if (wastePercentage > 20) {
      recommendations.push('Consider using different drum sizes or cable types');
      recommendations.push('Review cutting patterns for better optimization');
    }

    if (wastePercentage > 15) {
      recommendations.push('High waste detected - consider alternative algorithms');
    }

    const highWasteDrums = wasteByDrum.filter(drum => drum.waste > 100);
    if (highWasteDrums.length > 0) {
      recommendations.push(`Drums with high waste: ${highWasteDrums.map(d => d.drumId).join(', ')}`);
    }

    if (recommendations.length === 0) {
      recommendations.push('Waste levels are within acceptable range');
    }

    return recommendations;
  }

  /**
   * Calculate drum capacity (used in tests)
   */
  calculateDrumCapacity(drum: AvailableDrum): number {
    return drum.remainingLength || drum.availableLength || 0;
  }

  /**
   * Validate drum constraints (used in tests)
   */
  validateDrumConstraints(drum: AvailableDrum, constraints: CuttingConstraints): boolean {
    // Check maximum waste length if specified
    const capacity = this.calculateDrumCapacity(drum);
    if (constraints.maxWasteLength && capacity > constraints.maxWasteLength) {
      return false;
    }

    return true;
  }

  /**
   * Calculate optimal cut pattern for a single drum (used in tests)
   */
  calculateOptimalCutPattern(drum: AvailableDrum, requiredLengths: number[]): {
    cuts: { length: number; orderId: string }[];
    totalUsedLength: number;
    wasteLength: number;
    efficiency: number;
  } {
    const cuts: { length: number; orderId: string }[] = [];
    let remainingLength = this.calculateDrumCapacity(drum);

    // Sort lengths in descending order for better packing
    const sortedLengths = [...requiredLengths].sort((a, b) => b - a);

    for (let i = 0; i < sortedLengths.length; i++) {
      const length = sortedLengths[i];
      if (remainingLength >= length) {
        cuts.push({
          length,
          orderId: `auto-${i}`
        });
        remainingLength -= length;
      }
    }

    const totalUsedLength = cuts.reduce((sum, cut) => sum + cut.length, 0);
    const wasteLength = remainingLength;
    const efficiency = totalUsedLength / this.calculateDrumCapacity(drum);

    return {
      cuts,
      totalUsedLength,
      wasteLength,
      efficiency
    };
  }

  /**
   * Calculate waste percentage (used in tests)
   */
  calculateWastePercentage(drumLength: number, usedLength: number): number {
    if (drumLength === 0) return 0;
    return ((drumLength - usedLength) / drumLength) * 100;
  }

  /**
   * Vergleicht KI-Auswahl mit manueller Trommelauswahl
   */
  async compareWithManualSelection(
    aiPlan: CuttingPlan,
    manualSelections: ManualDrumSelection[],
    deliveryTracking: DeliveryTracking
  ): Promise<DrumComparisonResult> {
    if (!this.comparisonService) {
      throw new Error('ComparisonService nicht initialisiert. Rufen Sie setComparisonServices() auf.');
    }

    // Extract delivery items from the plan for comparison
    const deliveryItems = aiPlan.drumAllocations.map(allocation => ({
      id: allocation.drumId,
      cableType: 'Unknown', // Would need to be extracted from cuts or provided
      requiredLength: allocation.cuts.reduce((sum, cut) => sum + cut.length, 0),
      quantity: allocation.cuts.length,
      priority: 'medium' as const
    }));

    // Extract AI suggestions from plan
    const aiSuggestions = aiPlan.drumAllocations.map(allocation => ({
      id: allocation.drumId,
      availableLength: allocation.remainingLength,
      remainingLength: allocation.remainingLength,
      material: 'Unknown', // Would need to be extracted from drum data
      diameter: 0 // Would need to be extracted from drum data
    }));

    return await this.comparisonService.compareSelections(
      deliveryTracking.deliveryId,
      deliveryItems,
      aiSuggestions,
      manualSelections,
      deliveryTracking.selectionDate,
      {}
    );
  }

  /**
   * Erstellt eine Lieferverfolgung basierend auf dem aktuellen Plan
   */
  createDeliveryTracking(
    plan: CuttingPlan,
    deliveryId: string,
    selectionDate: string
  ): DeliveryTracking {
    return {
      deliveryId,
      aiSuggestedDrums: plan.drumAllocations.map(allocation => ({
          id: allocation.drumId,
          availableLength: allocation.remainingLength + allocation.cuts.reduce((sum, cut) => sum + cut.length, 0),
          remainingLength: allocation.remainingLength,
          material: 'Unknown', // Würde normalerweise aus Bestandsdaten kommen
          diameter: 0 // Würde normalerweise aus Bestandsdaten kommen
        })),
      selectedDrums: [], // Wird durch manuelle Auswahl gefüllt
      selectionDate: new Date(selectionDate),
      // comparisonResults: [] // Removed as it doesn't exist in DeliveryTracking type
    };
  }

  /**
   * Lädt verfügbare Trommeln für eine Lieferung
   */
  async loadAvailableDrumsForDelivery(
    deliveryItems: Array<{ cableType: string; requiredLength: number; quantity: number }>
  ): Promise<ManualDrumSelection[]> {
    if (!this.inventoryService) {
      throw new Error('InventoryService nicht initialisiert. Rufen Sie setComparisonServices() auf.');
    }

    const availableDrums: ManualDrumSelection[] = [];

    // Für jeden Kabeltyp in der Lieferung verfügbare Trommeln laden
    for (const item of deliveryItems) {
      try {
        const drumsResult = await this.inventoryService.getAvailableDrums({
          materials: [item.cableType],
          limit: 10
        });

        // Convert InventoryDrum to ManualDrumSelection
        const selections = drumsResult.drums.map((drum: InventoryDrum) => ({
          chargeNumber: drum.charge || '',
          material: drum.material || '',
          lagereinheitentyp: drum.lagereinheitentyp || '',
          gesamtbestand: drum.gesamtbestand || 0,
          aufnahmedatum: drum.aufnahmeDatum || ''
        }));

        availableDrums.push(...selections);
      } catch (error) {
        console.error(`Fehler beim Laden der Trommeln für Material ${item.cableType}: ${error}`);
      }
    }

    return availableDrums;
  }

  /**
   * Analysiert die Qualität der KI-Auswahl basierend auf Vergleichsergebnissen
   */
  async analyzeAIQuality(comparisonResults: DrumComparisonResult[]): Promise<{
    averageQualityScore: number;
    improvementAreas: string[];
    successPatterns: string[];
    recommendations: string[];
  }> {
    if (comparisonResults.length === 0) {
      return {
        averageQualityScore: 0,
        improvementAreas: ['Keine Vergleichsdaten verfügbar'],
        successPatterns: [],
        recommendations: ['Sammeln Sie mehr Vergleichsdaten für eine aussagekräftige Analyse']
      };
    }

    const averageQualityScore = comparisonResults.reduce(
      (sum, result) => sum + result.qualityScore, 0
    ) / comparisonResults.length;

    const improvementAreas: string[] = [];
    const successPatterns: string[] = [];
    const recommendations: string[] = [];

    // Analyse der häufigsten Verbesserungsbereiche basierend auf Unterschieden
    const allDifferences = comparisonResults.flatMap(result => result.differences);
    const differenceTypes = allDifferences.reduce((acc, diff) => {
      acc[diff.type] = (acc[diff.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Top 3 Verbesserungsbereiche
    Object.entries(differenceTypes)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 3)
      .forEach(([type]) => improvementAreas.push(`Verbesserung bei: ${type}`));

    // Erfolgspattern identifizieren
    const highQualityResults = comparisonResults.filter(result => result.qualityScore > 80);
    if (highQualityResults.length > 0) {
      successPatterns.push(`${highQualityResults.length} von ${comparisonResults.length} Auswahlen erreichten >80% Qualität`);
    }

    // Empfehlungen basierend auf Durchschnittswerten
    if (averageQualityScore < 70) {
      recommendations.push('KI-Algorithmus benötigt Optimierung - Qualitätsscore unter 70%');
    }
    if (averageQualityScore > 85) {
      recommendations.push('KI-Auswahl zeigt sehr gute Ergebnisse - aktuelle Konfiguration beibehalten');
    }

    return {
      averageQualityScore,
      improvementAreas,
      successPatterns,
      recommendations
    };
  }

  /**
   * KI-basierte automatische Trommelauswahl
   * Analysiert die Bestellungen und wählt automatisch die optimalen Trommeln aus
   * 
   * @param orders - Liste der Schnittaufträge
   * @param constraints - Optionale Einschränkungen
   * @returns Promise mit KI-Trommelauswahl-Ergebnis
   */
  async selectOptimalDrumsAI(
    orders: CuttingOrder[], 
    constraints?: CuttingConstraints
  ): Promise<AITrommelauswahlResult> {
    const selections: AITrommelSelection[] = [];
    const verfügbareTrammeln: AvailableDrum[] = [];
    const warnungen: string[] = [];
    let totalConfidence = 0;
    let successCount = 0;

    try {
      // Gruppiere Bestellungen nach Kabeltyp (MATNR)
      const kabeltypenMap = new Map<string, CuttingOrder[]>();
      for (const order of orders) {
        const existing = kabeltypenMap.get(order.material || '') || [];
        existing.push(order);
        kabeltypenMap.set(order.material || '', existing);
      }

      // Für jeden Kabeltyp die optimale Trommel finden
      for (const [kabeltyp, kabelOrders] of Array.from(kabeltypenMap.entries())) {
        try {
          // Lade Materialdaten für den Kabeltyp
          const materialData = await TrommelrechnungService.findKabelmaterial(kabeltyp);
          if (!materialData) {
            warnungen.push(`Keine Materialdaten für Kabeltyp ${kabeltyp} gefunden`);
            continue;
          }

          // Berechne die maximale benötigte Länge für diesen Kabeltyp
          const maxBenötigteLänge = Math.max(...kabelOrders.map(order => order.requiredLength));
          const gesamtLänge = kabelOrders.reduce((sum, order) => sum + (order.requiredLength * order.quantity), 0);

          // Verwende TrommelrechnungService für die optimale Trommelauswahl
          const empfehlung = await TrommelrechnungService.findeOptimaleTrommel(
            materialData.Kabeldurchmesser,
            materialData.Zuschlag_Kabedurchmesser / 100, // Konvertiere Prozent zu Dezimal
            materialData.Bruttogewicht_pro_m,
            Math.max(maxBenötigteLänge, gesamtLänge), // Verwende die größere Länge
            0 // Führungsbogenhöhe standardmäßig 0
          );

          if (empfehlung) {
            // Berechne Confidence basierend auf verschiedenen Faktoren
            let confidence = 0.8; // Basis-Confidence
            
            // Erhöhe Confidence wenn Ring empfohlen wird (sehr sicher)
            if (empfehlung.trommelname === 'Ring') {
              confidence = 0.95;
            }
            
            // Reduziere Confidence wenn maximale Länge knapp ist
            const längenVerhältnis = maxBenötigteLänge / empfehlung.maximale_kabellänge_m;
            if (längenVerhältnis > 0.9) {
              confidence -= 0.2;
            } else if (längenVerhältnis > 0.7) {
              confidence -= 0.1;
            }

            // Erstelle AITrommelSelection
            const selection: AITrommelSelection = {
              kabeltyp,
              empfohlene_trommel: empfehlung.trommelname,
              grund: `${empfehlung.grund}. Benötigt: ${maxBenötigteLänge}m, Kapazität: ${empfehlung.maximale_kabellänge_m}m`,
              maximale_länge: empfehlung.maximale_kabellänge_m,
              confidence: Math.max(0.1, Math.min(1.0, confidence)),
              material_daten: materialData
            };

            selections.push(selection);
            totalConfidence += selection.confidence;
            successCount++;

            // Erstelle AvailableDrum für die Optimierung (außer bei Ring)
            if (empfehlung.trommelname !== 'Ring') {
              // Lade Trommeldaten
              const trommelData = await TrommelrechnungService.findTrommel(empfehlung.trommelname);
              if (trommelData) {
                const availableDrum: AvailableDrum = {
                  id: `ai-${kabeltyp}-${empfehlung.trommelname}`,
                  availableLength: empfehlung.maximale_kabellänge_m,
                  material: kabeltyp,
                  // Zusätzliche Eigenschaften aus Trommeldaten
                  diameter: trommelData.Außendurchmesser
                };
                verfügbareTrammeln.push(availableDrum);
              } else {
                warnungen.push(`Trommeldaten für ${empfehlung.trommelname} nicht gefunden`);
              }
            }

          } else {
            warnungen.push(`Keine geeignete Trommel für Kabeltyp ${kabeltyp} gefunden (Länge: ${maxBenötigteLänge}m)`);
          }

        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler';
          warnungen.push(`Fehler bei Trommelauswahl für ${kabeltyp}: ${errorMessage}`);
        }
      }

      // Berechne Gesamt-Confidence
      const gesamtConfidence = successCount > 0 ? totalConfidence / successCount : 0;

      // Füge allgemeine Empfehlungen hinzu
      if (selections.length === 0) {
        warnungen.push('Keine automatische Trommelauswahl möglich. Bitte wählen Sie manuell.');
      } else if (gesamtConfidence < 0.6) {
        warnungen.push('Niedrige Confidence bei der Trommelauswahl. Überprüfen Sie die Empfehlungen.');
      }

      return {
        selections,
        verfügbare_trommeln: verfügbareTrammeln,
        gesamt_confidence: gesamtConfidence,
        warnungen,
        erfolg: selections.length > 0
      };

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unbekannter Fehler';
      return {
        selections: [],
        verfügbare_trommeln: [],
        gesamt_confidence: 0,
        warnungen: [`Fehler bei KI-Trommelauswahl: ${errorMessage}`],
        erfolg: false
      };
    }
  }
}