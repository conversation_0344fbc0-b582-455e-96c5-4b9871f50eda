import React, { createContext, useContext, ReactNode, useEffect } from 'react';
import { useAuth, AuthState, User } from '@/hooks/useAuth';
import { setAuthErrorHandler } from '@/services/api.service';

interface AuthContextType extends AuthState {
  login: (username: string, password: string) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  trackActivity: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const auth = useAuth();

  // Register the logout function with the API service for automatic authentication error handling
  useEffect(() => {
    setAuthErrorHandler(auth.logout);
    
    // Cleanup function to remove the handler when component unmounts
    return () => {
      setAuthErrorHandler(() => {});
    };
  }, [auth.logout]);

  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuthContext = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};

export type { User, AuthState };