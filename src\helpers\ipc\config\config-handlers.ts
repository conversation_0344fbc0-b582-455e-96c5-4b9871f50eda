/**
 * Konfigurations-Handler für Main-Process
 * 
 * Verwaltet sichere Konfigurationswerte und stellt sie
 * dem Renderer-Process zur Verfügung.
 */

import { ipcMain, BrowserWindow } from 'electron';
import { CONFIG_CHANNELS, AppConfig } from './config-context';

/**
 * Aktuelle Anwendungskonfiguration
 */
let currentConfig: AppConfig = {
  apiKey: 'sfm_api_a7c4f2e8b9d6c3a1f5e9b2d7c4a8f6e3b1d9c5a7f2e8b4d6c9a3f7e1b5d2c8a4f6e9b3d7c1a5f8e2b6d4c',
  apiBaseUrl: 'http://localhost:3001/api',
  environment: 'development',
  version: '1.0.0'
};

/**
 * Initialisiert die Konfiguration mit sicheren Werten
 * @param config Initiale Konfiguration
 */
export function initializeConfig(config: Partial<AppConfig>): void {
  currentConfig = {
    ...currentConfig,
    ...config
  };
  
  console.log('🔧 Konfiguration initialisiert:', {
    apiBaseUrl: currentConfig.apiBaseUrl,
    environment: currentConfig.environment,
    version: currentConfig.version,
    hasApiKey: !!currentConfig.apiKey
  });
}

/**
 * Aktualisiert die Konfiguration und benachrichtigt alle Renderer
 * @param updates Konfigurationsaktualisierungen
 * @param mainWindow Hauptfenster für Benachrichtigungen
 */
export function updateConfig(updates: Partial<AppConfig>, mainWindow?: BrowserWindow): void {
  const oldConfig = { ...currentConfig };
  currentConfig = { ...currentConfig, ...updates };
  
  console.log('🔄 Konfiguration aktualisiert:', updates);
  
  // Benachrichtige alle Renderer über die Änderung
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.send(CONFIG_CHANNELS.CONFIG_UPDATED, currentConfig);
  }
}

/**
 * Registriert IPC-Handler für Konfiguration
 */
export function registerConfigHandlers(): void {
  console.log('📋 Registriere Konfigurations-Handler...');

  // Handler für Konfigurationsabruf
  ipcMain.handle(CONFIG_CHANNELS.GET_CONFIG, async (): Promise<AppConfig> => {
    console.log('[CONFIG] Konfiguration angefordert');
    return currentConfig;
  });

  console.log('✅ Konfigurations-Handler registriert');
}

/**
 * Gibt die aktuelle Konfiguration zurück
 */
export function getCurrentConfig(): AppConfig {
  return { ...currentConfig };
}