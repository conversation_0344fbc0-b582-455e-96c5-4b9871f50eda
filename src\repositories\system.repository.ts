/**
 * System Repository
 * 
 * Repository für System-Daten (Service Level, Performance, System Stats)
 * Implementiert das Repository Pattern für System-Monitoring und Performance-Daten.
 */

import { BaseRepository, DateRangeFilter } from './base.repository';
import apiService from '@/services/api.service';
import { 
  ServiceLevelDataPoint,
  DailyPerformanceDataPoint,
  PickingDataPoint,
  ReturnsDataPoint,
  DeliveryPositionsDataPoint,
  TagesleistungDataPoint 
} from '../types/database';

/**
 * Filter für System-Daten
 */
export interface SystemFilter extends DateRangeFilter {
  systemType?: 'performance' | 'service-level' | 'picking' | 'returns' | 'delivery';
  minServiceLevel?: number;
  maxServiceLevel?: number;
}

/**
 * Service Level Repository
 */
export class ServiceLevelRepository extends BaseRepository<ServiceLevelDataPoint, SystemFilter> {
  protected repositoryName = 'service-level';
  
  async getAll(filter?: SystemFilter): Promise<ServiceLevelDataPoint[]> {
    const data = await apiService.getServiceLevelData();
    
    if (filter?.minServiceLevel !== undefined) {
      return data.filter(item => 
        ((item as any).servicegrad || 0) >= filter.minServiceLevel!
      );
    }
    
    if (filter?.maxServiceLevel !== undefined) {
      return data.filter(item => 
        ((item as any).servicegrad || 0) <= filter.maxServiceLevel!
      );
    }
    
    return data;
  }
  
  /**
   * Aktueller Service Level (letzter verfügbarer Wert)
   */
  async getCurrentServiceLevel(): Promise<ServiceLevelDataPoint | null> {
    const data = await this.getAll();
    
    if (!data || data.length === 0) return null;
    
    return data.sort((a, b) => {
      const dateA = new Date(a.datum || '');
      const dateB = new Date(b.datum || '');
      return dateB.getTime() - dateA.getTime();
    })[0];
  }
  
  /**
   * Service Level Statistiken berechnen
   */
  async getServiceLevelStats(filter?: SystemFilter): Promise<{
    average: number;
    current: number;
    trend: 'improving' | 'declining' | 'stable';
    aboveTarget: number;
    belowTarget: number;
    targetPercentage: number;
  }> {
    const data = await this.getAll(filter);
    const TARGET_SERVICE_LEVEL = 95; // 95% Service Level Ziel
    
    if (!data || data.length === 0) {
      return {
        average: 0,
        current: 0,
        trend: 'stable',
        aboveTarget: 0,
        belowTarget: 0,
        targetPercentage: 0
      };
    }
    
    const serviceLevels = data.map(item => (item as any).servicegrad || 0);
    const average = serviceLevels.reduce((sum, val) => sum + val, 0) / serviceLevels.length;
    
    const current = await this.getCurrentServiceLevel();
    const currentLevel = (current as any)?.servicegrad || 0;
    
    // Trend-Analyse (letzten 5 Werte)
    const recentValues = serviceLevels.slice(-5);
    const firstRecent = recentValues[0] || 0;
    const lastRecent = recentValues[recentValues.length - 1] || 0;
    const change = lastRecent - firstRecent;
    
    let trend: 'improving' | 'declining' | 'stable' = 'stable';
    if (change > 2) trend = 'improving';
    else if (change < -2) trend = 'declining';
    
    const aboveTarget = serviceLevels.filter(level => level >= TARGET_SERVICE_LEVEL).length;
    const belowTarget = serviceLevels.filter(level => level < TARGET_SERVICE_LEVEL).length;
    const targetPercentage = (aboveTarget / serviceLevels.length) * 100;
    
    return {
      average,
      current: currentLevel,
      trend,
      aboveTarget,
      belowTarget,
      targetPercentage
    };
  }
}

/**
 * Daily Performance Repository
 */
export class DailyPerformanceRepository extends BaseRepository<DailyPerformanceDataPoint, DateRangeFilter> {
  protected repositoryName = 'daily-performance';
  
  async getAll(filter?: DateRangeFilter): Promise<DailyPerformanceDataPoint[]> {
    return await apiService.getDailyPerformanceData();
  }
  
  /**
   * Performance-Trend über Zeit analysieren
   */
  async getPerformanceTrend(days: number = 30): Promise<{
    trend: 'improving' | 'declining' | 'stable';
    averageDaily: number;
    bestDay: DailyPerformanceDataPoint | null;
    worstDay: DailyPerformanceDataPoint | null;
    consistency: number;
  }> {
    const data = await this.getAll();
    const recentData = data.slice(-days);
    
    if (recentData.length === 0) {
      return {
        trend: 'stable',
        averageDaily: 0,
        bestDay: null,
        worstDay: null,
        consistency: 0
      };
    }
    
    const performances = recentData.map(item => 
      (item as any).produzierte_tonnagen || (item as any).ausgeliefert_lup || 0
    );
    
    const averageDaily = performances.reduce((sum, val) => sum + val, 0) / performances.length;
    
    // Trend berechnen
    const firstHalf = performances.slice(0, Math.floor(performances.length / 2));
    const secondHalf = performances.slice(Math.floor(performances.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    
    let trend: 'improving' | 'declining' | 'stable' = 'stable';
    const change = ((secondAvg - firstAvg) / firstAvg) * 100;
    if (change > 5) trend = 'improving';
    else if (change < -5) trend = 'declining';
    
    // Beste und schlechteste Tage
    const bestDay = recentData.reduce((best, current) => 
      ((current as any).produzierte_tonnagen || 0) > ((best as any).produzierte_tonnagen || 0) ? current : best
    );
    
    const worstDay = recentData.reduce((worst, current) => 
      ((current as any).produzierte_tonnagen || 0) < ((worst as any).produzierte_tonnagen || 0) ? current : worst
    );
    
    // Konsistenz (Standardabweichung)
    const variance = performances.reduce((sum, val) => 
      sum + Math.pow(val - averageDaily, 2), 0
    ) / performances.length;
    const consistency = Math.sqrt(variance);
    
    return {
      trend,
      averageDaily,
      bestDay,
      worstDay,
      consistency
    };
  }
}

/**
 * Picking Repository
 */
export class PickingRepository extends BaseRepository<PickingDataPoint, DateRangeFilter> {
  protected repositoryName = 'picking';
  
  async getAll(): Promise<PickingDataPoint[]> {
    return await apiService.getPickingData();
  }
  
  /**
   * Picking-Effizienz analysieren
   */
  async getPickingEfficiency(): Promise<{
    averagePicksPerDay: number;
    totalPicks: number;
    efficiency: number;
    trends: { date: string; picks: number }[];
  }> {
    const data = await this.getAll();
    
    if (data.length === 0) {
      return {
        averagePicksPerDay: 0,
        totalPicks: 0,
        efficiency: 0,
        trends: []
      };
    }
    
    const totalPicks = data.reduce((sum, item) => sum + (item.picks || 0), 0);
    const averagePicksPerDay = totalPicks / data.length;
    
    // Effizienz basierend auf Ziel-KPI (z.B. 1000 Picks pro Tag)
    const TARGET_PICKS_PER_DAY = 1000;
    const efficiency = (averagePicksPerDay / TARGET_PICKS_PER_DAY) * 100;
    
    const trends = data.map(item => ({
      date: item.datum || '',
      picks: item.picks || 0
    }));
    
    return {
      averagePicksPerDay,
      totalPicks,
      efficiency,
      trends
    };
  }
}

/**
 * Returns Repository
 */
export class ReturnsRepository extends BaseRepository<ReturnsDataPoint, DateRangeFilter> {
  protected repositoryName = 'returns';
  
  async getAll(): Promise<ReturnsDataPoint[]> {
    return await apiService.getReturnsData();
  }
  
  /**
   * Retourenanalyse
   */
  async getReturnsAnalysis(): Promise<{
    totalReturns: number;
    averageReturnsPerDay: number;
    returnRate: number;
    trends: { date: string; returns: number }[];
    categories: Record<string, number>;
  }> {
    const data = await this.getAll();
    
    if (data.length === 0) {
      return {
        totalReturns: 0,
        averageReturnsPerDay: 0,
        returnRate: 0,
        trends: [],
        categories: {}
      };
    }
    
    const totalReturns = data.reduce((sum, item) => sum + (item.anzahl || 0), 0);
    const averageReturnsPerDay = totalReturns / data.length;
    
    // Return Rate (Annahme: 5% ist normal)
    const returnRate = (totalReturns / (totalReturns + 10000)) * 100; // Beispiel-Berechnung
    
    const trends = data.map(item => ({
      date: item.datum || '',
      returns: item.anzahl || 0
    }));
    
    // Kategorien-Analyse (falls verfügbar)
    const categories = data.reduce((cats, item) => {
      const category = item.kategorie || 'Unbekannt';
      cats[category] = (cats[category] || 0) + (item.anzahl || 0);
      return cats;
    }, {} as Record<string, number>);
    
    return {
      totalReturns,
      averageReturnsPerDay,
      returnRate,
      trends,
      categories
    };
  }
}

/**
 * Delivery Positions Repository
 */
export class DeliveryPositionsRepository extends BaseRepository<DeliveryPositionsDataPoint, DateRangeFilter> {
  protected repositoryName = 'delivery-positions';
  
  async getAll(): Promise<DeliveryPositionsDataPoint[]> {
    return await apiService.getDeliveryPositionsData();
  }
  
  /**
   * Lieferpositions-Analyse
   */
  async getDeliveryAnalysis(): Promise<{
    totalPositions: number;
    averagePositionsPerDay: number;
    deliveryEfficiency: number;
    onTimeDeliveries: number;
    delays: number;
  }> {
    const data = await this.getAll();
    
    if (data.length === 0) {
      return {
        totalPositions: 0,
        averagePositionsPerDay: 0,
        deliveryEfficiency: 0,
        onTimeDeliveries: 0,
        delays: 0
      };
    }
    
    const totalPositions = data.reduce((sum, item) => sum + (item.positionen || 0), 0);
    const averagePositionsPerDay = totalPositions / data.length;
    
    // Liefereffizienz-Berechnung (Beispiel)
    const onTimeDeliveries = data.filter(item => 
      (item.status || '').toLowerCase() === 'pünktlich'
    ).length;
    
    const delays = data.length - onTimeDeliveries;
    const deliveryEfficiency = (onTimeDeliveries / data.length) * 100;
    
    return {
      totalPositions,
      averagePositionsPerDay,
      deliveryEfficiency,
      onTimeDeliveries,
      delays
    };
  }
}

/**
 * Tagesleistung Repository
 */
export class TagesleistungRepository extends BaseRepository<TagesleistungDataPoint, DateRangeFilter> {
  protected repositoryName = 'tagesleistung';
  
  async getAll(): Promise<TagesleistungDataPoint[]> {
    return await apiService.getTagesleistungData();
  }
  
  /**
   * Tagesleistungs-Statistiken
   */
  async getDailyPerformanceStats(): Promise<{
    averageDaily: number;
    bestPerformance: TagesleistungDataPoint | null;
    worstPerformance: TagesleistungDataPoint | null;
    targetAchievement: number;
    consistency: number;
  }> {
    const data = await this.getAll();
    
    if (data.length === 0) {
      return {
        averageDaily: 0,
        bestPerformance: null,
        worstPerformance: null,
        targetAchievement: 0,
        consistency: 0
      };
    }
    
    const performances = data.map(item => item.leistung || 0);
    const averageDaily = performances.reduce((sum, val) => sum + val, 0) / performances.length;
    
    const bestPerformance = data.reduce((best, current) => 
      (current.leistung || 0) > (best.leistung || 0) ? current : best
    );
    
    const worstPerformance = data.reduce((worst, current) => 
      (current.leistung || 0) < (worst.leistung || 0) ? current : worst
    );
    
    // Ziel-Erreichung (Annahme: 100 ist das Tagesziel)
    const TARGET_DAILY = 100;
    const targetAchievement = (averageDaily / TARGET_DAILY) * 100;
    
    // Konsistenz
    const variance = performances.reduce((sum, val) => 
      sum + Math.pow(val - averageDaily, 2), 0
    ) / performances.length;
    const consistency = Math.sqrt(variance);
    
    return {
      averageDaily,
      bestPerformance,
      worstPerformance,
      targetAchievement,
      consistency
    };
  }
}

/**
 * System Stats Repository
 */
export class SystemStatsRepository extends BaseRepository<any, DateRangeFilter> {
  protected repositoryName = 'system-stats';
  
  async getAll(filter?: DateRangeFilter): Promise<any> {
    return await apiService.getSystemStats(filter?.startDate, filter?.endDate);
  }
  
  /**
   * System-Health-Dashboard erstellen
   */
  async getSystemHealthDashboard(): Promise<{
    overall: 'healthy' | 'warning' | 'critical';
    components: Record<string, { status: string; value: number }>;
    recommendations: string[];
  }> {
    const stats = await this.getAll();
    
    if (!stats) {
      return {
        overall: 'critical',
        components: {},
        recommendations: ['System-Daten nicht verfügbar']
      };
    }
    
    // Beispiel-Health-Check-Logik
    const components = {
      serviceLevel: { 
        status: stats.serviceLevel > 95 ? 'healthy' : stats.serviceLevel > 85 ? 'warning' : 'critical',
        value: stats.serviceLevel || 0
      },
      performance: {
        status: stats.performance > 90 ? 'healthy' : stats.performance > 70 ? 'warning' : 'critical',
        value: stats.performance || 0
      },
      availability: {
        status: stats.availability > 99 ? 'healthy' : stats.availability > 95 ? 'warning' : 'critical',
        value: stats.availability || 0
      }
    };
    
    const criticalCount = Object.values(components).filter(c => c.status === 'critical').length;
    const warningCount = Object.values(components).filter(c => c.status === 'warning').length;
    
    let overall: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (criticalCount > 0) overall = 'critical';
    else if (warningCount > 0) overall = 'warning';
    
    const recommendations: string[] = [];
    if (components.serviceLevel.status !== 'healthy') {
      recommendations.push('Service Level unter Zielwert - Prozesse überprüfen');
    }
    if (components.performance.status !== 'healthy') {
      recommendations.push('Performance-Optimierung erforderlich');
    }
    if (components.availability.status !== 'healthy') {
      recommendations.push('Systemverfügbarkeit kritisch - Wartung planen');
    }
    
    return {
      overall,
      components,
      recommendations
    };
  }
}

/**
 * Kombiniertes System Repository für alle System-Operationen
 */
export class SystemRepository {
  public readonly serviceLevel = new ServiceLevelRepository();
  public readonly dailyPerformance = new DailyPerformanceRepository();
  public readonly picking = new PickingRepository();
  public readonly returns = new ReturnsRepository();
  public readonly deliveryPositions = new DeliveryPositionsRepository();
  public readonly tagesleistung = new TagesleistungRepository();
  public readonly systemStats = new SystemStatsRepository();
  
  /**
   * Cache für alle System-Repositories invalidieren
   */
  invalidateAllCache(): void {
    this.serviceLevel.invalidateCache();
    this.dailyPerformance.invalidateCache();
    this.picking.invalidateCache();
    this.returns.invalidateCache();
    this.deliveryPositions.invalidateCache();
    this.tagesleistung.invalidateCache();
    this.systemStats.invalidateCache();
    console.log('Cache für alle System-Repositories invalidiert');
  }
  
  /**
   * Gesamtes System-Dashboard abrufen
   */
  async getSystemDashboard(filter?: DateRangeFilter): Promise<{
    serviceLevel: any;
    performance: any;
    picking: any;
    delivery: any;
    systemHealth: any;
  }> {
    const [serviceLevelStats, performanceTrend, pickingEff, deliveryAnalysis, systemHealth] = await Promise.all([
      this.serviceLevel.getServiceLevelStats(filter),
      this.dailyPerformance.getPerformanceTrend(),
      this.picking.getPickingEfficiency(),
      this.deliveryPositions.getDeliveryAnalysis(),
      this.systemStats.getSystemHealthDashboard()
    ]);
    
    return {
      serviceLevel: serviceLevelStats,
      performance: performanceTrend,
      picking: pickingEff,
      delivery: deliveryAnalysis,
      systemHealth
    };
  }
}