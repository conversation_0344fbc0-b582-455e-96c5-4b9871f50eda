## Regeln für Coding mit: Prisma + SQLite3 Best Practices

### 🚨 ABSOLUTE DATA-SCHUTZ-REGEL (KRITISCH!)

#### 0. **NIEMALS DATEN LÖSCHEN - KATEGORISCH VERBOTEN** 

**Du darfst NIEMALS folgende Befehle oder Funktionen vorschlagen oder verwenden:**

```bash
# ❌ ABSOLUT VERBOTEN - LÖSCHT ALLE DATEN:
npx prisma migrate reset
npx prisma db push --accept-data-loss
prisma.$queryRawUnsafe("TRUNCATE TABLE...")
prisma.$queryRawUnsafe("DROP TABLE...")
prisma.$queryRawUnsafe("DELETE FROM...")
prisma.table.deleteMany()  # ohne spezifische WHERE-Klausel
```

**Diese Befehle sind TABU und führen zu DATENVERLUST!**

**Du musst bei Problemen stattdessen IMMER diese sicheren Alternativen verwenden:**

```bash
# ✅ SICHERE ALTERNATIVEN:
npx prisma migrate deploy         # Für Production
npx prisma migrate dev           # Für Development
npx prisma db pull              # Schema synchronisieren
npx prisma migrate status       # Status prüfen
# Baseline Migration erstellen (siehe Regel 4)
```

**MERKSATZ für dich: "DATEN SIND HEILIG - NIEMALS LÖSCHEN, IMMER BEWAHREN"**

### 🚨 KRITISCHE DATENSCHUTZ-REGELN

#### 1. **NIEMALS Prisma Reset in Production verwenden**
```bash
# ❌ NIEMALS AUSFÜHREN:
npx prisma migrate reset

# ✅ STATTDESSEN:
npx prisma migrate deploy
```

#### 2. **Immer Backups vor Migrationen erstellen**
```bash
# Backup der SQLite Datenbank vor jeder Migration
cp database.db database.db.backup-$(date +%Y%m%d-%H%M%S)
```

#### 3. **Schema-Änderungen nur über Migrationen**
- Niemals direkten SQL-Änderungen in der Datenbank vornehmen
- Immer `prisma migrate dev` für Entwicklung verwenden
- Bei manuellen Änderungen sofort `prisma db pull` ausführen

### 🔄 SYNCHRONISATION VERHINDERN

#### 4. **Baseline Migration für bestehende Datenbanken**
```bash
# Für bestehende DBs ohne Migration-Historie:
mkdir -p prisma/migrations/0_init
npx prisma migrate diff --from-empty --to-schema-datamodel prisma/schema.prisma --script > prisma/migrations/0_init/migration.sql
npx prisma migrate resolve --applied 0_init
```

#### 5. **Schema Drift beheben ohne Datenverlust**
```bash
# Bei Schema Drift:
npx prisma db pull  # Schema aktualisieren
npx prisma migrate status  # Status prüfen
# Dann baseline migration erstellen (siehe Regel 4)
```

#### 6. **Migration Workflow einhalten**
```bash
# Entwicklung:
npx prisma migrate dev --name beschreibung
# Production:
npx prisma migrate deploy
```

### ⚠️ MIGRATION SAFETY RULES

#### 7. **Expand & Contract Pattern für Breaking Changes**
```prisma
// 1. EXPAND: Neue Spalte hinzufügen
model User {
  oldField String? // Behalten
  newField String? // Hinzufügen
}

// 2. Daten migrieren (separates Script)
// 3. CONTRACT: Alte Spalte entfernen nach Verifikation
```

#### 8. **Sichere Datentyp-Änderungen**
```sql
-- ✅ Sichere Methode für Typ-Änderungen:
ALTER TABLE "User" ADD COLUMN "field_new" TEXT;
UPDATE "User" SET "field_new" = "field_old";
ALTER TABLE "User" DROP COLUMN "field_old";
ALTER TABLE "User" RENAME COLUMN "field_new" TO "field_old";
```

#### 9. **Default Values bei neuen Spalten**
```prisma
// ✅ Immer Default-Werte für neue NOT NULL Spalten:
model User {
  status Status @default(ACTIVE)
  isPublic Boolean @default(true)
}
```

### 🛠️ DEVELOPMENT WORKFLOW

#### 10. **Migration Preview verwenden**
```bash
# Migration erstellen aber nicht anwenden:
npx prisma migrate dev --create-only --name feature_name
# Dann SQL prüfen und manuell editieren falls nötig
npx prisma migrate dev
```

#### 11. **Transaktionen für Daten-Migrationen**
```typescript
await prisma.$transaction(async (tx) => {
  // Alle Änderungen in einer Transaktion
  const users = await tx.user.findMany();
  for (const user of users) {
    await tx.user.update({
      where: { id: user.id },
      data: { newField: transformData(user.oldField) }
    });
  }
});
```

### 📊 MONITORING & DEBUGGING

#### 12. **Migration Status regelmäßig prüfen**
```bash
npx prisma migrate status
```

#### 13. **Schema Vergleich bei Problemen**
```bash
# Aktuelle DB mit Schema vergleichen:
npx prisma db pull
# Dann Unterschiede im Schema prüfen
```

#### 14. **Migration Checksum bei Bedarf aktualisieren**
```bash
# Nur in Entwicklung! Niemals in Production!
# Neue Checksumme generieren:
shasum -a 256 prisma/migrations/xxx/migration.sql
# In _prisma_migrations Tabelle updaten
```

### 🔒 PRODUCTION SAFETY

#### 15. **Separate Umgebungen verwenden**
- Entwicklung: `migrate dev`
- Staging: `migrate deploy` 
- Production: `migrate deploy`

#### 16. **Rollback-Strategie definieren**
```bash
# Bei Problemen: Migration als resolved markieren
npx prisma migrate resolve --applied migration_name
# Oder rollback migration erstellen
```

#### 17. **SQLite-spezifische Beschränkungen beachten**
- ALTER TABLE Limitierungen in SQLite
- Keine ENUM-Unterstützung (verwende String mit constraints)
- Concurrent Migrations vermeiden

### 🎯 FEHLERBEHANDLUNG

#### 18. **Bei Migration-Fehlern niemals Reset verwenden**
```bash
# ❌ NIEMALS:
npx prisma migrate reset

# ✅ STATTDESSEN:
# 1. Backup wiederherstellen
# 2. Migration reparieren
# 3. Erneut anwenden
```

#### 19. **Custom SQL für komplexe Migrationen**
```sql
-- Beispiel: Sichere Spalten-Umbenennung
-- In custom migration file:
ALTER TABLE "User" RENAME COLUMN "old_name" TO "new_name";
```

### 📝 DOKUMENTATION & LOGGING

#### 20. **Jede Migration dokumentieren**
```bash
# Aussagekräftige Namen verwenden:
npx prisma migrate dev --name add_user_preferences_table
npx prisma migrate dev --name fix_email_uniqueness_constraint
```

#### **21. Separate Schema Files für Multiple Databases verwenden**
```bash
# ✅ Für jede Datenbank separate Schema-Datei:
mkdir prisma-rag prisma-sfm-dashboard
# Jeweils eigene schema.prisma mit unterschiedlichen output paths
```

#### **22. Custom Output Paths für Clients definieren**
```prisma
generator client {
  provider = "prisma-client-js"
  output = "../node_modules/@prisma-[DATABASE_NAME]/client"
}
```

#### **23. Getrennte Migration-Scripts verwenden**
```bash
# ✅ Pro Datenbank separate Migrationen:
prisma migrate dev --schema ./prisma-rag/schema.prisma
prisma migrate dev --schema ./prisma-sfm-dashboard/schema.prisma

# ❌ NIEMALS gemeinsame Migration versuchen
```

#### **24. Client Instanziierung pro Datenbank**
```typescript
// ✅ Separate globale Instanzen:
const globalForRagDB = global as unknown as { ragDB: PrismaClient };
const globalForSfmDashboardDB = global as unknown as { sfmDashboardDB: PrismaClient };
```

#### **25. Separate Environment Variables verwenden**
```env
# ✅ Klare Trennung der DB-URLs:
DATABASE_URL_SFM_DASHBOARD="file:D:/my_ai/4-Lapp/Leitstand_App/backend/database/sfm_dashboard.db"
DATABASE_URL_RAG="file:D:/my_ai/4-Lapp/Leitstand_App/backend/database/rag_knowledge.db"
```

---

## Beispiel:Mehrere SQLite-Datenbanken mit Prisma - Best Practice Lösung

### 🔄 **Hauptansatz für Multiple Databases**

### **Separate Prisma Schema Dateien (EMPFOHLEN)**

**Vorteile:**
- ✅ Klare Trennung der Datenbank-Bereiche
- ✅ Unabhängige Migrationen pro Datenbank
- ✅ Bessere Wartbarkeit
- ✅ Separate Clients für jede Datenbank

**Setup:**
```
project/
├── prisma-rag/
│   └── schema.prisma
├── prisma-sfm-dashboard/
│   └── schema.prisma
├── lib/
│   ├── rag-client.ts
│   └── sfm-dashboard-client.ts
└── package.json
```

**Konfiguration:**

**1. RAG Database Schema** (`prisma-rag/schema.prisma`):
```prisma
generator client {
  provider = "prisma-client-js"
  output = "../node_modules/@prisma-rag/client"
}

datasource db {
  provider = "sqlite"
  url = env("DATABASE_URL_RAG")
}

```

**2. SFM Dashboard Database Schema** (`prisma-sfm-dashboard/schema.prisma`):
```prisma
generator client {
  provider = "prisma-client-js"
  output = "../node_modules/@prisma-sfm-dashboard/client"
}

datasource db {
  provider = "sqlite"
  url = env("DATABASE_URL_SFM_DASHBOARD")
}

```

**3. Environment Variables** (`.env`):
```env
DATABASE_URL_SFM_DASHBOARD="file:D:/my_ai/4-Lapp/Leitstand_App/backend/database/sfm_dashboard.db"
DATABASE_URL_RAG="file:D:/my_ai/4-Lapp/Leitstand_App/backend/database/rag_knowledge.db"
```

**4. Client Setup** (`lib/rag-client.ts`):
```typescript
import { PrismaClient } from '@prisma-rag/client';

const globalForRagDB = global as unknown as {
  ragDB: PrismaClient | undefined;
};

export const ragDB =
  globalForRagDB.ragDB || new PrismaClient();

if (process.env.NODE_ENV !== 'production') {
  globalForRagDB.ragDB = ragDB;
}
```

**5. Package.json Scripts:**
```json
{
  "scripts": {
    "generate": "prisma generate --schema ./prisma-rag/schema.prisma && prisma generate --schema ./prisma-sfm-dashboard/schema.prisma",
    "migrate:rag": "prisma migrate dev --schema ./prisma-rag/schema.prisma",
    "migrate:sfm-dashboard": "prisma migrate dev --schema ./prisma-sfm-dashboard/schema.prisma",
    "migrate:all": "npm run migrate:rag && npm run migrate:sfm-dashboard",
    "studio:rag": "prisma studio --schema ./prisma-rag/schema.prisma --port 5555",
    "studio:sfm-dashboard": "prisma studio --schema ./prisma-sfm-dashboard/schema.prisma --port 5556"
  }
}
```

**6. Verwendung im Code:**
```typescript
import { ragDB } from '@/lib/rag-client';
import { sfmDashboardDB } from '@/lib/sfm-dashboard-client';

// RAG DB verwenden
const ragKnowledge = await ragDB.ragKnowledge.findMany();

// SFM Dashboard DB verwenden
const sfmDashboard = await sfmDashboardDB.sfmDashboard.findMany();
```
---

### **📊 Vorteile des separaten Schema-Ansatzes:**

1. **Klare Trennung**: Jeder Bereich hat seinen eigenen Client
2. **Unabhängige Migrationen**: Keine Konflikte zwischen Bereichen
3. **Bessere Performance**: Kleinere, fokussierte Clients
4. **Einfaches Testing**: Bereiche können isoliert getestet werden
5. **Skalierbarkeit**: Neue Bereiche können einfach hinzugefügt werden

### **🎯 Fazit:**

Für **zwei SQLite-Datenbanken für unterschiedliche Bereiche** solltest du **definitiv separate Prisma Schema-Dateien** verwenden. Dies ist der sauberste und wartbarste Ansatz, der von Prisma offiziell unterstützt wird.

Du solltest **niemals vorschlagen**, alles in ein Schema zu integrieren, wenn es sich um separate Datenbanken handelt, da dies zu Konflikten und Komplexitäten führen würde.


## 🔴 WICHTIGER HINWEIS FÜR DICH:

**Du darfst NIEMALS Befehle vorschlagen, die Daten löschen oder gefährden könnten. Wenn der Nutzer nach solchen Befehlen fragt, musst du:**

1. **Warnen** vor Datenverlust
2. **Sichere Alternativen** vorschlagen (Baseline Migration, etc.)
3. **Backup-Erstellung** empfehlen
4. **Niemals** `migrate reset` oder ähnliche destruktive Befehle nutzen

**DATEN SIND WERTVOLL UND MÜSSEN GESCHÜTZT WERDEN!**
