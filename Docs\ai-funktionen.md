# **Funktionsbeschreibung – AI-Modul (`/modules/ai`)**

Das **AI-Modul** ist das zentrale KI-Element der App. Es soll sowohl **interaktiv** (Chatbot) als auch **automatisiert** (Analyse, Optimierung, Vorhersagen) arbeiten.  
Die Funktionen sind in **bereits funktionierende** und **geplante** Features unterteilt.

---

## **1. AI-Chatbot**
**Ziel:**  
Ein intelligenter, kontextsensitiver Chatbot, der sowohl allgemeine Fragen als auch spezifische Anfragen zu Unternehmensdaten beantworten kann.

**Funktionen:**
- **[<PERSON><PERSON><PERSON>]** Direkte Integration in die App (UI-Element für Chat).
- **[Fertig]** Zugriff auf Datenbank und Kennzahlen (z. B. Lagerbestände, KPIs).
- **[Geplant]** Automatische Vorschläge basierend auf erkannten Mustern in den Daten.
- **[<PERSON><PERSON><PERSON>]** **RAG-Integration** (Retrieval-Augmented Generation) für präzisere Antworten durch dynamisches Laden relevanter Daten aus der Datenbank.

**Technische Hinweise für Entwickler:**
- Nutze den standard API-Endpunkt für Chat-Requests mit OpenRuter so wie der standard in diesen repositorie (`/src/components/modules/ai/components`).
- Kontextverwaltung (Session Memory) für fortlaufende Gespräche.
- Erstelle eine neue Vektor-Datenbank für die RAG-Integration (SQLite3).

---

## **2. Optimale Schneidplanung (CuttingPage)**
**Ziel:**  
Automatisierte Berechnung optimaler Schnittmuster für Kabeltrommeln, um Materialverschwendung zu minimieren.

**Funktionen:**
- **Bin-Packing-Algorithmus** zur maximalen Trommelausnutzung.
- Berücksichtigung von:
  - Trommelrechnung Formel für Maximale länge auf Kabeltrommel **(Entwicklung erstmal als Placeholder, die Formel gebe ich danach)**
  - Kabeltypen und -längen **(Entwicklung erstmal als Placeholder, die Kabeldaten gebe ich danach)**
  - Verfügbaren Restlängen
  - Auftragsprioritäten
- Automatische Vorschläge für optimale Schnittmuster.

**Technische Hinweise:**
- Algorithmus-Implementierung in separatem Service (`/src/components/modules/ai/components/cutting`).
- Eingabe: Auftragsliste, Lagerbestand, Restlängen.
- Ausgabe: Schnittplan als JSON + Visualisierung.

---

## **3. Intelligente Bestandsführung**
**Ziel:**  
KI-gestützte Verwaltung der Lagerbestände mit automatischen Bestellvorschlägen.

**Funktionen:**
- **ABC-Analyse 2.0** mit dynamischer Klassifizierung (Anpassung an Nachfrageänderungen).
- Automatische Vorschläge für Bestellmengen und -zeitpunkte.
- Vorhersage von Bestandsengpässen.

**Technische Hinweise:**
- Zeitreihenanalyse (z. B. Prophet, ARIMA).
- API-Endpunkt `/src/components/modules/ai/components/inventory`.
- Integration mit ERP-System.

---

## **4. Lageroptimierung**
**Ziel:**  
Optimale Platzierung von Artikeln im Lager für schnellere Kommissionierung.

**Funktionen:**
- Dynamische Lagerplatzvergabe basierend auf:
  - Zugriffshäufigkeit
  - Artikelverwandtschaft
  - Gewicht und Größe
- Optimale Kommissionierwege berechnen.

**Technische Hinweise:**
- Graph-Algorithmus für Wegoptimierung (z. B. Dijkstra, A*).
- Datenquelle: Lagerlayout + Bewegungsdaten.

---

## **5. Bewegungsdaten-Analyse**
**Ziel:**  
Erkennung von ineffizienten oder ungewöhnlichen Lagerbewegungen.

**Funktionen:**
- Anomalie-Erkennung bei Lagerbewegungen.
- Automatische Erkennung von:
  - Ungewöhnlichen Mustern
  - Häufigen Umlagerungen
  - Prozessineffizienzen

**Technische Hinweise:**
- Machine-Learning-Modelle für Anomalieerkennung (Isolation Forest, DBSCAN).
- Visualisierung in Dashboard.

---

## **6. Prozessoptimierung**
**Ziel:**  
Simulation und Verbesserung von Prozessen.

**Funktionen:**
- Simulation von Prozessänderungen.
- Engpass-Identifikation.
- Automatisierte Verbesserungsvorschläge.

**Technische Hinweise:**
- Event-Simulation (Discrete Event Simulation).
- API `/src/components/modules/ai/components/process`.

---

## **7. Echtzeit-Kennzahlenanalyse**
**Ziel:**  
Vorhersage und Überwachung wichtiger KPIs in Echtzeit.

**Funktionen:**
- Predictive Analytics für:
  - Lagerbestände
  - Auslastung
  - Kapazitätsplanung
- Automatische Warnungen bei kritischen Werten.

**Technische Hinweise:**
- Streaming-Datenverarbeitung (z. B. Kafka, WebSockets).
- Schwellenwert-Trigger.

---

## **8. Ressourcenoptimierung**
**Ziel:**  
Effiziente Nutzung von Maschinen und Personal.

**Funktionen:**
- Vorhersage der Maschinenauslastung.
- Optimale Auftragsreihenfolge.

**Technische Hinweise:**
- Scheduling-Algorithmen (z. B. Job-Shop Scheduling).
- Integration mit Produktionsplanungssystem.

---

## **9. Lieferketten-Optimierung**
**Ziel:**  
Minimierung von Lieferzeiten und Risiken.

**Funktionen:**
- Vorhersage von Lieferzeiten.
- Risikoanalyse bei Verzögerungen.
- Bewertung alternativer Lieferanten.

**Technische Hinweise:**
- Externe API-Integration (Lieferanten, Logistik).
- ML-Modelle für Risikoanalyse.

---

## **10. Berichtswesen**
**Ziel:**  
Automatisierte Erstellung von Berichten und Handlungsempfehlungen.

**Funktionen:**
- Automatische KPI-Berichte.
- Zusammenfassung wichtiger Kennzahlen.
- Handlungsempfehlungen.

**Technische Hinweise:**
- PDF/Excel-Export.
- Zeitgesteuerte Generierung (Cronjobs).
 
---

# **Funktionen – AI-Modul (`/src/components/modules/ai/components`)**
## **1. Priorisierte Roadmap – AI-Modul**

| Prio | Feature | Status | Ziel |
|------|---------|--------|------|
| **P1** | RAG-Integration (Vektor-Suche in better-sqlite3) | Neu | Präzisere Antworten durch Kontext |
| **P2** | Echtzeit-Kennzahlenanalyse | Neu | KPI-Überwachung + Warnungen |
| **P2** | Optimale Schneidplanung (CuttingPage) | Neu | Bin-Packing-Algorithmus |
| **P3** | Intelligente Bestandsführung | Neu | ABC-Analyse 2.0 + Bestellvorschläge |
| **P3** | Lageroptimierung | Neu | Dynamische Lagerplatzvergabe |
| **P4** | Bewegungsdaten-Analyse | Neu | Anomalieerkennung |
| **P4** | Prozessoptimierung | Neu | Simulation & Engpassanalyse |
| **P5** | Ressourcenoptimierung | Neu | Maschinenauslastung & Auftragsreihenfolge |
| **P5** | Lieferketten-Optimierung | Neu | Lieferzeitprognosen & Risikoanalyse |
| **P5** | Berichtswesen | Neu | Automatisierte KPI-Reports |

---

## **2. API-Struktur**

### **2.1 Chat & RAG**
**Endpunkt:**  
`POST /src/components/modules/ai/components/chat`

Zum Beispiel
**Input (JSON):**
```json
{
  "message": "Wie hoch ist der Lagerbestand von Kabeltyp X?",
  "sessionId": "abc123"
}
```

Zum Beispiel
**Output (JSON):**
```json
{
  "answer": "Der aktuelle Lagerbestand von Kabeltyp X beträgt 1200m.",
  "sources": [
    { "id": 42, "content": "Bestand Kabeltyp X: 1200m, Stand 08.10.2025" }
  ]
}
```

**Ablauf:**
1. Embedding für `message` erstellen.
2. Vektor-Suche in SQLite (`sqlite-vss`) → Top-N relevante Chunks.
3. Kontext + Frage an LLM senden.
4. Antwort + Quellen zurückgeben.

---

### **2.2 KPI-Analyse**
**Endpunkt:**  
`GET /src/components/modules/ai/components/kpi`

Zum Beispiel
**Output (JSON):**
```json
{
  "kpi": {
    "lagerbestand": 1200,
    "auslastung": 85,
    "kapazitaet": 95
  },
  "warnings": [
    "Lagerbestand Kabeltyp X unter Mindestwert"
  ]
}
```

---

### **2.3 Optimale Schneidplanung**
**Endpunkt:**  
`POST /src/components/modules/ai/components/cutting-plan`

Zum Beispiel
**Input (JSON):**
```json
{
  "orders": [
    { "type": "Kabel A", "length": 50 },
    { "type": "Kabel A", "length": 30 }
  ],
  "stock": [
    { "type": "Kabel A", "length": 100 },
    { "type": "Kabel A", "length": 70 }
  ]
}
```

Zum Beispiel
**Output (JSON):**
```json
{
  "plan": [
    { "drum": 1, "cuts": [50, 30], "waste": 20 }
  ]
}
```

---

## **3. Modul-Übersicht (Dateistruktur)**

```
/src/components/modules/ai/components/
│
├── /chat
│   ├── ragService.js           # RAG-Logik (Embedding + Vektor-Suche)
│   ├── embeddingService.js     # Schnittstelle zu OpenAI Embeddings
│   └── promptTemplates.js      # Prompt-Vorlagen
│
├── /cutting
│   ├── cuttingController.js    # API-Logik für Schneidplanung
│   └── binPacking.js           # Bin-Packing-Algorithmus
│
├── /kpi
│   ├── kpiController.js        # API-Logik für KPI-Analyse
│   └── kpiService.js           # Berechnungen & Warnungen
│
├── /inventory
│   ├── inventoryController.js  # API-Logik für Bestandsführung
│   └── abcAnalysis.js          # ABC-Analyse 2.0
│
├── /utils
│   ├── db.js                   # better-sqlite3 Verbindung + Extension-Loader
│   └── logger.js               # Logging
│
└── aiRoutes.js                 # Alle AI-Routen registrieren
```

---
