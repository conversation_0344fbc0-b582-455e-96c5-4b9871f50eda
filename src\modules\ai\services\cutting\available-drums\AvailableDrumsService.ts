/**
 * Available Drums Service
 * 
 * Service für die Abfrage verfügbarer Trommeln basierend auf Filterkriterien
 */

export interface AvailableDrumFilter {
  aufnahmeDatum: string; // Format: YYYY-MM-DD
  material?: string; // Materialcode (optional)
  minGesamtbestand: number; // Mindestlänge in Metern
}

export interface AvailableDrum {
  charge: string;
  material: string;
  gesamtbestand: number;
  lagereinheitentyp: string;
  aufnahmeDatum: string;
  lagerort?: string;
  lagerplatz?: string;
  lagerbereich?: string; // Lagerbereich für die Anzeige
  bestandsart?: string;
  sonderbestand?: string;
  chargenklassifizierung?: string;
  verfügbarkeitsdatum?: string;
}

export interface AvailableDrumsResponse {
  success: boolean;
  count: number;
  drums: AvailableDrum[];
  filters: AvailableDrumFilter;
}

class AvailableDrumsService {
  private baseUrl = 'http://localhost:3001/api/available-drums';

  /**
   * Filtert verfügbare Trommeln basierend auf den angegebenen Kriterien
   */
  async getAvailableDrums(filter: AvailableDrumFilter): Promise<AvailableDrumsResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/filter`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filter),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      const data: AvailableDrumsResponse = await response.json();
      
      console.log(`Verfügbare Trommeln geladen: ${data.count} Trommeln für Material ${filter.material}`);
      
      return data;
    } catch (error) {
      console.error('Fehler beim Laden verfügbarer Trommeln:', error);
      throw new Error(
        `Fehler beim Laden verfügbarer Trommeln: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`
      );
    }
  }

  /**
   * Lädt alle verfügbaren Materialcodes für die Dropdown-Auswahl
   */
  async getAvailableMaterials(): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/materials`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.materials || [];
    } catch (error) {
      console.error('Fehler beim Laden der Materialien:', error);
      throw new Error(
        `Fehler beim Laden der Materialien: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`
      );
    }
  }

  /**
   * Formatiert ein Datum für die API (YYYY-MM-DD)
   */
  formatDateForAPI(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Validiert die Filterkriterien
   */
  validateFilter(filter: Partial<AvailableDrumFilter>): string[] {
    const errors: string[] = [];

    if (!filter.aufnahmeDatum) {
      errors.push('Aufnahmedatum ist erforderlich');
    } else if (!/^\d{4}-\d{2}-\d{2}$/.test(filter.aufnahmeDatum)) {
      errors.push('Aufnahmedatum muss im Format YYYY-MM-DD sein');
    }

    // Material ist jetzt optional - keine Validierung erforderlich
    // if (!filter.material || filter.material.trim() === '') {
    //   errors.push('Material ist erforderlich');
    // }

    if (filter.minGesamtbestand === undefined || filter.minGesamtbestand < 0) {
      errors.push('Mindestbestand muss >= 0 sein');
    }

    return errors;
  }
}

// Singleton-Instanz exportieren
export const availableDrumsService = new AvailableDrumsService();