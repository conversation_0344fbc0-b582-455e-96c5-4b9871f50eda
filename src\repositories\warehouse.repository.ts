/**
 * Warehouse Repository
 * 
 * Repository für Warehouse-Daten (ATrL, ARiL, ManL, WE)
 * Implementiert das Repository Pattern für bessere Datenabstraktion.
 */

import { BaseRepository, DateRangeFilter } from './base.repository';
import apiService from '@/services/api.service';
import { 
  WEDataPoint,
  Lagerauslastung200DataPoint,
  Lagerauslastung240DataPoint 
} from '../types/database';

/**
 * Filter für Warehouse-Daten
 */
export interface WarehouseFilter extends DateRangeFilter {
  warehouseType?: 'atrl' | 'aril' | 'manl' | 'we';
  includeUtilization?: boolean;
}

/**
 * ATrL Data Repository
 */
export class AtrlRepository extends BaseRepository<any, DateRangeFilter> {
  protected repositoryName = 'atrl-data';
  
  async getAll(filter?: DateRangeFilter): Promise<any[]> {
    return await apiService.getAtrlData(filter?.startDate, filter?.endDate);
  }
  
  /**
   * ATrL-Daten mit Standardzeitraum abrufen (letzte 30 Tage)
   */
  async getRecent(): Promise<any[]> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const startDate = thirtyDaysAgo.toISOString().split('T')[0];
    
    return this.getAll({ startDate });
  }
}

/**
 * ARiL Data Repository
 */
export class ArilRepository extends BaseRepository<any, DateRangeFilter> {
  protected repositoryName = 'aril-data';
  
  async getAll(filter?: DateRangeFilter): Promise<any[]> {
    return await apiService.getArilData(filter?.startDate, filter?.endDate);
  }
  
  /**
   * ARiL-Daten mit Standardzeitraum abrufen (letzte 30 Tage)
   */
  async getRecent(): Promise<any[]> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const startDate = thirtyDaysAgo.toISOString().split('T')[0];
    
    return this.getAll({ startDate });
  }
}

/**
 * Wareneingang (WE) Repository
 */
export class WERepository extends BaseRepository<WEDataPoint, any> {
  protected repositoryName = 'we-data';
  
  async getAll(): Promise<WEDataPoint[]> {
    return await apiService.getWEData();
  }
  
  /**
   * WE-Daten nach Datum gruppiert abrufen
   */
  async getGroupedByDate(): Promise<Record<string, WEDataPoint[]>> {
    const data = await this.getAll();
    
    return data.reduce((grouped, item) => {
      const date = item.datum || 'unknown';
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(item);
      return grouped;
    }, {} as Record<string, WEDataPoint[]>);
  }
  
  /**
   * Summe der WE-Daten für einen Zeitraum berechnen
   */
  async getTotals(): Promise<{
    totalAtrl: number;
    totalManl: number;
    totalEntries: number;
  }> {
    const data = await this.getAll();
    
    return data.reduce((totals, item) => ({
      totalAtrl: totals.totalAtrl + (item.weAtrl || 0),
      totalManl: totals.totalManl + (item.weManl || 0),
      totalEntries: totals.totalEntries + 1
    }), { totalAtrl: 0, totalManl: 0, totalEntries: 0 });
  }
}

/**
 * Lagerauslastung 200 Repository
 */
export class Lagerauslastung200Repository extends BaseRepository<Lagerauslastung200DataPoint, DateRangeFilter> {
  protected repositoryName = 'lagerauslastung200';
  
  async getAll(filter?: DateRangeFilter): Promise<Lagerauslastung200DataPoint[]> {
    return await apiService.getLagerauslastung200Data(filter?.startDate, filter?.endDate);
  }
  
  /**
   * Aktuelle Auslastung abrufen (letzter verfügbarer Datensatz)
   */
  async getCurrentUtilization(): Promise<Lagerauslastung200DataPoint | null> {
    const data = await this.getAll();
    
    if (!data || data.length === 0) return null;
    
    // Sortiere nach Datum (neueste zuerst) und gib den ersten zurück
    return data.sort((a, b) => {
      const dateA = new Date(a.aufnahmeDatum || '');
      const dateB = new Date(b.aufnahmeDatum || '');
      return dateB.getTime() - dateA.getTime();
    })[0];
  }
  
  /**
   * Auslastungsstatistiken berechnen
   */
  async getUtilizationStats(filter?: DateRangeFilter): Promise<{
    avgUtilization: number;
    maxUtilization: number;
    minUtilization: number;
    currentUtilization: number;
    dataPoints: number;
  }> {
    const data = await this.getAll(filter);
    
    if (!data || data.length === 0) {
      return {
        avgUtilization: 0,
        maxUtilization: 0,
        minUtilization: 0,
        currentUtilization: 0,
        dataPoints: 0
      };
    }
    
    const utilizations = data
      .map(item => parseFloat(item.auslastung || '0'))
      .filter(val => !isNaN(val));
    
    const current = await this.getCurrentUtilization();
    
    return {
      avgUtilization: utilizations.reduce((sum, val) => sum + val, 0) / utilizations.length,
      maxUtilization: Math.max(...utilizations),
      minUtilization: Math.min(...utilizations),
      currentUtilization: parseFloat(current?.auslastung || '0'),
      dataPoints: data.length
    };
  }
}

/**
 * Lagerauslastung 240 Repository
 */
export class Lagerauslastung240Repository extends BaseRepository<Lagerauslastung240DataPoint, DateRangeFilter> {
  protected repositoryName = 'lagerauslastung240';
  
  async getAll(filter?: DateRangeFilter): Promise<Lagerauslastung240DataPoint[]> {
    return await apiService.getLagerauslastung240Data(filter?.startDate, filter?.endDate);
  }
  
  /**
   * Aktuelle Auslastung abrufen (letzter verfügbarer Datensatz)
   */
  async getCurrentUtilization(): Promise<Lagerauslastung240DataPoint | null> {
    const data = await this.getAll();
    
    if (!data || data.length === 0) return null;
    
    return data.sort((a, b) => {
      const dateA = new Date(a.aufnahmeDatum || '');
      const dateB = new Date(b.aufnahmeDatum || '');
      return dateB.getTime() - dateA.getTime();
    })[0];
  }
  
  /**
   * Auslastungsstatistiken berechnen
   */
  async getUtilizationStats(filter?: DateRangeFilter): Promise<{
    avgUtilization: number;
    maxUtilization: number;
    minUtilization: number;
    currentUtilization: number;
    dataPoints: number;
  }> {
    const data = await this.getAll(filter);
    
    if (!data || data.length === 0) {
      return {
        avgUtilization: 0,
        maxUtilization: 0,
        minUtilization: 0,
        currentUtilization: 0,
        dataPoints: 0
      };
    }
    
    const utilizations = data
      .map(item => parseFloat(item.auslastung || '0'))
      .filter(val => !isNaN(val));
    
    const current = await this.getCurrentUtilization();
    
    return {
      avgUtilization: utilizations.reduce((sum, val) => sum + val, 0) / utilizations.length,
      maxUtilization: Math.max(...utilizations),
      minUtilization: Math.min(...utilizations),
      currentUtilization: parseFloat(current?.auslastung || '0'),
      dataPoints: data.length
    };
  }
}

/**
 * Kombiniertes Warehouse Repository für alle Warehouse-Operationen
 */
export class WarehouseRepository {
  public readonly atrl = new AtrlRepository();
  public readonly aril = new ArilRepository();
  public readonly we = new WERepository();
  public readonly lagerauslastung200 = new Lagerauslastung200Repository();
  public readonly lagerauslastung240 = new Lagerauslastung240Repository();
  
  /**
   * Cache für alle Warehouse-Repositories invalidieren
   */
  invalidateAllCache(): void {
    this.atrl.invalidateCache();
    this.aril.invalidateCache();
    this.we.invalidateCache();
    this.lagerauslastung200.invalidateCache();
    this.lagerauslastung240.invalidateCache();
    console.log('Cache für alle Warehouse-Repositories invalidiert');
  }
  
  /**
   * Gesamtstatistiken für alle Warehouses abrufen
   */
  async getOverallStats(filter?: DateRangeFilter): Promise<{
    warehouse200: any;
    warehouse240: any;
    wareneingang: any;
    atrlData: number;
    arilData: number;
  }> {
    const [stats200, stats240, weTotals, atrlData, arilData] = await Promise.all([
      this.lagerauslastung200.getUtilizationStats(filter),
      this.lagerauslastung240.getUtilizationStats(filter),
      this.we.getTotals(),
      this.atrl.getAll(filter),
      this.aril.getAll(filter)
    ]);
    
    return {
      warehouse200: stats200,
      warehouse240: stats240,
      wareneingang: weTotals,
      atrlData: atrlData.length,
      arilData: arilData.length
    };
  }
}