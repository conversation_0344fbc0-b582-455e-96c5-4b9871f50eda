import { describe, test, expect, beforeAll, afterAll } from 'vitest';
import { StoerungenRepository } from '../stoerungen.repository';
import { db } from '../../db';
import { stoerungen, stoerungsComments } from '../../db/schema';
import { eq } from 'drizzle-orm';

describe('StoerungenRepository (Drizzle)', () => {
  let repository: StoerungenRepository;
  let testStoerungId: number;

  beforeAll(async () => {
    repository = StoerungenRepository.getInstance();
    
    // Erstelle eine Test-Störung
    const testStoerung = {
      title: 'Test Störung - Drizzle Migration',
      description: 'Dies ist eine Test-Störung für die Drizzle Migration',
      severity: 'HIGH',
      status: 'NEW',
      category: 'SYSTEM',
      affectedSystem: 'TEST_SYSTEM',
      reportedBy: '<EMAIL>',
      tags: ['test', 'migration', 'drizzle'],
    };

    const created = await repository.createStoerung(testStoerung);
    testStoerungId = created.id;
  });

  afterAll(async () => {
    // Cleanup: Lösche die Test-Störung
    if (testStoerungId) {
      await db.delete(stoerungen).where(eq(stoerungen.id, testStoerungId));
    }
  });

  test('should create störung with correct data', async () => {
    const stoerungData = {
      title: 'Neue Test Störung',
      description: 'Eine neue Test-Störung',
      severity: 'MEDIUM',
      status: 'NEW',
      category: 'HARDWARE',
      affectedSystem: 'SYSTEM_A',
      reportedBy: '<EMAIL>',
      tags: ['hardware', 'urgent'],
    };

    const created = await repository.createStoerung(stoerungData);

    expect(created).toBeDefined();
    expect(created.title).toBe(stoerungData.title);
    expect(created.severity).toBe(stoerungData.severity);
    expect(created.tags).toEqual(stoerungData.tags);
    expect(created.comments).toEqual([]);

    // Cleanup
    await db.delete(stoerungen).where(eq(stoerungen.id, created.id));
  });

  test('should retrieve störung by ID', async () => {
    const retrieved = await repository.getStoerungById(testStoerungId);

    expect(retrieved).toBeDefined();
    expect(retrieved!.id).toBe(testStoerungId);
    expect(retrieved!.title).toBe('Test Störung - Drizzle Migration');
    expect(retrieved!.tags).toEqual(['test', 'migration', 'drizzle']);
  });

  test('should return null for non-existent störung ID', async () => {
    const nonExistent = await repository.getStoerungById(999999);
    expect(nonExistent).toBeNull();
  });

  test('should list störungen with filters', async () => {
    const störungen = await repository.getStoerungen({
      status: 'NEW',
      severity: 'HIGH',
      limit: 10
    });

    expect(Array.isArray(störungen)).toBe(true);
    
    // Überprüfe, dass alle Störungen den Filterkriterien entsprechen
    störungen.forEach(stoerung => {
      expect(stoerung.status).toBe('NEW');
      expect(stoerung.severity).toBe('HIGH');
    });
    
    // Unsere Test-Störung sollte dabei sein
    const testStoerung = störungen.find(s => s.id === testStoerungId);
    expect(testStoerung).toBeDefined();
  });

  test('should update störung', async () => {
    const updateData = {
      status: 'IN_PROGRESS',
      assigned_to: '<EMAIL>',
      description: 'Updated description for test störung',
    };

    const updated = await repository.updateStoerung(testStoerungId, updateData);

    expect(updated.id).toBe(testStoerungId);
    expect(updated.status).toBe('IN_PROGRESS');
    expect(updated.assigned_to).toBe('<EMAIL>');
    expect(updated.description).toBe(updateData.description);
  });

  test('should add comment to störung', async () => {
    const commentData = {
      stoerung_id: testStoerungId,
      comment: 'This is a test comment for Drizzle migration',
      user_id: 'test-user',
    };

    const comment = await repository.addComment(commentData);

    expect(comment).toBeDefined();
    expect(comment.comment).toBe(commentData.comment);
    expect(comment.stoerungId).toBe(testStoerungId);

    // Überprüfe, dass das Kommentar bei der Störung angezeigt wird
    const störungWithComments = await repository.getStoerungById(testStoerungId);
    expect(störungWithComments!.comments.length).toBeGreaterThan(0);
    
    const addedComment = störungWithComments!.comments.find(c => c.comment === commentData.comment);
    expect(addedComment).toBeDefined();
  });

  test('should get störungen statistics', async () => {
    const stats = await repository.getStoerungsStats();

    expect(stats).toBeDefined();
    expect(typeof stats.total).toBe('number');
    expect(typeof stats.active).toBe('number');
    expect(typeof stats.resolved).toBe('number');
    expect(typeof stats.recent).toBe('number');
    expect(typeof stats.avgMttr).toBe('number');
    expect(typeof stats.bySeverity).toBe('object');

    // Stelle sicher, dass die Zahlen sinnvoll sind
    expect(stats.total).toBeGreaterThan(0);
    expect(stats.total).toBeGreaterThanOrEqual(stats.active + stats.resolved);
  });

  test('should handle deletion correctly', async () => {
    // Erstelle eine temporäre Störung zum Löschen
    const tempStoerung = await repository.createStoerung({
      title: 'Temp Störung zum Löschen',
      severity: 'LOW',
      status: 'NEW',
      category: 'TEST',
    });

    const deletionResult = await repository.deleteStoerung(tempStoerung.id);
    expect(deletionResult).toBe(true);

    // Überprüfe, dass die Störung wirklich gelöscht wurde
    const deletedStoerung = await repository.getStoerungById(tempStoerung.id);
    expect(deletedStoerung).toBeNull();
  });

  test('should handle caching correctly', async () => {
    // Erste Abfrage - sollte die Daten aus der DB holen
    const firstCall = await repository.getStoerungById(testStoerungId);
    
    // Zweite Abfrage - sollte gecachte Daten verwenden
    const secondCall = await repository.getStoerungById(testStoerungId);
    
    expect(firstCall).toEqual(secondCall);
    expect(firstCall!.id).toBe(testStoerungId);
  });
});
