# Requirements Document

## Introduction

Das Authentication-System soll sichers<PERSON>len, dass Benutzer sich anmelden müssen, bevor sie auf die Hauptfunktionen der JOZI1 Lapp Dashboard-Anwendung zugreifen können. Die Anwendung soll beim Start automatisch prüfen, ob ein Benutzer bereits eingeloggt ist, und entsprechend zur Login-Seite oder zum Dashboard weiterleiten.

## Requirements

### Requirement 1

**User Story:** Als ein nicht-authentifizierter Benutzer möchte ich beim Start der Anwendung automatisch zur Login-Seite weitergeleitet werden, damit ich mich anmelden kann.

#### Acceptance Criteria

1. WHEN die Anwendung gestartet wird AND kein gültiger JWT-Token im localStorage vorhanden ist THEN soll die Anwendung automatisch zur Login-Seite (/login) weiterleiten
2. WHEN ein <PERSON> versucht, eine geschützte Route aufzurufen AND nicht authentifiziert ist THEN soll er zur Login-Seite weitergeleitet werden
3. WHEN ein Benutzer die Login-Seite aufruft AND bereits authentifiziert ist THEN soll er zum Dashboard (/) weitergeleitet werden

### Requirement 2

**User Story:** Als ein authentifizierter Benutzer möchte ich nach erfolgreicher Anmeldung zum Dashboard weitergeleitet werden, damit ich die Anwendung nutzen kann.

#### Acceptance Criteria

1. WHEN ein Benutzer sich erfolgreich anmeldet THEN soll er automatisch zum Dashboard (/) weitergeleitet werden
2. WHEN ein authentifizierter Benutzer die Anwendung neu startet AND ein gültiger Token vorhanden ist THEN soll er direkt zum Dashboard weitergeleitet werden
3. WHEN ein Token abgelaufen ist THEN soll der Benutzer automatisch abgemeldet und zur Login-Seite weitergeleitet werden

### Requirement 3

**User Story:** Als ein Benutzer möchte ich mich abmelden können, damit meine Session beendet wird und andere Benutzer keinen Zugriff auf meine Daten haben.

#### Acceptance Criteria

1. WHEN ein Benutzer sich abmeldet THEN sollen alle gespeicherten Authentifizierungsdaten (Token, Benutzerdaten) aus dem localStorage entfernt werden
2. WHEN ein Benutzer sich abmeldet THEN soll er zur Login-Seite weitergeleitet werden
3. WHEN ein Benutzer sich abmeldet THEN soll eine Bestätigungsmeldung angezeigt werden

### Requirement 4

**User Story:** Als ein neuer Benutzer möchte ich mich registrieren können, damit ich Zugang zur Anwendung erhalte.

#### Acceptance Criteria

1. WHEN ein Benutzer auf "Registrieren" klickt THEN soll er zur Registrierungsseite (/register) weitergeleitet werden
2. WHEN ein Benutzer sich erfolgreich registriert THEN soll er zur Login-Seite mit einer Erfolgsmeldung weitergeleitet werden
3. WHEN ein Benutzer bereits registriert ist AND versucht sich erneut zu registrieren THEN soll eine entsprechende Fehlermeldung angezeigt werden

### Requirement 5

**User Story:** Als ein Systemadministrator möchte ich, dass die Anwendung automatisch Session-Management durchführt, damit die Sicherheit gewährleistet ist.

#### Acceptance Criteria

1. WHEN ein JWT-Token abläuft THEN soll der Benutzer automatisch abgemeldet werden
2. WHEN ein Benutzer inaktiv ist für mehr als 15 Minuten THEN soll eine Warnung angezeigt werden
3. WHEN ein Benutzer inaktiv ist für mehr als 30 Minuten THEN soll er automatisch abgemeldet werden
4. WHEN ein API-Aufruf einen 401-Fehler zurückgibt THEN soll der Benutzer automatisch abgemeldet werden