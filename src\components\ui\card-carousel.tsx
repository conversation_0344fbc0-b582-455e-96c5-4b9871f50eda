import React, { useState, useEffect, useRef } from "react"
import { Swiper, SwiperSlide } from "swiper/react"
import { AnimatePresence, motion } from "framer-motion"

import "swiper/css"
import "swiper/css/effect-coverflow"
import "swiper/css/pagination"
import "swiper/css/navigation"
import {
  SparklesIcon,
  Bot,
  LayoutDashboard,
  Database,
  AlertTriangle,
  Settings,
  User,
  ChevronLeft,
  ChevronRight,
  ArrowRight
} from "lucide-react"
import {
  EffectCoverflow,
  Navigation,
  Pagination,
} from "swiper/modules"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ModuleCard as ModuleCardType } from "@/types/module"
import { ShineBorder } from "@/components/Animation/magicui/shine-border"
import CardFlip from "@/components/Animation/kokonutui/card-flip"
// Statische Bilder per ESM-Import (Vite wandelt in URLs um, Electron-sicher)
import imgDashboard from '@/assets/Dashboard.png';
import imgError from '@/assets/Error.png';
import imgDatabase from '@/assets/Database.png';
import imgBot from '@/assets/Bot.png';
import imgSettings from '@/assets/Settings.png';
import imgUser from '@/assets/User.png';

// Hilfsfunktion zum Abrufen des passenden Icons basierend auf dem Titel
const getIconForTitle = (title: string): React.ReactNode => {
  // Standardgröße für alle Icons
  const iconProps = { className: "mr-2 h-5 w-5" };

  // Mapping von Titeln zu Icons
  switch (title.toLowerCase()) {
    case "ai":
      return <Bot {...iconProps} />;
    case "dashboard":
      return <LayoutDashboard {...iconProps} />;
    case "backend":
      return <Database {...iconProps} />;
    case "störungsmanagement":
      return <AlertTriangle {...iconProps} />;
    case "app-settings":
    case "settings":
      return <Settings {...iconProps} />;
    case "user-settings":
    case "user":
      return <User {...iconProps} />;
    default:
      // Fallback-Icon oder keines
      return null;
  }
};

// Helper function to get module image paths
const getModuleImagePath = (iconType: string): string => {
  const imageMap: Record<string, string> = {
    'LayoutDashboard': imgDashboard,
    'AlertTriangle': imgError,
    'Database': imgDatabase,
    'Bot': imgBot,
    'Settings': imgSettings,
    'User': imgUser,
  };
  const resolved = imageMap[iconType] || imgDashboard;
  try { console.log('[CardCarousel] getModuleImagePath', { iconType, resolved }); } catch {}
  return resolved;
};

// Custom Cursor Component für Carousel Navigation
interface CustomCursorProps {
  containerRef: React.RefObject<HTMLDivElement | null>;
  centerCardRef: React.RefObject<HTMLDivElement | null>;
  onClickLeft: () => void;
  onClickRight: () => void;
}

interface MousePosition {
  x: number;
  y: number;
}

const CustomCursor: React.FC<CustomCursorProps> = ({
  containerRef,
  centerCardRef,
  onClickLeft,
  onClickRight,
}) => {
  const [mousePosition, setMousePosition] = useState<MousePosition>({
    x: 0,
    y: 0,
  });
  const [isInNavigationArea, setIsInNavigationArea] = useState<boolean>(false);
  const [rotation, setRotation] = useState<boolean>(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });

      if (containerRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect();
        const isInContainer =
          e.clientX >= containerRect.left &&
          e.clientX <= containerRect.right &&
          e.clientY >= containerRect.top &&
          e.clientY <= containerRect.bottom;

        if (isInContainer && centerCardRef.current) {
          // Prüfe, ob die Maus über der mittleren Karte ist (mit größerer toter Zone)
          const centerCardRect = centerCardRef.current.getBoundingClientRect();
          const padding = 50; // Zusätzlicher Abstand um die mittlere Karte
          const isOverCenterCard =
            e.clientX >= centerCardRect.left - padding &&
            e.clientX <= centerCardRect.right + padding &&
            e.clientY >= centerCardRect.top - padding &&
            e.clientY <= centerCardRect.bottom + padding;

          // Custom Cursor nur anzeigen, wenn NICHT über der mittleren Karte (mit Puffer)
          setIsInNavigationArea(isInContainer && !isOverCenterCard);

          if (isInContainer && !isOverCenterCard) {
            const centerX = containerRect.left + containerRect.width / 2;
            const isLeftHalf = e.clientX < centerX;
            setRotation(isLeftHalf);
          }
        } else {
          setIsInNavigationArea(false);
        }
      }
    };

    const handleClick = (e: MouseEvent) => {
      if (isInNavigationArea && containerRef.current && centerCardRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect();
        const centerCardRect = centerCardRef.current.getBoundingClientRect();

        // Nur navigieren, wenn nicht über der mittleren Karte geklickt wurde (mit Puffer)
        const padding = 50; // Gleicher Puffer wie bei mousemove
        const isOverCenterCard =
          e.clientX >= centerCardRect.left - padding &&
          e.clientX <= centerCardRect.right + padding &&
          e.clientY >= centerCardRect.top - padding &&
          e.clientY <= centerCardRect.bottom + padding;

        if (!isOverCenterCard) {
          const centerX = containerRect.left + containerRect.width / 2;

          if (e.clientX < centerX) {
            onClickLeft();
          } else {
            onClickRight();
          }
        }
      }
    };

    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("click", handleClick);

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("click", handleClick);
    };
  }, [containerRef, centerCardRef, isInNavigationArea, onClickLeft, onClickRight]);

  return (
    <div>
      <AnimatePresence>
        {isInNavigationArea && (
          <motion.div
            initial={{
              scale: 0,
            }}
            animate={{
              scale: 1,
            }}
            exit={{
              scale: 0,
            }}
            className="fixed z-50 pointer-events-none"
            style={{
              left: mousePosition.x - 25,
              top: mousePosition.y - 25,
            }}
          >
            <motion.div
              whileTap={{ scale: 0.8 }}
              className="flex items-center justify-center w-[50px] h-[50px] bg-[#f67828] text-white rounded-full shadow-lg"
              animate={{
                rotate: rotation ? 180 : 0,
                transition: {
                  duration: 0.3,
                },
              }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <ArrowRight size={24} />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

interface CarouselProps {
  images?: { src: string; alt: string; title?: string; icon?: React.ReactNode }[]
  modules?: ModuleCardType[]
  username?: string
  name?: string // Neues Feld für den Namen
  autoplayDelay?: number
  showPagination?: boolean
  showNavigation?: boolean
  onNavigate?: (route: string) => void
}

export const CardCarousel: React.FC<CarouselProps> = ({
  images,
  modules,
  username = "Username",
  name, // Neues Feld für den Namen
  showPagination = true,
  showNavigation = true,
  onNavigate = () => { },
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const centerCardRef = useRef<HTMLDivElement>(null);
  const swiperRef = useRef<any>(null);
  const [activeIndex, setActiveIndex] = useState(0);

  const handlePrev = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  const handleNext = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext();
    }
  };
  const css = `
  .swiper {
    width: 100%;
    max-width: 100%;
    padding-top: 50px;
    padding-bottom: 50px;
    position: relative;
    overflow: hidden;
    background: transparent !important;
  }
  .swiper-slide {
    background-position: center;
    background-size: cover;
    width: 400px;
    height: 400px;
    max-width: 90%;
    margin: 0 auto;
  }
  /* Verstecke die Standard-Swiper-Navigationspfeile */
  .swiper-button-next::after,
  .swiper-button-prev::after {
    display: none;
  }
  /* Überschreibe die Standard-Swiper-Navigation Position */
  .swiper-button-next,
  .swiper-button-prev {
    top: 5% !important;
    transform: translateY(-50%) !important;
  }
  /* Stelle sicher, dass unsere benutzerdefinierten Pfeile richtig positioniert sind */
  .swiper-container {
    position: relative;
    overflow: hidden;
    max-width: 100%;
    margin: 0 auto;
    background: transparent !important;
  }
  .swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  
  .swiper-3d .swiper-slide-shadow-left,
  .swiper-3d .swiper-slide-shadow-right,
  .swiper-3d .swiper-slide-shadow-top,
  .swiper-3d .swiper-slide-shadow-bottom {
    background-image: none;
    background: none;
  }
  
  /* Entferne den schwarzen Rand um die Karten */
  .swiper-slide {
    background: transparent;
  }
  `
  return (
    <section className="w-ace-y-4">
      <style>{css}</style>
      {/* Custom Cursor für Carousel Navigation */}
      <CustomCursor
        containerRef={containerRef}
        centerCardRef={centerCardRef}
        onClickLeft={handlePrev}
        onClickRight={handleNext}
      />
      {/* Weißer Border-Container */}
      <div className="mx-auto w-full max-w-4xl rounded-[24px] border border-black/5 p-2 shadow-sm md:rounded-t-[44px] bg-white">
        {/* Transparenter Content-Container */}        <div className="relative mx-auto flex w-full flex-col rounded-[24px] border border-black/5 p-2 md:items-start md:gap-8 md:rounded-b-[20px] md:rounded-t-[40px] md:p-2 bg-black/10">

          <Badge
            variant="outline"
            className="absolute left-4 top-6 rounded-[14px] border border-black/20 text-sm px-4 py-1.5 md:left-6"
          >
            <SparklesIcon className="fill-[#EEBDE0] stroke- text-neutral-800" />{" "}
            {username} {/* Zeige Name an, wenn verfügbar, sonst Benutzername */}
          </Badge>
          <div className="flex flex-col justify-center pb-2 pl-4 pt-14 md:items-center">
            <div className="flex gap-2">
              <div>
                <h3 className="text-4xl opacity-85 font-bold tracking-tight">
                  Wähle deinen Bereich aus
                </h3>
                <p>Mit dem du weiter Arbeiten möchtest</p>
              </div>
            </div>
          </div>

          <div className="flex w-full items-center justify-center gap-4">
            <div className="w-full">
              <div ref={containerRef} className="relative">
                {/* Navigationspfeile für das Karussell */}
                {showNavigation && (
                  <div className="swiper-button-prev absolute left-0 bottom-8 z-20 flex h-12 w-12 transform cursor-pointer items-center justify-center rounded-full bg-white/70 shadow-md backdrop-blur-sm transition-all hover:bg-white/90 hover:scale-110">
                    <ChevronLeft className="h-6 w-6 text-teal-700" />
                  </div>
                )}

                {showNavigation && (
                  <div className="swiper-button-next absolute right-0 bottom-8 z-20 flex h-12 w-12 transform cursor-pointer items-center justify-center rounded-full bg-white/70 shadow-md backdrop-blur-sm transition-all hover:bg-white/90 hover:scale-110">
                    <ChevronRight className="h-6 w-6 text-teal-700" />
                  </div>
                )}
                <Swiper
                  ref={swiperRef}
                  className="swiper-container"
                  spaceBetween={50}
                  // Autoplay wurde entfernt, damit das Karussell nur manuell wechselt
                  effect={"coverflow"}
                  grabCursor={false} // Deaktiviert, da wir Custom Cursor verwenden
                  centeredSlides={true}
                  loop={true}
                  slidesPerView={"auto"}
                  coverflowEffect={{
                    rotate: 0,
                    stretch: 0,
                    depth: 80,
                    modifier: 2.0,
                  }}
                  pagination={showPagination}
                  navigation={{
                    nextEl: ".swiper-button-next",
                    prevEl: ".swiper-button-prev",
                    // Verstecke die Standard-Navigationspfeile von Swiper
                    disabledClass: "opacity-50 cursor-not-allowed"
                  }}
                  onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
                  modules={[EffectCoverflow, Pagination, Navigation]}
                >
                  {/* Render module cards if provided - using CardFlip components */}
                  {modules && modules.map((module, index) => {
                    const isActiveSlide = index === activeIndex;
                    return (
                      <SwiperSlide key={`module-${module.id}`}>
                        <div
                          className="relative flex justify-center items-center h-full"
                          ref={isActiveSlide ? centerCardRef : null}
                        >
                          {/* ShineBorder um die gesamte CardFlip-Komponente */}
                          <div className="relative w-full h-full max-w-[350px]">
                            <ShineBorder
                              borderWidth={2}
                              duration={10}
                              shineColor={[module.color || "#009080", (module.color || "#009080").replace("#", "#80")]}
                              className="rounded-2xl"
                            />
                            {/* CardFlip - nur die mittlere Karte ist klickbar */}
                            <CardFlip
                              title={module.title}
                              subtitle={module.description}
                              description={`Detaillierte Informationen über ${module.title} und dessen Funktionen.`}
                              features={[
                                module.title === 'Dashboard' ? 'KPI-Monitoring' :
                                module.title === 'Störungsmanagement' ? 'Live-Monitoring' :
                                module.title === 'Backend' ? 'Systemüberwachung' :
                                module.title === 'AI' ? 'AI-Knowledgebase' :
                                module.title === 'App-Settings' ? 'Systemkonfiguration' :
                                'Profilverwaltung',
                                
                                module.title === 'Dashboard' ? 'Datenfilterung' :
                                module.title === 'Störungsmanagement' ? 'Störungsüberwachung' :
                                module.title === 'Backend' ? 'Workflow-Management' :
                                module.title === 'AI' ? 'AI-Cutting Optimizer' :
                                module.title === 'App-Settings' ? 'Benutzereinstellungen' :
                                'Personalisierung',
                                
                                module.title === 'Dashboard' ? 'Visualisierung' :
                                module.title === 'Störungsmanagement' ? 'Störungsmeldung' :
                                module.title === 'Backend' ? 'Automatisierung' :
                                module.title === 'AI' ? 'AI-Inventory, and -Stock Optimizer' :
                                module.title === 'App-Settings' ? 'Sicherheit' :
                                'Sicherheit',
                                
                                module.title === 'Dashboard' ? 'Berichte' :
                                module.title === 'Störungsmanagement' ? 'Störungsanalyse' :
                                module.title === 'Backend' ? 'Überwachung' :
                                module.title === 'AI' ? 'AI-Predictive Analytics' :
                                module.title === 'App-Settings' ? 'Backup' :
                                'Präferenzen'
                              ]}
                              color={module.color || "#009080"}
                              isAccessible={module.isAccessible !== undefined ? module.isAccessible : true}
                              inDevelopment={module.inDevelopment || false}
                              onNavigate={isActiveSlide ? () => onNavigate(module.route) : () => {}} // Nur mittlere Karte navigiert
                              imageSrc={getModuleImagePath(module.icon)}
                              imageAlt={`${module.title} Modul`}
                            />
                          </div>
  
                          {/* Overlay für seitliche Karten um Klicks zu verhindern */}
                          {!isActiveSlide && (
                            <div className="absolute inset-0 z-10 cursor-default" />
                          )}
                        </div>
                      </SwiperSlide>
                    );
                  })}

                  {/* Fallback to legacy images if no modules provided */}
                  {!modules && images && images.map((image, index) => {
                    const isActiveSlide = index === activeIndex;
                    // Bestimme eine Farbe basierend auf dem Titel der Karte
                    let cardColor = "#009080"; // Standard-Farbe (Teal)

                    // Verschiedene Farben für verschiedene Kartentypen
                    if (image.title === "AI") cardColor = "#6366f1"; // Indigo
                    else if (image.title === "Dashboard") cardColor = "#10b981"; // Emerald
                    else if (image.title === "Backend") cardColor = "#f59e0b"; // Amber
                    else if (image.title === "Störungsmanagement") cardColor = "#ef4444"; // Rot
                    else if (image.title?.includes("Settings")) cardColor = "#8b5cf6"; // Violett
                    else if (image.title?.includes("User")) cardColor = "#3b82f6"; // Blau

                    return (
                      <SwiperSlide key={`image-${index}`}>
                        <div
                          className="size-full rounded-3xl flex flex-col relative"
                          ref={isActiveSlide ? centerCardRef : null}
                        >
                          {/* Überschrift als Button mit Icon, falls vorhanden */}
                          {image.title && (
                            <div className="relative mb-2">
                              <Button
                                variant="default"
                                className="text-lg font-semibold rounded-t-xl bg-black hover:bg-black/10 h-auto py-2 w-full flex items-center justify-center transition-colors z-10 relative"
                                disabled={!isActiveSlide} // Nur mittlere Karte ist klickbar
                              >
                                {getIconForTitle(image.title)}
                                {image.title}
                              </Button>
                              {/* ShineBorder-Effekt um den gesamten Button */}
                              <div className="absolute top-0 left-0 right-0 h-full pointer-events-none z-10">
                                <ShineBorder
                                  borderWidth={2}
                                  duration={10}
                                  shineColor={[cardColor, cardColor.replace(/#/g, '#80')]}
                                  className="rounded-t-xl w-full h-full"
                                />
                              </div>
                            </div>
                          )}
                          <img
                            src={image.src}
                            className="size-full rounded-xl w-[500px] h-[500px] object-cover"
                            alt={image.alt}
                          />

                          {/* Overlay für seitliche Karten um Klicks zu verhindern */}
                          {!isActiveSlide && (
                            <div className="absolute inset-0 z-10 cursor-default" />
                          )}
                        </div>
                      </SwiperSlide>
                    );
                  })}
                </Swiper>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section >
  )
}
