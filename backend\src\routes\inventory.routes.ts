import express from 'express';
import { PrismaClient } from '@prisma-sfm-dashboard/client';

const router = express.Router();
const prisma = new PrismaClient();

/**
 * POST /api/inventory/drums/by-charges
 * Sucht Trommeln anhand von <PERSON>n in der bestand200-Tabelle
 * 
 * Request Body: { charges: string[] }
 * Response: InventoryDrum[]
 */
router.post('/drums/by-charges', async (req, res) => {
  console.log('✅ Inventory drums/by-charges Route aufgerufen');
  
  try {
    const { charges } = req.body;
    
    // Validierung der Eingabe
    if (!charges || !Array.isArray(charges) || charges.length === 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'Charges array ist erforderlich und darf nicht leer sein' 
      });
    }
    
    console.log(`🔍 Suche nach Trommeln für Charges: ${charges.join(', ')}`);
    
    // Abfrage der bestand200-Tabelle nach den angegebenen Chargennummern
    const drums = await prisma.bestand200.findMany({
      where: {
        Charge: {
          in: charges
        },
        // Nur Trommeln mit positivem Bestand
        Gesamtbestand: {
          gt: 0
        }
      },
      select: {
        id: true,
        Material: true,
        Charge: true,
        Dauer: true,
        Lagereinheitentyp: true,
        Lieferung: true,
        Gesamtbestand: true,
        aufnahmeDatum: true,
        aufnahmeZeit: true
      },
      orderBy: {
        aufnahmeDatum: 'asc' // Älteste Trommeln zuerst
      }
    });
    
    // Transformiere die Daten in das erwartete Format
    const transformedDrums = drums.map(drum => ({
      id: drum.id,
      material: drum.Material,
      charge: drum.Charge,
      dauer: drum.Dauer,
      lagereinheitentyp: drum.Lagereinheitentyp,
      lieferung: drum.Lieferung,
      gesamtbestand: drum.Gesamtbestand,
      aufnahmeDatum: drum.aufnahmeDatum,
      aufnahmeZeit: drum.aufnahmeZeit
    }));
    
    console.log(`✅ ${transformedDrums.length} Trommeln gefunden`);
    
    res.json(transformedDrums);
    
  } catch (error) {
    console.error('❌ Fehler beim Abrufen der Trommeln:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Fehler beim Abrufen der Trommeldaten' 
    });
  }
});

/**
 * GET /api/inventory/drums
 * Allgemeine Trommelabfrage mit Filtern
 */
router.get('/drums', async (req, res) => {
  console.log('✅ Inventory drums Route aufgerufen');
  
  try {
    const { materials, limit = 50 } = req.query;
    
    let whereClause: any = {
      Gesamtbestand: {
        gt: 0
      }
    };
    
    // Filter nach Materialien falls angegeben
    if (materials) {
      const materialArray = Array.isArray(materials) ? materials : [materials];
      whereClause.Material = {
        in: materialArray
      };
    }
    
    const drums = await prisma.bestand200.findMany({
      where: whereClause,
      select: {
        id: true,
        Material: true,
        Charge: true,
        Dauer: true,
        Lagereinheitentyp: true,
        Lieferung: true,
        Gesamtbestand: true,
        aufnahmeDatum: true,
        aufnahmeZeit: true
      },
      orderBy: {
        aufnahmeDatum: 'asc'
      },
      take: parseInt(limit as string)
    });
    
    const transformedDrums = drums.map(drum => ({
      id: drum.id,
      material: drum.Material,
      charge: drum.Charge,
      dauer: drum.Dauer,
      lagereinheitentyp: drum.Lagereinheitentyp,
      lieferung: drum.Lieferung,
      gesamtbestand: drum.Gesamtbestand,
      aufnahmeDatum: drum.aufnahmeDatum,
      aufnahmeZeit: drum.aufnahmeZeit
    }));
    
    res.json({ 
      drums: transformedDrums,
      totalCount: transformedDrums.length,
      queryDate: new Date(),
      appliedFilters: materials ? [`Material: ${materials}`] : []
    });
    
  } catch (error) {
    console.error('❌ Fehler beim Abrufen der Trommeln:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Fehler beim Abrufen der Trommeldaten' 
    });
  }
});

export default router;