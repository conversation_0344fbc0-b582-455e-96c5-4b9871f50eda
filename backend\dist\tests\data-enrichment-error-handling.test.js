"use strict";
/**
 * Unit Tests for Data Enrichment Error Handling and Fallback Mechanisms
 *
 * Tests comprehensive error handling, timeout management, and graceful degradation
 * for the AI chatbot database integration.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
// Mock all external dependencies before importing
vitest_1.vi.mock('@prisma/client', () => ({
    PrismaClient: vitest_1.vi.fn().mockImplementation(() => ({}))
}));
vitest_1.vi.mock('../repositories/stoerungen.repository', () => ({
    StoerungenRepository: {
        getInstance: vitest_1.vi.fn().mockReturnValue({
            getStoerungen: vitest_1.vi.fn()
        })
    }
}));
vitest_1.vi.mock('../repositories/dispatch.repository', () => ({
    DispatchRepositoryImpl: vitest_1.vi.fn().mockImplementation(() => ({
        getServiceLevelData: vitest_1.vi.fn(),
        getDailyPerformanceData: vitest_1.vi.fn(),
        getPickingData: vitest_1.vi.fn(),
        getReturnsData: vitest_1.vi.fn(),
        getPerformanceMetrics: vitest_1.vi.fn()
    }))
}));
vitest_1.vi.mock('../repositories/cutting.repository', () => ({
    CuttingRepositoryImpl: vitest_1.vi.fn().mockImplementation(() => ({
        getCuttingChartData: vitest_1.vi.fn(),
        getMaschinenEfficiency: vitest_1.vi.fn(),
        getCuttingPerformanceOverview: vitest_1.vi.fn(),
        getTopPerformingMachines: vitest_1.vi.fn()
    }))
}));
vitest_1.vi.mock('../services/repository-query-coordinator.service', () => ({
    RepositoryQueryCoordinator: vitest_1.vi.fn().mockImplementation(() => ({
        executeQueries: vitest_1.vi.fn(),
        getMetrics: vitest_1.vi.fn(),
        resetMetrics: vitest_1.vi.fn()
    }))
}));
vitest_1.vi.mock('../services/data-formatting.service', () => ({
    DataFormattingService: vitest_1.vi.fn().mockImplementation(() => ({
        buildMultiSourceContext: vitest_1.vi.fn()
    }))
}));
// Import after mocking
const data_enrichment_service_1 = __importDefault(require("../services/data-enrichment.service"));
(0, vitest_1.describe)('DataEnrichmentService Error Handling', () => {
    let dataEnrichmentService;
    let mockQueryCoordinator;
    let mockDataFormatter;
    (0, vitest_1.beforeEach)(() => {
        // Reset all mocks
        vitest_1.vi.clearAllMocks();
        // Create mock instances
        mockQueryCoordinator = {
            executeQueries: vitest_1.vi.fn(),
            getMetrics: vitest_1.vi.fn(),
            resetMetrics: vitest_1.vi.fn()
        };
        mockDataFormatter = {
            buildMultiSourceContext: vitest_1.vi.fn()
        };
        // Create service instance with test configuration
        dataEnrichmentService = new data_enrichment_service_1.default({}, {
            maxQueryTime: 1000, // 1 second for faster tests
            cacheEnabled: false,
            intentThreshold: 0.1
        });
        // Replace internal dependencies with mocks
        dataEnrichmentService.queryCoordinator = mockQueryCoordinator;
        dataEnrichmentService.dataFormatter = mockDataFormatter;
    });
    (0, vitest_1.afterEach)(() => {
        vitest_1.vi.restoreAllMocks();
    });
    (0, vitest_1.describe)('Timeout Handling', () => {
        (0, vitest_1.it)('should handle query timeout gracefully', async () => {
            // Mock query coordinator to simulate timeout
            mockQueryCoordinator.executeQueries.mockImplementation(() => new Promise((_, reject) => setTimeout(() => reject(new Error('Query execution timeout')), 1500)));
            const result = await dataEnrichmentService.enrichChatContext('störungen heute');
            (0, vitest_1.expect)(result.hasData).toBe(false);
            (0, vitest_1.expect)(result.fallbackReason).toBe('CRITICAL_ERROR');
            (0, vitest_1.expect)(result.databaseContext).toContain('technischer Fehler');
            (0, vitest_1.expect)(result.requestId).toBeDefined();
            (0, vitest_1.expect)(result.processingTime).toBeGreaterThan(0);
        });
        (0, vitest_1.it)('should handle partial timeout with some successful queries', async () => {
            // Mock query coordinator to return mixed results
            mockQueryCoordinator.executeQueries.mockResolvedValue([
                {
                    dataType: 'stoerungen',
                    success: true,
                    data: { statistics: { total: 5 } },
                    summary: 'Störungen verfügbar'
                },
                {
                    dataType: 'dispatch',
                    success: false,
                    error: 'Query timeout',
                    summary: 'Timeout'
                }
            ]);
            mockDataFormatter.buildMultiSourceContext.mockReturnValue('Mixed data context');
            const result = await dataEnrichmentService.enrichChatContext('system status');
            (0, vitest_1.expect)(result.hasData).toBe(true);
            (0, vitest_1.expect)(result.partialFailure).toBe(true);
            (0, vitest_1.expect)(result.dataTypes).toContain('stoerungen');
            (0, vitest_1.expect)(result.databaseContext).toBe('Mixed data context');
        });
    });
    (0, vitest_1.describe)('Repository Unavailability', () => {
        (0, vitest_1.it)('should handle all repositories being unavailable', async () => {
            // Mock query coordinator to return all failed results
            mockQueryCoordinator.executeQueries.mockResolvedValue([
                {
                    dataType: 'stoerungen',
                    success: false,
                    error: 'Repository unavailable',
                    fallback: true
                },
                {
                    dataType: 'dispatch',
                    success: false,
                    error: 'Repository unavailable',
                    fallback: true
                }
            ]);
            const result = await dataEnrichmentService.enrichChatContext('system overview');
            (0, vitest_1.expect)(result.hasData).toBe(false);
            (0, vitest_1.expect)(result.fallbackReason).toBe('ALL_QUERIES_FAILED');
            (0, vitest_1.expect)(result.databaseContext).toContain('Systemdatenbanken sind momentan nicht erreichbar');
        });
        (0, vitest_1.it)('should handle single repository failure gracefully', async () => {
            // Mock query coordinator to return partial success
            mockQueryCoordinator.executeQueries.mockResolvedValue([
                {
                    dataType: 'stoerungen',
                    success: true,
                    data: { statistics: { total: 3 } },
                    summary: 'Störungen: 3 total'
                },
                {
                    dataType: 'dispatch',
                    success: false,
                    error: 'Dispatch repository unavailable',
                    fallback: true
                }
            ]);
            mockDataFormatter.buildMultiSourceContext.mockReturnValue('Partial system data');
            const result = await dataEnrichmentService.enrichChatContext('system metrics');
            (0, vitest_1.expect)(result.hasData).toBe(true);
            (0, vitest_1.expect)(result.partialFailure).toBe(true);
            (0, vitest_1.expect)(result.dataTypes).toEqual(['stoerungen']);
            (0, vitest_1.expect)(result.databaseContext).toBe('Partial system data');
        });
    });
    (0, vitest_1.describe)('Data Formatting Failures', () => {
        (0, vitest_1.it)('should handle data formatting service failure', async () => {
            // Mock successful queries but failing formatter
            mockQueryCoordinator.executeQueries.mockResolvedValue([
                {
                    dataType: 'stoerungen',
                    success: true,
                    data: { statistics: { total: 5 } },
                    summary: 'Störungen: 5 total'
                }
            ]);
            mockDataFormatter.buildMultiSourceContext.mockImplementation(() => {
                throw new Error('Data formatting failed');
            });
            const result = await dataEnrichmentService.enrichChatContext('störungen status');
            (0, vitest_1.expect)(result.hasData).toBe(true);
            (0, vitest_1.expect)(result.databaseContext).toContain('SYSTEMDATEN (Vereinfachte Darstellung)');
            (0, vitest_1.expect)(result.databaseContext).toContain('Datenformatierung war eingeschränkt');
        });
    });
    (0, vitest_1.describe)('No Intent Detection', () => {
        (0, vitest_1.it)('should handle messages with no detectable intents', async () => {
            const result = await dataEnrichmentService.enrichChatContext('hello world');
            (0, vitest_1.expect)(result.hasData).toBe(false);
            (0, vitest_1.expect)(result.detectedIntents).toHaveLength(0);
            (0, vitest_1.expect)(result.fallbackReason).toBe('NO_INTENTS_DETECTED');
            (0, vitest_1.expect)(result.databaseContext).toContain('Keine spezifischen Systemdaten');
        });
        (0, vitest_1.it)('should handle empty messages', async () => {
            const result = await dataEnrichmentService.enrichChatContext('');
            (0, vitest_1.expect)(result.hasData).toBe(false);
            (0, vitest_1.expect)(result.detectedIntents).toHaveLength(0);
            (0, vitest_1.expect)(result.fallbackReason).toBe('NO_INTENTS_DETECTED');
        });
    });
    (0, vitest_1.describe)('Critical Error Scenarios', () => {
        (0, vitest_1.it)('should handle query coordinator initialization failure', async () => {
            // Mock query coordinator to throw during execution
            mockQueryCoordinator.executeQueries.mockImplementation(() => {
                throw new Error('Query coordinator initialization failed');
            });
            const result = await dataEnrichmentService.enrichChatContext('system status');
            (0, vitest_1.expect)(result.hasData).toBe(false);
            (0, vitest_1.expect)(result.fallbackReason).toBe('CRITICAL_ERROR');
            (0, vitest_1.expect)(result.databaseContext).toContain('technischer Fehler');
        });
        (0, vitest_1.it)('should handle memory/resource exhaustion', async () => {
            // Mock out-of-memory error
            mockQueryCoordinator.executeQueries.mockImplementation(() => {
                const error = new Error('JavaScript heap out of memory');
                error.name = 'RangeError';
                throw error;
            });
            const result = await dataEnrichmentService.enrichChatContext('large data request');
            (0, vitest_1.expect)(result.hasData).toBe(false);
            (0, vitest_1.expect)(result.fallbackReason).toBe('CRITICAL_ERROR');
            (0, vitest_1.expect)(result.processingTime).toBeGreaterThan(0);
        });
    });
    (0, vitest_1.describe)('Logging and Debugging', () => {
        (0, vitest_1.it)('should log detailed error information for debugging', async () => {
            const consoleSpy = vitest_1.vi.spyOn(console, 'error').mockImplementation(() => { });
            mockQueryCoordinator.executeQueries.mockImplementation(() => {
                throw new Error('Test error for logging');
            });
            await dataEnrichmentService.enrichChatContext('test message');
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalledWith(vitest_1.expect.stringContaining('[DATA-ENRICHMENT]'), vitest_1.expect.stringContaining('Critical error enriching context'), vitest_1.expect.any(Error));
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalledWith(vitest_1.expect.stringContaining('[ERROR-DETAILS]'), vitest_1.expect.stringContaining('"errorType"'));
            consoleSpy.mockRestore();
        });
        (0, vitest_1.it)('should generate unique request IDs for tracking', async () => {
            const result1 = await dataEnrichmentService.enrichChatContext('test 1');
            const result2 = await dataEnrichmentService.enrichChatContext('test 2');
            (0, vitest_1.expect)(result1.requestId).toBeDefined();
            (0, vitest_1.expect)(result2.requestId).toBeDefined();
            (0, vitest_1.expect)(result1.requestId).not.toBe(result2.requestId);
        });
    });
    (0, vitest_1.describe)('Performance Monitoring', () => {
        (0, vitest_1.it)('should track processing time for successful operations', async () => {
            mockQueryCoordinator.executeQueries.mockResolvedValue([
                {
                    dataType: 'stoerungen',
                    success: true,
                    data: { statistics: { total: 1 } },
                    summary: 'Test data'
                }
            ]);
            mockDataFormatter.buildMultiSourceContext.mockReturnValue('Test context');
            const result = await dataEnrichmentService.enrichChatContext('störungen');
            (0, vitest_1.expect)(result.processingTime).toBeGreaterThan(0);
            (0, vitest_1.expect)(result.processingTime).toBeLessThan(1000); // Should be fast for mocked data
        });
        (0, vitest_1.it)('should track processing time for failed operations', async () => {
            mockQueryCoordinator.executeQueries.mockImplementation(() => {
                throw new Error('Test failure');
            });
            const result = await dataEnrichmentService.enrichChatContext('test');
            (0, vitest_1.expect)(result.processingTime).toBeGreaterThan(0);
            (0, vitest_1.expect)(result.fallbackReason).toBe('CRITICAL_ERROR');
        });
    });
    (0, vitest_1.describe)('Fallback Data Quality', () => {
        (0, vitest_1.it)('should provide meaningful fallback context for different intent types', async () => {
            // Test störungen fallback
            let result = await dataEnrichmentService.enrichChatContext('störungen');
            (0, vitest_1.expect)(result.databaseContext).toContain('Keine spezifischen Systemdaten');
            // Test dispatch fallback  
            result = await dataEnrichmentService.enrichChatContext('versand');
            (0, vitest_1.expect)(result.databaseContext).toContain('Keine spezifischen Systemdaten');
            // Test cutting fallback
            result = await dataEnrichmentService.enrichChatContext('ablängerei');
            (0, vitest_1.expect)(result.databaseContext).toContain('Keine spezifischen Systemdaten');
        });
        (0, vitest_1.it)('should indicate available data types when partial failure occurs', async () => {
            mockQueryCoordinator.executeQueries.mockResolvedValue([
                {
                    dataType: 'stoerungen',
                    success: true,
                    data: { statistics: { total: 1 } },
                    summary: 'Available'
                },
                {
                    dataType: 'dispatch',
                    success: false,
                    error: 'Unavailable'
                }
            ]);
            mockDataFormatter.buildMultiSourceContext.mockReturnValue('Partial data');
            const result = await dataEnrichmentService.enrichChatContext('system overview');
            (0, vitest_1.expect)(result.hasData).toBe(true);
            (0, vitest_1.expect)(result.partialFailure).toBe(true);
            (0, vitest_1.expect)(result.dataTypes).toContain('stoerungen');
            (0, vitest_1.expect)(result.dataTypes).not.toContain('dispatch');
        });
    });
});
