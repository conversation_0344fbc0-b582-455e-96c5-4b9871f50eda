import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StatCard } from '@/components/ui/stat-card';

/**
 * Lagerbestand-Statistiken Komponente
 * 
 * Zeigt wichtige Lagerbestand-Kennzahlen als Statistik-Karten an:
 * - ATRL/ARIL-Verhältnis
 * - Füllgrad ARIL
 * - Kritischer Füllgrad-Indikator
 */
export function InventoryStats() {
  const { t } = useTranslation();
  const [stats, setStats] = useState({
    atrlArilRatio: 0,
    arilFillLevel: 0,
    criticalLevel: false,
    previousAtrlArilRatio: 0,
    previousArilFillLevel: 0,
  });
  
  useEffect(() => {
    const loadData = () => {
      try {
        // Diese Daten wurden aus der Datenbank extrahiert
        // In einer echten Implementierung würden wir die Daten aus der Datenbank laden
        const currentData = {
          atrl: 85,
          aril: 92,
          fuellgrad_aril: 78.5,
        };
        
        const previousData = {
          atrl: 82,
          aril: 90,
          fuellgrad_aril: 76.2,
        };
        
        // Berechne die Kennzahlen
        const atrlArilRatio = (currentData.atrl / currentData.aril) * 100;
        const arilFillLevel = currentData.fuellgrad_aril;
        const criticalLevel = arilFillLevel > 85; // Kritischer Füllgrad bei über 85%
        
        const previousAtrlArilRatio = (previousData.atrl / previousData.aril) * 100;
        const previousArilFillLevel = previousData.fuellgrad_aril;
        
        setStats({
          atrlArilRatio,
          arilFillLevel,
          criticalLevel,
          previousAtrlArilRatio,
          previousArilFillLevel,
        });
      } catch (err) {
        // Error handling without console log
      }
    };
    
    loadData();
  }, []);
  
  // Berechne die Trends
  const ratioTrend = stats.atrlArilRatio > stats.previousAtrlArilRatio ? 'up' : 
                    stats.atrlArilRatio < stats.previousAtrlArilRatio ? 'down' : 'neutral';
                       
  const fillLevelTrend = stats.arilFillLevel > stats.previousArilFillLevel ? 'up' : 
                        stats.arilFillLevel < stats.previousArilFillLevel ? 'down' : 'neutral';
  
  // Berechne die Trendwerte in Prozent
  const ratioTrendValue = stats.previousAtrlArilRatio > 0 ? 
    `${((stats.atrlArilRatio - stats.previousAtrlArilRatio) / stats.previousAtrlArilRatio * 100).toFixed(1)}%` : '0%';
    
  const fillLevelTrendValue = stats.previousArilFillLevel > 0 ? 
    `${((stats.arilFillLevel - stats.previousArilFillLevel) / stats.previousArilFillLevel * 100).toFixed(1)}%` : '0%';
  
  return (
    <>
      <StatCard
        title={t("atrlArilRatio")}
        value={`${stats.atrlArilRatio.toFixed(1)}%`}
        description={t("atrlArilRatioDescription")}
        trend={ratioTrend}
        trendValue={ratioTrendValue}
        footer={t("comparedToPreviousDay")}
      />
      <StatCard
        title={t("arilFillLevel")}
        value={`${stats.arilFillLevel.toFixed(1)}%`}
        description={t("arilFillLevelDescription")}
        trend={fillLevelTrend}
        trendValue={fillLevelTrendValue}
        footer={t("comparedToPreviousDay")}
      />
      <StatCard
        title={t("criticalLevelIndicator")}
        value={stats.criticalLevel ? t("critical") : t("normal")}
        description={t("criticalLevelDescription")}
        className={stats.criticalLevel ? "border-red-500" : "border-green-500"}
        footer={t("basedOnCurrentFillLevel")}
      />
    </>
  );
}
