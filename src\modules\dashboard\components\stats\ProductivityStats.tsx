import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StatCard } from '@/components/ui/stat-card';

/**
 * Produktivitäts-Statistiken Komponente
 * 
 * Zeigt wichtige Produktivitätskennzahlen als Statistik-Karten an:
 * - Tonnagen pro Mitarbeiterstunde
 * - Ausgelieferte Positionen pro Mitarbeiterstunde
 */
export function ProductivityStats() {
  const { t } = useTranslation();
  const [stats, setStats] = useState({
    tonnagePerHour: 0,
    positionsPerHour: 0,
    previousTonnagePerHour: 0,
    previousPositionsPerHour: 0,
  });
  
  useEffect(() => {
    const loadData = () => {
      try {
        // Diese Daten wurden aus der Datenbank extrahiert
        // In einer echten Implementierung würden wir die Daten aus der Datenbank laden
        const currentData = {
          produzierte_tonnagen: 18.5,
          mitarbeiter_std: 160.5,
          ausgeliefert_lup: 1250,
        };
        
        const previousData = {
          produzierte_tonnagen: 17.8,
          mitarbeiter_std: 158.2,
          ausgeliefert_lup: 1180,
        };
        
        // Berechne die Kennzahlen
        const tonnagePerHour = currentData.produzierte_tonnagen / currentData.mitarbeiter_std;
        const positionsPerHour = currentData.ausgeliefert_lup / currentData.mitarbeiter_std;
        const previousTonnagePerHour = previousData.produzierte_tonnagen / previousData.mitarbeiter_std;
        const previousPositionsPerHour = previousData.ausgeliefert_lup / previousData.mitarbeiter_std;
        
        setStats({
          tonnagePerHour,
          positionsPerHour,
          previousTonnagePerHour,
          previousPositionsPerHour,
        });
      } catch (err) {
        // Error handling without console log
      }
    };
    
    loadData();
  }, []);
  
  // Berechne die Trends
  const tonnageTrend = stats.tonnagePerHour > stats.previousTonnagePerHour ? 'up' : 
                       stats.tonnagePerHour < stats.previousTonnagePerHour ? 'down' : 'neutral';
                       
  const positionsTrend = stats.positionsPerHour > stats.previousPositionsPerHour ? 'up' : 
                         stats.positionsPerHour < stats.previousPositionsPerHour ? 'down' : 'neutral';
  
  // Berechne die Trendwerte in Prozent
  const tonnageTrendValue = stats.previousTonnagePerHour > 0 ? 
    `${((stats.tonnagePerHour - stats.previousTonnagePerHour) / stats.previousTonnagePerHour * 100).toFixed(1)}%` : '0%';
    
  const positionsTrendValue = stats.previousPositionsPerHour > 0 ? 
    `${((stats.positionsPerHour - stats.previousPositionsPerHour) / stats.previousPositionsPerHour * 100).toFixed(1)}%` : '0%';
  
  return (
    <>
      <StatCard
        title={t("tonnagePerHour")}
        value={stats.tonnagePerHour.toFixed(2)}
        description={t("tonnagePerHourDescription")}
        trend={tonnageTrend}
        trendValue={tonnageTrendValue}
        footer={t("comparedToPreviousDay")}
      />
      <StatCard
        title={t("positionsPerHour")}
        value={stats.positionsPerHour.toFixed(1)}
        description={t("positionsPerHourDescription")}
        trend={positionsTrend}
        trendValue={positionsTrendValue}
        footer={t("comparedToPreviousDay")}
      />
    </>
  );
}
