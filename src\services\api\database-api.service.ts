/**
 * Database API Service
 * 
 * Service für den Abruf von Material- und Trommeldaten aus der Backend-Datenbank
 * Ersetzt die JSON-Datei-basierten Datenquellen durch API-Aufrufe
 */

// Basis-URL für API-Aufrufe - kann über Umgebungsvariablen konfiguriert werden
// Vite verwendet import.meta.env anstelle von process.env
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

/**
 * Interface für Materialdaten aus der Datenbank
 */
export interface MaterialdatenFromDB {
  MATNR: string;
  Materialkurztext: string;
  Kabeldurchmesser: number;
  ZuschlagKabeldurchmesser: number;
  Biegefaktor: number;
  Ringauslieferung: string;
  KleinsterErlauberFreiraum: number;
  Bruttogewicht: number;
}

/**
 * Interface für Trommeldaten aus der Datenbank
 */
export interface TrommeldatenFromDB {
  Trommelname: string;
  Außendurchmesser: number;
  Kerndurchmesser: number;
  Freiraum_mm: number;
  Wickelbreite_mm: number;
  MaxTragkraft_Kg: number;
  Max_Laenge: number;
  Max_Gewicht: number;
}

/**
 * Database API Service Klasse
 * Stellt Methoden für den Abruf von Material- und Trommeldaten bereit
 */
export class DatabaseApiService {
  
  /**
   * Materialdaten aus der Datenbank abrufen
   * @returns Promise mit Array von Materialdaten
   */
  static async getMaterialdaten(): Promise<MaterialdatenFromDB[]> {
    try {
      console.log('🔍 Lade Materialdaten von API...');
      
      const response = await fetch(`${API_BASE_URL}/database/materialdaten`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const response_data = await response.json();
      
      // API gibt {success: true, data: [...]} zurück, wir brauchen nur das data Array
      const data = response_data.data || [];
      console.log(`✅ ${data.length} Materialdaten von API geladen`);
      
      return data;
    } catch (error) {
      console.error('❌ Fehler beim Laden der Materialdaten von API:', error);
      throw new Error('Fehler beim Laden der Materialdaten aus der Datenbank');
    }
  }

  /**
   * Materialdaten mit Suchfunktion und Paginierung abrufen
   * @param searchTerm Suchbegriff für MATNR oder Materialkurztext
   * @param limit Maximale Anzahl der Ergebnisse (Standard: 100)
   * @param offset Offset für Paginierung (Standard: 0)
   * @returns Promise mit Array von gefilterten Materialdaten
   */
  static async searchMaterialdaten(
    searchTerm: string = '',
    limit: number = 100,
    offset: number = 0
  ): Promise<{ data: MaterialdatenFromDB[], total: number }> {
    try {
      console.log(`🔍 Suche Materialdaten: "${searchTerm}", Limit: ${limit}, Offset: ${offset}`);
      
      // URL-Parameter für Suche und Paginierung
      const params = new URLSearchParams({
        search: searchTerm,
        limit: limit.toString(),
        offset: offset.toString()
      });
      
      const response = await fetch(`${API_BASE_URL}/database/materialdaten/search?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        // Fallback: Wenn Search-Endpoint nicht existiert, verwende normale API mit Client-seitiger Filterung
        console.warn('Search-Endpoint nicht verfügbar, verwende Fallback...');
        const allData = await this.getMaterialdaten();
        
        // Client-seitige Filterung
        const filtered = allData.filter(material => 
          material.MATNR.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (material.Materialkurztext && material.Materialkurztext.toLowerCase().includes(searchTerm.toLowerCase()))
        );
        
        // Client-seitige Paginierung
        const paginatedData = filtered.slice(offset, offset + limit);
        
        return {
          data: paginatedData,
          total: filtered.length
        };
      }
      
      const response_data = await response.json();
      const data = response_data.data || [];
      const total = response_data.total || data.length;
      
      console.log(`✅ ${data.length} von ${total} Materialdaten gefunden`);
      
      return { data, total };
    } catch (error) {
      console.error('❌ Fehler beim Suchen der Materialdaten:', error);
      // Fallback zu normaler getMaterialdaten Methode
      const allData = await this.getMaterialdaten();
      return { data: allData.slice(0, limit), total: allData.length };
    }
  }
  
  /**
   * Trommeldaten aus der Datenbank abrufen
   * @returns Promise mit Array von Trommeldaten
   */
  static async getTrommeldaten(): Promise<TrommeldatenFromDB[]> {
    try {
      console.log('🔍 Lade Trommeldaten von API...');
      
      const response = await fetch(`${API_BASE_URL}/database/trommeldaten`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const response_data = await response.json();
      
      // API gibt {success: true, data: [...]} zurück, wir brauchen nur das data Array
      const data = response_data.data || [];
      console.log(`✅ ${data.length} Trommeldaten von API geladen`);
      
      return data;
    } catch (error) {
      console.error('❌ Fehler beim Laden der Trommeldaten von API:', error);
      throw new Error('Fehler beim Laden der Trommeldaten aus der Datenbank');
    }
  }
  
  /**
   * Einzelnes Kabelmaterial nach MATNR suchen
   * @param matnr - Materialnummer
   * @returns Promise mit Materialdaten oder null
   */
  static async findKabelmaterial(matnr: string): Promise<MaterialdatenFromDB | null> {
    try {
      const materialien = await this.getMaterialdaten();
      return materialien.find(m => m.MATNR === matnr) || null;
    } catch (error) {
      console.error('❌ Fehler beim Suchen des Kabelmaterials:', error);
      return null;
    }
  }
  
  /**
   * Einzelne Trommel nach Name suchen
   * @param trommelname - Name der Trommel
   * @returns Promise mit Trommeldaten oder null
   */
  static async findTrommel(trommelname: string): Promise<TrommeldatenFromDB | null> {
    try {
      const trommeln = await this.getTrommeldaten();
      return trommeln.find(t => t.Trommelname === trommelname) || null;
    } catch (error) {
      console.error('❌ Fehler beim Suchen der Trommel:', error);
      return null;
    }
  }
}

export default DatabaseApiService;