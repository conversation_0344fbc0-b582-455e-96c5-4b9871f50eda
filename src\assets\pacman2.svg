<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   viewBox="0 0 100 100"
   preserveAspectRatio="xMidYMid"
   width="200"
   height="200"
   style="shape-rendering: auto; display: block; background: transparent;"
   version="1.1"
   id="svg6"
   sodipodi:docname="pacman2.svg"
   inkscape:version="1.4 (86a8ad7, 2024-10-11)"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <defs
     id="defs6" />
  <sodipodi:namedview
     id="namedview6"
     pagecolor="#505050"
     bordercolor="#eeeeee"
     borderopacity="1"
     inkscape:showpageshadow="0"
     inkscape:pageopacity="0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#505050"
     inkscape:zoom="4.035"
     inkscape:cx="42.874845"
     inkscape:cy="108.17844"
     inkscape:window-width="1920"
     inkscape:window-height="1009"
     inkscape:window-x="1912"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg6" />
  <g
     id="g6">
    <g
       id="g3">
      <circle
         fill="#ff7a05"
         r="4"
         cy="50"
         cx="60"
         id="circle1">
        <animate
           begin="-0.67s"
           keyTimes="0;1"
           values="95;35"
           dur="1s"
           repeatCount="indefinite"
           attributeName="cx" />
        <animate
           begin="-0.67s"
           keyTimes="0;0.2;1"
           values="0;1;1"
           dur="1s"
           repeatCount="indefinite"
           attributeName="fill-opacity" />
      </circle>
      <circle
         fill="#ff7a05"
         r="4"
         cy="50"
         cx="60"
         id="circle2">
        <animate
           begin="-0.33s"
           keyTimes="0;1"
           values="95;35"
           dur="1s"
           repeatCount="indefinite"
           attributeName="cx" />
        <animate
           begin="-0.33s"
           keyTimes="0;0.2;1"
           values="0;1;1"
           dur="1s"
           repeatCount="indefinite"
           attributeName="fill-opacity" />
      </circle>
      <circle
         fill="#ff7a05"
         r="4"
         cy="50"
         cx="60"
         id="circle3">
        <animate
           begin="0s"
           keyTimes="0;1"
           values="95;35"
           dur="1s"
           repeatCount="indefinite"
           attributeName="cx" />
        <animate
           begin="0s"
           keyTimes="0;0.2;1"
           values="0;1;1"
           dur="1s"
           repeatCount="indefinite"
           attributeName="fill-opacity" />
      </circle>
    </g>
    <g
       transform="translate(-15 0)"
       id="g4">
      <path
         fill="none"
         stroke="#080808"
         stroke-width="2"
         d="M 20,50 C 20,66.568554 33.431458,79.999987 50,79.999987 66.568542,79.999987 80,66.568554 80,50 H 50"
         id="path3"
         sodipodi:nodetypes="cscc">
        <animateTransform
           keyTimes="0;0.5;1"
           values="0 50 50;45 50 50;0 50 50"
           dur="1s"
           repeatCount="indefinite"
           type="rotate"
           attributeName="transform" />
      </path>
      <path
         fill="none"
         stroke="#080808"
         stroke-width="2"
         d="M 20,50 C 20,33.431446 33.431458,20.000013 50,20.000013 66.568542,20.000013 80,33.431446 80,50 m -0.123916,0 H 50"
         id="path4"
         sodipodi:nodetypes="csccc">
        <animateTransform
           keyTimes="0;0.5;1"
           values="0 50 50;-45 50 50;0 50 50"
           dur="1s"
           repeatCount="indefinite"
           type="rotate"
           attributeName="transform" />
      </path>
    </g>
    <g
       id="g5" />
  </g>
  <!-- [ldio] generated by https://loading.io -->
  <circle
     style="fill:#0a0a0a;stroke:#000000;stroke-width:0;stroke-linecap:round;fill-opacity:1"
     id="path6"
     cx="35.344486"
     cy="32.289963"
     r="4.5" />
</svg>
