import { bereitschaftsService } from './bereitschaftsService';
import { BereitschaftsPerson, BereitschaftsWoche } from '@/types/bereitschafts';

/**
 * Service für die automatische Zuweisung der Bereitschaftsperson bei Störungsmeldungen
 * 
 * Dieser Service ermittelt die aktuell diensthabende Person und stellt deren Daten
 * für die automatische Zuweisung im StoerungsForm zur Verfügung.
 */
class BereitschaftsAssignmentService {
  
  /**
   * Ermittelt die aktuell diensthabende Person
   * 
   * @returns Promise<BereitschaftsAssignmentData | null> - Daten der aktuellen Bereitschaftsperson oder null
   */
  async getAktuelleBereitschaftsperson(): Promise<BereitschaftsAssignmentData | null> {
    try {
      // Hole die aktuelle Bereitschaftswoche mit Person-Daten
      const aktuelleBereitschaft = await bereitschaftsService.getAktuelleBereitschaft();
      
      if (!aktuelleBereitschaft || !aktuelleBereitschaft.person) {
        console.warn('Keine aktuelle Bereitschaftsperson gefunden');
        return null;
      }

      // Prüfe ob die Person aktiv ist
      if (!aktuelleBereitschaft.person.aktiv) {
        console.warn('Aktuelle Bereitschaftsperson ist nicht aktiv:', aktuelleBereitschaft.person.name);
        return null;
      }

      // Erstelle Assignment-Daten
      const assignmentData: BereitschaftsAssignmentData = {
        personId: aktuelleBereitschaft.person.id,
        name: aktuelleBereitschaft.person.name,
        email: aktuelleBereitschaft.person.email,
        telefon: aktuelleBereitschaft.person.telefon,
        abteilung: aktuelleBereitschaft.person.abteilung,
        bereitschaftsZeitraum: {
          wochenStart: aktuelleBereitschaft.wochenStart,
          wochenEnde: aktuelleBereitschaft.wochenEnde,
          von: aktuelleBereitschaft.von,
          bis: aktuelleBereitschaft.bis
        }
      };

      console.log('Aktuelle Bereitschaftsperson ermittelt:', assignmentData.name);
      return assignmentData;
      
    } catch (error) {
      console.error('Fehler beim Ermitteln der aktuellen Bereitschaftsperson:', error);
      return null;
    }
  }

  /**
   * Erstellt eine E-Mail-Vorlage für die Bereitschaftsperson
   * 
   * @param assignmentData - Daten der Bereitschaftsperson
   * @param stoerungData - Daten der Störungsmeldung
   * @returns EmailTemplate - E-Mail-Vorlage
   */
  createEmailTemplate(
    assignmentData: BereitschaftsAssignmentData,
    stoerungData: StoerungEmailData
  ): EmailTemplate {
    const subject = `[STÖRUNG] ${stoerungData.severity} - ${stoerungData.title}`;
    
    const body = `
Hallo ${assignmentData.name},

Ihnen wurde eine neue Störungsmeldung zugewiesen:

--- STÖRUNGSDETAILS ---
Titel: ${stoerungData.title}
Schweregrad: ${this.getSeverityLabel(stoerungData.severity)}
Kategorie: ${stoerungData.category || 'Nicht angegeben'}
Betroffenes System: ${stoerungData.affected_system || 'Nicht angegeben'}
Standort: ${stoerungData.location || 'Nicht angegeben'}
Gemeldet von: ${stoerungData.reported_by || 'Unbekannt'}

${stoerungData.description ? `Beschreibung:\n${stoerungData.description}\n\n` : ''}
--- BEREITSCHAFTSINFO ---
Störung gemeldet am: ${new Date().toLocaleString('de-DE', { 
  year: 'numeric', 
  month: '2-digit', 
  day: '2-digit', 
  hour: '2-digit', 
  minute: '2-digit' 
})}
Ihre Kontaktdaten:
- Telefon: ${assignmentData.telefon}
- E-Mail: ${assignmentData.email}
- Abteilung: ${assignmentData.abteilung}

Bitte bearbeiten Sie diese Störung entsprechend der definierten Prozesse.

Mit freundlichen Grüßen
Ihr Leitstand-System

--- 
Diese E-Mail wurde automatisch generiert.
    `.trim();

    return {
      to: assignmentData.email,
      subject,
      body,
      assignedPerson: assignmentData
    };
  }

  /**
   * Formatiert den Schweregrad für die E-Mail
   */
  private getSeverityLabel(severity: string): string {
    const severityLabels: Record<string, string> = {
      'LOW': 'Niedrig',
      'MEDIUM': 'Mittel', 
      'HIGH': 'Hoch',
      'CRITICAL': 'Kritisch'
    };
    return severityLabels[severity] || severity;
  }

  // Formatierung der Bereitschaftszeit wurde entfernt - wird durch aktuelles Datum ersetzt

  /**
   * Validiert ob eine automatische Zuweisung möglich ist
   */
  async validateAutoAssignment(): Promise<ValidationResult> {
    try {
      const bereitschaftsperson = await this.getAktuelleBereitschaftsperson();
      
      if (!bereitschaftsperson) {
        return {
          isValid: false,
          message: 'Keine aktuelle Bereitschaftsperson verfügbar'
        };
      }

      // Prüfe E-Mail-Format
      if (!this.isValidEmail(bereitschaftsperson.email)) {
        return {
          isValid: false,
          message: `Ungültige E-Mail-Adresse für ${bereitschaftsperson.name}: ${bereitschaftsperson.email}`
        };
      }

      return {
        isValid: true,
        message: `Automatische Zuweisung an ${bereitschaftsperson.name} möglich`,
        assignmentData: bereitschaftsperson
      };
      
    } catch (error) {
      return {
        isValid: false,
        message: `Fehler bei der Validierung: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`
      };
    }
  }

  /**
   * Einfache E-Mail-Validierung
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

// --- TypeScript Interfaces ---

/**
 * Daten für die automatische Zuweisung der Bereitschaftsperson
 */
export interface BereitschaftsAssignmentData {
  personId: number;
  name: string;
  email: string;
  telefon: string;
  abteilung: string;
  bereitschaftsZeitraum: BereitschaftsZeitraum;
}

/**
 * Zeitraum der Bereitschaft
 */
export interface BereitschaftsZeitraum {
  wochenStart: string;
  wochenEnde: string;
  von: string;
  bis: string;
}

/**
 * Störungsdaten für E-Mail-Template
 */
export interface StoerungEmailData {
  title: string;
  description?: string;
  severity: string;
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
}

/**
 * E-Mail-Template
 */
export interface EmailTemplate {
  to: string;
  subject: string;
  body: string;
  assignedPerson: BereitschaftsAssignmentData;
}

/**
 * Validierungsergebnis
 */
export interface ValidationResult {
  isValid: boolean;
  message: string;
  assignmentData?: BereitschaftsAssignmentData;
}

// Singleton-Export
export const bereitschaftsAssignmentService = new BereitschaftsAssignmentService();
export default bereitschaftsAssignmentService;