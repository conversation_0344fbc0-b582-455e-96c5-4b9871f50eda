/**
 * AI Base Service
 * 
 * Extended base service for AI operations with specialized error handling,
 * caching, and performance monitoring for AI workloads.
 */

import { BaseService, ServiceConfig, ServiceStatus } from '@/services/base.service';
import { AIServiceError } from '../types';

/**
 * Enhanced service configuration for AI operations
 */
export interface AIServiceConfig extends ServiceConfig {
  vectorDimensions?: number;
  embeddingModel?: string;
  maxContextLength?: number;
  similarityThreshold?: number;
  enableVectorCache?: boolean;
  vectorCacheTTL?: number;
}

/**
 * Enhanced service status for AI operations
 */
export interface AIServiceStatus extends ServiceStatus {
  performance?: {
    averageResponseTime: number;
    successRate: number;
    totalRequests: number;
    cacheHitRate?: number;
  };
  vectorStats?: {
    totalVectors: number;
    lastIndexUpdate: Date;
  };
}

/**
 * Base class for all AI services with common functionality
 */
export abstract class AIBaseService extends BaseService {
  protected aiConfig: AIServiceConfig;
  protected performanceMetrics: {
    totalRequests: number;
    successfulRequests: number;
    totalResponseTime: number;
    cacheHits: number;
    cacheMisses: number;
  };

  constructor(config: AIServiceConfig = {}) {
    super(config);
    this.aiConfig = {
      vectorDimensions: 1536, // OpenAI embedding dimensions
      embeddingModel: 'text-embedding-3-small',
      maxContextLength: 8000,
      similarityThreshold: 0.7,
      enableVectorCache: true,
      vectorCacheTTL: 3600000, // 1 hour
      ...config
    };

    this.performanceMetrics = {
      totalRequests: 0,
      successfulRequests: 0,
      totalResponseTime: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
  }

  /**
   * Enhanced health check for AI services
   */
  async healthCheck(): Promise<AIServiceStatus> {
    const baseStatus = await super.healthCheck();
    
    const aiStatus: AIServiceStatus = {
      ...baseStatus,
      performance: this.getPerformanceMetrics(),
      vectorStats: await this.getVectorStats()
    };

    return aiStatus;
  }

  /**
   * Get performance metrics
   */
  protected getPerformanceMetrics() {
    const { totalRequests, successfulRequests, totalResponseTime, cacheHits, cacheMisses } = this.performanceMetrics;
    
    return {
      averageResponseTime: totalRequests > 0 ? totalResponseTime / totalRequests : 0,
      successRate: totalRequests > 0 ? successfulRequests / totalRequests : 1,
      totalRequests,
      cacheHitRate: (cacheHits + cacheMisses) > 0 ? cacheHits / (cacheHits + cacheMisses) : 0
    };
  }

  /**
   * Get vector statistics (override in vector-enabled services)
   */
  protected async getVectorStats(): Promise<{ totalVectors: number; lastIndexUpdate: Date } | undefined> {
    return undefined;
  }

  /**
   * Enhanced error handling for AI operations
   */
  protected async handleAIError<T>(
    operation: () => Promise<T>,
    fallback: () => Promise<T>,
    errorType: AIServiceError
  ): Promise<T> {
    const startTime = Date.now();
    this.performanceMetrics.totalRequests++;

    try {
      const result = await operation();
      this.performanceMetrics.successfulRequests++;
      this.performanceMetrics.totalResponseTime += Date.now() - startTime;
      return result;
    } catch (error) {
      this.performanceMetrics.totalResponseTime += Date.now() - startTime;
      this.log(`AI Service Error [${errorType}]:`, error);
      
      // Try fallback if available
      try {
        const fallbackResult = await fallback();
        this.performanceMetrics.successfulRequests++;
        return fallbackResult;
      } catch (fallbackError) {
        this.lastError = error instanceof Error ? error : new Error(String(error));
        throw new Error(`${errorType}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }

  /**
   * Cache management for AI operations
   */
  protected async getCachedResult<T>(
    cacheKey: string,
    operation: () => Promise<T>,
    ttl: number = this.aiConfig.vectorCacheTTL || 3600000
  ): Promise<T> {
    if (!this.aiConfig.enableVectorCache) {
      return await operation();
    }

    // Simple in-memory cache implementation
    // In production, this should use a proper cache service
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      this.performanceMetrics.cacheHits++;
      return cached;
    }

    this.performanceMetrics.cacheMisses++;
    const result = await operation();
    this.setCache(cacheKey, result, ttl);
    return result;
  }

  /**
   * Simple cache implementation (override with proper cache service)
   */
  private cache = new Map<string, { value: any; expires: number }>();

  private getFromCache(key: string): any {
    const cached = this.cache.get(key);
    if (cached && cached.expires > Date.now()) {
      return cached.value;
    }
    if (cached) {
      this.cache.delete(key);
    }
    return null;
  }

  private setCache(key: string, value: any, ttl: number): void {
    this.cache.set(key, {
      value,
      expires: Date.now() + ttl
    });
  }

  /**
   * Validate AI service configuration
   */
  protected validateAIConfig(): void {
    if (this.aiConfig.vectorDimensions && this.aiConfig.vectorDimensions <= 0) {
      throw new Error('Vector dimensions must be positive');
    }
    
    if (this.aiConfig.maxContextLength && this.aiConfig.maxContextLength <= 0) {
      throw new Error('Max context length must be positive');
    }
    
    if (this.aiConfig.similarityThreshold && 
        (this.aiConfig.similarityThreshold < 0 || this.aiConfig.similarityThreshold > 1)) {
      throw new Error('Similarity threshold must be between 0 and 1');
    }
  }

  /**
   * Initialize AI service with validation
   */
  async initialize(config?: AIServiceConfig): Promise<void> {
    if (config) {
      this.aiConfig = { ...this.aiConfig, ...config };
    }
    
    this.validateAIConfig();
    await super.initialize(this.aiConfig);
  }

  /**
   * Cleanup AI service resources
   */
  async destroy(): Promise<void> {
    // Clear cache
    this.cache.clear();
    
    // Reset metrics
    this.performanceMetrics = {
      totalRequests: 0,
      successfulRequests: 0,
      totalResponseTime: 0,
      cacheHits: 0,
      cacheMisses: 0
    };

    await super.destroy();
  }
}