/**
 * AI Service Coordinator
 * 
 * Orchestrates and coordinates multiple AI operations,
 * manages service dependencies, handles resource allocation,
 * and provides comprehensive monitoring and performance tracking.
 */

import { AIBaseService, AIServiceConfig, AIServiceStatus } from '../base/AIBaseService';
import { AIServiceError } from '../types';

export interface AIOperation {
  id: string;
  type: string;
  serviceId: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  dependencies?: string[];
  payload: any;
  timeout?: number;
  retries?: number;
  queueTime?: number; // Timestamp when operation was queued
}

export interface AIOperationResult {
  operationId: string;
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
  serviceUsed: string;
  resourceUsage?: {
    memoryUsed: number;
    cpuUsage: number;
    startTime: Date;
    endTime: Date;
  };
  retryCount?: number;
  queueTime?: number;
}

export interface AIServiceDependency {
  serviceId: string;
  dependsOn: string[];
  optional?: boolean;
}

export interface ResourceUsage {
  memoryUsage: number; // in MB
  cpuUsage: number; // percentage
  timestamp: Date;
}

export interface PerformanceMetrics {
  operationId: string;
  serviceId: string;
  executionTime: number;
  success: boolean;
  resourceUsage: ResourceUsage;
  timestamp: Date;
}

export interface CoordinatorStatus extends AIServiceStatus {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  averageExecutionTime: number;
  queueLength: number;
  runningOperations: number;
  resourceUsage: ResourceUsage;
  services: Record<string, AIServiceStatus>;
  performanceHistory: PerformanceMetrics[];
}

export interface AICoordinatorConfig extends AIServiceConfig {
  maxConcurrentOperations?: number;
  defaultTimeout?: number;
  defaultRetries?: number;
  enableQueueing?: boolean;
  queueMaxSize?: number;
  enableResourceMonitoring?: boolean;
  resourceCheckInterval?: number;
  maxMemoryUsage?: number; // in MB
  maxCpuUsage?: number; // percentage
  enablePerformanceTracking?: boolean;
  performanceHistorySize?: number;
}

/**
 * AI Service Coordinator for managing multiple AI operations
 */
export class AIServiceCoordinator extends AIBaseService {
  readonly serviceName = 'AIServiceCoordinator';

  private services: Map<string, AIBaseService> = new Map();
  private operationQueue: AIOperation[] = [];
  private runningOperations: Map<string, Promise<AIOperationResult>> = new Map();
  private dependencies: Map<string, AIServiceDependency> = new Map();
  private coordinatorConfig: AICoordinatorConfig;
  private performanceHistory: PerformanceMetrics[] = [];
  private resourceMonitoringInterval?: NodeJS.Timeout;
  private operationStats = {
    total: 0,
    successful: 0,
    failed: 0,
    totalExecutionTime: 0
  };

  constructor(config: AICoordinatorConfig = {}) {
    super(config);
    this.coordinatorConfig = {
      maxConcurrentOperations: 5,
      defaultTimeout: 30000, // 30 seconds
      defaultRetries: 3,
      enableQueueing: true,
      queueMaxSize: 100,
      enableResourceMonitoring: true,
      resourceCheckInterval: 5000, // 5 seconds
      maxMemoryUsage: 1024, // 1GB
      maxCpuUsage: 80, // 80%
      enablePerformanceTracking: true,
      performanceHistorySize: 1000,
      ...config
    };
  }

  /**
   * Register an AI service with the coordinator
   */
  registerService(serviceId: string, service: AIBaseService, dependency?: AIServiceDependency): void {
    this.services.set(serviceId, service);

    if (dependency) {
      this.dependencies.set(serviceId, dependency);
    }

    this.log(`Registered AI service: ${serviceId}`);
  }

  /**
   * Unregister an AI service
   */
  unregisterService(serviceId: string): void {
    this.services.delete(serviceId);
    this.dependencies.delete(serviceId);
    this.log(`Unregistered AI service: ${serviceId}`);
  }

  /**
   * Execute a single AI operation with resource monitoring
   */
  async executeOperation(operation: AIOperation): Promise<AIOperationResult> {
    const startTime = Date.now();
    const queueTime = operation.queueTime || 0;
    let retryCount = 0;

    try {
      // Check resource availability before execution
      if (this.coordinatorConfig.enableResourceMonitoring) {
        await this.checkResourceAvailability();
      }

      // Validate operation
      this.validateOperation(operation);

      // Check dependencies
      await this.checkDependencies(operation.serviceId);

      // Get service
      const service = this.services.get(operation.serviceId);
      if (!service) {
        throw new Error(`Service not found: ${operation.serviceId}`);
      }

      // Track operation start
      this.runningOperations.set(operation.id, Promise.resolve({} as AIOperationResult));

      // Execute with timeout and retries
      const result = await this.executeWithRetries(service, operation);
      retryCount = operation.retries || 0;

      const executionTime = Date.now() - startTime;
      const resourceUsage = this.getCurrentResourceUsage();

      // Update statistics
      this.operationStats.total++;
      this.operationStats.successful++;
      this.operationStats.totalExecutionTime += executionTime;

      // Track performance if enabled
      if (this.coordinatorConfig.enablePerformanceTracking) {
        this.trackPerformance({
          operationId: operation.id,
          serviceId: operation.serviceId,
          executionTime,
          success: true,
          resourceUsage,
          timestamp: new Date()
        });
      }

      const operationResult: AIOperationResult = {
        operationId: operation.id,
        success: true,
        result,
        executionTime,
        serviceUsed: operation.serviceId,
        resourceUsage: {
          memoryUsed: resourceUsage.memoryUsage,
          cpuUsage: resourceUsage.cpuUsage,
          startTime: new Date(startTime),
          endTime: new Date()
        },
        retryCount,
        queueTime
      };

      return operationResult;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const resourceUsage = this.getCurrentResourceUsage();

      // Update statistics
      this.operationStats.total++;
      this.operationStats.failed++;
      this.operationStats.totalExecutionTime += executionTime;

      // Track performance if enabled
      if (this.coordinatorConfig.enablePerformanceTracking) {
        this.trackPerformance({
          operationId: operation.id,
          serviceId: operation.serviceId,
          executionTime,
          success: false,
          resourceUsage,
          timestamp: new Date()
        });
      }

      this.log(`Operation failed: ${operation.id} - ${errorMessage}`);

      return {
        operationId: operation.id,
        success: false,
        error: errorMessage,
        executionTime,
        serviceUsed: operation.serviceId,
        resourceUsage: {
          memoryUsed: resourceUsage.memoryUsage,
          cpuUsage: resourceUsage.cpuUsage,
          startTime: new Date(startTime),
          endTime: new Date()
        },
        retryCount,
        queueTime
      };
    } finally {
      // Remove from running operations
      this.runningOperations.delete(operation.id);
    }
  }

  /**
   * Execute multiple operations with coordination
   */
  async executeOperations(operations: AIOperation[]): Promise<AIOperationResult[]> {
    // Sort operations by priority and dependencies
    const sortedOperations = this.sortOperationsByPriority(operations);

    // Execute operations respecting concurrency limits
    const results: AIOperationResult[] = [];
    const batches = this.createExecutionBatches(sortedOperations);

    for (const batch of batches) {
      const batchPromises = batch.map(operation => this.executeOperation(operation));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * Queue an operation for later execution
   */
  queueOperation(operation: AIOperation): void {
    if (!this.coordinatorConfig.enableQueueing) {
      throw new Error('Queueing is disabled');
    }

    if (this.operationQueue.length >= (this.coordinatorConfig.queueMaxSize || 100)) {
      throw new Error('Operation queue is full');
    }

    // Add queue timestamp for tracking queue time
    const queuedOperation = {
      ...operation,
      queueTime: Date.now()
    };

    this.operationQueue.push(queuedOperation);
    this.log(`Queued operation: ${operation.id}`);
  }

  /**
   * Process queued operations
   */
  async processQueue(): Promise<AIOperationResult[]> {
    if (this.operationQueue.length === 0) {
      return [];
    }

    const operations = [...this.operationQueue];
    this.operationQueue = [];

    // Calculate queue time for each operation
    const now = Date.now();
    const operationsWithQueueTime = operations.map(op => ({
      ...op,
      queueTime: op.queueTime ? now - op.queueTime : 0
    }));

    return await this.executeOperations(operationsWithQueueTime);
  }

  /**
   * Get comprehensive coordinator status including all registered services
   */
  async healthCheck(): Promise<CoordinatorStatus> {
    const baseStatus = await super.healthCheck();

    // Check all registered services
    const serviceStatuses: Record<string, AIServiceStatus> = {};

    for (const [serviceId, service] of this.services) {
      try {
        serviceStatuses[serviceId] = await service.healthCheck();
      } catch (error) {
        serviceStatuses[serviceId] = {
          isHealthy: false,
          isInitialized: false,
          lastError: error instanceof Error ? error : new Error(String(error)),
          lastChecked: new Date()
        };
      }
    }

    const resourceUsage = this.getCurrentResourceUsage();
    const averageExecutionTime = this.operationStats.total > 0
      ? this.operationStats.totalExecutionTime / this.operationStats.total
      : 0;

    return {
      // Include all properties from AIServiceStatus
      ...baseStatus,
      // Add coordinator-specific properties
      totalOperations: this.operationStats.total,
      successfulOperations: this.operationStats.successful,
      failedOperations: this.operationStats.failed,
      averageExecutionTime,
      queueLength: this.operationQueue.length,
      runningOperations: this.runningOperations.size,
      resourceUsage,
      services: serviceStatuses,
      performanceHistory: this.getRecentPerformanceHistory()
    };
  }

  /**
   * Get queue status
   */
  getQueueStatus() {
    return {
      queueLength: this.operationQueue.length,
      runningOperations: this.runningOperations.size,
      maxConcurrent: this.coordinatorConfig.maxConcurrentOperations || 5,
      queueEnabled: this.coordinatorConfig.enableQueueing || false
    };
  }

  /**
   * Cancel a running operation
   */
  cancelOperation(operationId: string): boolean {
    if (this.runningOperations.has(operationId)) {
      // In a real implementation, this would cancel the actual operation
      this.runningOperations.delete(operationId);
      this.log(`Cancelled operation: ${operationId}`);
      return true;
    }
    return false;
  }

  /**
   * Clear the operation queue
   */
  clearQueue(): void {
    const queueLength = this.operationQueue.length;
    this.operationQueue = [];
    this.log(`Cleared operation queue (${queueLength} operations)`);
  }

  /**
   * Get detailed orchestration metrics
   */
  async getOrchestrationMetrics(): Promise<{
    coordinator: {
      totalOperations: number;
      successRate: number;
      averageExecutionTime: number;
      queueLength: number;
      runningOperations: number;
      resourceUsage: ResourceUsage;
    };
    services: Record<string, {
      isHealthy: boolean;
      totalOperations: number;
      averageResponseTime: number;
      successRate: number;
      lastError?: string;
    }>;
    performance: {
      averageExecutionTime: number;
      operationsPerMinute: number;
      resourceEfficiency: number;
      bottlenecks: string[];
    };
  }> {
    const resourceUsage = this.getCurrentResourceUsage();
    const performanceAnalytics = this.getPerformanceAnalytics();

    // Identify bottlenecks
    const bottlenecks: string[] = [];
    if (this.operationQueue.length > 10) {
      bottlenecks.push('High queue length');
    }
    if (resourceUsage.memoryUsage > (this.coordinatorConfig.maxMemoryUsage || 1024) * 0.8) {
      bottlenecks.push('High memory usage');
    }
    if (resourceUsage.cpuUsage > (this.coordinatorConfig.maxCpuUsage || 80) * 0.8) {
      bottlenecks.push('High CPU usage');
    }
    if (performanceAnalytics.successRate < 0.9) {
      bottlenecks.push('Low success rate');
    }

    // Get service metrics
    const serviceMetrics: Record<string, {
      isHealthy: boolean;
      totalOperations: number;
      averageResponseTime: number;
      successRate: number;
      lastError?: string;
    }> = {};

    // Get health status for all services concurrently
    const serviceHealthPromises = Array.from(this.services.entries()).map(async ([serviceId, service]) => {
      try {
        const health = await service.healthCheck();
        return { serviceId, health };
      } catch (error) {
        return {
          serviceId,
          health: {
            isHealthy: false,
            lastError: error instanceof Error ? error : new Error(String(error))
          }
        };
      }
    });

    const serviceHealthResults = await Promise.all(serviceHealthPromises);

    // Build service metrics
    for (const { serviceId, health } of serviceHealthResults) {
      const servicePerf = performanceAnalytics.servicePerformance[serviceId];
      
      serviceMetrics[serviceId] = {
        isHealthy: health.isHealthy,
        totalOperations: servicePerf?.totalOperations || 0,
        averageResponseTime: servicePerf?.averageTime || 0,
        successRate: servicePerf?.successRate || 1,
        lastError: health.lastError?.message
      };
    }

    return {
      coordinator: {
        totalOperations: this.operationStats.total,
        successRate: this.operationStats.total > 0
          ? this.operationStats.successful / this.operationStats.total
          : 1,
        averageExecutionTime: this.operationStats.total > 0
          ? this.operationStats.totalExecutionTime / this.operationStats.total
          : 0,
        queueLength: this.operationQueue.length,
        runningOperations: this.runningOperations.size,
        resourceUsage
      },
      services: serviceMetrics,
      performance: {
        averageExecutionTime: performanceAnalytics.averageExecutionTime,
        operationsPerMinute: performanceAnalytics.operationsPerMinute,
        resourceEfficiency: performanceAnalytics.resourceEfficiency,
        bottlenecks
      }
    };
  }

  // Resource monitoring and performance tracking methods

  /**
   * Get current resource usage
   */
  private getCurrentResourceUsage(): ResourceUsage {
    // In a real implementation, this would use actual system monitoring
    // For now, we'll simulate reasonable resource usage for testing
    const memoryUsage = process.memoryUsage();

    return {
      memoryUsage: Math.round(memoryUsage.heapUsed / 1024 / 1024), // Convert to MB
      cpuUsage: Math.random() * 50 + 10, // Simulated CPU usage between 10-60%
      timestamp: new Date()
    };
  }

  /**
   * Check if resources are available for operation execution
   */
  private async checkResourceAvailability(): Promise<void> {
    const resourceUsage = this.getCurrentResourceUsage();

    if (this.coordinatorConfig.maxMemoryUsage &&
      resourceUsage.memoryUsage > this.coordinatorConfig.maxMemoryUsage) {
      throw new Error(`Memory usage (${resourceUsage.memoryUsage}MB) exceeds limit (${this.coordinatorConfig.maxMemoryUsage}MB)`);
    }

    if (this.coordinatorConfig.maxCpuUsage &&
      resourceUsage.cpuUsage > this.coordinatorConfig.maxCpuUsage) {
      throw new Error(`CPU usage (${resourceUsage.cpuUsage}%) exceeds limit (${this.coordinatorConfig.maxCpuUsage}%)`);
    }
  }

  /**
   * Track performance metrics
   */
  private trackPerformance(metrics: PerformanceMetrics): void {
    this.performanceHistory.push(metrics);

    // Maintain history size limit
    const maxSize = this.coordinatorConfig.performanceHistorySize || 1000;
    if (this.performanceHistory.length > maxSize) {
      this.performanceHistory = this.performanceHistory.slice(-maxSize);
    }
  }

  /**
   * Get recent performance history
   */
  private getRecentPerformanceHistory(limit: number = 100): PerformanceMetrics[] {
    return this.performanceHistory.slice(-limit);
  }

  /**
   * Start resource monitoring
   */
  private startResourceMonitoring(): void {
    if (!this.coordinatorConfig.enableResourceMonitoring) return;

    const interval = this.coordinatorConfig.resourceCheckInterval || 5000;

    this.resourceMonitoringInterval = setInterval(() => {
      const resourceUsage = this.getCurrentResourceUsage();

      // Log resource usage if it's high
      if (resourceUsage.memoryUsage > (this.coordinatorConfig.maxMemoryUsage || 1024) * 0.8) {
        this.log(`High memory usage detected: ${resourceUsage.memoryUsage}MB`);
      }

      if (resourceUsage.cpuUsage > (this.coordinatorConfig.maxCpuUsage || 80) * 0.8) {
        this.log(`High CPU usage detected: ${resourceUsage.cpuUsage}%`);
      }
    }, interval);
  }

  /**
   * Stop resource monitoring
   */
  private stopResourceMonitoring(): void {
    if (this.resourceMonitoringInterval) {
      clearInterval(this.resourceMonitoringInterval);
      this.resourceMonitoringInterval = undefined;
    }
  }

  /**
   * Get performance analytics
   */
  getPerformanceAnalytics(): {
    averageExecutionTime: number;
    successRate: number;
    operationsPerMinute: number;
    resourceEfficiency: number;
    servicePerformance: Record<string, {
      averageTime: number;
      successRate: number;
      totalOperations: number;
    }>;
  } {
    const recentHistory = this.getRecentPerformanceHistory();
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    const recentOperations = recentHistory.filter(
      metric => metric.timestamp.getTime() > oneMinuteAgo
    );

    const averageExecutionTime = recentHistory.length > 0
      ? recentHistory.reduce((sum, metric) => sum + metric.executionTime, 0) / recentHistory.length
      : 0;

    const successRate = recentHistory.length > 0
      ? recentHistory.filter(metric => metric.success).length / recentHistory.length
      : 1;

    const operationsPerMinute = recentOperations.length;

    const averageResourceUsage = recentHistory.length > 0
      ? recentHistory.reduce((sum, metric) => sum + metric.resourceUsage.memoryUsage, 0) / recentHistory.length
      : 0;

    const resourceEfficiency = this.coordinatorConfig.maxMemoryUsage
      ? Math.max(0, 100 - (averageResourceUsage / this.coordinatorConfig.maxMemoryUsage * 100))
      : 100;

    // Calculate per-service performance
    const servicePerformance: Record<string, {
      averageTime: number;
      successRate: number;
      totalOperations: number;
    }> = {};

    for (const serviceId of this.services.keys()) {
      const serviceMetrics = recentHistory.filter(metric => metric.serviceId === serviceId);

      if (serviceMetrics.length > 0) {
        servicePerformance[serviceId] = {
          averageTime: serviceMetrics.reduce((sum, metric) => sum + metric.executionTime, 0) / serviceMetrics.length,
          successRate: serviceMetrics.filter(metric => metric.success).length / serviceMetrics.length,
          totalOperations: serviceMetrics.length
        };
      }
    }

    return {
      averageExecutionTime,
      successRate,
      operationsPerMinute,
      resourceEfficiency,
      servicePerformance
    };
  }

  // Private helper methods

  private validateOperation(operation: AIOperation): void {
    if (!operation.id) {
      throw new Error('Operation ID is required');
    }

    if (!operation.serviceId) {
      throw new Error('Service ID is required');
    }

    if (!this.services.has(operation.serviceId)) {
      throw new Error(`Service not registered: ${operation.serviceId}`);
    }
  }

  private async checkDependencies(serviceId: string): Promise<void> {
    const dependency = this.dependencies.get(serviceId);
    if (!dependency) return;

    for (const depServiceId of dependency.dependsOn) {
      const depService = this.services.get(depServiceId);
      if (!depService) {
        if (!dependency.optional) {
          throw new Error(`Required dependency not available: ${depServiceId}`);
        }
        continue;
      }

      try {
        const status = await depService.healthCheck();
        if (!status.isHealthy && !dependency.optional) {
          throw new Error(`Required dependency unhealthy: ${depServiceId}`);
        }
      } catch (error) {
        if (!dependency.optional) {
          throw new Error(`Dependency check failed: ${depServiceId}`);
        }
      }
    }
  }

  private async executeWithRetries(service: AIBaseService, operation: AIOperation): Promise<any> {
    const maxRetries = operation.retries ?? this.coordinatorConfig.defaultRetries ?? 3;
    const timeout = operation.timeout ?? this.coordinatorConfig.defaultTimeout ?? 30000;

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Create timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Operation timeout')), timeout);
        });

        // Execute operation with timeout
        const operationPromise = this.executeServiceOperation(service, operation);

        return await Promise.race([operationPromise, timeoutPromise]);

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt < maxRetries) {
          // Wait before retry (exponential backoff)
          const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
          await new Promise(resolve => setTimeout(resolve, delay));
          this.log(`Retrying operation ${operation.id}, attempt ${attempt + 2}/${maxRetries + 1}`);
        }
      }
    }

    throw lastError || new Error('Operation failed after retries');
  }

  private async executeServiceOperation(service: AIBaseService, operation: AIOperation): Promise<any> {
    // This is a simplified implementation
    // In a real implementation, this would call specific methods based on operation.type

    switch (operation.type) {
      case 'healthCheck':
        return await service.healthCheck();

      default:
        // For now, assume the service has a generic execute method
        if ('execute' in service && typeof (service as any).execute === 'function') {
          return await (service as any).execute(operation.payload);
        }
        throw new Error(`Unsupported operation type: ${operation.type}`);
    }
  }

  private sortOperationsByPriority(operations: AIOperation[]): AIOperation[] {
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };

    return [...operations].sort((a, b) => {
      // First sort by priority
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
      if (priorityDiff !== 0) return priorityDiff;

      // Then by dependencies (operations with fewer dependencies first)
      const aDeps = a.dependencies?.length || 0;
      const bDeps = b.dependencies?.length || 0;
      return aDeps - bDeps;
    });
  }

  private createExecutionBatches(operations: AIOperation[]): AIOperation[][] {
    const maxConcurrent = this.coordinatorConfig.maxConcurrentOperations || 5;
    const batches: AIOperation[][] = [];

    for (let i = 0; i < operations.length; i += maxConcurrent) {
      batches.push(operations.slice(i, i + maxConcurrent));
    }

    return batches;
  }

  /**
   * Initialize coordinator with default services
   */
  async initialize(config?: AICoordinatorConfig): Promise<void> {
    if (config) {
      this.coordinatorConfig = { ...this.coordinatorConfig, ...config };
    }

    await super.initialize(this.coordinatorConfig);

    // Start resource monitoring if enabled
    this.startResourceMonitoring();

    this.log('AI Service Coordinator initialized');
  }

  /**
   * Cleanup coordinator resources
   */
  async destroy(): Promise<void> {
    // Stop resource monitoring
    this.stopResourceMonitoring();

    // Cancel all running operations
    this.runningOperations.clear();

    // Clear queue
    this.clearQueue();

    // Clear performance history
    this.performanceHistory = [];

    // Reset operation statistics
    this.operationStats = {
      total: 0,
      successful: 0,
      failed: 0,
      totalExecutionTime: 0
    };

    // Cleanup services
    for (const [serviceId, service] of this.services) {
      try {
        await service.destroy();
      } catch (error) {
        this.log(`Error destroying service ${serviceId}:`, error);
      }
    }

    this.services.clear();
    this.dependencies.clear();

    await super.destroy();
    this.log('AI Service Coordinator destroyed');
  }
}

// Singleton instance
export const aiServiceCoordinator = new AIServiceCoordinator();