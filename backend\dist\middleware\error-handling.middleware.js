"use strict";
/**
 * Error Handling Middleware
 *
 * Express-Middleware für konsistente Error-Behandlung und
 * standardisierte API-Response-Formate.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExpressErrorHandlers = void 0;
exports.globalErrorHandler = globalErrorHandler;
exports.asyncErrorHandler = asyncErrorHandler;
exports.handleRequest = handleRequest;
exports.handleValidationError = handleValidationError;
exports.handleNotFound = handleNotFound;
exports.enhancedRateLimitHandler = enhancedRateLimitHandler;
exports.setupErrorHandling = setupErrorHandling;
exports.repositoryErrorHandler = repositoryErrorHandler;
exports.serviceErrorHandler = serviceErrorHandler;
exports.isStandardError = isStandardError;
exports.isApiErrorResponse = isApiErrorResponse;
const error_handler_service_1 = require("../services/error-handler.service");
/**
 * Global Error Handler Middleware
 * Fängt alle unbehandelten Fehler ab und formatiert sie einheitlich
 */
function globalErrorHandler(error, req, res, next) {
    const errorHandler = error_handler_service_1.ErrorHandlerService.getInstance();
    // Bestimme ob es bereits ein StandardError ist
    let standardError;
    if (error && typeof error === 'object' && 'id' in error && 'type' in error) {
        // Bereits ein StandardError
        standardError = error;
    }
    else {
        // Konvertiere zu StandardError
        standardError = errorHandler.handleError(error, {
            service: 'API',
            method: req.method,
            requestId: req.headers['x-request-id']
        });
    }
    // HTTP-Status-Code basierend auf Error-Typ bestimmen
    const statusCode = getHttpStatusCode(standardError);
    // API-Response erstellen
    const apiResponse = (0, error_handler_service_1.toApiErrorResponse)(standardError);
    // Log Request-Details für besseres Debugging
    console.error(`[API-ERROR] ${req.method} ${req.originalUrl}`, {
        errorId: standardError.id,
        statusCode,
        userAgent: req.headers['user-agent'],
        ip: req.ip,
        body: req.body,
        query: req.query
    });
    // Response senden
    res.status(statusCode).json(apiResponse);
}
/**
 * Async Error Handler Wrapper
 * Wrapper für async Route-Handler um Errors automatisch zu behandeln
 */
function asyncErrorHandler(handler) {
    return (req, res, next) => {
        Promise.resolve(handler(req, res, next)).catch(next);
    };
}
/**
 * Request-Handler mit eingebautem Error-Handling
 * Vereinfacht die Error-Behandlung in Route-Handlern
 */
async function handleRequest(req, res, operation, options) {
    try {
        const result = await operation();
        const response = {
            success: true,
            data: result
        };
        res.json(response);
    }
    catch (error) {
        const errorHandler = error_handler_service_1.ErrorHandlerService.getInstance();
        const standardError = errorHandler.handleError(error, {
            service: (options === null || options === void 0 ? void 0 : options.service) || 'API',
            method: (options === null || options === void 0 ? void 0 : options.method) || req.method,
            requestId: req.headers['x-request-id']
        });
        const statusCode = getHttpStatusCode(standardError);
        const apiResponse = (0, error_handler_service_1.toApiErrorResponse)(standardError);
        res.status(statusCode).json(apiResponse);
    }
}
/**
 * Validation Error Handler
 * Speziell für Validierungsfehler in Request-Daten
 */
function handleValidationError(req, res, validationErrors) {
    const errorHandler = error_handler_service_1.ErrorHandlerService.getInstance();
    const details = {
        validationErrors: validationErrors.map(err => ({
            field: err.param || err.path,
            value: err.value,
            message: err.msg || err.message
        }))
    };
    const standardError = errorHandler.handleValidationError('Validierungsfehler in den Eingabedaten', details, {
        service: 'API',
        method: req.method
    });
    const apiResponse = (0, error_handler_service_1.toApiErrorResponse)(standardError);
    res.status(400).json(apiResponse);
}
/**
 * Not Found Error Handler
 * Für 404-Fehler
 */
function handleNotFound(req, res) {
    const errorHandler = error_handler_service_1.ErrorHandlerService.getInstance();
    const standardError = errorHandler.handleNotFoundError('Route', req.originalUrl, {
        service: 'API',
        method: req.method
    });
    const apiResponse = (0, error_handler_service_1.toApiErrorResponse)(standardError);
    res.status(404).json(apiResponse);
}
/**
 * Rate Limit Error Handler
 * Erweitert die Standard-Rate-Limit-Responses
 */
function enhancedRateLimitHandler(req, res) {
    const errorHandler = error_handler_service_1.ErrorHandlerService.getInstance();
    const standardError = errorHandler.handleError(new Error('Rate limit exceeded'), {
        service: 'API',
        method: req.method
    });
    // Überschreibe Standard-Error-Details für Rate-Limiting
    standardError.code = 'RATE_LIMIT_EXCEEDED';
    standardError.userMessage = 'Zu viele Anfragen. Bitte versuchen Sie es später erneut.';
    standardError.suggestions = [
        'Reduzieren Sie die Anzahl der Anfragen',
        'Warten Sie bis zum Reset des Rate-Limits',
        'Kontaktieren Sie den Support bei anhaltenden Problemen'
    ];
    const retryAfter = res.getHeader('Retry-After') || 60;
    standardError.details = {
        retryAfter: Number(retryAfter),
        rateLimit: {
            windowMs: 15 * 60 * 1000, // 15 Minuten
            maxRequests: res.getHeader('X-RateLimit-Limit') || 'unknown'
        }
    };
    const apiResponse = (0, error_handler_service_1.toApiErrorResponse)(standardError);
    res.status(429).json(apiResponse);
}
/**
 * HTTP-Status-Code basierend auf Error-Typ bestimmen
 */
function getHttpStatusCode(error) {
    switch (error.type) {
        case 'VALIDATION_ERROR':
            return 400; // Bad Request
        case 'NOT_FOUND_ERROR':
            return 404; // Not Found
        case 'PERMISSION_ERROR':
            return 403; // Forbidden
        case 'TIMEOUT_ERROR':
            return 408; // Request Timeout
        case 'DATABASE_ERROR':
            return 500; // Internal Server Error
        case 'CACHE_ERROR':
            return 500; // Internal Server Error
        case 'NETWORK_ERROR':
            return 502; // Bad Gateway
        case 'CONFIGURATION_ERROR':
            return 500; // Internal Server Error
        case 'BUSINESS_LOGIC_ERROR':
            return 422; // Unprocessable Entity
        case 'UNKNOWN_ERROR':
        default:
            return 500; // Internal Server Error
    }
}
/**
 * Express Error Handler für spezifische Error-Typen
 */
class ExpressErrorHandlers {
    /**
     * 404 Handler für nicht gefundene Routen
     */
    static notFound(req, res, next) {
        handleNotFound(req, res);
    }
    /**
     * Allgemeiner Error Handler
     */
    static general(error, req, res, next) {
        globalErrorHandler(error, req, res, next);
    }
    /**
     * Unhandled Promise Rejection Handler
     */
    static unhandledRejection(reason, promise) {
        const errorHandler = error_handler_service_1.ErrorHandlerService.getInstance();
        console.error('[UNHANDLED-REJECTION] Unhandled Promise Rejection:', reason);
        errorHandler.handleError(reason, {
            service: 'System',
            method: 'unhandledRejection'
        });
    }
    /**
     * Uncaught Exception Handler
     */
    static uncaughtException(error) {
        const errorHandler = error_handler_service_1.ErrorHandlerService.getInstance();
        console.error('[UNCAUGHT-EXCEPTION] Uncaught Exception:', error);
        errorHandler.handleError(error, {
            service: 'System',
            method: 'uncaughtException'
        });
        // Graceful shutdown nach uncaught exception
        console.error('[SYSTEM] Graceful shutdown aufgrund uncaught exception...');
        process.exit(1);
    }
}
exports.ExpressErrorHandlers = ExpressErrorHandlers;
/**
 * Error-Handler Setup für Express-App
 */
function setupErrorHandling(app) {
    // Request-ID-Middleware (für besseres Error-Tracking)
    app.use((req, res, next) => {
        if (!req.headers['x-request-id']) {
            req.headers['x-request-id'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        res.setHeader('X-Request-ID', req.headers['x-request-id']);
        next();
    });
    // 404 Handler (muss vor dem allgemeinen Error Handler stehen)
    app.use(ExpressErrorHandlers.notFound);
    // Allgemeiner Error Handler
    app.use(ExpressErrorHandlers.general);
    // System-Level Error Handlers
    process.on('unhandledRejection', ExpressErrorHandlers.unhandledRejection);
    process.on('uncaughtException', ExpressErrorHandlers.uncaughtException);
    console.log('[ERROR-HANDLING] Error handling middleware konfiguriert');
}
/**
 * Repository Error Handler Decorator
 * Vereinfacht Error-Handling in Repository-Klassen
 */
function repositoryErrorHandler(serviceName) {
    return function (target, propertyName, descriptor) {
        const method = descriptor.value;
        descriptor.value = async function (...args) {
            try {
                return await method.apply(this, args);
            }
            catch (error) {
                const errorHandler = error_handler_service_1.ErrorHandlerService.getInstance();
                const standardError = errorHandler.handleError(error, {
                    service: serviceName,
                    method: propertyName
                });
                // Für Repository-Fehler, werfe den StandardError weiter
                // damit er von der API-Schicht behandelt werden kann
                throw standardError;
            }
        };
        return descriptor;
    };
}
/**
 * Service Error Handler Decorator
 * Für Service-Klassen
 */
function serviceErrorHandler(serviceName) {
    return repositoryErrorHandler(serviceName); // Gleiche Implementierung
}
/**
 * Type Guards für Error-Handling
 */
function isStandardError(error) {
    return error &&
        typeof error === 'object' &&
        'id' in error &&
        'type' in error &&
        'severity' in error;
}
function isApiErrorResponse(response) {
    return response &&
        typeof response === 'object' &&
        'success' in response;
}
