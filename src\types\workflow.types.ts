/**
 * Frontend-Typdefinitionen für Workflow-Monitoring
 * 
 * Stellt TypeScript-Typen für das Workflow-Management zur Verfügung.
 * Synchronisiert mit Backend-Typen aus workflow.types.ts
 */

export type WorkflowStatus = 'running' | 'completed' | 'failed' | 'disabled' | 'scheduled';
export type WorkflowSourceType = 'SAP' | 'BI_Excel' | 'Database' | 'API';
export type WorkflowFrequency = 'hourly' | 'daily' | 'weekly' | 'manual';

export interface Workflow {
  id: string;
  name: string;
  description: string;
  sourceType: WorkflowSourceType;
  frequency: WorkflowFrequency;
  targetTables: string[];
  scriptPath: string;
  isActive: boolean;
  status: WorkflowStatus;
  lastExecution: Date | null;
  nextExecution: Date | null;
  duration: number | null; // in seconds
  successRate: number; // percentage
  errorCount: number;
  totalRuns: number;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  startTime: Date;
  endTime: Date | null;
  duration: number | null; // in seconds
  status: 'running' | 'completed' | 'failed';
  errorMessage: string | null;
  recordsProcessed: number | null;
  dataSize: number | null; // in bytes
}

export interface WorkflowLogEntry {
  timestamp: Date;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  workflowId: string;
  executionId?: string;
  details?: Record<string, any>;
}

export interface WorkflowMetrics {
  workflowId: string;
  averageDuration: number; // in seconds
  successRate: number; // percentage
  lastSuccessfulRun: Date | null;
  totalExecutions: number;
  errorCount: number;
  dataFreshness: number; // minutes since last update
}

// API Response Types (vom Backend)
export interface WorkflowListResponse {
  workflows: Workflow[];
  totalCount: number;
  lastUpdated: Date;
}

export interface WorkflowDetailResponse {
  workflow: Workflow;
  metrics: WorkflowMetrics;
  recentExecutions: WorkflowExecution[];
  recentLogs: WorkflowLogEntry[];
}

export interface WorkflowHealthResponse {
  totalWorkflows: number;
  activeWorkflows: number;
  runningWorkflows: number;
  failedWorkflows: number;
  overallHealth: 'healthy' | 'warning' | 'critical';
}

// UI-spezifische Typen
export interface WorkflowCardProps {
  workflow: Workflow;
  onToggle: (workflowId: string) => void;
  onRestart: (workflowId: string) => void;
  onViewLogs: (workflowId: string) => void;
}

export interface WorkflowStatusBadgeProps {
  status: WorkflowStatus;
  isRunning?: boolean;
}

export interface WorkflowPerformanceData {
  workflowId: string;
  executionTimes: Array<{
    date: string;
    duration: number;
  }>;
  successRates: Array<{
    date: string;
    rate: number;
  }>;
  dataVolumes: Array<{
    date: string;
    records: number;
  }>;
}

// Utility Types für Status-Farben und Icons
export const WORKFLOW_STATUS_CONFIG = {
  running: {
    color: 'green',
    bgColor: 'bg-green-50',
    textColor: 'text-green-700',
    borderColor: 'border-green-200',
    icon: '🟢',
    label: 'Läuft'
  },
  completed: {
    color: 'blue',
    bgColor: 'bg-blue-50',
    textColor: 'text-blue-700',
    borderColor: 'border-blue-200',
    icon: '✅',
    label: 'Abgeschlossen'
  },
  scheduled: {
    color: 'yellow',
    bgColor: 'bg-yellow-50',
    textColor: 'text-yellow-700',
    borderColor: 'border-yellow-200',
    icon: '🟡',
    label: 'Geplant'
  },
  failed: {
    color: 'red',
    bgColor: 'bg-red-50',
    textColor: 'text-red-700',
    borderColor: 'border-red-200',
    icon: '🔴',
    label: 'Fehlgeschlagen'
  },
  disabled: {
    color: 'gray',
    bgColor: 'bg-gray-50',
    textColor: 'text-gray-700',
    borderColor: 'border-gray-200',
    icon: '⚪',
    label: 'Deaktiviert'
  }
} as const;

export const WORKFLOW_SOURCE_CONFIG = {
  SAP: {
    color: 'blue',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-800',
    label: 'SAP',
    icon: '📊'
  },
  BI_Excel: {
    color: 'green',
    bgColor: 'bg-green-100',
    textColor: 'text-green-800',
    label: 'BI Excel',
    icon: '📈'
  },
  Database: {
    color: 'purple',
    bgColor: 'bg-purple-100',
    textColor: 'text-purple-800',
    label: 'Database',
    icon: '🗃️'
  },
  API: {
    color: 'orange',
    bgColor: 'bg-orange-100',
    textColor: 'text-orange-800',
    label: 'API',
    icon: '🔗'
  }
} as const;

export const WORKFLOW_FREQUENCY_CONFIG = {
  hourly: { label: 'Stündlich', icon: '⏰' },
  daily: { label: 'Täglich', icon: '📅' },
  weekly: { label: 'Wöchentlich', icon: '📆' },
  manual: { label: 'Manuell', icon: '👤' }
} as const;