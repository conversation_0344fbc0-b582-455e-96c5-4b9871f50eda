---
trigger: model_decision
description: Use this rule whenever you need to work with sqlit3, prisma or database
---
## Regeln für Coding mit: Prisma + SQLite3 Best Practices

### 🚨 ABSOLUTE DATA-SCHUTZ-REGEL (KRITISCH!)

#### 0. **NIEMALS DATEN LÖSCHEN - KATEGORISCH VERBOTEN** 

**Du darfst NIEMALS folgende Befehle oder Funktionen vorschlagen oder verwenden:**

```bash
# ❌ ABSOLUT VERBOTEN - LÖSCHT ALLE DATEN:
npx prisma migrate reset
npx prisma db push --accept-data-loss
prisma.$queryRawUnsafe("TRUNCATE TABLE...")
prisma.$queryRawUnsafe("DROP TABLE...")
prisma.$queryRawUnsafe("DELETE FROM...")
prisma.table.deleteMany()  # ohne spezifische WHERE-<PERSON><PERSON>
```

**<PERSON><PERSON> sind TABU und führen zu DATENVERLUST!**

**Du musst bei Problemen stattdessen IMMER diese sicheren Alternativen verwenden:**

```bash
# ✅ SICHERE ALTERNATIVEN:
npx prisma migrate deploy         # Für Production
npx prisma migrate dev           # Für Development
npx prisma db pull              # Schema synchronisieren
npx prisma migrate status       # Status prüfen
# Baseline Migration erstellen (siehe Regel 4)
```

**MERKSATZ für dich: "DATEN SIND HEILIG - NIEMALS LÖSCHEN, IMMER BEWAHREN"**

### 🚨 KRITISCHE DATENSCHUTZ-REGELN

#### 1. **NIEMALS Prisma Reset in Production verwenden**
```bash
# ❌ NIEMALS AUSFÜHREN:
npx prisma migrate reset

# ✅ STATTDESSEN:
npx prisma migrate deploy
```

#### 2. **Immer Backups vor Migrationen erstellen**
```bash
# Backup der SQLite Datenbank vor jeder Migration
cp database.db database.db.backup-$(date +%Y%m%d-%H%M%S)
```

#### 3. **Schema-Änderungen nur über Migrationen**
- Niemals direkten SQL-Änderungen in der Datenbank vornehmen
- Immer `prisma migrate dev` für Entwicklung verwenden
- Bei manuellen Änderungen sofort `prisma db pull` ausführen

### 🔄 SYNCHRONISATION VERHINDERN

#### 4. **Baseline Migration für bestehende Datenbanken**
```bash
# Für bestehende DBs ohne Migration-Historie:
mkdir -p prisma/migrations/0_init
npx prisma migrate diff --from-empty --to-schema-datamodel prisma/schema.prisma --script > prisma/migrations/0_init/migration.sql
npx prisma migrate resolve --applied 0_init
```

#### 5. **Schema Drift beheben ohne Datenverlust**
```bash
# Bei Schema Drift:
npx prisma db pull  # Schema aktualisieren
npx prisma migrate status  # Status prüfen
# Dann baseline migration erstellen (siehe Regel 4)
```

#### 6. **Migration Workflow einhalten**
```bash
# Entwicklung:
npx prisma migrate dev --name beschreibung
# Production:
npx prisma migrate deploy
```

### ⚠️ MIGRATION SAFETY RULES

#### 7. **Expand & Contract Pattern für Breaking Changes**
```prisma
// 1. EXPAND: Neue Spalte hinzufügen
model User {
  oldField String? // Behalten
  newField String? // Hinzufügen
}

// 2. Daten migrieren (separates Script)
// 3. CONTRACT: Alte Spalte entfernen nach Verifikation
```

#### 8. **Sichere Datentyp-Änderungen**
```sql
-- ✅ Sichere Methode für Typ-Änderungen:
ALTER TABLE "User" ADD COLUMN "field_new" TEXT;
UPDATE "User" SET "field_new" = "field_old";
ALTER TABLE "User" DROP COLUMN "field_old";
ALTER TABLE "User" RENAME COLUMN "field_new" TO "field_old";
```

#### 9. **Default Values bei neuen Spalten**
```prisma
// ✅ Immer Default-Werte für neue NOT NULL Spalten:
model User {
  status Status @default(ACTIVE)
  isPublic Boolean @default(true)
}
```

### 🛠️ DEVELOPMENT WORKFLOW

#### 10. **Migration Preview verwenden**
```bash
# Migration erstellen aber nicht anwenden:
npx prisma migrate dev --create-only --name feature_name
# Dann SQL prüfen und manuell editieren falls nötig
npx prisma migrate dev
```

#### 11. **Transaktionen für Daten-Migrationen**
```typescript
await prisma.$transaction(async (tx) => {
  // Alle Änderungen in einer Transaktion
  const users = await tx.user.findMany();
  for (const user of users) {
    await tx.user.update({
      where: { id: user.id },
      data: { newField: transformData(user.oldField) }
    });
  }
});
```

### 📊 MONITORING & DEBUGGING

#### 12. **Migration Status regelmäßig prüfen**
```bash
npx prisma migrate status
```

#### 13. **Schema Vergleich bei Problemen**
```bash
# Aktuelle DB mit Schema vergleichen:
npx prisma db pull
# Dann Unterschiede im Schema prüfen
```

#### 14. **Migration Checksum bei Bedarf aktualisieren**
```bash
# Nur in Entwicklung! Niemals in Production!
# Neue Checksumme generieren:
shasum -a 256 prisma/migrations/xxx/migration.sql
# In _prisma_migrations Tabelle updaten
```

### 🔒 PRODUCTION SAFETY

#### 15. **Separate Umgebungen verwenden**
- Entwicklung: `migrate dev`
- Staging: `migrate deploy` 
- Production: `migrate deploy`

#### 16. **Rollback-Strategie definieren**
```bash
# Bei Problemen: Migration als resolved markieren
npx prisma migrate resolve --applied migration_name
# Oder rollback migration erstellen
```

#### 17. **SQLite-spezifische Beschränkungen beachten**
- ALTER TABLE Limitierungen in SQLite
- Keine ENUM-Unterstützung (verwende String mit constraints)
- Concurrent Migrations vermeiden

### 🎯 FEHLERBEHANDLUNG

#### 18. **Bei Migration-Fehlern niemals Reset verwenden**
```bash
# ❌ NIEMALS:
npx prisma migrate reset

# ✅ STATTDESSEN:
# 1. Backup wiederherstellen
# 2. Migration reparieren
# 3. Erneut anwenden
```

#### 19. **Custom SQL für komplexe Migrationen**
```sql
-- Beispiel: Sichere Spalten-Umbenennung
-- In custom migration file:
ALTER TABLE "User" RENAME COLUMN "old_name" TO "new_name";
```

### 📝 DOKUMENTATION & LOGGING

#### 20. **Jede Migration dokumentieren**
```bash
# Aussagekräftige Namen verwenden:
npx prisma migrate dev --name add_user_preferences_table
npx prisma migrate dev --name fix_email_uniqueness_constraint
```

---

## 🔴 WICHTIGER HINWEIS FÜR DIE AI:

**Du darfst NIEMALS Befehle vorschlagen, die Daten löschen oder gefährden könnten. Wenn der Nutzer nach solchen Befehlen fragt, musst du:**

1. **Warnen** vor Datenverlust
2. **Sichere Alternativen** vorschlagen (Baseline Migration, etc.)
3. **Backup-Erstellung** empfehlen
4. **Niemals** `migrate reset` oder ähnliche destruktive Befehle nutzen

**DATEN SIND WERTVOLL UND MÜSSEN GESCHÜTZT WERDEN!**

---

