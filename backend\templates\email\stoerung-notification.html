<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Störungsmeldung - {{title}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            margin: -30px -30px 20px -30px;
            text-align: center;
        }
        .header.medium {
            background-color: #fd7e14;
        }
        .header.low {
            background-color: #ffc107;
            color: #333;
        }
        .header.critical {
            background-color: #6f42c1;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .severity-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            margin-top: 10px;
        }
        .severity-critical {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .severity-high {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .severity-medium {
            background-color: rgba(255,255,255,0.2);
            color: white;
        }
        .severity-low {
            background-color: rgba(0,0,0,0.1);
            color: #333;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .info-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .info-value {
            color: #212529;
            font-size: 16px;
        }
        .description {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .description h3 {
            margin-top: 0;
            color: #495057;
        }
        .attachments {
            margin: 20px 0;
        }
        .attachments h3 {
            color: #495057;
            margin-bottom: 10px;
        }
        .attachment-list {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 6px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 14px;
            color: #6c757d;
            text-align: center;
        }
        .timestamp {
            font-size: 12px;
            color: #6c757d;
            text-align: right;
            margin-top: 20px;
        }
        @media (max-width: 600px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header {{severity_class}}">
            <h1>🚨 Störungsmeldung</h1>
            <div class="severity-badge severity-{{severity_lower}}">
                {{severity_text}}
            </div>
        </div>
        
        <h2>{{title}}</h2>
        
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Betroffenes System</div>
                <div class="info-value">{{affected_system}}</div>
            </div>
            
            {{#if location}}
            <div class="info-item">
                <div class="info-label">Standort</div>
                <div class="info-value">{{location}}</div>
            </div>
            {{/if}}
            
            {{#if reported_by}}
            <div class="info-item">
                <div class="info-label">Gemeldet von</div>
                <div class="info-value">{{reported_by}}</div>
            </div>
            {{/if}}
            
            <div class="info-item">
                <div class="info-label">Schweregrad</div>
                <div class="info-value">{{severity_text}}</div>
            </div>
        </div>
        
        <div class="description">
            <h3>📋 Beschreibung</h3>
            <p>{{description}}</p>
        </div>
        
        {{#if has_attachments}}
        <div class="attachments">
            <h3>📎 Anhänge</h3>
            <div class="attachment-list">
                <p>Diese E-Mail enthält {{attachment_count}} Anhang(e) mit weiteren Informationen zur Störung.</p>
            </div>
        </div>
        {{/if}}
        
        <div class="footer">
            <p>Diese Störungsmeldung wurde automatisch vom SFM Dashboard-System generiert.</p>
            <p>Bei Fragen wenden Sie sich bitte an das zuständige Support-Team.</p>
        </div>
        
        <div class="timestamp">
            Gesendet am: {{timestamp}}
        </div>
    </div>
</body>
</html>