import bcrypt from 'bcryptjs';
import * as jwt from 'jsonwebtoken';
import { UserRepository } from '../repositories/user.repository';

export class AuthService {
    private userRepository: UserRepository;

    constructor() {
        this.userRepository = new UserRepository();
    }

    async register(email: string, username: string, name: string | undefined, password: string) {
        // Check if user already exists by email or username
        const existingUser = await this.userRepository.findByEmailOrUsername(email, username);

        if (existingUser) {
            if (existingUser.email === email) {
                throw {
                    code: 'USER_EXISTS',
                    message: 'Ein Benutzer mit dieser E-Mail-Adresse existiert bereits.'
                };
            }
            if (existingUser.username === username) {
                throw {
                    code: 'USER_EXISTS',
                    message: 'Ein Benutzer mit diesem Benutzernamen existiert bereits.'
                };
            }
        }

        // Hash the password
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(password, saltRounds);

        // Create the user
        const newUser = await this.userRepository.createUser({
            email,
            username,
            name, // Neues Feld für den Namen
            passwordHash
        });

        return newUser;
    }

    async login(username: string, password: string) {
        // Find user by username
        const user = await this.userRepository.findByUsername(username);

        if (!user) {
            throw {
                code: 'INVALID_CREDENTIALS',
                message: 'Ungültige Anmeldedaten.'
            };
        }

        // Compare password with stored hash
        const isPasswordValid = await bcrypt.compare(password, user.passwordHash);

        if (!isPasswordValid) {
            throw {
                code: 'INVALID_CREDENTIALS',
                message: 'Ungültige Anmeldedaten.'
            };
        }

        // Extrahiere Rollennamen aus dem User-Objekt
        // Prisma gibt die Rollen als Array von Objekten zurück
        // Typumwandlung notwendig, da der Standard-User-Typ keine roles-Eigenschaft hat
        const userWithRoles = user as any;
        const userRoles = userWithRoles.roles ? userWithRoles.roles.map((role: any) => role.name) : [];
        console.log('🔑 Login: Benutzerrollen geladen:', userRoles);
        
        // Create JWT token
        const jwtSecret = process.env.JWT_SECRET || 'fallback-secret-key';
        const token = jwt.sign(
            {
                userId: user.id,
                username: user.username,
                roles: userRoles // Rollen in den Token aufnehmen
            },
            jwtSecret,
            { expiresIn: '15m' }
        );

        return {
            token,
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                name: user.name, // Name im User-Objekt zurückgeben
                roles: userRoles // Rollen im User-Objekt zurückgeben
            }
        };
    }

    async refreshToken(currentToken: string) {
        try {
            const jwtSecret = process.env.JWT_SECRET || 'fallback-secret-key';
            
            // Verify the current token (even if expired, we want to check if it's valid structure)
            let decoded: any;
            try {
                decoded = jwt.verify(currentToken, jwtSecret, { ignoreExpiration: true });
            } catch (error) {
                throw {
                    code: 'INVALID_TOKEN',
                    message: 'Ungültiger Token.'
                };
            }

            // Check if token is not too old (max 1 hour after expiration for refresh)
            const now = Math.floor(Date.now() / 1000);
            const tokenAge = now - decoded.exp;
            if (tokenAge > 3600) { // 1 hour
                throw {
                    code: 'TOKEN_TOO_OLD',
                    message: 'Token ist zu alt für eine Aktualisierung.'
                };
            }

            // Get fresh user data to ensure user still exists and roles are current
            const user = await this.userRepository.findByUsername(decoded.username);
            if (!user) {
                throw {
                    code: 'USER_NOT_FOUND',
                    message: 'Benutzer nicht gefunden.'
                };
            }

            // Get current roles
            const userWithRoles = user as any;
            const userRoles = userWithRoles.roles ? userWithRoles.roles.map((role: any) => role.name) : [];

            // Generate new token
            const newToken = jwt.sign(
                {
                    userId: user.id,
                    username: user.username,
                    roles: userRoles
                },
                jwtSecret,
                { expiresIn: '15m' }
            );

            return {
                token: newToken,
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    name: user.name,
                    roles: userRoles
                }
            };
        } catch (error: any) {
            console.error('[AUTH-REFRESH-ERROR]', error);
            throw error;
        }
    }
}