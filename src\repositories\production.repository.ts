/**
 * Production Repository
 * 
 * Repository für Produktionsdaten (Schnitte, Maschinen, Ablaengerei)
 * Implementiert das Repository Pattern für Produktions- und Fertigungsdaten.
 */

import { BaseRepository, DateRangeFilter } from './base.repository';
import apiService from '@/services/api.service';
import { AblaengereiDataPoint } from '@/types/database';

/**
 * Filter für Produktionsdaten
 */
export interface ProductionFilter extends DateRangeFilter {
  machineId?: string;
  productionType?: 'cutting' | 'ablängerei' | 'efficiency';
  minEfficiency?: number;
  maxEfficiency?: number;
}

/**
 * Schnitte Data Repository
 */
export class SchnitteRepository extends BaseRepository<any, DateRangeFilter> {
  protected repositoryName = 'schnitte-data';
  
  async getAll(): Promise<any[]> {
    return await apiService.getSchnitteData();
  }
  
  /**
   * Schnitte-Daten nach Datum gruppiert abrufen
   */
  async getGroupedByDate(): Promise<Record<string, any[]>> {
    const data = await this.getAll();
    
    return data.reduce((grouped, item) => {
      const date = item.Datum || 'unknown';
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(item);
      return grouped;
    }, {} as Record<string, any[]>);
  }
  
  /**
   * Tägliche Schnitte-Summen berechnen
   */
  async getDailySums(): Promise<Array<{
    date: string;
    totalH1: number;
    totalH3: number;
    grandTotal: number;
  }>> {
    const data = await this.getAll();
    
    return data.map(item => ({
      date: item.datum || '',
      totalH1: item['Sum-H1'] || 0,
      totalH3: item['Sum-H3'] || 0,
      grandTotal: (item['Sum-H1'] || 0) + (item['Sum-H3'] || 0)
    }));
  }
  
  /**
   * Maschinen-Performance-Analyse
   */
  async getMachinePerformance(): Promise<Array<{
    machine: string;
    totalCuts: number;
    avgPerDay: number;
    efficiency: number;
  }>> {
    const data = await this.getAll();
    
    // Extrahiere Maschinen-Spalten (M1-M27)
    const machineColumns = Object.keys(data[0] || {}).filter(key => 
      key.match(/^M\d+/) && !key.includes('Sum')
    );
    
    return machineColumns.map(machine => {
      const machineCuts = data.map(item => item[machine] || 0);
      const totalCuts = machineCuts.reduce((sum, cuts) => sum + cuts, 0);
      const avgPerDay = totalCuts / data.length;
      
      return {
        machine,
        totalCuts,
        avgPerDay,
        efficiency: avgPerDay > 0 ? (totalCuts / (data.length * 100)) * 100 : 0 // Beispiel-Effizienz
      };
    });
  }
}

/**
 * Maschinen Efficiency Repository
 */
export class MaschinenEfficiencyRepository extends BaseRepository<any, ProductionFilter> {
  protected repositoryName = 'maschinen-efficiency';
  
  async getAll(filter?: ProductionFilter): Promise<any[]> {
    const data = await apiService.getMaschinenEfficiency();
    
    if (filter?.minEfficiency !== undefined) {
      return data.filter(item => 
        (item.efficiency || 0) >= filter.minEfficiency!
      );
    }
    
    if (filter?.maxEfficiency !== undefined) {
      return data.filter(item => 
        (item.efficiency || 0) <= filter.maxEfficiency!
      );
    }
    
    return data;
  }
  
  /**
   * Top-Performance Maschinen abrufen
   */
  async getTopPerformers(limit: number = 5): Promise<any[]> {
    const data = await this.getAll();
    
    return data
      .sort((a, b) => (b.efficiency || 0) - (a.efficiency || 0))
      .slice(0, limit);
  }
  
  /**
   * Maschinen mit niedriger Effizienz identifizieren
   */
  async getLowPerformers(threshold: number = 50): Promise<any[]> {
    return await this.getAll({ minEfficiency: 0, maxEfficiency: threshold });
  }
  
  /**
   * Durchschnittliche Effizienz aller Maschinen berechnen
   */
  async getAverageEfficiency(): Promise<{
    average: number;
    total: number;
    aboveAverage: number;
    belowAverage: number;
  }> {
    const data = await this.getAll();
    
    const efficiencies = data
      .map(item => item.efficiency || 0)
      .filter(eff => eff > 0);
    
    const average = efficiencies.reduce((sum, eff) => sum + eff, 0) / efficiencies.length;
    
    return {
      average,
      total: data.length,
      aboveAverage: efficiencies.filter(eff => eff >= average).length,
      belowAverage: efficiencies.filter(eff => eff < average).length
    };
  }
}

/**
 * Ablaengerei Repository
 */
export class AblaengereiRepository extends BaseRepository<AblaengereiDataPoint, DateRangeFilter> {
  protected repositoryName = 'ablaengerei';
  
  async getAll(): Promise<AblaengereiDataPoint[]> {
    return await apiService.getAblaengereiData();
  }
  
  /**
   * Tägliche Produktionsstatistiken abrufen
   */
  async getDailyStats(): Promise<Array<{
    date: string;
    total220: number;
    total240: number;
    total200: number;
    totalCuts: number;
    pickCut: number;
  }>> {
    const data = await this.getAll();
    
    return data.map(item => ({
      date: item.datum || '',
      total220: (item.cutLagerK220 || 0) + (item.cutLagerR220 || 0) + (item.lagerCut220 || 0),
      total240: (item.cutLagerK240 || 0) + (item.cutLagerR240 || 0) + (item.lagerCut240 || 0),
      total200: (item.cutLager200 || 0) + (item.cutLagerK200 || 0) + (item.lagerCut200 || 0),
      totalCuts: (item as any).cutGesamt || 0,
      pickCut: item.pickCut || 0
    }));
  }
  
  /**
   * Lager-Performance-Analyse
   */
  async getWarehousePerformance(): Promise<{
    warehouse220: { cuts: number; percentage: number };
    warehouse240: { cuts: number; percentage: number };
    warehouse200: { cuts: number; percentage: number };
    totalCuts: number;
  }> {
    const data = await this.getAll();
    
    const totals = data.reduce((acc, item) => {
      acc.total220 += (item.cutLagerK220 || 0) + (item.cutLagerR220 || 0) + (item.lagerCut220 || 0);
      acc.total240 += (item.cutLagerK240 || 0) + (item.cutLagerR240 || 0) + (item.lagerCut240 || 0);
      acc.total200 += (item.cutLager200 || 0) + (item.cutLagerK200 || 0) + (item.lagerCut200 || 0);
      acc.totalCuts += (item as any).cutGesamt || 0;
      return acc;
    }, { total220: 0, total240: 0, total200: 0, totalCuts: 0 });
    
    return {
      warehouse220: {
        cuts: totals.total220,
        percentage: totals.totalCuts > 0 ? (totals.total220 / totals.totalCuts) * 100 : 0
      },
      warehouse240: {
        cuts: totals.total240,
        percentage: totals.totalCuts > 0 ? (totals.total240 / totals.totalCuts) * 100 : 0
      },
      warehouse200: {
        cuts: totals.total200,
        percentage: totals.totalCuts > 0 ? (totals.total200 / totals.totalCuts) * 100 : 0
      },
      totalCuts: totals.totalCuts
    };
  }
}

/**
 * Cutting Chart Repository
 */
export class CuttingChartRepository extends BaseRepository<any, DateRangeFilter> {
  protected repositoryName = 'cutting-chart-data';
  
  async getAll(): Promise<any[]> {
    return await apiService.getCuttingChartData();
  }
  
  /**
   * Trend-Analyse für Cutting-Daten
   */
  async getTrendAnalysis(days: number = 30): Promise<{
    trend: 'increasing' | 'decreasing' | 'stable';
    changePercentage: number;
    averageDaily: number;
    projectedNext: number;
  }> {
    const data = await this.getAll();
    
    if (data.length < 2) {
      return {
        trend: 'stable',
        changePercentage: 0,
        averageDaily: 0,
        projectedNext: 0
      };
    }
    
    const recentData = data.slice(-days);
    const values = recentData.map(item => item.total || item.value || 0);
    
    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    const changePercentage = firstValue > 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0;
    
    const averageDaily = values.reduce((sum, val) => sum + val, 0) / values.length;
    
    let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
    if (changePercentage > 5) trend = 'increasing';
    else if (changePercentage < -5) trend = 'decreasing';
    
    const projectedNext = lastValue + (changePercentage / 100) * lastValue;
    
    return {
      trend,
      changePercentage,
      averageDaily,
      projectedNext
    };
  }
}

/**
 * Lager Cuts Repository
 */
export class LagerCutsRepository extends BaseRepository<any, DateRangeFilter> {
  protected repositoryName = 'lager-cuts-chart-data';
  
  async getAll(): Promise<any[]> {
    return await apiService.getLagerCutsChartData();
  }
  
  /**
   * Lager-Verteilungsanalyse
   */
  async getDistributionAnalysis(): Promise<{
    byWarehouse: Record<string, number>;
    totalCuts: number;
    mostActiveWarehouse: string;
    efficiency: number;
  }> {
    const data = await this.getAll();
    
    const distribution = data.reduce((dist, item) => {
      Object.keys(item).forEach(key => {
        if (key.includes('lager') || key.includes('cut')) {
          if (!dist[key]) dist[key] = 0;
          dist[key] += item[key] || 0;
        }
      });
      return dist;
    }, {} as Record<string, number>);
    
    const totalCuts = Object.values(distribution).reduce((sum, val) => sum + val, 0);
    const mostActiveWarehouse = Object.entries(distribution)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || '';
    
    return {
      byWarehouse: distribution,
      totalCuts,
      mostActiveWarehouse,
      efficiency: data.length > 0 ? totalCuts / data.length : 0
    };
  }
}

/**
 * Kombiniertes Production Repository für alle Produktionsoperationen
 */
export class ProductionRepository {
  public readonly schnitte = new SchnitteRepository();
  public readonly maschinenEfficiency = new MaschinenEfficiencyRepository();
  public readonly ablaengerei = new AblaengereiRepository();
  public readonly cuttingChart = new CuttingChartRepository();
  public readonly lagerCuts = new LagerCutsRepository();
  
  /**
   * Cache für alle Production-Repositories invalidieren
   */
  invalidateAllCache(): void {
    this.schnitte.invalidateCache();
    this.maschinenEfficiency.invalidateCache();
    this.ablaengerei.invalidateCache();
    this.cuttingChart.invalidateCache();
    this.lagerCuts.invalidateCache();
    console.log('Cache für alle Production-Repositories invalidiert');
  }
  
  /**
   * Gesamtproduktionsstatistiken abrufen
   */
  async getOverallProductionStats(): Promise<{
    dailyAverages: any;
    machineEfficiency: any;
    warehousePerformance: any;
    trends: any;
  }> {
    const [dailySums, efficiency, warehousePerf, trends] = await Promise.all([
      this.schnitte.getDailySums(),
      this.maschinenEfficiency.getAverageEfficiency(),
      this.ablaengerei.getWarehousePerformance(),
      this.cuttingChart.getTrendAnalysis()
    ]);
    
    return {
      dailyAverages: dailySums,
      machineEfficiency: efficiency,
      warehousePerformance: warehousePerf,
      trends
    };
  }
}