import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { InventoryRecommendationPanel } from '../InventoryRecommendationPanel';
import { 
  ReorderRecommendation, 
  PurchaseRecommendation, 
  StockAnomaly 
} from '../../../services/inventory/types';

describe('InventoryRecommendationPanel', () => {
  const mockReorderRecommendations: ReorderRecommendation[] = [
    {
      itemId: 'item-1',
      currentStock: 50,
      reorderPoint: 100,
      recommendedOrderQuantity: 200,
      urgency: 'critical',
      reasoning: 'Stock level below critical threshold',
      confidence: 0.9,
      estimatedStockoutDate: new Date('2024-01-15'),
      leadTime: 7,
      safetyStock: 25
    },
    {
      itemId: 'item-2',
      currentStock: 150,
      reorderPoint: 200,
      recommendedOrderQuantity: 300,
      urgency: 'medium',
      reasoning: 'Approaching reorder point',
      confidence: 0.8,
      leadTime: 10,
      safetyStock: 50
    }
  ];

  const mockPurchaseRecommendations: PurchaseRecommendation[] = [
    {
      itemId: 'item-1',
      recommendedQuantity: 200,
      estimatedCost: 1000,
      supplier: 'Supplier A',
      priority: 5,
      reasoning: 'Critical stock level requires immediate purchase',
      expectedDelivery: new Date('2024-01-20'),
      alternatives: [
        {
          itemId: 'item-1',
          quantity: 200,
          cost: 1100,
          supplier: 'Supplier B',
          pros: ['Faster delivery'],
          cons: ['Higher cost']
        }
      ]
    }
  ];

  const mockStockAnomalies: StockAnomaly[] = [
    {
      id: 'anomaly-1',
      itemId: 'item-3',
      anomalyType: 'sudden_spike',
      severity: 'high',
      detectedAt: new Date('2024-01-10'),
      description: 'Unusual consumption spike detected',
      currentValue: 500,
      expectedValue: 200,
      deviation: 150,
      recommendations: ['Investigate cause', 'Adjust forecasting model'],
      confidence: 0.85
    }
  ];

  const mockOnApproveRecommendation = vi.fn();
  const mockOnDismissAnomaly = vi.fn();
  const mockOnViewDetails = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders recommendation panel with summary cards', () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Check summary cards
    expect(screen.getByText('Nachbestellungen')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument(); // Number of reorder recommendations
    expect(screen.getByText('1 dringend')).toBeInTheDocument(); // Urgent recommendations

    expect(screen.getByText('Einkaufsempfehlungen')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // Number of purchase recommendations
    expect(screen.getByText('1.000 €')).toBeInTheDocument(); // Total cost

    expect(screen.getByText('Anomalien')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // Number of anomalies
    expect(screen.getByText('1 kritisch')).toBeInTheDocument(); // Critical anomalies
  });

  it('displays reorder recommendations sorted by urgency', () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Check reorder recommendations section
    expect(screen.getByText('Nachbestellempfehlungen')).toBeInTheDocument();
    expect(screen.getByText('item-1')).toBeInTheDocument();
    expect(screen.getByText('item-2')).toBeInTheDocument();
    expect(screen.getByText('Stock level below critical threshold')).toBeInTheDocument();
    expect(screen.getByText('Approaching reorder point')).toBeInTheDocument();

    // Check urgency badges
    expect(screen.getByText('Kritisch')).toBeInTheDocument();
    expect(screen.getByText('Mittel')).toBeInTheDocument();
  });

  it('shows stockout date alert for critical items', () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Check stockout date alert
    expect(screen.getByText('Voraussichtlicher Lagerausfall: 15.1.2024')).toBeInTheDocument();
  });

  it('displays purchase recommendations with alternatives', () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Check purchase recommendations
    expect(screen.getByText('Einkaufsempfehlungen')).toBeInTheDocument();
    expect(screen.getByText('1.000 €')).toBeInTheDocument();
    expect(screen.getByText('200 Stück')).toBeInTheDocument();
    expect(screen.getByText('Supplier A')).toBeInTheDocument();

    // Check alternatives
    expect(screen.getByText('Alternativen:')).toBeInTheDocument();
    expect(screen.getByText('Supplier B')).toBeInTheDocument();
    expect(screen.getByText('1.100 €')).toBeInTheDocument();
    expect(screen.getByText('Faster delivery')).toBeInTheDocument();
  });

  it('shows stock anomalies with recommendations', () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Check anomalies section
    expect(screen.getByText('Bestandsanomalien')).toBeInTheDocument();
    expect(screen.getByText('item-3')).toBeInTheDocument();
    expect(screen.getByText('Unusual consumption spike detected')).toBeInTheDocument();
    expect(screen.getByText('Hoch')).toBeInTheDocument(); // Severity badge
    expect(screen.getByText('150%')).toBeInTheDocument(); // Deviation

    // Check recommendations
    expect(screen.getByText('Empfohlene Maßnahmen:')).toBeInTheDocument();
    expect(screen.getByText('Investigate cause')).toBeInTheDocument();
    expect(screen.getByText('Adjust forecasting model')).toBeInTheDocument();
  });

  it('calls onApproveRecommendation when approve button is clicked', async () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Click approve button for reorder recommendation
    const approveButtons = screen.getAllByText('Genehmigen');
    fireEvent.click(approveButtons[0]);

    await waitFor(() => {
      expect(mockOnApproveRecommendation).toHaveBeenCalledWith('item-1', 'reorder');
    });
  });

  it('calls onApproveRecommendation for purchase when order button is clicked', async () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Click order button for purchase recommendation
    const orderButton = screen.getByText('Bestellen');
    fireEvent.click(orderButton);

    await waitFor(() => {
      expect(mockOnApproveRecommendation).toHaveBeenCalledWith('item-1', 'purchase');
    });
  });

  it('calls onDismissAnomaly when ignore button is clicked', async () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Click ignore button for anomaly
    const ignoreButton = screen.getByText('Ignorieren');
    fireEvent.click(ignoreButton);

    await waitFor(() => {
      expect(mockOnDismissAnomaly).toHaveBeenCalledWith('anomaly-1');
    });
  });

  it('calls onViewDetails when details button is clicked', async () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Click details button
    const detailsButtons = screen.getAllByText('Details anzeigen');
    fireEvent.click(detailsButtons[0]);

    await waitFor(() => {
      expect(mockOnViewDetails).toHaveBeenCalledWith('item-1');
    });
  });

  it('displays empty state when no recommendations exist', () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={[]}
        purchaseRecommendations={[]}
        stockAnomalies={[]}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Check empty states
    expect(screen.getByText('Keine Nachbestellungen erforderlich')).toBeInTheDocument();
    expect(screen.getByText('Alle Artikel haben ausreichende Bestände')).toBeInTheDocument();
    expect(screen.getByText('Keine Einkaufsempfehlungen')).toBeInTheDocument();
    expect(screen.getByText('Keine Anomalien erkannt')).toBeInTheDocument();
  });

  it('displays progress bars for stock levels', () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Check progress bar labels
    expect(screen.getByText('Bestand vs. Meldebestand')).toBeInTheDocument();
    expect(screen.getByText('50%')).toBeInTheDocument(); // Stock level percentage for item-1
    expect(screen.getByText('75%')).toBeInTheDocument(); // Stock level percentage for item-2
  });

  it('handles missing callback props gracefully', () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
      />
    );

    // Should render without errors even without callback props
    expect(screen.getByText('Nachbestellempfehlungen')).toBeInTheDocument();
    expect(screen.getByText('Einkaufsempfehlungen')).toBeInTheDocument();
    expect(screen.getByText('Bestandsanomalien')).toBeInTheDocument();
  });

  it('sorts recommendations by urgency correctly', () => {
    const unsortedRecommendations: ReorderRecommendation[] = [
      {
        ...mockReorderRecommendations[1],
        urgency: 'low'
      },
      {
        ...mockReorderRecommendations[0],
        urgency: 'critical'
      },
      {
        itemId: 'item-3',
        currentStock: 100,
        reorderPoint: 150,
        recommendedOrderQuantity: 250,
        urgency: 'high',
        reasoning: 'High priority item',
        confidence: 0.85,
        leadTime: 5,
        safetyStock: 30
      }
    ];

    render(
      <InventoryRecommendationPanel
        reorderRecommendations={unsortedRecommendations}
        purchaseRecommendations={[]}
        stockAnomalies={[]}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Check that critical items appear first
    const urgencyBadges = screen.getAllByText(/Kritisch|Hoch|Niedrig/);
    expect(urgencyBadges[0]).toHaveTextContent('Kritisch');
    expect(urgencyBadges[1]).toHaveTextContent('Hoch');
    expect(urgencyBadges[2]).toHaveTextContent('Niedrig');
  });

  it('displays confidence levels correctly', () => {
    render(
      <InventoryRecommendationPanel
        reorderRecommendations={mockReorderRecommendations}
        purchaseRecommendations={mockPurchaseRecommendations}
        stockAnomalies={mockStockAnomalies}
        onApproveRecommendation={mockOnApproveRecommendation}
        onDismissAnomaly={mockOnDismissAnomaly}
        onViewDetails={mockOnViewDetails}
      />
    );

    // Check confidence percentages
    expect(screen.getByText('90%')).toBeInTheDocument(); // Reorder recommendation confidence
    expect(screen.getByText('85%')).toBeInTheDocument(); // Anomaly confidence
  });
});