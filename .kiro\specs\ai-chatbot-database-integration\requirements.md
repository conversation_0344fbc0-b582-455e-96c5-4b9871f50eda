# Requirements Document

## Introduction

This feature extends the existing AI chatbot system to provide real-time database insights by integrating with the application's data repositories. The chatbot will be able to answer questions about current and historical incidents (Störungen), dispatch operations (Versand), cutting operations (Ablängerei), and key performance indicators by querying the live database and providing contextual responses.

## Requirements

### Requirement 1

**User Story:** As a user interacting with the AI chatbot, I want the chatbot to recognize when my questions relate to database content, so that I can get data-driven responses instead of generic answers.

#### Acceptance Criteria

1. WHEN a user asks a question containing keywords like "Störungen", "incidents", "Versand", "dispatch", "Ablängerei", "cutting", "KPI", "performance", or "statistics" THEN the system SHALL analyze the intent and determine if database queries are needed
2. WHEN the system detects a database-related query THEN it SHALL identify which repositories need to be queried (StoerungenRepository, DispatchRepository, CuttingRepository)
3. WHEN a user asks about time-specific data (e.g., "last week", "today", "this month") THEN the system SHALL parse the time range and apply appropriate date filters
4. WHEN the intent recognition is uncertain THEN the system SHALL default to querying relevant repositories to provide comprehensive context

### Requirement 2

**User Story:** As a user asking about incidents and disruptions, I want the chatbot to provide current statistics and details from the Störungen database, so that I can get real-time insights about system status.

#### Acceptance Criteria

1. WHEN a user asks about "Störungen" or "incidents" THEN the system SHALL query the StoerungenRepository for current statistics including total, active, resolved, and severity breakdown
2. WHEN a user asks about specific incident details THEN the system SHALL retrieve recent incidents with their status, severity, affected systems, and resolution times
3. WHEN a user asks about system status THEN the system SHALL query system status data and provide current health information
4. WHEN a user asks about incident trends THEN the system SHALL provide MTTR statistics and resolution rates

### Requirement 3

**User Story:** As a user asking about dispatch operations, I want the chatbot to access current performance metrics and operational data, so that I can understand logistics performance.

#### Acceptance Criteria

1. WHEN a user asks about "Versand" or "dispatch" performance THEN the system SHALL query DispatchRepository for service level data, daily performance metrics, and delivery statistics
2. WHEN a user asks about picking operations THEN the system SHALL retrieve ATRL, ARIL, and filling grade data
3. WHEN a user asks about returns or quality metrics THEN the system SHALL provide QM data including accepted, rejected, and open items
4. WHEN a user asks about tonnage or production THEN the system SHALL retrieve production tonnage and direct loading statistics

### Requirement 4

**User Story:** As a user asking about cutting operations, I want the chatbot to provide insights about machine efficiency and cutting performance, so that I can monitor production effectiveness.

#### Acceptance Criteria

1. WHEN a user asks about "Ablängerei" or "cutting" operations THEN the system SHALL query CuttingRepository for cutting data including cutTT, cutTR, cutRR, and pickCut metrics
2. WHEN a user asks about machine efficiency THEN the system SHALL retrieve machine performance data with actual vs. target cutting rates
3. WHEN a user asks about warehouse cuts THEN the system SHALL provide lager cut data for different categories (200, 220, 240)
4. WHEN a user asks about top performing machines THEN the system SHALL identify and rank machines by efficiency

### Requirement 5

**User Story:** As a user receiving AI responses, I want the chatbot to present database information in a clear, contextual format within the conversation, so that I can easily understand the insights.

#### Acceptance Criteria

1. WHEN database data is retrieved THEN the system SHALL format the data in a human-readable structure for the LLM context
2. WHEN providing statistics THEN the system SHALL include relevant comparisons, trends, and contextual information
3. WHEN multiple data sources are queried THEN the system SHALL organize the information logically and highlight key insights
4. WHEN no relevant data is found THEN the system SHALL inform the user and suggest alternative queries
5. WHEN data is time-sensitive THEN the system SHALL include timestamps and indicate data freshness