# Störungen Modul

Das **Störungen Modul** ist ein umfassendes System für das Management und Monitoring von Systemstörungen in der JOZI1 Lapp Dashboard Anwendung. Es bietet Funktionen zur Erfassung, Verfolgung und Analyse von Störungen sowie Live-Monitoring der Systemgesundheit.

## 📋 Übersicht

Das Modul ist in drei Hauptbereiche unterteilt:
- **Live Monitoring**: Echtzeitüberwachung der Systemstatus
- **Störungsmanagement**: Erfassung und Verwaltung von Störungen
- **Analytics Dashboard**: Analyse und Trends von Störungsdaten

## 🏗️ Architektur

### Modulstruktur
```
src/modules/stoerungen/
├── index.ts                    # Hauptexport des Moduls
├── module.config.ts            # Modulkonfiguration
├── pages/
│   ├── index.ts               # Page-Exports
│   └── StoerungenPage.tsx     # Hauptseite mit Tab-Navigation
├── components/
│   ├── index.ts               # Component-Exports
│   ├── StoerungNavigation.tsx # Modulspezifische Navigation
│   ├── stoerungen/            # Störungsmanagement-Komponenten
│   │   ├── index.ts
│   │   ├── StoerungsForm.tsx
│   │   ├── StoerungsList.tsx
│   │   └── SystemStatusOverview.tsx
│   └── monitoring/            # Monitoring-Komponenten
│       ├── index.ts
│       ├── HealthIndicator.tsx
│       ├── PerformanceDashboard.tsx
│       ├── ErrorMonitoring.tsx
│       └── CacheAnalyticsWidget.tsx
```

### Modulkonfiguration
- **ID**: `stoerungen`
- **Anzeigename**: Störungen
- **Icon**: AlertTriangle
- **Basis-Route**: `/modules/stoerungen`
- **Erforderliche Rollen**: `['Benutzer', 'Administrator']`

## 🎯 Hauptfunktionen

### 1. Live Monitoring

#### SystemStatusOverview
- **Zweck**: Zeigt den aktuellen Status aller überwachten Systeme
- **Features**:
  - Echtzeit-Systemstatus (OK, WARNING, ERROR)
  - Farbkodierte Status-Badges
  - Letzte Überprüfungszeiten
  - Gesamtstatus-Übersicht
- **Systeme**: FTS, ARiL, ATrL, SAP, Scanner, etc.

#### HealthIndicator
- **Zweck**: Kompakter Gesundheitsindikator für die Navigation
- **Features**:
  - Gesundheitsscore (0-100%)
  - Status-Icons (CheckCircle, AlertTriangle, XCircle)
  - Uptime-Anzeige
  - Database/Cache-Status
  - Kompakte und detaillierte Ansicht

### 2. Störungsmanagement

#### StoerungsForm
- **Zweck**: Erfassung neuer Störungen
- **Felder**:
  - Titel (erforderlich)
  - Beschreibung
  - Schweregrad (LOW, MEDIUM, HIGH, CRITICAL)
  - Kategorie (System, Hardware, Software, etc.)
  - Betroffenes System
  - Standort
  - Gemeldet von / Zugewiesen an
  - Tags
- **Features**:
  - Dropdown-Auswahl für Systeme und Kategorien
  - Tag-Management
  - Validierung und Fehlerbehandlung

#### StoerungsList
- **Zweck**: Übersicht und Verwaltung aller Störungen
- **Features**:
  - Filterbare Liste (Status, Schweregrad, System)
  - Suchfunktion
  - Zeitbereich-Filter
  - Status-Updates
  - MTTR-Anzeige
  - Kommentar-System
- **Status-Workflow**: NEW → IN_PROGRESS → RESOLVED

### 3. Analytics Dashboard

#### Charts und Visualisierungen
- **StoerungsStatsChart**: Störungsstatistiken über Zeit
- **SystemStatusHeatmap**: Heatmap der Systemverfügbarkeit
- **StoerungsCategoryChart**: Kategorieverteilung
- **MTTRTrendChart**: Mean Time To Resolution Trends

#### PerformanceDashboard
- **Zweck**: Umfassende Performance-Metriken
- **Bereiche**:
  - System Health Score
  - Database Performance
  - Cache Performance
  - System Resources
- **Metriken**:
  - Abfragezeiten
  - Cache Hit Rate
  - Speicherauslastung
  - Event Loop Lag

#### ErrorMonitoring
- **Zweck**: Überwachung von Systemfehlern
- **Features**:
  - Error-Statistiken nach Schweregrad
  - 24h-Trends
  - Aktuelle Fehler-Liste
  - Fehler-Kategorisierung

#### CacheAnalyticsWidget
- **Zweck**: Detaillierte Cache-Performance-Analyse
- **Metriken**:
  - Hit/Miss Rate
  - Speicherauslastung
  - Hot Keys
  - Zugriffzeiten
  - Size Distribution

## 🔧 Technische Details

### Datentypen
```typescript
interface Stoerung {
  id: number;
  title: string;
  description?: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status: 'NEW' | 'IN_PROGRESS' | 'RESOLVED';
  category?: string;
  affected_system?: string;
  location?: string;
  reported_by?: string;
  assigned_to?: string;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  mttr_minutes?: number;
  tags?: string[];
  comments?: StoerungComment[];
}

interface SystemStatus {
  id: number;
  system_name: string;
  status: 'OK' | 'WARNING' | 'ERROR';
  last_check: string;
  created_at: string;
  updated_at: string;
}
```

### Services Integration
- **stoerungenService**: API-Calls für Störungsdaten
- **apiService**: Allgemeine API-Kommunikation
- **TanStack Query**: Caching und Server State Management

### Error Handling
- Graceful Fallbacks mit Mock-Daten
- Benutzerfreundliche deutsche Fehlermeldungen
- Loading States und Error Boundaries
- Offline-Unterstützung

## 🎨 UI/UX Features

### Design System
- **shadcn/ui** Komponenten
- **Neobrutalism Design** mit starken Borders
- **Farbkodierung** nach Schweregrad/Status
- **Responsive Design** für alle Bildschirmgrößen

### Navigation
- **Tab-basierte Navigation** (Monitoring, Management, Analytics)
- **Breadcrumb-Navigation** mit Kontext
- **Modulspezifische Navigation** mit Health Indicator
- **Mobile-optimierte** Sheet-Navigation

### Interaktivität
- **Real-time Updates** ohne Full-Page-Refresh
- **Auto-Refresh** Funktionalität
- **Date Range Picker** für Zeitbereich-Filter
- **Drag & Drop** für Tag-Management
- **Keyboard Shortcuts** für häufige Aktionen

## 🔄 Datenfluss

### Refresh-Mechanismus
```typescript
const [refreshKey, setRefreshKey] = useState(0);

const handleRefresh = () => {
  setRefreshKey(prev => prev + 1);
};

// Komponenten reagieren auf refreshKey-Änderungen
useEffect(() => {
  fetchData();
}, [refreshKey]);
```

### State Management
- **React State** für lokale Komponentenzustände
- **TanStack Query** für Server State
- **Props Drilling** für Refresh-Koordination
- **Error Boundaries** für Fehlerbehandlung

## 🚀 Performance Optimierungen

### Caching Strategy
- **TanStack Query** für API-Response-Caching
- **React.memo** für Component-Memoization
- **Lazy Loading** für große Datensätze
- **Virtualization** für große Listen (>100 Einträge)

### Update Intervals
- **Health Indicator**: 60 Sekunden
- **Performance Dashboard**: 60 Sekunden
- **Error Monitoring**: 120 Sekunden
- **Cache Analytics**: 90 Sekunden

## 🔐 Sicherheit & Berechtigungen

### Rollenbasierte Zugriffskontrolle
- **Besucher**: Kein Zugriff auf Störungen-Modul
- **Benutzer**: Vollzugriff auf alle Funktionen
- **Administrator**: Vollzugriff + erweiterte Einstellungen

### Datenvalidierung
- **Frontend-Validierung** für Benutzereingaben
- **Backend-Validierung** für API-Requests
- **XSS-Schutz** durch Input-Sanitization
- **CSRF-Schutz** durch Token-Validierung

## 📊 Monitoring & Metriken

### Key Performance Indicators (KPIs)
- **MTTR** (Mean Time To Resolution)
- **System Availability** (Uptime %)
- **Error Rate** nach Schweregrad
- **Response Time** für Störungsbearbeitung

### Alerting
- **Critical Störungen** → Sofortige Benachrichtigung
- **System Offline** → Health Indicator Update
- **Performance Degradation** → Dashboard Alerts

## 🛠️ Entwicklung & Wartung

### Code Standards
- **TypeScript Strict Mode**
- **ESLint + Prettier** Konfiguration
- **Component-basierte Architektur**
- **Error Boundaries** für alle Charts

### Testing Strategy
- **Unit Tests** für Services und Utils
- **Component Tests** für UI-Komponenten
- **Integration Tests** für API-Calls
- **E2E Tests** für kritische User Flows

### Deployment
- **Hot Reload** in Development
- **Build Optimization** für Production
- **Error Tracking** mit Sentry
- **Performance Monitoring** mit Custom Metrics

## 🔮 Zukünftige Erweiterungen

### Geplante Features
- **AI-basierte Störungsanalyse** mit OpenRouter Integration
- **Automatische Störungserkennung** durch System Monitoring
- **Mobile App** für Störungsmeldungen
- **Integration** mit externen Ticketing-Systemen

### Technische Verbesserungen
- **WebSocket** für Real-time Updates
- **Service Worker** für Offline-Funktionalität
- **Push Notifications** für kritische Störungen
- **Advanced Analytics** mit Machine Learning

---

*Dieses Modul ist Teil der JOZI1 Lapp Dashboard Anwendung und folgt den etablierten Architektur- und Design-Patterns der Gesamtanwendung.*