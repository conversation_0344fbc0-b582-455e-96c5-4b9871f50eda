/**
 * Konfigurations-Context für IPC
 * 
 * Stellt sichere Konfigurationswerte vom Main-Process
 * an den Renderer-Process bereit.
 */

import { contextBridge, ipcRenderer } from 'electron';

/**
 * Konfigurationswerte für das Frontend
 */
export interface AppConfig {
  apiKey: string;
  apiBaseUrl: string;
  environment: 'development' | 'production' | 'test';
  version: string;
}

/**
 * IPC-Kanäle für Konfiguration
 */
export const CONFIG_CHANNELS = {
  GET_CONFIG: 'config:get',
  CONFIG_UPDATED: 'config:updated'
} as const;

/**
 * Context-Bridge API für Konfiguration
 */
export const configAPI = {
  /**
   * Ruft die aktuelle Konfiguration ab
   */
  getConfig: (): Promise<AppConfig> => {
    return ipcRenderer.invoke(CONFIG_CHANNELS.GET_CONFIG);
  },

  /**
   * Lauscht auf Konfigurationsänderungen
   */
  onConfigUpdated: (callback: (config: AppConfig) => void) => {
    const listener = (_event: any, config: AppConfig) => callback(config);
    ipcRenderer.on(CONFIG_CHANNELS.CONFIG_UPDATED, listener);
    
    // Cleanup-Funktion zurückgeben
    return () => {
      ipcRenderer.removeListener(CONFIG_CHANNELS.CONFIG_UPDATED, listener);
    };
  }
};

// Context-Bridge registrieren (wird in preload.ts aufgerufen)
export function exposeConfigAPI() {
  contextBridge.exposeInMainWorld('configAPI', configAPI);
}