import React from 'react';
import { Activity, Database, Clock, CheckCircle, AlertTriangle, TrendingUp, Zap, Settings } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { SubtlePatternCard } from '@/components/ui/Card_SubtlePattern';
import { useWorkflows } from '@/hooks/useWorkflows';

export function WorkflowOverview() {
  const { processes, stats, loading, error } = useWorkflows();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  const getSystemHealthStatus = () => {
    const runningProcesses = processes.filter(p => p.status === 'running').length;
    const errorProcesses = processes.filter(p => p.status === 'error').length;
    const totalProcesses = processes.length;

    if (errorProcesses > totalProcesses / 2) return { status: 'critical', color: 'red' };
    if (errorProcesses > 0 || runningProcesses > 0) return { status: 'warning', color: 'yellow' };
    return { status: 'healthy', color: 'green' };
  };

  const systemHealth = getSystemHealthStatus();
  const successRate = stats ? (stats.totalExecutions > 0 ? (stats.successfulExecutions / stats.totalExecutions) * 100 : 0) : 0;

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  return (
    <div className="space-y-6">
      {/* System Status Overview mit SubtlePatternCard Design */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* System Status Card */}
        <SubtlePatternCard
          title="System Status"
          value={
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${systemHealth.color === 'green' ? 'bg-green-500' :
                  systemHealth.color === 'yellow' ? 'bg-yellow-500' : 'bg-red-500'
                } animate-pulse`}></div>
              <span className="font-medium capitalize">
                {systemHealth.status === 'healthy' ? 'Gesund' :
                  systemHealth.status === 'warning' ? 'Warnung' : 'Kritisch'}
              </span>
            </div>
          }
          subtitle={
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span>Aktive Prozesse:</span>
                <span className="font-semibold">{processes.filter(p => p.status === 'running').length}</span>
              </div>
              <div className="flex justify-between">
                <span>Fehlerhafte Prozesse:</span>
                <span className="font-semibold text-red-600">{processes.filter(p => p.status === 'error').length}</span>
              </div>
            </div>
          }
          icon={<Activity className="h-5 w-5" />}
          iconColor="#3b82f6"
          valueClassName={
            systemHealth.color === 'green' ? 'text-green-600' :
              systemHealth.color === 'yellow' ? 'text-yellow-600' : 'text-red-600'
          }
        />

        {/* Performance Card */}
        <SubtlePatternCard
          title="Performance"
          value={`${successRate.toFixed(1)}%`}
          subtitle={
            <div className="space-y-1 text-xs">
              <div className="mb-2">
                <Progress value={successRate} className="h-2" />
              </div>
              <div className="flex justify-between">
                <span>Ø Ausführungszeit:</span>
                <span className="font-semibold">{formatDuration(stats?.averageDuration || 0)}</span>
              </div>
              <div className="flex justify-between">
                <span>Gesamt Ausführungen:</span>
                <span className="font-semibold">{stats?.totalExecutions || 0}</span>
              </div>
            </div>
          }
          icon={<TrendingUp className="h-5 w-5" />}
          iconColor="#10b981"
          valueClassName="text-green-600"
        />

        {/* Letzte Aktivität Card */}
        <SubtlePatternCard
          title="Letzte Aktivität"
          value={stats?.lastExecution ? 'Aktiv' : 'Keine Ausführungen'}
          subtitle={
            stats?.lastExecution ? (
              <div className="text-xs">
                <p className="text-gray-600">Letzte Ausführung:</p>
                <p className="font-medium">{stats.lastExecution.toLocaleString('de-DE')}</p>
              </div>
            ) : (
              <p className="text-xs text-gray-500">Keine Ausführungen</p>
            )
          }
          icon={<Clock className="h-5 w-5" />}
          iconColor="#f97316"
          valueClassName={stats?.lastExecution ? "text-orange-600" : "text-gray-500"}
        />
      </div>

      {/* Workflow Systems */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* SAP Workflows */}
        <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-purple-600" />
              SAP Workflows
            </CardTitle>
            <CardDescription>
              Automatisierte SAP-Datenexport-Prozesse
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Prozesse:</span>
                <span className="ml-2 font-medium">{processes.length}</span>
              </div>
              <div>
                <span className="text-gray-600">Aktiviert:</span>
                <span className="ml-2 font-medium">{processes.filter(p => p.isActive).length}</span>
              </div>
            </div>

            <div className="space-y-2">
              {processes.slice(0, 3).map((process) => (
                <div key={process.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span className="text-sm font-medium">{process.name}</span>
                  <div className="flex items-center gap-2">
                    {/* Aktivierungsstatus Badge */}
                    <Badge
                      variant="outline"
                      className={
                        process.isActive
                          ? 'bg-green-100 text-green-800 border-green-300'
                          : 'bg-gray-100 text-gray-600 border-gray-300'
                      }
                    >
                      {process.isActive ? 'Aktiviert' : 'Deaktiviert'}
                    </Badge>
                    {/* Ausführungsstatus Badge */}
                    <Badge
                      variant={process.status === 'completed' ? 'secondary' :
                        process.status === 'error' ? 'destructive' : 'outline'}
                      className={process.status === 'completed' ? 'bg-blue-100 text-blue-800' : ''}
                    >
                      {process.status === 'running' ? 'Läuft' :
                        process.status === 'completed' ? 'Bereit' :
                          process.status === 'error' ? 'Fehler' : 'Bereit'}
                    </Badge>
                  </div>
                </div>
              ))}
              {processes.length > 3 && (
                <p className="text-xs text-gray-500 text-center">
                  +{processes.length - 3} weitere Prozesse
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Future Workflow Systems */}
        <Card className="border-2 border-dashed border-gray-300">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-500">
              <Zap className="h-5 w-5" />
              Weitere Workflow-Systeme
            </CardTitle>
            <CardDescription>
              Zukünftige Automatisierungsprozesse
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center py-8">
              <Settings className="h-8 w-8 mx-auto mb-3 text-gray-400" />
              <p className="text-sm text-gray-500 mb-4">
                Hier können weitere Workflow-Systeme integriert werden
              </p>
              <div className="space-y-2 text-xs text-gray-400">
                <p>• Email-Automatisierung</p>
                <p>• Datenbank-Synchronisation</p>
                <p>• Report-Generierung</p>
                <p>• API-Integrationen</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="text-black border-shadow-md border-slate-800 bg-slate-50 p-6 transition-all duration-300 hover:border-slate-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Schnellaktionen
          </CardTitle>
          <CardDescription>
            Häufig verwendete Workflow-Operationen
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button variant="outline" size="sm">
              <CheckCircle className="h-4 w-4 mr-2" />
              Alle Status prüfen
            </Button>
            <Button variant="outline" size="sm">
              <Activity className="h-4 w-4 mr-2" />
              System-Health-Check
            </Button>
            <Button variant="outline" size="sm">
              <Clock className="h-4 w-4 mr-2" />
              Letzte Logs anzeigen
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Konfiguration prüfen
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}