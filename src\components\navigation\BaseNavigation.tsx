import { type ReactNode, useState, useEffect } from "react";
import lappLogo from '@/assets/lappAlive.png';
import { HealthIndicator } from "@/modules/stoerungen/components";
import { UserMenu } from "./UserMenu";
import { SystemStatusIndicator } from "@/components/status";
import Tabnavbar from "./spectrumui/tabnavbar";
import { NavigationConfig } from "@/config/navigation/types";
import ThemeToggle from "@/components/ui/theme-toggle";

interface BaseNavigationProps {
    title?: ReactNode;
    navigationConfig: NavigationConfig;
    showUserMenu?: boolean;
    children?: ReactNode;
}

/**
 * Base Navigation Component
 *
 * Gemeinsame Navigation-Komponente für alle Module mit:
 * - Logo und Titel (links)
 * - Konfigurierbare Navigation (mitte)
 * - Health Indicator und User Menu (rechts)
 * - Optionale Zusatzaktionen über children (z. B. Links/Buttons)
 */
export const BaseNavigation = ({
    title,
    navigationConfig,
    showUserMenu = true,
    children
}: BaseNavigationProps) => {
    // Theme-State für das Toggle
    const [isDarkTheme, setIsDarkTheme] = useState(false);

    // Theme aus localStorage laden beim Start
    useEffect(() => {
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            setIsDarkTheme(true);
        } else if (savedTheme === 'light') {
            setIsDarkTheme(false);
        } else {
            // System-Theme prüfen
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            setIsDarkTheme(prefersDark);
        }
    }, []);

    // Handler für Theme-Wechsel
    const handleThemeToggle = (isDark: boolean) => {
        setIsDarkTheme(isDark);
        const theme = isDark ? 'dark' : 'light';
        localStorage.setItem('theme', theme);

        // Theme auf document root anwenden
        if (isDark) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    };
    return (
        <div className="w-screen h-14 border-b-2 border-black flex items-center relative px-4" style={{ backgroundColor: '#ff7a05' }}>
            {/* Linke Seite mit Logo - feste Breite */}
            <div className="flex items-center gap-2 w-100">
                <img src={lappLogo} alt="Lapp Logo" className="h-12 object-contain" />
                {title && (
                    <div className="flex select-none whitespace-nowrap p-2 text-sm font-bold">
                        {title}
                    </div>
                )}
            </div>

            {/* Mittlere Navbar: absolut zentriert */}
            <div className="absolute left-1/2 transform -translate-x-1/2">
                <div className="relative">
                    <Tabnavbar menu={navigationConfig.menu} />
                </div>
            </div>

            {/* Rechte Seite mit optionalen Children, Status Indicator, Health Indicator, Theme Toggle und User Menu - absolut rechts */}
            <div className="absolute right-4 flex items-center gap-3">
                {children}
                <SystemStatusIndicator />
                <HealthIndicator compact={true} showDetails={true} />
                {/* Theme-Toggle für schnellen Zugriff */}
                <div className="scale-50 -mt-3 -mr-6">
                    <ThemeToggle
                        checked={isDarkTheme}
                        onChange={handleThemeToggle}
                    />
                </div>
                {showUserMenu && <UserMenu />}
            </div>
        </div>
    );
};