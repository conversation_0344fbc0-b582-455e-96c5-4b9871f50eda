# Test-Anleitung: Automatische Bereitschaftsperson-Zuweisung

## Übersicht

Diese Anleitung beschreibt, wie die neu implementierte automatische Zuweisung der Bereitschaftsperson bei Störungsmeldungen getestet werden kann.

## Voraussetzungen

1. **Anwendung starten**:
   ```bash
   npm run dev
   ```
   - Frontend läuft auf: http://localhost:3000
   - Backend läuft auf: http://localhost:3001

2. **Testdaten vorbereiten**:
   - Mindestens eine Bereitschaftsperson in der Datenbank
   - Eine aktive Bereitschaftswoche für die aktuelle Woche

## Test-Szenarien

### 1. Erfolgreiche automatische Zuweisung

**Schritte:**
1. Navigiere zu "Störungen" → "Neue Störung"
2. Öffne das Störungsformular
3. Beobachte das Feld "Zugewiesen an"

**Erwartetes Verhalten:**
- ✅ Das Feld "Zugewiesen an" wird automatisch mit dem Namen der Bereitschaftsperson gefüllt
- ✅ Unter dem Feld erscheint: "✅ Automatisch zugewiesen an: [Name der Person]"
- ✅ Das Eingabefeld ist deaktiviert (grau hinterlegt)
- ✅ Die Checkbox "Automatische Zuweisung an Bereitschaftsperson" ist aktiviert

### 2. E-Mail-Template-Generierung

**Schritte:**
1. Fülle das Störungsformular aus:
   - **Titel**: "Test-Störung für Bereitschaftsperson"
   - **Beschreibung**: "Dies ist eine Test-Störung zur Überprüfung der automatischen Zuweisung"
   - **Schweregrad**: "Hoch"
   - **Kategorie**: Beliebige Kategorie auswählen
   - **Betroffenes System**: Beliebiges System auswählen
   - **Standort**: "Teststandort"
   - **Gemeldet von**: "Test-Benutzer"

2. Klicke auf "Speichern"

**Erwartetes Verhalten:**
- ✅ Vor dem Speichern erscheint ein neuer Bereich "E-Mail Vorschau für Bereitschaftsperson"
- ✅ Die E-Mail-Vorschau enthält:
  - **An**: E-Mail-Adresse der Bereitschaftsperson
  - **Betreff**: "Neue Störung zugewiesen: [Titel der Störung]"
  - **Nachricht**: Vollständige E-Mail mit allen Störungsdetails
- ✅ Die Störung wird erfolgreich gespeichert
- ✅ Erfolgsmeldung: "Die neue Störung wurde erfolgreich angelegt und an [Name] zugewiesen."

### 3. Manuelle Deaktivierung der automatischen Zuweisung

**Schritte:**
1. Öffne das Störungsformular
2. Deaktiviere die Checkbox "Automatische Zuweisung an Bereitschaftsperson"

**Erwartetes Verhalten:**
- ✅ Das Feld "Zugewiesen an" wird geleert
- ✅ Das Eingabefeld wird wieder editierbar (weiß hinterlegt)
- ✅ Die Status-Anzeige verschwindet
- ✅ Manuelle Eingabe ist wieder möglich

### 4. Reaktivierung der automatischen Zuweisung

**Schritte:**
1. Aktiviere die Checkbox "Automatische Zuweisung an Bereitschaftsperson" wieder

**Erwartetes Verhalten:**
- ✅ Die Bereitschaftsperson wird erneut geladen und zugewiesen
- ✅ Status-Anzeige erscheint wieder
- ✅ Eingabefeld wird wieder deaktiviert

### 5. Fehlerbehandlung - Keine Bereitschaftsperson verfügbar

**Vorbereitung:**
- Stelle sicher, dass keine aktive Bereitschaftswoche für die aktuelle Woche existiert
- Oder deaktiviere temporär alle Bereitschaftspersonen

**Schritte:**
1. Öffne das Störungsformular
2. Beobachte das Verhalten

**Erwartetes Verhalten:**
- ⚠️ Status-Anzeige: "⚠️ Keine aktive Bereitschaftsperson für diese Woche gefunden"
- ✅ Die Checkbox wird automatisch deaktiviert
- ✅ Das Eingabefeld bleibt editierbar
- ✅ Störungen können trotzdem normal erstellt werden

### 6. Fehlerbehandlung - Service nicht verfügbar

**Vorbereitung:**
- Stoppe das Backend temporär oder simuliere einen Service-Fehler

**Schritte:**
1. Öffne das Störungsformular
2. Beobachte das Verhalten

**Erwartetes Verhalten:**
- ❌ Status-Anzeige: "❌ Fehler beim Laden der Bereitschaftsperson"
- ✅ Die Checkbox wird automatisch deaktiviert
- ✅ Das Eingabefeld bleibt editierbar
- ✅ Störungen können trotzdem normal erstellt werden

## Datenbank-Überprüfung

### Nach erfolgreicher Störungserstellung:

1. **Störungen-Tabelle prüfen**:
   ```sql
   SELECT id, titel, assigned_to, created_at 
   FROM stoerungen 
   ORDER BY created_at DESC 
   LIMIT 5;
   ```
   - ✅ Das Feld `assigned_to` sollte den Namen der Bereitschaftsperson enthalten

2. **Bereitschaftsdaten prüfen**:
   ```sql
   SELECT bp.name, bp.email, bw.wochen_start, bw.wochen_ende
   FROM bereitschafts_personen bp
   JOIN bereitschafts_wochen bw ON bp.id = bw.personen_id
   WHERE bw.ist_aktiv = true
   AND CURRENT_DATE BETWEEN bw.wochen_start AND bw.wochen_ende;
   ```
   - ✅ Sollte die aktuell zugewiesene Bereitschaftsperson anzeigen

## Troubleshooting

### Problem: Automatische Zuweisung funktioniert nicht

**Mögliche Ursachen:**
1. **Keine aktive Bereitschaftswoche**: Prüfe, ob eine Bereitschaftswoche für die aktuelle Woche definiert ist
2. **Bereitschaftsperson inaktiv**: Prüfe, ob die Bereitschaftsperson als aktiv markiert ist
3. **Datenbankverbindung**: Prüfe die Backend-Logs auf Datenbankfehler
4. **Service-Fehler**: Prüfe die Browser-Konsole auf JavaScript-Fehler

### Problem: E-Mail-Template wird nicht generiert

**Mögliche Ursachen:**
1. **Unvollständige Formulardaten**: Stelle sicher, dass alle Pflichtfelder ausgefüllt sind
2. **Service-Fehler**: Prüfe die Backend-Logs auf Fehler beim Template-Service
3. **Bereitschaftsperson-Daten fehlen**: Prüfe, ob E-Mail-Adresse der Bereitschaftsperson vorhanden ist

### Problem: UI-Komponenten werden nicht angezeigt

**Mögliche Ursachen:**
1. **Import-Fehler**: Prüfe die Browser-Konsole auf Import-Fehler
2. **TypeScript-Fehler**: Prüfe die Entwicklerkonsole auf TypeScript-Kompilierungsfehler
3. **CSS-Probleme**: Prüfe, ob alle Tailwind-Klassen korrekt geladen werden

## Logs und Debugging

### Frontend-Logs (Browser-Konsole):
```javascript
// Öffne die Entwicklertools (F12) und prüfe:
// 1. Console-Tab für JavaScript-Fehler
// 2. Network-Tab für API-Aufrufe
// 3. Application-Tab für Local Storage
```

### Backend-Logs (Terminal):
```bash
# Prüfe das Backend-Terminal auf:
# 1. API-Aufrufe zu /api/bereitschaft/aktuelle-bereitschaft
# 2. Datenbankfehler
# 3. Service-Initialisierung
```

## Erfolgskriterien

✅ **Alle Tests bestanden, wenn:**
1. Automatische Zuweisung funktioniert bei verfügbarer Bereitschaftsperson
2. E-Mail-Template wird korrekt generiert und angezeigt
3. Manuelle Deaktivierung/Aktivierung funktioniert
4. Fehlerbehandlung funktioniert bei nicht verfügbarer Bereitschaftsperson
5. Störungen können in allen Szenarien erfolgreich erstellt werden
6. Datenbankeinträge sind korrekt
7. Keine JavaScript-Fehler in der Browser-Konsole
8. Keine Backend-Fehler in den Logs

## Nächste Schritte

Nach erfolgreichem Test:
1. **Produktionstest**: Test in einer produktionsähnlichen Umgebung
2. **Performance-Test**: Test mit mehreren gleichzeitigen Benutzern
3. **Integration-Test**: Test mit echten Bereitschaftsdaten
4. **Benutzer-Schulung**: Schulung der Endbenutzer auf die neue Funktionalität