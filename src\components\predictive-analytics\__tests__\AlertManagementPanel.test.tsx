/**
 * Alert Management Panel Tests
 * 
 * Tests for the alert management interface with German localization.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { AlertManagementPanel } from '@/components/predictive-analytics/AlertManagementPanel';
import type { Alert } from '@/types/predictive-analytics';

const mockAlerts: Alert[] = [
  {
    id: 'alert-1',
    alert_config_id: 'config-1',
    kpi_id: 'service_level',
    severity: 'critical',
    title: 'Critical Service Level Alert',
    message: 'Service level has dropped below critical threshold',
    current_value: 75.0,
    threshold_value: 80.0,
    triggered_at: '2024-01-01T12:00:00Z',
    status: 'active'
  },
  {
    id: 'alert-2',
    alert_config_id: 'config-2',
    kpi_id: 'picking_efficiency',
    severity: 'high',
    title: 'High Picking Efficiency Alert',
    message: 'Picking efficiency below expected levels',
    current_value: 85.0,
    threshold_value: 90.0,
    triggered_at: '2024-01-01T11:30:00Z',
    status: 'acknowledged'
  },
  {
    id: 'alert-3',
    alert_config_id: 'config-3',
    kpi_id: 'inventory_turnover',
    severity: 'medium',
    title: 'Medium Inventory Alert',
    message: 'Inventory turnover rate is concerning',
    current_value: 2.5,
    threshold_value: 3.0,
    triggered_at: '2024-01-01T10:00:00Z',
    status: 'resolved'
  }
];

describe('AlertManagementPanel', () => {
  const mockOnAlertAction = vi.fn();

  beforeEach(() => {
    mockOnAlertAction.mockClear();
  });

  it('renders panel header with German localization', () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    expect(screen.getByText('Aktive Alarme (3)')).toBeInTheDocument();
  });

  it('displays loading state correctly', () => {
    render(
      <AlertManagementPanel
        alerts={[]}
        loading={true}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    // Should show skeleton loaders
    expect(screen.getAllByTestId(/skeleton/i)).toBeTruthy();
  });

  it('shows empty state when no alerts', () => {
    render(
      <AlertManagementPanel
        alerts={[]}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    expect(screen.getByText('Keine aktiven Alarme')).toBeInTheDocument();
  });

  it('displays alerts with correct severity badges', () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    expect(screen.getByText('Kritisch')).toBeInTheDocument();
    expect(screen.getByText('Hoch')).toBeInTheDocument();
    expect(screen.getByText('Mittel')).toBeInTheDocument();
  });

  it('displays alerts with correct status badges', () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    expect(screen.getByText('Aktiv')).toBeInTheDocument();
    expect(screen.getByText('Bestätigt')).toBeInTheDocument();
    expect(screen.getByText('Gelöst')).toBeInTheDocument();
  });

  it('filters alerts by severity', () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    const severityFilter = screen.getByDisplayValue('Alle');
    fireEvent.change(severityFilter, { target: { value: 'critical' } });
    
    // Should only show critical alerts
    expect(screen.getByText('Critical Service Level Alert')).toBeInTheDocument();
    expect(screen.queryByText('High Picking Efficiency Alert')).not.toBeInTheDocument();
  });

  it('sorts alerts by severity and timestamp', () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    const alertTitles = screen.getAllByText(/Alert/);
    // Critical alert should be first
    expect(alertTitles[0]).toHaveTextContent('Critical Service Level Alert');
  });

  it('opens alert details dialog', async () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    const viewButtons = screen.getAllByLabelText(/view/i);
    fireEvent.click(viewButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByText('Alarm Details')).toBeInTheDocument();
    });
  });

  it('displays alert details in dialog', async () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    const viewButtons = screen.getAllByLabelText(/view/i);
    fireEvent.click(viewButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByText('Critical Service Level Alert')).toBeInTheDocument();
      expect(screen.getByText('service_level')).toBeInTheDocument();
      expect(screen.getByText('75.00')).toBeInTheDocument();
    });
  });

  it('handles acknowledge action', async () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    const acknowledgeButtons = screen.getAllByText('Bestätigen');
    fireEvent.click(acknowledgeButtons[0]);
    
    expect(mockOnAlertAction).toHaveBeenCalledWith('alert-1', 'acknowledge');
  });

  it('handles resolve action from dialog', async () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    // Open dialog
    const viewButtons = screen.getAllByLabelText(/view/i);
    fireEvent.click(viewButtons[0]);
    
    await waitFor(() => {
      const resolveButton = screen.getByText('Lösen');
      fireEvent.click(resolveButton);
    });
    
    expect(mockOnAlertAction).toHaveBeenCalledWith('alert-1', 'resolve');
  });

  it('handles dismiss action from dialog', async () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    // Open dialog
    const viewButtons = screen.getAllByLabelText(/view/i);
    fireEvent.click(viewButtons[0]);
    
    await waitFor(() => {
      const dismissButton = screen.getByText('Verwerfen');
      fireEvent.click(dismissButton);
    });
    
    expect(mockOnAlertAction).toHaveBeenCalledWith('alert-1', 'dismiss');
  });

  it('formats timestamps correctly in German', () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    // Should display German formatted timestamps
    expect(screen.getByText(/01\.01\.2024/)).toBeInTheDocument();
  });

  it('shows action note textarea in dialog', async () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    // Open dialog for active alert
    const viewButtons = screen.getAllByLabelText(/view/i);
    fireEvent.click(viewButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Notiz zur Aktion hinzufügen...')).toBeInTheDocument();
    });
  });

  it('does not show action buttons for resolved alerts', async () => {
    const resolvedAlert: Alert = {
      ...mockAlerts[0],
      status: 'resolved'
    };
    
    render(
      <AlertManagementPanel
        alerts={[resolvedAlert]}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    // Open dialog
    const viewButtons = screen.getAllByLabelText(/view/i);
    fireEvent.click(viewButtons[0]);
    
    await waitFor(() => {
      expect(screen.queryByText('Bestätigen')).not.toBeInTheDocument();
      expect(screen.queryByText('Lösen')).not.toBeInTheDocument();
      expect(screen.queryByText('Verwerfen')).not.toBeInTheDocument();
    });
  });

  it('applies custom className', () => {
    const { container } = render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
        className="custom-class"
      />
    );
    
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('displays KPI information for each alert', () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    expect(screen.getByText('KPI: service_level')).toBeInTheDocument();
    expect(screen.getByText('KPI: picking_efficiency')).toBeInTheDocument();
    expect(screen.getByText('KPI: inventory_turnover')).toBeInTheDocument();
  });

  it('displays current values with proper formatting', () => {
    render(
      <AlertManagementPanel
        alerts={mockAlerts}
        loading={false}
        onAlertAction={mockOnAlertAction}
      />
    );
    
    expect(screen.getByText('Wert: 75.00')).toBeInTheDocument();
    expect(screen.getByText('Wert: 85.00')).toBeInTheDocument();
    expect(screen.getByText('Wert: 2.50')).toBeInTheDocument();
  });
});