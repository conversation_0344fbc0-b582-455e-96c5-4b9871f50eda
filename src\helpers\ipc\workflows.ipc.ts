/**
 * Ren<PERSON>er-Client für Workflow-IPC.
 * <PERSON><PERSON><PERSON><PERSON>, dass preload die API unter window.api.workflows bereitstellt.
 */

export type WorkflowId = 'bestand' | 'servicegrad' | 'rueckstandsliste' | 'lx03_240' | 'lx03_200' | 'lx03_rest';

declare global {
  interface Window {
    api?: {
      workflows?: {
        list: () => Promise<{ id: WorkflowId; name: string }[]>;
        start: (id: WorkflowId, params?: Record<string, string>) => Promise<{ ok: boolean; message?: string }>;
        status: (id: WorkflowId) => Promise<{ status: 'idle' | 'running' | 'success' | 'error'; startedAt?: number | null; finishedAt?: number | null; lastLogFilePath?: string | null }>;
        subscribeLogs: (id: WorkflowId, onLine: (line: string) => void) => () => void;
      };
    };
  }
}

export const workflowsClient = {
  async list() {
    if (!window.api?.workflows) throw new Error('workflows API nicht verfügbar');
    return window.api.workflows.list();
  },
  async start(id: WorkflowId, params?: Record<string, string>) {
    if (!window.api?.workflows) throw new Error('workflows API nicht verfügbar');
    return window.api.workflows.start(id, params);
  },
  async status(id: WorkflowId) {
    if (!window.api?.workflows) throw new Error('workflows API nicht verfügbar');
    return window.api.workflows.status(id);
  },
  subscribeLogs(id: WorkflowId, onLine: (line: string) => void) {
    if (!window.api?.workflows) throw new Error('workflows API nicht verfügbar');
    return window.api.workflows.subscribeLogs(id, onLine);
  },
};