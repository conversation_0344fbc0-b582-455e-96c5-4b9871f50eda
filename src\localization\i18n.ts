import i18n from "i18next";
import { initReactI18next } from "react-i18next";

i18n.use(initReactI18next).init({
  fallbackLng: "de", // Deutsch als Standardsprache
  resources: {
    de: {
      translation: {
        // App-Titel
        appName: "SHOPFLOOR MANAGEMENT",
        shopfloorManagement: "Logistic Center Ludwigsburg",
        
        // Seitentitel
        titleHomePage: "Startseite",
        titleSecondPage: "Zweite Seite",
        
        // Abteilungen
        dispatchArea: "Versand",
        cuttingArea: "Ablängerei",
        incomingGoodsArea: "Wareneingang",
        
        // Diagramm-Titel
        serviceLevel: "Servicelevel",
        serviceLevelDescription: "Entwicklung des Servicegrads über Zeit",
        csrPercentage: "CSR - Prozentsatz",
        servicegradPercentage: "Servicegrad",
        dailyPerformance: "Tägliche Leistung",
        dailyPerformanceDescription: "Leistungskennzahlen im Vergleich",
        producedTonnage: "Produzierte Tonnagen",
        directLoading: "Direktverladung KIAA",
        turnover: "Umschlag",
        kgPerColli: "Kg / Colli",
        elephants: "Elefanten",
        date: "Datum",
        picking: "Kommissionierung",
        pickingDescription: "Kommissionierleistung und Füllgrad",
        atrl: "ATRL",
        aril: "ARIL",
        fillLevelAril: "Füllgrad ARIL",
        difference: "Differenz",
        returns: "Retouren",
        deliveryPositions: "Lieferpositionen",
        deliveryPositionsDescription: "Ausgelieferte und rückständige Positionen",
        deliveredLup: "Ausgeliefert LUP",
        outstanding: "Rückständig",
        ratio: "Verhältnis",
        returnsDescription: "Qualitätsmanagement-Status",
        qmOpen: "QM offen",
        qmRejected: "QM abgelehnt",
        qmAccepted: "QM angenommen",
        trendingUp: "Trend: +{{value}}% diesen Monat",
        trendingDown: "Trend: -{{value}}% diesen Monat",
        totalReturns: "Gesamtzahl der Retouren",
        clickToEnlarge: "Klicken zum Vergrößern",
        cuttings: "Schnitte",
        incomingPositions: "Eingehende Positionen",
        
        // Allgemeine Begriffe
        value: "Wert",
        positions: "Positionen",
        accepted: "Akzeptiert",
        notAccepted: "Nicht akzeptiert",
        open: "Offen",
        
        // Diagramm-Footer
        total: "Gesamt",
        average: "Durchschnitt",
        updated: "Aktualisiert",
        min: "Minimum",
        max: "Maximum",
        machineComparison: "Maschinenvergleich",
        
        // Diagramm-Status
        loading: "Wird geladen",
        noData: "Keine Daten verfügbar",
      },
    },
    en: {
      translation: {
        // App Title
        appName: "SFM Dashboard",
        shopfloorManagement: "Shopfloor Management",
        
        // Page Titles
        titleHomePage: "Home Page",
        titleSecondPage: "Second Page",
        
        // Departments
        dispatchArea: "Dispatch Area",
        cuttingArea: "Cutting Area",
        incomingGoodsArea: "Incoming Goods Area",
        
        // Chart Titles
        serviceLevel: "Service Level",
        serviceLevelDescription: "Service level development over time",
        csrPercentage: "CSR - Percentage",
        servicegradPercentage: "Service Level",
        dailyPerformance: "Daily Performance",
        dailyPerformanceDescription: "Performance metrics comparison",
        producedTonnage: "Produced Tonnage",
        directLoading: "Direct Loading KIAA",
        turnover: "Turnover",
        kgPerColli: "Kg / Colli",
        elephants: "Elephants",
        date: "Date",
        picking: "Picking",
        pickingDescription: "Picking performance and fill level",
        atrl: "ATRL",
        aril: "ARIL",
        fillLevelAril: "Fill Level ARIL",
        difference: "Difference",
        returns: "Returns",
        deliveryPositions: "Delivery Positions",
        deliveryPositionsDescription: "Delivered and outstanding positions",
        deliveredLup: "Delivered LUP",
        outstanding: "Outstanding",
        ratio: "Ratio",
        returnsDescription: "Quality Management Status",
        qmOpen: "QM open",
        qmRejected: "QM rejected",
        qmAccepted: "QM accepted",
        trendingUp: "Trending up by {{value}}% this month",
        trendingDown: "Trending down by {{value}}% this month",
        totalReturns: "Total returns",
        clickToEnlarge: "Click to enlarge",
        cuttings: "Cuttings",
        incomingPositions: "Incoming Positions",
        
        // General Terms
        value: "Value",
        positions: "Positions",
        accepted: "Accepted",
        notAccepted: "Not Accepted",
        open: "Open",
        
        // Chart Footer
        total: "Total",
        average: "Average",
        updated: "Updated",
        min: "Minimum",
        max: "Maximum",
        machineComparison: "Machine Comparison",
        
        // Chart Status
        loading: "Loading",
        noData: "No data available",
      },
    },
    "pt-BR": {
      translation: {
        // Título do Aplicativo
        appName: "SFM Dashboard",
        shopfloorManagement: "Gerenciamento de Chão de Fábrica",
        
        // Títulos de Página
        titleHomePage: "Página Inicial",
        titleSecondPage: "Segunda Página",
        
        // Departamentos
        dispatchArea: "Área de Despacho",
        cuttingArea: "Área de Corte",
        incomingGoodsArea: "Área de Recebimento",
        
        // Títulos de Gráficos
        serviceLevel: "Nível de Serviço",
        serviceLevelDescription: "Desenvolvimento do nível de serviço ao longo do tempo",
        csrPercentage: "CSR - Porcentagem",
        servicegradPercentage: "Nível de Serviço",
        dailyPerformance: "Desempenho Diário",
        dailyPerformanceDescription: "Comparação de métricas de desempenho",
        producedTonnage: "Tonelagem Produzida",
        directLoading: "Carregamento Direto KIAA",
        turnover: "Volume de Negócios",
        kgPerColli: "Kg / Colli",
        elephants: "Elefantes",
        date: "Data",
        picking: "Separação",
        pickingDescription: "Desempenho de separação e nível de enchimento",
        atrl: "ATRL",
        aril: "ARIL",
        fillLevelAril: "Nível de Enchimento ARIL",
        difference: "Diferença",
        returns: "Devoluções",
        deliveryPositions: "Posições de Entrega",
        deliveryPositionsDescription: "Posições entregues e pendentes",
        deliveredLup: "Entregue LUP",
        outstanding: "Pendente",
        ratio: "Proporção",
        returnsDescription: "Status de Gerenciamento de Qualidade",
        qmOpen: "QM aberto",
        qmRejected: "QM rejeitado",
        qmAccepted: "QM aceito",
        trendingUp: "Tendência de alta de {{value}}% este mês",
        trendingDown: "Tendência de baixa de {{value}}% este mês",
        totalReturns: "Total de devoluções",
        clickToEnlarge: "Clique para ampliar",
        cuttings: "Cortes",
        incomingPositions: "Posições de Entrada",
        
        // Termos Gerais
        value: "Valor",
        positions: "Posições",
        accepted: "Aceito",
        notAccepted: "Não Aceito",
        open: "Aberto",
        
        // Rodapé do Gráfico
        total: "Total",
        average: "Média",
        updated: "Atualizado",
        min: "Mínimo",
        max: "Máximo",
        machineComparison: "Comparação de Máquinas",
        
        // Status do Gráfico
        loading: "Carregando",
        noData: "Nenhum dado disponível",
      },
    },
  },
});
