import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import "./styles/global.css";

/**
 * Renderer Entry-Point für Electron
 * 
 * Diese Datei ist der Haupt-Entry-Point für die Renderer-Seite der Electron-Anwendung.
 * Sie initialisiert React und rendert die Haupt-App-Komponente.
 */

// Initialisiere React Root
const container = document.getElementById("app");
if (!container) {
  throw new Error("Container-Element '#app' nicht gefunden!");
}

const root = createRoot(container);

// Rendere die App
root.render(<App />);

 