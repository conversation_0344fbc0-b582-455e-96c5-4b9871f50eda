/**
 * Base Repository Interface
 * 
 * Definiert das grundlegende Repository Pattern für typisierte Datenabstraktion.
 * Alle spezifischen Repositories implementieren diese Basis-Schnittstelle.
 */

import { cacheManager } from '@/services/api.service';

/**
 * Basis-Repository-Interface mit CRUD-Operationen
 */
export interface IRepository<T, TFilter = any> {
  /**
   * Alle Datensätze abrufen
   * @param filter - Optionale Filter-Parameter
   * @returns Promise mit Array von Datensätzen
   */
  getAll(filter?: TFilter): Promise<T[]>;
  
  /**
   * Einzelnen Datensatz nach ID abrufen
   * @param id - Eindeutige ID
   * @returns Promise mit Datensatz oder null
   */
  getById?(id: string | number): Promise<T | null>;
  
  /**
   * Datensätze mit Paginierung abrufen
   * @param offset - Start-Index
   * @param limit - Maximale Anzahl
   * @param filter - Optionale Filter-Parameter
   * @returns Promise mit paginierte Daten
   */
  getPaginated?(offset: number, limit: number, filter?: TFilter): Promise<{
    data: T[];
    total: number;
    hasMore: boolean;
  }>;
  
  /**
   * Cache für dieses Repository invalidieren
   */
  invalidateCache(): void;
}

/**
 * Basis-Repository-Klasse mit gemeinsamen Funktionen
 */
export abstract class BaseRepository<T, TFilter = any> implements IRepository<T, TFilter> {
  protected abstract repositoryName: string;
  
  /**
   * Abstrakte Methode für Datenabruf - muss von Subklassen implementiert werden
   */
  abstract getAll(filter?: TFilter): Promise<T[]>;
  
  /**
   * Cache für dieses Repository invalidieren
   */
  invalidateCache(): void {
    cacheManager.invalidate(this.repositoryName);
    console.log(`Cache invalidiert für Repository: ${this.repositoryName}`);
  }
  
  /**
   * Cache-Statistiken für dieses Repository abrufen
   */
  getCacheStats() {
    return cacheManager.getStats();
  }
  
  /**
   * Hilfsmethode für Datum-String Parsing
   */
  protected parseDate(dateString?: string): Date | null {
    if (!dateString) return null;
    
    // Verschiedene Datumsformate unterstützen
    const formats = [
      /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
      /^\d{2}\.\d{2}\.\d{4}$/, // DD.MM.YYYY
      /^\d{2}\.\d{2}\.\d{2}$/, // DD.MM.YY
    ];
    
    for (const format of formats) {
      if (format.test(dateString)) {
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? null : date;
      }
    }
    
    return null;
  }
  
  /**
   * Hilfsmethode für Datum-Range-Validierung
   */
  protected validateDateRange(startDate?: string, endDate?: string): {
    isValid: boolean;
    start?: Date;
    end?: Date;
    error?: string;
  } {
    const start = startDate ? this.parseDate(startDate) : undefined;
    const end = endDate ? this.parseDate(endDate) : undefined;
    
    if (startDate && !start) {
      return { isValid: false, error: `Ungültiges Startdatum: ${startDate}` };
    }
    
    if (endDate && !end) {
      return { isValid: false, error: `Ungültiges Enddatum: ${endDate}` };
    }
    
    if (start && end && start > end) {
      return { isValid: false, error: 'Startdatum muss vor Enddatum liegen' };
    }
    
    return { isValid: true, start, end };
  }
}

/**
 * Filter-Interface für Datum-basierte Repositories
 */
export interface DateRangeFilter {
  startDate?: string;
  endDate?: string;
}

/**
 * Filter-Interface für Paginierung
 */
export interface PaginationFilter {
  offset?: number;
  limit?: number;
}

/**
 * Kombiniertes Filter-Interface
 */
export interface BaseFilter extends DateRangeFilter, PaginationFilter {
  [key: string]: any;
}