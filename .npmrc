# PNPM Konfiguration für Electron App
# Verhindert das Hoisting von nativen Modulen
shamefully-hoist=false

# Bessere Unterstützung für native Module in Electron
node-linker=hoisted

# Wichtig für better-sqlite3 und andere native Module
public-hoist-pattern[]=*better-sqlite3*
public-hoist-pattern[]=*electron*
public-hoist-pattern[]=*@electron*

# Electron Rebuild für native Module
rebuild=true

# Reduziert Symlink-Probleme auf Windows
symlink=false

# Auto-Install peer dependencies
auto-install-peers=true

# Strenge Version Resolution
strict-peer-dependencies=false
