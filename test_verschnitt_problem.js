/**
 * Test-Skript zur Reproduktion des Verschnitt-Problems
 * 
 * Problem: Wenn Trommeln aus der Datenbank ausgewählt werden,
 * wird der Verschnitt falsch berechnet, weil die firstFitOptimization
 * Funktion nicht auf das richtige Feld zugreift.
 */

// Simuliere verschiedene Szenarien
const testCases = [
  {
    name: "Fall 1: availableLength ist gesetzt (funktioniert)",
    drum: {
      id: "TEST123",
      cableType: "H07V-K 1,5",
      totalLength: 500,
      availableLength: 500,
      remainingLength: undefined
    }
  },
  {
    name: "Fall 2: availableLength ist 0, aber totalLength ist gesetzt (Problem!)",
    drum: {
      id: "TEST456",
      cableType: "H07V-K 1,5",
      totalLength: 500,
      availableLength: 0, // Könnte durch vorherige Optimierungen auf 0 gesetzt worden sein
      remainingLength: undefined
    }
  },
  {
    name: "Fall 3: availableLength ist undefined, totalLength ist gesetzt (Problem!)",
    drum: {
      id: "TEST789",
      cableType: "H07V-K 1,5",
      totalLength: 500,
      availableLength: undefined, // Nicht gesetzt
      remainingLength: 300 // Alter Wert
    }
  },
  {
    name: "Fall 4: Nur remainingLength ist gesetzt (Edge Case)",
    drum: {
      id: "TEST999",
      cableType: "H07V-K 1,5",
      totalLength: undefined,
      availableLength: undefined,
      remainingLength: 200
    }
  }
];

// Simuliere die Lieferungsanfrage
const deliveryItems = [
  {
    id: "item1",
    cableType: "H07V-K 1,5",
    requiredLength: 200, // 200m benötigt
    quantity: 2, // 2 Schnitte
    priority: "medium"
  }
];

const totalRequired = deliveryItems[0].requiredLength * deliveryItems[0].quantity; // 400m

// Simuliere das Problem in der firstFitOptimization
function simulateCurrentBuggyLogic(drum) {
  // Aktuelle (fehlerhafte) Logik aus der firstFitOptimization
  const remainingLength = drum.availableLength || drum.remainingLength || 0;
  return remainingLength;
}

function simulateFixedLogic(drum) {
  // Korrigierte Logik mit totalLength als Fallback
  const remainingLength = drum.availableLength || drum.totalLength || drum.remainingLength || 0;
  return remainingLength;
}

console.log("=== VERSCHNITT-PROBLEM REPRODUKTION ===");
console.log(`Lieferungsanfrage: ${deliveryItems[0].requiredLength}m × ${deliveryItems[0].quantity} = ${totalRequired}m benötigt`);
console.log("");

testCases.forEach((testCase, index) => {
  console.log(`=== ${testCase.name} ===`);
  console.log("Trommel-Daten:");
  console.log(`  ID: ${testCase.drum.id}`);
  console.log(`  totalLength: ${testCase.drum.totalLength}`);
  console.log(`  availableLength: ${testCase.drum.availableLength}`);
  console.log(`  remainingLength: ${testCase.drum.remainingLength}`);
  
  const currentResult = simulateCurrentBuggyLogic(testCase.drum);
  const fixedResult = simulateFixedLogic(testCase.drum);
  
  console.log("");
  console.log("Ergebnisse:");
  console.log(`  Aktuelle Logik: ${currentResult}m`);
  console.log(`  Korrigierte Logik: ${fixedResult}m`);
  
  if (currentResult === fixedResult) {
    console.log(`  ✅ Kein Problem - beide Logiken geben ${currentResult}m`);
  } else {
    console.log(`  ❌ PROBLEM GEFUNDEN!`);
    console.log(`     Aktuell: ${currentResult}m (falsch)`);
    console.log(`     Korrekt: ${fixedResult}m`);
    console.log(`     Unterschied: ${fixedResult - currentResult}m`);
    
    // Berechne Verschnitt für beide Fälle
    if (currentResult >= totalRequired && fixedResult >= totalRequired) {
      const currentWaste = currentResult - totalRequired;
      const correctWaste = fixedResult - totalRequired;
      console.log(`     Verschnitt aktuell: ${currentWaste}m`);
      console.log(`     Verschnitt korrekt: ${correctWaste}m`);
      console.log(`     Verschnitt-Differenz: ${correctWaste - currentWaste}m`);
    }
  }
  
  console.log("");
});

console.log("=== LÖSUNG ===");
console.log("In CuttingOptimizerService.ts, Zeile ~530:");
console.log("ÄNDERN VON:");
console.log("  remainingLength: drum.availableLength || drum.remainingLength || 0,");
console.log("ZU:");
console.log("  remainingLength: drum.availableLength || drum.totalLength || drum.remainingLength || 0,");
console.log("");
console.log("Dies stellt sicher, dass totalLength als Fallback verwendet wird,");
console.log("wenn availableLength nicht gesetzt oder 0 ist.");