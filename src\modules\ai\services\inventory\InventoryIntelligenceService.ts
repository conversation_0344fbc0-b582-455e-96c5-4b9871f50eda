/**
 * Inventory Intelligence Service
 * 
 * Main service for AI-powered inventory management with ABC analysis,
 * demand forecasting, and intelligent reorder recommendations
 */

import { AIBaseService, AIServiceConfig, AIServiceStatus } from '../base/AIBaseService';
import { AIServiceError } from '../types';
import { WarehouseRepository } from '@/repositories/warehouse.repository';
import { ABCAnalyzer } from './algorithms/ABCAnalyzer';
import { DemandForecaster } from './algorithms/DemandForecaster';
import { StatisticalForecaster } from './algorithms/StatisticalForecaster';
import {
  PredictiveAnalyzer,
  SeasonalAnalysisResult,
  ReorderOptimizationResult,
  AnomalyDetectionResult,
  TrendAnalysisResult
} from './algorithms/PredictiveAnalyzer';
import {
  InventoryItem,
  ConsumptionData,
  ABCClassification,
  ABCCriteria,
  DemandForecast,
  ReorderRecommendation,
  PurchaseRecommendation,
  StockAnomaly,
  ConsumptionAnalysis,
  InventoryIntelligenceConfig,
  InventoryIntelligenceStatus,
  SeasonalityPattern,
  StatisticalSummary
} from './types';

export interface InventoryIntelligenceServiceConfig extends AIServiceConfig {
  inventoryConfig?: InventoryIntelligenceConfig;
}

export class InventoryIntelligenceService extends AIBaseService {
  readonly serviceName = 'InventoryIntelligenceService';

  private warehouseRepository: WarehouseRepository;
  private inventoryConfig: InventoryIntelligenceConfig;
  private lastAnalysisRun: Date | null = null;
  private cachedClassifications: Map<string, ABCClassification> = new Map();
  private cachedForecasts: Map<string, DemandForecast> = new Map();

  constructor(config: InventoryIntelligenceServiceConfig = {}) {
    super(config);
    this.warehouseRepository = new WarehouseRepository();

    this.inventoryConfig = {
      abcAnalysis: {
        enabled: true,
        updateFrequency: 24, // Hours
        classAThreshold: 80, // 80% of value
        classBThreshold: 95, // 95% of value
        analysisWindow: 90 // 90 days
      },
      demandForecasting: {
        enabled: true,
        forecastHorizon: 30, // 30 days
        updateFrequency: 12, // Hours
        minHistoryDays: 14,
        models: ['simple_moving_average', 'exponential_smoothing', 'linear_regression']
      },
      anomalyDetection: {
        enabled: true,
        sensitivity: 0.7,
        checkFrequency: 6, // Hours
        alertThreshold: 0.8
      },
      reorderOptimization: {
        enabled: true,
        safetyStockDays: 7,
        leadTimeDays: 14,
        serviceLevel: 0.95
      },
      ...config.inventoryConfig
    };
  }

  /**
   * Initialize the inventory intelligence service
   */
  async initialize(config?: InventoryIntelligenceServiceConfig): Promise<void> {
    await super.initialize(config);

    if (config?.inventoryConfig) {
      this.inventoryConfig = { ...this.inventoryConfig, ...config.inventoryConfig };
    }

    this.log('Inventory Intelligence Service initialized');
  }

  /**
   * Perform ABC analysis on inventory items
   */
  async performABCAnalysis(items?: InventoryItem[]): Promise<ABCClassification> {
    return this.handleAIError(
      async () => {
        this.log('Starting ABC analysis');

        // Get inventory items if not provided
        const inventoryItems = items || await this.getInventoryItems();

        if (inventoryItems.length === 0) {
          throw new Error('No inventory items available for ABC analysis');
        }

        // Get consumption data
        const consumptionData = await this.getConsumptionData();

        // Set up analysis criteria
        const criteria: ABCCriteria = {
          method: 'combined',
          classAThreshold: this.inventoryConfig.abcAnalysis.classAThreshold,
          classBThreshold: this.inventoryConfig.abcAnalysis.classBThreshold,
          timeWindow: this.inventoryConfig.abcAnalysis.analysisWindow
        };

        // Perform analysis
        const classification = ABCAnalyzer.performAnalysis(inventoryItems, consumptionData, criteria);

        // Cache results
        this.cachedClassifications.set('current', classification);
        this.lastAnalysisRun = new Date();

        this.log(`ABC analysis completed: A=${classification.classA.length}, B=${classification.classB.length}, C=${classification.classC.length}`);

        return classification;
      },
      async () => {
        // Fallback: return cached classification or empty classification
        const cached = this.cachedClassifications.get('current');
        if (cached) {
          this.log('Returning cached ABC classification due to error');
          return cached;
        }

        return {
          classA: [],
          classB: [],
          classC: [],
          reclassifications: [],
          analysisDate: new Date(),
          criteria: {
            method: 'combined',
            classAThreshold: 80,
            classBThreshold: 95,
            timeWindow: 90
          }
        };
      },
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Update item classifications based on new data
   */
  async updateClassifications(): Promise<{ updated: number; reclassifications: number }> {
    return this.handleAIError(
      async () => {
        this.log('Updating item classifications');

        const previousClassification = this.cachedClassifications.get('current');
        const newClassification = await this.performABCAnalysis();

        let reclassifications = 0;
        if (previousClassification) {
          const changes = ABCAnalyzer.identifyReclassifications(newClassification, previousClassification);
          newClassification.reclassifications = changes;
          reclassifications = changes.length;

          this.log(`Found ${reclassifications} reclassifications`);
        }

        const totalUpdated = newClassification.classA.length + newClassification.classB.length + newClassification.classC.length;

        return { updated: totalUpdated, reclassifications };
      },
      async () => ({ updated: 0, reclassifications: 0 }),
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Generate demand forecast for a specific item
   */
  async forecastDemand(itemId: string, horizon?: number): Promise<DemandForecast> {
    return this.handleAIError(
      async () => {
        const forecastHorizon = horizon || this.inventoryConfig.demandForecasting.forecastHorizon;
        this.log(`Generating demand forecast for item ${itemId}, horizon: ${forecastHorizon} days`);

        // Get historical consumption data for the item
        const consumptionData = await this.getItemConsumptionData(itemId);

        if (consumptionData.length < this.inventoryConfig.demandForecasting.minHistoryDays) {
          throw new Error(`Insufficient historical data for item ${itemId} (need at least ${this.inventoryConfig.demandForecasting.minHistoryDays} days)`);
        }

        // Generate forecast using advanced forecasting
        const forecast = await DemandForecaster.generateForecast(itemId, consumptionData, forecastHorizon);

        // Cache the forecast
        this.cachedForecasts.set(itemId, forecast);

        this.log(`Demand forecast generated for item ${itemId}: ${forecast.predictions.length} predictions, confidence: ${forecast.confidence.toFixed(2)}`);

        return forecast;
      },
      async () => {
        // Fallback: return cached forecast or simple forecast
        const cached = this.cachedForecasts.get(itemId);
        if (cached) {
          this.log(`Returning cached forecast for item ${itemId} due to error`);
          return cached;
        }

        // Simple fallback forecast
        return {
          itemId,
          predictions: [],
          confidence: 0,
          seasonalFactors: [],
          trendDirection: 'stable',
          forecastMethod: 'fallback',
          accuracy: 100,
          generatedAt: new Date()
        };
      },
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Detect seasonality patterns in item demand
   */
  async detectSeasonality(itemId: string): Promise<SeasonalityPattern> {
    return this.handleAIError(
      async () => {
        this.log(`Detecting seasonality patterns for item ${itemId}`);

        // Use the advanced seasonality detection
        const advancedAnalysis = await this.detectAdvancedSeasonality(itemId);

        this.log(`Seasonality detection completed for item ${itemId}: ${advancedAnalysis.seasonalityPattern.hasSeasonality ? 'seasonal' : 'non-seasonal'}`);

        return advancedAnalysis.seasonalityPattern;
      },
      async () => ({
        itemId,
        hasSeasonality: false,
        patterns: [],
        strength: 0,
        detectedAt: new Date()
      }),
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Calculate optimal reorder point for an item
   */
  async calculateOptimalReorderPoint(itemId: string): Promise<ReorderRecommendation> {
    return this.handleAIError(
      async () => {
        this.log(`Calculating optimal reorder point for item ${itemId}`);

        // Get current stock and consumption data
        const currentStock = await this.getCurrentStock(itemId);
        const consumptionData = await this.getItemConsumptionData(itemId);

        if (consumptionData.length === 0) {
          throw new Error(`No consumption data available for item ${itemId}`);
        }

        // Calculate average daily consumption
        const totalDays = Math.max(1, consumptionData.length);
        const totalConsumption = consumptionData.reduce((sum, d) => sum + d.quantity, 0);
        const avgDailyConsumption = totalConsumption / totalDays;

        // Calculate consumption variability (standard deviation)
        const avgConsumption = totalConsumption / consumptionData.length;
        const variance = consumptionData.reduce((sum, d) => sum + Math.pow(d.quantity - avgConsumption, 2), 0) / consumptionData.length;
        const stdDevConsumption = Math.sqrt(variance);

        // Calculate safety stock based on service level
        const { safetyStockDays, leadTimeDays, serviceLevel } = this.inventoryConfig.reorderOptimization;
        const zScore = this.getZScoreForServiceLevel(serviceLevel);
        const safetyStock = zScore * stdDevConsumption * Math.sqrt(leadTimeDays);

        // Calculate reorder point
        const reorderPoint = (avgDailyConsumption * leadTimeDays) + safetyStock;

        // Calculate recommended order quantity (simple EOQ approximation)
        const recommendedOrderQuantity = Math.max(
          avgDailyConsumption * 30, // At least 30 days supply
          reorderPoint * 1.5 // Or 1.5x reorder point
        );

        // Determine urgency
        const stockoutRisk = currentStock / (avgDailyConsumption * leadTimeDays);
        let urgency: 'low' | 'medium' | 'high' | 'critical';

        if (currentStock <= reorderPoint * 0.5) urgency = 'critical';
        else if (currentStock <= reorderPoint) urgency = 'high';
        else if (currentStock <= reorderPoint * 1.5) urgency = 'medium';
        else urgency = 'low';

        // Estimate stockout date
        let estimatedStockoutDate: Date | undefined;
        if (avgDailyConsumption > 0) {
          const daysUntilStockout = currentStock / avgDailyConsumption;
          estimatedStockoutDate = new Date();
          estimatedStockoutDate.setDate(estimatedStockoutDate.getDate() + daysUntilStockout);
        }

        const recommendation: ReorderRecommendation = {
          itemId,
          currentStock,
          reorderPoint: Math.round(reorderPoint),
          recommendedOrderQuantity: Math.round(recommendedOrderQuantity),
          urgency,
          reasoning: `Based on ${totalDays} days of consumption data. Average daily consumption: ${avgDailyConsumption.toFixed(2)}, Lead time: ${leadTimeDays} days, Service level: ${(serviceLevel * 100).toFixed(0)}%`,
          confidence: Math.min(1, totalDays / 30), // Higher confidence with more data
          estimatedStockoutDate,
          leadTime: leadTimeDays,
          safetyStock: Math.round(safetyStock)
        };

        this.log(`Reorder recommendation calculated for item ${itemId}: reorder at ${recommendation.reorderPoint}, order ${recommendation.recommendedOrderQuantity}, urgency: ${urgency}`);

        return recommendation;
      },
      async () => ({
        itemId,
        currentStock: 0,
        reorderPoint: 0,
        recommendedOrderQuantity: 0,
        urgency: 'low' as const,
        reasoning: 'Fallback recommendation due to calculation error',
        confidence: 0,
        leadTime: this.inventoryConfig.reorderOptimization.leadTimeDays,
        safetyStock: 0
      }),
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Generate purchase recommendations for all items
   */
  async generatePurchaseRecommendations(): Promise<PurchaseRecommendation[]> {
    return this.handleAIError(
      async () => {
        this.log('Generating purchase recommendations');

        const inventoryItems = await this.getInventoryItems();
        const recommendations: PurchaseRecommendation[] = [];

        for (const item of inventoryItems) {
          try {
            const reorderRec = await this.calculateOptimalReorderPoint(item.id);

            if (reorderRec.urgency === 'high' || reorderRec.urgency === 'critical') {
              recommendations.push({
                itemId: item.id,
                recommendedQuantity: reorderRec.recommendedOrderQuantity,
                estimatedCost: reorderRec.recommendedOrderQuantity * item.unitPrice,
                supplier: item.supplier,
                priority: reorderRec.urgency === 'critical' ? 1 : 2,
                reasoning: reorderRec.reasoning,
                expectedDelivery: this.calculateExpectedDelivery(reorderRec.leadTime)
              });
            }
          } catch (error) {
            this.log(`Error generating recommendation for item ${item.id}:`, error);
          }
        }

        // Sort by priority and urgency
        recommendations.sort((a, b) => a.priority - b.priority);

        this.log(`Generated ${recommendations.length} purchase recommendations`);

        return recommendations;
      },
      async () => [],
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Detect stock anomalies using advanced statistical methods
   */
  async detectStockAnomalies(): Promise<StockAnomaly[]> {
    return this.handleAIError(
      async () => {
        this.log('Detecting stock anomalies using advanced methods');

        const anomalyResult = await this.detectAdvancedStockAnomalies({
          sensitivityLevel: this.inventoryConfig.anomalyDetection.sensitivity > 0.8 ? 'high' :
            this.inventoryConfig.anomalyDetection.sensitivity > 0.5 ? 'medium' : 'low',
          lookbackDays: 30,
          minDataPoints: 7
        });

        this.log(`Detected ${anomalyResult.anomalies.length} stock anomalies using advanced methods`);

        return anomalyResult.anomalies;
      },
      async () => [],
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Analyze consumption patterns for an item using advanced predictive analytics
   */
  async analyzeConsumptionPatterns(itemId: string): Promise<ConsumptionAnalysis> {
    return this.handleAIError(
      async () => {
        this.log(`Analyzing consumption patterns for item ${itemId} using predictive analytics`);

        const consumptionData = await this.getItemConsumptionData(itemId);

        if (consumptionData.length === 0) {
          throw new Error(`No consumption data available for item ${itemId}`);
        }

        // Use advanced predictive analyzer
        const analysis = PredictiveAnalyzer.analyzeConsumptionPatterns(itemId, consumptionData, {
          trendWindow: 14,
          outlierThreshold: 2.5,
          patternMinLength: 7
        });

        this.log(`Advanced consumption analysis completed for item ${itemId}: trend=${analysis.consumptionTrend}, volatility=${analysis.volatility.toFixed(2)}, patterns=${analysis.patterns.length}`);

        return analysis;
      },
      async () => ({
        itemId,
        averageDailyConsumption: 0,
        consumptionTrend: 'stable' as const,
        volatility: 0,
        patterns: [],
        outliers: [],
        analysisDate: new Date()
      }),
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Detect seasonal patterns with advanced analysis
   */
  async detectAdvancedSeasonality(itemId: string): Promise<SeasonalAnalysisResult> {
    return this.handleAIError(
      async () => {
        this.log(`Detecting advanced seasonal patterns for item ${itemId}`);

        const consumptionData = await this.getItemConsumptionData(itemId);

        if (consumptionData.length < 14) {
          return {
            itemId,
            seasonalityPattern: {
              itemId,
              hasSeasonality: false,
              patterns: [],
              strength: 0,
              detectedAt: new Date()
            },
            seasonalStrength: 0,
            dominantCycle: 0,
            seasonalIndices: [],
            confidence: 0
          };
        }

        const seasonalAnalysis = PredictiveAnalyzer.detectSeasonalPatterns(itemId, consumptionData, {
          minCycleLength: 7,
          maxCycleLength: Math.min(365, Math.floor(consumptionData.length / 2)),
          significanceThreshold: 0.1
        });

        this.log(`Advanced seasonality detection completed for item ${itemId}: ${seasonalAnalysis.seasonalityPattern.hasSeasonality ? 'seasonal' : 'non-seasonal'}, strength: ${seasonalAnalysis.seasonalStrength.toFixed(2)}`);

        return seasonalAnalysis;
      },
      async () => ({
        itemId,
        seasonalityPattern: {
          itemId,
          hasSeasonality: false,
          patterns: [],
          strength: 0,
          detectedAt: new Date()
        },
        seasonalStrength: 0,
        dominantCycle: 0,
        seasonalIndices: [],
        confidence: 0
      }),
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Optimize reorder point using advanced algorithms
   */
  async optimizeReorderPoint(itemId: string, options?: {
    targetServiceLevel?: number;
    leadTimeDays?: number;
    safetyStockMultiplier?: number;
    considerSeasonality?: boolean;
  }): Promise<ReorderOptimizationResult> {
    return this.handleAIError(
      async () => {
        this.log(`Optimizing reorder point for item ${itemId}`);

        const currentStock = await this.getCurrentStock(itemId);
        const consumptionData = await this.getItemConsumptionData(itemId);

        if (consumptionData.length < 7) {
          throw new Error(`Insufficient data for reorder point optimization for item ${itemId}`);
        }

        const optimization = PredictiveAnalyzer.optimizeReorderPoint(
          itemId,
          currentStock,
          consumptionData,
          {
            targetServiceLevel: options?.targetServiceLevel || this.inventoryConfig.reorderOptimization.serviceLevel,
            leadTimeDays: options?.leadTimeDays || this.inventoryConfig.reorderOptimization.leadTimeDays,
            safetyStockMultiplier: options?.safetyStockMultiplier || 1.0,
            considerSeasonality: options?.considerSeasonality ?? true
          }
        );

        this.log(`Reorder point optimization completed for item ${itemId}: current=${optimization.currentReorderPoint}, optimized=${optimization.optimizedReorderPoint}, improvement=${optimization.improvement.toFixed(1)}%`);

        return optimization;
      },
      async () => ({
        itemId,
        currentReorderPoint: 0,
        optimizedReorderPoint: 0,
        improvement: 0,
        reasoning: ['Fallback optimization due to error'],
        riskAssessment: {
          stockoutProbability: 0,
          overstockRisk: 0,
          serviceLevel: 0
        }
      }),
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Detect stock anomalies using advanced statistical methods
   */
  async detectAdvancedStockAnomalies(options?: {
    sensitivityLevel?: 'low' | 'medium' | 'high';
    lookbackDays?: number;
    minDataPoints?: number;
  }): Promise<AnomalyDetectionResult> {
    return this.handleAIError(
      async () => {
        this.log('Detecting advanced stock anomalies');

        const inventoryItems = await this.getInventoryItems();
        const consumptionDataMap = new Map<string, ConsumptionData[]>();

        // Collect consumption data for all items
        for (const item of inventoryItems) {
          try {
            const consumptionData = await this.getItemConsumptionData(item.id);
            consumptionDataMap.set(item.id, consumptionData);
          } catch (error) {
            this.log(`Error getting consumption data for item ${item.id}:`, error);
          }
        }

        const anomalyResult = PredictiveAnalyzer.detectStockAnomalies(
          inventoryItems,
          consumptionDataMap,
          {
            sensitivityLevel: options?.sensitivityLevel || 'medium',
            lookbackDays: options?.lookbackDays || 30,
            minDataPoints: options?.minDataPoints || 7
          }
        );

        this.log(`Advanced anomaly detection completed: ${anomalyResult.anomalies.length} anomalies found across ${anomalyResult.totalItemsAnalyzed} items`);

        return anomalyResult;
      },
      async () => ({
        anomalies: [],
        totalItemsAnalyzed: 0,
        anomalyRate: 0,
        severityDistribution: {}
      }),
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Analyze consumption trends for an item
   */
  async analyzeTrends(itemId: string, options?: {
    trendWindow?: number;
  }): Promise<TrendAnalysisResult> {
    return this.handleAIError(
      async () => {
        this.log(`Analyzing trends for item ${itemId}`);

        const consumptionData = await this.getItemConsumptionData(itemId);

        if (consumptionData.length < 14) {
          return {
            itemId,
            trendDirection: 'stable',
            trendStrength: 0,
            changeRate: 0,
            trendConfidence: 0,
            projectedChange: 0
          };
        }

        // Use the trend analysis from PredictiveAnalyzer
        const trendAnalysis = (PredictiveAnalyzer as any).analyzeTrend(
          consumptionData,
          options?.trendWindow || 14
        );

        trendAnalysis.itemId = itemId;

        this.log(`Trend analysis completed for item ${itemId}: ${trendAnalysis.trendDirection}, strength: ${trendAnalysis.trendStrength.toFixed(2)}, confidence: ${trendAnalysis.trendConfidence.toFixed(2)}`);

        return trendAnalysis;
      },
      async () => ({
        itemId,
        trendDirection: 'stable' as const,
        trendStrength: 0,
        changeRate: 0,
        trendConfidence: 0,
        projectedChange: 0
      }),
      AIServiceError.PREDICTION_FAILED
    );
  }

  /**
   * Get service health status
   */
  async healthCheck(): Promise<InventoryIntelligenceStatus> {
    const baseStatus = await super.healthCheck();

    const totalItems = (await this.getInventoryItems()).length;
    const classificationsUpToDate = this.lastAnalysisRun ?
      (Date.now() - this.lastAnalysisRun.getTime()) < (this.inventoryConfig.abcAnalysis.updateFrequency * 60 * 60 * 1000) : false;

    const forecastsUpToDate = this.cachedForecasts.size > 0;

    return {
      // Base ServiceStatus properties
      isInitialized: baseStatus.isInitialized,
      isHealthy: baseStatus.isHealthy,
      lastError: baseStatus.lastError,
      lastChecked: baseStatus.lastChecked,

      // AIServiceStatus properties
      performance: {
        ...baseStatus.performance!,
        analysisTime: this.getPerformanceMetrics().averageResponseTime,
        forecastAccuracy: 85, // Would be calculated from forecast accuracy
        anomalyDetectionRate: 0.95
      },
      vectorStats: baseStatus.vectorStats,

      // InventoryIntelligenceStatus specific properties
      lastAnalysisRun: this.lastAnalysisRun || new Date(0),
      totalItemsAnalyzed: totalItems,
      classificationsUpToDate,
      forecastsUpToDate,
      anomaliesDetected: 0, // Would be calculated from recent anomaly detection
      reorderRecommendations: 0, // Would be calculated from recent recommendations
      errors: baseStatus.lastError ? [baseStatus.lastError.message] : []
    };
  }

  // Private helper methods

  private async getInventoryItems(): Promise<InventoryItem[]> {
    try {
      // TODO: Implement real inventory items query from repository
      // For now, return empty array until inventory items table is implemented
      const warehouseStats = await this.warehouseRepository.getOverallStats();
      
      // Return empty array until proper inventory items implementation
      return [];
    } catch (error) {
      this.log('Failed to fetch inventory items:', error);
      return [];
    }
  }

  private async getConsumptionData(): Promise<ConsumptionData[]> {
    try {
      // TODO: Implement real consumption data query from repository
      // For now, return empty array until consumption tracking is implemented
      return [];
    } catch (error) {
      this.log('Failed to fetch consumption data:', error);
      return [];
    }
  }

  private async getItemConsumptionData(itemId: string): Promise<ConsumptionData[]> {
    const allData = await this.getConsumptionData();
    return allData.filter(d => d.itemId === itemId);
  }

  private async getCurrentStock(itemId: string): Promise<number> {
    const items = await this.getInventoryItems();
    const item = items.find(i => i.id === itemId);
    return item?.currentStock || 0;
  }

  private getZScoreForServiceLevel(serviceLevel: number): number {
    // Approximate z-scores for common service levels
    if (serviceLevel >= 0.99) return 2.33;
    if (serviceLevel >= 0.95) return 1.65;
    if (serviceLevel >= 0.90) return 1.28;
    if (serviceLevel >= 0.85) return 1.04;
    return 0.84; // ~80% service level
  }

  private calculateExpectedDelivery(leadTimeDays: number): Date {
    const deliveryDate = new Date();
    deliveryDate.setDate(deliveryDate.getDate() + leadTimeDays);
    return deliveryDate;
  }

  private getAnomalyDescription(
    type: StockAnomaly['anomalyType'],
    current: number,
    expected: number
  ): string {
    switch (type) {
      case 'sudden_spike':
        return `Consumption spiked to ${current.toFixed(1)} from expected ${expected.toFixed(1)}`;
      case 'sudden_drop':
        return `Consumption dropped to ${current.toFixed(1)} from expected ${expected.toFixed(1)}`;
      case 'stockout_risk':
        return 'Item is at risk of stockout based on current consumption patterns';
      case 'overstock':
        return 'Item appears to be overstocked based on consumption patterns';
      default:
        return `Unusual consumption pattern detected: ${current.toFixed(1)} vs expected ${expected.toFixed(1)}`;
    }
  }

  private getAnomalyRecommendations(type: StockAnomaly['anomalyType']): string[] {
    switch (type) {
      case 'sudden_spike':
        return ['Investigate cause of increased demand', 'Consider increasing safety stock', 'Review supplier capacity'];
      case 'sudden_drop':
        return ['Investigate cause of decreased demand', 'Consider reducing orders', 'Review market conditions'];
      case 'stockout_risk':
        return ['Place urgent order', 'Expedite delivery if possible', 'Consider alternative suppliers'];
      case 'overstock':
        return ['Reduce future orders', 'Consider promotional activities', 'Review demand forecasts'];
      default:
        return ['Monitor closely', 'Review consumption patterns', 'Update forecasting models'];
    }
  }

  private identifyConsumptionPatterns(
    data: ConsumptionData[],
    volatility: number
  ): Array<{ type: 'regular' | 'irregular' | 'seasonal' | 'trending'; description: string; strength: number }> {
    const patterns = [];

    if (volatility < 0.2) {
      patterns.push({
        type: 'regular' as const,
        description: 'Consistent consumption pattern with low variability',
        strength: 1 - volatility
      });
    } else if (volatility > 0.8) {
      patterns.push({
        type: 'irregular' as const,
        description: 'Highly variable consumption pattern',
        strength: volatility
      });
    }

    // Simple trend detection
    const quantities = data.map(d => d.quantity);
    const firstQuarter = quantities.slice(0, Math.floor(quantities.length / 4));
    const lastQuarter = quantities.slice(-Math.floor(quantities.length / 4));

    const firstAvg = firstQuarter.reduce((sum, q) => sum + q, 0) / firstQuarter.length;
    const lastAvg = lastQuarter.reduce((sum, q) => sum + q, 0) / lastQuarter.length;

    const trendStrength = Math.abs(lastAvg - firstAvg) / firstAvg;

    if (trendStrength > 0.2) {
      patterns.push({
        type: 'trending' as const,
        description: lastAvg > firstAvg ? 'Increasing consumption trend' : 'Decreasing consumption trend',
        strength: trendStrength
      });
    }

    return patterns;
  }

  private identifyOutliers(
    data: ConsumptionData[],
    mean: number,
    stdDev: number
  ): Array<{ date: Date; value: number; expectedValue: number; deviation: number; possibleCause?: string }> {
    const outliers = [];
    const threshold = 2; // 2 standard deviations

    for (const item of data) {
      const zScore = Math.abs(item.quantity - mean) / stdDev;
      if (zScore > threshold) {
        outliers.push({
          date: item.date,
          value: item.quantity,
          expectedValue: mean,
          deviation: zScore,
          possibleCause: item.quantity > mean ? 'Unusually high demand' : 'Unusually low demand'
        });
      }
    }

    return outliers;
  }
}