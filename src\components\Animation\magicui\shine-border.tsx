"use client";

import * as React from "react";

import { cn } from "@/lib/utils";

interface ShineBorderProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Width of the border in pixels
   * @default 1
   */
  borderWidth?: number;
  /**
   * Duration of the animation in seconds
   * @default 8
   */
  duration?: number;
  /**
   * Color of the border, can be a single color or an array of colors
   * @default "#000000"
   */
  shineColor?: string | string[];
}

/**
 * Shine Border
 *
 * An animated background border effect component with configurable properties.
 */
export function ShineBorder({
  borderWidth = 3,
  duration = 8,
  shineColor = "#000000",
  className,
  style,
  ...props
}: ShineBorderProps) {
  // Robust gegen HMR/Electron: nur optionale Mask-Properties setzen, wenn unterstützt
  const supportsMaskComposite =
    typeof window !== "undefined" &&
    typeof CSS !== "undefined" &&
    // @ts-ignore
    (CSS.supports?.("mask-composite", "exclude") || CSS.supports?.("-webkit-mask-composite", "xor"));

  const computed: React.CSSProperties = {
    ["--border-width" as any]: `${borderWidth}px`,
    ["--duration" as any]: `${duration}s`,
    backgroundImage: `radial-gradient(transparent,transparent, ${
      Array.isArray(shineColor) ? shineColor.join(",") : shineColor
    },transparent,transparent)`,
    backgroundSize: "300% 300%",
    // Basis-Maskierung (funktioniert unabhängig von mask-composite Support)
    mask: `linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)`,
    WebkitMask: `linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)`,
    padding: "var(--border-width)",
    ...style,
  };

  if (supportsMaskComposite) {
    // Nur zuweisen, wenn Engine es kann – verhindert 500/HMR-Fehler
    (computed as any).maskComposite = "exclude";
    (computed as any).WebkitMaskComposite = "xor";
  }

  return (
    <div
      style={computed}
      className={cn(
        "pointer-events-none absolute inset-0 size-full rounded-[inherit] will-change-[background-position] motion-safe:animate-shine",
        className,
      )}
      {...props}
    />
  );
}
