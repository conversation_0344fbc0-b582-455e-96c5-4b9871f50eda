/**
 * RAG Settings API Service
 * 
 * Handles communication with backend RAG settings endpoints
 */

import { RAGModuleSettings } from '../components/settings/RAGSettingsManager';

// API Response interfaces
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

interface SettingsListItem {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  version: string;
  createdAt: string;
  updatedAt: string;
}

// Base API URL - adjust based on your backend configuration
// Vite verwendet import.meta.env anstelle von process.env
const API_BASE_URL = import.meta.env.DEV 
  ? 'http://localhost:3001/api/rag' 
  : '/api/rag';

/**
 * RAG Settings API Service Class
 */
export class RAGSettingsApiService {
  /**
   * Get active RAG settings from database
   */
  static async getActiveSettings(userId?: string): Promise<RAGModuleSettings> {
    try {
      const url = new URL(`${API_BASE_URL}/settings`);
      if (userId) {
        url.searchParams.append('userId', userId);
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<RAGModuleSettings> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get settings');
      }

      return result.data!;
    } catch (error) {
      console.error('[RAGSettingsApi] Error getting active settings:', error);
      throw error;
    }
  }

  /**
   * Save RAG settings to database
   */
  static async saveSettings(
    settings: RAGModuleSettings,
    userId?: string,
    name: string = 'default'
  ): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settings,
          userId,
          name
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to save settings');
      }
    } catch (error) {
      console.error('[RAGSettingsApi] Error saving settings:', error);
      throw error;
    }
  }

  /**
   * Get all settings configurations
   */
  static async getAllSettings(userId?: string): Promise<SettingsListItem[]> {
    try {
      const url = new URL(`${API_BASE_URL}/settings/all`);
      if (userId) {
        url.searchParams.append('userId', userId);
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<SettingsListItem[]> = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to get settings list');
      }

      return result.data || [];
    } catch (error) {
      console.error('[RAGSettingsApi] Error getting all settings:', error);
      throw error;
    }
  }

  /**
   * Delete settings configuration
   */
  static async deleteSettings(settingsId: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/settings/${settingsId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete settings');
      }
    } catch (error) {
      console.error('[RAGSettingsApi] Error deleting settings:', error);
      throw error;
    }
  }
}

export default RAGSettingsApiService;