/**
 * Rate Limiting Middleware
 * 
 * Implementiert Rate-Limiting für verschiedene API-Endpunkte,
 * um Missbrauch zu verhindern und die Server-Performance zu schützen.
 */

import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';

/**
 * Standard Rate-Limiting-Konfiguration
 */
const createRateLimit = (windowMs: number, max: number, message: string) => {
  return rateLimit({
    windowMs, // Zeitfenster in Millisekunden
    max, // Maximale Anzahl von Anfragen pro Zeitfenster
    message: {
      success: false,
      error: message,
      code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true, // Fügt `RateLimit-*` Headers hinzu
    legacyHeaders: false, // Deaktiviert `X-RateLimit-*` Headers
    handler: (req: Request, res: Response) => {
      console.warn(`[RATE-LIMIT] Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
      res.status(429).json({
        success: false,
        error: message,
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil(windowMs / 1000) // Sekunden bis zum nächsten Versuch
      });
    }
  });
};

/**
 * Rate-Limiting für E-Mail-Endpunkte
 * Sehr restriktiv, da E-Mail-Versand ressourcenintensiv ist
 */
export const rateLimitMiddleware = createRateLimit(
  15 * 60 * 1000, // 15 Minuten
  5, // Maximal 5 E-Mails pro 15 Minuten
  'Zu viele E-Mail-Anfragen. Bitte versuchen Sie es später erneut.'
);

/**
 * Rate-Limiting für allgemeine API-Endpunkte
 */
export const generalRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 Minuten
  100, // Maximal 100 Anfragen pro 15 Minuten
  'Zu viele API-Anfragen. Bitte versuchen Sie es später erneut.'
);

/**
 * Rate-Limiting für Upload-Endpunkte
 */
export const uploadRateLimit = createRateLimit(
  60 * 1000, // 1 Minute
  10, // Maximal 10 Uploads pro Minute
  'Zu viele Upload-Anfragen. Bitte versuchen Sie es später erneut.'
);

/**
 * Rate-Limiting für Authentifizierungs-Endpunkte
 */
export const authRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 Minuten
  5, // Maximal 5 Login-Versuche pro 15 Minuten
  'Zu viele Login-Versuche. Bitte versuchen Sie es später erneut.'
);

/**
 * Konfigurationsobjekt für verschiedene Rate-Limits
 */
export const rateLimitConfig = {
  general: generalRateLimit,
  dataEndpoint: generalRateLimit,
  writeOperation: generalRateLimit,
  healthCheck: generalRateLimit,
  metricsEndpoint: generalRateLimit,
  unauthenticated: generalRateLimit,
  emailSend: rateLimitMiddleware, // E-Mail-spezifisches Rate-Limiting
  upload: uploadRateLimit,
  auth: authRateLimit,
  adaptive: (req: any, res: any, next: any) => {
    // Adaptive Rate-Limiting basierend auf Request-Typ
    if (req.path.includes('/email/')) {
      return rateLimitMiddleware(req, res, next);
    }
    return generalRateLimit(req, res, next);
  }
};

/**
 * Exportiere Standard-Rate-Limit für E-Mails
 */
export default rateLimitMiddleware;