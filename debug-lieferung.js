const axios = require('axios');

// Debug-Skript um Lieferung-Werte zu untersuchen
async function debugLieferungValues() {
  try {
    console.log('=== Debug: Lieferung-Spalte Werte ===');
    
    // Erste Abfrage: Alle Trommeln ohne Filter (nur die ersten 10)
    console.log('\n1. Erste 10 Trommeln ohne Filter:');
    const response1 = await axios.post('http://localhost:3000/api/available-drums', {
      aufnahmeDatum: '2025-07-11',
      material: '',  // Kein Material-Filter
      minGesamtbestand: 0  // Kein Mindestbestand
    });
    
    console.log(`Anzahl Trommeln: ${response1.data.count}`);
    if (response1.data.drums && response1.data.drums.length > 0) {
      console.log('Erste 5 Trommeln mit Lieferung-Werten:');
      response1.data.drums.slice(0, 5).forEach((drum, index) => {
        console.log(`${index + 1}. Charge: ${drum.Charge}, Lieferung: '${drum.Lieferung}' (Type: ${typeof drum.Lieferung})`);
      });
    }
    
    // Zweite Abfrage: Direkte Datenbankabfrage über Raw SQL
    console.log('\n2. Direkte Datenbankabfrage für Lieferung-Werte:');
    const response2 = await axios.get('http://localhost:3000/api/debug/lieferung-values');
    
    if (response2.data) {
      console.log('Verschiedene Lieferung-Werte in der Datenbank:');
      console.log(response2.data);
    }
    
  } catch (error) {
    console.error('Fehler beim Debug:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
}

debugLieferungValues();