"use strict";
/**
 * Unit Tests for Chat Routes Error Handling
 *
 * Tests error handling and fallback mechanisms in chat API endpoints
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const vitest_1 = require("vitest");
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
const chat_routes_1 = __importDefault(require("../routes/chat.routes"));
const data_enrichment_service_1 = __importDefault(require("../services/data-enrichment.service"));
const openrouter_service_1 = __importDefault(require("../services/openrouter.service"));
// Mock dependencies
vitest_1.vi.mock('../services/data-enrichment.service');
vitest_1.vi.mock('../services/openrouter.service');
vitest_1.vi.mock('@prisma/client');
(0, vitest_1.describe)('Chat Routes Error Handling', () => {
    let app;
    let mockDataEnrichmentService;
    let mockOpenRouterService;
    (0, vitest_1.beforeEach)(() => {
        vitest_1.vi.clearAllMocks();
        // Create Express app with chat routes
        app = (0, express_1.default)();
        app.use(express_1.default.json());
        app.use('/api/chat', chat_routes_1.default);
        // Mock services
        mockDataEnrichmentService = {
            enrichChatContext: vitest_1.vi.fn()
        };
        mockOpenRouterService = {
            generateResponse: vitest_1.vi.fn()
        };
        // Replace module exports with mocks
        vitest_1.vi.mocked(data_enrichment_service_1.default).mockImplementation(() => mockDataEnrichmentService);
        Object.assign(openrouter_service_1.default, mockOpenRouterService);
    });
    (0, vitest_1.afterEach)(() => {
        vitest_1.vi.restoreAllMocks();
    });
    (0, vitest_1.describe)('Basic Chat Endpoint Error Handling', () => {
        (0, vitest_1.it)('should handle OpenRouter service failures gracefully', async () => {
            mockOpenRouterService.generateResponse.mockRejectedValue(new Error('OpenRouter API unavailable'));
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({ message: 'test message' });
            (0, vitest_1.expect)(response.status).toBe(500);
            (0, vitest_1.expect)(response.body.success).toBe(false);
            (0, vitest_1.expect)(response.body.error).toBe('Interner Serverfehler');
            (0, vitest_1.expect)(response.body.message).toContain('Chat-Anfrage');
        });
        (0, vitest_1.it)('should handle invalid request data', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({ message: '' }); // Empty message should fail validation
            (0, vitest_1.expect)(response.status).toBe(400);
        });
        (0, vitest_1.it)('should handle missing message field', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({}); // No message field
            (0, vitest_1.expect)(response.status).toBe(400);
        });
    });
    (0, vitest_1.describe)('Enhanced Chat Endpoint Error Handling', () => {
        (0, vitest_1.it)('should handle data enrichment service failures gracefully', async () => {
            // Mock data enrichment to fail
            mockDataEnrichmentService.enrichChatContext.mockRejectedValue(new Error('Database connection failed'));
            // Mock OpenRouter to succeed
            mockOpenRouterService.generateResponse.mockResolvedValue({
                success: true,
                response: 'Fallback response without data enrichment'
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'störungen heute',
                includeInsights: true,
                includeAnomalies: true
            });
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.dataEnrichmentUsed).toBe(false);
            (0, vitest_1.expect)(response.body.dataEnrichmentError).toBe(true);
            (0, vitest_1.expect)(response.body.enrichmentDetails.error).toContain('Database connection failed');
            (0, vitest_1.expect)(response.body.enrichmentDetails.fallbackUsed).toBe(true);
        });
        (0, vitest_1.it)('should handle partial data enrichment failures', async () => {
            // Mock data enrichment to return partial failure
            mockDataEnrichmentService.enrichChatContext.mockResolvedValue({
                originalMessage: 'system status',
                detectedIntents: [{ type: 'stoerungen', confidence: 0.8, keywords: ['system'] }],
                databaseContext: 'Partial system data available',
                hasData: true,
                dataTypes: ['stoerungen'],
                timestamp: new Date(),
                requestId: 'test-req-123',
                processingTime: 150,
                partialFailure: true
            });
            mockOpenRouterService.generateResponse.mockResolvedValue({
                success: true,
                response: 'Response with partial data'
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({ message: 'system status' });
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.dataEnrichmentUsed).toBe(true);
            (0, vitest_1.expect)(response.body.dataEnrichmentError).toBe(false);
            (0, vitest_1.expect)(response.body.enrichmentDetails.partialFailure).toBe(true);
            (0, vitest_1.expect)(response.body.dataQuality.hasPartialData).toBe(true);
            (0, vitest_1.expect)(response.body.dataQuality.hasFullData).toBe(false);
        });
        (0, vitest_1.it)('should handle complete data enrichment failure with fallback', async () => {
            // Mock data enrichment to return fallback context
            mockDataEnrichmentService.enrichChatContext.mockResolvedValue({
                originalMessage: 'test message',
                detectedIntents: [],
                databaseContext: 'Systemdatenbanken sind momentan nicht erreichbar',
                hasData: false,
                dataTypes: [],
                timestamp: new Date(),
                requestId: 'test-req-456',
                processingTime: 50,
                fallbackReason: 'ALL_QUERIES_FAILED'
            });
            mockOpenRouterService.generateResponse.mockResolvedValue({
                success: true,
                response: 'General assistance without database context'
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({ message: 'test message' });
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.dataEnrichmentUsed).toBe(false);
            (0, vitest_1.expect)(response.body.enrichmentDetails.fallbackReason).toBe('ALL_QUERIES_FAILED');
            (0, vitest_1.expect)(response.body.dataQuality.fallbackUsed).toBe(true);
            (0, vitest_1.expect)(response.body.dataQuality.availableDataTypes).toEqual([]);
        });
        (0, vitest_1.it)('should handle both data enrichment and OpenRouter failures', async () => {
            // Mock both services to fail
            mockDataEnrichmentService.enrichChatContext.mockRejectedValue(new Error('Data enrichment failed'));
            mockOpenRouterService.generateResponse.mockRejectedValue(new Error('OpenRouter failed'));
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({ message: 'test message' });
            (0, vitest_1.expect)(response.status).toBe(500);
            (0, vitest_1.expect)(response.body.success).toBe(false);
            (0, vitest_1.expect)(response.body.error).toBe('Interner Serverfehler');
        });
    });
    (0, vitest_1.describe)('Data Quality Indicators', () => {
        (0, vitest_1.it)('should correctly indicate full data availability', async () => {
            mockDataEnrichmentService.enrichChatContext.mockResolvedValue({
                originalMessage: 'störungen status',
                detectedIntents: [{ type: 'stoerungen', confidence: 0.9, keywords: ['störungen'] }],
                databaseContext: 'Complete störungen data',
                hasData: true,
                dataTypes: ['stoerungen'],
                timestamp: new Date(),
                requestId: 'test-req-789',
                processingTime: 200,
                partialFailure: false
            });
            mockOpenRouterService.generateResponse.mockResolvedValue({
                success: true,
                response: 'Complete response with full data'
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({ message: 'störungen status' });
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.dataQuality.hasFullData).toBe(true);
            (0, vitest_1.expect)(response.body.dataQuality.hasPartialData).toBe(false);
            (0, vitest_1.expect)(response.body.dataQuality.fallbackUsed).toBe(false);
            (0, vitest_1.expect)(response.body.dataQuality.availableDataTypes).toEqual(['stoerungen']);
        });
        (0, vitest_1.it)('should correctly indicate no data availability', async () => {
            mockDataEnrichmentService.enrichChatContext.mockResolvedValue({
                originalMessage: 'hello world',
                detectedIntents: [],
                databaseContext: 'Keine spezifischen Systemdaten erkannt',
                hasData: false,
                dataTypes: [],
                timestamp: new Date(),
                requestId: 'test-req-000',
                processingTime: 10,
                fallbackReason: 'NO_INTENTS_DETECTED'
            });
            mockOpenRouterService.generateResponse.mockResolvedValue({
                success: true,
                response: 'General response without system data'
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({ message: 'hello world' });
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.dataQuality.hasFullData).toBe(false);
            (0, vitest_1.expect)(response.body.dataQuality.hasPartialData).toBe(false);
            (0, vitest_1.expect)(response.body.dataQuality.fallbackUsed).toBe(true);
            (0, vitest_1.expect)(response.body.dataQuality.availableDataTypes).toEqual([]);
        });
    });
    (0, vitest_1.describe)('Request Validation', () => {
        (0, vitest_1.it)('should validate message length limits', async () => {
            const longMessage = 'a'.repeat(1001); // Exceeds 1000 character limit
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({ message: longMessage });
            (0, vitest_1.expect)(response.status).toBe(400);
        });
        (0, vitest_1.it)('should handle boolean parameter validation', async () => {
            mockDataEnrichmentService.enrichChatContext.mockResolvedValue({
                originalMessage: 'test',
                detectedIntents: [],
                databaseContext: 'test context',
                hasData: false,
                dataTypes: [],
                timestamp: new Date()
            });
            mockOpenRouterService.generateResponse.mockResolvedValue({
                success: true,
                response: 'test response'
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({
                message: 'test',
                includeInsights: 'invalid', // Should be boolean
                includeAnomalies: true
            });
            // Should still work with type coercion or validation
            (0, vitest_1.expect)(response.status).toBe(200);
        });
    });
    (0, vitest_1.describe)('Performance and Timeout Scenarios', () => {
        (0, vitest_1.it)('should handle slow data enrichment gracefully', async () => {
            // Mock slow data enrichment
            mockDataEnrichmentService.enrichChatContext.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({
                originalMessage: 'slow query',
                detectedIntents: [],
                databaseContext: 'Slow response',
                hasData: false,
                dataTypes: [],
                timestamp: new Date(),
                processingTime: 5000 // 5 seconds
            }), 100) // Resolve after 100ms for test
            ));
            mockOpenRouterService.generateResponse.mockResolvedValue({
                success: true,
                response: 'Response after slow enrichment'
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({ message: 'slow query' });
            (0, vitest_1.expect)(response.status).toBe(200);
            (0, vitest_1.expect)(response.body.enrichmentDetails.processingTime).toBeDefined();
        });
    });
    (0, vitest_1.describe)('Logging and Monitoring', () => {
        (0, vitest_1.it)('should log errors appropriately', async () => {
            const consoleSpy = vitest_1.vi.spyOn(console, 'error').mockImplementation(() => { });
            mockOpenRouterService.generateResponse.mockRejectedValue(new Error('Test error for logging'));
            await (0, supertest_1.default)(app)
                .post('/api/chat')
                .send({ message: 'test' });
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalledWith(vitest_1.expect.stringContaining('[CHAT]'), vitest_1.expect.stringContaining('Fehler:'), vitest_1.expect.any(Error));
            consoleSpy.mockRestore();
        });
        (0, vitest_1.it)('should log enhanced chat operations', async () => {
            const consoleSpy = vitest_1.vi.spyOn(console, 'log').mockImplementation(() => { });
            mockDataEnrichmentService.enrichChatContext.mockResolvedValue({
                originalMessage: 'test',
                detectedIntents: [],
                databaseContext: 'test',
                hasData: false,
                dataTypes: [],
                timestamp: new Date()
            });
            mockOpenRouterService.generateResponse.mockResolvedValue({
                success: true,
                response: 'test response'
            });
            await (0, supertest_1.default)(app)
                .post('/api/chat/enhanced')
                .send({ message: 'test' });
            (0, vitest_1.expect)(consoleSpy).toHaveBeenCalledWith(vitest_1.expect.stringContaining('[CHAT-ENHANCED]'));
            consoleSpy.mockRestore();
        });
    });
});
