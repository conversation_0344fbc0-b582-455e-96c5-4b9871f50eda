# SAP Workflow Logging Integration - Optimierte Backend-Struktur

## Übersicht

Diese optimierte Integration verbindet das Python SAP-Workflow-Skript mit dem React Frontend über ein strukturiertes Logging-System. Die Architektur folgt Best Practices mit klarer Trennung zwischen Backend-Logik und Frontend-UI.

## 🏗️ Neue Optimierte Struktur

```
backend/
├── database/
│   └── sap_datenbank.db              # SQLite-Datenbank
├── scripts/
│   └── workflows/
│       ├── workflowSAP.py            # Hauptskript (optimiert)
│       ├── workflow_logger.py        # Logger-Klasse (optimiert)
│       ├── test_logger.py            # Test-Skript
│       └── config.json               # Konfiguration
└── src/
    ├── controllers/workflowController.ts
    └── services/workflowService.ts    # Echte DB-Integration

src/modules/backend/components/workflows/
├── WorkflowLogViewer.tsx             # React-Komponente (bleibt hier)
└── README_Logging_Integration.md     # Diese Dokumentation
```

## Komponenten

### 1. Python Logging System

#### `workflow_logger.py`
- **Zweck**: Strukturiertes Logging für SAP-Workflows
- **Features**:
  - SQLite-Speicherung mit automatischer Tabellenerstellung
  - Verschiedene Log-Level (info, warning, error, debug)
  - Spezielle Methoden für SAP-Aktionen, Datei- und DB-Operationen
  - Eindeutige Execution-IDs für Workflow-Verfolgung

#### `workflowSAP.py`
- **Zweck**: Optimierte Version des SAP-Workflows mit Backend-Integration
- **Verbesserungen**:
  - Automatische Pfad-Erkennung für Backend-Struktur
  - Verbesserte Fehlerbehandlung und Validierung
  - Strukturierte Logs für jeden Prozessschritt
  - Separate Logger für verschiedene Prozesse
  - Konfigurierbare Einstellungen über config.json

### 2. Backend API

#### `backend/src/controllers/workflowController.ts`
- **Endpunkte**:
  - `GET /api/workflows/logs` - Lädt Workflow-Logs
  - `GET /api/workflows/processes` - Lädt verfügbare Prozesse
  - `POST /api/workflows/execute` - Führt Workflow aus
  - `GET /api/workflows/stats` - Lädt Statistiken

#### `backend/src/services/workflowService.ts`
- **Funktionen**:
  - Lädt Logs aus SQLite-Datenbank (echte Integration)
  - Verwaltet Workflow-Prozesse
  - Führt Python-Skripte aus (echte Ausführung)
  - Berechnet Statistiken
  - Automatische Pfad-Erkennung für Scripts und DB

#### `backend/src/routes/workflowRoutes.ts`
- **Routing**: Definiert API-Routen für Workflow-Operationen

### 3. Frontend Integration

#### `src/services/workflowService.ts`
- **Aktualisiert**: Lädt Logs von Backend-API statt lokaler Simulation
- **Transformation**: Konvertiert Backend-Log-Format zu Frontend-Format

#### `src/modules/backend/components/workflows/WorkflowLogViewer.tsx`
- **Verbessert**: Bessere Fehlerbehandlung und Null-Checks
- **Features**: Filter, Suche, Export von Logs

## Datenbank Schema

### `workflow_logs` Tabelle
```sql
CREATE TABLE workflow_logs (
    id TEXT PRIMARY KEY,
    timestamp TEXT NOT NULL,
    level TEXT NOT NULL,
    message TEXT NOT NULL,
    workflow_id TEXT NOT NULL,
    execution_id TEXT,
    details TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Indizes
- `idx_workflow_logs_workflow_id` - Für Workflow-spezifische Abfragen
- `idx_workflow_logs_timestamp` - Für zeitbasierte Sortierung

## Verwendung

### 1. Python-Skript ausführen
```bash
cd backend/scripts/workflows
python workflowSAP.py
```

### 2. Test-Logger ausführen
```bash
cd backend/scripts/workflows
python test_logger.py
```

### 3. Frontend-Logs anzeigen
- Navigiere zu Workflow-Log-Viewer
- Logs werden automatisch von der API geladen
- Filter und Suche verfügbar

## Log-Typen

### Standard-Logs
```python
logger.info("Informationsnachricht")
logger.warning("Warnung")
logger.error("Fehlermeldung")
logger.debug("Debug-Information")
```

### Spezielle Log-Methoden
```python
# Prozess-Logs
logger.log_process_start("Prozessname")
logger.log_process_complete("Prozessname", "export_path")
logger.log_process_error("Prozessname", "Fehlermeldung")

# SAP-Aktionen
logger.log_sap_action("Aktion", "element_id", success=True)

# Datei-Operationen
logger.log_file_operation("Export", "datei.xlsx", success=True)

# Datenbank-Operationen
logger.log_database_operation("Import", "tabelle", record_count=100, success=True)
```

## API-Endpunkte

### Logs abrufen
```http
GET /api/workflows/logs
GET /api/workflows/logs?workflowId=servicegrad&limit=50
```

### Prozesse abrufen
```http
GET /api/workflows/processes
```

### Prozess ausführen
```http
POST /api/workflows/execute
Content-Type: application/json

{
  "processId": "servicegrad"
}
```

## Konfiguration

### Python-Konfiguration
- Datenbankpfad: `DB_PATH = "sap_datenbank.db"`
- Log-Level: Alle Level standardmäßig aktiviert

### Backend-Konfiguration
- Standard-Limit für Logs: 100
- Standard-Limit für Ausführungen: 50

### Frontend-Konfiguration
- Automatisches Laden beim Komponenten-Mount
- Refresh-Funktion verfügbar
- Export als CSV möglich

## Fehlerbehandlung

### Python
- Try-catch um alle kritischen Operationen
- Fallback auf Konsolen-Output bei DB-Fehlern
- Detaillierte Fehlermeldungen mit Context

### Backend
- HTTP-Statuscodes für verschiedene Fehlertypen
- Strukturierte Fehlerantworten
- Logging von Backend-Fehlern

### Frontend
- Fallback auf lokale Logs bei API-Fehlern
- Benutzerfreundliche Fehlermeldungen
- Loading-States während API-Aufrufen

## Nächste Schritte

1. **Echte Datenbankintegration**: SQLite-Verbindung im Backend implementieren
2. **Python-Skript-Ausführung**: Echte Ausführung statt Simulation
3. **Real-time Updates**: WebSocket für Live-Log-Updates
4. **Log-Archivierung**: Automatische Komprimierung und Archivierung alter Logs (NIEMALS löschen!)
5. **Monitoring**: Alerts bei kritischen Fehlern

## Testing

### Test-Skript ausführen
```bash
python test_logger.py
```

### Erwartete Ausgabe
- Verschiedene Log-Level werden demonstriert
- Logs werden in Datenbank gespeichert
- Logs werden aus Datenbank gelesen und angezeigt

### Frontend-Test
1. Starte Backend-Server
2. Öffne Workflow-Log-Viewer
3. Überprüfe, ob Logs geladen werden
4. Teste Filter- und Suchfunktionen