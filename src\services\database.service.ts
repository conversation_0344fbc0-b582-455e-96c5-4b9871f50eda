/**
 * Zentrale Datenbankservice für das Backend - Drizzle Version
 * 
 * Dieser Service nutzt Drizzle ORM für Datenbankoperationen und stellt
 * eine einheitliche Schnittstelle für alle Datenbankzugriffe bereit.
 */

import { db } from '../db';
import { ragDb } from '../db/rag-db';
import { eq, desc, count, and, gte, lte } from 'drizzle-orm';
import { 
  dispatchData, 
  stoerungen, 
  user,
  auslastung200,
  auslastung240,
  ariL,
  atrL,
  system,
  schnitte,
  we,
  ablaengerei,
  alleDaten 
} from '../db/schema';

// Simple caching implementation
const serviceCache = new Map<string, { data: any; expires: number }>();

const getCached = <T>(key: string): T | null => {
  const entry = serviceCache.get(key);
  if (entry && entry.expires > Date.now()) {
    return entry.data;
  }
  serviceCache.delete(key);
  return null;
};

const setCache = (key: string, data: any, ttlMs: number = 300000) => {
  serviceCache.set(key, { data, expires: Date.now() + ttlMs });
};

/**
 * Cache-TTL-Konfiguration für verschiedene Datentypen
 */
const CACHE_TTL = {
  MACHINE_EFFICIENCY: 15 * 60 * 1000, // 15 Minuten
  SCHNITTE_DATA: 15 * 60 * 1000, // 15 Minuten
  WAREHOUSE_DATA: 5 * 60 * 1000, // 5 Minuten
  ABLANGEREI_DATA: 5 * 60 * 1000, // 5 Minuten
  WE_DATA: 5 * 60 * 1000, // 5 Minuten
  DISPATCH_DATA: 2 * 60 * 1000, // 2 Minuten
  ARIL_ATRL_DATA: 3 * 60 * 1000, // 3 Minuten
  SERVICE_LEVEL: 1 * 60 * 1000, // 1 Minute
  DAILY_PERFORMANCE: 1 * 60 * 1000, // 1 Minute
  SYSTEM_FTS_DATA: 5 * 60 * 1000, // 5 Minuten
  SYSTEM_STATS: 30 * 1000 // 30 Sekunden
};

/**
 * Zentrale Datenbankservice-Klasse - Drizzle Version
 */
export class DatabaseService {
  private static instance: DatabaseService;

  constructor() {
    console.log('✅ Drizzle-basierter DatabaseService initialisiert');
  }

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Initialisiert die Datenbankverbindung
   */
  async connect(): Promise<boolean> {
    try {
      console.log('Teste Drizzle-Datenbankverbindung...');
      
      // Testabfrage für SFM Dashboard DB
      const testResult = await db.select({ count: count() }).from(stoerungen).limit(1);
      
      // Testabfrage für RAG DB
      const { knowledgeBases } = await import('../db/rag-schema');
      const ragTestResult = await ragDb.select({ count: count() }).from(knowledgeBases).limit(1);
      
      console.log('✅ Beide Drizzle-Datenbankverbindungen erfolgreich getestet');
      console.log(`   - SFM Dashboard: Verfügbar (${testResult[0]?.count || 0} Test-Records)`);
      console.log(`   - RAG Database: Verfügbar (${ragTestResult[0]?.count || 0} Test-Records)`);
      
      return true;
    } catch (error: unknown) {
      console.error('❌ Fehler beim Testen der Drizzle-Datenbankverbindung:', error);
      return false;
    }
  }

  /**
   * Cache-Statistiken abrufen
   */
  getCacheStats() {
    return {
      size: serviceCache.size,
      entries: serviceCache.size,
      hitRate: 0.85, // Geschätzt
      type: 'drizzle-service-cache'
    };
  }

  /**
   * Cache für bestimmte Datentypen invalidieren
   */
  invalidateCache(dataTypes: string[] = ['dispatch', 'warehouse', 'cutting']): number {
    let cleared = 0;
    for (const [key, _] of serviceCache) {
      if (dataTypes.some(type => key.includes(type))) {
        serviceCache.delete(key);
        cleared++;
      }
    }
    return cleared;
  }

  /**
   * Kompletten Cache leeren
   */
  clearAllCache(): void {
    serviceCache.clear();
  }

  /**
   * Service Level Daten abrufen
   */
  async getServiceLevelData(): Promise<any[]> {
    const cacheKey = 'service-level-data';
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await db
        .select({
          id: dispatchData.id,
          datum: dispatchData.datum,
          servicegrad: dispatchData.servicegrad,
        })
        .from(dispatchData)
        .where(eq(dispatchData.servicegrad, null))
        .orderBy(desc(dispatchData.datum))
        .limit(100);

      const formatted = data.map(item => ({
        date: item.datum,
        serviceLevel: item.servicegrad || 0,
      }));

      setCache(cacheKey, formatted, CACHE_TTL.SERVICE_LEVEL);
      return formatted;
    } catch (error) {
      console.error('Fehler beim Abrufen der ServiceLevel-Daten:', error);
      throw error;
    }
  }

  /**
   * Tägliche Performance-Daten abrufen
   */
  async getDailyPerformanceData(): Promise<any[]> {
    const cacheKey = 'daily-performance-data';
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await db
        .select({
          id: dispatchData.id,
          datum: dispatchData.datum,
          ausgeliefertLup: dispatchData.ausgeliefertLup,
          rueckstaendig: dispatchData.rueckstaendig,
        })
        .from(dispatchData)
        .orderBy(desc(dispatchData.datum))
        .limit(100);

      const formatted = data.map(item => ({
        date: item.datum,
        delivered: item.ausgeliefertLup || 0,
        backlog: item.rueckstaendig || 0,
      }));

      setCache(cacheKey, formatted, CACHE_TTL.DAILY_PERFORMANCE);
      return formatted;
    } catch (error) {
      console.error('Fehler beim Abrufen der täglichen Leistungsdaten:', error);
      throw error;
    }
  }

  /**
   * Schnitte-Daten abrufen (Maschinen-Effizienz)
   */
  async getSchnitteData(): Promise<any[]> {
    const cacheKey = 'schnitte-data';
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const data = await db
        .select()
        .from(schnitte)
        .orderBy(desc(schnitte.datum))
        .limit(50);

      setCache(cacheKey, data, CACHE_TTL.SCHNITTE_DATA);
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Schnitte-Daten:', error);
      throw error;
    }
  }

  /**
   * System-Statistiken abrufen
   */
  async getSystemStats(): Promise<any> {
    const cacheKey = 'system-stats';
    const cached = getCached<any>(cacheKey);
    if (cached) return cached;

    try {
      const [
        totalStoerungen,
        activeStoerungen,
        userCount,
        recentActivity
      ] = await Promise.all([
        db.select({ count: count() }).from(stoerungen),
        db.select({ count: count() }).from(stoerungen).where(eq(stoerungen.status, 'NEW')),
        db.select({ count: count() }).from(user),
        db.select().from(dispatchData).orderBy(desc(dispatchData.datum)).limit(1)
      ]);

      const stats = {
        totalStoerungen: totalStoerungen[0].count,
        activeStoerungen: activeStoerungen[0].count,
        userCount: userCount[0].count,
        lastActivity: recentActivity[0]?.datum || 'N/A',
        cacheStats: this.getCacheStats()
      };

      setCache(cacheKey, stats, CACHE_TTL.SYSTEM_STATS);
      return stats;
    } catch (error) {
      console.error('Fehler beim Abrufen der System-Statistiken:', error);
      throw error;
    }
  }

  /**
   * Warehouse-Daten abrufen
   */
  async getWarehouseData(dateRange?: { startDate: string; endDate: string }): Promise<any[]> {
    const cacheKey = `warehouse-data:${JSON.stringify(dateRange || {})}`;
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      let query = db
        .select()
        .from(auslastung200)
        .orderBy(desc(auslastung200.aufnahmeDatum))
        .limit(100);

      if (dateRange) {
        query = query.where(and(
          gte(auslastung200.aufnahmeDatum, dateRange.startDate),
          lte(auslastung200.aufnahmeDatum, dateRange.endDate)
        ));
      }

      const data = await query;
      
      setCache(cacheKey, data, CACHE_TTL.WAREHOUSE_DATA);
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Warehouse-Daten:', error);
      throw error;
    }
  }

  /**
   * RAG Database Service - Knowledge Bases
   */
  async getKnowledgeBases(): Promise<any[]> {
    const cacheKey = 'knowledge-bases';
    const cached = getCached<any[]>(cacheKey);
    if (cached) return cached;

    try {
      const { knowledgeBases } = await import('../db/rag-schema');
      
      const data = await ragDb
        .select()
        .from(knowledgeBases)
        .where(eq(knowledgeBases.isActive, 1))
        .orderBy(desc(knowledgeBases.createdAt));
      
      setCache(cacheKey, data, 5 * 60 * 1000); // 5 minutes
      return data;
    } catch (error) {
      console.error('Fehler beim Abrufen der Knowledge Bases:', error);
      throw error;
    }
  }

  /**
   * Gesamt-Performance des Service
   */
  async getServiceHealth(): Promise<any> {
    try {
      const startTime = Date.now();
      
      await Promise.all([
        this.getSystemStats(),
        this.connect(),
      ]);
      
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'healthy',
        responseTime,
        cache: this.getCacheStats(),
        databases: ['sfm_dashboard', 'rag_knowledge'],
        version: 'drizzle-v1.0.0'
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        version: 'drizzle-v1.0.0'
      };
    }
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();
