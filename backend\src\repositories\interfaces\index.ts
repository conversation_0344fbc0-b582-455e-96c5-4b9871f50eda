/**
 * Repository Interfaces Export
 * 
 * Zentrale Exportdatei für alle Repository-Interfaces
 */

// Base Repository Interface
export * from './base.repository.interface';

// Domain-specific Repository Interfaces
export * from './dispatch.repository.interface';
export * from './warehouse.repository.interface';
export * from './cutting.repository.interface';

// Repository Factory Interface
export interface RepositoryFactory {
  dispatch(): DispatchRepository;
  warehouse(): WarehouseRepository;
  cutting(): CuttingRepository;
}

// Import types for re-export
import { DispatchRepository } from './dispatch.repository.interface';
import { WarehouseRepository } from './warehouse.repository.interface';
import { CuttingRepository } from './cutting.repository.interface';