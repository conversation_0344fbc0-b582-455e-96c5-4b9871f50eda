/**
 * AI Input Validator Component
 * 
 * Provides real-time input validation and sanitization for AI inputs.
 * Shows security warnings and prevents dangerous inputs.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { aiSecurityService } from '@/modules/ai/services/security/AISecurityService';
import { AIInputValidationResult, SecurityRiskLevel } from '@/modules/ai/types/security';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Shield, CheckCircle, XCircle } from 'lucide-react';

interface AIInputValidatorProps {
  value: string;
  onChange: (value: string, isValid: boolean, sanitizedValue?: string) => void;
  feature: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  showValidationDetails?: boolean;
  maxLength?: number;
}

/**
 * Validated input component for AI features
 */
export const AIInputValidator: React.FC<AIInputValidatorProps> = ({
  value,
  onChange,
  feature,
  placeholder = 'Geben <PERSON>e Ihre Anfrage ein...',
  disabled = false,
  className = '',
  showValidationDetails = true,
  maxLength = 10000
}) => {
  const [validation, setValidation] = useState<AIInputValidationResult>({
    isValid: true,
    sanitizedInput: '',
    violations: [],
    riskLevel: SecurityRiskLevel.LOW
  });
  const [showWarnings, setShowWarnings] = useState(false);

  // Debounced validation
  const validateInput = useCallback(
    debounce((input: string) => {
      const result = aiSecurityService.validateInput(input, feature);
      setValidation(result);
      setShowWarnings(result.violations.length > 0);

      // Notify parent component
      onChange(input, result.isValid, result.sanitizedInput);
    }, 300),
    [feature, onChange]
  );

  useEffect(() => {
    if (value) {
      validateInput(value);
    } else {
      setValidation({
        isValid: true,
        sanitizedInput: '',
        violations: [],
        riskLevel: SecurityRiskLevel.LOW
      });
      setShowWarnings(false);
      onChange('', true, '');
    }
  }, [value, validateInput, onChange]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;

    // Enforce max length
    if (newValue.length > maxLength) {
      return;
    }

    onChange(newValue, validation.isValid, validation.sanitizedInput);
  };

  const getRiskLevelColor = (riskLevel: SecurityRiskLevel) => {
    switch (riskLevel) {
      case SecurityRiskLevel.HIGH:
        return 'text-red-600 bg-red-50 border-red-200';
      case SecurityRiskLevel.MEDIUM:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-green-600 bg-green-50 border-green-200';
    }
  };

  const getRiskLevelIcon = (riskLevel: SecurityRiskLevel) => {
    switch (riskLevel) {
      case SecurityRiskLevel.HIGH:
        return <XCircle className="h-4 w-4" />;
      case SecurityRiskLevel.MEDIUM:
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <CheckCircle className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-3">
      {/* Input field */}
      <div className="relative">
        <Textarea
          value={value}
          onChange={handleInputChange}
          placeholder={placeholder}
          disabled={disabled}
          className={`${className} ${validation.riskLevel === SecurityRiskLevel.HIGH
              ? 'border-red-300 focus:border-red-500'
              : validation.riskLevel === SecurityRiskLevel.MEDIUM
                ? 'border-yellow-300 focus:border-yellow-500'
                : 'border-gray-300 focus:border-blue-500'
            }`}
          rows={4}
        />

        {/* Character count */}
        <div className="absolute bottom-2 right-2 text-xs text-gray-500">
          {value.length}/{maxLength}
        </div>
      </div>

      {/* Validation status */}
      {showValidationDetails && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Shield className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">Sicherheitsstatus:</span>
            <Badge
              variant="outline"
              className={getRiskLevelColor(validation.riskLevel)}
            >
              {getRiskLevelIcon(validation.riskLevel)}
              <span className="ml-1">
                {validation.riskLevel === SecurityRiskLevel.HIGH ? 'Hoch' :
                  validation.riskLevel === SecurityRiskLevel.MEDIUM ? 'Mittel' : 'Niedrig'}
              </span>
            </Badge>
          </div>

          {validation.isValid ? (
            <div className="flex items-center text-green-600 text-sm">
              <CheckCircle className="h-4 w-4 mr-1" />
              Eingabe gültig
            </div>
          ) : (
            <div className="flex items-center text-red-600 text-sm">
              <XCircle className="h-4 w-4 mr-1" />
              Eingabe ungültig
            </div>
          )}
        </div>
      )}

      {/* Security warnings */}
      {showWarnings && validation.violations.length > 0 && (
        <Alert className={`${getRiskLevelColor(validation.riskLevel)} border`}>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="font-medium">Sicherheitswarnungen:</div>
              <ul className="list-disc list-inside space-y-1 text-sm">
                {validation.violations.map((violation, index) => (
                  <li key={index}>{violation}</li>
                ))}
              </ul>
              {validation.riskLevel === SecurityRiskLevel.HIGH && (
                <div className="mt-2 p-2 bg-red-100 rounded text-sm">
                  <strong>Eingabe blockiert:</strong> Diese Eingabe enthält potentiell gefährliche Inhalte und kann nicht verarbeitet werden.
                </div>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Sanitization notice */}
      {validation.sanitizedInput !== value && validation.sanitizedInput && (
        <Alert className="bg-blue-50 border-blue-200">
          <Shield className="h-4 w-4 text-blue-600" />
          <AlertDescription className="text-blue-800">
            <div className="space-y-2">
              <div className="font-medium">Eingabe wurde bereinigt</div>
              <div className="text-sm">
                Potentiell unsichere Zeichen wurden automatisch entfernt oder ersetzt.
              </div>
              <details className="text-sm">
                <summary className="cursor-pointer hover:text-blue-900">
                  Bereinigte Version anzeigen
                </summary>
                <div className="mt-2 p-2 bg-white rounded border font-mono text-xs">
                  {validation.sanitizedInput}
                </div>
              </details>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

/**
 * Simple debounce utility
 */
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}