import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import InventoryIntelligenceDashboard from '../InventoryIntelligenceDashboard';
import { InventoryIntelligenceService } from '../../../services/inventory/InventoryIntelligenceService';

// Mock the entire inventory intelligence service
vi.mock('../../../services/inventory/InventoryIntelligenceService');

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}));

// Mock recharts for integration testing
vi.mock('recharts', () => ({
  BarChart: ({ children, data }: any) => (
    <div data-testid="bar-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Bar: ({ dataKey, onClick }: any) => (
    <div data-testid="bar" data-key={dataKey} onClick={onClick} />
  ),
  LineChart: ({ children, data }: any) => (
    <div data-testid="line-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Line: ({ dataKey }: any) => <div data-testid="line" data-key={dataKey} />,
  Area: ({ dataKey }: any) => <div data-testid="area" data-key={dataKey} />,
  XAxis: ({ dataKey }: any) => <div data-testid="x-axis" data-key={dataKey} />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: ({ data }: any) => (
    <div data-testid="pie" data-pie-data={JSON.stringify(data)} />
  ),
  Cell: () => <div data-testid="cell" />,
  ReferenceLine: ({ x }: any) => <div data-testid="reference-line" data-x={x} />,
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="responsive-container">{children}</div>
  )
}));

// Mock chart components
vi.mock('@/components/ui/chart', () => ({
  ChartContainer: ({ children }: any) => (
    <div data-testid="chart-container">{children}</div>
  ),
  ChartTooltip: ({ children }: any) => (
    <div data-testid="chart-tooltip">{children}</div>
  ),
  ChartTooltipContent: ({ children }: any) => (
    <div data-testid="chart-tooltip-content">{children}</div>
  )
}));

// Mock ErrorBoundary
vi.mock('@/components/ErrorBoundary', () => ({
  ChartErrorBoundary: ({ children }: any) => <div data-testid="error-boundary">{children}</div>
}));

describe('Inventory Intelligence Integration Tests', () => {
  const mockService = {
    initialize: vi.fn(),
    performABCAnalysis: vi.fn(),
    detectStockAnomalies: vi.fn(),
    calculateOptimalReorderPoint: vi.fn(),
    forecastDemand: vi.fn(),
    detectSeasonality: vi.fn()
  };

  const mockInventoryItems = [
    {
      id: 'NYY-J-3x1.5',
      name: 'NYY-J 3x1,5 mm²',
      category: 'Installationskabel',
      currentStock: 2500,
      unitPrice: 1.25,
      supplier: 'Lapp Kabel',
      location: 'Lager A',
      lastUpdated: new Date()
    },
    {
      id: 'NYM-J-5x2.5',
      name: 'NYM-J 5x2,5 mm²',
      category: 'Mantelleitung',
      currentStock: 1800,
      unitPrice: 3.45,
      supplier: 'Lapp Kabel',
      location: 'Lager B',
      lastUpdated: new Date()
    },
    {
      id: 'H07V-K-1x16',
      name: 'H07V-K 1x16 mm²',
      category: 'Einzelader',
      currentStock: 850,
      unitPrice: 2.15,
      supplier: 'Lapp Kabel',
      location: 'Lager A',
      lastUpdated: new Date()
    }
  ];

  const mockABCClassification = {
    classA: [mockInventoryItems[0]],
    classB: [mockInventoryItems[1]],
    classC: [mockInventoryItems[2]],
    reclassifications: [
      {
        itemId: 'NYY-J-3x1.5',
        previousClass: 'B' as const,
        newClass: 'A' as const,
        reason: 'Increased consumption pattern detected',
        confidence: 0.87
      }
    ],
    analysisDate: new Date(),
    criteria: {
      method: 'value' as const,
      classAThreshold: 80,
      classBThreshold: 95,
      timeWindow: 90
    }
  };

  const mockReorderRecommendations = [
    {
      itemId: 'NYY-J-3x1.5',
      currentStock: 2500,
      reorderPoint: 3000,
      recommendedOrderQuantity: 5000,
      urgency: 'medium' as const,
      reasoning: 'Stock approaching reorder point based on consumption forecast',
      confidence: 0.85,
      leadTime: 14,
      safetyStock: 500
    },
    {
      itemId: 'H07V-K-1x16',
      currentStock: 850,
      reorderPoint: 1200,
      recommendedOrderQuantity: 2000,
      urgency: 'high' as const,
      reasoning: 'Low stock level with high consumption rate',
      confidence: 0.92,
      estimatedStockoutDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      leadTime: 10,
      safetyStock: 200
    }
  ];

  const mockStockAnomalies = [
    {
      id: 'anomaly-1',
      itemId: 'NYM-J-5x2.5',
      anomalyType: 'sudden_spike' as const,
      severity: 'medium' as const,
      detectedAt: new Date(),
      description: 'Consumption spike detected - 150% above normal pattern',
      currentValue: 300,
      expectedValue: 120,
      deviation: 150,
      recommendations: [
        'Investigate potential bulk order or project requirement',
        'Verify inventory accuracy',
        'Adjust forecasting parameters if pattern continues'
      ],
      confidence: 0.78
    }
  ];

  const mockDemandForecast = {
    itemId: 'NYY-J-3x1.5',
    predictions: Array.from({ length: 30 }, (_, i) => ({
      date: new Date(Date.now() + i * 24 * 60 * 60 * 1000),
      predictedDemand: 45 + Math.sin(i * 0.2) * 10 + Math.random() * 5,
      confidenceInterval: {
        lower: 35 + Math.sin(i * 0.2) * 8,
        upper: 55 + Math.sin(i * 0.2) * 12
      },
      seasonalAdjustment: Math.sin(i * 0.2) * 5
    })),
    confidence: 0.85,
    seasonalFactors: [
      { period: 'weekly' as const, factor: 1.15, confidence: 0.8 },
      { period: 'monthly' as const, factor: 0.95, confidence: 0.7 }
    ],
    trendDirection: 'stable' as const,
    forecastMethod: 'exponential_smoothing',
    accuracy: 0.89,
    generatedAt: new Date()
  };

  const mockSeasonalityPattern = {
    itemId: 'NYY-J-3x1.5',
    hasSeasonality: true,
    patterns: [
      {
        type: 'weekly' as const,
        cycle: 7,
        amplitude: 0.25,
        phase: 2
      }
    ],
    strength: 0.4,
    detectedAt: new Date()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup mock service
    (InventoryIntelligenceService as any).mockImplementation(() => mockService);
    
    // Setup default mock responses
    mockService.initialize.mockResolvedValue(undefined);
    mockService.performABCAnalysis.mockResolvedValue(mockABCClassification);
    mockService.detectStockAnomalies.mockResolvedValue(mockStockAnomalies);
    mockService.calculateOptimalReorderPoint.mockImplementation((itemId) => 
      Promise.resolve(mockReorderRecommendations.find(r => r.itemId === itemId) || mockReorderRecommendations[0])
    );
    mockService.forecastDemand.mockResolvedValue(mockDemandForecast);
    mockService.detectSeasonality.mockResolvedValue(mockSeasonalityPattern);
  });

  it('completes full inventory intelligence workflow', async () => {
    render(<InventoryIntelligenceDashboard />);

    // 1. Initial load and service initialization
    await waitFor(() => {
      expect(mockService.initialize).toHaveBeenCalled();
      expect(mockService.performABCAnalysis).toHaveBeenCalled();
      expect(mockService.detectStockAnomalies).toHaveBeenCalled();
    });

    // 2. Verify ABC analysis is displayed
    expect(screen.getByText('Gesamt Artikel')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('Klasse A Artikel')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();

    // 3. Check reclassification alerts
    expect(screen.getByText('Umklassifizierungen')).toBeInTheDocument();
    expect(screen.getByText('1 Artikel wurden umklassifiziert')).toBeInTheDocument();

    // 4. Switch to demand forecast tab and select an item
    const forecastTab = screen.getByText('Bedarfsprognose');
    fireEvent.click(forecastTab);

    // Select an item for detailed analysis
    const itemSelect = screen.getByDisplayValue('');
    fireEvent.change(itemSelect, { target: { value: 'NYY-J-3x1.5' } });

    await waitFor(() => {
      expect(mockService.forecastDemand).toHaveBeenCalledWith('NYY-J-3x1.5', 30);
      expect(mockService.detectSeasonality).toHaveBeenCalledWith('NYY-J-3x1.5');
    });

    // 5. Verify forecast display
    expect(screen.getByText('Bedarfsprognose')).toBeInTheDocument();
    expect(screen.getByText('Ø Tagesbedarf')).toBeInTheDocument();
    expect(screen.getByText('Stabil')).toBeInTheDocument();

    // 6. Switch to recommendations tab
    const recommendationsTab = screen.getByText('Empfehlungen');
    fireEvent.click(recommendationsTab);

    // 7. Verify recommendations are displayed
    expect(screen.getByText('Nachbestellempfehlungen')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument(); // Number of recommendations
    expect(screen.getByText('1 dringend')).toBeInTheDocument(); // Urgent recommendations

    // 8. Verify anomalies are displayed
    expect(screen.getByText('Bestandsanomalien')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // Number of anomalies
    expect(screen.getByText('Consumption spike detected - 150% above normal pattern')).toBeInTheDocument();

    // 9. Test recommendation approval workflow
    const approveButtons = screen.getAllByText('Genehmigen');
    expect(approveButtons.length).toBeGreaterThan(0);

    // 10. Switch to overview tab
    const overviewTab = screen.getByText('Übersicht');
    fireEvent.click(overviewTab);

    // 11. Verify overview metrics
    expect(screen.getByText('Analysierte Artikel')).toBeInTheDocument();
    expect(screen.getByText('Aktive Anomalien')).toBeInTheDocument();
    expect(screen.getByText('Empfehlungen')).toBeInTheDocument();
  });

  it('handles configuration changes and re-analysis', async () => {
    render(<InventoryIntelligenceDashboard />);

    // Wait for initial load
    await waitFor(() => {
      expect(mockService.initialize).toHaveBeenCalled();
    });

    // Change analysis timeframe
    const timeframeSelect = screen.getByDisplayValue('90');
    fireEvent.change(timeframeSelect, { target: { value: '180' } });

    // Change forecast horizon
    const horizonSelect = screen.getByDisplayValue('30');
    fireEvent.change(horizonSelect, { target: { value: '60' } });

    // Trigger refresh
    const refreshButton = screen.getByText('Aktualisieren');
    fireEvent.click(refreshButton);

    await waitFor(() => {
      expect(mockService.performABCAnalysis).toHaveBeenCalledTimes(2);
    });
  });

  it('handles error scenarios gracefully', async () => {
    // Mock service errors
    mockService.performABCAnalysis.mockRejectedValue(new Error('Analysis failed'));
    mockService.forecastDemand.mockRejectedValue(new Error('Forecast failed'));

    render(<InventoryIntelligenceDashboard />);

    // Should still render basic structure
    expect(screen.getByText('INVENTORY INTELLIGENCE')).toBeInTheDocument();
    expect(screen.getByText('Analyse-Einstellungen')).toBeInTheDocument();

    // Try to select an item for forecast
    const forecastTab = screen.getByText('Bedarfsprognose');
    fireEvent.click(forecastTab);

    const itemSelect = screen.getByDisplayValue('');
    fireEvent.change(itemSelect, { target: { value: 'NYY-J-3x1.5' } });

    // Should handle error gracefully
    await waitFor(() => {
      expect(mockService.forecastDemand).toHaveBeenCalled();
    });
  });

  it('supports export functionality with complete data', async () => {
    // Mock URL and document methods
    const mockCreateObjectURL = vi.fn(() => 'mock-url');
    const mockRevokeObjectURL = vi.fn();
    global.URL.createObjectURL = mockCreateObjectURL;
    global.URL.revokeObjectURL = mockRevokeObjectURL;

    const mockAnchor = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    const mockCreateElement = vi.fn(() => mockAnchor);
    const mockAppendChild = vi.fn();
    const mockRemoveChild = vi.fn();
    
    Object.defineProperty(document, 'createElement', { value: mockCreateElement });
    Object.defineProperty(document.body, 'appendChild', { value: mockAppendChild });
    Object.defineProperty(document.body, 'removeChild', { value: mockRemoveChild });

    render(<InventoryIntelligenceDashboard />);

    // Wait for data to load
    await waitFor(() => {
      expect(mockService.performABCAnalysis).toHaveBeenCalled();
    });

    // Select an item to generate forecast data
    const itemSelect = screen.getByDisplayValue('');
    fireEvent.change(itemSelect, { target: { value: 'NYY-J-3x1.5' } });

    await waitFor(() => {
      expect(mockService.forecastDemand).toHaveBeenCalled();
    });

    // Export data
    const exportButton = screen.getByText('Excel Export');
    fireEvent.click(exportButton);

    await waitFor(() => {
      expect(mockCreateObjectURL).toHaveBeenCalled();
      expect(mockAnchor.click).toHaveBeenCalled();
    });

    // Verify export data includes all components
    const createObjectURLCall = mockCreateObjectURL.mock.calls[0][0];
    expect(createObjectURLCall).toBeInstanceOf(Blob);
  });

  it('maintains state consistency across tab switches', async () => {
    render(<InventoryIntelligenceDashboard />);

    // Wait for initial load
    await waitFor(() => {
      expect(mockService.performABCAnalysis).toHaveBeenCalled();
    });

    // Select an item
    const itemSelect = screen.getByDisplayValue('');
    fireEvent.change(itemSelect, { target: { value: 'NYY-J-3x1.5' } });

    // Switch between tabs
    const forecastTab = screen.getByText('Bedarfsprognose');
    fireEvent.click(forecastTab);

    const recommendationsTab = screen.getByText('Empfehlungen');
    fireEvent.click(recommendationsTab);

    const abcTab = screen.getByText('ABC-Analyse');
    fireEvent.click(abcTab);

    // Data should remain consistent
    expect(screen.getByText('Klasse A Artikel')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('handles real-time updates and refresh cycles', async () => {
    render(<InventoryIntelligenceDashboard />);

    // Initial load
    await waitFor(() => {
      expect(mockService.performABCAnalysis).toHaveBeenCalledTimes(1);
    });

    // Simulate multiple refresh cycles
    const refreshButton = screen.getByText('Aktualisieren');
    
    fireEvent.click(refreshButton);
    await waitFor(() => {
      expect(mockService.performABCAnalysis).toHaveBeenCalledTimes(2);
    });

    fireEvent.click(refreshButton);
    await waitFor(() => {
      expect(mockService.performABCAnalysis).toHaveBeenCalledTimes(3);
    });

    // Service should be called for each refresh
    expect(mockService.detectStockAnomalies).toHaveBeenCalledTimes(3);
  });

  it('validates complete user workflow from analysis to action', async () => {
    render(<InventoryIntelligenceDashboard />);

    // 1. Wait for initial analysis
    await waitFor(() => {
      expect(screen.getByText('Klasse A Artikel')).toBeInTheDocument();
    });

    // 2. User reviews ABC analysis
    expect(screen.getByText('NYY-J 3x1,5 mm²')).toBeInTheDocument();
    expect(screen.getByText('Increased consumption pattern detected')).toBeInTheDocument();

    // 3. User selects item for detailed forecast
    const itemSelect = screen.getByDisplayValue('');
    fireEvent.change(itemSelect, { target: { value: 'NYY-J-3x1.5' } });

    // 4. Switch to forecast view
    const forecastTab = screen.getByText('Bedarfsprognose');
    fireEvent.click(forecastTab);

    await waitFor(() => {
      expect(screen.getByText('Bedarfsprognose')).toBeInTheDocument();
    });

    // 5. User reviews recommendations
    const recommendationsTab = screen.getByText('Empfehlungen');
    fireEvent.click(recommendationsTab);

    expect(screen.getByText('Stock approaching reorder point based on consumption forecast')).toBeInTheDocument();
    expect(screen.getByText('Low stock level with high consumption rate')).toBeInTheDocument();

    // 6. User can take action on recommendations
    const approveButtons = screen.getAllByText('Genehmigen');
    expect(approveButtons.length).toBeGreaterThan(0);

    // 7. User reviews anomalies
    expect(screen.getByText('Consumption spike detected - 150% above normal pattern')).toBeInTheDocument();
    expect(screen.getByText('Investigate potential bulk order or project requirement')).toBeInTheDocument();

    // Complete workflow validated
    expect(mockService.initialize).toHaveBeenCalled();
    expect(mockService.performABCAnalysis).toHaveBeenCalled();
    expect(mockService.forecastDemand).toHaveBeenCalled();
    expect(mockService.detectSeasonality).toHaveBeenCalled();
    expect(mockService.detectStockAnomalies).toHaveBeenCalled();
  });
});