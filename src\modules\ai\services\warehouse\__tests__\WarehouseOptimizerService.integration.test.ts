/**
 * Warehouse Optimizer Service Integration Tests
 * 
 * Tests the integration between warehouse optimization service and existing data repositories
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { WarehouseOptimizerService } from '../WarehouseOptimizerService';
import { WarehouseRepository } from '@/repositories/warehouse.repository';
import { AIServiceStatus } from '../../base/AIBaseService';

// Mock the warehouse repository
vi.mock('@/repositories/warehouse.repository');

describe('WarehouseOptimizerService - Integration Tests', () => {
  let service: WarehouseOptimizerService;
  let mockWarehouseRepository: vi.Mocked<WarehouseRepository>;

  beforeEach(() => {
    mockWarehouseRepository = vi.mocked(new WarehouseRepository());
    
    service = new WarehouseOptimizerService({
      optimizationDepth: 'detailed',
      enableRealTimeOptimization: true,
      maxOptimizationTime: 30
    });
    
    // Mock repository methods
    mockWarehouseRepository.getOverallStats.mockResolvedValue({
      warehouse200: { avgUtilization: 75 },
      warehouse240: { avgUtilization: 80 },
      cutting: { totalAril: 500, totalManl: 400 },
      wareneingang: { totalAtrl: 1000, totalManl: 800 },
      atrlData: 30,
      manualData: 25
    });

    // Replace the repository instance
    (service as any).warehouseRepository = mockWarehouseRepository;
  });

  describe('Service Initialization', () => {
    it('should initialize successfully with warehouse repository connection', async () => {
      await service.initialize();
      
      expect(service.getStatus()).toBe(AIServiceStatus.READY);
      expect(mockWarehouseRepository.getOverallStats).toHaveBeenCalled();
    });

    it('should handle repository connection failures gracefully', async () => {
      mockWarehouseRepository.getOverallStats.mockRejectedValue(new Error('Database connection failed'));
      
      await expect(service.initialize()).rejects.toThrow('Warehouse optimizer initialization failed');
      expect(service.getStatus()).toBe(AIServiceStatus.ERROR);
    });
  });

  describe('Warehouse Layout Analysis', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should analyze warehouse layout and identify inefficiencies', async () => {
      const analysis = await service.analyzeWarehouseLayout();
      
      expect(analysis).toBeDefined();
      expect(analysis.totalItems).toBeGreaterThan(0);
      expect(analysis.utilizationRate).toBeGreaterThanOrEqual(0);
      expect(analysis.utilizationRate).toBeLessThanOrEqual(1);
      expect(analysis.accessibilityScore).toBeGreaterThanOrEqual(0);
      expect(analysis.accessibilityScore).toBeLessThanOrEqual(10);
      expect(Array.isArray(analysis.inefficiencies)).toBe(true);
      expect(Array.isArray(analysis.recommendations)).toBe(true);
    });

    it('should calculate optimization potential correctly', async () => {
      const analysis = await service.analyzeWarehouseLayout();
      
      expect(analysis.optimizationPotential).toBeGreaterThanOrEqual(0);
      expect(analysis.optimizationPotential).toBeLessThanOrEqual(100);
    });

    it('should identify misplaced high-frequency items', async () => {
      const analysis = await service.analyzeWarehouseLayout();
      
      const misplacedInefficiency = analysis.inefficiencies.find(
        ineff => ineff.type === 'misplaced_high_frequency'
      );
      
      if (misplacedInefficiency) {
        expect(misplacedInefficiency.affectedItems.length).toBeGreaterThan(0);
        expect(misplacedInefficiency.estimatedImpact).toBeGreaterThan(0);
        expect(['low', 'medium', 'high', 'critical']).toContain(misplacedInefficiency.severity);
      }
    });
  });

  describe('Optimal Placement Generation', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should generate optimal placements for warehouse items', async () => {
      const placements = await service.generateOptimalPlacements();
      
      expect(Array.isArray(placements)).toBe(true);
      
      for (const placement of placements) {
        expect(placement.itemId).toBeDefined();
        expect(placement.currentLocation).toBeDefined();
        expect(placement.recommendedLocation).toBeDefined();
        expect(placement.reason).toBeDefined();
        expect(placement.expectedBenefit).toBeDefined();
        expect(placement.confidence).toBeGreaterThanOrEqual(0);
        expect(placement.confidence).toBeLessThanOrEqual(1);
        
        // Verify expected benefit structure
        expect(placement.expectedBenefit.timeSavingsPerDay).toBeGreaterThanOrEqual(0);
        expect(placement.expectedBenefit.efficiencyImprovement).toBeGreaterThanOrEqual(0);
        expect(placement.expectedBenefit.costSavings).toBeGreaterThanOrEqual(0);
        expect(placement.expectedBenefit.pickingDistanceReduction).toBeGreaterThanOrEqual(0);
      }
    });

    it('should prioritize high-frequency items for optimization', async () => {
      const placements = await service.generateOptimalPlacements();
      
      // If there are placements, they should be for items that benefit from relocation
      for (const placement of placements) {
        expect(placement.expectedBenefit.timeSavingsPerDay).toBeGreaterThan(0);
      }
    });
  });

  describe('Picking Route Optimization', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should optimize picking routes for given items', async () => {
      const request = {
        orderId: 'TEST_ORDER_001',
        items: [
          {
            itemId: 'ITEM_001',
            quantity: 2,
            location: {
              id: 'A-1-1',
              zone: 'A',
              aisle: '1',
              shelf: '1',
              position: '1',
              coordinates: { x: 10, y: 5, z: 0 },
              capacity: 100,
              currentUtilization: 0.8,
              accessibilityScore: 9
            },
            priority: 'high' as const,
            weight: 2.5,
            fragile: false
          }
        ],
        startLocation: { x: 0, y: 0, z: 0 },
        constraints: {
          maxWeight: 50,
          maxVolume: 1000,
          timeLimit: 60
        }
      };

      const optimizedRoute = await service.optimizePickingRoute(request);
      
      expect(optimizedRoute).toBeDefined();
      expect(optimizedRoute.routeId).toBeDefined();
      expect(optimizedRoute.orderId).toBe(request.orderId);
      expect(optimizedRoute.sequence.length).toBe(request.items.length);
      expect(optimizedRoute.totalDistance).toBeGreaterThanOrEqual(0);
      expect(optimizedRoute.estimatedTime).toBeGreaterThan(0);
      expect(optimizedRoute.efficiency).toBeGreaterThanOrEqual(0);
      expect(optimizedRoute.efficiency).toBeLessThanOrEqual(1);
      expect(Array.isArray(optimizedRoute.alternativeRoutes)).toBe(true);
      
      // Verify sequence structure
      for (let i = 0; i < optimizedRoute.sequence.length; i++) {
        const step = optimizedRoute.sequence[i];
        expect(step.stepNumber).toBe(i + 1);
        expect(step.itemId).toBeDefined();
        expect(step.location).toBeDefined();
        expect(step.quantity).toBeGreaterThan(0);
        expect(step.distanceFromPrevious).toBeGreaterThanOrEqual(0);
        expect(step.estimatedTime).toBeGreaterThan(0);
        expect(step.instructions).toBeDefined();
      }
    });

    it('should generate alternative routes when possible', async () => {
      const request = {
        orderId: 'TEST_ORDER_002',
        items: [
          {
            itemId: 'ITEM_001',
            quantity: 1,
            location: {
              id: 'A-1-1',
              zone: 'A',
              aisle: '1',
              shelf: '1',
              position: '1',
              coordinates: { x: 10, y: 5, z: 0 },
              capacity: 100,
              currentUtilization: 0.8,
              accessibilityScore: 9
            },
            priority: 'medium' as const,
            weight: 1.5,
            fragile: false
          },
          {
            itemId: 'ITEM_002',
            quantity: 1,
            location: {
              id: 'B-2-3',
              zone: 'B',
              aisle: '2',
              shelf: '3',
              position: '1',
              coordinates: { x: 120, y: 15, z: 0 },
              capacity: 100,
              currentUtilization: 0.6,
              accessibilityScore: 7
            },
            priority: 'low' as const,
            weight: 3.0,
            fragile: true
          }
        ],
        startLocation: { x: 0, y: 0, z: 0 },
        constraints: {
          maxWeight: 50,
          maxVolume: 1000
        }
      };

      const optimizedRoute = await service.optimizePickingRoute(request);
      
      expect(optimizedRoute.alternativeRoutes.length).toBeGreaterThanOrEqual(0);
      
      for (const altRoute of optimizedRoute.alternativeRoutes) {
        expect(altRoute.routeId).toBeDefined();
        expect(altRoute.description).toBeDefined();
        expect(altRoute.totalDistance).toBeGreaterThanOrEqual(0);
        expect(altRoute.estimatedTime).toBeGreaterThan(0);
        expect(altRoute.efficiency).toBeGreaterThanOrEqual(0);
        expect(altRoute.efficiency).toBeLessThanOrEqual(1);
        expect(Array.isArray(altRoute.tradeoffs)).toBe(true);
      }
    });
  });

  describe('Warehouse Efficiency Analysis', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should analyze warehouse efficiency comprehensively', async () => {
      const analysis = await service.analyzeWarehouseEfficiency();
      
      expect(analysis).toBeDefined();
      expect(analysis.overallEfficiency).toBeGreaterThanOrEqual(0);
      expect(analysis.overallEfficiency).toBeLessThanOrEqual(1);
      expect(analysis.pickingEfficiency).toBeGreaterThanOrEqual(0);
      expect(analysis.pickingEfficiency).toBeLessThanOrEqual(1);
      expect(analysis.storageEfficiency).toBeGreaterThanOrEqual(0);
      expect(analysis.storageEfficiency).toBeLessThanOrEqual(1);
      expect(analysis.accessibilityEfficiency).toBeGreaterThanOrEqual(0);
      expect(analysis.accessibilityEfficiency).toBeLessThanOrEqual(1);
      
      expect(Array.isArray(analysis.bottlenecks)).toBe(true);
      expect(Array.isArray(analysis.trends)).toBe(true);
      expect(Array.isArray(analysis.benchmarks)).toBe(true);
    });

    it('should identify efficiency bottlenecks', async () => {
      const analysis = await service.analyzeWarehouseEfficiency();
      
      for (const bottleneck of analysis.bottlenecks) {
        expect(['congested_aisle', 'poor_item_placement', 'inefficient_routing', 'underutilized_space'])
          .toContain(bottleneck.type);
        expect(bottleneck.location).toBeDefined();
        expect(['low', 'medium', 'high', 'critical']).toContain(bottleneck.severity);
        expect(bottleneck.impact).toBeGreaterThanOrEqual(0);
        expect(bottleneck.description).toBeDefined();
        expect(Array.isArray(bottleneck.recommendations)).toBe(true);
      }
    });

    it('should provide efficiency benchmarks', async () => {
      const analysis = await service.analyzeWarehouseEfficiency();
      
      for (const benchmark of analysis.benchmarks) {
        expect(benchmark.metric).toBeDefined();
        expect(benchmark.currentValue).toBeGreaterThanOrEqual(0);
        expect(benchmark.industryAverage).toBeGreaterThanOrEqual(0);
        expect(benchmark.bestPractice).toBeGreaterThanOrEqual(0);
        expect(benchmark.gap).toBeDefined();
        expect(Array.isArray(benchmark.recommendations)).toBe(true);
      }
    });
  });

  describe('Performance and Caching', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should complete warehouse analysis within reasonable time', async () => {
      const startTime = Date.now();
      
      await service.analyzeWarehouseLayout();
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle cache operations correctly', async () => {
      const healthStatus = service.getHealthStatus();
      
      expect(healthStatus.status).toBeDefined();
      expect(healthStatus.details).toBeDefined();
      expect(healthStatus.details.cachedResultsCount).toBeGreaterThanOrEqual(0);
      
      // Clear cache
      service.clearCache();
      
      const healthStatusAfterClear = service.getHealthStatus();
      expect(healthStatusAfterClear.details.cachedResultsCount).toBe(0);
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should handle repository errors gracefully during analysis', async () => {
      // Mock repository to throw error
      mockWarehouseRepository.getOverallStats.mockRejectedValue(new Error('Database connection failed'));
      
      await expect(service.analyzeWarehouseLayout()).rejects.toThrow();
    });

    it('should handle invalid picking route requests', async () => {
      const invalidRequest = {
        orderId: '',
        items: [],
        startLocation: { x: 0, y: 0, z: 0 },
        constraints: {
          maxWeight: 0,
          maxVolume: 0
        }
      };

      const result = await service.optimizePickingRoute(invalidRequest);
      
      // Should handle empty items gracefully
      expect(result.sequence).toHaveLength(0);
      expect(result.totalDistance).toBe(0);
    });
  });

  describe('Integration with Existing Data', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should integrate with warehouse repository data', async () => {
      const analysis = await service.analyzeWarehouseLayout();
      
      // Verify that the service is using repository data
      expect(mockWarehouseRepository.getOverallStats).toHaveBeenCalled();
      
      // Analysis should reflect realistic warehouse data
      expect(analysis.totalItems).toBeGreaterThan(0);
      expect(analysis.utilizationRate).toBeGreaterThan(0);
    });

    it('should maintain consistency with existing department patterns', async () => {
      const analysis = await service.analyzeWarehouseLayout();
      
      // Verify that recommendations follow department organization
      for (const recommendation of analysis.recommendations) {
        expect(recommendation.id).toBeDefined();
        expect(['relocate_item', 'reorganize_zone', 'optimize_grouping', 'improve_accessibility'])
          .toContain(recommendation.type);
        expect(['low', 'medium', 'high', 'critical']).toContain(recommendation.priority);
      }
    });
  });
});