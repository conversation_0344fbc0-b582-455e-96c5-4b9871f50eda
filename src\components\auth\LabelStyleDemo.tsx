import React, { useState } from 'react';
import { 
  LABEL_CLASSES, 
  getLabelFormClasses, 
  getLabelInputClasses, 
  getLabelButtonClasses,
  getLabelTextClasses,
  getLabelMessageClasses,
  LABEL_ACCESSIBILITY,
  getCurrentBreakpoint,
  prefersReducedMotion,
  prefersHighContrast
} from '@/utils/labelStyles';
import '@/styles/label-auth.css';

/**
 * Demo component to showcase label authentication styling
 * This component demonstrates all the styling features and responsive behavior
 */
export const LabelStyleDemo: React.FC = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear messages when user starts typing
    if (error) setError(null);
    if (success) setSuccess(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    // Simulate API call
    setTimeout(() => {
      if (formData.username === 'demo' && formData.password === 'demo') {
        setSuccess('Login successful!');
      } else {
        setError('Invalid credentials provided');
      }
      setIsLoading(false);
    }, 1500);
  };

  const handleReset = () => {
    setFormData({ username: '', password: '' });
    setError(null);
    setSuccess(null);
    setIsLoading(false);
  };

  // Get current environment info for debugging
  const debugInfo = {
    breakpoint: getCurrentBreakpoint(),
    reducedMotion: prefersReducedMotion(),
    highContrast: prefersHighContrast()
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(45deg, #2c3e50, #34495e)',
      padding: '2rem',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{ maxWidth: '500px', width: '100%' }}>
        {/* Main Form */}
        <div className={getLabelFormClasses()}>
          <div className={LABEL_CLASSES.stack}>
            {/* Header */}
            <div className={LABEL_CLASSES.center}>
              <h1 className={getLabelTextClasses('primary')}>
                LABEL AUTH DEMO
              </h1>
            </div>
            
            <p className={getLabelTextClasses('secondary')}>
              Demonstration of label-style authentication interface
            </p>

            {/* Form */}
            <form onSubmit={handleSubmit} className={LABEL_CLASSES.stack}>
              {/* Username Field */}
              <div>
                <label 
                  htmlFor="demo-username" 
                  className={LABEL_CLASSES.formLabel}
                >
                  Username
                </label>
                <input
                  {...LABEL_ACCESSIBILITY.getFormAriaAttributes(
                    'demo-username', 
                    !!error, 
                    error ? 'demo-error' : undefined
                  )}
                  type="text"
                  className={getLabelInputClasses(LABEL_CLASSES.fullWidth)}
                  placeholder="Enter username (try 'demo')"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  disabled={isLoading}
                  style={{ boxSizing: 'border-box' }}
                />
              </div>

              {/* Password Field */}
              <div>
                <label 
                  htmlFor="demo-password" 
                  className={LABEL_CLASSES.formLabel}
                >
                  Password
                </label>
                <input
                  {...LABEL_ACCESSIBILITY.getFormAriaAttributes(
                    'demo-password', 
                    !!error, 
                    error ? 'demo-error' : undefined
                  )}
                  type="password"
                  className={getLabelInputClasses(LABEL_CLASSES.fullWidth)}
                  placeholder="Enter password (try 'demo')"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  disabled={isLoading}
                  style={{ boxSizing: 'border-box' }}
                />
              </div>

              {/* Error Message */}
              {error && (
                <div 
                  id="demo-error"
                  className={getLabelMessageClasses('error')}
                  role="alert"
                  aria-live="polite"
                >
                  {error}
                </div>
              )}

              {/* Success Message */}
              {success && (
                <div 
                  className={getLabelMessageClasses('success')}
                  role="alert"
                  aria-live="polite"
                >
                  {success}
                </div>
              )}

              {/* Buttons */}
              <div className={LABEL_CLASSES.row}>
                <button
                  {...LABEL_ACCESSIBILITY.getButtonAriaAttributes(isLoading, false)}
                  type="submit"
                  className={getLabelButtonClasses('primary', `${LABEL_CLASSES.fullWidth} ${isLoading ? LABEL_CLASSES.loading : ''}`)}
                  disabled={isLoading || !formData.username || !formData.password}
                >
                  {isLoading ? 'PROCESSING...' : 'LOGIN'}
                  <span className={LABEL_CLASSES.srOnly}>
                    {isLoading ? LABEL_ACCESSIBILITY.getLoadingText('Login') : ''}
                  </span>
                </button>
                
                <button
                  type="button"
                  className={getLabelButtonClasses('secondary', LABEL_CLASSES.fullWidth)}
                  onClick={handleReset}
                  disabled={isLoading}
                >
                  RESET
                </button>
              </div>
            </form>

            {/* Help Text */}
            <p className={getLabelTextClasses('muted')}>
              Use username: "demo" and password: "demo" to test success state
            </p>
          </div>
        </div>

        {/* Typography Showcase */}
        <div className={getLabelFormClasses()} style={{ marginTop: '2rem' }}>
          <div className={LABEL_CLASSES.stack}>
            <h2 className={getLabelTextClasses('primary')}>Typography Showcase</h2>
            
            <div className={LABEL_CLASSES.stack}>
              <h3 className={getLabelTextClasses('primary')}>Primary Text</h3>
              <p className={getLabelTextClasses('primary')}>
                This is primary text using monospace font with high contrast shadow for readability.
              </p>
              
              <h3 className={getLabelTextClasses('secondary')}>Secondary Text</h3>
              <p className={getLabelTextClasses('secondary')}>
                This is secondary text with slightly reduced contrast but still readable.
              </p>
              
              <h3 className={getLabelTextClasses('muted')}>Muted Text</h3>
              <p className={getLabelTextClasses('muted')}>
                This is muted text for less important information like help text or footnotes.
              </p>
            </div>
          </div>
        </div>

        {/* Button Showcase */}
        <div className={getLabelFormClasses()} style={{ marginTop: '2rem' }}>
          <div className={LABEL_CLASSES.stack}>
            <h2 className={getLabelTextClasses('primary')}>Button Showcase</h2>
            
            <div className={LABEL_CLASSES.row}>
              <button className={getLabelButtonClasses('primary')}>
                PRIMARY
              </button>
              <button className={getLabelButtonClasses('secondary')}>
                SECONDARY
              </button>
              <button className={getLabelButtonClasses('secondary')} disabled>
                DISABLED
              </button>
            </div>
            
            <button className={getLabelButtonClasses('primary', `${LABEL_CLASSES.fullWidth} ${LABEL_CLASSES.loading}`)}>
              LOADING STATE
            </button>
          </div>
        </div>

        {/* Debug Info */}
        {process.env.NODE_ENV === 'development' && (
          <div className={getLabelFormClasses()} style={{ marginTop: '2rem' }}>
            <div className={LABEL_CLASSES.stack}>
              <h2 className={getLabelTextClasses('primary')}>Debug Information</h2>
              <div className={LABEL_CLASSES.stack}>
                <p className={getLabelTextClasses('muted')}>
                  <strong>Breakpoint:</strong> {debugInfo.breakpoint}
                </p>
                <p className={getLabelTextClasses('muted')}>
                  <strong>Reduced Motion:</strong> {debugInfo.reducedMotion ? 'Yes' : 'No'}
                </p>
                <p className={getLabelTextClasses('muted')}>
                  <strong>High Contrast:</strong> {debugInfo.highContrast ? 'Yes' : 'No'}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LabelStyleDemo;