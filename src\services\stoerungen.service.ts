import apiService from './api.service';
import { 
  Stoerung, 
  StoerungCreateData, 
  StoerungUpdateData, 
  StoerungsStats, 
  SystemStatus, 
  SystemStatusUpdate,
  StoerungComment
} from '@/types/stoerungen.types';

class StoerungenService {
  private baseUrl = '/stoerungen';

  async getStoerungen(options?: {
    status?: string;
    severity?: string;
    category?: string;
    affected_system?: string;
    limit?: number;
    offset?: number;
  }): Promise<Stoerung[]> {
    try {
      const params = new URLSearchParams();
      if (options?.status) params.append('status', options.status);
      if (options?.severity) params.append('severity', options.severity);
      if (options?.category) params.append('category', options.category);
      if (options?.affected_system) params.append('affected_system', options.affected_system);
      if (options?.limit) params.append('limit', options.limit.toString());
      if (options?.offset) params.append('offset', options.offset.toString());

      const queryString = params.toString();
      const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;
      
      const response = await apiService.get<Stoerung[]>(url);
      
      // Process the response to ensure proper data format
      if (Array.isArray(response)) {
        const processed = response.map(stoerung => ({
          ...stoerung,
          // Ensure tags is an array
          tags: typeof stoerung.tags === 'string' 
            ? stoerung.tags.split(' ').filter(Boolean)
            : Array.isArray(stoerung.tags) 
              ? stoerung.tags 
              : []
        }));
        return processed;
      }
      
      console.warn('StoerungenService - Response is not an array:', response);
      return [];
    } catch (error) {
      console.error('Error fetching störungen:', error);
      // Re-throw authentication errors to allow proper handling
      if (error instanceof Error && error.message.includes('Sitzung ist abgelaufen')) {
        throw error;
      }
      // Return empty array instead of throwing to prevent component crashes
      return [];
    }
  }

  async getStoerungById(id: number): Promise<Stoerung> {
    try {
      return await apiService.get<Stoerung>(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Error fetching störung ${id}:`, error);
      throw error;
    }
  }

  async createStoerung(data: StoerungCreateData): Promise<Stoerung> {
    try {
      return await apiService.post<Stoerung>(this.baseUrl, data);
    } catch (error) {
      console.error('Error creating störung:', error);
      throw error;
    }
  }

  async updateStoerung(id: number, data: StoerungUpdateData): Promise<Stoerung> {
    try {
      return await apiService.put<Stoerung>(`${this.baseUrl}/${id}`, data);
    } catch (error) {
      console.error(`Error updating störung ${id}:`, error);
      throw error;
    }
  }

  async deleteStoerung(id: number): Promise<void> {
    try {
      await apiService.delete(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error(`Error deleting störung ${id}:`, error);
      throw error;
    }
  }

  async addComment(data: { stoerung_id: number; user_id?: string; comment: string }): Promise<StoerungComment> {
    try {
      return await apiService.post<StoerungComment>(`${this.baseUrl}/comments`, data);
    } catch (error) {
      console.error('Error adding comment:', error);
      throw error;
    }
  }

  async getStoerungsStats(): Promise<StoerungsStats> {
    try {
      return await apiService.get<StoerungsStats>(`${this.baseUrl}/stats`);
    } catch (error) {
      console.error('Error fetching störungen stats:', error);
      throw error;
    }
  }

  async getActiveStoerungen(): Promise<Stoerung[]> {
    try {
      const response = await apiService.get<Stoerung[]>(`${this.baseUrl}/active`);
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching active störungen:', error);
      // Re-throw authentication errors to allow proper handling
      if (error instanceof Error && error.message.includes('Sitzung ist abgelaufen')) {
        throw error;
      }
      return [];
    }
  }

  async getSystemStatus(): Promise<SystemStatus[]> {
    try {
      const response = await apiService.get<SystemStatus[]>(`${this.baseUrl}/system/status`);
      // Ensure we return an array even if the API returns an object
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Error fetching system status:', error);
      // Re-throw authentication errors to allow proper handling
      if (error instanceof Error && error.message.includes('Sitzung ist abgelaufen')) {
        throw error;
      }
      // Return empty array instead of throwing to prevent component crashes
      return [];
    }
  }

  async updateSystemStatus(data: SystemStatusUpdate): Promise<SystemStatus> {
    try {
      return await apiService.post<SystemStatus>(`${this.baseUrl}/system/status`, data);
    } catch (error) {
      console.error('Error updating system status:', error);
      throw error;
    }
  }

  // Helper methods for filtering and searching
  async searchStoerungen(query: string): Promise<Stoerung[]> {
    try {
      const allStoerungen = await this.getStoerungen();
      return allStoerungen.filter(stoerung => 
        stoerung.title.toLowerCase().includes(query.toLowerCase()) ||
        stoerung.description?.toLowerCase().includes(query.toLowerCase()) ||
        stoerung.affected_system?.toLowerCase().includes(query.toLowerCase())
      );
    } catch (error) {
      console.error('Error searching störungen:', error);
      return [];
    }
  }

  async getStoerungsByStatus(status: 'NEW' | 'IN_PROGRESS' | 'RESOLVED'): Promise<Stoerung[]> {
    return this.getStoerungen({ status });
  }

  async getStoerungsBySeverity(severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'): Promise<Stoerung[]> {
    return this.getStoerungen({ severity });
  }

  async getCriticalStoerungen(): Promise<Stoerung[]> {
    return this.getStoerungsBySeverity('CRITICAL');
  }

  // Utility method for formatting MTTR
  formatMTTR(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} Min`;
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    } else {
      const days = Math.floor(minutes / 1440);
      const remainingHours = Math.floor((minutes % 1440) / 60);
      return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;
    }
  }

  // Utility method for getting severity display info
  getSeverityInfo(severity: string) {
    const severityMap = {
      LOW: { label: 'Niedrig', color: 'text-green-600', bgColor: 'bg-green-100' },
      MEDIUM: { label: 'Mittel', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
      HIGH: { label: 'Hoch', color: 'text-orange-600', bgColor: 'bg-orange-100' },
      CRITICAL: { label: 'Kritisch', color: 'text-red-600', bgColor: 'bg-red-100' },
    };
    return severityMap[severity as keyof typeof severityMap] || severityMap.LOW;
  }

  // Utility method for getting status display info
  getStatusInfo(status: string) {
    const statusMap = {
      NEW: { label: 'Neu', color: 'text-blue-600', bgColor: 'bg-blue-100' },
      IN_PROGRESS: { label: 'In Bearbeitung', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
      RESOLVED: { label: 'Gelöst', color: 'text-green-600', bgColor: 'bg-green-100' },
    };
    return statusMap[status as keyof typeof statusMap] || statusMap.NEW;
  }
}

export default new StoerungenService();