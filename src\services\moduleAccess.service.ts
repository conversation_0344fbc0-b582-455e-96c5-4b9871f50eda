import { ModuleConfig, ModuleCard, UserRole } from '@/types/module';
// Statische Bild-ESM-Imports (Vite -> URL), Electron-sicher
import imgDashboard from '@/assets/Dashboard.png';
import imgError from '@/assets/Error.png';
import imgDatabase from '@/assets/Database.png';
import imgBot from '@/assets/Bot.png';
import imgSettings from '@/assets/Settings.png';
import imgUser from '@/assets/User.png';

/**
 * Service for managing module access based on user roles
 */
export class ModuleAccessService {
  /**
   * Defines the available modules with their configurations
   */
  private static readonly MODULE_CONFIGS: ModuleConfig[] = [
    {
      id: 'dashboard',
      name: 'dashboard',
      displayName: 'Dashboard',
      description: 'Dashboard und KPI-Übersicht für alle Bereiche',
      icon: 'LayoutDashboard',
      baseRoute: '/modules/dashboard',
      requiredRoles: ['Besucher', 'Benutzer', 'Administrator'],
      isEnabled: true,
      pages: [
        { id: 'home', name: 'Dashboard', route: '/modules/dashboard', component: 'DashboardPage' },
        { id: 'dispatch', name: 'Versand', route: '/modules/dashboard/dispatch', component: 'DispatchPage' },
        { id: 'cutting', name: 'Ablängerei', route: '/modules/dashboard/cutting', component: 'CuttingPage' },
        { id: 'incoming-goods', name: 'Wareneingang', route: '/modules/dashboard/incoming-goods', component: 'IncomingGoodsPage' },
        { id: 'CSR', name: 'Costumer Service Rate', route: '/modules/dashboard/CSR', component: 'CSRPage' },
        { id: 'aril', name: 'ARiL', route: '/modules/dashboard/aril', component: 'ArilPage' },
        { id: 'atrl', name: 'ATrL', route: '/modules/dashboard/atrl', component: 'AtrlPage' },
        { id: 'machines', name: 'Maschinen', route: '/modules/dashboard/machines', component: 'MachinesPage' }
      ]
    },
    {
      id: 'stoerungen',
      name: 'stoerungen',
      displayName: 'Störungen',
      description: 'Störungsmanagement und Monitoring',
      icon: 'AlertTriangle',
      baseRoute: '/modules/stoerungen',
      requiredRoles: ['Benutzer', 'Administrator'],
      isEnabled: true,
      pages: [
        { id: 'overview', name: 'Störungsübersicht', route: '/modules/stoerungen', component: 'StoerungenPage' }
      ]
    },
    {
      id: 'backend',
      name: 'backend',
      displayName: 'Backend & Automatisierung',
      description: 'Systemverwaltung und Workflow-Automatisierung',
      icon: 'Database',
      baseRoute: '/modules/backend',
      requiredRoles: ['Administrator'],
      isEnabled: true,
      pages: [
        { id: 'system', name: 'System', route: '/modules/backend/system', component: 'SystemPage' },
        { id: 'workflows', name: 'Workflows', route: '/modules/backend/workflows', component: 'WorkflowPage' }
      ]
    },
    {
      id: 'ai',
      name: 'ai',
      displayName: 'AI',
      description: 'KI-gestützte Analyse und Chat-Funktionen',
      icon: 'Bot',
      baseRoute: '/modules/ai',
      requiredRoles: ['Besucher', 'Benutzer', 'Administrator'],
      isEnabled: true,
      pages: []
    },
    {
      id: 'settings',
      name: 'settings',
      displayName: 'App-Settings',
      description: 'Anwendungseinstellungen und Konfiguration',
      icon: 'Settings',
      baseRoute: '/modules/settings',
      requiredRoles: ['Besucher', 'Benutzer', 'Administrator'],
      isEnabled: true,
      pages: [
        { id: 'general', name: 'Allgemein', route: '/modules/settings', component: 'SettingsPage' }
      ]
    },
    {
      id: 'user',
      name: 'user',
      displayName: 'User-Settings',
      description: 'Benutzerverwaltung und Benutzereinstellungen',
      icon: 'User',
      baseRoute: '/modules/user',
      requiredRoles: ['Besucher', 'Benutzer', 'Administrator'],
      isEnabled: true,
      pages: [
        { id: 'profile', name: 'Profil', route: '/modules/user', component: 'UserProfilePage' }
      ]
    }
  ];

  /**
   * Gets all available modules filtered by user role
   * @param userRole The role of the current user
   * @returns Array of modules accessible to the user
   */
  static getAvailableModules(userRole: UserRole): ModuleConfig[] {
    return this.MODULE_CONFIGS.filter(module => 
      module.isEnabled && module.requiredRoles.includes(userRole)
    );
  }
  
  /**
   * Gets all enabled modules regardless of user role
   * @returns Array of all enabled module configurations
   */
  static getAllEnabledModules(): ModuleConfig[] {
    return this.MODULE_CONFIGS.filter(module => module.isEnabled);
  }

  /**
   * Converts module configs to module cards for UI display
   * @param userRole The role of the current user
   * @returns Array of module cards for the user
   */
  static getModuleCards(userRole: UserRole): ModuleCard[] {
    const availableModules = this.getAvailableModules(userRole);
    
    return availableModules.map(module => ({
      id: module.id,
      title: module.displayName,
      description: module.description,
      icon: module.icon,
      route: module.baseRoute,
      requiredRoles: module.requiredRoles,
      isAvailable: true
    }));
  }
  
  /**
   * Gibt alle aktivierten Module als Karten zurück, unabhängig von der Benutzerrolle
   * @returns Array aller aktivierten Module als Karten
   */
  static getAllModuleCards(): ModuleCard[] {
    const allEnabledModules = this.getAllEnabledModules();
    
    return allEnabledModules.map(module => ({
      id: module.id,
      title: module.displayName,
      description: module.description,
      icon: module.icon,
      route: module.baseRoute,
      requiredRoles: module.requiredRoles,
      isAvailable: true
    }));
  }

  /**
   * Checks if a user can access a specific module
   * @param moduleId The ID of the module to check
   * @param userRole The role of the current user
   * @returns true if the user can access the module
   */
  static canAccessModule(moduleId: string, userRole: UserRole): boolean {
    const module = this.MODULE_CONFIGS.find(m => m.id === moduleId);
    return module ? module.requiredRoles.includes(userRole) : false;
  }

  /**
   * Gets a specific module configuration by ID
   * @param moduleId The ID of the module
   * @returns Module configuration or undefined if not found
   */
  static getModuleConfig(moduleId: string): ModuleConfig | undefined {
    return this.MODULE_CONFIGS.find(m => m.id === moduleId);
  }

  /**
   * Gets the image path for a module based on its icon type
   * @param iconType The icon type from module config
   * @returns The image path for the module
   */
  static getModuleImagePath(iconType: string): string {
    const imageMap: Record<string, string> = {
      'LayoutDashboard': imgDashboard,
      'AlertTriangle': imgError,
      'Database': imgDatabase,
      'Bot': imgBot,
      'Settings': imgSettings,
      'User': imgUser,
    };
    return imageMap[iconType] || imgDashboard;
  }
}