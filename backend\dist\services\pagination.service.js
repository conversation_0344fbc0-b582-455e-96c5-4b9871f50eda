"use strict";
/**
 * Pagination Service
 *
 * Implementiert cursor-basierte und offset-basierte Paginierung
 * für große Datasets mit Performance-Optimierungen.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.repositoryPaginationHelper = exports.paginationService = exports.RepositoryPaginationHelper = exports.PaginationService = exports.SortDirection = exports.PaginationStrategy = void 0;
/**
 * Pagination-Strategien
 */
var PaginationStrategy;
(function (PaginationStrategy) {
    PaginationStrategy["CURSOR"] = "cursor";
    PaginationStrategy["OFFSET"] = "offset";
})(PaginationStrategy || (exports.PaginationStrategy = PaginationStrategy = {}));
/**
 * Sortierungs-Richtungen
 */
var SortDirection;
(function (SortDirection) {
    SortDirection["ASC"] = "asc";
    SortDirection["DESC"] = "desc";
})(SortDirection || (exports.SortDirection = SortDirection = {}));
/**
 * Pagination Service
 */
class PaginationService {
    constructor() {
        this.DEFAULT_LIMIT = 50;
        this.MAX_LIMIT = 1000;
        this.DEFAULT_ORDER_BY = 'id';
    }
    static getInstance() {
        if (!PaginationService.instance) {
            PaginationService.instance = new PaginationService();
        }
        return PaginationService.instance;
    }
    /**
     * Cursor-basierte Paginierung implementieren
     */
    async applyCursorPagination(query, // Prisma-Query-Builder
    params, defaultOrderBy = this.DEFAULT_ORDER_BY) {
        const orderBy = params.orderBy || defaultOrderBy;
        const orderDirection = params.orderDirection || SortDirection.ASC;
        // Validiere und normalisiere Parameter
        const first = this.validateLimit(params.first);
        const last = this.validateLimit(params.last);
        if (first && last) {
            throw new Error('Cannot specify both "first" and "last" parameters');
        }
        if (params.after && params.before) {
            throw new Error('Cannot specify both "after" and "before" cursors');
        }
        let limit = first || last || this.DEFAULT_LIMIT;
        const isReversed = !!last;
        // Cursor dekodieren
        let cursorCondition = {};
        if (params.after) {
            const cursor = this.decodeCursor(params.after);
            cursorCondition = this.buildCursorCondition(cursor, false, orderDirection);
        }
        else if (params.before) {
            const cursor = this.decodeCursor(params.before);
            cursorCondition = this.buildCursorCondition(cursor, true, orderDirection);
        }
        // Query konfigurieren
        const queryConfig = {
            where: {
                ...query.where,
                ...cursorCondition
            },
            orderBy: {
                [orderBy]: isReversed ? this.reverseDirection(orderDirection) : orderDirection,
                // Sekundäre Sortierung nach ID für Eindeutigkeit
                ...(orderBy !== 'id' && { id: orderDirection })
            },
            take: limit + 1 // +1 um hasNextPage zu bestimmen
        };
        // Query ausführen
        const results = await query.findMany(queryConfig);
        // Prüfe ob mehr Daten verfügbar sind
        const hasMore = results.length > limit;
        if (hasMore) {
            results.pop(); // Entferne den extra Datensatz
        }
        // Bei Rückwärts-Paginierung die Reihenfolge umkehren
        if (isReversed) {
            results.reverse();
        }
        // Edges und Cursors erstellen
        const edges = results.map((node) => ({
            cursor: this.encodeCursor(orderBy, node[orderBy], node.id, orderDirection),
            node
        }));
        // PageInfo erstellen
        const pageInfo = {
            hasNextPage: isReversed ? false : hasMore,
            hasPreviousPage: isReversed ? hasMore : false,
            startCursor: edges.length > 0 ? edges[0].cursor : undefined,
            endCursor: edges.length > 0 ? edges[edges.length - 1].cursor : undefined
        };
        // Bei Bedarf auch Rückwärts-Navigation prüfen
        if (!isReversed && params.after) {
            // Prüfe ob vorherige Seite existiert
            const cursor = this.decodeCursor(params.after);
            const previousCondition = this.buildCursorCondition(cursor, true, orderDirection);
            const previousQuery = {
                ...query,
                where: {
                    ...query.where,
                    ...previousCondition
                },
                take: 1
            };
            const previousExists = await query.findFirst(previousQuery);
            pageInfo.hasPreviousPage = !!previousExists;
        }
        return {
            edges,
            pageInfo
        };
    }
    /**
     * Offset-basierte Paginierung implementieren
     */
    async applyOffsetPagination(query, // Prisma-Query-Builder
    params, defaultOrderBy = this.DEFAULT_ORDER_BY) {
        const orderBy = params.orderBy || defaultOrderBy;
        const orderDirection = params.orderDirection || SortDirection.ASC;
        const limit = this.validateLimit(params.limit);
        // Berechne Offset
        let offset = 0;
        if (params.offset !== undefined) {
            offset = Math.max(0, params.offset);
        }
        else if (params.page !== undefined) {
            const page = Math.max(1, params.page);
            offset = (page - 1) * limit;
        }
        // Query konfigurieren
        const queryConfig = {
            where: query.where,
            orderBy: {
                [orderBy]: orderDirection,
                // Sekundäre Sortierung nach ID für Konsistenz
                ...(orderBy !== 'id' && { id: orderDirection })
            },
            skip: offset,
            take: limit
        };
        // Parallel: Daten und Gesamtanzahl abrufen
        const [data, totalCount] = await Promise.all([
            query.findMany(queryConfig),
            query.count({ where: query.where })
        ]);
        // Pagination-Metadaten berechnen
        const totalPages = Math.ceil(totalCount / limit);
        const currentPage = Math.floor(offset / limit) + 1;
        const meta = {
            currentPage,
            totalPages,
            totalCount,
            hasNextPage: currentPage < totalPages,
            hasPreviousPage: currentPage > 1,
            limit,
            offset
        };
        return {
            data,
            meta
        };
    }
    /**
     * Intelligente Pagination-Strategie-Auswahl
     */
    async applyPagination(query, params, defaultOrderBy = this.DEFAULT_ORDER_BY) {
        switch (params.strategy) {
            case PaginationStrategy.CURSOR:
                if (!params.cursor) {
                    throw new Error('Cursor parameters required for cursor pagination');
                }
                return this.applyCursorPagination(query, params.cursor, defaultOrderBy);
            case PaginationStrategy.OFFSET:
                if (!params.offset) {
                    throw new Error('Offset parameters required for offset pagination');
                }
                return this.applyOffsetPagination(query, params.offset, defaultOrderBy);
            default:
                throw new Error(`Unknown pagination strategy: ${params.strategy}`);
        }
    }
    /**
     * Cursor-Funktionen
     */
    /**
     * Cursor kodieren
     */
    encodeCursor(field, value, id, direction) {
        const cursorInfo = {
            field,
            value,
            id,
            direction
        };
        const encoded = Buffer.from(JSON.stringify(cursorInfo)).toString('base64');
        return encoded;
    }
    /**
     * Cursor dekodieren
     */
    decodeCursor(cursor) {
        try {
            const decoded = Buffer.from(cursor, 'base64').toString('utf-8');
            const cursorInfo = JSON.parse(decoded);
            if (!cursorInfo.field || cursorInfo.value === undefined || !cursorInfo.id) {
                throw new Error('Invalid cursor format');
            }
            return cursorInfo;
        }
        catch (error) {
            throw new Error('Invalid cursor format');
        }
    }
    /**
     * Cursor-Condition für Prisma-Query erstellen
     */
    buildCursorCondition(cursor, isBefore, orderDirection) {
        const { field, value, id } = cursor;
        // Bestimme Vergleichsoperator basierend auf Richtung
        const isAscending = orderDirection === SortDirection.ASC;
        const operator = isBefore
            ? (isAscending ? 'lt' : 'gt')
            : (isAscending ? 'gt' : 'lt');
        if (field === 'id') {
            return {
                id: {
                    [operator]: id
                }
            };
        }
        // Für Nicht-ID-Felder: Kombiniere Feld-Wert und ID für eindeutige Sortierung
        return {
            OR: [
                {
                    [field]: {
                        [operator]: value
                    }
                },
                {
                    [field]: value,
                    id: {
                        [operator]: id
                    }
                }
            ]
        };
    }
    /**
     * Hilfsfunktionen
     */
    validateLimit(limit) {
        if (limit === undefined) {
            return this.DEFAULT_LIMIT;
        }
        if (limit < 1) {
            throw new Error('Limit must be positive');
        }
        if (limit > this.MAX_LIMIT) {
            throw new Error(`Limit cannot exceed ${this.MAX_LIMIT}`);
        }
        return limit;
    }
    reverseDirection(direction) {
        return direction === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC;
    }
    /**
     * Utility-Funktionen für API-Handler
     */
    /**
     * Parse Cursor-Parameter aus Request-Query
     */
    parseCursorParams(query) {
        return {
            first: query.first ? parseInt(query.first, 10) : undefined,
            after: query.after || undefined,
            last: query.last ? parseInt(query.last, 10) : undefined,
            before: query.before || undefined,
            orderBy: query.orderBy || undefined,
            orderDirection: query.orderDirection === 'desc' ? SortDirection.DESC : SortDirection.ASC
        };
    }
    /**
     * Parse Offset-Parameter aus Request-Query
     */
    parseOffsetParams(query) {
        return {
            page: query.page ? parseInt(query.page, 10) : undefined,
            limit: query.limit ? parseInt(query.limit, 10) : undefined,
            offset: query.offset ? parseInt(query.offset, 10) : undefined,
            orderBy: query.orderBy || undefined,
            orderDirection: query.orderDirection === 'desc' ? SortDirection.DESC : SortDirection.ASC
        };
    }
    /**
     * Automatische Strategie-Erkennung basierend auf Request-Parametern
     */
    detectPaginationStrategy(query) {
        const hasCursorParams = !!(query.first || query.last || query.after || query.before);
        const hasOffsetParams = !!(query.page || query.offset || query.limit);
        if (hasCursorParams && hasOffsetParams) {
            throw new Error('Cannot mix cursor and offset pagination parameters');
        }
        if (hasCursorParams) {
            return PaginationStrategy.CURSOR;
        }
        // Default zu Offset-Pagination
        return PaginationStrategy.OFFSET;
    }
    /**
     * Vollständige Parameter-Parsing aus Request
     */
    parseRequestParams(query) {
        const strategy = this.detectPaginationStrategy(query);
        return {
            strategy,
            cursor: strategy === PaginationStrategy.CURSOR ? this.parseCursorParams(query) : undefined,
            offset: strategy === PaginationStrategy.OFFSET ? this.parseOffsetParams(query) : undefined
        };
    }
    /**
     * Performance-Empfehlungen für große Datasets
     */
    getPerformanceRecommendations(totalCount, currentLimit) {
        const recommendations = [];
        if (totalCount > 10000 && currentLimit > 100) {
            recommendations.push('Verwenden Sie kleinere Seitengrößen für bessere Performance bei großen Datasets');
        }
        if (totalCount > 100000) {
            recommendations.push('Erwägen Sie Cursor-Pagination für sehr große Datasets');
            recommendations.push('Implementieren Sie Filteroptionen um die Datenmenge zu reduzieren');
        }
        if (currentLimit > 500) {
            recommendations.push('Limit über 500 kann zu Performance-Problemen führen');
        }
        return recommendations;
    }
}
exports.PaginationService = PaginationService;
/**
 * Pagination-Helper für Repository-Integration
 */
class RepositoryPaginationHelper {
    constructor() {
        this.paginationService = PaginationService.getInstance();
    }
    /**
     * Erweitere Repository findAll-Methode mit Pagination
     */
    async paginatedFindAll(prismaModel, baseWhere = {}, paginationParams, defaultOrderBy = 'id') {
        const query = {
            where: baseWhere,
            findMany: (config) => prismaModel.findMany(config),
            findFirst: (config) => prismaModel.findFirst(config),
            count: (config) => prismaModel.count(config)
        };
        return this.paginationService.applyPagination(query, paginationParams, defaultOrderBy);
    }
    /**
     * Cache-Key für paginierte Queries
     */
    generatePaginationCacheKey(baseKey, paginationParams) {
        const strategy = paginationParams.strategy;
        let paramString = '';
        if (strategy === PaginationStrategy.CURSOR && paginationParams.cursor) {
            const { first, after, last, before, orderBy, orderDirection } = paginationParams.cursor;
            paramString = `cursor:${first || ''}:${after || ''}:${last || ''}:${before || ''}:${orderBy || ''}:${orderDirection || ''}`;
        }
        else if (strategy === PaginationStrategy.OFFSET && paginationParams.offset) {
            const { page, limit, offset, orderBy, orderDirection } = paginationParams.offset;
            paramString = `offset:${page || ''}:${limit || ''}:${offset || ''}:${orderBy || ''}:${orderDirection || ''}`;
        }
        return `${baseKey}:paginated:${paramString}`;
    }
}
exports.RepositoryPaginationHelper = RepositoryPaginationHelper;
// Singleton instances
exports.paginationService = PaginationService.getInstance();
exports.repositoryPaginationHelper = new RepositoryPaginationHelper();
