/**
 * Supply Chain Analytics Page
 * 
 * Main page for supply chain analytics and monitoring functionality.
 * Integrates the SupplyChainAnalyticsService with the dashboard UI.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  RefreshCw, 
  Settings, 
  Download,
  TrendingUp,
  BarChart3,
  Users,
  Truck
} from 'lucide-react';
import { SupplyChainAnalyticsDashboard } from '../components/supply-chain/SupplyChainAnalyticsDashboard';
import { SupplyChainAnalyticsService } from '../services/supply-chain/SupplyChainAnalyticsService';
import type { 
  SupplyChainAnalytics, 
  SupplyChainAlert,
  SupplyChainRecommendation 
} from '@/types/supply-chain-optimization';

/**
 * Supply Chain Analytics Page Component
 */
export const SupplyChainAnalyticsPage: React.FC = () => {
  // State management
  const [analytics, setAnalytics] = useState<SupplyChainAnalytics | null>(null);
  const [alerts, setAlerts] = useState<SupplyChainAlert[]>([]);
  const [recommendations, setRecommendations] = useState<SupplyChainRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [analyticsService, setAnalyticsService] = useState<SupplyChainAnalyticsService | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  /**
   * Initialize the analytics service
   */
  const initializeService = useCallback(async () => {
    try {
      const service = new SupplyChainAnalyticsService({
        monitoringInterval: 15, // 15 minutes
        alertThresholds: {
          riskScore: 7,
          onTimeRate: 0.85,
          qualityRate: 0.95,
          deliveryDelay: 2
        },
        trendAnalysisPeriod: 90,
        performanceWeights: {
          onTimeDelivery: 0.4,
          quality: 0.3,
          cost: 0.2,
          reliability: 0.1
        }
      });

      await service.initialize();
      setAnalyticsService(service);
      return service;
    } catch (err) {
      console.error('Failed to initialize SupplyChainAnalyticsService:', err);
      setError('Fehler beim Initialisieren des Analytics-Service');
      throw err;
    }
  }, []);

  /**
   * Load analytics data
   */
  const loadAnalytics = useCallback(async (service?: SupplyChainAnalyticsService) => {
    try {
      setLoading(true);
      setError(null);

      const activeService = service || analyticsService;
      if (!activeService) {
        throw new Error('Analytics service not initialized');
      }

      // Load analytics data
      const analyticsData = await activeService.getSupplyChainAnalytics({ days: 30 });
      setAnalytics(analyticsData);

      // Generate and load alerts
      const alertsData = await activeService.generateAlerts();
      setAlerts(alertsData);

      // Generate and load recommendations
      const recommendationsData = await activeService.generateRecommendations();
      setRecommendations(recommendationsData);

      setLastUpdated(new Date());
    } catch (err) {
      console.error('Failed to load analytics:', err);
      setError('Fehler beim Laden der Analytics-Daten');
    } finally {
      setLoading(false);
    }
  }, [analyticsService]);

  /**
   * Handle refresh
   */
  const handleRefresh = useCallback(async () => {
    await loadAnalytics();
  }, [loadAnalytics]);

  /**
   * Handle alert acknowledgment
   */
  const handleAcknowledgeAlert = useCallback((alertId: string) => {
    if (analyticsService) {
      const success = analyticsService.acknowledgeAlert(alertId);
      if (success) {
        setAlerts(prev => prev.map(alert => 
          alert.alertId === alertId 
            ? { ...alert, acknowledged: true }
            : alert
        ));
      }
    }
  }, [analyticsService]);

  /**
   * Setup auto-refresh
   */
  const setupAutoRefresh = useCallback(() => {
    if (refreshInterval) {
      clearInterval(refreshInterval);
    }

    if (autoRefresh) {
      const interval = setInterval(() => {
        loadAnalytics();
      }, 5 * 60 * 1000); // 5 minutes

      setRefreshInterval(interval);
    }
  }, [autoRefresh, loadAnalytics]);

  /**
   * Export analytics data
   */
  const handleExportData = useCallback(() => {
    if (!analytics) return;

    const exportData = {
      analytics,
      alerts: alerts.filter(a => !a.acknowledged),
      recommendations,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `supply-chain-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [analytics, alerts, recommendations]);

  // Initialize service on mount
  useEffect(() => {
    const init = async () => {
      try {
        const service = await initializeService();
        await loadAnalytics(service);
      } catch (err) {
        // Error already handled in initializeService
      }
    };

    init();

    // Cleanup on unmount
    return () => {
      if (analyticsService) {
        analyticsService.destroy();
      }
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, []);

  // Setup auto-refresh when autoRefresh changes
  useEffect(() => {
    setupAutoRefresh();
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [setupAutoRefresh]);

  // Error state
  if (error && !analytics) {
    return (
      <div className="w-full bg-bg min-h-screen p-8">
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Button onClick={handleRefresh} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Erneut versuchen
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-bg min-h-screen p-8 space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Supply Chain Analytics</h1>
          <p className="text-gray-600 mt-1">
            KI-gestützte Analyse und Überwachung der Lieferkette
          </p>
          {lastUpdated && (
            <p className="text-sm text-gray-500 mt-1">
              Letzte Aktualisierung: {lastUpdated.toLocaleString('de-DE')}
            </p>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto-Refresh {autoRefresh ? 'An' : 'Aus'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportData}
            disabled={!analytics}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Aktualisieren
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Aktive Warnungen</p>
                  <p className="text-2xl font-bold text-red-600">
                    {alerts.filter(a => !a.acknowledged).length}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Empfehlungen</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {recommendations.length}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Lieferanten</p>
                  <p className="text-2xl font-bold text-green-600">
                    {analytics.supplierPerformance.length}
                  </p>
                </div>
                <Users className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Performance Score</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {(analytics.overallPerformance.onTimeDeliveryRate * 100).toFixed(0)}%
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Error Alert */}
      {error && analytics && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error} - Zeige zuletzt geladene Daten an.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Dashboard */}
      <SupplyChainAnalyticsDashboard
        analytics={analytics}
        alerts={alerts}
        recommendations={recommendations}
        loading={loading}
        onRefresh={handleRefresh}
        onAcknowledgeAlert={handleAcknowledgeAlert}
      />

      {/* Service Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Service Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-600">Analytics Service</p>
              <Badge variant={analyticsService ? "default" : "destructive"}>
                {analyticsService ? "Aktiv" : "Inaktiv"}
              </Badge>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-600">Auto-Refresh</p>
              <Badge variant={autoRefresh ? "default" : "secondary"}>
                {autoRefresh ? "Aktiviert" : "Deaktiviert"}
              </Badge>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-600">Letzte Analyse</p>
              <p className="text-sm text-gray-600">
                {lastUpdated ? lastUpdated.toLocaleTimeString('de-DE') : 'Nie'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SupplyChainAnalyticsPage;