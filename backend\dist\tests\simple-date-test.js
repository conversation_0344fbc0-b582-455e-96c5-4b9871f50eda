"use strict";
/**
 * Einfacher Test für die Datumsverarbeitung
 */
Object.defineProperty(exports, "__esModule", { value: true });
const data_enrichment_service_1 = require("../services/data-enrichment.service");
const client_1 = require("@prisma/client");
async function testDateParsing() {
    console.log('🧪 Testing Date Parsing Functionality...\n');
    const prisma = new client_1.PrismaClient();
    const service = new data_enrichment_service_1.DataEnrichmentService(prisma);
    const testCases = [
        'Servicegrad vom 17.04.2025',
        'Show data from 2025-04-17',
        'Störungen am 17/04/2025',
        'Daten für April 2025',
        'von 15.04.2025 bis 20.04.2025',
        'heute',
        'letzte 7 tage'
    ];
    for (const testCase of testCases) {
        console.log(`Testing: "${testCase}"`);
        try {
            const timeRange = service.parseTimeRange(testCase);
            if (timeRange) {
                console.log(`  ✅ Detected time range: ${timeRange.startDate} to ${timeRange.endDate}`);
            }
            else {
                console.log(`  ❌ No time range detected`);
            }
            const intents = service.parseIntent(testCase);
            console.log(`  📋 Intents: ${intents.map(i => `${i.type} (${i.confidence.toFixed(2)})`).join(', ')}`);
        }
        catch (error) {
            console.log(`  💥 Error: ${error}`);
        }
        console.log('');
    }
    await prisma.$disconnect();
    console.log('✅ Date parsing test completed!');
}
// Run the test
testDateParsing().catch(console.error);
