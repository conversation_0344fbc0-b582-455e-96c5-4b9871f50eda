"use strict";
/**
 * Cutting Data Integration Service
 *
 * Specialized service for querying and formatting Cutting (Ablängerei) data
 * for AI chatbot integration. Provides machine efficiency data, cutting performance
 * metrics, warehouse cuts data, and top performing machines with time-based filtering.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CuttingDataIntegrationService = void 0;
const cutting_repository_1 = require("../repositories/cutting.repository");
class CuttingDataIntegrationService {
    constructor(prisma) {
        this.cuttingRepo = new cutting_repository_1.CuttingRepositoryImpl(prisma);
    }
    /**
     * Get comprehensive Cutting data for AI chatbot context
     */
    async getCuttingDataForAI(options = {}) {
        const startTime = Date.now();
        try {
            console.log('✂️ [CUTTING-INTEGRATION] Fetching comprehensive Cutting data');
            // Execute parallel queries for better performance
            const [cuttingData, lagerCutsData, machineEfficiency, performanceOverview, topMachines, machineUtilization, warehouseCuts, trends] = await Promise.all([
                this.getCuttingChartData(options.timeRange),
                this.getLagerCutsData(options.timeRange),
                options.includeMachineEfficiency !== false ? this.getMachineEfficiencyData(options.timeRange) : Promise.resolve([]),
                this.getPerformanceOverview(options.timeRange),
                options.includeTopMachines !== false ? this.getTopMachines(options.maxDataPoints || 5) : Promise.resolve([]),
                options.includeMachineUtilization !== false ? this.getMachineUtilizationData() : Promise.resolve([]),
                options.includeWarehouseCuts !== false ? this.getWarehouseCutsAnalysis(options.timeRange) : Promise.resolve(this.getEmptyWarehouseCuts()),
                options.includeTrends !== false ? this.getCuttingTrends(options.timeRange) : Promise.resolve(this.getEmptyTrends())
            ]);
            // Generate comprehensive summary
            const summary = this.formatCuttingSummary(cuttingData, machineEfficiency, performanceOverview, topMachines, warehouseCuts, trends);
            const result = {
                cuttingData,
                lagerCutsData,
                machineEfficiency,
                performanceOverview,
                topMachines,
                machineUtilization,
                warehouseCuts,
                trends,
                summary,
                timestamp: new Date()
            };
            console.log(`✅ [CUTTING-INTEGRATION] Data fetched in ${Date.now() - startTime}ms`);
            return result;
        }
        catch (error) {
            console.error('❌ [CUTTING-INTEGRATION] Error fetching Cutting data:', error);
            throw new Error(`Failed to fetch Cutting data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Get cutting chart data with optional time filtering
     */
    async getCuttingChartData(timeRange) {
        try {
            const data = await this.cuttingRepo.getCuttingChartData(timeRange);
            // Sort by date and ensure data quality
            return data
                .filter(item => item.cutTT > 0 || item.cutTR > 0 || item.cutRR > 0 || item.pickCut > 0) // Filter out empty records
                .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        }
        catch (error) {
            console.error('❌ [CUTTING-INTEGRATION] Error fetching cutting chart data:', error);
            throw error;
        }
    }
    /**
     * Get warehouse cuts data
     */
    async getLagerCutsData(timeRange) {
        try {
            const data = await this.cuttingRepo.getLagerCutsChartData(timeRange);
            return data.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        }
        catch (error) {
            console.error('❌ [CUTTING-INTEGRATION] Error fetching lager cuts data:', error);
            throw error;
        }
    }
    /**
     * Get machine efficiency data with performance indicators
     */
    async getMachineEfficiencyData(timeRange) {
        try {
            const data = await this.cuttingRepo.getMaschinenEfficiency(timeRange);
            // Sort by efficiency and ensure data quality
            return data
                .filter(item => item.tagesSchnitte > 0) // Filter out inactive machines
                .sort((a, b) => b.effizienzProzent - a.effizienzProzent); // Sort by efficiency descending
        }
        catch (error) {
            console.error('❌ [CUTTING-INTEGRATION] Error fetching machine efficiency data:', error);
            throw error;
        }
    }
    /**
     * Get performance overview for the specified time range
     */
    async getPerformanceOverview(timeRange) {
        try {
            return await this.cuttingRepo.getCuttingPerformanceOverview(timeRange);
        }
        catch (error) {
            console.error('❌ [CUTTING-INTEGRATION] Error fetching performance overview:', error);
            throw error;
        }
    }
    /**
     * Get top performing machines
     */
    async getTopMachines(limit = 5) {
        try {
            return await this.cuttingRepo.getTopPerformingMachines(limit);
        }
        catch (error) {
            console.error('❌ [CUTTING-INTEGRATION] Error fetching top machines:', error);
            throw error;
        }
    }
    /**
     * Get machine utilization analysis
     */
    async getMachineUtilizationData() {
        try {
            return await this.cuttingRepo.getMachineUtilizationAnalysis();
        }
        catch (error) {
            console.error('❌ [CUTTING-INTEGRATION] Error fetching machine utilization data:', error);
            throw error;
        }
    }
    /**
     * Analyze warehouse cuts performance
     */
    async getWarehouseCutsAnalysis(timeRange) {
        try {
            const ablaengereiData = await this.cuttingRepo.getAblaengereiData(timeRange);
            if (ablaengereiData.length === 0) {
                return this.getEmptyWarehouseCuts();
            }
            // Calculate totals and averages for each warehouse category
            const lager200Total = ablaengereiData.reduce((sum, item) => sum + (item.lagerCut200 || 0), 0);
            const lager220Total = ablaengereiData.reduce((sum, item) => sum + (item.lagerCut220 || 0), 0);
            const lager240Total = ablaengereiData.reduce((sum, item) => sum + (item.lagerCut240 || 0), 0);
            const lager200Average = lager200Total / ablaengereiData.length;
            const lager220Average = lager220Total / ablaengereiData.length;
            const lager240Average = lager240Total / ablaengereiData.length;
            const totalWarehouseCuts = lager200Total + lager220Total + lager240Total;
            // Calculate trends (simplified - would need historical comparison in real implementation)
            const lager200Trend = this.calculateSimpleTrend(ablaengereiData.map(item => item.lagerCut200 || 0));
            const lager220Trend = this.calculateSimpleTrend(ablaengereiData.map(item => item.lagerCut220 || 0));
            const lager240Trend = this.calculateSimpleTrend(ablaengereiData.map(item => item.lagerCut240 || 0));
            // Determine efficiency based on total cuts and consistency
            let efficiency = 'poor';
            const averageCutsPerDay = totalWarehouseCuts / ablaengereiData.length;
            if (averageCutsPerDay >= 1000) {
                efficiency = 'excellent';
            }
            else if (averageCutsPerDay >= 750) {
                efficiency = 'good';
            }
            else if (averageCutsPerDay >= 500) {
                efficiency = 'average';
            }
            return {
                lager200: {
                    total: lager200Total,
                    average: lager200Average,
                    trend: lager200Trend
                },
                lager220: {
                    total: lager220Total,
                    average: lager220Average,
                    trend: lager220Trend
                },
                lager240: {
                    total: lager240Total,
                    average: lager240Average,
                    trend: lager240Trend
                },
                totalWarehouseCuts,
                efficiency
            };
        }
        catch (error) {
            console.error('❌ [CUTTING-INTEGRATION] Error analyzing warehouse cuts:', error);
            return this.getEmptyWarehouseCuts();
        }
    }
    /**
     * Calculate cutting trends and performance indicators
     */
    async getCuttingTrends(timeRange) {
        try {
            const [machineEfficiency, cuttingData, warehouseCuts] = await Promise.all([
                this.getMachineEfficiencyData(timeRange),
                this.getCuttingChartData(timeRange),
                this.getWarehouseCutsAnalysis(timeRange)
            ]);
            // Calculate efficiency trends
            const efficiencyTrend = this.calculateEfficiencyTrend(machineEfficiency);
            // Calculate volume trends
            const volumeTrend = this.calculateVolumeTrend(cuttingData);
            // Calculate machine performance
            const machinePerformance = this.calculateMachinePerformance(machineEfficiency);
            // Calculate warehouse performance
            const warehousePerformance = this.calculateWarehousePerformance(warehouseCuts, cuttingData);
            return {
                efficiencyTrend,
                volumeTrend,
                machinePerformance,
                warehousePerformance
            };
        }
        catch (error) {
            console.error('❌ [CUTTING-INTEGRATION] Error calculating cutting trends:', error);
            return this.getEmptyTrends();
        }
    }
    /**
     * Format comprehensive Cutting summary for AI consumption
     */
    formatCuttingSummary(cuttingData, machineEfficiency, performanceOverview, topMachines, warehouseCuts, trends) {
        let summary = `ABLÄNGEREI-ÜBERSICHT:\n`;
        // Performance overview summary
        summary += `Gesamtschnitte: ${performanceOverview.totalCuts.cutTT + performanceOverview.totalCuts.cutTR + performanceOverview.totalCuts.cutRR + performanceOverview.totalCuts.pickCut}\n`;
        summary += `Durchschnittliche Effizienz: ${performanceOverview.averageEfficiency.toFixed(1)}%\n`;
        summary += `Top-Maschine: ${performanceOverview.topMachine.name} (${performanceOverview.topMachine.efficiency.toFixed(1)}%)\n`;
        summary += `Zeitraum: ${performanceOverview.periodSummary.totalDays} Tage\n\n`;
        // Machine efficiency trends
        summary += `MASCHINEN-EFFIZIENZ:\n`;
        summary += `Aktuelle Effizienz: ${trends.efficiencyTrend.current.toFixed(1)}% (${trends.efficiencyTrend.trend === 'up' ? '↗️' : trends.efficiencyTrend.trend === 'down' ? '↘️' : '→'} ${Math.abs(trends.efficiencyTrend.changePercent).toFixed(1)}%)\n`;
        summary += `Durchschnitt: ${trends.efficiencyTrend.average.toFixed(1)}%\n`;
        summary += `Aktive Maschinen: ${trends.machinePerformance.totalMachines} (${(trends.machinePerformance.activePercentage * 100).toFixed(1)}% aktiv)\n\n`;
        // Volume trends
        summary += `SCHNITT-VOLUMEN:\n`;
        summary += `Aktuelles Volumen: ${trends.volumeTrend.current.toFixed(0)} Schnitte (${trends.volumeTrend.trend === 'up' ? '↗️' : trends.volumeTrend.trend === 'down' ? '↘️' : '→'} ${Math.abs(trends.volumeTrend.changePercent).toFixed(1)}%)\n`;
        summary += `Durchschnitt: ${trends.volumeTrend.average.toFixed(0)} Schnitte/Tag\n\n`;
        // Top machines
        if (topMachines.length > 0) {
            summary += `TOP-MASCHINEN:\n`;
            topMachines.slice(0, 3).forEach((machine, index) => {
                summary += `${index + 1}. ${machine.name}: ${machine.averageEfficiency.toFixed(1)}% Effizienz, ${machine.totalCuts} Schnitte\n`;
            });
            summary += `\n`;
        }
        // Warehouse cuts analysis
        summary += `LAGER-SCHNITTE:\n`;
        summary += `Lager 200: ${warehouseCuts.lager200.total} (Ø ${warehouseCuts.lager200.average.toFixed(0)}/Tag, ${this.getTrendSymbol(warehouseCuts.lager200.trend)})\n`;
        summary += `Lager 220: ${warehouseCuts.lager220.total} (Ø ${warehouseCuts.lager220.average.toFixed(0)}/Tag, ${this.getTrendSymbol(warehouseCuts.lager220.trend)})\n`;
        summary += `Lager 240: ${warehouseCuts.lager240.total} (Ø ${warehouseCuts.lager240.average.toFixed(0)}/Tag, ${this.getTrendSymbol(warehouseCuts.lager240.trend)})\n`;
        summary += `Gesamt Lager-Schnitte: ${warehouseCuts.totalWarehouseCuts}\n`;
        summary += `Lager-Effizienz: ${this.getEfficiencyDisplayName(warehouseCuts.efficiency)}\n\n`;
        // Recent performance highlights
        if (cuttingData.length > 0) {
            const recentData = cuttingData.slice(-5);
            const avgRecentCuts = recentData.reduce((sum, item) => sum + item.cutTT + item.cutTR + item.cutRR + item.pickCut, 0) / recentData.length;
            summary += `AKTUELLE LEISTUNG:\n`;
            summary += `Schnitte letzte 5 Tage: ${avgRecentCuts.toFixed(0)}/Tag\n`;
        }
        if (machineEfficiency.length > 0) {
            const activeMachines = machineEfficiency.filter(m => m.effizienzProzent > 0);
            const avgEfficiency = activeMachines.reduce((sum, m) => sum + m.effizienzProzent, 0) / activeMachines.length;
            summary += `Durchschnittliche Maschinen-Effizienz: ${avgEfficiency.toFixed(1)}%\n`;
        }
        return summary;
    }
    /**
     * Format cutting data for specific AI queries
     */
    formatCuttingDataForQuery(data, queryType = 'overview') {
        let formatted = '';
        switch (queryType) {
            case 'efficiency':
                formatted = 'MASCHINEN-EFFIZIENZ:\n';
                if (data.machineEfficiency.length > 0) {
                    const topEfficient = data.machineEfficiency.slice(0, 5);
                    formatted += `Top 5 effizienteste Maschinen:\n`;
                    topEfficient.forEach((machine, index) => {
                        formatted += `${index + 1}. ${machine.Machine}: ${machine.effizienzProzent.toFixed(1)}% (${machine.tagesSchnitte} Schnitte)\n`;
                    });
                    formatted += `Durchschnittliche Effizienz: ${data.trends.efficiencyTrend.average.toFixed(1)}%\n`;
                }
                else {
                    formatted += 'Keine Effizienz-Daten verfügbar.\n';
                }
                break;
            case 'machines':
                formatted = 'MASCHINEN-LEISTUNG:\n';
                if (data.topMachines.length > 0) {
                    formatted += `Top-Performer:\n`;
                    data.topMachines.forEach(machine => {
                        formatted += `${machine.name}: ${machine.averageEfficiency.toFixed(1)}% Effizienz, ${machine.totalCuts} Schnitte, Rang ${machine.rank}\n`;
                    });
                    formatted += `Gesamt aktive Maschinen: ${data.trends.machinePerformance.totalMachines}\n`;
                }
                else {
                    formatted += 'Keine Maschinen-Daten verfügbar.\n';
                }
                break;
            case 'warehouse':
                formatted = 'LAGER-SCHNITTE:\n';
                formatted += `Lager 200: ${data.warehouseCuts.lager200.total} Schnitte (Ø ${data.warehouseCuts.lager200.average.toFixed(0)}/Tag)\n`;
                formatted += `Lager 220: ${data.warehouseCuts.lager220.total} Schnitte (Ø ${data.warehouseCuts.lager220.average.toFixed(0)}/Tag)\n`;
                formatted += `Lager 240: ${data.warehouseCuts.lager240.total} Schnitte (Ø ${data.warehouseCuts.lager240.average.toFixed(0)}/Tag)\n`;
                formatted += `Gesamt: ${data.warehouseCuts.totalWarehouseCuts} Lager-Schnitte\n`;
                formatted += `Effizienz: ${this.getEfficiencyDisplayName(data.warehouseCuts.efficiency)}\n`;
                break;
            case 'volume':
                formatted = 'SCHNITT-VOLUMEN:\n';
                if (data.cuttingData.length > 0) {
                    const recent = data.cuttingData.slice(-5); // Last 5 days
                    formatted += `Aktuelle Schnitte (letzte 5 Tage):\n`;
                    recent.forEach(item => {
                        const totalCuts = item.cutTT + item.cutTR + item.cutRR + item.pickCut;
                        formatted += `${this.formatDate(item.date)}: ${totalCuts} Schnitte (TT:${item.cutTT}, TR:${item.cutTR}, RR:${item.cutRR}, Pick:${item.pickCut})\n`;
                    });
                    formatted += `Trend: ${data.trends.volumeTrend.trend === 'up' ? 'Steigend' : data.trends.volumeTrend.trend === 'down' ? 'Fallend' : 'Stabil'}\n`;
                }
                else {
                    formatted += 'Keine Volumen-Daten verfügbar.\n';
                }
                break;
            case 'overview':
            default:
                formatted = data.summary;
                break;
        }
        return formatted;
    }
    // Private helper methods
    calculateSimpleTrend(values) {
        if (values.length < 2)
            return 'stable';
        const firstHalf = values.slice(0, Math.floor(values.length / 2));
        const secondHalf = values.slice(Math.floor(values.length / 2));
        const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
        const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;
        if (Math.abs(changePercent) < 5)
            return 'stable'; // 5% threshold
        return changePercent > 0 ? 'up' : 'down';
    }
    calculateEfficiencyTrend(machineEfficiency) {
        if (machineEfficiency.length === 0) {
            return { current: 0, average: 0, trend: 'stable', changePercent: 0 };
        }
        const efficiencies = machineEfficiency.map(m => m.effizienzProzent);
        const current = efficiencies[0] || 0; // Most recent (sorted by efficiency)
        const average = efficiencies.reduce((sum, eff) => sum + eff, 0) / efficiencies.length;
        // Calculate trend based on first half vs second half
        let trend = 'stable';
        let changePercent = 0;
        if (efficiencies.length >= 4) {
            const firstHalf = efficiencies.slice(0, Math.floor(efficiencies.length / 2));
            const secondHalf = efficiencies.slice(Math.floor(efficiencies.length / 2));
            const firstAvg = firstHalf.reduce((sum, eff) => sum + eff, 0) / firstHalf.length;
            const secondAvg = secondHalf.reduce((sum, eff) => sum + eff, 0) / secondHalf.length;
            changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;
            if (Math.abs(changePercent) > 5) { // 5% threshold for trend detection
                trend = changePercent > 0 ? 'up' : 'down';
            }
        }
        return { current, average, trend, changePercent };
    }
    calculateVolumeTrend(cuttingData) {
        if (cuttingData.length === 0) {
            return { current: 0, average: 0, trend: 'stable', changePercent: 0 };
        }
        const volumes = cuttingData.map(item => item.cutTT + item.cutTR + item.cutRR + item.pickCut);
        const current = volumes[volumes.length - 1] || 0; // Most recent
        const average = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
        // Calculate trend
        let trend = 'stable';
        let changePercent = 0;
        if (volumes.length >= 4) {
            const firstHalf = volumes.slice(0, Math.floor(volumes.length / 2));
            const secondHalf = volumes.slice(Math.floor(volumes.length / 2));
            const firstAvg = firstHalf.reduce((sum, vol) => sum + vol, 0) / firstHalf.length;
            const secondAvg = secondHalf.reduce((sum, vol) => sum + vol, 0) / secondHalf.length;
            changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;
            if (Math.abs(changePercent) > 10) { // 10% threshold for volume trend
                trend = changePercent > 0 ? 'up' : 'down';
            }
        }
        return { current, average, trend, changePercent };
    }
    calculateMachinePerformance(machineEfficiency) {
        var _a;
        if (machineEfficiency.length === 0) {
            return { topPerformer: 'N/A', averageEfficiency: 0, totalMachines: 0, activePercentage: 0 };
        }
        const topPerformer = ((_a = machineEfficiency[0]) === null || _a === void 0 ? void 0 : _a.Machine) || 'N/A'; // Already sorted by efficiency
        const averageEfficiency = machineEfficiency.reduce((sum, m) => sum + m.effizienzProzent, 0) / machineEfficiency.length;
        const totalMachines = machineEfficiency.length;
        const activeMachines = machineEfficiency.filter(m => m.effizienzProzent > 0).length;
        const activePercentage = activeMachines / totalMachines;
        return {
            topPerformer,
            averageEfficiency,
            totalMachines,
            activePercentage
        };
    }
    calculateWarehousePerformance(warehouseCuts, cuttingData) {
        const totalCuts = warehouseCuts.totalWarehouseCuts;
        const averageCutsPerDay = cuttingData.length > 0 ? totalCuts / cuttingData.length : 0;
        // Determine best category
        let bestCategory = 'Lager 200';
        let bestValue = warehouseCuts.lager200.total;
        if (warehouseCuts.lager220.total > bestValue) {
            bestCategory = 'Lager 220';
            bestValue = warehouseCuts.lager220.total;
        }
        if (warehouseCuts.lager240.total > bestValue) {
            bestCategory = 'Lager 240';
        }
        return {
            totalCuts,
            averageCutsPerDay,
            bestCategory,
            efficiency: warehouseCuts.efficiency
        };
    }
    getEmptyWarehouseCuts() {
        return {
            lager200: { total: 0, average: 0, trend: 'stable' },
            lager220: { total: 0, average: 0, trend: 'stable' },
            lager240: { total: 0, average: 0, trend: 'stable' },
            totalWarehouseCuts: 0,
            efficiency: 'poor'
        };
    }
    getEmptyTrends() {
        return {
            efficiencyTrend: { current: 0, average: 0, trend: 'stable', changePercent: 0 },
            volumeTrend: { current: 0, average: 0, trend: 'stable', changePercent: 0 },
            machinePerformance: { topPerformer: 'N/A', averageEfficiency: 0, totalMachines: 0, activePercentage: 0 },
            warehousePerformance: { totalCuts: 0, averageCutsPerDay: 0, bestCategory: 'N/A', efficiency: 'poor' }
        };
    }
    getTrendSymbol(trend) {
        return trend === 'up' ? '↗️' : trend === 'down' ? '↘️' : '→';
    }
    getEfficiencyDisplayName(efficiency) {
        const efficiencyMap = {
            'excellent': 'Ausgezeichnet',
            'good': 'Gut',
            'average': 'Durchschnittlich',
            'poor': 'Verbesserungsbedürftig'
        };
        return efficiencyMap[efficiency];
    }
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('de-DE', {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit'
        });
    }
}
exports.CuttingDataIntegrationService = CuttingDataIntegrationService;
exports.default = CuttingDataIntegrationService;
