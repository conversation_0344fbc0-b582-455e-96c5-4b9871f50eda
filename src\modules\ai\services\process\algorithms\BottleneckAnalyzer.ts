/**
 * Bottleneck Analysis Algorithms
 * Identifies bottlenecks in business processes using various analytical methods
 */

import {
  ProcessData,
  ProcessStep,
  ProcessResource,
  Bottleneck,
  ProcessHistoricalData,
  ProcessEvent,
  ProcessOptimizationError
} from '../types';

export class BottleneckAnalyzer {
  /**
   * Identify bottlenecks in a process using multiple analysis methods
   */
  public static identifyBottlenecks(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): Bottleneck[] {
    try {
      // Validate input data
      if (!processData.steps || processData.steps.length === 0) {
        throw new ProcessOptimizationError(
          'Process data must contain at least one step',
          'INVALID_PROCESS_DATA',
          processData.processId
        );
      }

      if (!processData.resources || processData.resources.length === 0) {
        throw new ProcessOptimizationError(
          'Process data must contain at least one resource',
          'INVALID_PROCESS_DATA',
          processData.processId
        );
      }

      const bottlenecks: Bottleneck[] = [];

      // 1. Capacity-based bottleneck analysis
      const capacityBottlenecks = this.analyzeCapacityBottlenecks(processData);
      bottlenecks.push(...capacityBottlenecks);

      // 2. Utilization-based bottleneck analysis
      const utilizationBottlenecks = this.analyzeUtilizationBottlenecks(processData);
      bottlenecks.push(...utilizationBottlenecks);

      // 3. Queue-based bottleneck analysis
      const queueBottlenecks = this.analyzeQueueBottlenecks(processData, historicalData);
      bottlenecks.push(...queueBottlenecks);

      // 4. Cycle time bottleneck analysis
      const cycleTimeBottlenecks = this.analyzeCycleTimeBottlenecks(processData, historicalData);
      bottlenecks.push(...cycleTimeBottlenecks);

      // 5. Resource constraint analysis
      const resourceBottlenecks = this.analyzeResourceConstraints(processData);
      bottlenecks.push(...resourceBottlenecks);

      // Remove duplicates and rank by severity
      return this.consolidateAndRankBottlenecks(bottlenecks);
    } catch (error) {
      throw new ProcessOptimizationError(
        `Bottleneck analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'BOTTLENECK_ANALYSIS_FAILED',
        processData.processId
      );
    }
  }

  /**
   * Analyze bottlenecks based on step capacity vs demand
   */
  private static analyzeCapacityBottlenecks(processData: ProcessData): Bottleneck[] {
    const bottlenecks: Bottleneck[] = [];

    for (const step of processData.steps) {
      const demandRate = this.calculateStepDemandRate(step, processData);
      const capacityRate = this.calculateStepCapacityRate(step, processData);
      
      // Check if step is over capacity or high utilization
      const utilizationRatio = demandRate / capacityRate;
      if (utilizationRatio > 0.9 || step.currentUtilization > 0.9) { // 90% threshold
        const severity = this.calculateCapacitySeverity(demandRate, capacityRate);
        const impact = this.calculateCapacityImpact(step, processData);

        bottlenecks.push({
          stepId: step.stepId,
          severity,
          impact,
          description: `Step "${step.name}" capacity bottleneck - operating at ${Math.round(Math.max(utilizationRatio, step.currentUtilization) * 100)}% capacity`,
          suggestedActions: [
            'Increase step capacity',
            'Add parallel processing capability',
            'Optimize step efficiency',
            'Redistribute workload'
          ],
          estimatedImprovement: Math.min(50, Math.max(utilizationRatio - 0.8, step.currentUtilization - 0.8) * 100)
        });
      }
    }

    return bottlenecks;
  }

  /**
   * Analyze bottlenecks based on resource utilization
   */
  private static analyzeUtilizationBottlenecks(processData: ProcessData): Bottleneck[] {
    const bottlenecks: Bottleneck[] = [];

    for (const resource of processData.resources) {
      if (resource.currentLoad > 0.85) { // 85% utilization threshold
        const severity = this.calculateUtilizationSeverity(resource.currentLoad);
        const affectedSteps = this.getStepsUsingResource(resource.resourceId, processData);
        const impact = this.calculateResourceImpact(resource, affectedSteps, processData);

        bottlenecks.push({
          stepId: affectedSteps[0]?.stepId || '',
          resourceId: resource.resourceId,
          severity,
          impact,
          description: `Resource "${resource.name}" is ${Math.round(resource.currentLoad * 100)}% utilized`,
          suggestedActions: [
            'Add additional resource capacity',
            'Optimize resource scheduling',
            'Cross-train personnel for flexibility',
            'Implement resource pooling'
          ],
          estimatedImprovement: Math.min(40, (resource.currentLoad - 0.7) * 100)
        });
      }
    }

    return bottlenecks;
  }

  /**
   * Analyze bottlenecks based on queue lengths and wait times
   */
  private static analyzeQueueBottlenecks(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): Bottleneck[] {
    const bottlenecks: Bottleneck[] = [];

    if (historicalData.length === 0) {
      // If no historical data, check for potential queue issues based on utilization
      for (const step of processData.steps) {
        if (step.currentUtilization > 0.85) {
          bottlenecks.push({
            stepId: step.stepId,
            severity: 'medium',
            impact: 30,
            description: `Step "${step.name}" likely has wait time issues due to high utilization`,
            suggestedActions: [
              'Monitor queue lengths',
              'Increase processing capacity',
              'Implement priority queuing'
            ],
            estimatedImprovement: (step.currentUtilization - 0.8) * 50
          });
        }
      }
      return bottlenecks;
    }

    // Analyze wait times for each step
    for (const step of processData.steps) {
      const avgWaitTime = this.calculateAverageWaitTime(step.stepId, historicalData);
      const processingTime = step.duration;
      
      if (avgWaitTime > processingTime * 1.5) { // Wait time > 1.5x processing time
        const severity = this.calculateQueueSeverity(avgWaitTime, processingTime);
        const impact = this.calculateQueueImpact(step, avgWaitTime, processData);

        bottlenecks.push({
          stepId: step.stepId,
          severity,
          impact,
          description: `Step "${step.name}" has excessive wait time (${Math.round(avgWaitTime)} min avg)`,
          suggestedActions: [
            'Increase processing capacity',
            'Implement priority queuing',
            'Optimize upstream processes',
            'Add buffer management'
          ],
          estimatedImprovement: Math.min(60, (avgWaitTime / processingTime - 1) * 20)
        });
      }
    }

    return bottlenecks;
  }

  /**
   * Analyze bottlenecks based on cycle time variations
   */
  private static analyzeCycleTimeBottlenecks(
    processData: ProcessData,
    historicalData: ProcessHistoricalData[]
  ): Bottleneck[] {
    const bottlenecks: Bottleneck[] = [];

    if (historicalData.length < 5) return bottlenecks; // Need sufficient data

    // Calculate cycle time statistics
    const cycleTimes = historicalData.map(data => data.metrics.cycleTime);
    const avgCycleTime = cycleTimes.reduce((sum, time) => sum + time, 0) / cycleTimes.length;
    const stdDeviation = this.calculateStandardDeviation(cycleTimes);
    const variationCoefficient = stdDeviation / avgCycleTime;

    if (variationCoefficient > 0.2) { // Lower threshold for variation
      // Find steps with highest variation
      const stepVariations = this.calculateStepVariations(processData.steps, historicalData);
      
      for (const [stepId, variation] of stepVariations.entries()) {
        if (variation > 0.3) { // Lower threshold
          const step = processData.steps.find(s => s.stepId === stepId);
          if (step) {
            bottlenecks.push({
              stepId,
              severity: variation > 0.5 ? 'high' : 'medium',
              impact: variation * 40, // Convert to percentage impact
              description: `Step "${step.name}" has high cycle time variation (${Math.round(variation * 100)}%)`,
              suggestedActions: [
                'Standardize work procedures',
                'Implement quality controls',
                'Provide additional training',
                'Identify root causes of variation'
              ],
              estimatedImprovement: Math.min(35, variation * 50)
            });
          }
        }
      }
    }

    return bottlenecks;
  }

  /**
   * Analyze resource constraint bottlenecks
   */
  private static analyzeResourceConstraints(processData: ProcessData): Bottleneck[] {
    const bottlenecks: Bottleneck[] = [];

    // Check for resource conflicts and dependencies
    for (const step of processData.steps) {
      const resourceConflicts = this.identifyResourceConflicts(step, processData);
      
      if (resourceConflicts.length > 0) {
        const severity = resourceConflicts.length > 2 ? 'high' : 'medium';
        const impact = this.calculateConflictImpact(resourceConflicts, processData);

        bottlenecks.push({
          stepId: step.stepId,
          severity,
          impact,
          description: `Step "${step.name}" has ${resourceConflicts.length} resource conflicts`,
          suggestedActions: [
            'Resolve resource scheduling conflicts',
            'Implement resource reservation system',
            'Add alternative resource options',
            'Optimize resource allocation'
          ],
          estimatedImprovement: Math.min(45, resourceConflicts.length * 15)
        });
      }
    }

    return bottlenecks;
  }

  /**
   * Calculate demand rate for a step
   */
  private static calculateStepDemandRate(step: ProcessStep, processData: ProcessData): number {
    // Simplified calculation based on process throughput
    return processData.metrics.throughput / processData.steps.length;
  }

  /**
   * Calculate capacity rate for a step
   */
  private static calculateStepCapacityRate(step: ProcessStep, processData: ProcessData): number {
    // Calculate based on step capacity and duration
    return (step.capacity * 60) / step.duration; // items per hour
  }

  /**
   * Calculate severity based on capacity utilization
   */
  private static calculateCapacitySeverity(demandRate: number, capacityRate: number): 'low' | 'medium' | 'high' | 'critical' {
    const utilization = demandRate / capacityRate;
    
    if (utilization >= 1.1) return 'critical';
    if (utilization >= 1.0) return 'high';
    if (utilization >= 0.9) return 'medium';
    return 'low';
  }

  /**
   * Calculate impact of capacity bottleneck
   */
  private static calculateCapacityImpact(step: ProcessStep, processData: ProcessData): number {
    // Impact based on step position and dependencies
    const dependentSteps = processData.steps.filter(s => s.dependencies.includes(step.stepId));
    return Math.min(100, 20 + dependentSteps.length * 10);
  }

  /**
   * Calculate severity based on resource utilization
   */
  private static calculateUtilizationSeverity(utilization: number): 'low' | 'medium' | 'high' | 'critical' {
    if (utilization >= 0.95) return 'critical';
    if (utilization >= 0.90) return 'high';
    if (utilization >= 0.85) return 'medium';
    return 'low';
  }

  /**
   * Get steps that use a specific resource
   */
  private static getStepsUsingResource(resourceId: string, processData: ProcessData): ProcessStep[] {
    return processData.steps.filter(step =>
      step.resourceRequirements.some(req => req.resourceId === resourceId)
    );
  }

  /**
   * Calculate impact of resource bottleneck
   */
  private static calculateResourceImpact(
    resource: ProcessResource,
    affectedSteps: ProcessStep[],
    processData: ProcessData
  ): number {
    // Impact based on number of affected steps and their importance
    const baseImpact = affectedSteps.length * 15;
    const utilizationMultiplier = resource.currentLoad;
    return Math.min(100, baseImpact * utilizationMultiplier);
  }

  /**
   * Calculate average wait time for a step
   */
  private static calculateAverageWaitTime(stepId: string, historicalData: ProcessHistoricalData[]): number {
    const waitTimes: number[] = [];
    
    for (const data of historicalData) {
      const stepEvents = data.events.filter(event => 
        event.stepId === stepId && event.type === 'delay'
      );
      
      for (const event of stepEvents) {
        if (event.duration) {
          waitTimes.push(event.duration);
        }
      }
    }

    return waitTimes.length > 0 
      ? waitTimes.reduce((sum, time) => sum + time, 0) / waitTimes.length 
      : 0;
  }

  /**
   * Calculate severity based on queue length
   */
  private static calculateQueueSeverity(waitTime: number, processingTime: number): 'low' | 'medium' | 'high' | 'critical' {
    const ratio = waitTime / processingTime;
    
    if (ratio >= 5) return 'critical';
    if (ratio >= 3) return 'high';
    if (ratio >= 2) return 'medium';
    return 'low';
  }

  /**
   * Calculate impact of queue bottleneck
   */
  private static calculateQueueImpact(step: ProcessStep, waitTime: number, processData: ProcessData): number {
    const processingTime = step.duration;
    const ratio = waitTime / processingTime;
    return Math.min(100, ratio * 20);
  }

  /**
   * Calculate standard deviation
   */
  private static calculateStandardDeviation(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
    return Math.sqrt(avgSquaredDiff);
  }

  /**
   * Calculate step variations from historical data
   */
  private static calculateStepVariations(
    steps: ProcessStep[],
    historicalData: ProcessHistoricalData[]
  ): Map<string, number> {
    const variations = new Map<string, number>();

    for (const step of steps) {
      const stepTimes: number[] = [];
      
      // Extract processing times for this step from historical data
      for (const data of historicalData) {
        const stepEvents = data.events.filter(event => 
          event.stepId === step.stepId && event.duration
        );
        
        stepEvents.forEach(event => {
          if (event.duration) stepTimes.push(event.duration);
        });
      }

      if (stepTimes.length > 1) {
        const mean = stepTimes.reduce((sum, time) => sum + time, 0) / stepTimes.length;
        const stdDev = this.calculateStandardDeviation(stepTimes);
        variations.set(step.stepId, stdDev / mean);
      }
    }

    return variations;
  }

  /**
   * Identify resource conflicts for a step
   */
  private static identifyResourceConflicts(step: ProcessStep, processData: ProcessData): string[] {
    const conflicts: string[] = [];

    for (const requirement of step.resourceRequirements) {
      const resource = processData.resources.find(r => r.resourceId === requirement.resourceId);
      if (resource && resource.currentLoad + requirement.quantity > resource.capacity) {
        conflicts.push(requirement.resourceId);
      }
    }

    return conflicts;
  }

  /**
   * Calculate impact of resource conflicts
   */
  private static calculateConflictImpact(conflicts: string[], processData: ProcessData): number {
    // Impact based on number of conflicts and affected resources
    return Math.min(100, conflicts.length * 25);
  }

  /**
   * Consolidate and rank bottlenecks by severity and impact
   */
  private static consolidateAndRankBottlenecks(bottlenecks: Bottleneck[]): Bottleneck[] {
    // Remove duplicates based on stepId and resourceId
    const uniqueBottlenecks = new Map<string, Bottleneck>();

    for (const bottleneck of bottlenecks) {
      const key = `${bottleneck.stepId}_${bottleneck.resourceId || ''}`;
      const existing = uniqueBottlenecks.get(key);

      if (!existing || bottleneck.impact > existing.impact) {
        uniqueBottlenecks.set(key, bottleneck);
      }
    }

    // Sort by severity and impact
    return Array.from(uniqueBottlenecks.values()).sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
      
      if (severityDiff !== 0) return severityDiff;
      return b.impact - a.impact;
    });
  }
}