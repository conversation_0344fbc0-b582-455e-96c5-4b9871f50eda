/**
 * AI Module
 * Exports for the AI Assistant module
 */

export { aiModuleConfig } from './module.config';

// Pages exports (will be populated during migration)
// export * from './pages';

// Components exports (excluding Source to avoid conflicts)
export * from './components/ai';
export * from './components/chat';
export * from './components/cutting';
export * from './components/inventory';
export * from './components/warehouse';
export * from './components/reporting';
export * from './components/supply-chain';
export * from './components/settings';
export * from './components/navigation';
export * from './components/documentation';
export * from './components/security';

// Export components from directories with index files
export * from './components/error-handling';
export * from './components/performance';

// Export ai-elements components individually to exclude Source
export * from './components/ai-elements/actions';
export * from './components/ai-elements/code-block';
export * from './components/ai-elements/conversation';
export * from './components/ai-elements/loader';
export * from './components/ai-elements/message';
export * from './components/ai-elements/prompt-input';
export * from './components/ai-elements/response';
export * from './components/ai-elements/suggestion';
export * from './components/ai-elements/task';
export * from './components/ai-elements/tool';

// Hooks exports
export * from './hooks';

// Services exports
export * from './services';

// Resolve naming conflicts by explicitly re-exporting with aliases
export type { Source as SourceInterface } from './services/types';
export { Source as SourceComponent } from './components/ai-elements/source';

// Error Handling exports - use specific exports to avoid conflicts
export { 
  AIErrorHandler,
  AIOperationLogger,
  createAIErrorHandler,
  createRAGErrorHandler,
  createCuttingErrorHandler,
  createInventoryErrorHandler,
  createProcessErrorHandler,
  createPredictiveErrorHandler,
  createReportingErrorHandler,
  createSupplyChainErrorHandler,
  createWarehouseErrorHandler
} from './services/error-handling';

export * from './utils/errorMessages';
export * from './decorators/errorHandling';

// Types exports - export directly from types to avoid conflicts
export type { 
  AIServiceError, 
  ErrorRecoveryStrategy,
  AIOperationLog,
  ErrorHandlingConfig,
  FallbackStrategy
} from './types/errors';

export { 
  AIServiceErrorCode, 
  AIServiceErrorSeverity 
} from './types/errors';

// Module-specific types
export type AIPageType =
  | 'dashboard'
  | 'chat'
  | 'cutting-optimization'
  | 'inventory-intelligence'
  | 'warehouse-optimization'
  | 'supply-chain-analytics'
  | 'reporting'
  | 'predictive-analytics'
  | 'settings';