import { ModuleConfig } from '@/types/module';

export const stoerungenModuleConfig: ModuleConfig = {
  id: 'stoerungen',
  name: 'stoerungen',
  displayName: 'Störungen',
  description: 'Störungsmanagement und Monitoring-System',
  icon: 'Alert<PERSON>riangle',
  baseRoute: '/modules/stoerungen',
  requiredRoles: ['<PERSON><PERSON><PERSON>', 'Administrator'],
  isEnabled: true,
  pages: [
    {
      id: 'stoerungen',
      name: 'Störungen',
      route: '/modules/stoerungen',
      component: 'StoerungenPage'
    },
    {
      id: 'monitoring',
      name: 'Monitoring',
      route: '/modules/stoerungen/monitoring',
      component: 'MonitoringPage'
    }
  ]
};