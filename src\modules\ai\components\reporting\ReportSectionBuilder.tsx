/**
 * Report Section Builder Component
 * 
 * Form component for configuring individual report sections
 */

import React, { useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  Table, 
  TrendingUp, 
  FileText, 
  Lightbulb 
} from 'lucide-react';
import type { ReportSectionFormData } from '@/types/reporting';

interface ReportSectionBuilderProps {
  section: ReportSectionFormData;
  onChange: (section: ReportSectionFormData) => void;
}

/**
 * Report Section Builder Component
 */
export const ReportSectionBuilder: React.FC<ReportSectionBuilderProps> = ({
  section,
  onChange
}) => {
  /**
   * Handle field changes
   */
  const handleFieldChange = useCallback((field: keyof ReportSectionFormData, value: any) => {
    onChange({
      ...section,
      [field]: value
    });
  }, [section, onChange]);

  /**
   * Get section type icon
   */
  const getSectionTypeIcon = (type: string) => {
    switch (type) {
      case 'chart':
        return <BarChart3 className="h-4 w-4" />;
      case 'table':
        return <Table className="h-4 w-4" />;
      case 'kpi':
        return <TrendingUp className="h-4 w-4" />;
      case 'text':
        return <FileText className="h-4 w-4" />;
      case 'insights':
        return <Lightbulb className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  /**
   * Get data source display name
   */
  const getDataSourceName = (source: string) => {
    const sources: Record<string, string> = {
      'delivery': 'Lieferungen',
      'production': 'Produktion',
      'warehouse': 'Lager',
      'supplier': 'Lieferanten',
      'system': 'System'
    };
    return sources[source] || source;
  };

  return (
    <div className="space-y-4">
      {/* Basic Section Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="title">Titel</Label>
          <Input
            id="title"
            value={section.title}
            onChange={(e) => handleFieldChange('title', e.target.value)}
            placeholder="z.B. Lieferzeiten Trend"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="type">Abschnittstyp</Label>
          <Select value={section.type} onValueChange={(value) => handleFieldChange('type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="chart">
                <div className="flex items-center">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Diagramm
                </div>
              </SelectItem>
              <SelectItem value="table">
                <div className="flex items-center">
                  <Table className="h-4 w-4 mr-2" />
                  Tabelle
                </div>
              </SelectItem>
              <SelectItem value="kpi">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  KPI-Kennzahl
                </div>
              </SelectItem>
              <SelectItem value="text">
                <div className="flex items-center">
                  <FileText className="h-4 w-4 mr-2" />
                  Text
                </div>
              </SelectItem>
              <SelectItem value="insights">
                <div className="flex items-center">
                  <Lightbulb className="h-4 w-4 mr-2" />
                  KI-Erkenntnisse
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Data Source Configuration */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center">
            {getSectionTypeIcon(section.type)}
            <span className="ml-2">Datenquelle</span>
          </CardTitle>
          <CardDescription>
            Konfigurieren Sie die Datenquelle und Verarbeitung für diesen Abschnitt.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="dataSource">Datenquelle</Label>
              <Select value={section.dataSource} onValueChange={(value) => handleFieldChange('dataSource', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="delivery">Lieferungen</SelectItem>
                  <SelectItem value="production">Produktion</SelectItem>
                  <SelectItem value="warehouse">Lager</SelectItem>
                  <SelectItem value="supplier">Lieferanten</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="timeRange">Zeitraum</Label>
              <Select value={section.timeRange} onValueChange={(value) => handleFieldChange('timeRange', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="day">Tag</SelectItem>
                  <SelectItem value="week">Woche</SelectItem>
                  <SelectItem value="month">Monat</SelectItem>
                  <SelectItem value="quarter">Quartal</SelectItem>
                  <SelectItem value="year">Jahr</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Chart-specific options */}
          {section.type === 'chart' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="chartType">Diagrammtyp</Label>
                <Select value={section.chartType} onValueChange={(value) => handleFieldChange('chartType', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="line">Liniendiagramm</SelectItem>
                    <SelectItem value="bar">Balkendiagramm</SelectItem>
                    <SelectItem value="pie">Kreisdiagramm</SelectItem>
                    <SelectItem value="area">Flächendiagramm</SelectItem>
                    <SelectItem value="scatter">Streudiagramm</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="aggregation">Aggregation</Label>
                <Select value={section.aggregation} onValueChange={(value) => handleFieldChange('aggregation', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sum">Summe</SelectItem>
                    <SelectItem value="avg">Durchschnitt</SelectItem>
                    <SelectItem value="count">Anzahl</SelectItem>
                    <SelectItem value="min">Minimum</SelectItem>
                    <SelectItem value="max">Maximum</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {/* KPI-specific options */}
          {section.type === 'kpi' && (
            <div className="space-y-2">
              <Label htmlFor="aggregation">KPI-Berechnung</Label>
              <Select value={section.aggregation} onValueChange={(value) => handleFieldChange('aggregation', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sum">Gesamtsumme</SelectItem>
                  <SelectItem value="avg">Durchschnittswert</SelectItem>
                  <SelectItem value="count">Gesamtanzahl</SelectItem>
                  <SelectItem value="min">Minimalwert</SelectItem>
                  <SelectItem value="max">Maximalwert</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Section Preview */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Vorschau</CardTitle>
          <CardDescription>
            So wird dieser Abschnitt im Bericht dargestellt.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 border-2 border-dashed border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">{section.title || 'Unbenannter Abschnitt'}</h4>
              <div className="flex items-center space-x-2">
                <Badge variant="outline">
                  {getSectionTypeIcon(section.type)}
                  <span className="ml-1 capitalize">{section.type}</span>
                </Badge>
                <Badge variant="secondary">
                  {getDataSourceName(section.dataSource)}
                </Badge>
              </div>
            </div>
            
            <div className="text-sm text-gray-600">
              <p>Datenquelle: {getDataSourceName(section.dataSource)}</p>
              <p>Zeitraum: {section.timeRange}</p>
              {section.chartType && <p>Diagrammtyp: {section.chartType}</p>}
              {section.aggregation && <p>Aggregation: {section.aggregation}</p>}
            </div>

            {/* Mock visualization based on type */}
            <div className="mt-4 h-32 bg-gray-50 rounded flex items-center justify-center">
              {section.type === 'chart' && (
                <div className="text-center">
                  <BarChart3 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">
                    {section.chartType} Diagramm Vorschau
                  </p>
                </div>
              )}
              {section.type === 'table' && (
                <div className="text-center">
                  <Table className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Tabellen Vorschau</p>
                </div>
              )}
              {section.type === 'kpi' && (
                <div className="text-center">
                  <TrendingUp className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">KPI Kennzahl Vorschau</p>
                </div>
              )}
              {section.type === 'text' && (
                <div className="text-center">
                  <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Text Abschnitt Vorschau</p>
                </div>
              )}
              {section.type === 'insights' && (
                <div className="text-center">
                  <Lightbulb className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">KI-Erkenntnisse Vorschau</p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportSectionBuilder;