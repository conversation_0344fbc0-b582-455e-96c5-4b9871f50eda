import { ModuleConfig } from '@/types/module';

export const dashboardModuleConfig: ModuleConfig = {
  id: 'dashboard',
  name: 'dashboard',
  displayName: 'Dashboard',
  description: 'Dashboard für Produktionsübersicht, KPIs und Betriebsdaten',
  icon: 'BarChart3',
  baseRoute: '/modules/dashboard',
  requiredRoles: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Administrator'],
  isEnabled: true,
  pages: [
    {
      id: 'home',
      name: 'Dashboard',
      route: '/modules/dashboard',
      component: 'HomePage'
    },
    {
      id: 'dispatch',
      name: 'Versand',
      route: '/modules/dashboard/dispatch',
      component: 'DispatchPage'
    },
    {
      id: 'cutting',
      name: '<PERSON><PERSON><PERSON><PERSON>nger<PERSON>',
      route: '/modules/dashboard/cutting',
      component: 'CuttingPage'
    },
    {
      id: 'incoming-goods',
      name: 'Wareneingang',
      route: '/modules/dashboard/incoming-goods',
      component: 'IncomingGoodsPage'
    },
    {
      id: 'csr',
      name: 'CSR',
      route: '/modules/dashboard/csr',
      component: 'CSRPage'
    },
    {
      id: 'aril',
      name: 'ARiL',
      route: '/modules/dashboard/aril',
      component: 'ArilPage'
    },
    {
      id: 'atrl',
      name: 'ATrL',
      route: '/modules/dashboard/atrl',
      component: 'AtrlPage'
    },
    {
      id: 'machines',
      name: 'Maschinen',
      route: '/modules/dashboard/machines',
      component: 'MachinesPage'
    }
  ]
};