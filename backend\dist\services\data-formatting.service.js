"use strict";
/**
 * Data Formatting Service
 *
 * Provides human-readable data formatting templates for each data type
 * and context building logic to combine multiple data sources for AI consumption.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataFormattingService = void 0;
/**
 * Data Formatting Service for AI Context Building
 *
 * This service provides comprehensive data formatting capabilities for converting
 * raw database information into human-readable context suitable for AI consumption.
 * It handles multiple data types, supports various formatting options, and can
 * combine data from multiple sources into coherent summaries.
 *
 * @class DataFormattingService
 * @implements {IDataFormattingService}
 *
 * @example
 * ```typescript
 * const formatter = new DataFormattingService();
 * const formattedData = formatter.formatStoerungenData(incidentData, {
 *   format: 'detailed',
 *   includeTimestamp: true,
 *   language: 'de'
 * });
 * ```
 *
 * @features
 * - Multi-language support (German/English)
 * - Flexible formatting options (summary/detailed/minimal)
 * - Time series data with trend analysis
 * - Cross-system data correlation
 * - Automatic data freshness indicators
 */
class DataFormattingService {
    constructor() {
        /** Default configuration options for context building */
        this.DEFAULT_OPTIONS = {
            includeTimestamp: true,
            includeFreshness: true,
            includeTrends: true,
            includeDetails: false,
            format: 'summary',
            maxLength: 2000,
            language: 'de'
        };
    }
    /**
     * Format Störungen data into human-readable template
     */
    formatStoerungenData(data, options = {}) {
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        const stats = data.statistics;
        const trends = data.trends;
        const title = opts.language === 'de' ? 'STÖRUNGEN-STATUS' : 'INCIDENTS STATUS';
        // Create summary
        const summary = opts.language === 'de'
            ? `${stats.total} Störungen gesamt: ${stats.active} aktiv, ${stats.resolved} gelöst. ` +
                `Schweregrade: ${stats.critical} kritisch, ${stats.high} hoch, ${stats.medium} mittel, ${stats.low} niedrig. ` +
                `MTTR: ${stats.avg_mttr_minutes} Minuten. Systemgesundheit: ${trends.systemHealthScore}%.`
            : `${stats.total} total incidents: ${stats.active} active, ${stats.resolved} resolved. ` +
                `Severity: ${stats.critical} critical, ${stats.high} high, ${stats.medium} medium, ${stats.low} low. ` +
                `MTTR: ${stats.avg_mttr_minutes} minutes. System health: ${trends.systemHealthScore}%.`;
        // Create detailed information
        let details = '';
        if (opts.includeDetails) {
            details = this.formatStoerungenDetails(data, opts);
        }
        // Create trends information
        let trendsText = '';
        if (opts.includeTrends) {
            trendsText = this.formatStoerungenTrends(trends, opts);
        }
        return {
            title,
            summary,
            details,
            trends: trendsText,
            timestamp: this.formatTimestamp(data.timestamp, opts),
            freshness: this.calculateDataFreshness(data.timestamp, opts)
        };
    }
    /**
     * Format Dispatch data into human-readable template
     */
    formatDispatchData(data, options = {}) {
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        const metrics = data.performanceMetrics;
        const trends = data.trends;
        const title = opts.language === 'de' ? 'VERSAND-STATUS' : 'DISPATCH STATUS';
        // Create summary
        const summary = opts.language === 'de'
            ? `Servicegrad: ${metrics.averageServiceLevel.toFixed(1)}%, Tonnage: ${metrics.totalTonnage.toFixed(1)}t, ` +
                `Lieferungen: ${metrics.totalDeliveries}, Kommissionierrate: ${metrics.averagePickingRate.toFixed(1)}. ` +
                `Qualitätsscore: ${trends.qualityMetrics.qualityScore}/100.`
            : `Service level: ${metrics.averageServiceLevel.toFixed(1)}%, Tonnage: ${metrics.totalTonnage.toFixed(1)}t, ` +
                `Deliveries: ${metrics.totalDeliveries}, Picking rate: ${metrics.averagePickingRate.toFixed(1)}. ` +
                `Quality score: ${trends.qualityMetrics.qualityScore}/100.`;
        // Create detailed information
        let details = '';
        if (opts.includeDetails) {
            details = this.formatDispatchDetails(data, opts);
        }
        // Create trends information
        let trendsText = '';
        if (opts.includeTrends) {
            trendsText = this.formatDispatchTrends(trends, opts);
        }
        return {
            title,
            summary,
            details,
            trends: trendsText,
            timestamp: this.formatTimestamp(data.timestamp, opts),
            freshness: this.calculateDataFreshness(data.timestamp, opts)
        };
    }
    /**
     * Format Cutting data into human-readable template
     */
    formatCuttingData(data, options = {}) {
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        const overview = data.performanceOverview;
        const trends = data.trends;
        const title = opts.language === 'de' ? 'ABLÄNGEREI-STATUS' : 'CUTTING STATUS';
        const totalCuts = overview.totalCuts.cutTT + overview.totalCuts.cutTR +
            overview.totalCuts.cutRR + overview.totalCuts.pickCut;
        // Create summary
        const summary = opts.language === 'de'
            ? `${totalCuts} Gesamtschnitte, Effizienz: ${overview.averageEfficiency.toFixed(1)}%, ` +
                `Top-Maschine: ${overview.topMachine.name} (${overview.topMachine.efficiency.toFixed(1)}%), ` +
                `${data.topMachines.length} aktive Maschinen. Lager-Schnitte: ${data.warehouseCuts.totalWarehouseCuts}.`
            : `${totalCuts} total cuts, Efficiency: ${overview.averageEfficiency.toFixed(1)}%, ` +
                `Top machine: ${overview.topMachine.name} (${overview.topMachine.efficiency.toFixed(1)}%), ` +
                `${data.topMachines.length} active machines. Warehouse cuts: ${data.warehouseCuts.totalWarehouseCuts}.`;
        // Create detailed information
        let details = '';
        if (opts.includeDetails) {
            details = this.formatCuttingDetails(data, opts);
        }
        // Create trends information
        let trendsText = '';
        if (opts.includeTrends) {
            trendsText = this.formatCuttingTrends(trends, opts);
        }
        return {
            title,
            summary,
            details,
            trends: trendsText,
            timestamp: this.formatTimestamp(data.timestamp, opts),
            freshness: this.calculateDataFreshness(data.timestamp, opts)
        };
    }
    /**
     * Build comprehensive context from multiple data sources
     */
    buildMultiSourceContext(queryResults, options = {}, detectedIntents) {
        var _a;
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        const successfulResults = queryResults.filter(r => r.success);
        if (successfulResults.length === 0) {
            return opts.language === 'de'
                ? 'Keine relevanten Daten verfügbar.'
                : 'No relevant data available.';
        }
        let context = '';
        // Add header with timestamp - use queried date if available
        if (opts.includeTimestamp) {
            let headerDate = new Date();
            let isSpecificDate = false;
            // Check if we have a specific date from the query intents
            if (detectedIntents && detectedIntents.length > 0) {
                const intentWithDate = detectedIntents.find(intent => { var _a; return (_a = intent.timeRange) === null || _a === void 0 ? void 0 : _a.startDate; });
                if ((_a = intentWithDate === null || intentWithDate === void 0 ? void 0 : intentWithDate.timeRange) === null || _a === void 0 ? void 0 : _a.startDate) {
                    const queriedDate = new Date(intentWithDate.timeRange.startDate);
                    if (!isNaN(queriedDate.getTime())) {
                        headerDate = queriedDate;
                        isSpecificDate = true;
                    }
                }
            }
            const timestamp = headerDate.toLocaleString(opts.language === 'de' ? 'de-DE' : 'en-US');
            const dateLabel = isSpecificDate
                ? (opts.language === 'de' ? 'Daten für' : 'Data for')
                : (opts.language === 'de' ? 'Stand' : 'As of');
            context += opts.language === 'de'
                ? `SYSTEMDATEN-ÜBERSICHT (${dateLabel}: ${timestamp})\n\n`
                : `SYSTEM DATA OVERVIEW (${dateLabel}: ${timestamp})\n\n`;
        }
        // Format each data source
        successfulResults.forEach(result => {
            const template = this.formatQueryResult(result, opts);
            context += `${template.title}\n`;
            context += `${template.summary}\n`;
            if (opts.includeTrends && template.trends) {
                context += `${template.trends}\n`;
            }
            if (opts.includeDetails && template.details) {
                context += `${template.details}\n`;
            }
            if (opts.includeFreshness && template.freshness) {
                context += `${template.freshness}\n`;
            }
            context += '\n';
        });
        // Add multi-source summary if multiple data sources
        if (successfulResults.length > 1) {
            const multiSourceSummary = this.generateMultiSourceSummary(successfulResults, opts);
            context += this.formatMultiSourceSummary(multiSourceSummary, opts);
        }
        // Truncate if too long
        if (opts.maxLength && context.length > opts.maxLength) {
            context = context.substring(0, opts.maxLength - 100) +
                (opts.language === 'de' ? '...\n[Ausgabe gekürzt]' : '...\n[Output truncated]');
        }
        return context;
    }
    /**
     * Generate summary for complex multi-source queries
     */
    generateMultiSourceSummary(queryResults, options = {}) {
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        const dataTypes = queryResults.map(r => r.dataType);
        // Analyze overall system status
        const overallStatus = this.analyzeOverallStatus(queryResults, opts);
        // Extract key insights
        const keyInsights = this.extractKeyInsights(queryResults, opts);
        // Perform cross-system analysis
        const crossSystemAnalysis = this.performCrossSystemAnalysis(queryResults, opts);
        // Generate recommendations
        const recommendations = this.generateRecommendations(queryResults, opts);
        // Calculate data freshness
        const dataFreshness = this.calculateOverallDataFreshness(queryResults, opts);
        return {
            overallStatus,
            keyInsights,
            crossSystemAnalysis,
            recommendations,
            dataFreshness,
            timestamp: new Date()
        };
    }
    // Private helper methods for detailed formatting
    formatStoerungenDetails(data, options) {
        const opts = options;
        let details = '';
        if (opts.language === 'de') {
            details += `Systemstatus: ${data.systemStatus.length} Systeme überwacht\n`;
            const errorSystems = data.systemStatus.filter(s => s.status === 'ERROR');
            const warningSystems = data.systemStatus.filter(s => s.status === 'WARNING');
            if (errorSystems.length > 0) {
                details += `Fehlerhafte Systeme: ${errorSystems.map(s => s.system_name).join(', ')}\n`;
            }
            if (warningSystems.length > 0) {
                details += `Systeme mit Warnungen: ${warningSystems.map(s => s.system_name).join(', ')}\n`;
            }
            if (data.recentIncidents.length > 0) {
                details += `Aktuelle Vorfälle (${data.recentIncidents.length}):\n`;
                data.recentIncidents.slice(0, 3).forEach(incident => {
                    details += `- ${incident.title} (${incident.severity}, ${incident.status})\n`;
                });
            }
        }
        else {
            details += `System status: ${data.systemStatus.length} systems monitored\n`;
            const errorSystems = data.systemStatus.filter(s => s.status === 'ERROR');
            const warningSystems = data.systemStatus.filter(s => s.status === 'WARNING');
            if (errorSystems.length > 0) {
                details += `Systems with errors: ${errorSystems.map(s => s.system_name).join(', ')}\n`;
            }
            if (warningSystems.length > 0) {
                details += `Systems with warnings: ${warningSystems.map(s => s.system_name).join(', ')}\n`;
            }
            if (data.recentIncidents.length > 0) {
                details += `Recent incidents (${data.recentIncidents.length}):\n`;
                data.recentIncidents.slice(0, 3).forEach(incident => {
                    details += `- ${incident.title} (${incident.severity}, ${incident.status})\n`;
                });
            }
        }
        return details;
    }
    formatStoerungenTrends(trends, options) {
        const opts = options;
        let trendsText = '';
        if (opts.language === 'de') {
            trendsText += `Trends: MTTR ${trends.mttrTrend.changePercent > 0 ? 'gestiegen' : 'gesunken'} ` +
                `um ${Math.abs(trends.mttrTrend.changePercent).toFixed(1)}%. `;
            trendsText += `Auflösungsrate 24h: ${trends.resolutionRate.last24h}. `;
            const criticalTrend = trends.severityTrends.critical.trend;
            if (criticalTrend !== 'stable') {
                trendsText += `Kritische Störungen: ${criticalTrend === 'up' ? 'steigend' : 'fallend'}. `;
            }
        }
        else {
            trendsText += `Trends: MTTR ${trends.mttrTrend.changePercent > 0 ? 'increased' : 'decreased'} ` +
                `by ${Math.abs(trends.mttrTrend.changePercent).toFixed(1)}%. `;
            trendsText += `Resolution rate 24h: ${trends.resolutionRate.last24h}. `;
            const criticalTrend = trends.severityTrends.critical.trend;
            if (criticalTrend !== 'stable') {
                trendsText += `Critical incidents: ${criticalTrend === 'up' ? 'increasing' : 'decreasing'}. `;
            }
        }
        return trendsText;
    }
    formatDispatchDetails(data, options) {
        const opts = options;
        let details = '';
        if (opts.language === 'de') {
            details += `Kommissionierung: ATRL ${data.trends.pickingEfficiency.atrlAverage.toFixed(1)}, ` +
                `ARIL ${data.trends.pickingEfficiency.arilAverage.toFixed(1)}, ` +
                `Füllgrad ${data.trends.pickingEfficiency.fuellgradAverage.toFixed(1)}%\n`;
            details += `Lieferleistung: ${data.trends.deliveryPerformance.deliveredTotal} LUP ausgeliefert, ` +
                `${data.trends.deliveryPerformance.backlogTotal} LUP rückständig\n`;
            if (data.returnsData.length > 0) {
                details += `Retouren: `;
                data.returnsData.forEach(item => {
                    details += `${item.name} ${item.value}, `;
                });
                details = details.slice(0, -2) + '\n'; // Remove last comma
            }
            if (data.topPerformanceDays.length > 0) {
                details += `Top-Leistungstage:\n`;
                data.topPerformanceDays.slice(0, 3).forEach(day => {
                    details += `- ${day.datum}: ${day.produzierte_tonnagen.toFixed(1)}t, ${(day.servicegrad * 100).toFixed(1)}%\n`;
                });
            }
        }
        else {
            details += `Picking: ATRL ${data.trends.pickingEfficiency.atrlAverage.toFixed(1)}, ` +
                `ARIL ${data.trends.pickingEfficiency.arilAverage.toFixed(1)}, ` +
                `Fill rate ${data.trends.pickingEfficiency.fuellgradAverage.toFixed(1)}%\n`;
            details += `Delivery performance: ${data.trends.deliveryPerformance.deliveredTotal} LUP delivered, ` +
                `${data.trends.deliveryPerformance.backlogTotal} LUP backlog\n`;
            if (data.returnsData.length > 0) {
                details += `Returns: `;
                data.returnsData.forEach(item => {
                    details += `${item.name} ${item.value}, `;
                });
                details = details.slice(0, -2) + '\n'; // Remove last comma
            }
            if (data.topPerformanceDays.length > 0) {
                details += `Top performance days:\n`;
                data.topPerformanceDays.slice(0, 3).forEach(day => {
                    details += `- ${day.datum}: ${day.produzierte_tonnagen.toFixed(1)}t, ${day.servicegrad.toFixed(1)}%\n`;
                });
            }
        }
        return details;
    }
    formatDispatchTrends(trends, options) {
        const opts = options;
        let trendsText = '';
        if (opts.language === 'de') {
            const serviceTrend = trends.serviceLevelTrend.trend;
            trendsText += `Trends: Servicegrad ${serviceTrend === 'up' ? 'steigend' : serviceTrend === 'down' ? 'fallend' : 'stabil'} ` +
                `(${Math.abs(trends.serviceLevelTrend.changePercent).toFixed(1)}%). `;
            const tonnageTrend = trends.tonnageTrend.trend;
            trendsText += `Tonnage ${tonnageTrend === 'up' ? 'steigend' : tonnageTrend === 'down' ? 'fallend' : 'stabil'}. `;
            trendsText += `Kommissionierungs-Effizienz: ${this.getEfficiencyDisplayName(trends.pickingEfficiency.efficiency, opts.language)}. `;
            trendsText += `Lieferleistung: ${this.getPerformanceDisplayName(trends.deliveryPerformance.performance, opts.language)}.`;
        }
        else {
            const serviceTrend = trends.serviceLevelTrend.trend;
            trendsText += `Trends: Service level ${serviceTrend === 'up' ? 'increasing' : serviceTrend === 'down' ? 'decreasing' : 'stable'} ` +
                `(${Math.abs(trends.serviceLevelTrend.changePercent).toFixed(1)}%). `;
            const tonnageTrend = trends.tonnageTrend.trend;
            trendsText += `Tonnage ${tonnageTrend === 'up' ? 'increasing' : tonnageTrend === 'down' ? 'decreasing' : 'stable'}. `;
            trendsText += `Picking efficiency: ${this.getEfficiencyDisplayName(trends.pickingEfficiency.efficiency, opts.language)}. `;
            trendsText += `Delivery performance: ${this.getPerformanceDisplayName(trends.deliveryPerformance.performance, opts.language)}.`;
        }
        return trendsText;
    }
    formatCuttingDetails(data, options) {
        const opts = options;
        let details = '';
        if (opts.language === 'de') {
            details += `Schnitt-Aufschlüsselung: TT ${data.performanceOverview.totalCuts.cutTT}, ` +
                `TR ${data.performanceOverview.totalCuts.cutTR}, ` +
                `RR ${data.performanceOverview.totalCuts.cutRR}, ` +
                `Pick ${data.performanceOverview.totalCuts.pickCut}\n`;
            details += `Lager-Schnitte: Lager 200: ${data.warehouseCuts.lager200.total}, ` +
                `Lager 220: ${data.warehouseCuts.lager220.total}, ` +
                `Lager 240: ${data.warehouseCuts.lager240.total}\n`;
            if (data.topMachines.length > 0) {
                details += `Top-Maschinen:\n`;
                data.topMachines.slice(0, 3).forEach(machine => {
                    details += `- ${machine.name}: ${machine.averageEfficiency.toFixed(1)}% Effizienz, ${machine.totalCuts} Schnitte\n`;
                });
            }
            if (data.machineUtilization.length > 0) {
                const activeUtilization = data.machineUtilization.filter(m => m.utilizationRate > 0);
                details += `Maschinen-Auslastung: ${activeUtilization.length}/${data.machineUtilization.length} aktiv\n`;
            }
        }
        else {
            details += `Cut breakdown: TT ${data.performanceOverview.totalCuts.cutTT}, ` +
                `TR ${data.performanceOverview.totalCuts.cutTR}, ` +
                `RR ${data.performanceOverview.totalCuts.cutRR}, ` +
                `Pick ${data.performanceOverview.totalCuts.pickCut}\n`;
            details += `Warehouse cuts: Warehouse 200: ${data.warehouseCuts.lager200.total}, ` +
                `Warehouse 220: ${data.warehouseCuts.lager220.total}, ` +
                `Warehouse 240: ${data.warehouseCuts.lager240.total}\n`;
            if (data.topMachines.length > 0) {
                details += `Top machines:\n`;
                data.topMachines.slice(0, 3).forEach(machine => {
                    details += `- ${machine.name}: ${machine.averageEfficiency.toFixed(1)}% efficiency, ${machine.totalCuts} cuts\n`;
                });
            }
            if (data.machineUtilization.length > 0) {
                const activeUtilization = data.machineUtilization.filter(m => m.utilizationRate > 0);
                details += `Machine utilization: ${activeUtilization.length}/${data.machineUtilization.length} active\n`;
            }
        }
        return details;
    }
    formatCuttingTrends(trends, options) {
        const opts = options;
        let trendsText = '';
        if (opts.language === 'de') {
            const efficiencyTrend = trends.efficiencyTrend.trend;
            trendsText += `Trends: Effizienz ${efficiencyTrend === 'up' ? 'steigend' : efficiencyTrend === 'down' ? 'fallend' : 'stabil'} ` +
                `(${Math.abs(trends.efficiencyTrend.changePercent).toFixed(1)}%). `;
            const volumeTrend = trends.volumeTrend.trend;
            trendsText += `Volumen ${volumeTrend === 'up' ? 'steigend' : volumeTrend === 'down' ? 'fallend' : 'stabil'}. `;
            trendsText += `Top-Performer: ${trends.machinePerformance.topPerformer}. `;
            trendsText += `Aktive Maschinen: ${(trends.machinePerformance.activePercentage * 100).toFixed(0)}%. `;
            trendsText += `Beste Lager-Kategorie: ${trends.warehousePerformance.bestCategory}.`;
        }
        else {
            const efficiencyTrend = trends.efficiencyTrend.trend;
            trendsText += `Trends: Efficiency ${efficiencyTrend === 'up' ? 'increasing' : efficiencyTrend === 'down' ? 'decreasing' : 'stable'} ` +
                `(${Math.abs(trends.efficiencyTrend.changePercent).toFixed(1)}%). `;
            const volumeTrend = trends.volumeTrend.trend;
            trendsText += `Volume ${volumeTrend === 'up' ? 'increasing' : volumeTrend === 'down' ? 'decreasing' : 'stable'}. `;
            trendsText += `Top performer: ${trends.machinePerformance.topPerformer}. `;
            trendsText += `Active machines: ${(trends.machinePerformance.activePercentage * 100).toFixed(0)}%. `;
            trendsText += `Best warehouse category: ${trends.warehousePerformance.bestCategory}.`;
        }
        return trendsText;
    }
    formatQueryResult(result, options) {
        // This method would need to be enhanced to handle different data types
        // For now, return a basic template
        return {
            title: result.dataType.toUpperCase(),
            summary: result.summary,
            details: '',
            trends: '',
            timestamp: this.formatTimestamp(result.timestamp, options),
            freshness: this.calculateDataFreshness(result.timestamp, options)
        };
    }
    formatTimestamp(timestamp, options) {
        const opts = options;
        const locale = opts.language === 'de' ? 'de-DE' : 'en-US';
        return timestamp.toLocaleString(locale, {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    calculateDataFreshness(timestamp, options) {
        const opts = options;
        const now = new Date();
        const diffMs = now.getTime() - timestamp.getTime();
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);
        if (opts.language === 'de') {
            if (diffMinutes < 1)
                return 'Gerade aktualisiert';
            if (diffMinutes < 60)
                return `Vor ${diffMinutes} Minute${diffMinutes > 1 ? 'n' : ''}`;
            if (diffHours < 24)
                return `Vor ${diffHours} Stunde${diffHours > 1 ? 'n' : ''}`;
            return `Vor ${diffDays} Tag${diffDays > 1 ? 'en' : ''}`;
        }
        else {
            if (diffMinutes < 1)
                return 'Just updated';
            if (diffMinutes < 60)
                return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
            if (diffHours < 24)
                return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
            return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        }
    }
    analyzeOverallStatus(queryResults, options) {
        const opts = options;
        const successCount = queryResults.filter(r => r.success).length;
        const totalCount = queryResults.length;
        if (successCount === totalCount) {
            return opts.language === 'de'
                ? 'Alle Systeme operativ und Daten verfügbar'
                : 'All systems operational and data available';
        }
        else if (successCount > totalCount / 2) {
            return opts.language === 'de'
                ? 'Meiste Systeme operativ, einige Datenlücken'
                : 'Most systems operational, some data gaps';
        }
        else {
            return opts.language === 'de'
                ? 'Mehrere Systeme mit Datenproblemen'
                : 'Multiple systems with data issues';
        }
    }
    extractKeyInsights(queryResults, options) {
        const opts = options;
        const insights = [];
        queryResults.forEach(result => {
            var _a;
            if (!result.success)
                return;
            // Extract key insights from each data type
            if (result.dataType === 'stoerungen' && result.data) {
                const stats = result.data.statistics || result.data.stats;
                if ((stats === null || stats === void 0 ? void 0 : stats.critical) > 0) {
                    insights.push(opts.language === 'de'
                        ? `${stats.critical} kritische Störungen aktiv`
                        : `${stats.critical} critical incidents active`);
                }
                const healthScore = (_a = result.data.trends) === null || _a === void 0 ? void 0 : _a.systemHealthScore;
                if (healthScore !== undefined && healthScore < 80) {
                    insights.push(opts.language === 'de'
                        ? `Systemgesundheit bei ${healthScore}% - Aufmerksamkeit erforderlich`
                        : `System health at ${healthScore}% - attention required`);
                }
            }
            if (result.dataType === 'dispatch' && result.data) {
                const metrics = result.data.metrics || result.data.performanceMetrics;
                if ((metrics === null || metrics === void 0 ? void 0 : metrics.averageServiceLevel) < 90) {
                    insights.push(opts.language === 'de'
                        ? `Servicegrad unter 90% (${metrics.averageServiceLevel.toFixed(1)}%)`
                        : `Service level below 90% (${metrics.averageServiceLevel.toFixed(1)}%)`);
                }
            }
            if (result.dataType === 'cutting' && result.data) {
                const overview = result.data.overview;
                if ((overview === null || overview === void 0 ? void 0 : overview.averageEfficiency) > 100) {
                    insights.push(opts.language === 'de'
                        ? `Schnitt-Effizienz über 100% (${overview.averageEfficiency.toFixed(1)}%)`
                        : `Cutting efficiency above 100% (${overview.averageEfficiency.toFixed(1)}%)`);
                }
            }
        });
        return insights;
    }
    performCrossSystemAnalysis(queryResults, options) {
        var _a, _b, _c, _d, _e, _f, _g;
        const opts = options;
        const stoerungenResult = queryResults.find(r => r.dataType === 'stoerungen' && r.success);
        const dispatchResult = queryResults.find(r => r.dataType === 'dispatch' && r.success);
        const cuttingResult = queryResults.find(r => r.dataType === 'cutting' && r.success);
        const analyses = [];
        // Analyze correlation between incidents and service level
        if ((stoerungenResult === null || stoerungenResult === void 0 ? void 0 : stoerungenResult.data) && (dispatchResult === null || dispatchResult === void 0 ? void 0 : dispatchResult.data)) {
            const activeIncidents = ((_a = stoerungenResult.data.statistics) === null || _a === void 0 ? void 0 : _a.active) || ((_b = stoerungenResult.data.stats) === null || _b === void 0 ? void 0 : _b.active) || 0;
            const serviceLevel = ((_c = dispatchResult.data.metrics) === null || _c === void 0 ? void 0 : _c.averageServiceLevel) ||
                ((_d = dispatchResult.data.performanceMetrics) === null || _d === void 0 ? void 0 : _d.averageServiceLevel) || 100;
            if (activeIncidents > 3 && serviceLevel < 95) {
                analyses.push(opts.language === 'de'
                    ? 'Hohe Störungsanzahl korreliert mit reduziertem Servicegrad'
                    : 'High incident count correlates with reduced service level');
            }
        }
        // Analyze cutting efficiency impact on dispatch
        if ((cuttingResult === null || cuttingResult === void 0 ? void 0 : cuttingResult.data) && (dispatchResult === null || dispatchResult === void 0 ? void 0 : dispatchResult.data)) {
            const cuttingEfficiency = ((_e = cuttingResult.data.overview) === null || _e === void 0 ? void 0 : _e.averageEfficiency) || 0;
            const tonnage = ((_f = dispatchResult.data.metrics) === null || _f === void 0 ? void 0 : _f.totalTonnage) ||
                ((_g = dispatchResult.data.performanceMetrics) === null || _g === void 0 ? void 0 : _g.totalTonnage) || 0;
            if (cuttingEfficiency > 100 && tonnage > 1000) {
                analyses.push(opts.language === 'de'
                    ? 'Hohe Schnitt-Effizienz unterstützt starke Versandleistung'
                    : 'High cutting efficiency supports strong dispatch performance');
            }
        }
        if (analyses.length === 0) {
            analyses.push(opts.language === 'de'
                ? 'Systeme arbeiten unabhängig im normalen Bereich'
                : 'Systems operating independently within normal ranges');
        }
        return analyses.join('. ');
    }
    generateRecommendations(queryResults, options) {
        const opts = options;
        const recommendations = [];
        queryResults.forEach(result => {
            var _a, _b, _c, _d, _e;
            if (!result.success)
                return;
            if (result.dataType === 'stoerungen' && result.data) {
                const stats = result.data.statistics || result.data.stats;
                if ((stats === null || stats === void 0 ? void 0 : stats.critical) > 0) {
                    recommendations.push(opts.language === 'de'
                        ? 'Sofortige Bearbeitung kritischer Störungen priorisieren'
                        : 'Prioritize immediate handling of critical incidents');
                }
                const healthScore = (_a = result.data.trends) === null || _a === void 0 ? void 0 : _a.systemHealthScore;
                if (healthScore !== undefined && healthScore < 70) {
                    recommendations.push(opts.language === 'de'
                        ? 'Systemwartung und präventive Maßnahmen einleiten'
                        : 'Initiate system maintenance and preventive measures');
                }
            }
            if (result.dataType === 'dispatch' && result.data) {
                const trends = result.data.trends;
                if (((_b = trends === null || trends === void 0 ? void 0 : trends.serviceLevelTrend) === null || _b === void 0 ? void 0 : _b.trend) === 'down') {
                    recommendations.push(opts.language === 'de'
                        ? 'Servicegrad-Verbesserungsmaßnahmen implementieren'
                        : 'Implement service level improvement measures');
                }
                if (((_c = trends === null || trends === void 0 ? void 0 : trends.pickingEfficiency) === null || _c === void 0 ? void 0 : _c.efficiency) === 'poor') {
                    recommendations.push(opts.language === 'de'
                        ? 'Kommissionierungsprozesse optimieren'
                        : 'Optimize picking processes');
                }
            }
            if (result.dataType === 'cutting' && result.data) {
                const trends = result.data.trends;
                if (((_d = trends === null || trends === void 0 ? void 0 : trends.efficiencyTrend) === null || _d === void 0 ? void 0 : _d.trend) === 'down') {
                    recommendations.push(opts.language === 'de'
                        ? 'Maschinen-Effizienz analysieren und verbessern'
                        : 'Analyze and improve machine efficiency');
                }
                if (((_e = trends === null || trends === void 0 ? void 0 : trends.machinePerformance) === null || _e === void 0 ? void 0 : _e.activePercentage) < 0.8) {
                    recommendations.push(opts.language === 'de'
                        ? 'Maschinen-Auslastung erhöhen'
                        : 'Increase machine utilization');
                }
            }
        });
        return recommendations;
    }
    calculateOverallDataFreshness(queryResults, options) {
        const opts = options;
        const timestamps = queryResults.map(r => r.timestamp);
        const oldestTimestamp = new Date(Math.min(...timestamps.map(t => t.getTime())));
        const newestTimestamp = new Date(Math.max(...timestamps.map(t => t.getTime())));
        const now = new Date();
        const oldestDiffMinutes = Math.floor((now.getTime() - oldestTimestamp.getTime()) / (1000 * 60));
        const newestDiffMinutes = Math.floor((now.getTime() - newestTimestamp.getTime()) / (1000 * 60));
        if (opts.language === 'de') {
            if (newestDiffMinutes < 5) {
                return 'Daten sind aktuell (< 5 Min)';
            }
            else if (oldestDiffMinutes < 60) {
                return `Daten sind frisch (${oldestDiffMinutes}-${newestDiffMinutes} Min alt)`;
            }
            else {
                return `Daten sind älter (${Math.floor(oldestDiffMinutes / 60)}-${Math.floor(newestDiffMinutes / 60)} Std alt)`;
            }
        }
        else {
            if (newestDiffMinutes < 5) {
                return 'Data is current (< 5 min)';
            }
            else if (oldestDiffMinutes < 60) {
                return `Data is fresh (${oldestDiffMinutes}-${newestDiffMinutes} min old)`;
            }
            else {
                return `Data is older (${Math.floor(oldestDiffMinutes / 60)}-${Math.floor(newestDiffMinutes / 60)} hrs old)`;
            }
        }
    }
    formatMultiSourceSummary(summary, options) {
        const opts = options;
        let formatted = '';
        if (opts.language === 'de') {
            formatted += 'SYSTEMÜBERGREIFENDE ANALYSE:\n';
            formatted += `Status: ${summary.overallStatus}\n`;
            if (summary.keyInsights.length > 0) {
                formatted += `Wichtige Erkenntnisse: ${summary.keyInsights.join(', ')}\n`;
            }
            formatted += `Analyse: ${summary.crossSystemAnalysis}\n`;
            if (summary.recommendations.length > 0) {
                formatted += `Empfehlungen: ${summary.recommendations.join('; ')}\n`;
            }
            formatted += `Datenaktualität: ${summary.dataFreshness}\n`;
        }
        else {
            formatted += 'CROSS-SYSTEM ANALYSIS:\n';
            formatted += `Status: ${summary.overallStatus}\n`;
            if (summary.keyInsights.length > 0) {
                formatted += `Key insights: ${summary.keyInsights.join(', ')}\n`;
            }
            formatted += `Analysis: ${summary.crossSystemAnalysis}\n`;
            if (summary.recommendations.length > 0) {
                formatted += `Recommendations: ${summary.recommendations.join('; ')}\n`;
            }
            formatted += `Data freshness: ${summary.dataFreshness}\n`;
        }
        return formatted;
    }
    getEfficiencyDisplayName(efficiency, language = 'de') {
        if (language === 'de') {
            const efficiencyMap = {
                'excellent': 'Ausgezeichnet',
                'good': 'Gut',
                'average': 'Durchschnittlich',
                'poor': 'Verbesserungsbedürftig'
            };
            return efficiencyMap[efficiency];
        }
        else {
            const efficiencyMap = {
                'excellent': 'Excellent',
                'good': 'Good',
                'average': 'Average',
                'poor': 'Poor'
            };
            return efficiencyMap[efficiency];
        }
    }
    getPerformanceDisplayName(performance, language = 'de') {
        if (language === 'de') {
            const performanceMap = {
                'excellent': 'Ausgezeichnet',
                'good': 'Gut',
                'average': 'Durchschnittlich',
                'poor': 'Verbesserungsbedürftig'
            };
            return performanceMap[performance];
        }
        else {
            const performanceMap = {
                'excellent': 'Excellent',
                'good': 'Good',
                'average': 'Average',
                'poor': 'Poor'
            };
            return performanceMap[performance];
        }
    }
}
exports.DataFormattingService = DataFormattingService;
exports.default = DataFormattingService;
