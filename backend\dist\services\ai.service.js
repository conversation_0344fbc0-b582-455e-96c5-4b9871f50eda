"use strict";
/**
 * AIService - Erweiterte AI-Funktionen für SFM Dashboard
 *
 * Bietet intelligente Datenanalyse, Anomalie-Erkennung,
 * Predictive Analytics und Prozessoptimierung
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const client_1 = require("@prisma-sfm-dashboard/client");
class AIService {
    constructor() {
        this.prisma = new client_1.PrismaClient();
    }
    /**
     * Anomalie-Erkennung in Produktionsdaten
     */
    async detectAnomalies(timeframe = 7) {
        const anomalies = [];
        const now = new Date();
        const startDate = new Date(now.getTime() - timeframe * 24 * 60 * 60 * 1000);
        try {
            // 1. Lagerauslastung Anomalien
            const warehouseAnomalies = await this.detectWarehouseAnomalies();
            anomalies.push(...warehouseAnomalies);
            // 2. Schnitt-Performance Anomalien
            const cuttingAnomalies = await this.detectCuttingAnomalies(startDate);
            anomalies.push(...cuttingAnomalies);
            // 3. Dispatch/Servicegrad Anomalien
            const dispatchAnomalies = await this.detectDispatchAnomalies(startDate);
            anomalies.push(...dispatchAnomalies);
            // 4. System-Performance Anomalien
            const systemAnomalies = await this.detectSystemAnomalies();
            anomalies.push(...systemAnomalies);
            return anomalies.sort((a, b) => {
                const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
                return severityOrder[b.severity] - severityOrder[a.severity];
            });
        }
        catch (error) {
            console.error('Fehler bei Anomalie-Erkennung:', error);
            return [];
        }
    }
    /**
     * Lagerauslastung Anomalien
     */
    async detectWarehouseAnomalies() {
        const anomalies = [];
        try {
            // Aktuelle Auslastung abrufen
            const currentStock = await this.prisma.bestand200.findFirst({
                where: { auslastung: { not: null } },
                select: { auslastung: true, auslastungA: true, auslastungB: true, auslastungC: true }
            });
            if (currentStock) {
                const auslastung = parseFloat(currentStock.auslastung || '0');
                const auslastungA = parseFloat(currentStock.auslastungA || '0');
                // Kritische Überauslastung (>95%)
                if (auslastung > 0.95) {
                    anomalies.push({
                        type: 'warehouse',
                        severity: 'critical',
                        description: 'Lager kritisch überausgelastet',
                        value: auslastung,
                        threshold: 0.95,
                        timestamp: new Date(),
                        recommendation: 'Sofortige Umlagerung oder Auslagerung erforderlich'
                    });
                }
                // A-Artikel Überauslastung (>98%)
                if (auslastungA > 0.98) {
                    anomalies.push({
                        type: 'warehouse',
                        severity: 'high',
                        description: 'A-Artikel Bereich überausgelastet',
                        value: auslastungA,
                        threshold: 0.98,
                        timestamp: new Date(),
                        recommendation: 'Prioritäre A-Artikel umlagern oder auslagern'
                    });
                }
                // Unterauslastung (<20%)
                if (auslastung < 0.20) {
                    anomalies.push({
                        type: 'warehouse',
                        severity: 'medium',
                        description: 'Lager stark unterausgelastet',
                        value: auslastung,
                        threshold: 0.20,
                        timestamp: new Date(),
                        recommendation: 'Lagerplatz-Konsolidierung prüfen'
                    });
                }
            }
        }
        catch (error) {
            console.error('Fehler bei Lager-Anomalie-Erkennung:', error);
        }
        return anomalies;
    }
    /**
     * Schnitt-Performance Anomalien
     */
    async detectCuttingAnomalies(startDate) {
        const anomalies = [];
        try {
            // Aktuelle Schnittdaten der letzten 7 Tage
            const recentCuttings = await this.prisma.schnitte.findMany({
                where: {
                    Datum: { gte: startDate.toISOString().split('T')[0] }
                },
                select: { Sum_H1: true, Sum_H3: true, Datum: true }
            });
            if (recentCuttings.length > 0) {
                // Durchschnittliche tägliche Schnitte berechnen
                const totalCuts = recentCuttings.reduce((sum, cutting) => sum + (cutting.Sum_H1 || 0) + (cutting.Sum_H3 || 0), 0);
                const avgDailyCuts = totalCuts / recentCuttings.length;
                // Letzte Schnittdaten
                const latestCutting = recentCuttings[recentCuttings.length - 1];
                const latestTotal = (latestCutting.Sum_H1 || 0) + (latestCutting.Sum_H3 || 0);
                // Performance Drop (>30% unter Durchschnitt)
                if (latestTotal < avgDailyCuts * 0.7) {
                    anomalies.push({
                        type: 'cutting',
                        severity: 'high',
                        description: 'Schnittleistung deutlich unter Durchschnitt',
                        value: latestTotal,
                        threshold: avgDailyCuts * 0.7,
                        timestamp: new Date(),
                        recommendation: 'Maschinenauslastung und -wartung prüfen'
                    });
                }
                // Außergewöhnlich hohe Performance (>150% über Durchschnitt)
                if (latestTotal > avgDailyCuts * 1.5) {
                    anomalies.push({
                        type: 'cutting',
                        severity: 'medium',
                        description: 'Außergewöhnlich hohe Schnittleistung',
                        value: latestTotal,
                        threshold: avgDailyCuts * 1.5,
                        timestamp: new Date(),
                        recommendation: 'Qualitätskontrolle verstärken, Verschleiß prüfen'
                    });
                }
            }
        }
        catch (error) {
            console.error('Fehler bei Schnitt-Anomalie-Erkennung:', error);
        }
        return anomalies;
    }
    /**
     * Dispatch/Servicegrad Anomalien
     */
    async detectDispatchAnomalies(startDate) {
        const anomalies = [];
        try {
            const recentDispatch = await this.prisma.dispatchData.findMany({
                where: {
                    datum: { gte: startDate.toISOString().split('T')[0] }
                },
                select: { servicegrad: true, datum: true },
                orderBy: { datum: 'desc' },
                take: 7
            });
            if (recentDispatch.length > 0) {
                const latestServicegrad = recentDispatch[0].servicegrad;
                // Kritischer Servicegrad (<70%)
                if (latestServicegrad && latestServicegrad < 0.70) {
                    anomalies.push({
                        type: 'dispatch',
                        severity: 'critical',
                        description: 'Servicegrad kritisch niedrig',
                        value: latestServicegrad || 0,
                        threshold: 0.70,
                        timestamp: new Date(),
                        recommendation: 'Sofortige Kapazitätserhöhung oder Prozessoptimierung'
                    });
                }
                // Niederer Servicegrad (<85%)
                if (latestServicegrad && latestServicegrad < 0.85 && latestServicegrad >= 0.70) {
                    anomalies.push({
                        type: 'dispatch',
                        severity: 'medium',
                        description: 'Servicegrad unter Zielwert',
                        value: latestServicegrad || 0,
                        threshold: 0.85,
                        timestamp: new Date(),
                        recommendation: 'Engpässe in der Kommissionierung prüfen'
                    });
                }
            }
        }
        catch (error) {
            console.error('Fehler bei Dispatch-Anomalie-Erkennung:', error);
        }
        return anomalies;
    }
    /**
     * System-Performance Anomalien
     */
    async detectSystemAnomalies() {
        const anomalies = [];
        try {
            // Prüfe auf System-Störungen oder kritische Verfügbarkeit
            const systemData = await this.prisma.system.findFirst({
                orderBy: { Datum: 'desc' }
            });
            if (systemData) {
                // ATrL Verfügbarkeit
                const atrlVerfuegbarkeit = systemData.verfuegbarkeitAtrl_FT_RBG_MFR1 || 0;
                if (atrlVerfuegbarkeit < 0.90) {
                    anomalies.push({
                        type: 'system',
                        severity: 'high',
                        description: 'ATrL Systemverfügbarkeit niedrig',
                        value: atrlVerfuegbarkeit,
                        threshold: 0.90,
                        timestamp: new Date(),
                        recommendation: 'ATrL System-Check und Wartung einplanen'
                    });
                }
                // Gesamtverfügbarkeit
                const gesamtVerfuegbarkeit = systemData.gesamtverfuegbarkeit_AtrL_ARiL_FTS || 0;
                if (gesamtVerfuegbarkeit < 0.85) {
                    anomalies.push({
                        type: 'system',
                        severity: 'critical',
                        description: 'Gesamtsystem-Verfügbarkeit kritisch',
                        value: gesamtVerfuegbarkeit,
                        threshold: 0.85,
                        timestamp: new Date(),
                        recommendation: 'Sofortige Systemanalyse und Reparatur erforderlich'
                    });
                }
            }
        }
        catch (error) {
            console.error('Fehler bei System-Anomalie-Erkennung:', error);
        }
        return anomalies;
    }
    /**
     * Predictive Analytics für Bestandsengpässe
     */
    async predictInventoryShortage(days = 14) {
        var _a;
        const predictions = [];
        try {
            // Analysiere historische Bewegungsdaten
            const historicalMovements = await this.prisma.ablaengerei.findMany({
                take: 30,
                orderBy: { Datum: 'desc' }
            });
            // Berechne durchschnittliche tägliche Auslagerungen
            const avgDailyOutbound = historicalMovements.reduce((sum, movement) => sum + (movement.cutLagerK200 || 0) + (movement.cutLagerK220 || 0) + (movement.cutLagerK240 || 0), 0) / historicalMovements.length;
            // Aktuelle Lagerbestände
            const currentStock = await this.prisma.bestand200.aggregate({
                _sum: { Gesamtbestand: true }
            });
            const totalStock = currentStock._sum.Gesamtbestand || 0;
            const projectedDaysUntilEmpty = totalStock / avgDailyOutbound;
            if (projectedDaysUntilEmpty < days) {
                predictions.push({
                    type: 'inventory_shortage',
                    probability: Math.min(0.95, (days - projectedDaysUntilEmpty) / days),
                    daysUntil: Math.ceil(projectedDaysUntilEmpty),
                    description: `Bestandsengpass in ${Math.ceil(projectedDaysUntilEmpty)} Tagen erwartet`,
                    impact: projectedDaysUntilEmpty < 7 ? 'high' : 'medium',
                    preventionActions: [
                        'Eilbestellungen einleiten',
                        'Verbrauch reduzieren',
                        'Alternative Lieferanten kontaktieren',
                        'Lagerumschlag optimieren'
                    ]
                });
            }
            // Kapazitätsüberlastung vorhersagen
            const avgDailyInbound = historicalMovements.reduce((sum, movement) => sum + (movement.lagerCut200 || 0) + (movement.lagerCut220 || 0) + (movement.lagerCut240 || 0), 0) / historicalMovements.length;
            const currentUtilization = parseFloat(((_a = (await this.prisma.bestand200.findFirst({
                select: { auslastung: true }
            }))) === null || _a === void 0 ? void 0 : _a.auslastung) || '0');
            const netInflow = avgDailyInbound - avgDailyOutbound;
            if (netInflow > 0 && currentUtilization > 0.80) {
                const daysUntilOverload = (0.95 - currentUtilization) / (netInflow / totalStock);
                if (daysUntilOverload < days) {
                    predictions.push({
                        type: 'capacity_overload',
                        probability: Math.min(0.90, (days - daysUntilOverload) / days),
                        daysUntil: Math.ceil(daysUntilOverload),
                        description: `Lagerkapazität überlastet in ${Math.ceil(daysUntilOverload)} Tagen`,
                        impact: 'high',
                        preventionActions: [
                            'Auslagerungen verstärken',
                            'Umlagerungen einplanen',
                            'Zusätzliche Lagerkapazität schaffen',
                            'Eingangskontrolle verschärfen'
                        ]
                    });
                }
            }
        }
        catch (error) {
            console.error('Fehler bei Predictive Analytics:', error);
        }
        return predictions.sort((a, b) => b.probability - a.probability);
    }
    /**
     * Schnittoptimierung mit einfachem Bin-Packing
     */
    async suggestCuttingOptimization() {
        try {
            // Hole aktuelle Schnittdaten
            const recentCuttings = await this.prisma.schnitte.findMany({
                take: 7,
                orderBy: { Datum: 'desc' }
            });
            // Berechne aktuelle Effizienz
            const totalCuts = recentCuttings.reduce((sum, cutting) => sum + (cutting.Sum_H1 || 0) + (cutting.Sum_H3 || 0), 0);
            const avgDailyCuts = totalCuts / recentCuttings.length;
            // Hole Materialverbrauchsdaten
            const materialMovements = await this.prisma.ablaengerei.findMany({
                take: 7,
                orderBy: { Datum: 'desc' }
            });
            const avgMaterialUsage = materialMovements.reduce((sum, movement) => sum + (movement.cutGesamt || 0), 0) / materialMovements.length;
            // Berechne aktuelle Effizienz (Schnitte pro Material)
            const currentEfficiency = avgMaterialUsage > 0 ? (avgDailyCuts / avgMaterialUsage) * 100 : 0;
            // Optimierungsvorschläge basierend auf Analyse
            const actions = [];
            // Bin-Packing Optimierung
            if (currentEfficiency < 75) {
                actions.push({
                    priority: 'high',
                    action: 'Schnittmuster-Optimierung',
                    description: 'Implementierung von Bin-Packing Algorithmus für bessere Materialausnutzung',
                    effort: 'medium',
                    impact: 'high'
                });
            }
            // Maschinenauslastung
            const h1Ratio = recentCuttings.reduce((sum, c) => sum + (c.Sum_H1 || 0), 0) / totalCuts;
            const h3Ratio = recentCuttings.reduce((sum, c) => sum + (c.Sum_H3 || 0), 0) / totalCuts;
            if (Math.abs(h1Ratio - h3Ratio) > 0.3) {
                actions.push({
                    priority: 'medium',
                    action: 'Maschinenauslastung ausgleichen',
                    description: `H1: ${(h1Ratio * 100).toFixed(1)}%, H3: ${(h3Ratio * 100).toFixed(1)}% - Umverteilung optimieren`,
                    effort: 'low',
                    impact: 'medium'
                });
            }
            // Restlängen-Management
            actions.push({
                priority: 'medium',
                action: 'Restlängen-Datenbank',
                description: 'Systematische Erfassung und Wiederverwendung von Restlängen',
                effort: 'high',
                impact: 'high'
            });
            const potentialEfficiency = Math.min(95, currentEfficiency + 15); // Realistisches Optimierungspotential
            return {
                type: 'cutting',
                currentEfficiency: Math.round(currentEfficiency),
                potentialEfficiency: Math.round(potentialEfficiency),
                improvementPercent: Math.round(potentialEfficiency - currentEfficiency),
                actions,
                estimatedSavings: {
                    time: Math.round((potentialEfficiency - currentEfficiency) * 2), // Minuten pro Tag
                    material: Math.round((potentialEfficiency - currentEfficiency) * 0.5), // Prozent Materialeinsparung
                    cost: Math.round((potentialEfficiency - currentEfficiency) * 50) // Euro pro Tag
                }
            };
        }
        catch (error) {
            console.error('Fehler bei Schnittoptimierung:', error);
            return {
                type: 'cutting',
                currentEfficiency: 0,
                potentialEfficiency: 0,
                improvementPercent: 0,
                actions: [],
                estimatedSavings: { time: 0, material: 0, cost: 0 }
            };
        }
    }
    /**
     * Generiere intelligente Insights für Dashboard
     */
    async generateInsights(timeframe = 30) {
        const insights = [];
        try {
            // Performance Insights
            const cuttingInsight = await this.generateCuttingInsights(timeframe);
            if (cuttingInsight)
                insights.push(cuttingInsight);
            // Effizienz Insights
            const warehouseInsight = await this.generateWarehouseInsights();
            if (warehouseInsight)
                insights.push(warehouseInsight);
            // Qualitäts Insights
            const qualityInsight = await this.generateQualityInsights(timeframe);
            if (qualityInsight)
                insights.push(qualityInsight);
            // Kapazitäts Insights
            const capacityInsight = await this.generateCapacityInsights();
            if (capacityInsight)
                insights.push(capacityInsight);
        }
        catch (error) {
            console.error('Fehler bei Insight-Generierung:', error);
        }
        return insights.sort((a, b) => {
            const urgencyOrder = { high: 3, medium: 2, low: 1 };
            return urgencyOrder[b.urgency] - urgencyOrder[a.urgency];
        });
    }
    async generateCuttingInsights(timeframe) {
        try {
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - timeframe);
            const cuttingData = await this.prisma.schnitte.findMany({
                where: { Datum: { gte: startDate.toISOString().split('T')[0] } },
                select: { Sum_H1: true, Sum_H3: true, Datum: true }
            });
            if (cuttingData.length < 7)
                return null;
            const totalCuts = cuttingData.reduce((sum, c) => sum + (c.Sum_H1 || 0) + (c.Sum_H3 || 0), 0);
            const avgDaily = totalCuts / cuttingData.length;
            // Trend-Analyse
            const recent = cuttingData.slice(-7);
            const older = cuttingData.slice(0, Math.min(7, cuttingData.length - 7));
            const recentAvg = recent.reduce((sum, c) => sum + (c.Sum_H1 || 0) + (c.Sum_H3 || 0), 0) / recent.length;
            const olderAvg = older.length > 0 ?
                older.reduce((sum, c) => sum + (c.Sum_H1 || 0) + (c.Sum_H3 || 0), 0) / older.length : recentAvg;
            const trendPercent = ((recentAvg - olderAvg) / olderAvg) * 100;
            return {
                category: 'performance',
                title: 'Schnittleistung Trend',
                description: `Durchschnittlich ${Math.round(avgDaily)} Schnitte/Tag. Trend: ${trendPercent > 0 ? '+' : ''}${trendPercent.toFixed(1)}%`,
                trend: trendPercent > 5 ? 'positive' : trendPercent < -5 ? 'negative' : 'stable',
                urgency: Math.abs(trendPercent) > 15 ? 'high' : 'medium',
                actionRequired: Math.abs(trendPercent) > 20,
                recommendations: trendPercent < -10 ?
                    ['Maschinenauslastung prüfen', 'Wartungsplan überarbeiten', 'Personalplanung optimieren'] :
                    ['Aktuelle Performance beibehalten', 'Effizienzsteigerungen dokumentieren']
            };
        }
        catch (error) {
            console.error('Fehler bei Cutting Insights:', error);
            return null;
        }
    }
    async generateWarehouseInsights() {
        try {
            const warehouseData = await this.prisma.bestand200.findFirst({
                select: { auslastung: true, auslastungA: true, auslastungB: true, auslastungC: true }
            });
            if (!warehouseData)
                return null;
            const utilization = parseFloat(warehouseData.auslastung || '0');
            const utilizationA = parseFloat(warehouseData.auslastungA || '0');
            let urgency = 'low';
            let recommendations = [];
            if (utilization > 0.90) {
                urgency = 'high';
                recommendations = ['Sofortige Auslagerung einplanen', 'Umlagerungen priorisieren', 'Zusätzliche Kapazität schaffen'];
            }
            else if (utilization > 0.80) {
                urgency = 'medium';
                recommendations = ['Auslagerungen verstärken', 'Bestandsoptimierung prüfen'];
            }
            else {
                recommendations = ['Aktuelle Auslastung optimal', 'Kontinuierliches Monitoring'];
            }
            return {
                category: 'efficiency',
                title: 'Lagerauslastung Status',
                description: `Gesamtauslastung: ${(utilization * 100).toFixed(1)}%, A-Artikel: ${(utilizationA * 100).toFixed(1)}%`,
                trend: utilization > 0.85 ? 'negative' : utilization < 0.60 ? 'positive' : 'stable',
                urgency,
                actionRequired: utilization > 0.90,
                recommendations
            };
        }
        catch (error) {
            console.error('Fehler bei Warehouse Insights:', error);
            return null;
        }
    }
    async generateQualityInsights(timeframe) {
        try {
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - timeframe);
            const dispatchData = await this.prisma.dispatchData.findMany({
                where: { datum: { gte: startDate.toISOString().split('T')[0] } },
                select: { servicegrad: true, qm_angenommen: true, qm_abgelehnt: true }
            });
            if (dispatchData.length === 0)
                return null;
            const avgServicegrad = dispatchData.reduce((sum, d) => sum + (d.servicegrad || 0), 0) / dispatchData.length;
            const totalQMAngenommen = dispatchData.reduce((sum, d) => sum + (d.qm_angenommen || 0), 0);
            const totalQMAbgelehnt = dispatchData.reduce((sum, d) => sum + (d.qm_abgelehnt || 0), 0);
            const qmRate = totalQMAngenommen + totalQMAbgelehnt > 0 ?
                totalQMAngenommen / (totalQMAngenommen + totalQMAbgelehnt) : 1;
            return {
                category: 'quality',
                title: 'Qualitäts-Performance',
                description: `Servicegrad: ${(avgServicegrad * 100).toFixed(1)}%, QM-Rate: ${(qmRate * 100).toFixed(1)}%`,
                trend: avgServicegrad > 0.90 ? 'positive' : avgServicegrad < 0.80 ? 'negative' : 'stable',
                urgency: avgServicegrad < 0.75 ? 'high' : 'medium',
                actionRequired: avgServicegrad < 0.80 || qmRate < 0.95,
                recommendations: avgServicegrad < 0.85 ?
                    ['Kommissionierungsprozess optimieren', 'Kapazitäten erhöhen', 'Qualitätskontrolle verstärken'] :
                    ['Aktuelle Qualität beibehalten', 'Kontinuierliche Verbesserung']
            };
        }
        catch (error) {
            console.error('Fehler bei Quality Insights:', error);
            return null;
        }
    }
    async generateCapacityInsights() {
        try {
            // Aktuelle Systemverfügbarkeit
            const systemData = await this.prisma.system.findFirst({
                orderBy: { Datum: 'desc' }
            });
            if (!systemData)
                return null;
            const gesamtVerfuegbarkeit = systemData.gesamtverfuegbarkeit_AtrL_ARiL_FTS || 0;
            const atrlVerfuegbarkeit = systemData.verfuegbarkeitAtrl_FT_RBG_MFR1 || 0;
            return {
                category: 'capacity',
                title: 'System-Kapazität',
                description: `Gesamtverfügbarkeit: ${(gesamtVerfuegbarkeit * 100).toFixed(1)}%, ATrL: ${(atrlVerfuegbarkeit * 100).toFixed(1)}%`,
                trend: gesamtVerfuegbarkeit > 0.95 ? 'positive' : gesamtVerfuegbarkeit < 0.85 ? 'negative' : 'stable',
                urgency: gesamtVerfuegbarkeit < 0.80 ? 'high' : gesamtVerfuegbarkeit < 0.90 ? 'medium' : 'low',
                actionRequired: gesamtVerfuegbarkeit < 0.85,
                recommendations: gesamtVerfuegbarkeit < 0.90 ?
                    ['Systemwartung priorisieren', 'Ausfallursachen analysieren', 'Backup-Systeme aktivieren'] :
                    ['Präventive Wartung beibehalten', 'Performance monitoring']
            };
        }
        catch (error) {
            console.error('Fehler bei Capacity Insights:', error);
            return null;
        }
    }
    /**
     * Cleanup Ressourcen
     */
    async disconnect() {
        await this.prisma.$disconnect();
    }
}
exports.AIService = AIService;
exports.default = AIService;
