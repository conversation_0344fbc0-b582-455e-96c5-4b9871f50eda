import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  HelpCircle, 
  X, 
  Search, 
  Book, 
  PlayCircle, 
  Settings,
  ChevronRight,
  Star,
  Clock,
  User
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAIDocumentation } from './AIDocumentationProvider';

interface AIHelpSystemProps {
  className?: string;
}

export const AIHelpSystem: React.FC<AIHelpSystemProps> = ({ className }) => {
  const { t } = useTranslation();
  const {
    showHelp,
    toggleHelp,
    sections,
    tutorials,
    searchDocumentation,
    getDocumentationByCategory,
    getContextualHelp,
    startTutorial,
    getUserProgress,
    completedTutorials
  } = useAIDocumentation();

  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(sections);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (searchQuery.trim()) {
      setSearchResults(searchDocumentation(searchQuery));
    } else {
      setSearchResults(sections);
    }
  }, [searchQuery, searchDocumentation, sections]);

  const contextualHelp = getContextualHelp();
  const progress = getUserProgress();

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatEstimatedTime = (minutes: number) => {
    if (minutes < 60) {
      return t('ai.help.time.minutes', { count: minutes });
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) {
      return t('ai.help.time.hours', { count: hours });
    }
    return t('ai.help.time.hoursMinutes', { hours, minutes: remainingMinutes });
  };

  return (
    <>
      {/* Help Toggle Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={toggleHelp}
        className={`fixed bottom-4 right-4 z-50 shadow-lg ${className}`}
        title={t('ai.help.toggle')}
      >
        <HelpCircle className="h-4 w-4" />
      </Button>

      {/* Help Panel */}
      <AnimatePresence>
        {showHelp && (
          <motion.div
            initial={{ opacity: 0, x: 400 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 400 }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-96 bg-white shadow-2xl z-40 border-l border-gray-200"
          >
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <div className="flex items-center gap-2">
                  <Book className="h-5 w-5 text-blue-600" />
                  <h2 className="text-lg font-semibold text-gray-900">
                    {t('ai.help.title')}
                  </h2>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleHelp}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* Search */}
              <div className="p-4 border-b border-gray-200">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder={t('ai.help.search.placeholder')}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
                    <TabsTrigger value="overview" className="text-xs">
                      {t('ai.help.tabs.overview')}
                    </TabsTrigger>
                    <TabsTrigger value="tutorials" className="text-xs">
                      {t('ai.help.tabs.tutorials')}
                    </TabsTrigger>
                    <TabsTrigger value="docs" className="text-xs">
                      {t('ai.help.tabs.docs')}
                    </TabsTrigger>
                    <TabsTrigger value="api" className="text-xs">
                      {t('ai.help.tabs.api')}
                    </TabsTrigger>
                  </TabsList>

                  <div className="p-4">
                    <TabsContent value="overview" className="mt-0">
                      {/* Progress Overview */}
                      <Card className="mb-4">
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm flex items-center gap-2">
                            <User className="h-4 w-4" />
                            {t('ai.help.progress.title')}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center justify-between text-sm">
                            <span>{t('ai.help.progress.completed')}</span>
                            <span className="font-medium">
                              {progress.completed}/{progress.total}
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{
                                width: `${progress.total > 0 ? (progress.completed / progress.total) * 100 : 0}%`
                              }}
                            />
                          </div>
                        </CardContent>
                      </Card>

                      {/* Contextual Help */}
                      {contextualHelp.length > 0 && (
                        <Card className="mb-4">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-sm">
                              {t('ai.help.contextual.title')}
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-2">
                            {contextualHelp.slice(0, 3).map((section) => (
                              <div
                                key={section.id}
                                className="p-2 rounded-lg bg-blue-50 border border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors"
                              >
                                <h4 className="text-sm font-medium text-blue-900">
                                  {section.title}
                                </h4>
                                <p className="text-xs text-blue-700 mt-1 line-clamp-2">
                                  {section.content.substring(0, 100)}...
                                </p>
                              </div>
                            ))}
                          </CardContent>
                        </Card>
                      )}

                      {/* Quick Actions */}
                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm">
                            {t('ai.help.quickActions.title')}
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-start"
                            onClick={() => setActiveTab('tutorials')}
                          >
                            <PlayCircle className="h-4 w-4 mr-2" />
                            {t('ai.help.quickActions.startTutorial')}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-start"
                            onClick={() => setActiveTab('docs')}
                          >
                            <Book className="h-4 w-4 mr-2" />
                            {t('ai.help.quickActions.browseGuides')}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-start"
                            onClick={() => setActiveTab('api')}
                          >
                            <Settings className="h-4 w-4 mr-2" />
                            {t('ai.help.quickActions.apiDocs')}
                          </Button>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="tutorials" className="mt-0">
                      <div className="space-y-3">
                        {tutorials.map((tutorial) => (
                          <Card
                            key={tutorial.id}
                            className={`cursor-pointer transition-all hover:shadow-md ${
                              completedTutorials.includes(tutorial.id)
                                ? 'border-green-200 bg-green-50'
                                : 'hover:border-blue-200'
                            }`}
                            onClick={() => startTutorial(tutorial.id)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-2">
                                    <h3 className="text-sm font-medium">
                                      {tutorial.title}
                                    </h3>
                                    {completedTutorials.includes(tutorial.id) && (
                                      <Star className="h-4 w-4 text-green-600 fill-current" />
                                    )}
                                  </div>
                                  <p className="text-xs text-gray-600 mb-3">
                                    {tutorial.description}
                                  </p>
                                  <div className="flex items-center gap-2 flex-wrap">
                                    <Badge
                                      variant="secondary"
                                      className={getDifficultyColor(tutorial.difficulty)}
                                    >
                                      {t(`ai.help.difficulty.${tutorial.difficulty}`)}
                                    </Badge>
                                    <div className="flex items-center gap-1 text-xs text-gray-500">
                                      <Clock className="h-3 w-3" />
                                      {formatEstimatedTime(tutorial.estimatedTime)}
                                    </div>
                                  </div>
                                </div>
                                <ChevronRight className="h-4 w-4 text-gray-400 ml-2" />
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </TabsContent>

                    <TabsContent value="docs" className="mt-0">
                      <div className="space-y-3">
                        {searchResults.map((section) => (
                          <Card key={section.id} className="cursor-pointer hover:shadow-md transition-all">
                            <CardContent className="p-4">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h3 className="text-sm font-medium mb-2">
                                    {section.title}
                                  </h3>
                                  <p className="text-xs text-gray-600 mb-3 line-clamp-3">
                                    {section.content.substring(0, 150)}...
                                  </p>
                                  <div className="flex items-center gap-2 flex-wrap">
                                    <Badge variant="outline" className="text-xs">
                                      {t(`ai.help.categories.${section.category}`)}
                                    </Badge>
                                    {section.tags.slice(0, 2).map((tag) => (
                                      <Badge key={tag} variant="secondary" className="text-xs">
                                        {tag}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                                <ChevronRight className="h-4 w-4 text-gray-400 ml-2" />
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </TabsContent>

                    <TabsContent value="api" className="mt-0">
                      <div className="space-y-3">
                        <Card>
                          <CardHeader className="pb-3">
                            <CardTitle className="text-sm">
                              {t('ai.help.api.services.title')}
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-2">
                            <div className="p-2 rounded-lg border border-gray-200">
                              <h4 className="text-sm font-medium">RAG Service</h4>
                              <p className="text-xs text-gray-600 mt-1">
                                {t('ai.help.api.services.rag')}
                              </p>
                            </div>
                            <div className="p-2 rounded-lg border border-gray-200">
                              <h4 className="text-sm font-medium">Cutting Optimizer</h4>
                              <p className="text-xs text-gray-600 mt-1">
                                {t('ai.help.api.services.cutting')}
                              </p>
                            </div>
                            <div className="p-2 rounded-lg border border-gray-200">
                              <h4 className="text-sm font-medium">Inventory Intelligence</h4>
                              <p className="text-xs text-gray-600 mt-1">
                                {t('ai.help.api.services.inventory')}
                              </p>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </TabsContent>
                  </div>
                </Tabs>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};