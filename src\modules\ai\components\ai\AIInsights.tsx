/**
 * AI Insights Component
 * Zeigt intelligente Analysen und Empfehlungen basierend auf AI-Service
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Activity,
  Brain,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { apiService } from '@/services/api.service';

interface Anomaly {
  type: 'warehouse' | 'cutting' | 'dispatch' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  value: number;
  threshold: number;
  timestamp: Date;
  recommendation: string;
}

interface Prediction {
  type: 'inventory_shortage' | 'capacity_overload' | 'efficiency_drop';
  probability: number;
  daysUntil: number;
  description: string;
  impact: 'low' | 'medium' | 'high';
  preventionActions: string[];
}

interface Insight {
  category: 'performance' | 'efficiency' | 'quality' | 'capacity';
  title: string;
  description: string;
  trend: 'positive' | 'negative' | 'stable';
  urgency: 'low' | 'medium' | 'high';
  actionRequired: boolean;
  recommendations: string[];
}

interface OptimizationPlan {
  type: 'cutting' | 'warehouse' | 'logistics';
  currentEfficiency: number;
  potentialEfficiency: number;
  improvementPercent: number;
  actions: Array<{
    priority: 'high' | 'medium' | 'low';
    action: string;
    description: string;
    effort: 'low' | 'medium' | 'high';
    impact: 'low' | 'medium' | 'high';
  }>;
  estimatedSavings: {
    time: number;
    material: number;
    cost: number;
  };
}

const AIInsights: React.FC = () => {
  const [insights, setInsights] = useState<Insight[]>([]);
  const [anomalies, setAnomalies] = useState<Anomaly[]>([]);
  const [predictions, setPredictions] = useState<Prediction[]>([]);
  const [optimization, setOptimization] = useState<OptimizationPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const fetchAIData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Parallele API-Aufrufe
      const [insightsData, anomaliesData, predictionsData, optimizationData] = await Promise.all([
        apiService.get<Insight[]>('/ai/insights?timeframe=7'),
        apiService.get<Anomaly[]>('/ai/anomalies?timeframe=7'),
        apiService.get<Prediction[]>('/ai/predictions?days=14'),
        apiService.get<OptimizationPlan>('/ai/optimization/cutting')
      ]);

      setInsights(insightsData || []);
      setAnomalies(anomaliesData || []);
      setPredictions(predictionsData || []);
      setOptimization(optimizationData || null);

      setLastUpdate(new Date());
    } catch (err) {
      setError('Fehler beim Laden der AI-Daten: ' + (err instanceof Error ? err.message : 'Unbekannter Fehler'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAIData();
    // Auto-refresh alle 5 Minuten
    const interval = setInterval(fetchAIData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'high': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'medium': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'low': return <Info className="h-4 w-4 text-blue-500" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'positive': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'negative': return <TrendingDown className="h-4 w-4 text-red-500" />;
      case 'stable': return <Activity className="h-4 w-4 text-gray-500" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-5/6"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header mit Refresh */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Brain className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-bold">AI-Insights</h2>
          {lastUpdate && (
            <span className="text-sm text-muted-foreground">
              Letztes Update: {lastUpdate.toLocaleTimeString()}
            </span>
          )}
        </div>
        <Button
          onClick={fetchAIData}
          variant="outline"
          size="sm"
          disabled={loading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Aktualisieren
        </Button>
      </div>

      {/* Kritische Anomalien */}
      {anomalies.filter(a => a.severity === 'critical' || a.severity === 'high').length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>Kritische Anomalien erkannt!</strong> Sofortige Aufmerksamkeit erforderlich.
          </AlertDescription>
        </Alert>
      )}

      {/* Insights Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

        {/* Aktuelle Insights */}
        {insights.slice(0, 3).map((insight, index) => (
          <Card key={index} className="border-l-4 border-l-primary">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  {getUrgencyIcon(insight.urgency)}
                  {insight.title}
                </CardTitle>
                <div className="flex items-center gap-1">
                  {getTrendIcon(insight.trend)}
                  <Badge variant={insight.actionRequired ? 'destructive' : 'secondary'} className="text-xs">
                    {insight.category}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-3">{insight.description}</p>
              {insight.recommendations.length > 0 && (
                <div className="space-y-1">
                  <p className="text-xs font-medium">Empfehlungen:</p>
                  <ul className="text-xs text-muted-foreground space-y-1">
                    {insight.recommendations.slice(0, 2).map((rec, i) => (
                      <li key={i} className="flex items-start gap-1">
                        <span className="text-primary">•</span>
                        {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        ))}

        {/* Anomalien */}
        {anomalies.slice(0, 2).map((anomaly, index) => (
          <Card key={`anomaly-${index}`} className={`border-l-4 ${anomaly.severity === 'critical' ? 'border-l-red-500' :
              anomaly.severity === 'high' ? 'border-l-orange-500' : 'border-l-yellow-500'
            }`}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">🚨 Anomalie</CardTitle>
                <Badge variant={getSeverityColor(anomaly.severity) as any} className="text-xs">
                  {anomaly.severity}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-2">{anomaly.description}</p>
              <div className="text-xs space-y-1">
                <div className="flex justify-between">
                  <span>Wert:</span>
                  <span className="font-mono">{anomaly.value.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Schwellwert:</span>
                  <span className="font-mono">{anomaly.threshold.toFixed(2)}</span>
                </div>
              </div>
              <p className="text-xs text-primary mt-2">{anomaly.recommendation}</p>
            </CardContent>
          </Card>
        ))}

        {/* Vorhersagen */}
        {predictions.slice(0, 1).map((prediction, index) => (
          <Card key={`prediction-${index}`} className="border-l-4 border-l-blue-500">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">🔮 Vorhersage</CardTitle>
                <Badge variant="outline" className="text-xs">
                  {(prediction.probability * 100).toFixed(0)}% Wahrscheinlichkeit
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-2">{prediction.description}</p>
              <div className="text-xs space-y-1 mb-3">
                <div className="flex justify-between">
                  <span>In Tagen:</span>
                  <span className="font-mono">{prediction.daysUntil}</span>
                </div>
                <div className="flex justify-between">
                  <span>Auswirkung:</span>
                  <Badge variant={prediction.impact === 'high' ? 'destructive' : 'secondary'} className="text-xs">
                    {prediction.impact}
                  </Badge>
                </div>
              </div>
              {prediction.preventionActions.length > 0 && (
                <div>
                  <p className="text-xs font-medium mb-1">Präventionsmaßnahmen:</p>
                  <ul className="text-xs text-muted-foreground">
                    {prediction.preventionActions.slice(0, 2).map((action, i) => (
                      <li key={i} className="flex items-start gap-1">
                        <span className="text-blue-500">•</span>
                        {action}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        ))}

        {/* Optimierungsplan */}
        {optimization && (
          <Card className="border-l-4 border-l-green-500 md:col-span-2 lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Schnittoptimierung - Verbesserungspotential
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-muted-foreground">
                    {optimization.currentEfficiency}%
                  </div>
                  <div className="text-xs text-muted-foreground">Aktuelle Effizienz</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {optimization.potentialEfficiency}%
                  </div>
                  <div className="text-xs text-muted-foreground">Potential</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    +{optimization.improvementPercent}%
                  </div>
                  <div className="text-xs text-muted-foreground">Verbesserung</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-center p-3 bg-gray-50 rounded">
                  <div className="font-semibold">{optimization.estimatedSavings.time} min/Tag</div>
                  <div className="text-xs text-muted-foreground">Zeitersparnis</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded">
                  <div className="font-semibold">{optimization.estimatedSavings.material}%</div>
                  <div className="text-xs text-muted-foreground">Materialeinsparung</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded">
                  <div className="font-semibold">€{optimization.estimatedSavings.cost}/Tag</div>
                  <div className="text-xs text-muted-foreground">Kosteneinsparung</div>
                </div>
              </div>

              {optimization.actions.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Empfohlene Maßnahmen:</h4>
                  <div className="space-y-2">
                    {optimization.actions.slice(0, 3).map((action, i) => (
                      <div key={i} className="flex items-start gap-3 p-3 bg-gray-50 rounded">
                        <Badge variant={action.priority === 'high' ? 'destructive' : 'secondary'} className="text-xs">
                          {action.priority}
                        </Badge>
                        <div className="flex-1">
                          <div className="font-medium text-sm">{action.action}</div>
                          <div className="text-xs text-muted-foreground">{action.description}</div>
                          <div className="flex gap-2 mt-1">
                            <span className="text-xs">Aufwand: {action.effort}</span>
                            <span className="text-xs">Impact: {action.impact}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Alle Daten leer */}
      {insights.length === 0 && anomalies.length === 0 && predictions.length === 0 && !optimization && (
        <Card>
          <CardContent className="text-center py-8">
            <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              Keine AI-Insights verfügbar. Die Analyse läuft...
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AIInsights;