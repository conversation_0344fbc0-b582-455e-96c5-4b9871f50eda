import express from 'express';
import { z } from 'zod';
import { createValidationMiddleware } from '../middleware/validation.middleware';
import { rateLimitConfig } from '../middleware/rate-limiting.middleware';
import OpenRouterService from '../services/openrouter.service';

const router = express.Router();

// Validierungsschema für Chat-Anfragen
const chatRequestSchema = z.object({
  message: z.string().min(1, "Nachricht darf nicht leer sein").max(1000, "Nachricht zu lang"),
  includeInsights: z.boolean().optional().default(false),
  includeAnomalies: z.boolean().optional().default(false)
});

// Einfacher Chat-Endpunkt
router.post('/chat', rateLimitConfig.general, createValidationMiddleware({ body: chatRequestSchema }), async (req, res) => {
  try {
    const { message } = req.body;
    
    console.log(`📝 [AI-CHAT] Anfrage erhalten: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
    
    // Anfrage an OpenRouter-Service weiterleiten
    const response = await OpenRouterService.generateResponse({
      message,
      includeInsights: false,
      includeAnomalies: false
    });
    
    console.log(`✅ [AI-CHAT] Antwort gesendet für: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
    
    res.json(response);
  } catch (error) {
    console.error('❌ [AI-CHAT] Fehler:', error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Verarbeitung der Chat-Anfrage ist ein Fehler aufgetreten.'
    });
  }
});

// Erweiterter Chat-Endpunkt mit Insights und Anomalien
router.post('/chat/enhanced', rateLimitConfig.general, createValidationMiddleware({ body: chatRequestSchema }), async (req, res) => {
  try {
    const { message, includeInsights, includeAnomalies } = req.body;
    
    console.log(`📝 [AI-CHAT-ENHANCED] Anfrage erhalten: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}" (Insights: ${includeInsights}, Anomalien: ${includeAnomalies})`);
    
    // Anfrage an OpenRouter-Service weiterleiten
    const response = await OpenRouterService.generateResponse({
      message,
      includeInsights,
      includeAnomalies
    });
    
    console.log(`✅ [AI-CHAT-ENHANCED] Antwort gesendet für: "${message.substring(0, 50)}${message.length > 50 ? '...' : ''}"`);
    
    res.json(response);
  } catch (error) {
    console.error('❌ [AI-CHAT-ENHANCED] Fehler:', error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Verarbeitung der erweiterten Chat-Anfrage ist ein Fehler aufgetreten.'
    });
  }
});

// AI-Modelle abrufen
router.get('/models', rateLimitConfig.general, async (req, res) => {
  try {
    // TODO: Implement real model retrieval from OpenRouter service
    const models = await OpenRouterService.getAvailableModels();
    
    res.json({ models });
  } catch (error) {
    console.error('❌ [AI-MODELS] Fehler:', error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Abfrage der verfügbaren AI-Modelle ist ein Fehler aufgetreten.'
    });
  }
});

// AI-Kontext aktualisieren
router.post('/context', rateLimitConfig.general, async (req, res) => {
  try {
    const { context } = req.body;
    
    // Hier würde normalerweise der Kontext für zukünftige AI-Anfragen gespeichert werden
    console.log(`📝 [AI-CONTEXT] Kontext aktualisiert: ${JSON.stringify(context).substring(0, 100)}...`);
    
    res.json({ 
      success: true,
      message: 'Kontext erfolgreich aktualisiert'
    });
  } catch (error) {
    console.error('❌ [AI-CONTEXT] Fehler:', error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Aktualisierung des AI-Kontexts ist ein Fehler aufgetreten.'
    });
  }
});

// Insights abrufen
router.get('/insights', rateLimitConfig.dataEndpoint, async (req, res) => {
  try {
    const timeframe = req.query.timeframe ? parseInt(req.query.timeframe as string) : 7;
    
    // TODO: Implement real insights generation from data analysis
    const insights = await OpenRouterService.generateInsights(timeframe);
    
    res.json({ 
      success: true,
      data: insights,
      timeframe
    });
  } catch (error) {
    console.error('❌ [AI-INSIGHTS] Fehler:', error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Abfrage der AI-Insights ist ein Fehler aufgetreten.'
    });
  }
});

// Anomalien abrufen
router.get('/anomalies', rateLimitConfig.dataEndpoint, async (req, res) => {
  try {
    const timeframe = req.query.timeframe ? parseInt(req.query.timeframe as string) : 7;
    
    // TODO: Implement real anomaly detection from system data
    const anomalies = await OpenRouterService.detectAnomalies(timeframe);
    
    res.json({ 
      success: true,
      data: anomalies,
      timeframe
    });
  } catch (error) {
    console.error('❌ [AI-ANOMALIES] Fehler:', error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Abfrage der Anomalien ist ein Fehler aufgetreten.'
    });
  }
});

// Vorhersagen abrufen
router.get('/predictions', rateLimitConfig.dataEndpoint, async (req, res) => {
  try {
    const days = req.query.days ? parseInt(req.query.days as string) : 14;
    
    // TODO: Implement real prediction generation from historical data
    const predictions = await OpenRouterService.generatePredictions(days);
    
    res.json({ 
      success: true,
      data: predictions,
      days
    });
  } catch (error) {
    console.error('❌ [AI-PREDICTIONS] Fehler:', error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Abfrage der Vorhersagen ist ein Fehler aufgetreten.'
    });
  }
});

// Optimierungsvorschläge abrufen
router.get('/optimization/:area', rateLimitConfig.dataEndpoint, async (req, res) => {
  try {
    const area = req.params.area;
    
    if (area !== 'cutting' && area !== 'warehouse' && area !== 'logistics') {
      return res.status(400).json({
        success: false,
        error: 'Ungültiger Bereich',
        message: 'Gültige Bereiche sind: cutting, warehouse, logistics'
      });
    }
    
    // TODO: Implement real optimization analysis from system data
    const optimizationData = await OpenRouterService.generateOptimization(area);
    
    res.json({ 
      success: true,
      data: optimizationData
    });
  } catch (error) {
    console.error(`❌ [AI-OPTIMIZATION] Fehler:`, error);
    res.status(500).json({
      success: false,
      error: 'Interner Serverfehler',
      message: 'Bei der Abfrage der Optimierungsvorschläge ist ein Fehler aufgetreten.'
    });
  }
});

export default router;
