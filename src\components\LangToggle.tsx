import React from "react";
import { ToggleGroup, ToggleGroupItem } from "./ui/toggle-group";
import langs from "@/localization/langs";
import { useTranslation } from "react-i18next";
import { setAppLanguage } from "@/helpers/language_helpers";
import { cn } from "@/lib/utils";

export default function LangToggle() {
  const { i18n } = useTranslation();
  const currentLang = i18n.language;

  // Funktion zum Ändern der Sprache
  function onValueChange(value: string) {
    setAppLanguage(value, i18n);
  }

  return (
    <ToggleGroup
      type="single"
      onValueChange={onValueChange}
      value={currentLang}
      className="flex gap-2"
    >
      {langs.map((lang) => (
        <ToggleGroupItem 
          key={lang.key} 
          value={lang.key}
          className={cn(
            "flex items-center justify-center px-3 py-1 font-medium border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transition-all",
            "data-[state=on]:bg-blue-500 data-[state=on]:text-white data-[state=on]:translate-y-1 data-[state=on]:translate-x-1 data-[state=on]:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]",
            "hover:bg-gray-100"
          )}
        >
          <span className="font-bold mr-2">{lang.prefix}</span>
          {lang.nativeName}
        </ToggleGroupItem>
      ))}
    </ToggleGroup>
  );
}
