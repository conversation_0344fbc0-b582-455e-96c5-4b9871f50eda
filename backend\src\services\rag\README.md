# RAG (Retrieval-Augmented Generation) System

Eine vollständige RAG-Implementation für das SFM Dashboard mit separater SQLite-Datenbank für Vector Embeddings und Knowledge Management.

## 🏗️ Architektur

### Separate RAG-Datenbank
- **Datei**: `backend/database/rag_knowledge.db`
- **Technologie**: SQLite mit `better-sqlite3`
- **Grund**: <PERSON><PERSON><PERSON> getrennt von Produktionsdaten, keine Berührung der Hauptdatenbank

### Services

#### 1. VectorDatabaseService
- Vector Storage und Similarity Search
- Cosine Similarity Berechnungen
- SQLite-optimierte Vector-Operationen

#### 2. EmbeddingService  
- OpenAI Embedding Generation
- Batch-Processing
- Caching für Performance
- Mock-Embeddings für Development

#### 3. DocumentService
- Document Management
- Text Chunking
- Knowledge Base Organization

#### 4. RAGService
- Hauptorchestrator
- Query Processing
- Context Preparation
- Performance Monitoring

## 📊 Datenbank Schema

```sql
-- Dokumente
documents (id, title, content, source, language, status)

-- Text-Chunks für Embeddings  
chunks (id, document_id, content, chunk_index, token_count)

-- Vector Embeddings (1536 Dimensionen)
embeddings (id, chunk_id, vector, model_name, dimension)

-- Kategorien für Organisation
categories (id, name, description, parent_id)

-- Query-Logging für Analytics
search_history (id, query, results_count, response_time_ms)
```

## 🚀 Setup und Initialisierung

### 1. Dependencies installieren
```bash
cd backend
npm install
```

### 2. RAG-Datenbank initialisieren
```bash
npm run rag:init
```

### 3. OpenAI API Key setzen (optional)
```bash
# In .env file
OPENAI_API_KEY=your_api_key_here
```

**Ohne API Key**: System nutzt Mock-Embeddings für Development

## 💡 Verwendung

### Basic RAG Search
```typescript
import RAGService from './services/rag/RAGService';

const ragService = new RAGService();

// Semantic Search
const response = await ragService.search({
  query: "Wie optimiere ich den Servicegrad?",
  limit: 5,
  threshold: 0.7
});

console.log(response.context); // Relevanter Kontext
console.log(response.results); // Similarity Results
```

### Document hinzufügen
```typescript
// Neues Dokument mit automatischem Chunking und Embedding
const result = await ragService.addDocument({
  knowledgeBaseId: 1,
  title: "Neue Prozessdokumentation",
  content: "Detaillierte Beschreibung...",
  language: "de"
});
```

### Knowledge Base erstellen
```typescript
const kb = await ragService.createKnowledgeBase({
  name: "Qualitätsmanagement",
  description: "QM Prozesse und Standards",
  category: "procedures"
});
```

## 🎯 Features

### ✅ Implementiert
- [x] Separate SQLite Vector Database
- [x] OpenAI Embedding Integration
- [x] Cosine Similarity Search
- [x] Document Chunking
- [x] Knowledge Base Management
- [x] Query Logging & Analytics
- [x] Mock Embeddings für Development
- [x] Batch Processing
- [x] Performance Monitoring

### 🔄 Geplant
- [ ] Hybrid Search (Vector + Text)
- [ ] Advanced Chunking Strategies
- [ ] Multi-language Support
- [ ] Vector Index Optimization
- [ ] Real-time Updates
- [ ] Advanced Analytics Dashboard

## 📈 Performance

### Vector Search
- **Dimensions**: 1536 (OpenAI text-embedding-3-small)
- **Similarity**: Cosine Similarity
- **Storage**: Binary BLOB in SQLite
- **Index**: Optimiert für < 100k Embeddings

### Chunking
- **Default Size**: 500 tokens
- **Overlap**: 50 tokens  
- **Strategy**: Sentence-based splitting
- **Metadata**: Token count, positions

## 🔧 Konfiguration

### Environment Variables
```bash
# Optional - ohne API Key werden Mock-Embeddings verwendet
OPENAI_API_KEY=sk-...

# RAG Database Path (optional)
RAG_DATABASE_PATH=./database/rag_knowledge.db
```

### Service Configuration
```typescript
const ragService = new RAGService(prisma, openAIApiKey);

// Konfiguration anpassen
ragService.config = {
  defaultSimilarityThreshold: 0.7,
  maxContextLength: 4000,
  maxResults: 10,
  enableQueryLogging: true
};
```

## 🧪 Testing

### RAG System testen
```bash
# Unit Tests
npm run test:unit

# RAG-spezifische Tests
npm run test -- --testPathPattern=rag

# Integration Tests
npm run test:integration
```

### Manual Testing
```typescript
// Health Check
const health = await ragRepository.healthCheck();
console.log(health.status); // 'healthy' | 'degraded' | 'unhealthy'

// Performance Metrics
const metrics = await ragService.getPerformanceMetrics();
console.log(metrics.averageResponseTime);
```

## 📚 Sample Data

Das System wird mit Beispieldokumenten initialisiert:

- **Versand KPI Definitionen** (Servicegrad, Tonnage, ATRL)
- **Ablängerei Optimierung** (Schnittmuster, Effizienz)
- **Wareneingang Prozesse** (Kontrolle, Lagerplätze)
- **System Monitoring** (Verfügbarkeit, Störungen)
- **Troubleshooting Guide** (Häufige Probleme)

## 🔒 Sicherheit

### Datentrennung
- **Separate Datenbank**: Keine Berührung der Produktionsdaten
- **Read-Only Access**: RAG liest nur, schreibt nicht in Hauptdatenbank
- **Sandboxed**: Komplett isolierte Vector-Operationen

### API Security
- **Rate Limiting**: Schutz vor Embedding-API Missbrauch
- **Input Validation**: Sanitization aller User-Inputs
- **Error Handling**: Keine sensiblen Daten in Error Messages

## 🛠️ Troubleshooting

### Häufige Probleme

**Problem**: `Database locked`
```bash
# Lösung: WAL-Mode aktivieren (automatisch)
# Oder: Datenbank neu initialisieren
npm run rag:reset
```

**Problem**: `Embedding generation failed`
```bash
# Lösung: API Key prüfen oder Mock-Mode nutzen
# Mock-Embeddings werden automatisch verwendet ohne API Key
```

**Problem**: `No search results`
```bash
# Lösung: Similarity threshold senken
const results = await ragService.search({
  query: "...",
  threshold: 0.5  // Niedriger threshold
});
```

## 📞 Support

Bei Fragen oder Problemen:
1. Prüfe die Logs: `console.log` Ausgaben
2. Health Check: `ragRepository.healthCheck()`
3. Database Stats: `vectorService.getStatistics()`
4. Reset Database: `npm run rag:reset`