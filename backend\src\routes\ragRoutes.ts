/**
 * RAG Routes - Document Upload and Management API
 */

import { Router } from 'express';
import RAGController, { uploadMiddleware } from '../controllers/ragController';

const router = Router();
const ragController = new RAGController();

// Note: Authentication is handled at the server level based on NODE_ENV

// Document upload and management
router.post('/documents/upload', uploadMiddleware, ragController.uploadDocument);
router.get('/documents', ragController.getDocuments);
router.delete('/documents/:documentId', ragController.deleteDocument);
router.get('/documents/search', ragController.searchDocuments);

// Knowledge base management
router.get('/knowledge-bases', ragController.getKnowledgeBases);
router.post('/knowledge-bases', ragController.createKnowledgeBase);

// Analytics and monitoring
router.get('/statistics', ragController.getStatistics);
router.get('/queries/recent', ragController.getRecentQueries);

// Settings management
router.get('/settings', ragController.getSettings);
router.put('/settings', ragController.saveSettings);
router.get('/settings/all', ragController.getAllSettings);
router.delete('/settings/:settingsId', ragController.deleteSettings);

// Test endpoint
router.get('/test', (req, res) => {
  res.json({ success: true, message: 'RAG service is running', timestamp: new Date().toISOString() });
});

export default router;