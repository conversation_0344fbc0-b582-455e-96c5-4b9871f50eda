import { type ReactNode } from "react";
import { BaseNavigation } from "@/components/navigation";
import { dashboardNavigationConfig } from "@/config/navigation";

interface DashboardNavigationProps {
    title?: ReactNode;
}

/**
 * Dashboard Navigation Component
 *
 * Spezifische Navigation für das Dashboard-Modul mit:
 * - Logo und Titel
 * - Dashboard-spezifische Navigation (Versand, Ablängerei, Lagerauslastung, etc.)
 * - Health Indicator
 * - Settings Button
 */
export default function DashboardNavigation({ title }: DashboardNavigationProps) {
    return (
        <BaseNavigation
            title={title}
            navigationConfig={dashboardNavigationConfig}
            showUserMenu={true}
        />
    );
}