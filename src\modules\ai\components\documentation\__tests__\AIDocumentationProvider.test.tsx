import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AIDocumentationProvider, useAIDocumentation } from '../AIDocumentationProvider';

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, options?: any) => {
      if (options?.count !== undefined) {
        return `${key}_${options.count}`;
      }
      return key;
    }
  })
}));

// Test component to access context
const TestComponent: React.FC = () => {
  const {
    sections,
    tutorials,
    searchDocumentation,
    currentTutorial,
    startTutorial,
    showHelp,
    toggleHelp,
    getUserProgress
  } = useAIDocumentation();

  return (
    <div>
      <div data-testid="sections-count">{sections.length}</div>
      <div data-testid="tutorials-count">{tutorials.length}</div>
      <div data-testid="current-tutorial">{currentTutorial?.id || 'none'}</div>
      <div data-testid="show-help">{showHelp.toString()}</div>
      <div data-testid="progress">{getUserProgress().completed}/{getUserProgress().total}</div>
      
      <button onClick={() => startTutorial('test-tutorial')}>
        Start Tutorial
      </button>
      <button onClick={toggleHelp}>
        Toggle Help
      </button>
      <button onClick={() => {
        const results = searchDocumentation('test');
        const resultsDiv = document.getElementById('search-results');
        if (resultsDiv) {
          resultsDiv.textContent = results.length.toString();
        }
      }}>
        Search
      </button>
      <div id="search-results" data-testid="search-results">0</div>
    </div>
  );
};

describe('AIDocumentationProvider', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should provide documentation context', () => {
    render(
      <AIDocumentationProvider>
        <TestComponent />
      </AIDocumentationProvider>
    );

    expect(screen.getByTestId('sections-count')).toHaveTextContent('0');
    expect(screen.getByTestId('tutorials-count')).toHaveTextContent('0');
    expect(screen.getByTestId('current-tutorial')).toHaveTextContent('none');
    expect(screen.getByTestId('show-help')).toHaveTextContent('false');
  });

  it('should toggle help visibility', () => {
    render(
      <AIDocumentationProvider>
        <TestComponent />
      </AIDocumentationProvider>
    );

    const toggleButton = screen.getByText('Toggle Help');
    
    expect(screen.getByTestId('show-help')).toHaveTextContent('false');
    
    fireEvent.click(toggleButton);
    expect(screen.getByTestId('show-help')).toHaveTextContent('true');
    
    fireEvent.click(toggleButton);
    expect(screen.getByTestId('show-help')).toHaveTextContent('false');
  });

  it('should handle tutorial management', () => {
    render(
      <AIDocumentationProvider>
        <TestComponent />
      </AIDocumentationProvider>
    );

    const startButton = screen.getByText('Start Tutorial');
    
    expect(screen.getByTestId('current-tutorial')).toHaveTextContent('none');
    
    fireEvent.click(startButton);
    // Since we don't have actual tutorials loaded, it should remain 'none'
    expect(screen.getByTestId('current-tutorial')).toHaveTextContent('none');
  });

  it('should handle search functionality', () => {
    render(
      <AIDocumentationProvider>
        <TestComponent />
      </AIDocumentationProvider>
    );

    const searchButton = screen.getByText('Search');
    
    fireEvent.click(searchButton);
    expect(screen.getByTestId('search-results')).toHaveTextContent('0');
  });

  it('should track user progress', () => {
    render(
      <AIDocumentationProvider>
        <TestComponent />
      </AIDocumentationProvider>
    );

    expect(screen.getByTestId('progress')).toHaveTextContent('0/0');
  });

  it('should throw error when used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAIDocumentation must be used within AIDocumentationProvider');
    
    consoleSpy.mockRestore();
  });

  it('should handle contextual help', () => {
    const TestContextualComponent: React.FC = () => {
      const { setHelpContext, getContextualHelp } = useAIDocumentation();

      React.useEffect(() => {
        setHelpContext({
          currentPage: 'ai-dashboard',
          userRole: 'administrator',
          availableFeatures: ['rag', 'cutting']
        });
      }, [setHelpContext]);

      const contextualHelp = getContextualHelp();

      return (
        <div data-testid="contextual-help-count">
          {contextualHelp.length}
        </div>
      );
    };

    render(
      <AIDocumentationProvider>
        <TestContextualComponent />
      </AIDocumentationProvider>
    );

    expect(screen.getByTestId('contextual-help-count')).toHaveTextContent('0');
  });
});