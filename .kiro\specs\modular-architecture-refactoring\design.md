# Design Document

## Overview

Das Design für die modulare Architektur-Umstrukturierung transformiert die bestehende monolithische Frontend-Struktur in eine saubere, modulare Architektur mit vier Hauptmodulen: Leitstand, Störungen, Backend & Automatisierung und AI. Diese Umstrukturierung verbessert die Wartbarkeit, Skalierbarkeit und ermöglicht eine klare rollenbasierte Zugriffskontrolle.

Die UserLandingPage fungiert als zentrale Navigationsschnittstelle, die Benutzern basierend auf ihren Rollen Zugriff auf die entsprechenden Module gewährt.

## Architecture

### Modulare Struktur

```
src/
├── modules/
│   ├── leitstand/
│   │   ├── pages/
│   │   │   ├── HomePage.tsx
│   │   │   ├── DispatchPage.tsx
│   │   │   ├── CuttingPage.tsx
│   │   │   ├── IncomingGoodsPage.tsx
│   │   │   ├── ArilPage.tsx
│   │   │   ├── AtrlPage.tsx
│   │   │   └── MachinesPage.tsx
│   │   └── components/
│   │       ├── charts/
│   │       └── stats/
│   ├── stoerungen/
│   │   ├── pages/
│   │   │   └── StoerungenPage.tsx
│   │   └── components/
│   │       ├── stoerungen/
│   │       └── monitoring/
│   ├── backend/
│   │   ├── pages/
│   │   │   ├── SystemPage.tsx
│   │   │   └── WorkflowPage.tsx
│   │   └── components/
│   │       └── workflows/
│   └── ai/
│       ├── pages/
│       │   └── (zukünftige AI-Seiten)
│       └── components/
│           ├── ai/
│           └── chat/
├── pages/
│   ├── UserLandingPage.tsx (zentrale Modulauswahl)
│   ├── LoginPage.tsx
│   ├── RegistrationPage.tsx
│   └── SettingsPage.tsx
└── components/
    ├── ui/ (shared components)
    ├── auth/
    └── (andere shared components)
```

### Routing-Architektur

Die neue Routing-Struktur folgt einem modularen Ansatz:

```
/login                    → LoginPage
/register                 → RegistrationPage
/dashboard                → UserLandingPage (nach Login)
/modules/leitstand/*      → Leitstand-Module
/modules/stoerungen/*     → Störungen-Module
/modules/backend/*        → Backend & Automatisierung-Module
/modules/ai/*             → AI-Module
```

### Rollenbasierte Zugriffskontrolle

| Rolle | Leitstand | Störungen | Backend & Automatisierung | AI |
|-------|-----------|-----------|---------------------------|-----|
| Besucher | ✓ | ✗ | ✗ | ✓ |
| Benutzer | ✓ | ✓ | ✗ | ✓ |
| Administrator | ✓ | ✓ | ✓ | ✓ |

## Components and Interfaces

### UserLandingPage Enhancement

Die bestehende UserLandingPage wird erweitert, um als zentrale Modulauswahl zu fungieren:

```typescript
interface ModuleCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  route: string;
  requiredRoles: UserRole[];
  isAvailable: boolean;
}

interface UserLandingPageProps {
  user: User;
  availableModules: ModuleCard[];
}
```

### Module Navigation Component

Ein neuer NavigationProvider wird erstellt, um die modulspezifische Navigation zu verwalten:

```typescript
interface ModuleNavigationContext {
  currentModule: string;
  availableRoutes: Route[];
  navigateToModule: (moduleId: string) => void;
  canAccessModule: (moduleId: string) => boolean;
}
```

### Route Protection

Ein ModuleGuard-Component wird implementiert, um den Zugriff auf Module zu kontrollieren:

```typescript
interface ModuleGuardProps {
  moduleId: string;
  requiredRoles: UserRole[];
  children: React.ReactNode;
  fallbackRoute?: string;
}
```

## Data Models

### Module Configuration

```typescript
interface ModuleConfig {
  id: string;
  name: string;
  displayName: string;
  description: string;
  icon: string;
  baseRoute: string;
  requiredRoles: UserRole[];
  isEnabled: boolean;
  pages: PageConfig[];
}

interface PageConfig {
  id: string;
  name: string;
  route: string;
  component: string;
  requiredPermissions?: string[];
}
```

### Navigation State

```typescript
interface NavigationState {
  currentModule: string | null;
  currentPage: string | null;
  breadcrumbs: BreadcrumbItem[];
  availableModules: ModuleConfig[];
}

interface BreadcrumbItem {
  label: string;
  route: string;
  isActive: boolean;
}
```

## Error Handling

### Module Access Errors

```typescript
class ModuleAccessError extends Error {
  constructor(
    public moduleId: string,
    public userRole: UserRole,
    public requiredRoles: UserRole[]
  ) {
    super(`Access denied to module ${moduleId} for role ${userRole}`);
  }
}
```

### Import Path Resolution

Ein robustes System für die Auflösung von Import-Pfaden nach der Umstrukturierung:

```typescript
// Automatische Pfad-Umleitung für Legacy-Imports
const importPathResolver = {
  resolveModulePath: (originalPath: string) => string;
  validateImports: (filePath: string) => ImportValidationResult;
  updateImportPaths: (filePath: string, mappings: PathMapping[]) => void;
}
```

## Testing Strategy

### Unit Testing

1. **Modul-spezifische Tests**: Jedes Modul erhält eigene Test-Suites
2. **Navigation Tests**: Testen der rollenbasierten Navigation
3. **Import-Pfad Tests**: Validierung aller Import-Pfade nach der Umstrukturierung

### Integration Testing

1. **Modul-übergreifende Tests**: Testen der Interaktion zwischen Modulen
2. **Routing Tests**: End-to-End Tests für die neue Routing-Struktur
3. **Rollen-basierte Tests**: Testen des Zugriffs für verschiedene Benutzerrollen

### Regression Testing

1. **Funktionalitäts-Tests**: Alle bestehenden Features müssen nach der Umstrukturierung funktionieren
2. **Performance Tests**: Sicherstellen, dass die modulare Struktur keine Performance-Einbußen verursacht
3. **UI/UX Tests**: Validierung der Benutzeroberfläche und -erfahrung

## Migration Strategy

### Phase 1: Struktur-Erstellung
- Erstellen der neuen Modulverzeichnisse
- Einrichten der Basis-Konfiguration für jedes Modul

### Phase 2: Leitstand-Migration
- Verschieben aller Leitstand-bezogenen Seiten und Komponenten
- Aktualisierung der Import-Pfade für das Leitstand-Modul

### Phase 3: Störungen- und Backend-Migration
- Verschieben der StoerungenPage und zugehörigen Komponenten
- Verschieben der SystemPage und WorkflowPage
- Aktualisierung aller Import-Pfade

### Phase 4: AI-Modul-Migration
- Verschieben aller AI- und Chat-Komponenten
- Einrichtung der AI-Modul-Struktur

### Phase 5: Navigation und Routing
- Implementierung der UserLandingPage als Modulauswahl
- Einrichtung der neuen Routing-Struktur
- Implementierung der rollenbasierten Zugriffskontrolle

### Phase 6: Testing und Validierung
- Umfassende Tests aller Module
- Regression-Tests für bestehende Funktionalitäten
- Performance-Validierung

## Security Considerations

### Module Access Control
- Implementierung von Route Guards für jedes Modul
- Validierung der Benutzerrollen auf Frontend- und Backend-Ebene
- Sichere Weiterleitung bei unautorisierten Zugriffsversuchen

### Import Security
- Validierung aller Import-Pfade zur Vermeidung von Sicherheitslücken
- Sicherstellung, dass Module nur auf autorisierte Komponenten zugreifen können

## Performance Considerations

### Code Splitting
- Implementierung von Lazy Loading für Module
- Optimierung der Bundle-Größe durch modulare Aufteilung

### Caching Strategy
- Implementierung von Modul-spezifischem Caching
- Optimierung der Ladezeiten für häufig verwendete Module

## Deployment Considerations

### Build Process
- Anpassung des Build-Prozesses für die modulare Struktur
- Sicherstellung der korrekten Asset-Verknüpfung

### Rollback Strategy
- Implementierung einer Rollback-Strategie für den Fall von Problemen
- Backup der aktuellen Struktur vor der Migration