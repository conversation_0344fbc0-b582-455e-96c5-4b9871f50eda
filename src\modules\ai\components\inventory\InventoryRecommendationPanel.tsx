import React, { memo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ShoppingCart, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp, 
  Package, 
  DollarSign,
  Calendar,
  ArrowRight
} from 'lucide-react';
import { 
  ReorderRecommendation, 
  PurchaseRecommendation, 
  StockAnomaly 
} from '../../services/inventory/types';

interface InventoryRecommendationPanelProps {
  reorderRecommendations: ReorderRecommendation[];
  purchaseRecommendations: PurchaseRecommendation[];
  stockAnomalies: StockAnomaly[];
  onApproveRecommendation?: (itemId: string, type: 'reorder' | 'purchase') => void;
  onDismissAnomaly?: (anomalyId: string) => void;
  onViewDetails?: (itemId: string) => void;
}

/**
 * Inventory Recommendation Panel Component
 * 
 * Displays AI-generated inventory recommendations including:
 * - Reorder point recommendations
 * - Purchase suggestions
 * - Stock anomaly alerts
 * - Action buttons for approval/dismissal
 */
export const InventoryRecommendationPanel = memo(function InventoryRecommendationPanel({
  reorderRecommendations,
  purchaseRecommendations,
  stockAnomalies,
  onApproveRecommendation,
  onDismissAnomaly,
  onViewDetails
}: InventoryRecommendationPanelProps) {

  // Sort recommendations by urgency
  const sortedReorderRecommendations = React.useMemo(() => {
    const urgencyOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
    return [...reorderRecommendations].sort((a, b) => 
      urgencyOrder[b.urgency] - urgencyOrder[a.urgency]
    );
  }, [reorderRecommendations]);

  // Sort purchase recommendations by priority
  const sortedPurchaseRecommendations = React.useMemo(() => {
    return [...purchaseRecommendations].sort((a, b) => b.priority - a.priority);
  }, [purchaseRecommendations]);

  // Sort anomalies by severity
  const sortedAnomalies = React.useMemo(() => {
    const severityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
    return [...stockAnomalies].sort((a, b) => 
      severityOrder[b.severity] - severityOrder[a.severity]
    );
  }, [stockAnomalies]);

  // Get urgency color and icon
  const getUrgencyStyle = (urgency: string) => {
    switch (urgency) {
      case 'critical':
        return { color: 'bg-red-100 text-red-800 border-red-200', icon: AlertTriangle, iconColor: 'text-red-600' };
      case 'high':
        return { color: 'bg-orange-100 text-orange-800 border-orange-200', icon: Clock, iconColor: 'text-orange-600' };
      case 'medium':
        return { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: Package, iconColor: 'text-yellow-600' };
      case 'low':
        return { color: 'bg-blue-100 text-blue-800 border-blue-200', icon: CheckCircle, iconColor: 'text-blue-600' };
      default:
        return { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: Package, iconColor: 'text-gray-600' };
    }
  };

  // Get severity style
  const getSeverityStyle = (severity: string) => {
    switch (severity) {
      case 'critical':
        return { color: 'bg-red-100 text-red-800', iconColor: 'text-red-600' };
      case 'high':
        return { color: 'bg-orange-100 text-orange-800', iconColor: 'text-orange-600' };
      case 'medium':
        return { color: 'bg-yellow-100 text-yellow-800', iconColor: 'text-yellow-600' };
      case 'low':
        return { color: 'bg-blue-100 text-blue-800', iconColor: 'text-blue-600' };
      default:
        return { color: 'bg-gray-100 text-gray-800', iconColor: 'text-gray-600' };
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-[#ff7a05]">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <ShoppingCart className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Nachbestellungen</p>
                <p className="text-2xl font-bold text-blue-600">
                  {reorderRecommendations.length}
                </p>
                <p className="text-xs text-gray-500">
                  {reorderRecommendations.filter(r => r.urgency === 'critical' || r.urgency === 'high').length} dringend
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#ff7a05]">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Einkaufsempfehlungen</p>
                <p className="text-2xl font-bold text-green-600">
                  {purchaseRecommendations.length}
                </p>
                <p className="text-xs text-gray-500">
                  {Math.round(purchaseRecommendations.reduce((sum, p) => sum + p.estimatedCost, 0)).toLocaleString()} €
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#ff7a05]">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Anomalien</p>
                <p className="text-2xl font-bold text-orange-600">
                  {stockAnomalies.length}
                </p>
                <p className="text-xs text-gray-500">
                  {stockAnomalies.filter(a => a.severity === 'critical' || a.severity === 'high').length} kritisch
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reorder Recommendations */}
      <Card className="border-[#ff7a05]">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ShoppingCart className="h-5 w-5" />
            <span>Nachbestellempfehlungen</span>
          </CardTitle>
          <CardDescription>
            AI-generierte Empfehlungen für Nachbestellungen basierend auf Bedarfsprognosen
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {sortedReorderRecommendations.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                <p>Keine Nachbestellungen erforderlich</p>
                <p className="text-sm">Alle Artikel haben ausreichende Bestände</p>
              </div>
            ) : (
              sortedReorderRecommendations.map((recommendation, index) => {
                const urgencyStyle = getUrgencyStyle(recommendation.urgency);
                const UrgencyIcon = urgencyStyle.icon;
                
                return (
                  <div key={recommendation.itemId} className="p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center space-x-2">
                        <UrgencyIcon className={`h-5 w-5 ${urgencyStyle.iconColor}`} />
                        <div>
                          <h4 className="font-medium">{recommendation.itemId}</h4>
                          <Badge className={urgencyStyle.color}>
                            {recommendation.urgency === 'critical' ? 'Kritisch' :
                             recommendation.urgency === 'high' ? 'Hoch' :
                             recommendation.urgency === 'medium' ? 'Mittel' : 'Niedrig'}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-bold">
                          {recommendation.recommendedOrderQuantity} Stück
                        </p>
                        <p className="text-xs text-gray-600">
                          Aktuell: {recommendation.currentStock}
                        </p>
                      </div>
                    </div>

                    <div className="mb-3">
                      <div className="flex justify-between text-xs text-gray-600 mb-1">
                        <span>Bestand vs. Meldebestand</span>
                        <span>{Math.round((recommendation.currentStock / recommendation.reorderPoint) * 100)}%</span>
                      </div>
                      <Progress 
                        value={Math.min((recommendation.currentStock / recommendation.reorderPoint) * 100, 100)} 
                        className="h-2"
                      />
                    </div>

                    <p className="text-sm text-gray-700 mb-3">{recommendation.reasoning}</p>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs mb-3">
                      <div>
                        <p className="text-gray-600">Meldebestand</p>
                        <p className="font-medium">{recommendation.reorderPoint}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Sicherheitsbestand</p>
                        <p className="font-medium">{recommendation.safetyStock}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Lieferzeit</p>
                        <p className="font-medium">{recommendation.leadTime} Tage</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Konfidenz</p>
                        <p className="font-medium">{Math.round(recommendation.confidence * 100)}%</p>
                      </div>
                    </div>

                    {recommendation.estimatedStockoutDate && (
                      <Alert className="mb-3">
                        <Calendar className="h-4 w-4" />
                        <AlertDescription>
                          Voraussichtlicher Lagerausfall: {recommendation.estimatedStockoutDate.toLocaleDateString('de-DE')}
                        </AlertDescription>
                      </Alert>
                    )}

                    <div className="flex justify-between items-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onViewDetails?.(recommendation.itemId)}
                      >
                        Details anzeigen
                      </Button>
                      <Button
                        variant="sfm"
                        size="sm"
                        onClick={() => onApproveRecommendation?.(recommendation.itemId, 'reorder')}
                        className="flex items-center space-x-1"
                      >
                        <CheckCircle className="h-4 w-4" />
                        <span>Genehmigen</span>
                      </Button>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </CardContent>
      </Card>

      {/* Purchase Recommendations */}
      <Card className="border-[#ff7a05]">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>Einkaufsempfehlungen</span>
          </CardTitle>
          <CardDescription>
            Optimierte Einkaufsvorschläge mit Kostenanalyse und Lieferantenvergleich
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {sortedPurchaseRecommendations.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                <p>Keine Einkaufsempfehlungen</p>
                <p className="text-sm">Alle Bestellungen sind optimal</p>
              </div>
            ) : (
              sortedPurchaseRecommendations.map((recommendation, index) => (
                <div key={recommendation.itemId} className="p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">{recommendation.itemId}</h4>
                      <Badge variant="outline">
                        Priorität: {recommendation.priority}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-green-600">
                        {Math.round(recommendation.estimatedCost).toLocaleString()} €
                      </p>
                      <p className="text-sm text-gray-600">
                        {recommendation.recommendedQuantity} Stück
                      </p>
                    </div>
                  </div>

                  <p className="text-sm text-gray-700 mb-3">{recommendation.reasoning}</p>

                  <div className="grid grid-cols-2 gap-2 text-xs mb-3">
                    {recommendation.supplier && (
                      <div>
                        <p className="text-gray-600">Empfohlener Lieferant</p>
                        <p className="font-medium">{recommendation.supplier}</p>
                      </div>
                    )}
                    {recommendation.expectedDelivery && (
                      <div>
                        <p className="text-gray-600">Erwartete Lieferung</p>
                        <p className="font-medium">
                          {recommendation.expectedDelivery.toLocaleDateString('de-DE')}
                        </p>
                      </div>
                    )}
                  </div>

                  {recommendation.alternatives && recommendation.alternatives.length > 0 && (
                    <div className="mb-3">
                      <p className="text-sm font-medium text-gray-700 mb-2">Alternativen:</p>
                      <div className="space-y-2">
                        {recommendation.alternatives.slice(0, 2).map((alt, altIndex) => (
                          <div key={altIndex} className="flex justify-between items-center p-2 bg-gray-50 rounded text-xs">
                            <div>
                              <p className="font-medium">{alt.supplier}</p>
                              <p className="text-gray-600">{alt.quantity} Stück</p>
                            </div>
                            <div className="text-right">
                              <p className="font-bold">{Math.round(alt.cost).toLocaleString()} €</p>
                              <div className="flex space-x-1">
                                {alt.pros.slice(0, 1).map((pro, proIndex) => (
                                  <Badge key={proIndex} variant="outline" className="text-xs bg-green-50 text-green-700">
                                    {pro}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between items-center">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onViewDetails?.(recommendation.itemId)}
                    >
                      Details anzeigen
                    </Button>
                    <Button
                      variant="sfm"
                      size="sm"
                      onClick={() => onApproveRecommendation?.(recommendation.itemId, 'purchase')}
                      className="flex items-center space-x-1"
                    >
                      <ShoppingCart className="h-4 w-4" />
                      <span>Bestellen</span>
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Stock Anomalies */}
      <Card className="border-[#ff7a05]">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Bestandsanomalien</span>
          </CardTitle>
          <CardDescription>
            Erkannte Unregelmäßigkeiten im Lagerbestand mit Handlungsempfehlungen
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {sortedAnomalies.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                <p>Keine Anomalien erkannt</p>
                <p className="text-sm">Alle Bestände sind normal</p>
              </div>
            ) : (
              sortedAnomalies.map((anomaly, index) => {
                const severityStyle = getSeverityStyle(anomaly.severity);
                
                return (
                  <div key={anomaly.id} className="p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className={`h-5 w-5 ${severityStyle.iconColor}`} />
                        <div>
                          <h4 className="font-medium">{anomaly.itemId}</h4>
                          <Badge className={severityStyle.color}>
                            {anomaly.severity === 'critical' ? 'Kritisch' :
                             anomaly.severity === 'high' ? 'Hoch' :
                             anomaly.severity === 'medium' ? 'Mittel' : 'Niedrig'}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-bold">
                          {anomaly.currentValue} 
                          <ArrowRight className="inline h-3 w-3 mx-1" />
                          {anomaly.expectedValue}
                        </p>
                        <p className="text-xs text-gray-600">
                          Abweichung: {Math.round(anomaly.deviation)}%
                        </p>
                      </div>
                    </div>

                    <p className="text-sm text-gray-700 mb-3">{anomaly.description}</p>

                    <div className="mb-3">
                      <p className="text-sm font-medium text-gray-700 mb-2">Empfohlene Maßnahmen:</p>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {anomaly.recommendations.map((rec, recIndex) => (
                          <li key={recIndex} className="flex items-start space-x-2">
                            <span className="text-[#ff7a05] mt-1">•</span>
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="flex justify-between items-center text-xs text-gray-600 mb-3">
                      <span>Erkannt: {anomaly.detectedAt.toLocaleDateString('de-DE')}</span>
                      <span>Konfidenz: {Math.round(anomaly.confidence * 100)}%</span>
                    </div>

                    <div className="flex justify-between items-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onViewDetails?.(anomaly.itemId)}
                      >
                        Details anzeigen
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => onDismissAnomaly?.(anomaly.id)}
                      >
                        Ignorieren
                      </Button>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
});