import React, { type ReactNode } from "react";
import lappLogo from '@/assets/lappLogo.png';
import { Settings } from "lucide-react";

interface DragWindowRegionProps {
  title?: ReactNode;
}

/**
 * Generic DragWindowRegion Component
 * 
 * Einfache Titelleiste für Module ohne spezifische Navigation.
 * Für das Dashboard-Modul wird DashboardNavigation verwendet.
 */
export default function DragWindowRegion({ title }: DragWindowRegionProps) {
  return (
    <div className="w-screen h-14 border-b-2 border-black flex items-center" style={{ backgroundColor: '#ff7a05' }}>
      {/* Obere Leiste mit Logo und Titel */}
      <div className="w-full flex items-center justify-between px-4">
        {/* Linke Seite mit Logo */}
        <div className="flex items-center">
          <img src={lappLogo} alt="Lapp Logo" className="h-10 object-contain" />
          {title && (
            <div className="flex select-none whitespace-nowrap p-2 text-sm font-bold">
              {title}
            </div>
          )}
        </div>

        {/* Rechte Seite mit Einstellungen */}
        <div className="flex items-center gap-2">
          <button className="flex h-8 w-8 items-center justify-center rounded-full hover:bg-muted/20">
            <Settings className="size-5" />
          </button>
        </div>
      </div>
    </div>
  );
}