import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { BarChart3, RefreshCw, Calendar, Clock, User, TrendingUp } from 'lucide-react';
import { bereitschaftsService } from '@/services/bereitschaftsService';
import { BereitschaftsStatistiken as StatistikenType } from '@/types/bereitschafts';
import { format, subWeeks, startOfWeek, endOfWeek } from 'date-fns';
import { de } from 'date-fns/locale';
import { toast } from 'sonner';

export const BereitschaftsStatistiken: React.FC = () => {
  const [statistiken, setStatistiken] = useState<StatistikenType | null>(null);
  const [loading, setLoading] = useState(true);
  const [startDate, setStartDate] = useState(() => {
    const twelveWeeksAgo = subWeeks(new Date(), 12);
    return format(startOfWeek(twelveWeeksAgo, { weekStartsOn: 1 }), 'yyyy-MM-dd');
  });
  const [endDate, setEndDate] = useState(() => {
    return format(endOfWeek(new Date(), { weekStartsOn: 1 }), 'yyyy-MM-dd');
  });

  useEffect(() => {
    loadStatistiken();
  }, []);

  const loadStatistiken = async () => {
    try {
      setLoading(true);
      const data = await bereitschaftsService.getBereitschaftsStatistiken(startDate, endDate);
      setStatistiken(data);
    } catch (error) {
      console.error('Fehler beim Laden der Statistiken:', error);
      toast.error('Statistiken konnten nicht geladen werden');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = () => {
    if (new Date(startDate) >= new Date(endDate)) {
      toast.error('Startdatum muss vor Enddatum liegen');
      return;
    }
    loadStatistiken();
  };

  const getPersonenFarbe = (index: number): string => {
    const farben = [
      'bg-blue-100 text-blue-800 border-blue-300',
      'bg-green-100 text-green-800 border-green-300',
      'bg-purple-100 text-purple-800 border-purple-300',
      'bg-orange-100 text-orange-800 border-orange-300',
      'bg-pink-100 text-pink-800 border-pink-300',
      'bg-indigo-100 text-indigo-800 border-indigo-300',
      'bg-yellow-100 text-yellow-800 border-yellow-300',
      'bg-red-100 text-red-800 border-red-300'
    ];
    return farben[index % farben.length];
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Bereitschafts-Statistiken
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
            <span className="ml-2">Statistiken werden geladen...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Zeitraum-Auswahl */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-blue-600" />
            Zeitraum auswählen
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Von</Label>
              <Input
                id="startDate"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">Bis</Label>
              <Input
                id="endDate"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>

            <div className="flex items-end">
              <Button
                onClick={handleDateChange}
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Aktualisieren
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {statistiken && (
        <>
          {/* Übersicht */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <Calendar className="h-8 w-8 text-blue-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">
                      {statistiken.gesamtBereitschaften}
                    </p>
                    <p className="text-sm text-gray-600">Gesamt-Bereitschaften</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <User className="h-8 w-8 text-green-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">
                      {statistiken.personenStatistiken.length}
                    </p>
                    <p className="text-sm text-gray-600">Aktive Personen</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <Clock className="h-8 w-8 text-purple-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">
                      {statistiken.personenStatistiken.length > 0
                        ? Math.round(
                            statistiken.personenStatistiken.reduce((sum, p) => sum + p.gesamtStunden, 0) /
                            statistiken.personenStatistiken.length
                          )
                        : 0}h
                    </p>
                    <p className="text-sm text-gray-600">Ø Stunden/Person</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Zeitraum-Info */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Calendar className="h-4 w-4" />
                <span>
                  Statistiken für den Zeitraum vom{' '}
                  <strong>{format(new Date(statistiken.zeitraum.von), 'dd.MM.yyyy', { locale: de })}</strong>{' '}
                  bis{' '}
                  <strong>{format(new Date(statistiken.zeitraum.bis), 'dd.MM.yyyy', { locale: de })}</strong>
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Personen-Statistiken */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                Personen-Statistiken
              </CardTitle>
            </CardHeader>
            <CardContent>
              {statistiken.personenStatistiken.length === 0 ? (
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Keine Daten vorhanden</h3>
                  <p className="text-gray-600">
                    Für den gewählten Zeitraum sind keine Bereitschaftsdaten verfügbar.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {statistiken.personenStatistiken
                    .sort((a, b) => b.gesamtStunden - a.gesamtStunden)
                    .map((personStat, index) => {
                      const maxStunden = Math.max(...statistiken.personenStatistiken.map(p => p.gesamtStunden));
                      const prozent = maxStunden > 0 ? (personStat.gesamtStunden / maxStunden) * 100 : 0;

                      return (
                        <div key={personStat.person.id} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${getPersonenFarbe(index)}`}>
                                {index + 1}
                              </div>
                              <div>
                                <h4 className="font-semibold text-gray-900">
                                  {personStat.person.name}
                                </h4>
                                <p className="text-sm text-gray-600">
                                  {personStat.person.abteilung}
                                </p>
                              </div>
                            </div>

                            <div className="text-right">
                              <div className="flex items-center gap-4">
                                <Badge variant="outline" className="text-blue-600 border-blue-300">
                                  {personStat.anzahlBereitschaften} Bereitschaften
                                </Badge>
                                <Badge variant="outline" className="text-green-600 border-green-300">
                                  {personStat.gesamtStunden}h gesamt
                                </Badge>
                                <Badge variant="outline" className="text-purple-600 border-purple-300">
                                  Ø {personStat.durchschnittStundenProWoche}h/Woche
                                </Badge>
                              </div>
                            </div>
                          </div>

                          {/* Fortschrittsbalken */}
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-300 ${getPersonenFarbe(index).replace('text-', 'bg-').replace('border-', '').replace('100', '500')}`}
                              style={{ width: `${prozent}%` }}
                            />
                          </div>
                        </div>
                      );
                    })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Verteilungs-Analyse */}
          {statistiken.personenStatistiken.length > 1 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                  Verteilungs-Analyse
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Bereitschafts-Verteilung</h4>
                    <div className="space-y-2">
                      {(() => {
                        const bereitschaften = statistiken.personenStatistiken.map(p => p.anzahlBereitschaften);
                        const min = Math.min(...bereitschaften);
                        const max = Math.max(...bereitschaften);
                        const durchschnitt = bereitschaften.reduce((sum, val) => sum + val, 0) / bereitschaften.length;

                        return (
                          <>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Minimum:</span>
                              <span className="font-medium">{min} Bereitschaften</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Maximum:</span>
                              <span className="font-medium">{max} Bereitschaften</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Durchschnitt:</span>
                              <span className="font-medium">{durchschnitt.toFixed(1)} Bereitschaften</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Differenz:</span>
                              <span className={`font-medium ${max - min <= 1 ? 'text-green-600' : max - min <= 2 ? 'text-yellow-600' : 'text-red-600'}`}>
                                {max - min} Bereitschaften
                              </span>
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Stunden-Verteilung</h4>
                    <div className="space-y-2">
                      {(() => {
                        const stunden = statistiken.personenStatistiken.map(p => p.gesamtStunden);
                        const min = Math.min(...stunden);
                        const max = Math.max(...stunden);
                        const durchschnitt = stunden.reduce((sum, val) => sum + val, 0) / stunden.length;

                        return (
                          <>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Minimum:</span>
                              <span className="font-medium">{min}h</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Maximum:</span>
                              <span className="font-medium">{max}h</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Durchschnitt:</span>
                              <span className="font-medium">{durchschnitt.toFixed(1)}h</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Differenz:</span>
                              <span className={`font-medium ${max - min <= 24 ? 'text-green-600' : max - min <= 48 ? 'text-yellow-600' : 'text-red-600'}`}>
                                {max - min}h
                              </span>
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Bewertung:</strong>{' '}
                    {(() => {
                      const bereitschaften = statistiken.personenStatistiken.map(p => p.anzahlBereitschaften);
                      const diff = Math.max(...bereitschaften) - Math.min(...bereitschaften);
                      
                      if (diff <= 1) {
                        return 'Sehr ausgewogene Verteilung der Bereitschaften.';
                      } else if (diff <= 2) {
                        return 'Gute Verteilung der Bereitschaften mit geringen Abweichungen.';
                      } else {
                        return 'Ungleichmäßige Verteilung - Überprüfung der Rotation empfohlen.';
                      }
                    })()}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
};