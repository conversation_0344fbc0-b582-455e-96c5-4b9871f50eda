/**
 * Multi-Objective Optimization for Cutting Plans
 * 
 * Implements NSGA-II (Non-dominated Sorting Genetic Algorithm II)
 * for multi-objective cutting optimization with Pareto front analysis
 */

import { 
  CuttingRequest, 
  CuttingAlternative 
} from '../../../types/cutting';
import { GeneticAlgorithm, Individual, GeneticAlgorithmConfig } from './GeneticAlgorithm';

export interface MultiObjectiveConfig extends GeneticAlgorithmConfig {
  objectives: ObjectiveConfig[];
  paretoFrontSize?: number;
  crowdingDistanceWeight?: number;
}

export interface ObjectiveConfig {
  name: string;
  weight: number;
  minimize: boolean; // true for minimization, false for maximization
  normalize?: boolean;
}

export interface ParetoSolution {
  individual: Individual;
  rank: number;
  crowdingDistance: number;
  dominationCount: number;
  dominatedSolutions: Set<number>;
}

export interface MultiObjectiveResult {
  paretoFront: ParetoSolution[];
  allSolutions: ParetoSolution[];
  hypervolume: number;
  convergenceMetrics: {
    generation: number;
    paretoFrontSize: number;
    hypervolume: number;
    spacing: number;
  }[];
  executionTime: number;
  alternatives: CuttingAlternative[];
}

/**
 * Multi-objective optimizer using NSGA-II algorithm
 */
export class MultiObjectiveOptimizer {
  private config: MultiObjectiveConfig;
  private geneticAlgorithm: GeneticAlgorithm;

  constructor(config: MultiObjectiveConfig) {
    const defaultConfig: MultiObjectiveConfig = {
      populationSize: 100,
      generations: 150,
      mutationRate: 0.1,
      crossoverRate: 0.9,
      elitismRate: 0.0, // NSGA-II doesn't use traditional elitism
      tournamentSize: 2,
      convergenceThreshold: 0.001,
      maxStagnantGenerations: 30,
      paretoFrontSize: 20,
      crowdingDistanceWeight: 0.5,
      objectives: [
        { name: 'wasteMinimization', weight: 1.0, minimize: false, normalize: true },
        { name: 'efficiency', weight: 1.0, minimize: false, normalize: true },
        { name: 'timeOptimization', weight: 1.0, minimize: false, normalize: true }
      ]
    };

    this.config = { ...defaultConfig, ...config };
    this.geneticAlgorithm = new GeneticAlgorithm(this.config);
  }

  /**
   * Run multi-objective optimization
   */
  async optimize(request: CuttingRequest): Promise<MultiObjectiveResult> {
    const startTime = Date.now();

    // Run genetic algorithm to get population
    const gaResult = await this.geneticAlgorithm.optimize(request);
    
    // Convert to Pareto solutions
    const solutions = gaResult.finalPopulation.map((individual) => 
      this.createParetoSolution(individual)
    );

    // Perform non-dominated sorting
    const rankedSolutions = this.nonDominatedSort(solutions);
    
    // Calculate crowding distances
    this.calculateCrowdingDistances(rankedSolutions);

    // Extract Pareto front
    const paretoFront = rankedSolutions
      .filter(sol => sol.rank === 0)
      .sort((a, b) => b.crowdingDistance - a.crowdingDistance)
      .slice(0, this.config.paretoFrontSize || 20);

    // Calculate metrics
    const hypervolume = this.calculateHypervolume(paretoFront);
    const convergenceMetrics = this.buildConvergenceMetrics(gaResult.convergenceData, paretoFront);

    // Generate alternatives from Pareto front
    const alternatives = await this.generateAlternatives(paretoFront);

    const executionTime = Date.now() - startTime;

    return {
      paretoFront,
      allSolutions: rankedSolutions,
      hypervolume,
      convergenceMetrics,
      executionTime,
      alternatives
    };
  }

  /**
   * Create Pareto solution from individual
   */
  private createParetoSolution(individual: Individual): ParetoSolution {
    return {
      individual,
      rank: 0,
      crowdingDistance: 0,
      dominationCount: 0,
      dominatedSolutions: new Set<number>()
    };
  }

  /**
   * Non-dominated sorting (NSGA-II)
   */
  private nonDominatedSort(solutions: ParetoSolution[]): ParetoSolution[] {
    // Initialize domination relationships
    for (let i = 0; i < solutions.length; i++) {
      solutions[i].dominationCount = 0;
      solutions[i].dominatedSolutions.clear();

      for (let j = 0; j < solutions.length; j++) {
        if (i !== j) {
          const dominationResult = this.dominates(solutions[i].individual, solutions[j].individual);
          
          if (dominationResult === 1) {
            // i dominates j
            solutions[i].dominatedSolutions.add(j);
          } else if (dominationResult === -1) {
            // j dominates i
            solutions[i].dominationCount++;
          }
        }
      }
    }

    // Assign ranks
    let currentRank = 0;
    let currentFront = solutions.filter(sol => sol.dominationCount === 0);
    
    while (currentFront.length > 0) {
      // Assign current rank
      currentFront.forEach(sol => sol.rank = currentRank);
      
      // Find next front
      const nextFront: ParetoSolution[] = [];
      
      for (const solution of currentFront) {
        solution.dominatedSolutions.forEach(dominatedIndex => {
          const dominatedSolution = solutions[dominatedIndex];
          dominatedSolution.dominationCount--;
          
          if (dominatedSolution.dominationCount === 0) {
            nextFront.push(dominatedSolution);
          }
        });
      }
      
      currentRank++;
      currentFront = nextFront;
    }

    return solutions;
  }

  /**
   * Check if solution1 dominates solution2
   * Returns: 1 if sol1 dominates sol2, -1 if sol2 dominates sol1, 0 if non-dominated
   */
  private dominates(individual1: Individual, individual2: Individual): number {
    let betterInAny = false;
    let worseInAny = false;

    for (const objective of this.config.objectives) {
      const value1 = this.getObjectiveValue(individual1, objective.name);
      const value2 = this.getObjectiveValue(individual2, objective.name);

      if (objective.minimize) {
        if (value1 < value2) betterInAny = true;
        if (value1 > value2) worseInAny = true;
      } else {
        if (value1 > value2) betterInAny = true;
        if (value1 < value2) worseInAny = true;
      }
    }

    if (betterInAny && !worseInAny) return 1;  // individual1 dominates
    if (worseInAny && !betterInAny) return -1; // individual2 dominates
    return 0; // non-dominated
  }

  /**
   * Get objective value from individual
   */
  private getObjectiveValue(individual: Individual, objectiveName: string): number {
    switch (objectiveName) {
      case 'wasteMinimization':
        return individual.objectives.wasteMinimization;
      case 'efficiency':
        return individual.objectives.efficiency;
      case 'timeOptimization':
        return individual.objectives.timeOptimization;
      default:
        return 0;
    }
  }

  /**
   * Calculate crowding distances for solutions
   */
  private calculateCrowdingDistances(solutions: ParetoSolution[]): void {
    // Group by rank
    const rankGroups = new Map<number, ParetoSolution[]>();
    
    for (const solution of solutions) {
      if (!rankGroups.has(solution.rank)) {
        rankGroups.set(solution.rank, []);
      }
      rankGroups.get(solution.rank)!.push(solution);
    }

    // Calculate crowding distance for each rank
    rankGroups.forEach((rankSolutions) => {
      this.calculateCrowdingDistanceForRank(rankSolutions);
    });
  }

  /**
   * Calculate crowding distance for solutions in the same rank
   */
  private calculateCrowdingDistanceForRank(solutions: ParetoSolution[]): void {
    if (solutions.length <= 2) {
      // Boundary solutions get infinite distance
      solutions.forEach(sol => sol.crowdingDistance = Infinity);
      return;
    }

    // Initialize distances
    solutions.forEach(sol => sol.crowdingDistance = 0);

    // For each objective
    for (const objective of this.config.objectives) {
      // Sort by objective value
      const sortedSolutions = [...solutions].sort((a, b) => {
        const valueA = this.getObjectiveValue(a.individual, objective.name);
        const valueB = this.getObjectiveValue(b.individual, objective.name);
        return objective.minimize ? valueA - valueB : valueB - valueA;
      });

      // Boundary solutions get infinite distance
      sortedSolutions[0].crowdingDistance = Infinity;
      sortedSolutions[sortedSolutions.length - 1].crowdingDistance = Infinity;

      // Calculate range
      const minValue = this.getObjectiveValue(sortedSolutions[0].individual, objective.name);
      const maxValue = this.getObjectiveValue(sortedSolutions[sortedSolutions.length - 1].individual, objective.name);
      const range = maxValue - minValue;

      if (range > 0) {
        // Calculate crowding distance for intermediate solutions
        for (let i = 1; i < sortedSolutions.length - 1; i++) {
          if (sortedSolutions[i].crowdingDistance !== Infinity) {
            const prevValue = this.getObjectiveValue(sortedSolutions[i - 1].individual, objective.name);
            const nextValue = this.getObjectiveValue(sortedSolutions[i + 1].individual, objective.name);
            
            sortedSolutions[i].crowdingDistance += Math.abs(nextValue - prevValue) / range;
          }
        }
      }
    }
  }

  /**
   * Calculate hypervolume indicator
   */
  private calculateHypervolume(paretoFront: ParetoSolution[]): number {
    if (paretoFront.length === 0) return 0;

    // Reference point (worst possible values)
    const referencePoint = this.config.objectives.map(obj => obj.minimize ? 1.0 : 0.0);
    
    // Simple hypervolume calculation (for 2D/3D cases)
    if (this.config.objectives.length === 2) {
      return this.calculateHypervolume2D(paretoFront, referencePoint);
    } else if (this.config.objectives.length === 3) {
      return this.calculateHypervolume3D(paretoFront, referencePoint);
    }

    // For higher dimensions, use approximation
    return this.approximateHypervolume(paretoFront, referencePoint);
  }

  /**
   * Calculate 2D hypervolume
   */
  private calculateHypervolume2D(solutions: ParetoSolution[], refPoint: number[]): number {
    if (solutions.length === 0) return 0;

    // Sort by first objective
    const sorted = [...solutions].sort((a, b) => {
      const obj1Name = this.config.objectives[0].name;
      const valueA = this.getObjectiveValue(a.individual, obj1Name);
      const valueB = this.getObjectiveValue(b.individual, obj1Name);
      return this.config.objectives[0].minimize ? valueA - valueB : valueB - valueA;
    });

    let hypervolume = 0;
    let prevValue = refPoint[1];

    for (const solution of sorted) {
      const obj1Value = this.getObjectiveValue(solution.individual, this.config.objectives[0].name);
      const obj2Value = this.getObjectiveValue(solution.individual, this.config.objectives[1].name);
      
      const width = Math.abs(obj1Value - refPoint[0]);
      const height = Math.abs(obj2Value - prevValue);
      
      hypervolume += width * height;
      prevValue = obj2Value;
    }

    return hypervolume;
  }

  /**
   * Calculate 3D hypervolume (simplified)
   */
  private calculateHypervolume3D(solutions: ParetoSolution[], refPoint: number[]): number {
    // Simplified 3D hypervolume calculation
    let totalVolume = 0;

    for (const solution of solutions) {
      const obj1Value = this.getObjectiveValue(solution.individual, this.config.objectives[0].name);
      const obj2Value = this.getObjectiveValue(solution.individual, this.config.objectives[1].name);
      const obj3Value = this.getObjectiveValue(solution.individual, this.config.objectives[2].name);

      const volume = Math.abs(obj1Value - refPoint[0]) * 
                    Math.abs(obj2Value - refPoint[1]) * 
                    Math.abs(obj3Value - refPoint[2]);
      
      totalVolume += volume;
    }

    return totalVolume / solutions.length; // Average to avoid overlap issues
  }

  /**
   * Approximate hypervolume for higher dimensions
   */
  private approximateHypervolume(solutions: ParetoSolution[], refPoint: number[]): number {
    let totalVolume = 0;

    for (const solution of solutions) {
      let volume = 1;
      
      for (let i = 0; i < this.config.objectives.length; i++) {
        const objValue = this.getObjectiveValue(solution.individual, this.config.objectives[i].name);
        volume *= Math.abs(objValue - refPoint[i]);
      }
      
      totalVolume += volume;
    }

    return totalVolume / solutions.length;
  }

  /**
   * Build convergence metrics
   */
  private buildConvergenceMetrics(
    gaConvergence: any[], 
    paretoFront: ParetoSolution[]
  ): MultiObjectiveResult['convergenceMetrics'] {
    return gaConvergence.map((data) => ({
      generation: data.generation,
      paretoFrontSize: paretoFront.length,
      hypervolume: this.calculateHypervolume(paretoFront),
      spacing: this.calculateSpacing(paretoFront)
    }));
  }

  /**
   * Calculate spacing metric (diversity measure)
   */
  private calculateSpacing(solutions: ParetoSolution[]): number {
    if (solutions.length < 2) return 0;

    const distances: number[] = [];

    for (let i = 0; i < solutions.length; i++) {
      let minDistance = Infinity;
      
      for (let j = 0; j < solutions.length; j++) {
        if (i !== j) {
          const distance = this.calculateEuclideanDistance(
            solutions[i].individual, 
            solutions[j].individual
          );
          minDistance = Math.min(minDistance, distance);
        }
      }
      
      distances.push(minDistance);
    }

    // Calculate standard deviation of distances
    const meanDistance = distances.reduce((sum, d) => sum + d, 0) / distances.length;
    const variance = distances.reduce((sum, d) => sum + Math.pow(d - meanDistance, 2), 0) / distances.length;
    
    return Math.sqrt(variance);
  }

  /**
   * Calculate Euclidean distance between two individuals in objective space
   */
  private calculateEuclideanDistance(individual1: Individual, individual2: Individual): number {
    let sumSquares = 0;

    for (const objective of this.config.objectives) {
      const value1 = this.getObjectiveValue(individual1, objective.name);
      const value2 = this.getObjectiveValue(individual2, objective.name);
      sumSquares += Math.pow(value1 - value2, 2);
    }

    return Math.sqrt(sumSquares);
  }

  /**
   * Generate cutting alternatives from Pareto front
   */
  private async generateAlternatives(paretoFront: ParetoSolution[]): Promise<CuttingAlternative[]> {
    const alternatives: CuttingAlternative[] = [];

    for (let i = 0; i < Math.min(paretoFront.length, 5); i++) {
      const solution = paretoFront[i];
      
      if (solution.individual.plan) {
        const advantages = this.generateAdvantages(solution.individual);
        const disadvantages = this.generateDisadvantages(solution.individual);
        
        alternatives.push({
          id: `pareto-${i}`,
          plan: solution.individual.plan,
          score: solution.individual.fitness,
          advantages,
          disadvantages
        });
      }
    }

    return alternatives;
  }

  /**
   * Generate advantages description for a solution
   */
  private generateAdvantages(individual: Individual): string[] {
    const advantages: string[] = [];
    const objectives = individual.objectives;

    if (objectives.wasteMinimization > 0.8) {
      advantages.push('Excellent waste minimization');
    } else if (objectives.wasteMinimization > 0.6) {
      advantages.push('Good waste reduction');
    }

    if (objectives.efficiency > 0.8) {
      advantages.push('High cutting efficiency');
    } else if (objectives.efficiency > 0.6) {
      advantages.push('Reasonable efficiency');
    }

    if (objectives.timeOptimization > 0.8) {
      advantages.push('Fast cutting process');
    } else if (objectives.timeOptimization > 0.6) {
      advantages.push('Moderate processing time');
    }

    if (advantages.length === 0) {
      advantages.push('Balanced solution');
    }

    return advantages;
  }

  /**
   * Generate disadvantages description for a solution
   */
  private generateDisadvantages(individual: Individual): string[] {
    const disadvantages: string[] = [];
    const objectives = individual.objectives;

    if (objectives.wasteMinimization < 0.4) {
      disadvantages.push('Higher material waste');
    }

    if (objectives.efficiency < 0.4) {
      disadvantages.push('Lower cutting efficiency');
    }

    if (objectives.timeOptimization < 0.4) {
      disadvantages.push('Longer processing time');
    }

    if (disadvantages.length === 0) {
      disadvantages.push('Minor trade-offs in optimization');
    }

    return disadvantages;
  }
}