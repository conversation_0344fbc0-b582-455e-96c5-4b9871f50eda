import { Router, Request, Response } from 'express';
import { rateLimitConfig } from '../middleware/rate-limiting.middleware';

const router = Router();

/**
 * AI Module Health Check Routes
 * 
 * Provides health check endpoints specifically for the AI module,
 * integrating with the existing application health monitoring system.
 */

/**
 * Basic AI module health check
 */
router.get('/health', rateLimitConfig.healthCheck, async (req: Request, res: Response) => {
  try {
    console.log('🤖 AI Module health check requested');
    
    const healthStatus = {
      timestamp: new Date().toISOString(),
      module: 'ai',
      status: 'healthy',
      version: '1.0.0',
      services: {
        chat: 'operational',
        analysis: 'operational',
        optimization: 'operational'
      },
      metrics: {
        uptime: process.uptime(),
        memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024), // MB
        responseTime: Date.now() // This would be actual response time
      }
    };

    res.json({
      success: true,
      data: healthStatus,
      message: 'AI module is healthy'
    });
  } catch (error) {
    console.error('❌ AI module health check failed:', error);
    
    res.status(503).json({
      success: false,
      error: 'AI module health check failed',
      message: 'AI module is experiencing issues',
      details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Detailed AI module health check
 */
router.get('/health/detailed', rateLimitConfig.healthCheck, async (req: Request, res: Response) => {
  try {
    console.log('🔍 Detailed AI Module health check requested');
    
    const detailedHealth = {
      timestamp: new Date().toISOString(),
      module: 'ai',
      overallStatus: 'healthy',
      components: {
        configuration: await checkConfiguration(),
        services: await checkServices(),
        security: await checkSecurity(),
        performance: await checkPerformance(),
        resources: await checkResources()
      },
      metrics: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
        requestCount: 0, // This would be tracked
        errorCount: 0,   // This would be tracked
        averageResponseTime: 0 // This would be calculated
      },
      dependencies: {
        database: 'connected',
        openrouter: 'available',
        cache: 'operational'
      }
    };

    // Determine overall status based on components
    const componentStatuses = Object.values(detailedHealth.components);
    if (componentStatuses.includes('critical')) {
      detailedHealth.overallStatus = 'critical';
    } else if (componentStatuses.includes('degraded')) {
      detailedHealth.overallStatus = 'degraded';
    }

    const statusCode = detailedHealth.overallStatus === 'healthy' ? 200 : 503;
    
    res.status(statusCode).json({
      success: detailedHealth.overallStatus === 'healthy',
      data: detailedHealth,
      message: `AI module status: ${detailedHealth.overallStatus}`
    });
  } catch (error) {
    console.error('❌ Detailed AI module health check failed:', error);
    
    res.status(503).json({
      success: false,
      error: 'Detailed health check failed',
      message: 'Unable to perform detailed AI module health check',
      details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * AI module metrics endpoint
 */
router.get('/metrics', rateLimitConfig.healthCheck, async (req: Request, res: Response) => {
  try {
    const metrics = {
      timestamp: new Date().toISOString(),
      module: 'ai',
      performance: {
        uptime: process.uptime(),
        requestsPerSecond: 0, // This would be calculated
        averageResponseTime: 0, // This would be calculated
        errorRate: 0, // This would be calculated
        throughput: 0 // This would be calculated
      },
      resources: {
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024)
        },
        cpu: process.cpuUsage()
      },
      services: {
        chat: {
          status: 'operational',
          requestCount: 0,
          errorCount: 0
        },
        analysis: {
          status: 'operational',
          requestCount: 0,
          errorCount: 0
        },
        optimization: {
          status: 'operational',
          requestCount: 0,
          errorCount: 0
        }
      }
    };

    res.json({
      success: true,
      data: metrics,
      message: 'AI module metrics retrieved successfully'
    });
  } catch (error) {
    console.error('❌ AI module metrics retrieval failed:', error);
    
    res.status(500).json({
      success: false,
      error: 'Metrics retrieval failed',
      message: 'Unable to retrieve AI module metrics',
      details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * Helper functions for health checks
 */

async function checkConfiguration(): Promise<string> {
  try {
    // Check if AI module configuration is valid
    // This would normally validate actual configuration
    return 'healthy';
  } catch (error) {
    return 'critical';
  }
}

async function checkServices(): Promise<string> {
  try {
    // Check if AI services are responding
    // This would normally test actual service endpoints
    return 'healthy';
  } catch (error) {
    return 'degraded';
  }
}

async function checkSecurity(): Promise<string> {
  try {
    // Check security configuration and status
    // This would normally validate security settings
    return 'healthy';
  } catch (error) {
    return 'critical';
  }
}

async function checkPerformance(): Promise<string> {
  try {
    // Check performance metrics
    const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024;
    
    if (memoryUsage > 500) {
      return 'degraded';
    }
    
    return 'healthy';
  } catch (error) {
    return 'critical';
  }
}

async function checkResources(): Promise<string> {
  try {
    // Check resource availability
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;
    
    if (memoryUsageMB > 1000) {
      return 'critical';
    } else if (memoryUsageMB > 500) {
      return 'degraded';
    }
    
    return 'healthy';
  } catch (error) {
    return 'critical';
  }
}

export default router;