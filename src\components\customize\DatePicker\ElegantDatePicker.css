/* ElegantDatePicker.css */
.elegant-datepicker-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }
  
  .datepicker.elegant {
    max-width: 800px;
    margin: 0 auto;
    padding: 25px;
    border-radius: 12px;
    background: linear-gradient(to bottom, #ffffff, #f8f6f2);
    border: 1px solid #e0d6c9;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  }
  
  .datepicker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0d6c9;
  }
  
  .datepicker-title {
    color: #7d5b3d;
    font-weight: 500;
    font-size: 1.4rem;
  }
  
  .datepicker-icon {
    color: #9f8972;
    font-size: 1.6rem;
  }
  
  .date-input-container {
    position: relative;
    margin-top: 15px;
  }
  
  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .input-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #9f8972;
    pointer-events: none;
    z-index: 1;
  }
  
  .date-input {
    width: 100%;
    padding: 10px 40px 10px 12px;
    font-size: 14px;
    border-radius: 6px;
    border: 1px solid #d6c9b5;
    background: #fbf9f7;
    color: #7d5b3d;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .date-input:focus {
    outline: none;
    border-color: #9f8972;
    box-shadow: 0 0 0 3px rgba(159, 137, 114, 0.2);
  }
  
  .calendar-container {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    z-index: 9999;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    width: min(700px, 90vw);
    max-height: 80vh;
    overflow-y: auto;
  }
  
  .calendar-container.adjust-left {
    right: auto;
    left: 0;
  }
  
  .calendar-container.adjust-center {
    right: auto;
    left: 50%;
    transform: translateX(-50%);
  }

  
  .calendar {
    padding: 25px;
    border-radius: 12px;
    background: #fff;
    border: 1px solid #1e293b;
    color: #7d5b3d;
  }
  
  .calendar-custom-header {
    border-bottom: 1px solid #e0d6c9;
    margin-bottom: 20px;
    padding-bottom: 15px;
  }
  
  .calendar-custom-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #7d5b3d;
    margin: 0;
    text-align: center;
  }
  
  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .calendar-nav {
    background: transparent;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    color: #7d5b3d;
    transition: background 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
  }
  
  .calendar-nav svg {
    width: 16px;
    height: 16px;
  }
  
  .calendar-nav:hover {
    background: #f5f1eb;
  }
  
  .calendar-title {
    font-weight: 600;
    font-size: 1.2rem;
    color: #7d5b3d;
  }
  
  .calendar-months {
    display: flex;
    gap: 30px;
    margin-top: 20px;
  }
  
  .calendar-month {
    flex: 1;
  }
  
  .calendar-month-title {
    text-align: center;
    margin-bottom: 15px;
    font-weight: 600;
    color: #7d5b3d;
    font-size: 1.1rem;
  }
  
  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 6px;
  }
  
  .calendar-day-header {
    text-align: center;
    font-weight: bold;
    padding: 10px 5px;
    font-size: 14px;
    color: #9f8972;
  }
  
  .calendar-day {
    text-align: center;
    padding: 10px 5px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    position: relative;
  }
  
  .calendar-day:hover {
    background: #f5f1eb;
    transform: scale(1.05);
  }
  
  .calendar-day.selected {
    background: #8fb89e;
    color: white;
    font-weight: bold;
  }
  
  .calendar-day.in-range {
    background: rgba(143, 184, 158, 0.3);
  }
  
  .calendar-day.current-month {
    opacity: 1;
  }
  
  .calendar-day.other-month {
    opacity: 0.4;
  }
  
  .calendar-day.today {
    font-weight: bold;
  }
  
  .calendar-day.today::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 2px;
    background: #8fb89e;
  }
  
  .selected-range {
    margin-top: 20px;
    text-align: center;
    color: #7d5b3d;
    font-size: 16px;
    padding: 12px;
    background: #fbf9f7;
    border-radius: 8px;
    border: 1px solid #e0d6c9;
    min-height: 24px;
  }
  
  .calendar-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e0d6c9;
  }
  
  .calendar-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 120px;
  }
  
  .calendar-btn svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }
  
  .calendar-btn.reset {
    background: #d6c9b5;
    color: #7d5b3d;
  }
  
  .calendar-btn.reset:hover {
    background: #c9b9a2;
  }
  
  .calendar-btn.today {
    background: #7d5b3d;
    color: white;
  }
  
  .calendar-btn.today:hover {
    background: #6d4c33;
  }
  
  .calendar-btn.accept {
    background: #8fb89e;
    color: white;
  }
  
  .calendar-btn.accept:hover {
    background: #7da58c;
  }
  
  @media (max-width: 768px) {
    .calendar-container {
      width: 95vw;
      max-height: 85vh;
      right: auto;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .calendar-container.adjust-left,
    .calendar-container.adjust-center {
      right: auto;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .calendar-months {
      flex-direction: column;
      gap: 20px;
    }
    
    .calendar-actions {
      flex-wrap: wrap;
      gap: 10px;
    }
    
    .calendar-btn {
      flex: 1;
      justify-content: center;
      min-width: 120px;
    }
  }