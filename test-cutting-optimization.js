/**
 * Test-Skript für die Schneidoptimierung
 * Reproduziert das Problem mit der falschen Verschnittberechnung
 */

// Simuliere die Datenstrukturen
const testDelivery = {
    id: 'delivery-1',
    items: [
        {
            id: 'item-1',
            cableType: '0010000',
            requiredLength: 200, // 200m benötigt
            quantity: 2, // 2 Schnitte
            priority: 'medium'
        }
    ]
};

const testDrums = [
    {
        id: 'drum-1',
        cableType: '0010000',
        totalLength: 500, // 500m Trommel
        availableLength: 500, // 500m verfügbar
        quality: 'A'
    }
];

// Simuliere die First-Fit Optimierung
function simulateFirstFitOptimization(orders, drums) {
    console.log('=== Test der Schneidoptimierung ===');
    console.log('Lieferung:', testDelivery.items[0]);
    console.log('Verfügbare Trommel:', testDrums[0]);
    console.log('');
    
    const drum = drums[0];
    const order = orders[0];
    
    // Berechne benötigte Gesamtlänge
    const totalRequiredLength = order.requiredLength * order.quantity;
    console.log(`Benötigte Gesamtlänge: ${order.requiredLength}m × ${order.quantity} = ${totalRequiredLength}m`);
    
    // Berechne Verschnitt
    const wasteLength = drum.availableLength - totalRequiredLength;
    console.log(`Verfügbare Trommellänge: ${drum.availableLength}m`);
    console.log(`Verschnitt: ${drum.availableLength}m - ${totalRequiredLength}m = ${wasteLength}m`);
    
    // Berechne Effizienz
    const efficiency = (totalRequiredLength / drum.availableLength) * 100;
    console.log(`Effizienz: ${efficiency.toFixed(1)}%`);
    
    console.log('');
    console.log('=== Erwartetes Ergebnis ===');
    console.log('Verschnitt sollte 100m sein, nicht 8m!');
    
    return {
        wasteLength,
        efficiency,
        totalRequiredLength
    };
}

// Konvertiere Delivery Items zu CuttingOrders (wie in der echten App)
const orders = testDelivery.items.map(item => ({
    id: item.id,
    cableType: item.cableType,
    requiredLength: item.requiredLength,
    quantity: item.quantity,
    priority: item.priority
}));

// Führe Test aus
const result = simulateFirstFitOptimization(orders, testDrums);

console.log('');
console.log('=== Test-Ergebnis ===');
console.log(`Berechneter Verschnitt: ${result.wasteLength}m`);
console.log(`Berechnete Effizienz: ${result.efficiency.toFixed(1)}%`);

if (result.wasteLength === 100) {
    console.log('✅ TEST BESTANDEN: Verschnitt korrekt berechnet!');
} else {
    console.log('❌ TEST FEHLGESCHLAGEN: Verschnitt falsch berechnet!');
    console.log('Das Problem liegt wahrscheinlich daran, dass die ausgewählten Trommeldaten nicht korrekt an die Optimierung weitergegeben werden.');
}