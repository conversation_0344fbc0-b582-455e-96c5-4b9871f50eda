"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.app = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const client_1 = require("@prisma-sfm-dashboard/client");
const database_routes_1 = __importDefault(require("./routes/database.routes"));
const system_routes_1 = __importDefault(require("./routes/system.routes"));
const aril_debug_routes_1 = __importDefault(require("./routes/aril-debug.routes"));
const performance_routes_1 = __importDefault(require("./routes/performance.routes"));
const performance_db_routes_1 = __importDefault(require("./routes/performance-db.routes"));
const error_monitoring_routes_1 = __importDefault(require("./routes/error-monitoring.routes"));
const paginated_data_routes_1 = __importDefault(require("./routes/paginated-data.routes"));
const workflow_routes_1 = __importDefault(require("./routes/workflow.routes"));
const stoerungen_routes_1 = __importDefault(require("./routes/stoerungen.routes"));
const ai_routes_1 = __importDefault(require("./routes/ai.routes"));
const ai_health_routes_1 = __importDefault(require("./routes/ai-health.routes"));
const chat_routes_1 = __importDefault(require("./routes/chat.routes"));
const auth_routes_1 = __importDefault(require("./routes/auth.routes"));
const bereitschaftsRoutes_1 = __importDefault(require("./routes/bereitschaftsRoutes"));
const ragRoutes_1 = __importDefault(require("./routes/ragRoutes"));
const inventory_routes_1 = __importDefault(require("./routes/inventory.routes"));
const available_drums_routes_1 = __importDefault(require("./routes/available-drums.routes"));
const email_routes_1 = __importDefault(require("./routes/email.routes"));
const env_validation_1 = require("./utils/env.validation");
const auth_middleware_1 = require("./middleware/auth.middleware");
const rate_limiting_middleware_1 = require("./middleware/rate-limiting.middleware");
const performance_tracking_middleware_1 = require("./middleware/performance-tracking.middleware");
const repository_factory_1 = require("./repositories/repository.factory");
const path_1 = __importDefault(require("path"));
// import warehouseRoutes from './routes/warehouse.routes';
// Validiere Umgebungsvariablen beim Start
const env = (0, env_validation_1.validateEnvironment)();
(0, env_validation_1.logEnvironmentInfo)(env);
const app = (0, express_1.default)();
exports.app = app;
const port = env.API_PORT;
// Security-Middleware
app.use((0, helmet_1.default)({
    // Konfiguriere Helmet für Electron-Kompatibilität
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'"], // Notwendig für Vite dev
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "ws:", "wss:"] // WebSocket für Vite dev
        }
    },
    crossOriginEmbedderPolicy: false // Für Electron-Kompatibilität
}));
// CORS-Konfiguration mit produktionstauglichen Origins
const allowedOrigins = env.NODE_ENV === 'production'
    ? ['electron://localhost', 'https://localhost:3000'] // Produktions-Origins
    : true; // Entwicklung: alle Origins erlaubt
app.use((0, cors_1.default)({
    origin: allowedOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
}));
// Request-Parsing mit Sicherheitsbegrenzungen
app.use(express_1.default.json({
    limit: '10mb',
    type: ['application/json', 'text/plain']
}));
app.use(express_1.default.urlencoded({
    extended: true,
    limit: '10mb',
    parameterLimit: 100 // Begrenze Parameter-Anzahl
}));
// Security Event Logging Middleware (Alternative Implementation)
app.use((req, res, next) => {
    const start = Date.now();
    // Log request
    console.log(`📝 [REQUEST] ${new Date().toISOString()} - ${req.method} ${req.path} - IP: ${req.ip || 'unknown'}`);
    // Hook into response finish event
    res.on('finish', () => {
        const duration = Date.now() - start;
        const statusCode = res.statusCode;
        // Log security-relevant events
        if (statusCode === 401 || statusCode === 403) {
            console.log(`🚨 [SECURITY] ${new Date().toISOString()} - ${req.method} ${req.path} - Status: ${statusCode} - IP: ${req.ip || 'unknown'} - Duration: ${duration}ms`);
        }
        else if (statusCode >= 400) {
            console.log(`⚠️  [ERROR] ${new Date().toISOString()} - ${req.method} ${req.path} - Status: ${statusCode} - IP: ${req.ip || 'unknown'} - Duration: ${duration}ms`);
        }
        else if (req.path.startsWith('/api/') && statusCode === 200) {
            console.log(`✅ [ACCESS] ${new Date().toISOString()} - ${req.method} ${req.path} - Status: ${statusCode} - IP: ${req.ip || 'unknown'} - Duration: ${duration}ms`);
        }
    });
    next();
});
// Statische Dateien für Uploads bereitstellen
// Uploads-Ordner für Störungsbilder und andere Dateien
app.use('/api/uploads', express_1.default.static(path_1.default.join(process.cwd(), 'uploads')));
console.log('📁 Statische Dateien werden bereitgestellt unter /api/uploads');
// IP-Erkennung und Basis-Rate-Limiting
app.use(auth_middleware_1.ipDetectionMiddleware);
app.use(rate_limiting_middleware_1.rateLimitConfig.general); // Globales Rate-Limiting
// Performance-Tracking für alle API-Requests
app.use(performance_tracking_middleware_1.performanceTrackingMiddleware);
// Authentifizierungs-Middleware für alle API-Routen
const authMiddleware = (0, auth_middleware_1.createAuthMiddleware)(env.API_SECRET_KEY);
// Prisma-Client
const prisma = new client_1.PrismaClient();
// Repository Factory initialisieren
console.log('🏭 Initialisiere Repository Factory...');
const repositoryFactory = (0, repository_factory_1.initializeRepositoryFactory)(prisma);
console.log('✅ Repository Factory initialisiert');
console.log('🚀 Server startet...');
// Öffentliche Routen (ohne Authentifizierung, aber mit Rate-Limiting)
app.get('/api/health', rate_limiting_middleware_1.rateLimitConfig.healthCheck, (req, res) => {
    console.log('✅ Health-Check aufgerufen');
    res.json({
        status: 'ok',
        message: 'Backend läuft!',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: env.NODE_ENV,
        security: {
            authentication: 'enabled',
            rateLimit: 'enabled',
            validation: 'enabled'
        }
    });
});
// Erweiterte Health-Check mit Datenbankstatus
app.get('/api/health/detailed', rate_limiting_middleware_1.rateLimitConfig.healthCheck, async (req, res) => {
    try {
        // Teste Datenbankverbindung
        await prisma.$queryRaw `SELECT 1`;
        res.json({
            status: 'ok',
            message: 'Backend und Datenbank sind verfügbar',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            environment: env.NODE_ENV,
            services: {
                database: 'healthy',
                api: 'healthy'
            },
            security: {
                authentication: 'enabled',
                rateLimit: 'enabled',
                validation: 'enabled'
            }
        });
    }
    catch (error) {
        console.error('❌ Datenbankfehler im Health-Check:', error);
        res.status(503).json({
            status: 'degraded',
            message: 'Backend läuft, aber Datenbankverbindung fehlgeschlagen',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            environment: env.NODE_ENV,
            services: {
                database: 'unhealthy',
                api: 'healthy'
            },
            error: env.NODE_ENV === 'development' ? error.message : 'Datenbankfehler'
        });
    }
});
// System-Metriken Endpunkt für Monitoring
app.get('/api/metrics', rate_limiting_middleware_1.rateLimitConfig.healthCheck, (req, res) => {
    const memUsage = process.memoryUsage();
    const uptime = process.uptime();
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        system: {
            uptime: Math.floor(uptime),
            memory: {
                rss: Math.round(memUsage.rss / 1024 / 1024), // MB
                heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
                heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
                external: Math.round(memUsage.external / 1024 / 1024) // MB
            },
            nodejs: process.version,
            platform: process.platform,
            arch: process.arch
        }
    });
});
// API-Routen (temporär ohne Authentifizierung für Development)
// Auth routes are always public (for registration and login)
app.use('/api/auth', auth_routes_1.default);
if (env.NODE_ENV === 'development') {
    // Development: Keine Authentifizierung für einfachere Entwicklung
    app.use('/api/database', database_routes_1.default);
    app.use('/api/system', system_routes_1.default);
    app.use('/api/aril', aril_debug_routes_1.default);
    app.use('/api/performance', performance_routes_1.default);
    app.use('/api/performance-db', performance_db_routes_1.default);
    app.use('/api/errors', error_monitoring_routes_1.default);
    app.use('/api/paginated', paginated_data_routes_1.default);
    app.use('/api/workflows', workflow_routes_1.default);
    app.use('/api/stoerungen', stoerungen_routes_1.default);
    app.use('/api/ai', ai_routes_1.default);
    app.use('/api/ai', ai_health_routes_1.default);
    app.use('/api/chat', chat_routes_1.default);
    app.use('/api/bereitschafts', bereitschaftsRoutes_1.default);
    app.use('/api/rag', ragRoutes_1.default);
    app.use('/api/inventory', inventory_routes_1.default);
    app.use('/api/available-drums', available_drums_routes_1.default);
    app.use('/api/email', email_routes_1.default); // E-Mail-Service auch in Development verfügbar
}
else {
    // Production: Mit Authentifizierung
    app.use('/api/database', authMiddleware, database_routes_1.default);
    app.use('/api/system', authMiddleware, system_routes_1.default);
    app.use('/api/aril', authMiddleware, aril_debug_routes_1.default);
    app.use('/api/performance', authMiddleware, performance_routes_1.default);
    app.use('/api/performance-db', authMiddleware, performance_db_routes_1.default);
    app.use('/api/errors', authMiddleware, error_monitoring_routes_1.default);
    app.use('/api/paginated', authMiddleware, paginated_data_routes_1.default);
    app.use('/api/workflows', authMiddleware, workflow_routes_1.default);
    app.use('/api/stoerungen', authMiddleware, stoerungen_routes_1.default);
    app.use('/api/ai', authMiddleware, ai_routes_1.default);
    app.use('/api/ai', ai_health_routes_1.default); // Health routes are public
    app.use('/api/chat', authMiddleware, chat_routes_1.default);
    app.use('/api/bereitschafts', authMiddleware, bereitschaftsRoutes_1.default);
    app.use('/api/rag', authMiddleware, ragRoutes_1.default);
    app.use('/api/inventory', authMiddleware, inventory_routes_1.default);
    app.use('/api/available-drums', authMiddleware, available_drums_routes_1.default);
    app.use('/api/email', authMiddleware, email_routes_1.default); // E-Mail-Service mit Authentifizierung
}
// app.use('/api/warehouse', authMiddleware, warehouseRoutes);
// Globaler Error-Handler für nicht abgefangene Fehler
app.use((error, req, res, next) => {
    console.error('[GLOBAL-ERROR]', error);
    // Keine sensiblen Informationen in Produktion preisgeben
    const isDevelopment = env.NODE_ENV === 'development';
    res.status(500).json({
        success: false,
        error: 'Interner Serverfehler',
        message: 'Es ist ein unerwarteter Fehler aufgetreten.',
        ...(isDevelopment && { details: error.message, stack: error.stack }),
        code: 'INTERNAL_SERVER_ERROR'
    });
});
// 404-Handler für nicht gefundene Routen
app.use('*', (req, res) => {
    const clientIP = req.clientIP || req.ip || 'unbekannt';
    console.warn(`[404] Nicht gefundene Route: ${req.method} ${req.originalUrl} von IP: ${clientIP}`);
    res.status(404).json({
        success: false,
        error: 'Route nicht gefunden',
        message: `Die Route ${req.method} ${req.originalUrl} existiert nicht.`,
        code: 'ROUTE_NOT_FOUND'
    });
});
// Server starten
app.listen(port, () => {
    console.log(`✅ Server läuft auf Port ${port}`);
    console.log(`✅ Alle Warehouse-Service-Funktionen verfügbar!`);
});
// Graceful shutdown
process.on('SIGTERM', async () => {
    await prisma.$disconnect();
    process.exit(0);
});
