/**
 * CuttingVisualizationSection
 *
 * Zeigt die Visualisierung des Schnittplans oder eine Hinweiskarte,
 * falls noch kein Plan vorhanden ist. Reines Präsentations-Component.
 *
 * Hinweise zur Architektur:
 * - Diese Komponente enthält KEINE Geschäftslogik.
 * - Alle Daten und Handler werden als Props übergeben.
 * - Das hält die Seite `CuttingOptimizationPage` schlank und lesbar.
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';
import { ChartErrorBoundary } from '@/components/ErrorBoundary';
import { CuttingPlanVisualization } from './CuttingPlanVisualization';
import type { CuttingPlan, CuttingOrder, DrumInventory } from '../../services/types';

interface CuttingVisualizationSectionProps {
  plan: CuttingPlan | null;
  drums: DrumInventory[];
  orders: CuttingOrder[];
}

export function CuttingVisualizationSection({ plan, drums, orders }: CuttingVisualizationSectionProps) {
  return (
    <div className="space-y-6">
      {plan ? (
        <ChartErrorBoundary>
          <CuttingPlanVisualization plan={plan} drums={drums} orders={orders} />
        </ChartErrorBoundary>
      ) : (
        <Card className="border-[#ff7a05]">
          <CardContent className="flex flex-col items-center justify-center h-64">
            <AlertTriangle className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-600">Führen Sie eine Optimierung durch, um die Visualisierung zu sehen</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default CuttingVisualizationSection;
