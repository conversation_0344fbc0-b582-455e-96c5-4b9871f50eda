/**
 * AI Security Guard Component
 * 
 * Protects AI module routes and components with role-based access control.
 * Integrates with existing authentication system.
 */

import React, { useEffect, useState } from 'react';
import { useAuthContext } from '@/contexts/AuthContext';
import { aiSecurityService } from '@/modules/ai/services/security/AISecurityService';
import { AISecurityContext } from '@/modules/ai/types/security';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Shield, AlertTriangle, Lock } from 'lucide-react';

interface AISecurityGuardProps {
  children: React.ReactNode;
  requiredFeature?: string;
  fallbackComponent?: React.ReactNode;
  showAccessDenied?: boolean;
}

/**
 * Guards AI components with security checks
 */
export const AISecurityGuard: React.FC<AISecurityGuardProps> = ({
  children,
  requiredFeature,
  fallbackComponent,
  showAccessDenied = true
}) => {
  const { user, isAuthenticated, isLoading } = useAuthContext();
  const [securityContext, setSecurityContext] = useState<AISecurityContext | null>(null);
  const [hasAccess, setHasAccess] = useState(false);
  const [accessReason, setAccessReason] = useState<string>('');

  useEffect(() => {
    if (isAuthenticated && user) {
      const context = aiSecurityService.createSecurityContext(user);
      setSecurityContext(context);

      if (requiredFeature) {
        const access = aiSecurityService.hasFeatureAccess(context, requiredFeature);
        setHasAccess(access);

        if (!access) {
          const featureAccess = context.featureAccess[requiredFeature];
          setAccessReason(featureAccess?.reason || 'Zugriff verweigert');
        }
      } else {
        // If no specific feature required, just check if user has any AI permissions
        setHasAccess(context.permissions.length > 0);
        if (context.permissions.length === 0) {
          setAccessReason('Keine KI-Berechtigungen vorhanden');
        }
      }
    } else {
      setSecurityContext(null);
      setHasAccess(false);
      setAccessReason('Authentifizierung erforderlich');
    }
  }, [user, isAuthenticated, requiredFeature]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3">Sicherheitsprüfung läuft...</span>
      </div>
    );
  }

  // Show access denied if not authenticated or no access
  if (!isAuthenticated || !hasAccess) {
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }

    if (!showAccessDenied) {
      return null;
    }

    return (
      <div className="p-6 max-w-md mx-auto">
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <div className="space-y-3">
              <div className="font-medium">Zugriff verweigert</div>
              <div className="text-sm">{accessReason}</div>
              {!isAuthenticated && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.location.href = '/login'}
                  className="mt-2"
                >
                  <Lock className="h-4 w-4 mr-2" />
                  Anmelden
                </Button>
              )}
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Render children if access granted
  return <>{children}</>;
};

/**
 * Hook for accessing AI security context
 */
export const useAISecurityContext = () => {
  const { user, isAuthenticated } = useAuthContext();
  const [securityContext, setSecurityContext] = useState<AISecurityContext | null>(null);

  useEffect(() => {
    if (isAuthenticated && user) {
      const context = aiSecurityService.createSecurityContext(user);
      setSecurityContext(context);
    } else {
      setSecurityContext(null);
    }
  }, [user, isAuthenticated]);

  return {
    securityContext,
    hasFeatureAccess: (featureId: string) =>
      securityContext ? aiSecurityService.hasFeatureAccess(securityContext, featureId) : false,
    getUserPermissions: () => securityContext?.permissions || [],
    isAuthenticated
  };
};

/**
 * Higher-order component for securing AI components
 */
export function withAISecurity<P = {}>(
  WrappedComponent: React.ComponentType<P>,
  requiredFeature?: string
) {
  const SecuredComponent = (props: P) => {
    return (
      <AISecurityGuard requiredFeature={requiredFeature}>
        <WrappedComponent {...props} />
      </AISecurityGuard>
    );
  };

  SecuredComponent.displayName = `withAISecurity(${WrappedComponent.displayName || WrappedComponent.name})`;

  return SecuredComponent;
}

/**
 * Component for displaying user's AI permissions
 */
export const AIPermissionsDisplay: React.FC = () => {
  const { securityContext } = useAISecurityContext();

  if (!securityContext) {
    return null;
  }

  return (
    <div className="bg-gray-50 p-4 rounded-lg">
      <div className="flex items-center mb-3">
        <Shield className="h-5 w-5 text-blue-600 mr-2" />
        <h3 className="font-medium">KI-Berechtigungen</h3>
      </div>

      <div className="space-y-2">
        <div className="text-sm text-gray-600">
          Benutzer: <span className="font-medium">{securityContext.username}</span>
        </div>
        <div className="text-sm text-gray-600">
          Rollen: <span className="font-medium">{securityContext.roles.join(', ')}</span>
        </div>

        <div className="mt-3">
          <div className="text-sm font-medium text-gray-700 mb-2">Verfügbare Funktionen:</div>
          <div className="space-y-1">
            {securityContext.permissions.map(permission => (
              <div key={permission.id} className="text-sm text-green-600 flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                {permission.name}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};